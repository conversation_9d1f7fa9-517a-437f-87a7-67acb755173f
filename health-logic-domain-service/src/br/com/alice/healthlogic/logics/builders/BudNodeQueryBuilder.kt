package br.com.alice.healthlogic.logics.builders

import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.data.layer.models.ServiceScriptStatus.ACTIVE
import br.com.alice.data.layer.services.BudNodeDataService.FieldOptions
import br.com.alice.data.layer.services.BudNodeDataService.OrderingOptions
import java.util.UUID

class BudNodeQueryBuilder(
    private val budNodeQueryBuilder: QueryBuilder<FieldOptions, OrderingOptions>,
) {

    companion object {
        val BUD_NODE_TYPES = BudNode.BudNodeType.values().toList()
    }

    private var predicates: Predicate? = null

    fun build(
        vararg type: BudNode.BudNodeType,
        ids: List<UUID> = emptyList(),
        name: String? = null,
        status: List<ServiceScriptStatus> = emptyList(),
    ): QueryBuilder<FieldOptions, OrderingOptions> {

        addIdPredicate(ids)
        addNamePredicate(name)
        addTypePredicate(type.toList())
        addStatusPredicate(status.toList())

        return budNodeQueryBuilder.where { predicates!! }
    }

    private fun addToPredicate(newPredicate: Predicate) {
        if (predicates == null) {
            predicates = newPredicate
            return
        }

        predicates = predicates!!.and(newPredicate)
    }

    private fun addIdPredicate(ids: List<UUID>) {
        if(ids.isEmpty()) return

        val newPredicate = Predicate.inList(budNodeQueryBuilder.fieldOptions.id, ids)
        addToPredicate(newPredicate)
    }
    private fun addTypePredicate(type: List<BudNode.BudNodeType>) {
        val newPredicate = when (type.size) {
            0 -> Predicate.inList(budNodeQueryBuilder.fieldOptions.type, BUD_NODE_TYPES)
            1 -> Predicate.eq(budNodeQueryBuilder.fieldOptions.type, type.first())
            else -> Predicate.inList(budNodeQueryBuilder.fieldOptions.type, type)
        }

        addToPredicate(newPredicate)
    }

    @OptIn(LikePredicateUsage::class)
    private fun addNamePredicate(name: String?) {
        if (name == null) return

        val newPredicate = Predicate.like(budNodeQueryBuilder.fieldOptions.name, name)
        addToPredicate(newPredicate)
    }

    private fun addStatusPredicate(status: List<ServiceScriptStatus>) {
        val newPredicate = when {
            status.isEmpty() -> Predicate.eq(budNodeQueryBuilder.fieldOptions.status, ACTIVE)
            status.size == 1 -> Predicate.eq(budNodeQueryBuilder.fieldOptions.status, status.first())
            else -> Predicate.inList(budNodeQueryBuilder.fieldOptions.status, status)
        }

        addToPredicate(newPredicate)
    }
}
