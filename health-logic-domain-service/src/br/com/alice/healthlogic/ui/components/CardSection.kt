package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.CardSection

object CardSection {

    fun build(
        id: String,
        text: String,
        navigationGroupId: String,
        url: String,
        params: Map<String, Any>? = null,
        minAppVersion: String
    ) =
        Section(
            id = id,
            type = SectionType.CARD_SECTION,
            data = CardSection(
                title = text,
                type = CardType.TEXT_CARD,
                onCardClick = RemoteAction(
                    navigationGroup = navigationGroupId,
                    mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                    params = mapOf(
                        "action" to mapOf(
                            "method" to "POST",
                            "endpoint" to url,
                            "params" to params
                        )
                    )
                )
            ),
            minAppVersion = minAppVersion
        )
}
