package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.ChatInputSection

object InputSection {

    const val minAppVersion: String = "3.30.0"

    fun build(
        id: String,
        placeholder: String?,
        text: String?,
        remoteAction: RemoteAction? = null
    ) = Section(
        id = id,
        type = SectionType.CHAT_INPUT_SECTION,
        data = ChatInputSection(
            placeholder = placeholder,
            text = text,
            onSendAction = remoteAction
        ),
        minAppVersion = minAppVersion
    )
}
