package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.CalendarAvailableDays
import br.com.alice.app.content.model.CalendarMode
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.CalendarSection
import java.time.LocalDate

object CalendarSection {

    fun build(
        id: String,
        initialDate: LocalDate,
        firstDate: LocalDate,
        lastDate: LocalDate,
        confirmButton: Button,
        minAppVersion: String
    ) =
        Section(
            id = id,
            type = SectionType.CALENDAR_SECTION,
            data = CalendarSection(
                variant = Variant.PRIMARY,
                initialDate = initialDate,
                firstDate = firstDate,
                lastDate = lastDate,
                availableDays = CalendarAvailableDays.ALL,
                initialCalendarMode = CalendarMode.DAY,
                confirmButton = confirmButton,
            ),
            minAppVersion = minAppVersion
        )
}
