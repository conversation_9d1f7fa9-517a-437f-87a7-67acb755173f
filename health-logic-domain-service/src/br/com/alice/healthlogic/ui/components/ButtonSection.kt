package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Size
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import java.time.Duration

object ButtonSection {
    fun build(
        id: String,
        text: String,
        remoteAction: RemoteAction? = null,
        minAppVersion: String,
        shrinkWrap: Boolean = true,
        alignment: Alignment? = null
    ) = Section(
        id = id,
        type = SectionType.BUTTON_SECTION,
        data = ButtonSection(
            button = Button(
                label = Label(text),
                variant = Variant.PRIMARY,
                onTapAction = remoteAction,
                size = Size.LARGE,
                shrinkWrap = shrinkWrap
            ),
            alignment = alignment
        ),
        minAppVersion = minAppVersion,
        cacheDuration = Duration.ZERO
    )
}
