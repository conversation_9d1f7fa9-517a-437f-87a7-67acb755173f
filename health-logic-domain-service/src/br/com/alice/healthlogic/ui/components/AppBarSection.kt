package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionItem
import br.com.alice.app.content.model.RemoteActionTransition

object AppBarSection {

    fun build(
        text: String,
        withOpenChatButton: Boolean = false,
        endpointUrl: String? = null
    ) = AppBar(
        title = text,
        back = "",
        rightRemoteActionItems = if (withOpenChatButton) listOf(
            RemoteActionItem(
                icon = "chat",
                action = RemoteAction(
                    mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                    params = mapOf(
                        "action" to mapOf(
                            "method" to "GET",
                            "endpoint" to endpointUrl,
                        )
                    ),
                    transition = RemoteActionTransition.BOTTOM_SHEET_FIXED
                )
            )
        ) else null
    )
}
