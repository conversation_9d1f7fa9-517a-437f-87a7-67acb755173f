package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.SheetSection

object SheetSection {

    fun build(
        id: String,
        title: String,
        subtitle: String,
        confirmationButton: Button,
        cancelButton: Button,
        illustrationUrl: String,
        minAppVersion: String
    ): Section =
        Section(
            id = id,
            type = SectionType.SHEET_SECTION,
            data = SheetSection(
                title = title,
                content = SheetSection.Content(
                    text = subtitle,
                ),
                confirmationButton = confirmationButton,
                cancelButton = cancelButton,
                illustrationUrl = illustrationUrl,
            ),
            minAppVersion = minAppVersion
        )
}
