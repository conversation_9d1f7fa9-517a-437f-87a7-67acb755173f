package br.com.alice.healthlogic.ui.factory

import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.CloseBehavior
import br.com.alice.app.content.model.CloseStrategy
import br.com.alice.app.content.model.ImageSize
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.data.layer.models.BudNode
import br.com.alice.healthlogic.models.bud.EndScreenData
import br.com.alice.healthlogic.ui.components.AppBarSection
import br.com.alice.healthlogic.ui.components.ButtonSection
import br.com.alice.healthlogic.ui.components.LinkSection
import br.com.alice.healthlogic.ui.components.SubtitleSection
import br.com.alice.healthlogic.ui.components.TitleSection

object TriagePrescriptionEndScreenFactory : ScreenFactory("Renovação de Receita") {

    fun build(
        screeningNavigationId: String,
        endScreenData: EndScreenData,
        budNode: BudNode
    ) = ScreensTransport(
            id = "triage_prescription_empty_screen",
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.END,
                    horizontal = ScreenAlignmentType.START
                ),
                closeBehavior = CloseBehavior(
                    strategy = CloseStrategy.BLOCK,
                    action = null
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = buildAppBar(budNode),
                body = buildBody(screeningNavigationId, endScreenData)
            )
        )
    private fun buildAppBar(
        budNode: BudNode
    ): AppBar =
        AppBarSection.build(
            text = getAppBarTitleByRootNodeId(budNode.rootNodeId),
            withOpenChatButton = false,
            endpointUrl = null
        )

    private fun buildBody(
        screeningNavigationId: String,
        endScreenData: EndScreenData
    ): List<Section> {
        val buttonText = endScreenData.button?.text!!
        val actionButton = endScreenData.button.action
        val image = endScreenData.image ?: imageUrl

        return listOfNotNull(
            buildImage(image, ImageSize.MEDIUM),
            endScreenData.label?.let { label ->
                TitleSection.build("bud_title_label", label, minAppVersion, SectionTextLayout.TITLE_SMALL)
            },
            TitleSection.build("bud_title", endScreenData.title!!, minAppVersion),
            endScreenData.subTitle?.let { SubtitleSection.build("bud_subtitle", endScreenData.subTitle, minAppVersion) },
            ButtonSection.build(
                id = "bud_confirm_button",
                text = buttonText,
                remoteAction = addNavigationGroupInAction(actionButton, screeningNavigationId),
                minAppVersion = minAppVersion,
                shrinkWrap = false,
                alignment = Alignment.CENTER
            )
        ).let {
            if (endScreenData.link != null) {
                it.plus(
                    LinkSection.build(
                        id = "bud_link_button",
                        text = endScreenData.link.text,
                        remoteAction = addNavigationGroupInAction(endScreenData.link.action, screeningNavigationId),
                        minAppVersion = minAppVersion,
                        alignment = Alignment.CENTER
                    )
                )
            } else it
        }
    }

    private fun addNavigationGroupInAction(
        remoteAction: RemoteAction?,
        screeningNavigationId: String
    ) =
        remoteAction?.let { action ->
            if(action.navigationGroup == null) {
                action.copy(navigationGroup = screeningNavigationId)
            } else action
        }
}
