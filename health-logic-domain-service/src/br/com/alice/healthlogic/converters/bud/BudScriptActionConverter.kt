package br.com.alice.healthlogic.converters.bud

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.healthlogic.models.bud.EndScreenData
import com.google.gson.Gson

private val gson = Gson()
object BudScriptActionConverter {
    fun convertScriptActionContentMapToEndScreenData(source: Map<String, Any?>) =
        source.let {
            val contentJson = gson.toJson(source)

            gson.fromJson<EndScreenData>(contentJson)
        }
}
