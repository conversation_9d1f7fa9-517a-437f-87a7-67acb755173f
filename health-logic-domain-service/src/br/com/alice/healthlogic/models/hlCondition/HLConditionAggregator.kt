package br.com.alice.healthlogic.models.hlCondition

import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.models.ServiceScriptOperator
import br.com.alice.healthlogic.logics.creators.hlCondition.InvalidHLConditionOperator

data class HLConditionAggregator(
    val operator: ServiceScriptOperator,
    val conditions: List<List<HLCondition>>,
    private val hDataOverview: HDataOverview,
) : HLCondition {

    override fun evaluate(): <PERSON>olean =
        if (conditions.isEmpty()) false
        else when (operator) {
            ServiceScriptOperator.AND -> conditions.all { validateCondition(it) }
            ServiceScriptOperator.OR -> conditions.any { validateCondition(it) }
            else -> throw InvalidHLConditionOperator(this.classSimpleName(), operator.toString())
        }

    private fun validateCondition(conditions: List<HLCondition>): Boolean =
        if (conditions.isEmpty()) false
        else conditions.all { it.evaluate() }
}
