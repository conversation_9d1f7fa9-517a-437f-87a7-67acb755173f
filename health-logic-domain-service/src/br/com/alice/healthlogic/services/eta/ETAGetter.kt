package br.com.alice.healthlogic.services.eta

import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.models.eta.AutomaticTaskEngine
import java.util.UUID

class ETAGetter(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val id: UUID,
) {

    private lateinit var relationship: ServiceScriptRelationship
    private lateinit var actionNode: ServiceScriptNode
    private lateinit var triggerNode: ServiceScriptNode

    suspend fun get(): AutomaticTaskEngine {
        getRelationship()
        getNodes()

        return AutomaticTaskEngine(
            relationship = relationship,
            triggerNode = triggerNode,
            actionNode = actionNode
        )
    }

    private suspend fun getRelationship() {
        relationship = serviceScriptRelationshipDataService.get(id).get()
    }

    private suspend fun getNodes() {
        val nodes  = serviceScriptNodeDataService.find {
            where { id.inList(listOf(relationship.nodeParentId!!, relationship.nodeChildId!!)) }
        }.get()

        triggerNode = nodes.first { it.id == relationship.nodeParentId!! }
        actionNode = nodes.first { it.id == relationship.nodeChildId!! }
    }
}
