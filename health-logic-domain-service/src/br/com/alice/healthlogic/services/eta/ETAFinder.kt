package br.com.alice.healthlogic.services.eta

import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType.TRIGGER
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus.ACTIVE
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.models.eta.AutomaticTaskEngineLite
import java.util.UUID

class ETAFinder(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val triggerName: String?,
    private val queryFilter: String?,
    private val range: IntRange,
) {

    lateinit var triggerNodes: List<ServiceScriptNode>

    private val relationshipQueryBuilder = serviceScriptRelationshipDataService.queryBuilder()
    private val serviceScriptNodeQueryBuilder = serviceScriptNodeDataService.queryBuilder()

    suspend fun find(): List<AutomaticTaskEngineLite> {
        getTriggerNodes()

        val mappedTriggerNodes = triggerNodes.associateBy { it.id }
        val relationships = getRelationships(triggerNodes.map { it.id })

        return relationships.map { relationship ->
            AutomaticTaskEngineLite(
                id = relationship.id,
                triggerNodeId = relationship.nodeParentId!!,
                name = relationship.name,
                active = relationship.status == ACTIVE,
                triggerType = mappedTriggerNodes[relationship.nodeParentId!!]!!.name
            )
        }
    }

    suspend fun count(): Int {
        getTriggerNodes()

        return countRelationships(triggerNodes.map { it.id })
    }

    private suspend fun getTriggerNodes() {
        if (!this::triggerNodes.isInitialized) {
            var predicate = Predicate.eq(
                serviceScriptNodeQueryBuilder.fieldOptions.type, TRIGGER
            ) and Predicate.eq(
                serviceScriptNodeQueryBuilder.fieldOptions.status, ACTIVE
            )

            if (triggerName != null) {
                predicate = predicate and Predicate.eq(
                    serviceScriptNodeQueryBuilder.fieldOptions.name, triggerName
                )
            }

            triggerNodes = serviceScriptNodeDataService.find { where { predicate } }.get()
        }
    }

    private suspend fun getRelationships(parentIds: List<UUID>): List<ServiceScriptRelationship> {
        val predicate = relationshipsPredicate(parentIds)

        return serviceScriptRelationshipDataService.find {
            where { predicate }
                .orderBy { this.name }
                .offset { range.first }
                .limit { range.count() }
        }.get()
    }

    private suspend fun countRelationships(parentIds: List<UUID>): Int {
        val predicate = relationshipsPredicate(parentIds)

        return serviceScriptRelationshipDataService.count { where { predicate } }.get()
    }

    @OptIn(LikePredicateUsage::class)
    private fun relationshipsPredicate(parentIds: List<UUID>): Predicate {
        var predicate = Predicate.inList(relationshipQueryBuilder.fieldOptions.parentId, parentIds)

        if (queryFilter != null) {
            predicate = predicate and Predicate.like(relationshipQueryBuilder.fieldOptions.name, queryFilter)
        }

        return predicate
    }
}
