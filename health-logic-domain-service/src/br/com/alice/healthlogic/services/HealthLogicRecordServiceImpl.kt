package br.com.alice.healthlogic.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.HealthLogicRecord
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType.HEALTH_LOGIC
import br.com.alice.data.layer.models.ServiceScriptNodeType.PROGRAM
import br.com.alice.data.layer.services.HealthLogicRecordDataService
import br.com.alice.healthlogic.client.HealthLogicRecordService
import br.com.alice.healthlogic.client.ScriptService
import br.com.alice.healthlogic.converters.HealthLogicResponseConverter
import br.com.alice.healthlogic.converters.PersonHealthLogicTransportConverter
import br.com.alice.healthlogic.event.HealthLogicRecordCreatedEvent
import br.com.alice.healthlogic.models.HealthLogicResponse
import br.com.alice.healthlogic.models.PersonHealthLogicTransport
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class HealthLogicRecordServiceImpl(
    private val healthLogicRecordDataService: HealthLogicRecordDataService,
    private val scriptService: ScriptService,
    private val kafkaProducerService: KafkaProducerService
): HealthLogicRecordService {

    override suspend fun create(
        record: HealthLogicRecord,
        caseRecordId: UUID?
    ): Result<PersonHealthLogicTransport, Throwable> =
        healthLogicRecordDataService.add(record).flatMapPair {
            scriptService.findNodeByIds(it.getNodeIds())
        }.map { (logics, record) ->
            val healthLogic = logics.first { it.id == record.healthLogicNodeId }
            val program = logics.first { it.id == record.currentNodeId }
            PersonHealthLogicTransportConverter.convert(
                records = listOf(record),
                logicNode = healthLogic,
                programNode = program,
                currentStatus = record.status
            )
        }.then {
            kafkaProducerService.produce(HealthLogicRecordCreatedEvent(record, caseRecordId))
        }

    private suspend fun getHealthLogicByPersonId(personId: PersonId): Result< List<Pair<PersonHealthLogicTransport, ServiceScriptNode>>, Throwable> =
        healthLogicRecordDataService.find { where { this.personId.eq(personId) } }.flatMapPair { healthLogics ->
            val nodeIds = healthLogics.map { it.getNodeIds() }.flatten().distinct()
            scriptService.findNodeByIds(nodeIds)
        }.map { (nodes, healthLogics) ->
            val nodesMap = nodes.associateBy { it.id }
            val personLogics = healthLogics.sortedByDescending { it.addedAt }.groupBy { it.healthLogicNodeId }
            personLogics
                .map { (logicId, records) ->
                    val currentLogic = records.first()

                    val programNode = nodesMap[currentLogic.currentNodeId]!!

                    val  personHealthLogicTransport = PersonHealthLogicTransportConverter.convert(
                        records = records,
                        logicNode = nodesMap[logicId]!!,
                        programNode = programNode,
                        currentStatus = currentLogic.status
                    )

                    Pair(personHealthLogicTransport, programNode)
                }
        }

    override suspend fun getProgramsByPersonId(personId: PersonId): Result<List<ServiceScriptNode>, Throwable> =
        Result.of { getHealthLogicByPersonId(personId).get().map { it.second } }

    override suspend fun getByPersonId(personId: PersonId): Result<List<PersonHealthLogicTransport>, Throwable> =
        Result.of { getHealthLogicByPersonId(personId).get().map { it.first } }

    override suspend fun getCurrentHealthLogicRecordByPersonId(personId: PersonId, healthLogicId: UUID): Result<HealthLogicRecord, Throwable> =
        healthLogicRecordDataService.find {
            where { this.personId.eq(personId) and this.healthLogicId.eq(healthLogicId) }.orderBy { this.addedAt }.sortOrder { desc }
        }.map { it.first() }

    override suspend fun searchByName(name: String): Result<List<HealthLogicResponse>, Throwable> =
        scriptService.searchNodeAndRelationshipsByNameAndType(name, listOf(HEALTH_LOGIC, PROGRAM)).map { logics ->
            val programNodes = logics.filter { it.serviceScriptNode.type == HEALTH_LOGIC }
            val categoryNodes = logics.filter { it.serviceScriptNode.type == PROGRAM }

            val categoriesIds = programNodes.flatMap { it.serviceScriptRelationship }.mapNotNull { it.nodeChildId }
            val programIds = categoryNodes.flatMap { it.serviceScriptRelationship }.mapNotNull { it.nodeParentId }

            val nodes = scriptService.findNodeByIds(categoriesIds + programIds)
                .get()
                .plus(categoryNodes.map { it.serviceScriptNode })
                .associateBy { it.id }

            val programsFromCategories = categoryNodes.map { categoryWithRelationship ->
                categoryWithRelationship.serviceScriptRelationship.mapNotNull { relationship ->
                    if (nodes[relationship.nodeParentId] != null && nodes[relationship.nodeParentId]!!.type == HEALTH_LOGIC) {
                        NodeWithRelationships(
                            nodes[relationship.nodeParentId]!!,
                            listOf(relationship)
                        )
                    } else { null }
                }
            }.flatten()

            (programsFromCategories + programNodes).map { HealthLogicResponseConverter.convert(it, nodes) }
        }

    override suspend fun getProgramsByHealthLogicId(healthLogicId: UUID): Result<HealthLogicResponse, Throwable> =
        scriptService.getNodeAndRelationshipsById(healthLogicId).map { logic ->
            val programIds = logic.serviceScriptRelationship.mapNotNull { it.nodeChildId }
            val programs = scriptService.findNodeByIds(programIds).get().associateBy { it.id }
            HealthLogicResponseConverter.convert(logic, programs)
        }

    override suspend fun isFirstOfProgram(healthLogicRecord: HealthLogicRecord): Result<Boolean, Throwable> =
        Result.of {
            if (!healthLogicRecord.hasValidStatus()) return@of false

            val records = healthLogicRecordDataService.find {
                where {
                    this.healthLogicId.eq(healthLogicRecord.healthLogicNodeId) and
                            this.personId.eq(healthLogicRecord.personId)
                }.orderBy { this.addedAt }.sortOrder { asc }
            }.get()

            val index = records.indexOf(healthLogicRecord)
            if (index <= 0) return@of true

            val previousRecord = records[index - 1]
            !previousRecord.hasValidStatus() || healthLogicRecord.currentNodeId != previousRecord.currentNodeId
        }
}
