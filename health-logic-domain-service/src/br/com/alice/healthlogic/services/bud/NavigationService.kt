package br.com.alice.healthlogic.services.bud

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Answer
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Option
import br.com.alice.data.layer.models.OptionType
import br.com.alice.data.layer.models.SelectionType
import br.com.alice.data.layer.models.ServiceScriptNavigation
import br.com.alice.data.layer.models.ServiceScriptNavigationGroup
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.models.ServiceScriptNavigationType.MANUAL
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.services.ServiceScriptActionDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.logics.extensions.isValid
import br.com.alice.healthlogic.models.bud.BudNavigation
import br.com.alice.healthlogic.models.bud.BudNavigationHistory
import br.com.alice.healthlogic.models.bud.BudNodeNavigationHistory
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import br.com.alice.healthlogic.services.internal.ServiceScriptNavigationService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class NavigationService(
    private val budNodeDataService: BudNodeCacheDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipCacheDataService,
    private val serviceScriptNavigationService: ServiceScriptNavigationService,
    private val serviceScriptActionDataService: ServiceScriptActionDataService
) {

    suspend fun goTo(nodeId: UUID, navigation: BudNavigation? = null): NodeWithRelationships<BudNode> {
        val budNode = budNodeDataService.get(nodeId).get()
        val relationships = getRelationships(nodeId)
        var savedNavigation: BudNavigation? = null

        if (navigation != null) {
            val updatedNavigation = saveNavigation(navigation).get()
            savedNavigation = convertToBudNavigation(
                serviceScriptNavigation = updatedNavigation,
                personId = navigation.personId,
                scriptNodeId = navigation.scriptNodeId,
                source = navigation.source
            )
            finishNavigation(budNode, navigation)
        }

        return buildNodeResponse(budNode, relationships, savedNavigation)
    }

    suspend fun navigate(
        budNode: BudNode,
        answersOption: Option,
        source: ServiceScriptNavigationSource,
        personId: PersonId
    ): NodeWithRelationships<BudNode>? {
        val relationships = getRelationships(budNode.id)
        val navigation = getNavigation(personId, budNode.id, source.id)
            ?.toBudNavigation(personId, source, budNode.rootNodeId!!)

        if (relationships.isEmpty()) {
            logger.info(
                "NavigationService::navigate - Empty relationships",
                "nodeId" to budNode.id,
                "personId" to personId,
                "answersOption" to answersOption,
                "navigation" to navigation
            )
            if (navigation != null) {
                saveNavigation(navigation.copy(questionAnswers = answersOption))
            }
            return null
        }

        return getAnswers(answersOption)?.let { answers ->
            val matchedRelationship = findMatchActions(relationships, answers)
            logger.info(
                "NavigationService::navigate - findMatchActions",
                "nodeId" to budNode.id,
                "personId" to personId,
                "answersOption" to answersOption,
                "answers" to answers,
                "relationships", relationships,
                "matchedRelationship" to matchedRelationship
            )

            return if (matchedRelationship?.nodeChildId != null) {
                val newNode = budNodeDataService.get(matchedRelationship.nodeChildId!!).get()

                val budNavigation = BudNavigation(
                    personId = personId,
                    scriptNodeId = newNode.rootNodeId!!,
                    previousNodeId = matchedRelationship.nodeParentId,
                    relationshipId = matchedRelationship.id,
                    source = source,
                    questionAnswers = answersOption
                )

                val newNavigation = saveNavigation(budNavigation).get()

                buildNodeResponse(
                    newNode,
                    listOf(matchedRelationship),
                    convertToBudNavigation(
                        serviceScriptNavigation = newNavigation,
                        personId = personId,
                        scriptNodeId = budNavigation.scriptNodeId,
                        source = budNavigation.source
                    )
                )
            } else {
                if (navigation != null) {
                    matchedRelationship?.let{
                        val budNavigation = BudNavigation(
                            personId = personId,
                            scriptNodeId = budNode.rootNodeId!!,
                            previousNodeId = matchedRelationship.nodeParentId,
                            relationshipId = matchedRelationship.id,
                            source = source,
                            questionAnswers = answersOption,
                            groupId = navigation.groupId,
                            staffId = navigation.staffId
                        )

                        val newNavigation = saveNavigation(budNavigation).get()

                        logger.info(
                            "NavigationService::navigate - matchedRelationship without child",
                            "saved navigation" to newNavigation
                        )

                        val finishedNavigation = serviceScriptNavigationService.finishNavigation(
                            convertToBudNavigation(
                                serviceScriptNavigation = newNavigation,
                                personId = personId,
                                scriptNodeId = budNavigation.scriptNodeId,
                                source = budNavigation.source
                            )
                        )
                        logger.info(
                            "NavigationService::navigate - matchedRelationship without child",
                            "navigation finished" to finishedNavigation
                        )
                    } ?: serviceScriptNavigationService.finishNavigation(navigation)
                }
                null
            }
        }
    }

    suspend fun navigateBackwards(
        personId: PersonId,
        source: ServiceScriptNavigationSource,
        nodeId: UUID
    ): Result<Pair<BudNode, ServiceScriptNavigation>, Throwable> =
        serviceScriptNavigationService.navigateBackwards(personId, source, nodeId).flatMapPair { serviceScriptNavigation ->
            budNodeDataService.get(serviceScriptNavigation.currentNodeId!!)
        }

    suspend fun continueNavigationFrom(
        personId: PersonId,
        source: ServiceScriptNavigationSource?
    ): BudNavigationHistory {
        val lastHistoryRegister = serviceScriptNavigationService.getHistory(personId, source).get()
        val relationship = serviceScriptRelationshipDataService.get(lastHistoryRegister.relationshipId).get()

        return BudNavigationHistory(
            scriptNodeId = lastHistoryRegister.scriptNodeId,
            nodeId = relationship.nodeChildId!!
        )
    }

    suspend fun finishNavigation(
        personId: PersonId,
        source: ServiceScriptNavigationSource? = null
    ): Result<ServiceScriptNavigationGroup, Throwable> =
        serviceScriptNavigationService.finishNavigation(personId, source)

    suspend fun getNavigation(
        personId: PersonId,
        currentNodeId: UUID,
        sourceId: String
    ): ServiceScriptNavigation? =
        serviceScriptNavigationService.getNavigation(personId, currentNodeId, sourceId).getOrNullIfNotFound()

    suspend fun startNavigation(rootNodeId: UUID, personId: PersonId, source: ServiceScriptNavigationSource): NodeWithRelationships<BudNode> =
        budNodeDataService.get(rootNodeId).flatMapPair {
            serviceScriptNavigationService.getGroup(personId, it.id, source)
        }.map {(_, node) ->
            val relationships = getRelationships(rootNodeId)
            buildNodeResponse(node, relationships)
        }.get()

    suspend fun getNavigationHistory(source: ServiceScriptNavigationSource): Result<List<BudNodeNavigationHistory>, Throwable> = coroutineScope {
        serviceScriptNavigationService.getNavigationHistory(source).map { navigation ->

            val nodesDeferred = async { getNodes(navigation) }
            val relationshipsDeferred = async { getRelationships(navigation) }

            buildNavigationHistory(navigation, nodesDeferred.await(), relationshipsDeferred.await())
        }
    }

    suspend fun getNavigationHistoryById(navigationId: UUID): Result<List<ServiceScriptNavigation>, Throwable> =
        serviceScriptNavigationService.get(navigationId).flatMap {
            serviceScriptNavigationService.getNavigationActives(it.groupId)
        }

    private suspend fun buildNodeResponse(
        budNode: BudNode,
        relationships: List<ServiceScriptRelationship>,
        navigation: BudNavigation? = null
    ): NodeWithRelationships<BudNode> =
        NodeWithRelationships(
            serviceScriptNode = budNode,
            actions = getActions(budNode),
            serviceScriptRelationship = relationships,
            navigation = navigation
        )

    private suspend fun getActions(budNode: BudNode) =
        if (budNode.serviceScriptActionIds.isNotEmpty()) {
            serviceScriptActionDataService.find {
                where {
                    id.inList(budNode.serviceScriptActionIds)
                }
            }.getOrNullIfNotFound()
                ?: emptyList()
        } else emptyList()

    private suspend fun saveNavigation(navigation: BudNavigation): Result<ServiceScriptNavigation, Throwable> =
        serviceScriptNavigationService.saveNavigation(navigation)

    private suspend fun finishNavigation(node: BudNode, navigation: BudNavigation) {
        if (node.type == BudNode.BudNodeType.ACTION) {
            serviceScriptNavigationService.finishNavigation(navigation)
        }
    }

    private suspend fun getRelationships(nodeId: UUID) =
        serviceScriptRelationshipDataService.getActivesByParentId(nodeId).get()

    private fun buildNavigationHistory(
        navigationList: List<ServiceScriptNavigation>,
        nodeMap: Map<UUID, BudNode>,
        relationshipMap: Map<UUID, ServiceScriptRelationship>
    ) =
        navigationList.sortedBy { it.createdAt }.let { sortedList ->
            sortedList.mapIndexedNotNull { index, navigation ->
                nodeMap.get(navigation.previousNodeId)?.let { node ->
                    val relationship = relationshipMap.getValue(navigation.relationshipId)
                    val rootNode = nodeMap.get(node.rootNodeId)

                    BudNodeNavigationHistory.from(node, navigation, index, rootNode, relationship)
                }
            }.let { historyList ->
                val lastNode = sortedList.last().let { navigation ->
                    nodeMap.get(navigation.currentNodeId)?.let { node ->
                        val rootNode = nodeMap.get(node.rootNodeId)
                        BudNodeNavigationHistory.from(node, navigation, sortedList.size, rootNode)
                    }
                }

                // force append the last node because it does not have an "answer"
                historyList.plusSafe(lastNode)
            }
        }

    private suspend fun getNodes(navigation: List<ServiceScriptNavigation>): Map<UUID, BudNode> {
        val nodeIds = navigation.map { listOfNotNull(it.previousNodeId, it.currentNodeId) }.flatten().distinct()
        val nodesMap = getNodesMap(nodeIds)

        val rootNodeIds = nodesMap.values.mapNotNull { it.rootNodeId }.distinct()

        return nodesMap.plus(getNodesMap(rootNodeIds))
    }

    private suspend fun getRelationships(navigation: List<ServiceScriptNavigation>) =
        serviceScriptRelationshipDataService.get(navigation.map { it.relationshipId }.distinct())
            .get()
            .associateBy { it.id }

    private suspend fun getNodesMap(ids: List<UUID>) =
        if (ids.isEmpty()) emptyMap()
        else budNodeDataService.get(ids).get().associateBy { it.id }

    private fun convertToBudNavigation(
        serviceScriptNavigation: ServiceScriptNavigation,
        personId: PersonId,
        scriptNodeId: UUID,
        source: ServiceScriptNavigationSource? = null
    ) =
        BudNavigation(
            personId = personId,
            scriptNodeId = scriptNodeId,
            previousNodeId = serviceScriptNavigation.previousNodeId,
            relationshipId = serviceScriptNavigation.relationshipId,
            staffId = serviceScriptNavigation.staffId,
            type = serviceScriptNavigation.type ?: MANUAL,
            questionAnswers = serviceScriptNavigation.questionAnswers,
            groupId = serviceScriptNavigation.groupId,
            source = source
        )

    private fun Map<UUID, BudNode>.get(id: UUID?) = id?.let { this.getValue(it) }

    private fun getAnswers(answersOption: Option): Any? {
        return answersOption.let { option ->
            when(option.type) {
                OptionType.HEALTH_CONDITION, OptionType.SYMPTOMS -> option.getAnswersByField { it.externalId }
                OptionType.FREE_TEXT -> option.getAnswersByField { it.id }
                else -> option.getAnswersByField { it.value }
            }
        }
    }

    private fun Option.getAnswersByField(map: (answer: Answer) -> Any?): Any? =
        if (selectionType == SelectionType.MULTIPLE) {
            answers.map { map(it) }
        } else {
            answers.firstOrNull()?.let { map(it) }
        }

    private fun findMatchActions(relationships: List<ServiceScriptRelationship>, answer: Any): ServiceScriptRelationship? {
        logger.info(
            "NavigationService::findMatchActions Parameters" ,
            "relationships" to relationships,
            "answer" to answer
        )

        val match = relationships
            .filter { it.conditions.isNotEmpty() }
            .firstOrNull { relationship ->
                relationship.conditions.all { condition -> condition.isValid(answer) }
            }

        logger.info("NavigationService::findMatchActions Relationship match", "match" to match)

        if(match == null && relationships.size == 1) {
            return relationships.first()
        }

        return match
    }

    private fun ServiceScriptNavigation.toBudNavigation(
        personId: PersonId,
        source: ServiceScriptNavigationSource,
        rootNodeId: UUID
    ) =
        BudNavigation(
            personId = personId,
            scriptNodeId = rootNodeId,
            previousNodeId = previousNodeId,
            relationshipId = relationshipId,
            staffId = staffId,
            source = source,
            type = type!!,
            groupId = groupId
        )
}
