package br.com.alice.healthlogic.services.bud

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.MessageType.TEXT
import br.com.alice.channel.models.acuteImmediateTag
import br.com.alice.channel.models.budTriageTag
import br.com.alice.data.layer.models.ChannelCategory.ASSISTANCE
import br.com.alice.data.layer.models.ChannelCreationParameters
import br.com.alice.data.layer.models.ChannelKind.CHAT
import br.com.alice.data.layer.models.ChannelScreeningNavigation
import br.com.alice.data.layer.models.ChannelScreeningNavigationStatus
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory.SCREENING
import br.com.alice.data.layer.models.ScreeningNavigation
import br.com.alice.data.layer.models.ScreeningNavigationStatus.CLOSED
import br.com.alice.data.layer.models.ScreeningNavigationStatus.DROPPED
import br.com.alice.data.layer.models.ScreeningNavigationStatus.FINISHED
import com.github.kittinunf.result.Result

class ChatService(
    private val channelService: ChannelService
) {

    suspend fun createChat(
        screeningNavigation: ScreeningNavigation,
        channelCreationParameters: ChannelCreationParameters?,
        userAgent: String?
    ): Result<ChannelDocument, Throwable> {
        val chatParamsWithValidations = getChatParams(
            channelCreationParameters = channelCreationParameters
        )
        val createChatRequest = CreateChatRequest(
            personId = screeningNavigation.personId,
            allowOnCallFlow = true,
            origin = "newChannel",
            message = ChatMessageRequest(
                content = chatParamsWithValidations.content,
                type = TEXT,
                appVersion = userAgent,
            ),
            category = chatParamsWithValidations.category,
            subCategory = chatParamsWithValidations.subCategory,
            tags = chatParamsWithValidations.tags,
            screeningNavigation = ChannelScreeningNavigation(
                screeningNavigation.id,
                hasProtocol = false,
                status = getChannelStatus(screeningNavigation)
            ),
            appVersion = userAgent
        )
        return channelService.addChatV2(request = createChatRequest)
    }

    suspend fun getChatByScreeningNavigationId(
        screeningNavigation: ScreeningNavigation
    ) =
        channelService.getByScreeningNavigationId(
            personId = screeningNavigation.personId,
            screeningNavigationId = screeningNavigation.id,
            statuses = listOf(ChannelStatus.ACTIVE)
        )

    private fun getChatParams(
        channelCreationParameters: ChannelCreationParameters?,
    ) =
        ChannelCreationParameters(
            content = channelCreationParameters?.content
                ?: "Preciso de ajuda nesse momento",
            kind = CHAT,
            category = ASSISTANCE,
            subCategory = SCREENING,
            tags = if (channelCreationParameters?.tags.isNullOrEmpty())
                listOf(acuteImmediateTag, budTriageTag)
            else channelCreationParameters!!.tags
        )

    private fun getChannelStatus(
        screeningNavigation: ScreeningNavigation
    ) =
        when (screeningNavigation.status) {
            FINISHED -> ChannelScreeningNavigationStatus.COMPLETED
            DROPPED -> ChannelScreeningNavigationStatus.DROPPED
            CLOSED -> ChannelScreeningNavigationStatus.CLOSED
            else -> ChannelScreeningNavigationStatus.INCOMPLETED
        }

}
