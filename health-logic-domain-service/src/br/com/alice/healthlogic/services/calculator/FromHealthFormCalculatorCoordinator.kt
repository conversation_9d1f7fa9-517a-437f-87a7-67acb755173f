package br.com.alice.healthlogic.services.calculator

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.data.layer.services.OutcomeConfDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthlogic.client.ClinicalOutcomeService
import br.com.alice.healthlogic.client.HealthFormOutcomeCalculatorConfService
import br.com.alice.healthlogic.client.OutcomeConfService
import br.com.alice.healthlogic.client.OutcomeRequestSchedulingService
import br.com.alice.healthlogic.metrics.Metrics
import br.com.alice.healthlogic.metrics.Result
import br.com.alice.healthlogic.strategies.CalculatorThresholdStrategyCouldntMatchConditionException
import br.com.alice.healthlogic.strategies.WrongHealthFormCalculatorArgumentsException
import br.com.alice.questionnaire.client.HealthFormManagementService
import java.util.UUID

class FromHealthFormCalculatorCoordinator(
    private val healthFormOutcomeCalculatorConfService: HealthFormOutcomeCalculatorConfService,
    private val clinicalOutcomeService: ClinicalOutcomeService,
    private val healthFormManagementService: HealthFormManagementService,
    private val outcomeConfDataService: OutcomeConfDataService,
    private val outcomeConfService: OutcomeConfService,
    private val outcomeRequestSchedulingService: OutcomeRequestSchedulingService,
    private val caseRecordService: CaseRecordService
) {
    suspend fun call(
        personId: PersonId,
        healthFormId: UUID,
        answerGroupId: UUID,
        source: HealthFormAnswerSource?
    ) {
        val configs = healthFormOutcomeCalculatorConfService.getByFormId(healthFormId).get()

        if (configs.isEmpty()) return

        val caseRecordIds = findCaseRecordIds(source)

        sendAnswerGroupSourceMetrics(source)

        configs.forEach { config ->
            sendGenericOutcomeMetrics(config.outcomeConfId, caseRecordIds)

            try {
                FromHealthFormCalculator(
                    config,
                    clinicalOutcomeService,
                    outcomeConfDataService,
                    outcomeConfService,
                    personId,
                    healthFormManagementService,
                    answerGroupId,
                    healthFormId,
                    caseRecordIds
                ).calculate()

            } catch(ex: Exception) {
                when {
                    !isMultipleBranchHealthForm(healthFormId) -> throw ex
                    ex is WrongHealthFormCalculatorArgumentsException ||
                    ex is CalculatorThresholdStrategyCouldntMatchConditionException -> {
                        logger.error(
                            "FromHealthFormCalculatorCoordinator - Error getting answer from form",
                            "exception_message" to ex.message,
                            "exception_cause" to ex.cause,
                            "answer_group_id" to answerGroupId,
                            "health_form_id" to healthFormId,
                            "person_id" to personId
                        )
                    }
                    else -> throw ex
                }
            }
        }
    }


    private fun isMultipleBranchHealthForm(healthFormId: UUID) =
        FeatureService.inList(
            namespace = FeatureNamespace.HEALTH_LOGICS,
            key = "is_multiple_branch_health_form",
            testValue = healthFormId.toString(),
            defaultReturn = false
        )

    private suspend fun findCaseRecordIds(source: HealthFormAnswerSource?): List<UUID> {
        if (source != null && source.type == HealthFormAnswerSourceType.OUTCOME_REQUEST_SCHEDULING) {
            val scheduling = outcomeRequestSchedulingService.get(source.id.toUUID()).get()
            val caseRecords = caseRecordService.getByIds(scheduling.caseRecordIds.toList()).get()
            val caseRecordsGroupedByCaseId = caseRecords.groupBy { it.caseId }

            return caseRecordsGroupedByCaseId.values.map { records ->
                records.maxByOrNull { it.addedAt }!!.id
            }
        }
        return emptyList()
    }

    private fun sendAnswerGroupSourceMetrics(source: HealthFormAnswerSource?) {
        val hasSource = source != null
        val sourceId = source?.id
        val sourceType = source?.type
        Metrics.incrementAnswerGroupSource(Result.SUCCESS, hasSource, sourceId, sourceType)
    }

    private fun sendGenericOutcomeMetrics(outcomeConfId: UUID, caseRecordIds: List<UUID>) {
        if (isGenericOutcome(outcomeConfId)) {
            val hasCaseId = caseRecordIds.isNotEmpty()
            Metrics.incrementGenericOutcome(Result.SUCCESS, hasCaseId, caseRecordIds)
        }
    }

    private fun isGenericOutcome(outcomeConfId: UUID): Boolean =
        FeatureService.inList(
            FeatureNamespace.HEALTH_LOGICS,
            "outcome_request_generic_outcome_id",
            testValue = outcomeConfId.toString()
        )
}
