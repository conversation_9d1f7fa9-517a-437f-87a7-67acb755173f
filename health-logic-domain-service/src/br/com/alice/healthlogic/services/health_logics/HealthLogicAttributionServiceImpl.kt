package br.com.alice.healthlogic.services.health_logics

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.models.HealthLogicRecord
import br.com.alice.data.layer.models.HealthLogicStatus
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.healthlogic.client.HealthLogicRecordService
import br.com.alice.healthlogic.client.health_logics.HealthLogicAttributionService
import br.com.alice.healthlogic.client.health_logics.HigherPriorityMatchingRelationshipFinderService
import br.com.alice.healthlogic.models.PersonHealthLogicTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import java.time.LocalDateTime
import java.util.UUID

class HealthLogicAttributionServiceImpl(
    private val healthLogicRecordService: HealthLogicRecordService,
    private val relationshipsFinderService: HigherPriorityMatchingRelationshipFinderService
) : HealthLogicAttributionService {

    override suspend fun assignHealthLogic(
        personId: PersonId,
        healthLogicNode: ServiceScriptNode,
        relationships: List<ServiceScriptRelationship>,
        hDataOverview: HDataOverview,
        caseRecordId: UUID?
    ): Result<PersonHealthLogicTransport, Throwable> {
        val newProgramRelationship = matchRelationships(relationships, hDataOverview)
            ?: return NotFoundException("No matching programs relationships were found").failure()

        val currentHealthLogicRecord = findCurrentProgram(personId, healthLogicNode)

        if (currentHealthLogicRecord?.currentNodeId == newProgramRelationship.nodeChildId)
            return NotFoundException("No new matching programs were found").failure()

        return addToNewProgram(
            newProgramRelationship = newProgramRelationship,
            caseRecordId = caseRecordId,
            currentHealthLogicRecord = currentHealthLogicRecord,
            personId = personId,
            healthLogicNode = healthLogicNode
        )
    }

    private suspend fun matchRelationships(
        relationships: List<ServiceScriptRelationship>,
        hDataOverview: HDataOverview
    ): ServiceScriptRelationship? =
        relationshipsFinderService.findHigherPriority(
            relationships = relationships,
            hDataOverview = hDataOverview
        ).getOrNullIfNotFound()

    private suspend fun findCurrentProgram(
        personId: PersonId,
        healthLogicNode: ServiceScriptNode
    ): HealthLogicRecord? {
        return try {
            healthLogicRecordService.getCurrentHealthLogicRecordByPersonId(
                personId = personId,
                healthLogicId = healthLogicNode.id
            ).get()
        } catch (_: NoSuchElementException) {
            null
        }
    }

    private suspend fun addToNewProgram(
        newProgramRelationship: ServiceScriptRelationship,
        caseRecordId: UUID? = null,
        currentHealthLogicRecord: HealthLogicRecord?,
        personId: PersonId,
        healthLogicNode: ServiceScriptNode
    ): Result<PersonHealthLogicTransport, Throwable> {
        val healthLogicRecord = HealthLogicRecord(
            personId = personId,
            healthLogicNodeId = healthLogicNode.id,
            currentNodeId = newProgramRelationship.nodeChildId!!,
            status = currentHealthLogicRecord?.status ?: HealthLogicStatus.ACTIVE,
            startedAt = currentHealthLogicRecord?.startedAt ?: LocalDateTime.now(),
            referencedLink = HealthLogicRecord.ReferencedLink(
                id = newProgramRelationship.id,
                model = HealthLogicRecord.ReferenceModel.SCRIPT_RELATIONSHIP
            )
        )

        return healthLogicRecordService.create(healthLogicRecord, caseRecordId)
    }

}
