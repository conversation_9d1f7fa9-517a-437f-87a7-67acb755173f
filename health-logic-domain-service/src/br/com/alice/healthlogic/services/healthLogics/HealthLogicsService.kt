package br.com.alice.healthlogic.services.healthLogics

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType.PROGRAM
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus.ACTIVE
import br.com.alice.data.layer.services.ServiceScriptActionDataService
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class HealthLogicsService(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val serviceScriptActionDataService: ServiceScriptActionDataService,
) {

    suspend fun getByHealthConditionId(healthConditionId: UUID) : Result<List<NodeWithRelationships<ServiceScriptNode>>, Throwable> =
        serviceScriptRelationshipDataService.find { where { this.healthConditionField.eq(healthConditionId.toString()).and(this.status.eq(ACTIVE)) } }
            .flatMap { relationships -> findNodes(relationships.filter { it.nodeParentId != null }) }

    suspend fun getByOutcomeConfId(outcomeConfId: UUID) : Result<List<NodeWithRelationships<ServiceScriptNode>>, Throwable> =
        serviceScriptRelationshipDataService.find { where { this.outcomeConfField.eq(outcomeConfId.toString()).and(this.status.eq(ACTIVE)) } }
            .flatMap { relationships -> findNodes(relationships.filter { it.nodeParentId != null }) }

    private suspend fun findNodes(relationships: List<ServiceScriptRelationship>) : Result<List<NodeWithRelationships<ServiceScriptNode>>, Throwable> =
        serviceScriptNodeDataService.find { where { this.id.inList(relationships.map { it.nodeParentId!! }) and this.status.eq(ACTIVE) } }
            .map { nodes ->
                nodes.map { node ->
                    NodeWithRelationships(
                        serviceScriptNode = node,
                        serviceScriptRelationship = relationships.filter { it.nodeParentId == node.id }
                    )
                }
            }

    private suspend fun getProgram(id: UUID): Result<ServiceScriptNode, Throwable> =
        serviceScriptNodeDataService.get(id).flatMap {
            if (it.type != PROGRAM || it.status != ACTIVE) {
                NotFoundException("health_logic_program_not_found").failure()
            } else {
                it.success()
            }
        }

    suspend fun getProgramActions(id: UUID): Result<List<ServiceScriptAction>, Throwable> =
        getProgram(id).flatMap {
            if (it.serviceScriptActionIds.isEmpty()) {
                return@flatMap emptyList<ServiceScriptAction>().success()
            } else {
                serviceScriptActionDataService.find {
                    where { this.id.inList(it.serviceScriptActionIds) }
                }
            }
        }
}
