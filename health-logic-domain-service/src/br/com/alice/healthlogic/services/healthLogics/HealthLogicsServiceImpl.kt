package br.com.alice.healthlogic.services.healthLogics

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.models.ServiceScriptActionStatus
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptNodeType.HEALTH_LOGIC
import br.com.alice.data.layer.models.ServiceScriptNodeType.PROGRAM
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.data.layer.models.ServiceScriptStatus.DELETED
import br.com.alice.data.layer.services.ServiceScriptActionDataService
import br.com.alice.data.layer.services.ServiceScriptNodeDataService
import br.com.alice.data.layer.services.ServiceScriptNodeDataService.FieldOptions
import br.com.alice.data.layer.services.ServiceScriptRelationshipDataService
import br.com.alice.healthlogic.client.HealthLogicsService
import br.com.alice.healthlogic.converters.ProgramUpdateToRelationshipConverter
import br.com.alice.healthlogic.converters.ServiceScriptNodeToHealthLogicConverter
import br.com.alice.healthlogic.converters.ServiceScriptNodeToProgramConverter
import br.com.alice.healthlogic.models.healthLogics.HealthLogic
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgram
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgramLite
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgramUpsertRequest
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class HealthLogicsServiceImpl(
    private val serviceScriptNodeDataService: ServiceScriptNodeDataService,
    private val serviceScriptRelationshipDataService: ServiceScriptRelationshipDataService,
    private val serviceScriptActionDataService: ServiceScriptActionDataService,
): HealthLogicsService {

    override suspend fun findHealthLogics(name: String?, active: Boolean?, range: IntRange): Result<List<HealthLogic>, Throwable> {
        return serviceScriptNodeDataService.find {
            where { this.type.eq(HEALTH_LOGIC).andNamePredicate(name).andStatusPredicate(active) }
                .offset { range.first }
                .limit { range.count() }
                .orderBy { this.name }
        }.mapEach {
            HealthLogic(
                id = it.id,
                name = it.name,
                active = it.status.toBoolean()
            )
        }
    }

    override suspend fun countHealthLogics(name: String?, active: Boolean?): Result<Int, Throwable> {
        return serviceScriptNodeDataService.count {
            where { this.type.eq(HEALTH_LOGIC).andNamePredicate(name).andStatusPredicate(active) }
        }
    }

    override suspend fun get(id: UUID): Result<HealthLogic, Throwable> =
        getHealthLogicNode(id).map { ServiceScriptNodeToHealthLogicConverter.convert(it) }

    override suspend fun create(name:String, active: Boolean): Result<HealthLogic, Throwable> {
        val serviceScriptNode = ServiceScriptNode(
            name = name,
            content = name,
            type = HEALTH_LOGIC,
            status = ServiceScriptStatus.fromBoolean(active)
        )

        return serviceScriptNodeDataService.add(serviceScriptNode).map {
            ServiceScriptNodeToHealthLogicConverter.convert(it)
        }
    }

    override suspend fun update(id: UUID, name:String, active: Boolean): Result<HealthLogic, Throwable> {
        return getHealthLogicNode(id).flatMap {
            serviceScriptNodeDataService.update(it.copy(
                name = name,
                content = name,
                status = ServiceScriptStatus.fromBoolean(active)
            ))
        }.map { ServiceScriptNodeToHealthLogicConverter.convert(it) }
    }

    override suspend fun delete(id: UUID): Result<Boolean, Throwable> {
        return Result.of {
            val healthLogicNode = getHealthLogicNode(id).get()

            serviceScriptNodeDataService.update(
                healthLogicNode.copy(
                    status = DELETED
                )
            ).get()

            true
        }
    }

    override suspend fun createProgram(
        healthLogicId: UUID,
        request: HealthLogicProgramUpsertRequest
    ): Result<HealthLogicProgram, Throwable> =
        Result.of {
            val status = ServiceScriptStatus.fromBoolean(request.active)
            val healthLogicNode = getHealthLogicNode(healthLogicId).get()

            val programNode = serviceScriptNodeDataService.add(ServiceScriptNode(
                name = request.name,
                content = request.name,
                type = PROGRAM,
                status = status,
                serviceScriptActionIds = request.actionIds.distinct(),
                rootNodeId = healthLogicId
            )).get()

            val relationship = ProgramUpdateToRelationshipConverter.convert(
                request,
                programNode,
                healthLogicNode,
                ServiceScriptRelationship(
                    name = "",
                    status = status
                )
            )
            serviceScriptRelationshipDataService.add(relationship).get()
            val actions = getProgramActions(programNode.serviceScriptActionIds).get()

            ServiceScriptNodeToProgramConverter.convert(programNode, relationship, actions)
        }

    override suspend fun updateProgram(
        healthLogicId: UUID,
        programId: UUID,
        request: HealthLogicProgramUpsertRequest
    ): Result<HealthLogicProgram, Throwable> =
        Result.of {
            val status = ServiceScriptStatus.fromBoolean(request.active)
            val currentProgram = getProgramNode(programId).get()
            val currentHealthLogic = getHealthLogicNode(healthLogicId).get()

            return serviceScriptNodeDataService.update(
                currentProgram.copy(
                    name = request.name,
                    content = request.name,
                    status = status,
                    serviceScriptActionIds = request.actionIds.distinct()
                )
            )
                .flatMapPair { getRelationship(currentHealthLogic.id, currentProgram.id) }
                .flatMap { (relationship, program) ->
                    val toUpdate = ProgramUpdateToRelationshipConverter.convert(
                        request,
                        program,
                        currentHealthLogic,
                        relationship
                    )
                    serviceScriptRelationshipDataService.update(toUpdate)
                        .map {
                            val actions = getProgramActions(program.serviceScriptActionIds).get()
                            ServiceScriptNodeToProgramConverter.convert(program, it, actions)
                        }
                }
        }

    override suspend fun deleteProgram(
        healthLogicId: UUID,
        programId: UUID
    ): Result<Boolean, Throwable> {
        return Result.of {
            val currentProgram = getProgramNode(programId).get()
            val currentHealthLogic = getHealthLogicNode(healthLogicId).get()
            val relationship = getRelationship(currentHealthLogic.id, currentProgram.id).get()

            serviceScriptNodeDataService.update(
                currentProgram.copy(
                    status = DELETED
                )
            ).get()

            serviceScriptRelationshipDataService.update(
                relationship.copy(
                    status = DELETED
                )
            ).get()

            true
        }
    }

    override suspend fun findPrograms(healthLogicId: UUID): Result<List<HealthLogicProgramLite>, Throwable> =
        Result.of {
            val children = serviceScriptRelationshipDataService.find {
                where { this.parentId.eq(healthLogicId) and not(this.status.eq(DELETED)) }
            }.coFoldNotFound { emptyList<ServiceScriptRelationship>().success() }.get()

            val childrenIds = children.mapNotNull { it.nodeChildId }

            if (childrenIds.isEmpty()) return@of emptyList<HealthLogicProgramLite>()

            val healthLogicProgramNodes = getHealthLogicProgramNodes(childrenIds).get()

            return healthLogicProgramNodes.map {
                HealthLogicProgramLite(
                    id = it.id,
                    name = it.name,
                    active = it.status.toBoolean(),
                    priority = children.first { child -> child.nodeChildId == it.id }.priority
                )
            }.success()
        }

    override suspend fun getProgramDetails(healthLogicId: UUID, healthLogicProgramId: UUID): Result<HealthLogicProgram, Throwable> =
        Result.of {
            val currentHealthLogic = getHealthLogicNode(healthLogicId).get()
            val currentProgram = getProgramNode(healthLogicProgramId).get()
            val relationship = getRelationship(currentHealthLogic.id, currentProgram.id).get()
            val actions = getProgramActions(currentProgram.serviceScriptActionIds).get()
            ServiceScriptNodeToProgramConverter.convert(currentProgram, relationship, actions)
        }

    override suspend fun getProgramLite(
        healthLogicId: UUID, healthLogicProgramId: UUID
    ): Result<HealthLogicProgramLite, Throwable> =
        Result.of {
            val currentHealthLogic = getHealthLogicNode(healthLogicId).get()
            val currentProgram = getProgramNode(healthLogicProgramId).get()
            val relationship = getRelationship(currentHealthLogic.id, currentProgram.id).get()
            HealthLogicProgramLite(
                id = currentProgram.id,
                name = currentProgram.name,
                active = currentProgram.status.toBoolean(),
                priority = relationship.priority
            )
        }

    override suspend fun getOrCreateAction(type: ServiceScriptAction.ActionType, externalId: UUID): Result<ServiceScriptAction, Throwable> =
        Result.of {
            val action = serviceScriptActionDataService.findOne { where {
                this.type.eq(type) and this.externalId.eq(externalId)
            } }.getOrNullIfNotFound()

            if (action != null) return action.success()

            return serviceScriptActionDataService.add(
                ServiceScriptAction(
                    type = type,
                    externalId = externalId,
                    status = ServiceScriptActionStatus.ACTIVE
                )
            )
        }

    override suspend fun getActions(
        ids: List<UUID>
    ): Result<List<ServiceScriptAction>, Throwable> =
        serviceScriptActionDataService.find { where { id.inList(ids) } }

    private suspend fun getHealthLogicNode(id: UUID): Result<ServiceScriptNode, Throwable> =
        serviceScriptNodeDataService.get(id)
            .flatMap {
                if (it.type == HEALTH_LOGIC && it.status != DELETED) it.success()
                else NotFoundException("missing health logic with this id $id").failure()
            }

    private suspend fun getProgramNode(programNodeId: UUID): Result<ServiceScriptNode, Throwable> =
        serviceScriptNodeDataService.get(programNodeId)
            .flatMap {
                if (it.type == PROGRAM) it.success()
                else NotFoundException("missing program with this id $programNodeId").failure()
            }

    private suspend fun getHealthLogicProgramNodes(ids: List<UUID>): Result<List<ServiceScriptNode>, Throwable> =
        serviceScriptNodeDataService.find {
            where { this.id.inList(ids) and this.type.eq(PROGRAM) and not(this.status.eq(DELETED)) }
        }

    private suspend fun getProgramActions(actionIds: List<UUID>): Result<List<ServiceScriptAction>, Throwable> =
        serviceScriptActionDataService.find {
            where { this.id.inList(actionIds) }
        }.coFoldNotFound { emptyList<ServiceScriptAction>().success() }

    private suspend fun getRelationship(healthLogicId: UUID, programId: UUID): Result<ServiceScriptRelationship, Throwable> =
        serviceScriptRelationshipDataService.findOne {
            where {
                parentId.eq(healthLogicId) and childId.eq(programId)
            }
        }

    @OptIn(LikePredicateUsage::class)
    private fun Predicate.andNamePredicate(name: String?): Predicate {
        if (name == null ) return this

        return this and Predicate.like(FieldOptions().name, name)
    }


    private fun Predicate.andStatusPredicate(active: Boolean?): Predicate {
        if (active == null ) return this and not(Predicate.eq(FieldOptions().status, DELETED))

        return this and Predicate.eq(FieldOptions().status, ServiceScriptStatus.fromBoolean(active))
    }
}
