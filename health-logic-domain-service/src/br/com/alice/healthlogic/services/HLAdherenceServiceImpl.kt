package br.com.alice.healthlogic.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.data.layer.models.AdherenceRecommendedAction
import br.com.alice.data.layer.models.AdherenceValidation
import br.com.alice.data.layer.models.HLAdherence
import br.com.alice.data.layer.models.HLAdherence.AcceptedRecommendation
import br.com.alice.data.layer.models.HLAdherence.AdherenceResultType
import br.com.alice.data.layer.models.HLAdherence.AdherenceStatus
import br.com.alice.data.layer.services.HLAdherenceDataService
import br.com.alice.healthlogic.client.HLAdherenceService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class HLAdherenceServiceImpl(
    private val hlAdherenceDataService: HLAdherenceDataService
) : HLAdherenceService {

    override suspend fun upsert(
        taskId: UUID,
        personId: PersonId,
        staffId: UUID,
        validationModels: List<AdherenceRecommendedAction>,
        resultType: AdherenceResultType,
        healthConditionId: UUID?,
        forceValidation: Boolean,
    ): Result<HLAdherence, Throwable> =
        getByTaskId(taskId)
            .flatMap { hlAdherence ->
                update(
                    hlAdherence,
                    hlAdherence.copy(
                        validationHistory = addValidation(
                            staffId = staffId,
                            validationModels = validationModels,
                            resultType = resultType,
                            hlAdherence = hlAdherence,
                            healthConditionId = healthConditionId
                        ),
                    ),
                    forceValidation = forceValidation
                )
            }
            .coFoldNotFound {
                add(taskId, personId, staffId, validationModels, resultType, healthConditionId)
            }

    override suspend fun getByTaskId(taskId: UUID): Result<HLAdherence, Throwable> =
        hlAdherenceDataService.findOne {
            where { this.taskId.eq(taskId) }
        }

    override suspend fun getByTaskIds(taskIds: List<UUID>): Result<List<HLAdherence>, Throwable> =
        hlAdherenceDataService.find {
            where { this.taskId.inList(taskIds) }
        }

    override suspend fun recommendation(
        acceptedRecommendation: AcceptedRecommendation,
        taskId: UUID,
        staffId: UUID,
        validationModels: List<AdherenceRecommendedAction>,
        resultType: AdherenceResultType,
        healthConditionId: UUID?
    ): Result<HLAdherence, Throwable> =
        getByTaskId(taskId).flatMap { hlAdherence ->
            update(
                hlAdherence,
                hlAdherence.copy(
                    acceptedRecommendation = acceptedRecommendation,
                    status = AdherenceStatus.VALIDATED,
                    validationHistory = addValidation(
                        staffId = staffId,
                        validationModels = validationModels,
                        resultType = resultType,
                        hlAdherence = hlAdherence,
                        healthConditionId = healthConditionId
                    ),
                )
            )
        }

    override suspend fun cancel(taskId: UUID): Result<HLAdherence, Throwable> =
        getByTaskId(taskId).flatMap {
            update(
                it, it.copy(
                    status = AdherenceStatus.CANCELED,
                    acceptedRecommendation = AcceptedRecommendation.REFUSED
                )
            )
        }

    override suspend fun getEmptyValidationHistory(range: IntRange): Result<List<HLAdherence>, Throwable> =
        hlAdherenceDataService.find {
            where { this.validationHistory.isEmpty() }.offset { range.first }.limit { range.count() }
        }

    private fun addValidation(
        staffId: UUID,
        validationModels: List<AdherenceRecommendedAction>,
        resultType: AdherenceResultType,
        hlAdherence: HLAdherence,
        healthConditionId: UUID?
    ) =
        buildValidationHistory(
            validationModels = validationModels,
            staffId = staffId,
            resultType = resultType,
            healthConditionId = healthConditionId
        ) + hlAdherence.validationHistory

    private suspend fun add(
        taskId: UUID,
        personId: PersonId,
        staffId: UUID,
        validationModels: List<AdherenceRecommendedAction>,
        resultType: AdherenceResultType,
        healthConditionId: UUID?
    ) = hlAdherenceDataService.add(

        HLAdherence(
            taskId = taskId,
            acceptedRecommendation = AcceptedRecommendation.PENDING,
            status = AdherenceStatus.PENDING,
            personId = personId,
            comment = null,
            validationHistory = buildValidationHistory(
                staffId = staffId,
                resultType = resultType,
                validationModels = validationModels,
                healthConditionId = healthConditionId
            )
        )
    )

    private fun buildValidationHistory(
        validationModels: List<AdherenceRecommendedAction>,
        staffId: UUID,
        resultType: AdherenceResultType,
        healthConditionId: UUID?
    ) = validationModels
        .takeIf { it.isNotEmpty() }?.map { validationModel ->
            AdherenceValidation(
                staffId = staffId,
                resultType = resultType,
                validationModel = validationModel,
                healthConditionId = healthConditionId
            )
        }?.toList() ?: listOf(
            AdherenceValidation(
                staffId = staffId,
                resultType = resultType,
                healthConditionId = healthConditionId
            )
        )

    private suspend fun update(
        hlAdherenceOld: HLAdherence,
        hlAdherenceNew: HLAdherence,
        forceValidation: Boolean = false
    ): Result<HLAdherence, Throwable> {
        if (!forceValidation) {
            hlAdherenceOld.takeIf { it.status != AdherenceStatus.PENDING }?.let {
                throw IllegalAccessException("Cannot be updated if status is other than pending")
            }
        }
        return hlAdherenceDataService.update(hlAdherenceNew)
    }
}
