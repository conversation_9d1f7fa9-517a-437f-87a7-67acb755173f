package br.com.alice.healthlogic.services

import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CalculatorConfStatus
import br.com.alice.data.layer.models.ClinicalOutcomesConsolidatedCalculatorConf
import br.com.alice.data.layer.services.ClinicalOutcomesConsolidatedCalculatorConfDataService
import br.com.alice.healthlogic.client.ClinicalOutcomesConsolidatedCalculatorConfService
import com.github.kittinunf.result.flatMap
import java.util.UUID

class ClinicalOutcomesConsolidatedCalculatorConfServiceImpl(
    private val confDataService: ClinicalOutcomesConsolidatedCalculatorConfDataService
) : ClinicalOutcomesConsolidatedCalculatorConfService,
    Adder<ClinicalOutcomesConsolidatedCalculatorConf> by confDataService,
    Getter<ClinicalOutcomesConsolidatedCalculatorConf> by confDataService {

    override suspend fun inactivate(configurationId: UUID) =
        confDataService.get(configurationId)
            .flatMap {
                confDataService.update(
                    it.copy(status = CalculatorConfStatus.INACTIVE)
                )
            }

    override suspend fun getByOutcomeConfIdUsedForCalculation(outcomeConfId: UUID) =
        confDataService.find {
            where {
                this.outcomeConfIds.contains(outcomeConfId) and
                        this.status.eq(CalculatorConfStatus.ACTIVE)
            }
        }

    override suspend fun getByOutcomeConfId(outcomeConfId: UUID) =
        confDataService.find {
            where {
                this.outcomeConfId.eq(outcomeConfId) and
                    this.status.eq(CalculatorConfStatus.ACTIVE)
            }
        }
}
