package br.com.alice.healthlogic.routes


import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.healthlogic.controllers.CronTasksController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.cronTasksRoutes() {
    val cronTasksController by inject<CronTasksController>()

    route("/recurring_subscribers") {
        post("/process_daily_outcome_request_scheduling") {
            asyncLayer {
                coHandler(cronTasksController::processDailyOutcomeRequestScheduling)
            }
        }
    }
}
