package br.com.alice.healthlogic.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.healthlogic.controllers.BudBusBackfillController
import br.com.alice.healthlogic.controllers.ClinicalOutcomeBackfillController
import br.com.alice.healthlogic.controllers.HDataOverviewBackfillController
import br.com.alice.healthlogic.controllers.HLAdherenceBackfill
import br.com.alice.healthlogic.controllers.HealthDemandMonitoringBackfillController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    val clinicalOutcomeBackfillController by inject<ClinicalOutcomeBackfillController>()
    val hDataOverviewBackfillController by inject<HDataOverviewBackfillController>()
    val hLAdherenceBackfill by inject<HLAdherenceBackfill>()
    val budBusBackfill by inject<BudBusBackfillController>()
    val healthDemandMonitoringBackfillController by inject<HealthDemandMonitoringBackfillController>()

    route("/backfill") {
        post("/clinical-outcome") {
            asyncLayer {
                coHandler(clinicalOutcomeBackfillController::backfillOutcome)
            }
        }
        post("/h-data-overview/month-year-birth") {
            asyncLayer {
                coHandler(hDataOverviewBackfillController::backfillMonthYearBirth)
            }
        }
        post("/hl-adherence") {
            asyncLayer {
                coHandler(hLAdherenceBackfill::validationHistoryBackfill)
            }
        }
        post("/bus_protocol") {
            asyncLayer {
                coHandler(budBusBackfill::createBusProtocol)
            }
        }
        put("/node") {
            asyncLayer {
                coHandler(budBusBackfill::updateNode)
            }
        }
        put("/relationship") {
            asyncLayer {
                coHandler(budBusBackfill::updateRelationship)
            }
        }
        put("/health-demand-monitoring/update-outcome-evolution-range") {
            asyncLayer {
                coHandler(healthDemandMonitoringBackfillController::setOutcomeEvolutionRange)
            }
        }
    }
}
