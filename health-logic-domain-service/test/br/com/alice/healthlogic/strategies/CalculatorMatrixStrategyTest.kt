package br.com.alice.healthlogic.strategies

import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CalculatorConf
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.data.layer.models.OutcomeConf.OutcomeType.CLINICAL
import br.com.alice.healthlogic.client.OutcomeConfService
import br.com.alice.healthlogic.util.EuroqolReference
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertFailsWith

internal class CalculatorMatrixStrategyTest {
    private val outcomeConfService: OutcomeConfService = mockk()

    private val outcomeConf = OutcomeConf(
        id = RangeUUID.generate(),
        type = CLINICAL,
        key = "EUROQOL",
        description = "EUROQOL",
    )


    private val config = TestModelFactory.buildHealthFormOutcomeCalculatorConf(
        CalculatorConf.Strategy.MATRIX,
    )

    @Test
    fun `#calculate - returns outcome if correct params and type Euroqol`(): Unit = runBlocking {
        val questionId1 = RangeUUID.generate()
        val questionId2 = RangeUUID.generate()
        val questionId3 = RangeUUID.generate()
        val questionId4 = RangeUUID.generate()
        val questionId5 = RangeUUID.generate()

        val answers = mapOf(
            questionId1 to 1.toDouble(),
            questionId5 to 5.toDouble(),
            questionId4 to 4.toDouble(),
            questionId3 to 3.toDouble(),
            questionId2 to 2.toDouble(),
        )

        val questions = listOf(
            questionId1, questionId2, questionId3, questionId4, questionId5
        )

        val config = config.copy(
            questionIds = questions,
        )

        coEvery { outcomeConfService.get(config.outcomeConfId) } returns outcomeConf.success()

        mockkObject(EuroqolReference) {
            val response = CalculatorMatrixStrategy(
                config,
                answers,
                config.questionIds,
                outcomeConfService
            ).calculate()

            assertThat(response).isEqualTo("0.3700".toBigDecimal())
            coVerifyOnce { EuroqolReference.getReferenceValue("12345") }
        }
    }

    @Test
    fun `#calculate - throws error if missing answer`(): Unit = runBlocking {
        assertFailsWith<NullPointerException> {
            val questions = listOf(RangeUUID.generate())
            val answers = emptyMap<UUID, Double>()

            val config = config.copy(
                questionIds = questions,
            )

            coEvery { outcomeConfService.get(config.outcomeConfId) } returns outcomeConf.success()

            CalculatorMatrixStrategy(
                config,
                answers,
                config.questionIds,
                outcomeConfService
            ).calculate()
        }
    }

    @Test
    fun `#calculate - throws error if wrong conf type`(): Unit = runBlocking {
        assertFailsWith<OutcomeNotMatrixStrategyException> {
            val questions = listOf(RangeUUID.generate())
            val answers = mapOf(questions.first() to 1.toDouble())
            val outcomeConf = outcomeConf.copy(key = "ANY_KEY")
            val config = config.copy(
                outcomeConfId = outcomeConf.id
            )

            coEvery { outcomeConfService.get(config.outcomeConfId) } returns outcomeConf.success()

            CalculatorMatrixStrategy(
                config,
                answers,
                config.questionIds,
                outcomeConfService
            ).calculate()
        }
    }
}
