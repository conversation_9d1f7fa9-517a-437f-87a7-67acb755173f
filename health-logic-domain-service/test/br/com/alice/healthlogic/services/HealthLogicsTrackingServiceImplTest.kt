package br.com.alice.healthlogic.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthLogicTracking
import br.com.alice.data.layer.services.HealthLogicTrackingDataService
import br.com.alice.data.layer.services.ProtocolTrackingDataService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

internal class HealthLogicsTrackingServiceImplTest {
    private val healthLogicTrackingDataService: HealthLogicTrackingDataService = mockk()
    private val protocolTrackingDataService: ProtocolTrackingDataService = mockk()
    private val service = HealthLogicsTrackingServiceImpl(healthLogicTrackingDataService, protocolTrackingDataService)

    private val healthLogicTracking = HealthLogicTracking(
        type = HealthLogicTracking.HealthLogicTrackingModel.HEALTH_PLAN_TASK,
        externalId = RangeUUID.generate(),
        serviceScriptActionId = RangeUUID.generate(),
        healthLogicRecordId = RangeUUID.generate()
    )

    private val protocolTracking = TestModelFactory.buildProtocolTracking()

    @AfterTest
    fun confirmVerify() {
        confirmVerified(
            healthLogicTrackingDataService,
            protocolTrackingDataService
        )
        clearAllMocks()
    }

    @Test
    fun `#find calls data service with correct params`() = runBlocking {
        coEvery { healthLogicTrackingDataService.findOne(queryEq {
            where {
                this.serviceScriptActionId.eq(healthLogicTracking.serviceScriptActionId) and
                        this.healthLogicRecordId.eq(healthLogicTracking.healthLogicRecordId) }
        }) } returns healthLogicTracking.success()

        val result = service.find(healthLogicTracking.serviceScriptActionId, healthLogicTracking.healthLogicRecordId)
        assertThat(result).isSuccessWithData(healthLogicTracking)

        coVerifyOnce { healthLogicTrackingDataService.findOne(any()) }
    }

    @Test
    fun `#create calls data service with correct params`() = runBlocking {
        coEvery { healthLogicTrackingDataService.add(healthLogicTracking) } returns healthLogicTracking.success()

        val result = service.create(healthLogicTracking)
        assertThat(result).isSuccessWithData(healthLogicTracking)

        coVerifyOnce { healthLogicTrackingDataService.add(any()) }
    }

    @Test
    fun `#findByProtocol calls data service with correct params`() = runBlocking {
        coEvery { protocolTrackingDataService.findOne(queryEq {
            where {
                this.serviceScriptActionId.eq(protocolTracking.serviceScriptActionId) and
                        this.serviceScriptNavigationId.eq(protocolTracking.serviceScriptNavigationId) }
        }) } returns protocolTracking.success()

        val result = service.findByProtocol(protocolTracking.serviceScriptActionId, protocolTracking.serviceScriptNavigationId)
        assertThat(result).isSuccessWithData(protocolTracking)

        coVerifyOnce { protocolTrackingDataService.findOne(any()) }
    }

    @Test
    fun `#createByProtocol calls data service with correct params`() = runBlocking {
        coEvery { protocolTrackingDataService.add(protocolTracking) } returns protocolTracking.success()

        val result = service.createByProtocol(protocolTracking)
        assertThat(result).isSuccessWithData(protocolTracking)

        coVerifyOnce { protocolTrackingDataService.add(any()) }
    }
}
