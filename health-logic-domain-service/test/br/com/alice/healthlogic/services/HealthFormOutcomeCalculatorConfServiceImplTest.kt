package br.com.alice.healthlogic.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CalculatorConfStatus
import br.com.alice.data.layer.services.HealthFormOutcomeCalculatorConfDataService
import br.com.alice.data.layer.services.HealthFormOutcomeCalculatorConfDataService.FieldOptions
import br.com.alice.data.layer.services.HealthFormOutcomeCalculatorConfDataService.OrderingOptions
import br.com.alice.healthlogic.logics.builders.HFormOutcomeCalculatorConfQueryBuilder
import br.com.alice.healthlogic.models.HFormOutcomeCalculatorConfFilters
import com.github.kittinunf.result.success
import io.mockk.EqMatcher
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkConstructor
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class HealthFormOutcomeCalculatorConfServiceImplTest {
    private val dataService: HealthFormOutcomeCalculatorConfDataService = mockk()
    private val service = HealthFormOutcomeCalculatorConfServiceImpl(dataService)

    private val outcomeConf = TestModelFactory.buildHealthFormOutcomeCalculatorConf()

    @AfterTest
    fun confirmVerify() {
        confirmVerified(dataService)
        clearAllMocks()
    }

    @Test
    fun `#updateStatus from outcome`() = runBlocking {
        val updatedOutcomeConf = outcomeConf.copy(
            status = CalculatorConfStatus.INACTIVE
        )

        coEvery { dataService.get(outcomeConf.id) } returns outcomeConf.success()
        coEvery { dataService.update(updatedOutcomeConf) } returns updatedOutcomeConf.success()

        val result = service.updateStatus(updatedOutcomeConf.id, CalculatorConfStatus.INACTIVE)

        assertThat(result).isSuccessWithData(updatedOutcomeConf)

        coVerifyOnce { dataService.get(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#getByFormId`() = runBlocking {
        coEvery {
            dataService.find(queryEq {
                where { this.healthFormId.eq(outcomeConf.healthFormId) and this.status.eq(CalculatorConfStatus.ACTIVE) }
            })
        } returns listOf(outcomeConf).success()

        val result = service.getByFormId(outcomeConf.healthFormId)
        assertThat(result).isSuccessWithData(listOf(outcomeConf))

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#getByQuestionId`() = runBlocking {
        coEvery {
            dataService.find(queryEq {
                where {
                    this.questionId.contains(outcomeConf.questionIds.first()) and
                            this.status.eq(CalculatorConfStatus.ACTIVE)
                }
            })
        } returns listOf(outcomeConf).success()

        val result = service.getByQuestionId(outcomeConf.questionIds.first())
        assertThat(result).isSuccessWithData(listOf(outcomeConf))

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#getByOutcomeConfIds`() = runBlocking {
        val outcomeConfId = outcomeConf.outcomeConfId
        coEvery {
            dataService.find(queryEq {
                where {
                    this.outcomeConfId.inList(listOf(outcomeConfId)) and
                        this.status.eq(CalculatorConfStatus.ACTIVE)
                }
            })
        } returns listOf(outcomeConf).success()

        val result = service.getByOutcomeConfIds(listOf(outcomeConfId))
        assertThat(result).isSuccessWithData(listOf(outcomeConf))

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#find`() = runBlocking {
        val filters = HFormOutcomeCalculatorConfFilters(range = IntRange(0, 9))
        val mockedQuery = QueryBuilder(
            FieldOptions(),
            OrderingOptions()
        ).where { this.healthFormId.eq(RangeUUID.generate()) }

        mockkConstructor(HFormOutcomeCalculatorConfQueryBuilder::class)
        coEvery { constructedWith<HFormOutcomeCalculatorConfQueryBuilder>(
            EqMatcher(dataService),
            EqMatcher(filters),
        ).build() } returns mockedQuery

        coEvery { dataService.find(queryEq {
            mockedQuery.offset { 0 }.limit { 10 }
        }) } returns listOf(outcomeConf).success()

        val result = service.find(filters)
        assertThat(result).isSuccessWithData(listOf(outcomeConf))

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#count`() = runBlocking {
        val filters = HFormOutcomeCalculatorConfFilters(range = IntRange(0, 9))
        val mockedQuery = mockk<QueryBuilder<FieldOptions, OrderingOptions>>()

        mockkConstructor(HFormOutcomeCalculatorConfQueryBuilder::class)
        coEvery { constructedWith<HFormOutcomeCalculatorConfQueryBuilder>(
            EqMatcher(dataService),
            EqMatcher(filters),
        ).build() } returns mockedQuery

        coEvery { dataService.count(queryEq { mockedQuery }) } returns 15.success()

        val result = service.count(filters)
        assertThat(result).isSuccessWithData(15)

        coVerifyOnce { dataService.count(any()) }
    }
}
