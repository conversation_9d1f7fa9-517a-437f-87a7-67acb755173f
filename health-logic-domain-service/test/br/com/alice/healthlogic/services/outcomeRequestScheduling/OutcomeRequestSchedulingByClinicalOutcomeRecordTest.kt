package br.com.alice.healthlogic.services.outcomeRequestScheduling

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.models.HealthDemandMonitoring
import br.com.alice.data.layer.models.OutcomeRequestScheduling.SchedulingStatus
import br.com.alice.healthlogic.client.ClinicalOutcomeService
import br.com.alice.healthlogic.client.HealthDemandMonitoringService
import br.com.alice.healthlogic.client.OutcomeRequestSchedulingService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class OutcomeRequestSchedulingByClinicalOutcomeRecordTest {
    private val healthDemandMonitoringService: HealthDemandMonitoringService = mockk()
    private val clinicalOutcomeRecordService: ClinicalOutcomeService = mockk()
    private val outcomeRequestSchedulingService: OutcomeRequestSchedulingService = mockk()
    private val outcomeRequestSchedulingByClinicalOutcomeRecord: OutcomeRequestSchedulingByClinicalOutcomeRecord =
        OutcomeRequestSchedulingByClinicalOutcomeRecord(
            healthDemandMonitoringService,
            clinicalOutcomeRecordService,
            outcomeRequestSchedulingService
        )

    private val outcomeConfId = RangeUUID.generate()
    private val caseRecordId = RangeUUID.generate()
    private val healthConditionId = RangeUUID.generate()
    private val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring(
        outcomeId = outcomeConfId,
        healthConditionId = healthConditionId
    )
    private val clinicalOutcome = HDataOverview.ClinicalOutcome(
        id = RangeUUID.generate(),
        addedAt = LocalDateTime.now(),
        outcomeConfId = outcomeConfId,
        outcome = BigDecimal.ONE,
        overviewUpdatedAt = LocalDateTime.now()
    )
    private val hDemand = HDataOverview.HDemand(
        id = RangeUUID.generate(),
        addedAt = LocalDateTime.now(),
        severity = CaseSeverity.COMPENSATED,
        status = CaseStatus.ACTIVE,
        healthConditionId = healthConditionId,
    )
    private val clinicalOutcomeRecord = TestModelFactory.buildClinicalOutcomeRecord(
        outcomeConfId = outcomeConfId,
        caseRecordId = caseRecordId
    )

    private val hDataOverview = TestModelFactory.buildHDataOverview(
        healthDemands = mapOf(caseRecordId to hDemand),
        outcomeRecords = mapOf(outcomeConfId to clinicalOutcome)
    )
    private val outcomeRequestScheduling = TestModelFactory.buildOutcomeRequestScheduling(
        status = SchedulingStatus.PENDING,
        scheduledFor = clinicalOutcome.addedAt.plusDays(30),
        healthDemandMonitoringId = healthDemandMonitoring.id
    )

    @BeforeTest
    fun setup() {
        mockkObject(logger)
    }

    @AfterTest
    fun clear() {
        confirmVerified(
            healthDemandMonitoringService,
            clinicalOutcomeRecordService,
            outcomeRequestSchedulingService
        )
        clearAllMocks()
    }

    @Test
    fun `#setOutcomeRequestScheduling - cancel old schedule Outcome Request, create new one and return true when isn't generic outcome`() =
        runBlocking {
            val canceledOutcomeRequestScheduling = outcomeRequestScheduling.copy(status = SchedulingStatus.CANCELLED)

            coEvery {
                healthDemandMonitoringService.findByOutcomeConfId(outcomeConfId = outcomeConfId)
            } returns listOf(healthDemandMonitoring).success()

            coEvery {
                outcomeRequestSchedulingService.findToBeExecuted(
                    personId = hDataOverview.personId,
                    healthDemandMonitoringId = healthDemandMonitoring.id,
                )
            } returns outcomeRequestScheduling.success()

            coEvery {
                outcomeRequestSchedulingService.update(canceledOutcomeRequestScheduling)
            } returns canceledOutcomeRequestScheduling.success()

            coEvery {
                outcomeRequestSchedulingService.add(match {
                    it.status == outcomeRequestScheduling.status &&
                            it.scheduledFor == outcomeRequestScheduling.scheduledFor &&
                            it.healthDemandMonitoringId == outcomeRequestScheduling.healthDemandMonitoringId &&
                            it.caseRecordIds == outcomeRequestScheduling.caseRecordIds
                })
            } returns outcomeRequestScheduling.success()

            val result =
                outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(hDataOverview, outcomeConfId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { healthDemandMonitoringService.findByOutcomeConfId(any()) }
            coVerifyOnce { outcomeRequestSchedulingService.findToBeExecuted(any(), any()) }
            coVerifyOnce { outcomeRequestSchedulingService.add(any()) }
            coVerifyOnce { outcomeRequestSchedulingService.update(any()) }
        }

    @Test
    fun `#setOutcomeRequestScheduling - return false when isn't generic outcome and does not have HealthDemandMonitoring `() =
        runBlocking {
            coEvery {
                healthDemandMonitoringService.findByOutcomeConfId(outcomeConfId = outcomeConfId)
            } returns NotFoundException().failure()

            val result =
                outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(hDataOverview, outcomeConfId)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { healthDemandMonitoringService.findByOutcomeConfId(any()) }
            coVerifyOnce {
                logger.warn(
                    "Health Demand Monitoring not found",
                    "outcome_conf_id" to outcomeConfId
                )
            }
        }

    @Test
    fun `#setOutcomeRequestScheduling - returns a specific exception when generic outcome does not have caseRecordId as source`() =
        runBlocking {
            coEvery {
                clinicalOutcomeRecordService.getById(id = clinicalOutcome.id)
            } returns clinicalOutcomeRecord.copy(caseRecordId = null).success()

            withFeatureFlag(
                FeatureNamespace.HEALTH_LOGICS,
                "outcome_request_generic_outcome_id",
                listOf(clinicalOutcomeRecord.outcomeConfId.toString())
            ) {
                val result =
                    outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(hDataOverview, outcomeConfId)
                assertThat(result).isFailureOfType(GenericOutcomeMissingCaseRecordIdException::class)
            }

            coVerifyOnce { clinicalOutcomeRecordService.getById(any()) }
        }

    @Test
    fun `#setOutcomeRequestScheduling - does nothing and return true when isn't generic outcome and does not have a HDemand`() =
        runBlocking {
            coEvery {
                healthDemandMonitoringService.findByOutcomeConfId(outcomeConfId = outcomeConfId)
            } returns listOf(healthDemandMonitoring).success()

            coEvery {
                outcomeRequestSchedulingService.findToBeExecuted(
                    personId = hDataOverview.personId,
                    healthDemandMonitoringId = healthDemandMonitoring.id,
                )
            } returns outcomeRequestScheduling.success()

            val result = outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(
                hDataOverview.copy(healthDemands = emptyMap()),
                outcomeConfId
            )

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { healthDemandMonitoringService.findByOutcomeConfId(any()) }
            coVerifyOnce { outcomeRequestSchedulingService.findToBeExecuted(any(), any()) }
            coVerifyOnce {
                logger.warn(
                    "Health Demand not found",
                    "outcome_conf_id" to outcomeConfId,
                    "health_condition_id" to healthDemandMonitoring.healthConditionId
                )
            }
        }

    @Test
    fun `#setOutcomeRequestScheduling - does nothing and return true when isn't generic outcome and has already executed the scheduling`() =
        runBlocking {
            val expectedOutcomeRequestScheduling = outcomeRequestScheduling.copy(caseRecordIds = setOf(hDemand.id))

            coEvery {
                healthDemandMonitoringService.findByOutcomeConfId(outcomeConfId = outcomeConfId)
            } returns listOf(healthDemandMonitoring).success()

            coEvery {
                outcomeRequestSchedulingService.findToBeExecuted(
                    personId = hDataOverview.personId,
                    healthDemandMonitoringId = healthDemandMonitoring.id,
                )
            } returns expectedOutcomeRequestScheduling.success()

            val result =
                outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(hDataOverview, outcomeConfId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { healthDemandMonitoringService.findByOutcomeConfId(any()) }
            coVerifyOnce { outcomeRequestSchedulingService.findToBeExecuted(any(), any()) }
            coVerifyOnce {
                logger.info(
                    "Schedule already Executed",
                    "outcome_conf_id" to outcomeConfId,
                    "case_record_id" to hDemand.id
                )
            }
        }

    @Test
    fun `#setOutcomeRequestScheduling - cancel old schedule Outcome Request and does not create a new when isn't generic outcome and does not have schedule in loop`() =
        runBlocking {
            val canceledOutcomeRequestScheduling = outcomeRequestScheduling.copy(status = SchedulingStatus.CANCELLED)
            val newHealthDemandMonitoring = healthDemandMonitoring.copy(
                schedulingConfig = listOf(
                    HealthDemandMonitoring.SchedulingConfig(
                        severity = CaseSeverity.COMPENSATED,
                        daysToFirstSchedule = 10,
                        scheduleInLoop = false,
                        schedulingInterval = null
                    )
                )
            )

            coEvery {
                healthDemandMonitoringService.findByOutcomeConfId(outcomeConfId = outcomeConfId)
            } returns listOf(newHealthDemandMonitoring).success()

            coEvery {
                outcomeRequestSchedulingService.findToBeExecuted(
                    personId = hDataOverview.personId,
                    healthDemandMonitoringId = healthDemandMonitoring.id,
                )
            } returns outcomeRequestScheduling.success()

            coEvery {
                outcomeRequestSchedulingService.update(canceledOutcomeRequestScheduling)
            } returns canceledOutcomeRequestScheduling.success()

            val result =
                outcomeRequestSchedulingByClinicalOutcomeRecord.scheduleOutcomeRequest(hDataOverview, outcomeConfId)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { healthDemandMonitoringService.findByOutcomeConfId(any()) }
            coVerifyOnce { outcomeRequestSchedulingService.findToBeExecuted(any(), any()) }
            coVerifyOnce { outcomeRequestSchedulingService.update(any()) }
        }
}
