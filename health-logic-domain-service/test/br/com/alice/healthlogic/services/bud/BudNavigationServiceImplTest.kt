package br.com.alice.healthlogic.services.bud

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.healthlogic.client.ScreeningNavigationService
import br.com.alice.healthlogic.models.bud.BudNavigation
import br.com.alice.healthlogic.models.bud.EndScreenData
import br.com.alice.healthlogic.models.bud.LinkData
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import br.com.alice.healthlogic.services.triage.appointmentschedule.TriageAppointmentScheduleService
import br.com.alice.healthlogic.services.triage.prescription.TriagePrescriptionService
import br.com.alice.healthlogic.ui.factory.BudNodeEndScreenFactory
import br.com.alice.healthlogic.ui.factory.BudNodeScreenFactory
import br.com.alice.healthlogic.ui.factory.DropTriageScreenFactory
import br.com.alice.healthlogic.ui.factory.TriagePrescriptionEndScreenFactory
import br.com.alice.screening.client.ScreenInput
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test

internal class BudNavigationServiceImplTest {
    private val screeningNavigationService: ScreeningNavigationService = mockk()
    private val budAdministrationService: BudNodeCacheDataService = mockk()
    private val chatService: ChatService = mockk()
    private val navigationService: NavigationService = mockk()
    private val triagePrescriptionService: TriagePrescriptionService = mockk()
    private val budScriptActionService: BudScriptActionService = mockk()
    private val triageAppointmentScheduleService: TriageAppointmentScheduleService = mockk()

    private val service = BudNavigationServiceImpl(
        screeningNavigationService,
        budAdministrationService,
        chatService,
        navigationService,
        triagePrescriptionService,
        budScriptActionService,
        triageAppointmentScheduleService
    )

    private val appVersion = "3.12.0"
    private val personId = PersonId()
    private val healthConditions = listOf(RangeUUID.generate(), RangeUUID.generate(), RangeUUID.generate())
    private val budNode = TestModelFactory.buildBudNode(
        type = BudNode.BudNodeType.SCRIPT
    )
    private val symptomsOption = Option(
        selectionType = SelectionType.MULTIPLE,
        type = OptionType.SYMPTOMS,
        answers = listOf(
            Answer(
                title = "Febre",
                externalId = healthConditions[0]
            ),
            Answer(
                title = "Dor de cabeça",
                externalId = healthConditions[1]
            )
        )
    )

    private val budNodeChild = TestModelFactory.buildBudNode(
        type = BudNode.BudNodeType.QUESTION_WITH_OPTIONS,
        option = symptomsOption
    )
    private val relationship = TestModelFactory.buildServiceScriptRelationship(
        parentId = budNode.id,
        childId = budNodeChild.id,
        conditions = listOf(
            Condition(
                key = ConditionOptions.HEALTH_DEMAND_ID.key,
                operator = ServiceScriptOperator.CONTAINS,
                value = listOf(healthConditions[0], healthConditions[2])
            )
        )
    )
    private var screeningNavigation = TestModelFactory.buildScreeningNavigation(
        personId = personId,
        status = ScreeningNavigationStatus.STARTED
    )
    private val navigationGroup = TestModelFactory.buildServiceScriptNavigationGroup(scriptNodeId = budNode.id)
    private val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
        previousNodeId = budNode.id,
        currentNodeId = budNodeChild.id,
        relationshipId = relationship.id,
        groupId = navigationGroup.id,
        type = ServiceScriptNavigationType.AUTOMATIC
    )
    private var budNavigation = BudNavigation(
        personId = personId,
        previousNodeId = budNode.id,
        scriptNodeId = budNode.id,
        relationshipId = relationship.id,
        type = ServiceScriptNavigationType.AUTOMATIC,
        source = ServiceScriptNavigationSource(
            id = screeningNavigation.id.toString(),
            type = ServiceScriptNavigationSourceType.SCREENING_NAVIGATION
        )
    )
    private val channelDocument = ChannelDocument(
        id = "channelId",
        personId = personId.toString(),
        channelPersonId = ""
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#startNavigation - should start navigation and return screen with first question when match conditions`() = runBlocking {
        val pillScreen = BudNodeScreenFactory.build(
            budNode = budNodeChild,
            showBackLink = false,
            screeningNavigationId = screeningNavigation.id.toString()
        )!!

        val  nodeWithRelationships =  NodeWithRelationships(
            serviceScriptNode = budNode,
            serviceScriptRelationship = listOf(relationship)
        )

        coEvery { screeningNavigationService.startScreeningNavigation(personId) } returns screeningNavigation.success()
        coEvery { navigationService.startNavigation(budNode.id, personId, budNavigation.source!!) } returns nodeWithRelationships
        coEvery {
            navigationService.goTo(budNodeChild.id, budNavigation.copy(scriptNodeId = budNodeChild.rootNodeId!!))
        } returns nodeWithRelationships.copy(navigation = budNavigation.copy(groupId = serviceScriptNavigation.groupId))
        coEvery { budAdministrationService.get(budNodeChild.id) } returns budNodeChild.success()

        val result = service.startNavigation(personId, budNode.id, "3.12.0")

        assertThat(result).isSuccessWithData(pillScreen)
    }

    @Test
    fun `#startNavigation - should return endScreen when there is no relationship`() = runBlocking {
        val endScreen = BudNodeEndScreenFactory.build(
            screeningNavigationId = budNavigation.source!!.id,
            channelId = channelDocument.id!!,
            budNode = budNode
        )

        val  nodeWithRelationships = NodeWithRelationships(
            serviceScriptNode = budNode,
            serviceScriptRelationship = emptyList()
        )

        coEvery { screeningNavigationService.startScreeningNavigation(personId) } returns screeningNavigation.success()
        coEvery { navigationService.startNavigation(budNode.id, personId, budNavigation.source!!) } returns nodeWithRelationships
        coEvery { budAdministrationService.get(budNode.id) } returns budNode.success()
        coEvery { chatService.createChat(
            screeningNavigation = screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
            channelCreationParameters = null,
            userAgent = "3.12.0"
        ) } returns channelDocument.success()
        coEvery {
            navigationService.finishNavigation(
                personId = screeningNavigation.personId,
                source = budNavigation.source
            )
        } returns navigationGroup.success()

        val result = service.startNavigation(personId, budNode.id, "3.12.0")

        assertThat(result).isSuccessWithData(endScreen)
    }

    @Nested
    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    inner class ResolveNavigationTests {
        val nodeChildId = RangeUUID.generate()
        private val freeTextOption = Option(
            selectionType = SelectionType.MULTIPLE,
            type = OptionType.FREE_TEXT,
            answers = listOf(
                Answer(
                    id = budNodeChild.option!!.answers[0].id,
                    title = "Menos de 15 dias",
                    value = "Menos de 15 dias"
                ),
                Answer(
                    id = budNodeChild.option!!.answers[1].id,
                    title = "Mais de 15 dias",
                    value = "Mais de 15 dias"
                )
            )
        )
        private val calendarOption = Option(
            selectionType = SelectionType.NONE,
            type = OptionType.CALENDAR,
            answers = listOf(
                Answer(
                    id = budNodeChild.option!!.answers[0].id,
                    title = "Quando comearam os sintomas",
                    value = "10/10/2023"
                )
            )
        )
        private val inputTextOption = Option(
            selectionType = SelectionType.NONE,
            type = OptionType.INPUT_TEXT,
            answers = listOf(
                Answer(
                    id = budNodeChild.option!!.answers[0].id,
                    title = "Tem mais alguma coisa sobre os sintomas relatados que gostaria de nos contar?",
                    value = "Texto de resposta do membro"
                )
            )
        )

        private val screenInput = ScreenInput(
            selectedPills = null,
            backButtonClicked = false,
            closeButtonClicked = false,
            chatButtonClicked = false,
            clickedOption = null
        )
        private fun testParameters() = listOf(
            arrayOf(
                "should navigate to the next screen and return it when option is multiple symptom",
                budNodeChild,
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId)
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is single symptom",
                budNodeChild.copy(option = symptomsOption.copy(selectionType = SelectionType.SINGLE)),
                budNodeChild.copy(id = nodeChildId, option = symptomsOption.copy(selectionType = SelectionType.SINGLE)),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                Option(
                    selectionType = SelectionType.SINGLE,
                    type = OptionType.SYMPTOMS,
                    answers = listOf(symptomsOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(clickedOption = symptomsOption.answers[0].id.toString()),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId, option = symptomsOption.copy(selectionType = SelectionType.SINGLE)),
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is multiple free text",
                budNodeChild.copy(option = freeTextOption),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = freeTextOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                Option(
                    selectionType = SelectionType.MULTIPLE,
                    type = OptionType.FREE_TEXT,
                    answers = listOf(freeTextOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = freeTextOption
                    )
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is single free text",
                budNodeChild.copy(
                    option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                freeTextOption.copy(
                    selectionType = SelectionType.SINGLE,
                    answers = listOf(freeTextOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                    ),
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it without back link when is rootNode",
                budNodeChild.copy(rootNodeId = null),
                budNodeChild.copy(id = nodeChildId, rootNodeId = null),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId),
                    showBackLink = false
                )!!,
                "should navigate to the next screen and return it without back link when previous node is rootNode",
                budNodeChild.copy(rootNodeId = null),
                budNodeChild.copy(id = nodeChildId, rootNodeId = budNodeChild.id),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId),
                    showBackLink = false
                )!!,
                "should navigate to the next screen and return it when option is calendar",
                budNodeChild.copy(
                    option = calendarOption
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = calendarOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                calendarOption,
                screeningNavigation,
                screenInput.copy(selectedDate = calendarOption.answers[0].value?.toString()),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = calendarOption
                    )
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is input text",
                budNodeChild.copy(
                    option = inputTextOption
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = inputTextOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                inputTextOption,
                screeningNavigation,
                screenInput.copy(selectedDate = inputTextOption.answers[0].value?.toString()),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = inputTextOption
                    )
                )!!
            )
        )

        private fun testParametersWithScreeningNavigationId() = listOf(
            arrayOf(
                "should navigate to the next screen and return it when option is multiple symptom",
                budNodeChild,
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId)
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is single symptom",
                budNodeChild.copy(option = symptomsOption.copy(selectionType = SelectionType.SINGLE)),
                budNodeChild.copy(id = nodeChildId, option = symptomsOption.copy(selectionType = SelectionType.SINGLE)),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                Option(
                    selectionType = SelectionType.SINGLE,
                    type = OptionType.SYMPTOMS,
                    answers = listOf(symptomsOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(clickedOption = symptomsOption.answers[0].id.toString(), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId, option = symptomsOption.copy(selectionType = SelectionType.SINGLE))
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is multiple free text",
                budNodeChild.copy(option = freeTextOption),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = freeTextOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                Option(
                    selectionType = SelectionType.MULTIPLE,
                    type = OptionType.FREE_TEXT,
                    answers = listOf(freeTextOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = freeTextOption
                    )
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is single free text",
                budNodeChild.copy(
                    option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                freeTextOption.copy(
                    selectionType = SelectionType.SINGLE,
                    answers = listOf(freeTextOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = freeTextOption.copy(selectionType = SelectionType.SINGLE)
                    )
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it without back link when is rootNode",
                budNodeChild.copy(rootNodeId = null),
                budNodeChild.copy(id = nodeChildId, rootNodeId = null),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId),
                    showBackLink = false
                )!!,
                "should navigate to the next screen and return it without back link when previous node is rootNode",
                budNodeChild.copy(rootNodeId = null),
                budNodeChild.copy(id = nodeChildId, rootNodeId = budNodeChild.id),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId),
                    showBackLink = false
                )!!,
                "should navigate to the next screen and return it when option is calendar",
                budNodeChild.copy(
                    option = calendarOption
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = calendarOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                calendarOption,
                screeningNavigation,
                screenInput.copy(selectedDate = calendarOption.answers[0].value?.toString(), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = calendarOption
                    )
                )!!
            ),
            arrayOf(
                "should navigate to the next screen and return it when option is input text",
                budNodeChild.copy(
                    option = inputTextOption
                ),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = inputTextOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                inputTextOption,
                screeningNavigation,
                screenInput.copy(selectedDate = inputTextOption.answers[0].value?.toString(), screeningNavigationId = screeningNavigation.id),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(
                        id = nodeChildId,
                        option = inputTextOption
                    )
                )!!
            )
        )


        @ParameterizedTest(name = "{0}")
        @MethodSource("testParameters")
        fun `#resolveNavigation navigate forward`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild,
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }

        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersWithScreeningNavigationId")
        fun `#resolveNavigation navigate forward with new flow`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild,
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { screeningNavigationService.get(screeningNavigation.id) } returns screeningNavigation.success()
            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)

            coVerifyOnce { screeningNavigationService.get(any()) }
            coVerifyOnce { budAdministrationService.get(any<UUID>()) }
            coVerifyOnce { navigationService.navigate(any(), any(), any(), any())}

            coVerifyNone { screeningNavigationService.getCurrentOrCreate(any()) }
        }

        private fun testParametersBackwardsNavigation() = listOf(
            arrayOf(
                "should navigate backwards and return screen with back link",
                budNodeChild.id,
                budNodeChild.copy(id = budNodeChild.id),
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                screeningNavigation,
                screenInput.copy(screeningNavigationId = screeningNavigation.id, backButtonClicked = true),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = budNodeChild.id),
                    showBackLink = true
                )!!
            ),
            arrayOf(
                "should navigate backwards and return screen without back link when previous node is rootNode",
                budNodeChild.id,
                budNodeChild.copy(id = nodeChildId, rootNodeId = budNodeChild.id),
                budNodeChild.copy(id = RangeUUID.generate(), rootNodeId = budNodeChild.id),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                screeningNavigation,
                screenInput.copy(screeningNavigationId = screeningNavigation.id, backButtonClicked = true),
                BudNodeScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    budNode = budNodeChild.copy(id = nodeChildId, rootNodeId = budNodeChild.id),
                    showBackLink = false
                )!!
            )
        )
        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersBackwardsNavigation")
        fun `#resolveNavigation navigate backwards`(
            testName: String,
            previousNodeId: UUID,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = previousNodeId,
                currentNodeId = nodeParent.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )

            coEvery { screeningNavigationService.get(screeningNavigation.id) } returns screeningNavigation.success()
            coEvery { navigationService.navigateBackwards(
                personId = screeningNavigation.personId,
                source = ServiceScriptNavigationSource(
                    id = screeningNavigation.id.toString(),
                    type = ServiceScriptNavigationSourceType.SCREENING_NAVIGATION
                ),
                nodeId = nodeChild.id
            ) } returns (nodeParent to serviceScriptNavigation).success()

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeChild.id
            )

            assertThat(result).isSuccessWithData(screen)

            coVerifyOnce { screeningNavigationService.get(any()) }
            coVerifyOnce { navigationService.navigateBackwards(any(), any(), any()) }
        }


        private fun testParametersChat() = listOf(
            arrayOf(
                "should return drop screen when close button is clicked",
                budNodeChild,
                screeningNavigation,
                screenInput.copy(closeButtonClicked = true),
                DropTriageScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    onlyCloseSheet = true,
                    nodeId = budNodeChild.id,
                    appVersion = appVersion
                ),
                null
            ),
            arrayOf(
                "should return drop screen when chat button is clicked",
                budNodeChild,
                screeningNavigation,
                screenInput.copy(chatButtonClicked = true),
                DropTriageScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    onlyCloseSheet = false,
                    nodeId = budNodeChild.id,
                    appVersion = appVersion
                ),
                null
            ),
            arrayOf(
                "should return end screen with previous created chat id when triage is finished",
                budNodeChild,
                screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
                screenInput,
                BudNodeEndScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    channelId =  channelDocument.id!!,
                    budNode = budNodeChild
                ),
                listOf(
                    ChannelResponse(
                        id = channelDocument.id!!,
                        channelPersonId = personId.toString(),
                        name = "Channel",
                        type = ChannelType.CHAT,
                        status = ChannelStatus.ACTIVE,
                        createdAt = LocalDateTime.now(),
                    )
                )
            )
        )

        private fun testParametersChatWithScreeningNavigationId() = listOf(
            arrayOf(
                "should return drop screen when close button is clicked",
                budNodeChild,
                screeningNavigation,
                screenInput.copy(closeButtonClicked = true, screeningNavigationId = screeningNavigation.id),
                DropTriageScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    onlyCloseSheet = true,
                    nodeId = budNodeChild.id,
                    appVersion = appVersion
                )
            ),
            arrayOf(
                "should return drop screen when chat button is clicked",
                budNodeChild,
                screeningNavigation,
                screenInput.copy(chatButtonClicked = true, screeningNavigationId = screeningNavigation.id),
                DropTriageScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    onlyCloseSheet = false,
                    nodeId = budNodeChild.id,
                    appVersion = appVersion
                )
            )
        )


        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersChat")
        fun `#resolveNavigation navigate forward and return chat`(
            testName: String,
            nodeParent: BudNode,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport,
            channelResponse: List<ChannelResponse>? = null
        ) = runBlocking {
            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            channelResponse?.let{
                coEvery { chatService.getChatByScreeningNavigationId(screeningNavigation) } returns channelResponse.success()
            }

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = appVersion,
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }

        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersChatWithScreeningNavigationId")
        fun `#resolveNavigation navigate forward and return drop screen with new flow`(
            testName: String,
            nodeParent: BudNode,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {
            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = appVersion,
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)

            coVerifyNone { budAdministrationService.get(any<UUID>()) }
            coVerifyNone { chatService.getChatByScreeningNavigationId(any())}

            coVerifyNone { screeningNavigationService.get(any()) }
            coVerifyNone { screeningNavigationService.getCurrentOrCreate(any()) }
        }


        private fun testParametersCreateChat() = listOf(
            arrayOf(
                "should navigate to end screen and open chat when option is null",
                budNodeChild,
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeEndScreenFactory.build(
                    screeningNavigationId = budNavigation.source!!.id,
                    channelId = channelDocument.id(),
                    budNode = budNodeChild.copy(id = nodeChildId)
                ),
                null
            ),
            arrayOf(
                "should return end screen with new chat id when triage is finished but don't find chat",
                budNodeChild,
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeEndScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    channelId =  channelDocument.id(),
                    budNode = budNodeChild
                ),
                emptyList<ChannelResponse>()
            )
        )

        private fun testParametersCreateChatWithScreeningNavigation() = listOf(
            arrayOf(
                "should navigate to end screen and open chat when option is null",
                budNodeChild,
                budNodeChild.copy(id = nodeChildId),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                symptomsOption.copy( answers = listOf( symptomsOption.answers[0])),
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0"), screeningNavigationId = screeningNavigation.id),
                BudNodeEndScreenFactory.build(
                    screeningNavigationId = budNavigation.source!!.id,
                    channelId = channelDocument.id(),
                    budNode = budNodeChild.copy(id = nodeChildId)
                ),
                null
            ))


        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersCreateChat")
        fun `#resolveNavigation navigate forward and create chat`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport,
            channelResponse: List<ChannelResponse>? = null
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild.copy(option = null),
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships

            coEvery { chatService.createChat(
                screeningNavigation = screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
                channelCreationParameters = null,
                userAgent = "3.12.0"
            ) } returns channelDocument.success()

            coEvery { budAdministrationService.get(nodeChild.id) } returns nodeChild.success()

            coEvery {
                navigationService.finishNavigation(
                    personId = screeningNavigation.personId,
                    source = budNavigation.source
                )
            } returns navigationGroup.success()

            channelResponse?.let{
                coEvery { chatService.getChatByScreeningNavigationId(screeningNavigation) } returns channelResponse.success()
            }

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }

        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersCreateChatWithScreeningNavigation")
        fun `#resolveNavigation navigate forward and create chat with new flow`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport,
            channelResponse: List<ChannelResponse>? = null
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild.copy(option = null),
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { screeningNavigationService.get(screeningNavigation.id) } returns screeningNavigation.success()
            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships

            coEvery { chatService.createChat(
                screeningNavigation = screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
                channelCreationParameters = null,
                userAgent = "3.12.0"
            ) } returns channelDocument.success()

            coEvery { budAdministrationService.get(nodeChild.id) } returns nodeChild.success()

            coEvery {
                navigationService.finishNavigation(
                    personId = screeningNavigation.personId,
                    source = budNavigation.source
                )
            } returns navigationGroup.success()

            channelResponse?.let{
                coEvery { chatService.getChatByScreeningNavigationId(screeningNavigation) } returns channelResponse.success()
            }

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)

            coVerifyOnce { screeningNavigationService.get(any()) }
            coVerify(exactly = 2) { budAdministrationService.get(any<UUID>()) }
            coVerifyOnce { navigationService.navigate(any(), any(), any(), any())}
            coVerifyOnce { chatService.createChat(any(), any(), any()) }
            coVerifyOnce { navigationService.finishNavigation(any(), any()) }
        }


        private fun testParametersCreateChatWithParameters() = listOf(
            arrayOf(
                "should navigate to end screen and open chat when is input text without next node",
                budNodeChild.copy(option = inputTextOption),
                budNodeChild.copy(
                    id = nodeChildId,
                    option = inputTextOption
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                inputTextOption,
                screeningNavigation,
                screenInput.copy(selectedPills = listOf("0")),
                BudNodeEndScreenFactory.build(
                    screeningNavigationId = budNavigation.source!!.id,
                    channelId = channelDocument.id(),
                    budNode = budNodeChild
                )
            ),
        )

        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersCreateChatWithParameters")
        fun `#resolveNavigation navigate forward and create chat with parameters`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {
            val channelCreationParameters = ChannelCreationParameters(
                content = questionAnswers.answers.first().value.toString(),
                kind = ChannelKind.CHAT,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.SCREENING,
                tags = listOf("emergencia_sem_sinal_de_alarme")
            )

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns null

            coEvery { chatService.createChat(
                screeningNavigation = screeningNavigation.copy(status = ScreeningNavigationStatus.FINISHED),
                channelCreationParameters = channelCreationParameters,
                userAgent = "3.12.0"
            ) } returns channelDocument.success()

            coEvery { budAdministrationService.get(nodeChild.id) } returns nodeChild.success()

            coEvery {
                navigationService.finishNavigation(
                    personId = screeningNavigation.personId,
                    source = budNavigation.source
                )
            } returns navigationGroup.success()

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }


        private fun testParametersExternalValidation() = listOf(
            arrayOf(
                "should navigate to the next screen and return endscreen when type is external validation",
                budNodeChild.copy(option = Option(
                    selectionType = SelectionType.SINGLE,
                    type = OptionType.FREE_TEXT,
                    answers = listOf(freeTextOption.answers[0])
                )),
                budNodeChild.copy(
                    id = nodeChildId,
                    type = BudNode.BudNodeType.EXTERNAL_VALIDATION,
                    externalSource = ExternalSource.HEALTH_PLAN_TASK_PRESCRIPTION
                ),
                relationship.copy(nodeParentId = budNodeChild.id, nodeChildId = nodeChildId),
                Option(
                    selectionType = SelectionType.SINGLE,
                    type = OptionType.FREE_TEXT,
                    answers = listOf(freeTextOption.answers[0])
                ),
                screeningNavigation,
                screenInput.copy(clickedOption = freeTextOption.answers[0].id.toString()),
                TriagePrescriptionEndScreenFactory.build(
                    screeningNavigationId = screeningNavigation.id.toString(),
                    endScreenData = EndScreenData(
                        image  = "http://localhost",
                        title = "Você não tem outras prescrições",
                        subTitle = "Agende uma consulta com sua Médica ou Médico de Família para que a gente possa te ajudar.",
                        button = LinkData(
                            text = "Entendi",
                            action = RemoteAction(
                                type = RemoteActionType.CLOSE,
                                params = mapOf("pop_to_root" to true)
                            )
                        ),
                        link = LinkData(
                            text = "Agendar com médico de familia",
                            action = RemoteAction(
                                mobileRoute = ActionRouting.APPOINTMENT_SCHEDULE,
                                removeUntilRouteName = "LOGGED_IN"
                            )
                        )
                    ),
                    budNode = budNodeChild
                )
            )
        )

        @ParameterizedTest(name = "{0}")
        @MethodSource("testParametersExternalValidation")
        fun `#resolveNavigation navigate forward and return from external validation`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild,
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships
            coEvery { triagePrescriptionService.getPrescription(
                personId = personId,
                node = nodeChild,
                screeningNavigation = screeningNavigation,
                groupId = navigationGroup.id,
                userAgent = "3.12.0"
            ) } returns screen.success()

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }

        @ParameterizedTest(name = "{1}")
        @MethodSource("testParametersExternalValidation")
        fun `#resolveNavigation navigate forward and return from external validation when is APPOINTMENT_SCHEDULE_FIRST_OCCURRENCE`(
            testName: String,
            nodeParent: BudNode,
            nodeChild: BudNode,
            relationship: ServiceScriptRelationship,
            questionAnswers: Option,
            screeningNavigation: ScreeningNavigation,
            screenInput: ScreenInput,
            screen: ScreensTransport
        ) = runBlocking {
            val nodeChild = budNodeChild.copy(
                id = nodeChildId,
                type = BudNode.BudNodeType.EXTERNAL_VALIDATION,
                externalSource = ExternalSource.APPOINTMENT_SCHEDULE_FIRST_OCCURRENCE
            )

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild,
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = questionAnswers)
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    questionAnswers,
                    newBudNavigation.source!!,
                    personId
                )
            } returns expectedNodeWithRelationships
            coEvery { triageAppointmentScheduleService.validateFirstOccurrence(
                personId = personId,
                node = nodeChild,
                screeningNavigation = screeningNavigation,
                groupId = navigationGroup.id,
                userAgent = "3.12.0"
            ) } returns screen.success()

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)
        }

        @Test
        fun `#resolveNavigation finish navigation when type is Action`() = runBlocking {
            val screenInput = screenInput.copy(clickedOption = freeTextOption.answers[0].id.toString())
            val nodeParent = budNodeChild.copy(option = Option(
                selectionType = SelectionType.SINGLE,
                type = OptionType.FREE_TEXT,
                answers = listOf(freeTextOption.answers[0])
            ))

            val endScreenData = EndScreenData(
                image  = "http://localhost",
                title = "Você não tem outras prescrições",
                subTitle = "Agende uma consulta com sua Médica ou Médico de Família para que a gente possa te ajudar.",
                button = LinkData(
                    text = "Entendi",
                    action = null
                ),
                link = LinkData(
                    text = "Agendar com médico de familia",
                    action = RemoteAction(
                        mobileRoute = ActionRouting.APPOINTMENT_SCHEDULE,
                        removeUntilRouteName = "LOGGED_IN"
                    )
                ),
                channelCreationParameters = ChannelCreationParameters(
                    kind = ChannelKind.CHAT,
                    category = ChannelCategory.ADMINISTRATIVE,
                    content = "Quero renovar uma receita",
                    tags = listOf("bud_triage", "Atendimento Administrativo")
                )
            )
            val action = ScriptAction(
                id = RangeUUID.generate(),
                type = ScriptActionType.FINISH_NAVIGATION,
                content = mapOf(
                    "image"  to "http://localhost",
                    "title" to "Você não tem outras prescrições",
                    "subTitle" to "Agende uma consulta com sua Médica ou Médico de Família para que a gente possa te ajudar.",
                    "button" to mapOf(
                        "text" to "Entendi"
                    ),
                    "link" to mapOf(
                        "text" to "Agendar com médico de familia",
                        "action" to mapOf(
                            "mobileRoute" to "APPOINTMENT_SCHEDULE",
                            "removeUntilRouteName" to "LOGGED_IN"
                        )
                    ),
                    "channelCreationParameters" to mapOf(
                        "kind" to "CHAT",
                        "category" to "ADMINISTRATIVE",
                        "content" to "Quero renovar uma receita",
                        "tags" to listOf("bud_triage", "Atendimento Administrativo")
                    )
                )
            )
            val nodeChild = budNodeChild.copy(
                id = nodeChildId,
                type = BudNode.BudNodeType.ACTION,
                actions = listOf(action)
            )
            val relationship = relationship.copy(nodeParentId = nodeParent.id, nodeChildId = nodeChild.id)
            val endScreenDataWithChat = endScreenData.copy(
                button = endScreenData.button!!.copy(
                    action = RemoteAction(
                        mobileRoute = ActionRouting.CHANNEL,
                        params = mapOf(
                            "channel_id" to channelDocument.id(),
                            "pop_before_navigate" to true
                        )
                    )
                )
            )

            val screen = TriagePrescriptionEndScreenFactory.build(
                screeningNavigationId = screeningNavigation.id.toString(),
                endScreenData = endScreenDataWithChat,
                budNode = budNodeChild
            )

            val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(
                previousNodeId = nodeParent.id,
                currentNodeId = nodeChild.id,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                type = ServiceScriptNavigationType.AUTOMATIC
            )
            val newBudNavigation = budNavigation.copy(
                scriptNodeId = relationship.nodeChildId!!,
                previousNodeId = relationship.nodeParentId,
                relationshipId = relationship.id,
                groupId = navigationGroup.id,
                staffId= serviceScriptNavigation.staffId
            )

            val expectedNodeWithRelationships = NodeWithRelationships(
                serviceScriptNode = nodeChild,
                serviceScriptRelationship = listOf(relationship),
                navigation = newBudNavigation.copy(questionAnswers = nodeParent.option)
            )

            coEvery { budAdministrationService.get(nodeParent.id) } returns nodeParent.success()
            coEvery { screeningNavigationService.getCurrentOrCreate(personId) } returns screeningNavigation.success()
            coEvery {
                navigationService.navigate(
                    nodeParent,
                    nodeParent.option!!,
                    newBudNavigation.source!!,
                    personId,
                )
            } returns expectedNodeWithRelationships

            coEvery { budScriptActionService.executeAction(
                action = action,
                screeningNavigation = screeningNavigation,
                userAgent = "3.12.0",
                groupId = serviceScriptNavigation.groupId
            ) } returns endScreenDataWithChat.success()

            coEvery {
                navigationService.finishNavigation(
                    personId = screeningNavigation.personId,
                    source = budNavigation.source
                )
            } returns navigationGroup.success()

            val result = service.resolveNavigation(
                screenId = screeningNavigation.id,
                screenInput = screenInput,
                personId = personId,
                userAgent = "3.12.0",
                appVersion = appVersion,
                nodeId = nodeParent.id
            )

            assertThat(result).isSuccessWithData(screen)

            coVerifyOnce { budScriptActionService.executeAction(any(), any(), any(), any()) }
        }
    }
}
