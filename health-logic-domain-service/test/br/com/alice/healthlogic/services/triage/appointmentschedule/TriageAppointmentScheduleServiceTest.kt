package br.com.alice.healthlogic.services.triage.appointmentschedule

import ServiceConfig
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.CloseBehavior
import br.com.alice.app.content.model.CloseStrategy
import br.com.alice.app.content.model.ImageSize
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.data.layer.models.ServiceScriptOperator
import br.com.alice.data.layer.models.TierType
import br.com.alice.healthlogic.client.BudAdministrationService
import br.com.alice.healthlogic.models.bud.EndScreenData
import br.com.alice.healthlogic.models.bud.LinkData
import br.com.alice.healthlogic.services.bud.BudScriptActionService
import br.com.alice.healthlogic.services.bud.ServiceScriptRelationshipCacheDataService
import br.com.alice.healthlogic.services.internal.ServiceScriptNavigationService
import br.com.alice.healthlogic.ui.components.ButtonSection
import br.com.alice.healthlogic.ui.components.ImageSection
import br.com.alice.healthlogic.ui.components.SubtitleSection
import br.com.alice.healthlogic.ui.components.TitleSection
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.UUID

class TriageAppointmentScheduleServiceTest {
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val serviceScriptRelationshipCacheDataService: ServiceScriptRelationshipCacheDataService = mockk()
    private val budAdministrationService: BudAdministrationService = mockk()
    private val serviceScriptNavigationService: ServiceScriptNavigationService = mockk()
    private val budScriptActionService: BudScriptActionService = mockk()
    private val personService: PersonService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()

    private val service = TriageAppointmentScheduleService(
        appointmentScheduleService,
        healthcareTeamService,
        serviceScriptRelationshipCacheDataService,
        serviceScriptNavigationService,
        budAdministrationService,
        budScriptActionService,
        personService,
        healthProfessionalService
    )

    companion object {
        // Test data setup
        private val rootNodeId = RangeUUID.generate()
        private val groupId = RangeUUID.generate()
        private val physicianStaffId = RangeUUID.generate()
        private val healthCareTeam = TestModelFactory.buildHealthcareTeam(physicianStaffId = physicianStaffId)
        private val person = TestModelFactory.buildPerson()

        private val validationNode = TestModelFactory.buildBudNode(rootNodeId = rootNodeId)
        private val screeningNavigation = TestModelFactory.buildScreeningNavigation(personId = person.id)

        private val actionNodeFirstSchedule = buildActionNode(
            title = "Agendar 1ª consulta",
            link = "https://localhost/schedule/first"
        )

        private val actionNodeSecondSchedule = buildActionNode(
            title = "Agendar consulta",
            link = "https://localhost/schedule/second"
        )

        private val actionNodeFirstScheduleT1 = buildActionNode(
            title = "Agendar 1ª consulta T1",
            link = "https://localhost/schedule/first"
        )

        private val actionNodeSecondScheduleT1 = buildActionNode(
            title = "Agendar consulta T1",
            link = "https://localhost/schedule/second"
        )

        private val relationshipFirstSchedule = TestModelFactory.buildServiceScriptRelationship(
            parentId = validationNode.id,
            childId = actionNodeFirstSchedule.id,
            conditions = listOf(
                Condition(
                    key = ConditionOptions.TEXT.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = "Sim"
                )
            )
        )

        private val relationshipSecondSchedule = TestModelFactory.buildServiceScriptRelationship(
            parentId = validationNode.id,
            childId = actionNodeSecondSchedule.id,
            conditions = listOf(
                Condition(
                    key = ConditionOptions.TEXT.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = "Não"
                )
            )
        )

        private val relationshipFirstScheduleT1 = TestModelFactory.buildServiceScriptRelationship(
            parentId = validationNode.id,
            childId = actionNodeFirstScheduleT1.id,
            conditions = listOf(
                Condition(
                    key = ConditionOptions.TEXT.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = "Sim_TIER_1"
                )
            )
        )

        private val relationshipSecondScheduleT1 = TestModelFactory.buildServiceScriptRelationship(
            parentId = validationNode.id,
            childId = actionNodeSecondScheduleT1.id,
            conditions = listOf(
                Condition(
                    key = ConditionOptions.TEXT.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = "Não_TIER_1"
                )
            )
        )

        private val endScreenDataFirstSchedule = buildEndScreenData(
            title = "Agendar 1ª consulta",
            link = "https://localhost/schedule/first"
        )

        private val endScreenDataSecondSchedule = buildEndScreenData(
            title = "Agendar consulta",
            link = "https://localhost/schedule/second"
        )

        private val endScreenDataFirstScheduleT1 = buildEndScreenData(
            title = "Agendar 1ª consulta T1",
            link = "https://localhost/schedule/first"
        )

        private val endScreenDataSecondScheduleT1 = buildEndScreenData(
            title = "Agendar consulta T1",
            link = "https://localhost/schedule/second"
        )

        private val firstScheduleScreensTransport = buildExpectedScreensTransport(endScreenDataFirstSchedule)

        private val secondScheduleScreensTransport = buildExpectedScreensTransport(endScreenDataSecondSchedule)

        private val firstScheduleScreensTransportT1 = buildExpectedScreensTransport(endScreenDataFirstScheduleT1)

        private val secondScheduleScreensTransportT1 = buildExpectedScreensTransport(endScreenDataSecondScheduleT1)

        // Aux methods
        private fun buildActionNode(title: String, link: String) = TestModelFactory.buildBudNode(
            rootNodeId = rootNodeId,
            type = BudNode.BudNodeType.ACTION,
            actions = listOf(
                ScriptAction(
                    type = ScriptActionType.FINISH_NAVIGATION,
                    content = mapOf(
                        "image" to "https://localhost",
                        "title" to title,
                        "sub_title" to "Consulta com MFC",
                        "label" to "**Recomendação**",
                        "button" to mapOf(
                            "text" to "Agendar consulta",
                            "action" to mapOf(
                                "mobile_route" to "WEBVIEW",
                                "params" to mapOf(
                                    "link" to link,
                                    "token" to "true",
                                    "pop_on_complete" to true,
                                )
                            )
                        )
                    )
                )
            )
        )

        private fun buildEndScreenData(title: String, link: String) = EndScreenData(
            image = "https://localhost",
            title = title,
            subTitle = "Consulta com MFC",
            button = LinkData(
                text = "Agendar consulta",
                action = RemoteAction(
                    mobileRoute = ActionRouting.WEBVIEW,
                    params = mapOf(
                        "link" to link,
                        "token" to "true",
                        "pop_on_complete" to true
                    ),
                    navigationGroup = screeningNavigation.id.toString(),
                )
            )
        )

        private fun buildExpectedScreensTransport(endScreenData: EndScreenData) = ScreensTransport(
            id = "triage_prescription_empty_screen",
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.END,
                    horizontal = ScreenAlignmentType.START
                ),
                closeBehavior = CloseBehavior(
                    strategy = CloseStrategy.BLOCK,
                    action = null
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(title = "Novo agendamento", back = ""),
                body = listOf(
                    ImageSection.build(
                        "bud_image",
                        "https://localhost",
                        ServiceConfig.triageMinAppVersion,
                        ImageSize.MEDIUM
                    ),
                    TitleSection.build("bud_title", endScreenData.title!!, ServiceConfig.triageMinAppVersion),
                    SubtitleSection.build(
                        "bud_subtitle",
                        endScreenData.subTitle!!,
                        ServiceConfig.triageMinAppVersion
                    ),
                    ButtonSection.build(
                        id = "bud_confirm_button",
                        text = endScreenData.button!!.text,
                        minAppVersion = ServiceConfig.triageMinAppVersion,
                        shrinkWrap = false,
                        alignment = Alignment.CENTER,
                        remoteAction = endScreenData.button!!.action
                    )
                )
            ),
        )

        private val healthProfessional = TestModelFactory.buildHealthProfessional()
        private val healthProfessionalSuperExpert = healthProfessional.copy(
            tier = SpecialistTier.SUPER_EXPERT,
        )

        // Test scenarios
        @JvmStatic
        private fun validateFirstOccurrenceScenarios() = listOf(
            Arguments.of(TierType.TIER_0, listOf(rootNodeId), true, true, firstScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, listOf(rootNodeId), true, true, firstScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_2, listOf(rootNodeId), true, true, firstScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, listOf(rootNodeId), true, false, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_0, listOf(rootNodeId), true, false, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_2, listOf(rootNodeId), true, false, secondScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, listOf(rootNodeId), true, false, secondScheduleScreensTransport, healthProfessional),

            // Tier validation disabled
            Arguments.of(TierType.TIER_0, emptyList<UUID>(), true, true, firstScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_0, emptyList<UUID>(), true, false, secondScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, emptyList<UUID>(), true, true, firstScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, emptyList<UUID>(), true, false, secondScheduleScreensTransport, healthProfessionalSuperExpert),

            // First appointment flow disabled
            Arguments.of(TierType.TIER_2, listOf(rootNodeId), false, true, secondScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_2, listOf(rootNodeId), false, false, secondScheduleScreensTransport, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, listOf(rootNodeId), false, true, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_1, listOf(rootNodeId), false, false, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_0, listOf(rootNodeId), false, true, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
            Arguments.of(TierType.TIER_0, listOf(rootNodeId), false, false, secondScheduleScreensTransportT1, healthProfessionalSuperExpert),
        )
    }

    @ParameterizedTest(name="tier {0}, allowedNodes: {1}, flowEnabled: {2}, isFirstSchedule: {3}, expectedScreensTransport: {4}, healthProfessional: {5}")
    @MethodSource("validateFirstOccurrenceScenarios")
    fun `#validateFirstOccurrence should return expected screen based on scenario`(
        tierType: TierType,
        tierValidationAllowedNodes: List<UUID>,
        isFirstAppointmentFlowEnabled: Boolean,
        isFirstSchedule: Boolean,
        expectedScreensTransport: ScreensTransport,
        healthProfessional: HealthProfessional
    ) = runBlocking {
        withFeatureFlags(
            namespace = FeatureNamespace.SCHEDULE,
            keyValueMap = mapOf(
                "use_first_appointment_schedule_flow" to isFirstAppointmentFlowEnabled,
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthCareTeam.physicianStaffId.toString()),
                "nodes_with_tier_validation_enabled" to tierValidationAllowedNodes,
            )
        ) {
            val personWithTier = person.copy(
                productInfo = TestModelFactory.buildProductInfo(tierType = tierType)
            )

            coEvery { personService.get(person.id) } returns personWithTier.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery {
                appointmentScheduleService.existsBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.COMPLETED),
                        staffId = healthCareTeam.physicianStaffId,
                    )
                )
            } returns (!isFirstSchedule).success()
            coEvery {
                serviceScriptRelationshipCacheDataService.getActivesByParentId(validationNode.id)
            } returns listOf(
                relationshipFirstSchedule,
                relationshipSecondSchedule,
                relationshipFirstScheduleT1,
                relationshipSecondScheduleT1,
            ).success()
            coEvery { budAdministrationService.getNode(actionNodeFirstSchedule.id) } returns actionNodeFirstSchedule.success()
            coEvery { budAdministrationService.getNode(actionNodeSecondSchedule.id) } returns actionNodeSecondSchedule.success()
            coEvery { budAdministrationService.getNode(actionNodeFirstScheduleT1.id) } returns actionNodeFirstScheduleT1.success()
            coEvery { budAdministrationService.getNode(actionNodeSecondScheduleT1.id) } returns actionNodeSecondScheduleT1.success()
            coEvery {
                budScriptActionService.executeAction(
                    actionNodeFirstSchedule.actions.first(),
                    screeningNavigation,
                    groupId = groupId,
                )
            } returns endScreenDataFirstSchedule.success()
            coEvery {
                budScriptActionService.executeAction(
                    actionNodeSecondSchedule.actions.first(),
                    screeningNavigation,
                    groupId = groupId,
                )
            } returns endScreenDataSecondSchedule.success()
            coEvery {
                budScriptActionService.executeAction(
                    actionNodeFirstScheduleT1.actions.first(),
                    screeningNavigation,
                    groupId = groupId,
                )
            } returns endScreenDataFirstScheduleT1.success()
            coEvery {
                budScriptActionService.executeAction(
                    actionNodeSecondScheduleT1.actions.first(),
                    screeningNavigation,
                    groupId = groupId,
                )
            } returns endScreenDataSecondScheduleT1.success()
            coEvery {
                serviceScriptNavigationService.saveNavigation(any())
            } returns TestModelFactory.buildServiceScriptNavigation().success()
            coEvery {
                serviceScriptNavigationService.finishNavigation(any())
            } returns TestModelFactory.buildServiceScriptNavigationGroup().success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { healthProfessionalService.findByStaffId(healthCareTeam.physicianStaffId) } returns healthProfessional.success()

            val result = service.validateFirstOccurrence(
                person.id,
                validationNode,
                screeningNavigation,
                groupId,
            )

            ResultAssert.assertThat(result).isSuccessWithData(expectedScreensTransport)
        }
    }
}

