package br.com.alice.healthlogic.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.HealthDemandMonitoringDataService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@Suppress("OPT_IN_IS_NOT_ENABLED")
@OptIn(QueryAllUsage::class)
internal class HealthDemandMonitoringServiceImplTest {
    private val dataService: HealthDemandMonitoringDataService = mockk()

    private val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

    private val service = HealthDemandMonitoringServiceImpl(
        dataService
    )

    @BeforeTest
    fun beforeTest() {
        coEvery {
            dataService.queryBuilder()
        } returns QueryBuilder(
            HealthDemandMonitoringDataService.FieldOptions(),
            HealthDemandMonitoringDataService.OrderingOptions()
        )
    }

    @AfterTest
    fun confirmVerify() {
        confirmVerified(dataService)
        clearAllMocks()
    }

    @Test
    fun `#find calls data service without a query`() = runBlocking {
        val outcomes = listOf(
            TestModelFactory.buildHealthDemandMonitoring(),
            TestModelFactory.buildHealthDemandMonitoring()
        )

        coEvery {
            dataService.find(queryEq{
                all()
                    .offset { 0 }
                    .limit { 10 }
            })
        } returns outcomes.success()

        val result = service.find(
            range = 0..9,
            outcomeConfId = null,
            healthConditionId = null
        )

        assertThat(result).isSuccessWithData(outcomes)

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#find calls data service with a query when supplied a healthConditionId`() = runBlocking {
        val outcomeA = TestModelFactory.buildHealthDemandMonitoring()
        val outcomes = listOf(outcomeA)

        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.healthConditionId.eq(outcomeA.healthConditionId)
                    }
                        .offset { 0 }
                        .limit { 10 }
                }
            )
        } returns outcomes.success()

        val result = service.find(
            range = 0..9,
            outcomeConfId = null,
            healthConditionId = outcomeA.healthConditionId.toString()
        )

        assertThat(result).isSuccessWithData(outcomes)

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#find calls data service with a query when supplied an outcomeId`() = runBlocking {
        val outcomeA = TestModelFactory.buildHealthDemandMonitoring()
        val outcomes = listOf(outcomeA)

        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.outcomeId.eq(outcomeA.outcomeId)
                    }
                        .offset { 0 }
                        .limit { 10 }
                }
            )
        } returns outcomes.success()

        val result = service.find(
            range = 0..9,
            outcomeConfId = outcomeA.outcomeId.toString(),
            healthConditionId = null
        )

        assertThat(result).isSuccessWithData(outcomes)

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#find calls data service with a query when supplied both outcomeId and a healthConditionId`() = runBlocking {
        val outcomeA = TestModelFactory.buildHealthDemandMonitoring()
        val outcomes = listOf(outcomeA)

        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.outcomeId.eq(outcomeA.outcomeId)
                            .and(
                                this.healthConditionId.eq(outcomeA.healthConditionId)
                            )
                    }
                        .offset { 0 }
                        .limit { 10 }
                }
            )
        } returns outcomes.success()

        val result = service.find(
            range = 0..9,
            outcomeConfId = outcomeA.outcomeId.toString(),
            healthConditionId = outcomeA.healthConditionId.toString()
        )

        assertThat(result).isSuccessWithData(outcomes)

        coVerifyOnce { dataService.find(any()) }
    }
    @Test
    fun `#create calls HealthDemandMonitoring data service with correct params`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()
        coEvery { dataService.add(match {
            it.healthConditionId == healthDemandMonitoring.healthConditionId &&
                    it.outcomeId == healthDemandMonitoring.outcomeId
        }) } returns healthDemandMonitoring.success()

        val result = service.create(
            healthConditionId = healthDemandMonitoring.healthConditionId,
            outcomeConfId = healthDemandMonitoring.outcomeId,
            schedulingConfig = healthDemandMonitoring.schedulingConfig,
            outcomeEvolutionRanges = healthDemandMonitoring.outcomeEvolutionRanges
        )

        assertThat(result).isSuccessWithData(healthDemandMonitoring)

        coVerifyOnce { dataService.add(any()) }
    }

    @Test
    fun `#update calls HealthDemandMonitoring data service with correct params`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

        coEvery { dataService.get(healthDemandMonitoring.id) } returns healthDemandMonitoring.success()
        coEvery { dataService.update(match {
            it.healthConditionId == healthDemandMonitoring.healthConditionId &&
                    it.outcomeId == healthDemandMonitoring.outcomeId
        }) } returns healthDemandMonitoring.success()

        val result = service.update(
            id = healthDemandMonitoring.id,
            healthConditionId = healthDemandMonitoring.healthConditionId,
            outcomeConfId = healthDemandMonitoring.outcomeId,
            schedulingConfig = healthDemandMonitoring.schedulingConfig,
            outcomeEvolutionRanges = healthDemandMonitoring.outcomeEvolutionRanges
        )

        assertThat(result).isSuccessWithData(healthDemandMonitoring)

        coVerifyOnce { dataService.get(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#findByHealthConditionId calls HealthDemandMonitoring data service with correct params`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

        coEvery {
            dataService.findOne(queryEq{
                where { this.healthConditionId.eq(healthDemandMonitoring.healthConditionId) }
            })
        } returns healthDemandMonitoring.success()

        val result = service.findByHealthConditionId(healthConditionId = healthDemandMonitoring.healthConditionId)

        assertThat(result).isSuccessWithData(healthDemandMonitoring)

        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findByOutcomeConfId calls HealthDemandMonitoring data service with correct params`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

        coEvery {
            dataService.find(queryEq{
                where { this.outcomeId.eq(healthDemandMonitoring.outcomeId) }
            })
        } returns listOf(healthDemandMonitoring).success()

        val result = service.findByOutcomeConfId(outcomeConfId = healthDemandMonitoring.outcomeId)

        assertThat(result).isSuccessWithData(listOf(healthDemandMonitoring))

        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#count calls HealthDemandMonitoring data service with filters`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

        coEvery {
            dataService.count(queryEq{
                where {
                    this.outcomeId.eq(healthDemandMonitoring.outcomeId)
                        .and(this.healthConditionId.eq(healthDemandMonitoring.healthConditionId))
                }
            })
        } returns 1.success()

        val result = service.count(
            outcomeConfId = healthDemandMonitoring.outcomeId.toString(),
            healthConditionId = healthDemandMonitoring.healthConditionId.toString()
        )

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { dataService.queryBuilder() }
        coVerifyOnce { dataService.count(any()) }
    }

    @Test
    fun `#count calls HealthDemandMonitoring data service without filters`() = runBlocking {

        coEvery {
            dataService.count(queryEq{
                all()
            })
        } returns 1.success()

        val result = service.count(
            outcomeConfId = null,
            healthConditionId = null
        )

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { dataService.queryBuilder() }
        coVerifyOnce { dataService.count(any()) }
    }

    @Test
    fun `#findByOutcomesConfigsIds - should return list of health_demand_monitoring by list of outcome_ids`() = runBlocking {
        val healthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring()

        coEvery { dataService.find(
            queryEq {
                where {
                    this.outcomeId.inList(listOf(healthDemandMonitoring.outcomeId))
                }
            }
        ) } returns listOf(healthDemandMonitoring).success()

        val result = service.findByOutcomesConfigsIds(listOf(healthDemandMonitoring.outcomeId))

        assertThat(result).isSuccessWithData(listOf(healthDemandMonitoring))
        
        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#findByOutcomeEvolutionRangesEmpty - should return health_demand_monitoring with rang empty`() = runBlocking {
        val range = IntRange(0, 20)
        val outcomeConfId = RangeUUID.generate()

        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.outcomeId.eq(outcomeConfId) and
                        this.outcomeEvolutionRange.isEmpty()
                    }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns listOf(healthDemandMonitoring).success()

        val result = service.findByOutcomeEvolutionRangesEmpty(outcomeConfId, range)

        assertThat(result).isSuccessWithData(listOf(healthDemandMonitoring))

        coVerifyOnce { dataService.find(any()) }

    }
}
