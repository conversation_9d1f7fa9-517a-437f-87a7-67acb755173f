package br.com.alice.healthlogic.services.outcome_request

import br.com.alice.common.RangeUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.data.layer.models.OutcomeRequestScheduling
import br.com.alice.healthlogic.client.HealthDemandMonitoringService
import br.com.alice.healthlogic.client.OutcomeConfService
import br.com.alice.healthlogic.client.OutcomeRequestSchedulingService
import br.com.alice.healthlogic.services.outcomeRequestScheduling.OutcomeRequestScheduler
import br.com.alice.healthlogic.services.outcome_request.actions.ActionResult
import br.com.alice.healthlogic.services.outcome_request.actions.QuestionnaireOutcomeRequestAction
import br.com.alice.healthlogic.services.outcome_request.actions.WandaOutcomeRequestAction
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

internal class OutcomeRequestSchedulingProcessorTest {

    private val outcomeRequestSchedulingService: OutcomeRequestSchedulingService = mockk()
    private val healthDemandMonitoringService: HealthDemandMonitoringService = mockk()
    private val outcomeConfService: OutcomeConfService = mockk()
    private val questionnaireOutcomeRequestAction: QuestionnaireOutcomeRequestAction = mockk()
    private val wandaOutcomeRequestAction: WandaOutcomeRequestAction = mockk()
    private val outcomeRequestScheduler: OutcomeRequestScheduler = mockk()
    private val processor = OutcomeRequestSchedulingProcessor(
        outcomeRequestSchedulingService,
        healthDemandMonitoringService,
        outcomeConfService,
        questionnaireOutcomeRequestAction,
        wandaOutcomeRequestAction,
        outcomeRequestScheduler
    )

    private val healthDemand = TestModelFactory.buildHealthCondition()

    @AfterTest
    fun confirmVerify() {
        confirmVerified(
            outcomeRequestSchedulingService,
            healthDemandMonitoringService,
            outcomeConfService,
            questionnaireOutcomeRequestAction,
            wandaOutcomeRequestAction,
            outcomeRequestScheduler
        )
        clearAllMocks()
    }

    @Test
    fun `#process should call Wanda action correctly when configured`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.HEALTH_LOGICS,
            key = "should_trigger_wanda_outcome",
            value = true
        ) {
            val wandaOutcomeConf = TestModelFactory.buildOutcomeConf(
                action = OutcomeConf.OutcomeAction(
                    OutcomeConf.ActionType.WANDA,
                    RangeUUID.generate()
                )
            )

            val wandaHealthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring(
                healthConditionId = healthDemand.id,
                outcomeId = wandaOutcomeConf.id
            )

            val wandaScheduling = TestModelFactory.buildOutcomeRequestScheduling(
                healthDemandMonitoringId = wandaHealthDemandMonitoring.id
            )

            coEvery { outcomeRequestSchedulingService.get(wandaScheduling.id) } returns wandaScheduling.success()
            coEvery { outcomeRequestSchedulingService.update(any()) } returns wandaScheduling.success()
            coEvery { healthDemandMonitoringService.get(wandaHealthDemandMonitoring.id) } returns wandaHealthDemandMonitoring.success()
            coEvery { outcomeConfService.get(wandaHealthDemandMonitoring.outcomeId) } returns wandaOutcomeConf.success()
            coEvery {
                wandaOutcomeRequestAction.execute(
                    wandaScheduling,
                    wandaOutcomeConf,
                    wandaHealthDemandMonitoring,
                    wandaScheduling.personId
                )
            } returns ActionResult(
                actionType = OutcomeConf.ActionType.WANDA,
                generatedId = RangeUUID.generate()
            ).success()
            val nextSchedulingSlot = slot<OutcomeRequestScheduling>()
            coEvery { outcomeRequestScheduler.scheduleNext(capture(nextSchedulingSlot)) } answers { nextSchedulingSlot.captured.success() }

            val result = processor.process(wandaScheduling.id)

            assertThat(result.get()).isTrue

            coVerifyOnce { outcomeRequestSchedulingService.get(any()) }
            coVerifyOnce { outcomeRequestScheduler.scheduleNext(any()) }
            coVerifyOnce { healthDemandMonitoringService.get(any()) }
            coVerifyOnce { outcomeConfService.get(any()) }
            coVerifyOnce { wandaOutcomeRequestAction.execute(any(), any(), any(), any()) }
            coVerify(exactly = 2) { outcomeRequestSchedulingService.update(any()) }
        }
    }

    @Test
    fun `#process should not call Wanda action correctly when feature flag value is false`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.HEALTH_LOGICS,
            key = "should_trigger_wanda_outcome",
            value = false
        ) {
            val wandaOutcomeConf = TestModelFactory.buildOutcomeConf(
                action = OutcomeConf.OutcomeAction(
                    OutcomeConf.ActionType.WANDA,
                    RangeUUID.generate()
                )
            )

            val wandaHealthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring(
                healthConditionId = healthDemand.id,
                outcomeId = wandaOutcomeConf.id
            )

            val wandaScheduling = TestModelFactory.buildOutcomeRequestScheduling(
                healthDemandMonitoringId = wandaHealthDemandMonitoring.id
            )

            coEvery { outcomeRequestSchedulingService.get(wandaScheduling.id) } returns wandaScheduling.success()
            coEvery { outcomeRequestSchedulingService.update(any()) } returns wandaScheduling.success()
            coEvery { healthDemandMonitoringService.get(wandaHealthDemandMonitoring.id) } returns wandaHealthDemandMonitoring.success()
            coEvery { outcomeConfService.get(wandaHealthDemandMonitoring.outcomeId) } returns wandaOutcomeConf.success()

            val nextSchedulingSlot = slot<OutcomeRequestScheduling>()
            coEvery { outcomeRequestScheduler.scheduleNext(capture(nextSchedulingSlot)) } answers { nextSchedulingSlot.captured.success() }

            val result = processor.process(wandaScheduling.id)

            assertThat(result.get()).isTrue

            coVerifyOnce { outcomeRequestSchedulingService.get(any()) }
            coVerifyOnce { outcomeRequestScheduler.scheduleNext(any()) }
            coVerifyOnce { healthDemandMonitoringService.get(any()) }
            coVerifyOnce { outcomeConfService.get(any()) }
            coVerifyNone { wandaOutcomeRequestAction.execute(any(), any(), any(), any()) }
            coVerify(exactly = 2) { outcomeRequestSchedulingService.update(any()) }
        }
    }

    @Test
    fun `#process should call Questionnaire Action correctly when configured`() = runBlocking {
        val questOutcomeConf = TestModelFactory.buildOutcomeConf(
            action = OutcomeConf.OutcomeAction(
                OutcomeConf.ActionType.QUESTIONNAIRE,
                RangeUUID.generate()
            )
        )

        val questHealthDemandMonitoring = TestModelFactory.buildHealthDemandMonitoring(
            healthConditionId = healthDemand.id,
            outcomeId = questOutcomeConf.id
        )


        val questScheduling = TestModelFactory.buildOutcomeRequestScheduling(
            healthDemandMonitoringId = questHealthDemandMonitoring.id
        )

        coEvery { outcomeRequestSchedulingService.get(questScheduling.id) } returns questScheduling.success()
        coEvery { outcomeRequestSchedulingService.update(any()) } returns questScheduling.success()
        coEvery { healthDemandMonitoringService.get(questHealthDemandMonitoring.id) } returns questHealthDemandMonitoring.success()
        coEvery { outcomeConfService.get(questHealthDemandMonitoring.outcomeId) } returns questOutcomeConf.success()
        coEvery {
            questionnaireOutcomeRequestAction.execute(
                questScheduling,
                questOutcomeConf,
                questHealthDemandMonitoring,
                questScheduling.personId
            )
        } returns ActionResult(
            actionType = OutcomeConf.ActionType.QUESTIONNAIRE,
            referencedId = RangeUUID.generate()
        ).success()
        val nextSchedulingSlot = slot<OutcomeRequestScheduling>()
        coEvery { outcomeRequestScheduler.scheduleNext(capture(nextSchedulingSlot)) } answers { nextSchedulingSlot.captured.success() }

        val result = processor.process(questScheduling.id)

        assertThat(result.get()).isTrue

        coVerifyOnce { outcomeRequestSchedulingService.get(any()) }
        coVerifyOnce { outcomeRequestScheduler.scheduleNext(any()) }
        coVerifyOnce { healthDemandMonitoringService.get(any()) }
        coVerifyOnce { outcomeConfService.get(any()) }
        coVerifyOnce { questionnaireOutcomeRequestAction.execute(any(), any(), any(), any()) }
        coVerify(exactly = 2) { outcomeRequestSchedulingService.update(any()) }
    }
}
