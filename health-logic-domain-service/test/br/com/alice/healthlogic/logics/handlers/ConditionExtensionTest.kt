package br.com.alice.healthlogic.logics.handlers

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ServiceScriptOperator.CONTAINS
import br.com.alice.data.layer.models.ServiceScriptOperator.EQUALITY
import br.com.alice.healthlogic.logics.extensions.isValid
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ConditionExtensionTest {

    private val nextHandler = mockk<ConditionHandler>()

    private val condition = Condition(
        key = "any",
        operator = CONTAINS,
        value = "a"
    )
    private val value = "b"

    @Test
    fun `#isValid returns false if none handlers match`() {
        every { nextHandler.validate(any(), any()) } returns mockk()

        val result = condition.isValid(value)

        assertThat(result).isFalse
    }

    @Test
    fun `#isValid returns true if matches list equality`() {
        val uuid = RangeUUID.generate()
        val condition = Condition(
            key = "any",
            operator = EQUALITY,
            value = listOf(uuid)
        )

        val result = condition.isValid(uuid)

        assertThat(result).isTrue
    }

    @Test
    fun `#isValid returns true if matches list equality with toString`() {
        val uuid = RangeUUID.generate()
        val condition = Condition(
            key = "any",
            operator = EQUALITY,
            value = listOf(uuid)
        )

        val result = condition.isValid(uuid.toString())

        assertThat(result).isTrue
    }

    @Test
    fun `#isValid returns true if matches list equality with toString reversed`() {
        val uuid = RangeUUID.generate()
        val condition = Condition(
            key = "any",
            operator = EQUALITY,
            value = listOf(uuid.toString())
        )

        val result = condition.isValid(uuid)

        assertThat(result).isTrue
    }

    @Test
    fun `#isValid returns true if matches list equality with toString both lists`() {
        val uuid = RangeUUID.generate()
        val uuid2 = RangeUUID.generate()
        val condition = Condition(
            key = "any",
            operator = EQUALITY,
            value = listOf(uuid, uuid2)
        )

        val result = condition.isValid(listOf(uuid.toString(), uuid2.toString()))

        assertThat(result).isTrue
    }

    @Test
    fun `#isValid returns true if matches list equality with toString reversed both lists`() {
        val uuid = RangeUUID.generate()
        val uuid2 = RangeUUID.generate()
        val condition = Condition(
            key = "any",
            operator = EQUALITY,
            value = listOf(uuid.toString(), uuid2.toString())
        )

        val result = condition.isValid(listOf(uuid, uuid2))

        assertThat(result).isTrue
    }
}
