package br.com.alice.healthlogic.logics.handlers

import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ServiceScriptOperator.EQUALITY
import br.com.alice.data.layer.models.ServiceScriptOperator.RANGE
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class RangeHandlerTest {

    private val nextHandler = mockk<ConditionHandler>()

    private val condition = Condition(
        key = "age",
        operator = RANGE,
        value = "1..10"
    )
    private val value: Any = "5"

    @Test
    fun `#isValid returns null if does not have next operator and does not match`() {
        val condition = condition.copy(operator = EQUALITY)

        val result = RangeHandler(null).validate(condition, this.value)

        assertThat(result).isNull()
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid calls next handler if operator is not contains`() {
        val condition = condition.copy(operator = EQUALITY)
        every { nextHandler.validate(any(), any()) } returns mockk()

        RangeHandler(nextHandler).validate(condition, this.value)

        verify { nextHandler.validate(condition, <EMAIL>) }
    }

    @Test
    fun `#isValid returns true if value is first in range`() {
        val result = RangeHandler(nextHandler).validate(condition, 1)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if value is inside range`() {
        val result = RangeHandler(nextHandler).validate(condition, this.value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if value is last in range`() {
        val result = RangeHandler(nextHandler).validate(condition, 10)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if value is outside range`() {
        val result = RangeHandler(nextHandler).validate(condition, 11)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if condition is not int range`() {
        val condition = condition.copy(value = "abc..abc")

        val result = RangeHandler(nextHandler).validate(condition, this.value)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if value is not int or bigDecimal`() {
        val result = RangeHandler(nextHandler).validate(condition, "abc")

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if condition is not formatted`() {
        val condition = condition.copy(value = "1...10")

        val result = RangeHandler(nextHandler).validate(condition, this.value)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if value is array`() {
        val condition = condition.copy(value = "1..10")
        val value: Any = listOf("5")

        val result = RangeHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }
}
