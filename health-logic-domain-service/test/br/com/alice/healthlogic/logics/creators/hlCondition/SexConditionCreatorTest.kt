package br.com.alice.healthlogic.logics.creators.hlCondition

import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.models.ServiceScriptOperator
import br.com.alice.healthlogic.models.hlCondition.SexCondition
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.Test

internal class SexConditionCreatorTest {
    private val hDataOverview: HDataOverview = mockk()

    private val conditions = listOf(
        Condition(
            key = ConditionOptions.SEX.key,
            operator = ServiceScriptOperator.EQUALITY,
            value = Sex.INTERSEX.toString(),
        )
    )

    @Test
    fun `#create - returns new SexCondition with correct value`() = runBlocking<Unit> {
        val expectedResult = SexCondition(
            sex = listOf(Sex.INTERSEX),
            operator = ServiceScriptOperator.EQUALITY,
            hDataOverview = hDataOverview
        )

        val result = SexConditionCreator.create(conditions, hDataOverview)
        Assertions.assertThat(result).isEqualTo(expectedResult)
    }
}
