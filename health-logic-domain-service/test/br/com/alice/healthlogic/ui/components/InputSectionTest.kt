package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.ChatInputSection
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class InputSectionTest {
    @Test
    fun `#build should build correctly`() = runBlocking {
        val id = "bud_input"
        val placeholder = "Escreva sua mensagem"
        val text = "Test"
        val remoteAction = RemoteAction(
            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
            params = mapOf(
                "action" to RemoteAction(
                    method = RemoteActionMethod.POST,
                    endpoint = "https://localhost:8080"
                )
            )
        )
        val minAppVersion = "3.30.0"

        val expected = Section(
            id = id,
            type = SectionType.CHAT_INPUT_SECTION,
            data = ChatInputSection(
                placeholder = placeholder,
                text = text,
                onSendAction = remoteAction
            ),
            minAppVersion = minAppVersion
        )

        val result = InputSection.build(
            id = id,
            placeholder = placeholder,
            text = text,
            remoteAction = remoteAction
        )

        assertEquals(expected, result)
    }
}
