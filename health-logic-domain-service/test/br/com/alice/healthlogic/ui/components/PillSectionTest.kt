package br.com.alice.healthlogic.ui.components

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.PillOption
import br.com.alice.app.content.model.PillResponseSelectionAlgorithm
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionOrientation
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.PillResponseSection
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class PillSectionTest {

    @Test
    fun `#build should build correctly`() = runBlocking {
        val id = "id"
        val pillOptions = listOf(PillOption(id = "id", title = "opcao"))
        val confirmButton = Button(
            label = Label("Confirmar"),
            variant = Variant.PRIMARY,
            onTapAction = RemoteAction(
                mobileRoute = ActionRouting.CHANNEL,
                params = mapOf("channel_id" to "id")
            ),
        )
        val minAppVersion = "3.25.0"

        val expected = Section(
            id = id,
            type = SectionType.PILL_RESPONSE_SECTION,
            data = PillResponseSection(
                options = pillOptions,
                stack = SectionOrientation.VERTICAL,
                selectionAlgorithm = PillResponseSelectionAlgorithm.MULTIPLE,
                confirmButton = confirmButton,
            ),
            minAppVersion = minAppVersion
        )

        val result = PillSection.build(
            id = id,
            pillOptions = pillOptions,
            confirmButton = confirmButton,
            minAppVersion = minAppVersion
        )

        assertEquals(expected, result)
    }
}
