package br.com.alice.appointment.services

import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.appointment.event.TimelineUpsertEvent
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.services.TimelineDataService
import br.com.alice.data.layer.services.TimelineDataService.FieldOptions
import br.com.alice.data.layer.services.TimelineDataService.OrderingOptions
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class TimelineServiceImpl(
    private val timelineDataService: TimelineDataService,
    private val kafkaProducerService: KafkaProducerService
) : TimelineService {

    override suspend fun get(id: UUID): Result<Timeline, Throwable> =
        timelineDataService.get(id)

    override suspend fun addAndNotify(model: Timeline): Result<Timeline, Throwable> =
        span("addAndNotify") { span ->
            span.setAttribute("id", model.id)
            span.setAttribute("referenced_model_id", model.referencedModelId)
            span.setAttribute("referenced_model_class", model.referencedModelClass)
            span.setAttribute("person_id", model.personId)

            timelineDataService.add(model)
                .then { kafkaProducerService.produce(TimelineUpsertEvent(model)) }
                .recordResult(span)
        }

    override suspend fun updatedAndNotify(model: Timeline): Result<Timeline, Throwable> =
        span("updatedAndNotify") { span ->
            span.setAttribute("id", model.id)
            span.setAttribute("referenced_model_id", model.referencedModelId)
            span.setAttribute("referenced_model_class", model.referencedModelClass)
            span.setAttribute("person_id", model.personId)

            timelineDataService.update(model)
                .then { kafkaProducerService.produce(TimelineUpsertEvent(model)) }
                .recordResult(span)
        }

    override suspend fun updated(model: Timeline): Result<Timeline, Throwable> = span("updated") { span ->
        span.setAttribute("id", model.id)
        span.setAttribute("referenced_model_id", model.referencedModelId)
        span.setAttribute("referenced_model_class", model.referencedModelClass)
        span.setAttribute("person_id", model.personId)

        timelineDataService.update(model).recordResult(span)
    }

    override suspend fun upsertAndNotify(model: Timeline): Result<Timeline, Throwable> =
        span("upsertAndNotify") { span ->
            getByPk(model)
                .flatMap { updatedAndNotify(model.copy(id = it.id, version = it.version)) }
                .coFoldNotFound { addAndNotify(model) }
                .recordResult(span)
        }

    override suspend fun upsert(model: Timeline): Result<Timeline, Throwable> = span("upsert") { span ->
        span.setAttribute("referenced_model_id", model.referencedModelId)
        span.setAttribute("referenced_model_class", model.referencedModelClass)
        span.setAttribute("person_id", model.personId)

        getByPk(model)
            .flatMap { timelineDataService.update(model.copy(id = it.id, version = it.version)) }
            .coFoldNotFound { timelineDataService.add(model) }
    }

    override suspend fun updateList(models: List<Timeline>): Result<List<Timeline>, Throwable> =
        timelineDataService.updateList(models)

    override suspend fun getByPk(model: Timeline): Result<Timeline, Throwable> =
        findOneBy(
            TimelineFilter(
                referencedModelIds = listOf(model.referencedModelId),
                referencedModelClasses = listOf(model.referencedModelClass)
            )
        )

    override suspend fun findBy(filters: TimelineFilter): Result<List<Timeline>, Throwable> =
        span("findBy") { span ->
            catchResult {
                filters.isValid()
                timelineDataService.find { byFilter(filters) }
            }.recordResult(span)
        }

    override suspend fun findOneBy(filters: TimelineFilter): Result<Timeline, Throwable> =
        span("findOneBy") { span ->
            catchResult {
                filters.isValid()
                timelineDataService.findOne { byFilter(filters) }
            }.recordResult(span)
        }

    @OptIn(WithFilterPredicateUsage::class, OrPredicateUsage::class)
    private fun QueryBuilder<FieldOptions, OrderingOptions>.byFilter(
        filters: TimelineFilter
    ): QueryBuilder<FieldOptions, OrderingOptions> = where {
        basePredicateForFilters()
            .withFilter(filters.personId) { this.personId.eq(it) }
            .withFilter(filters.types) { this.type.inList(it) }
            .withFilter(filters.staffIds) { this.staffId.inList(it) }
            .withFilter(filters.referencedModelIds) { this.referencedModelId.inList(it) }
            .withFilter(filters.referencedModelClasses) { this.referencedModelClass.inList(it) }
            .withFilter(filters.channelIds) { this.channelIds.containsAny(it) }
            .withFilter(filters.specialtyIds) { this.specialtyId.inList(it) }!!

    }.let { query ->
        filters.range?.let { range ->
            query
                .offset { range.first }
                .limit { range.count() }
        } ?: query
    }.orderBy { this.referencedModelDate }
        .sortOrder { desc }
}
