package br.com.alice.appointment.converters.clinical_data.tasks

import br.com.alice.appointment.model.TaskHtmlGroup
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType

object SleepContentConverter : GenericTasksConverterBuilder() {
    override fun build(tasks: List<HealthPlanTask>): TaskHtmlGroup? =
        genericBuild(tasks, HealthPlanTaskType.SLEEP, "Sono")

    override fun buildToHtml(tasks: List<HealthPlanTask>): String? =
        this.build(tasks)?.let { toHtml(it) }
}
