package br.com.alice.appointment.converters.clinical_data.tasks

import br.com.alice.appointment.model.TaskHtmlContent
import br.com.alice.appointment.model.TaskHtmlGroup
import br.com.alice.common.document.html.removeHtml
import br.com.alice.data.layer.models.GenericTask
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.SentenceReference

abstract class GenericTasksConverterBuilder : CommonContentBuilder() {
    fun genericBuild(tasks: List<HealthPlanTask>, type: HealthPlanTaskType, title: String): TaskHtmlGroup? =
        getTasksOrNull<GenericTask>(tasks, type)?.let { generic ->
            TaskHtmlGroup(
                title = title,
                tasks = generic.map { buildGenericContent(it) }
            )
        }

    private fun buildGenericContent(genericTask: GenericTask) = TaskHtmlContent(
        content = listOfNotNull(
            genericTask.frequency?.let { SentenceReference.friendlyFrequency(it) },
            genericTask.description?.removeHtml()
        ).joinToString(separator = " - ")
    )
}
