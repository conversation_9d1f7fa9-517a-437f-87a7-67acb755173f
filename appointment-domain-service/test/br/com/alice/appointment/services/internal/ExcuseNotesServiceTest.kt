package br.com.alice.appointment.services.internal

import br.com.alice.appointment.event.AppointmentExcuseNotesCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.ExcuseNote
import br.com.alice.data.layer.services.AppointmentDataService
import br.com.alice.documentsigner.services.DocumentPrinterService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.RandomStringUtils
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals

class ExcuseNotesServiceTest {

    private val printerService: DocumentPrinterService = mockk()
    private val appointmentDataService: AppointmentDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = ExcuseNotesService(
        printerService,
        appointmentDataService,
        kafkaProducerService
    )

    private val staff = TestModelFactory.buildStaff()
    private val appointment = TestModelFactory.buildAppointment().copy(
        excuseNotes = listOf(
            ExcuseNote(
                description = "description",
                id = "0c56bf1b-bfa6-4cff-964b-99473917a36e"
            )
        )
    )

    private val documentToken = RandomStringUtils.randomNumeric(4)
    private val token = "token"
    private val byteArray = ByteArray(0)

    private val attachment = Attachment(
        id = RangeUUID.generate(),
        type = "pdf",
        fileName = "excuse note",
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        printerService,
        appointmentDataService,
        kafkaProducerService
    )

    @Test
    fun `#publishExcuseNotes should generate excuse notes and return update appointment`() =
        runBlocking {
            mockkStatic(RandomStringUtils::class) {
                every { RandomStringUtils.randomNumeric(4) } returns documentToken

                coEvery {
                    printerService.printExcuseNotes(
                        appointment,
                        staff.id,
                        documentToken,
                        token
                    )
                } returns byteArray.success()

                coEvery {
                    printerService.saveFile(
                        appointment.id,
                        appointment.personId,
                        byteArray,
                        "signed_excuse_notes",
                        "Atestado.pdf"
                    )
                } returns attachment

                val updateAppointment = appointment.copy(
                    excuseNotes = appointment.excuseNotes.map {
                        it.copy(attachmentId = attachment.id.toString(), token = documentToken)
                    },
                    attachments = listOf(attachment)
                )

                val result = service.publishExcuseNotes(
                    appointment,
                    staff.id,
                    token
                )

                assertEquals(result, updateAppointment)

                coVerifyOnce { printerService.printExcuseNotes(any(), any(), any(), any()) }
                coVerifyOnce { printerService.saveFile(any(), any(), any(), any(), any()) }
            }
        }

    @Test
    fun `#publishExcuseNotes should generate excuse notes, update appointment ang launch excuse notes created event`() =
        runBlocking {
            mockkStatic(RandomStringUtils::class) {
                every { RandomStringUtils.randomNumeric(4) } returns documentToken

                coEvery {
                    printerService.printExcuseNotes(
                        appointment,
                        staff.id,
                        documentToken,
                        token
                    )
                } returns byteArray.success()

                coEvery {
                    printerService.saveFile(
                        appointment.id,
                        appointment.personId,
                        byteArray,
                        "signed_excuse_notes",
                        "Atestado.pdf"
                    )
                } returns attachment

                val updateAppointment = appointment.copy(
                    excuseNotes = appointment.excuseNotes.map {
                        it.copy(attachmentId = attachment.id.toString(), token = documentToken)
                    },
                    attachments = listOf(attachment)
                )

                coEvery {
                    appointmentDataService.update(updateAppointment)
                } returns updateAppointment.success()

                coEvery {
                    kafkaProducerService.produce(AppointmentExcuseNotesCreatedEvent(updateAppointment))
                } returns mockk()

                val result = service.publishExcuseNotes(
                    appointment,
                    staff.id,
                    token,
                    withExcuseNotesEvent = true
                )

                assertEquals(result, updateAppointment)

                coVerifyOnce { printerService.printExcuseNotes(any(), any(), any(), any()) }
                coVerifyOnce { printerService.saveFile(any(), any(), any(), any(), any()) }
                coVerifyOnce { appointmentDataService.update(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
            }
        }

    @Test
    fun `#publishExcuseNotes should generate excuse notes with id, update appointment and launch excuse notes created event`() =
        runBlocking {
            val id = RangeUUID.generate()

            val appointment = TestModelFactory.buildAppointment().copy(
                excuseNotes = listOf(
                    ExcuseNote(description = "description")
                )
            )
            val appointmentToPrint = appointment.copy(
                excuseNotes = appointment.excuseNotes.map { it.copy(id = id.toString()) }
            )

            mockkObject(RangeUUID) {
                mockkStatic(RandomStringUtils::class) {
                    every { RandomStringUtils.randomNumeric(4) } returns documentToken
                    every { RangeUUID.generate().toString() } returns id.toString()

                    coEvery {
                        printerService.printExcuseNotes(
                            appointmentToPrint,
                            staff.id,
                            documentToken,
                            token
                        )
                    } returns byteArray.success()

                    coEvery {
                        printerService.saveFile(
                            appointment.id,
                            appointment.personId,
                            byteArray,
                            "signed_excuse_notes",
                            "Atestado.pdf"
                        )
                    } returns attachment

                    val updateAppointment = appointmentToPrint.copy(
                        excuseNotes = appointmentToPrint.excuseNotes.map {
                            it.copy(attachmentId = attachment.id.toString(), token = documentToken)
                        },
                        attachments = listOf(attachment)
                    )

                    coEvery {
                        appointmentDataService.update(updateAppointment)
                    } returns updateAppointment.success()

                    coEvery {
                        kafkaProducerService.produce(AppointmentExcuseNotesCreatedEvent(updateAppointment))
                    } returns mockk()

                    val result = service.publishExcuseNotes(
                        appointment,
                        staff.id,
                        token,
                        withExcuseNotesEvent = true
                    )

                    assertEquals(result, updateAppointment)

                    coVerifyOnce { printerService.printExcuseNotes(any(), any(), any(), any()) }
                    coVerifyOnce { printerService.saveFile(any(), any(), any(), any(), any()) }
                    coVerifyOnce { appointmentDataService.update(any()) }
                    coVerifyOnce { kafkaProducerService.produce(any()) }
                }
            }
        }
}
