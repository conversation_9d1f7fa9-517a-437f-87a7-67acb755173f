package br.com.alice.staff.consumers

import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ChangeShowOnSpecialistConsumer(
    private val healthProfessionalService: HealthProfessionalService,
) : Consumer(), Spannable {
    suspend fun changeShowOnAppByCreated(event: ProviderUnitCreatedEvent) = withSubscribersEnvironment {
        val providerUnit = event.payload.providerUnit
        loggerProvider("Consuming provider unit created", providerUnit)
        processProviderUnit(providerUnit)
    }

    suspend fun changeShowOnAppByUpdated(event: ProviderUnitUpdatedEvent) = withSubscribersEnvironment {
        val providerUnit = event.payload.providerUnit
        loggerProvider("Consuming provider unit updated", providerUnit)
        processProviderUnit(providerUnit)
    }

    private suspend fun processProviderUnit(providerUnit: ProviderUnit): Result<Boolean, Throwable> {
        return if (providerUnit.type != ProviderUnit.Type.CLINICAL_COMMUNITY || providerUnit.clinicalStaffIds.isNullOrEmpty()) false.success()
        else {
            updateByStaffsId(providerUnit.clinicalStaffIds!!)
            true.success()
        }
    }

    private suspend fun updateByStaffsId(clinicalStaffId: List<UUID>) = coroutineScope {
        val processHealthProfessionalDeferred =
            async {
                healthProfessionalService.getByStaffIds(clinicalStaffId).pmapEach { updateHealthProfessional(it) }.get()
            }

        val processProfessional = processHealthProfessionalDeferred.await().map { it.get() }
        logger.info(
            "staffs processed",
            "clinical_staff_ids" to clinicalStaffId,
            "staff_ids" to clinicalStaffId,
            "clinical_staff_count" to clinicalStaffId.size,
            "process_professional" to processProfessional.size,
        )
    }

    private suspend fun updateHealthProfessional(healthProfessional: HealthProfessional): Result<Boolean, Throwable> =
        span("updateHealthProfessional") { span ->
            if (!healthProfessional.showOnApp) {
                logger.info(
                    "not necessary update health_professional",
                    "id" to healthProfessional.id,
                    "staff_id" to healthProfessional.staffId
                )
                true.success()
            } else {
                healthProfessionalService.changeShowOnApp(healthProfessional.id, false)
                    .recordResult(span)
                    .map { true }
            }
        }

    private fun loggerProvider(message: String, providerUnit: ProviderUnit) = logger.info(
        message, "id" to providerUnit.id, "type" to providerUnit.type, "clinicalStaffIds" to providerUnit.clinicalStaffIds
    )
}
