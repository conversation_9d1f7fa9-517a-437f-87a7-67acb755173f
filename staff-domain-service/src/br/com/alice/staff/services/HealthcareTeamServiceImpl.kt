package br.com.alice.staff.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role.HEALTHCARE_TEAM_NURSE
import br.com.alice.common.core.Role.MANAGER_PHYSICIAN
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.coverage.client.AddressService
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.HealthcareTeamModel
import br.com.alice.data.layer.services.HealthcareTeamModelDataService
import br.com.alice.data.layer.services.PersonClinicalAccountDataService
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.InvalidRoleException
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.HealthcareTeamUpsertedEvent
import br.com.alice.staff.models.HealthcareTeamInfo
import br.com.alice.staff.models.StaffInfo
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import java.util.UUID

class HealthcareTeamServiceImpl(
    private val healthcareTeamDataService: HealthcareTeamModelDataService,
    private val staffService: StaffService,
    private val personClinicalAccountDataService: PersonClinicalAccountDataService,
    private val addressService: AddressService,
    private val kafkaProducer: KafkaProducerService
) : HealthcareTeamService {

    override suspend fun get(id: UUID, withAddress: Boolean): Result<HealthcareTeam, Throwable> =
        healthcareTeamDataService.get(id)
            .map { it.enrichAddress(withAddress).toTransport() }

    override suspend fun findBy(filters: HealthcareTeamFilters) =
        findByFilters(filters).map { it.enrichAddress(filters.withAddress).map { it.toTransport() } }

    override suspend fun findOneBy(filters: HealthcareTeamFilters) =
        findByFilters(filters).mapFirst().map { it.enrichAddress(filters.withAddress).toTransport() }

    @OptIn(WithFilterPredicateUsage::class, OrPredicateUsage::class)
    override suspend fun countBy(filters: HealthcareTeamFilters): Result<Int, Throwable> =
        healthcareTeamDataService.count {
            where {
                this.active.eq(filters.active)
                    .withFilter(filters.ids) { this.id.inList(it) }
                    .withFilter(filters.type) { this.type.eq(HealthcareTeamModel.Type.valueOf(it.name)) }
                    .withFilter(filters.segment) { this.segment.eq(HealthcareTeamModel.Segment.valueOf(it.name)) }
                    .withFilter(filters.staffId) { this.physicianStaffId.eq(it).or(this.nurseStaffId.eq(it)) }
                    .withFilter(filters.staffIds) {
                        this.physicianStaffId.inList(it).or(this.nurseStaffId.inList(it))
                    }!!
            }
        }

    override suspend fun update(model: HealthcareTeam, withAddress: Boolean): Result<HealthcareTeam, Throwable> =
        catchResult {
            val team = model.toModel()
            validateModel(team)

            healthcareTeamDataService.get(team.id).flatMap { healthcareTeam ->
                healthcareTeamDataService.update(team).then {
                    kafkaProducer.produce(HealthcareTeamUpsertedEvent(model, healthcareTeam.toTransport()))
                }
            }.map { it.enrichAddress(withAddress).toTransport() }
        }

    override suspend fun add(model: HealthcareTeam, withAddress: Boolean): Result<HealthcareTeam, Throwable> =
        catchResult {
            val team = model.toModel()
            validateModel(team)
            healthcareTeamDataService.add(team)
                .then { kafkaProducer.produce(HealthcareTeamUpsertedEvent(model)) }
                .map { it.enrichAddress(withAddress).toTransport() }
        }

    override suspend fun getHealthcareTeamByPerson(
        personId: PersonId,
        withAddress: Boolean
    ): Result<HealthcareTeam, Throwable> =
        getPersonClinicalAccountByPersonId(personId).flatMap {
            healthcareTeamDataService.get(it.healthcareTeamId)
        }.map { it.enrichAddress(withAddress).toTransport() }

    override suspend fun getHealthcareTeamInformationByPersonId(personId: PersonId): Result<HealthcareTeamInfo, Throwable> =
        getPersonClinicalAccountByPersonId(personId).flatMap {
            healthcareTeamDataService.get(it.healthcareTeamId).flatMap { healthcareTeam ->
                buildHealthcareTeamInfo(healthcareTeam, it.multiStaffIds)
            }
        }

    private suspend fun validateModel(model: HealthcareTeamModel) {
        model.nurseStaffId?.let { id ->
            val nurse = staffService.get(id).get()
            if (!nurse.isImmersionTeamNurse())
                throw InvalidRoleException("Expected ${nurse.firstName}'s role to be $HEALTHCARE_TEAM_NURSE")
        }

        val physician = staffService.get(model.physicianStaffId).get()

        if (!physician.isImmersionTeamPhysician())
            throw InvalidRoleException("Expected ${physician.firstName}'s role to be $MANAGER_PHYSICIAN")

    }

    private suspend fun buildHealthcareTeamInfo(
        healthcareTeam: HealthcareTeamModel,
        multiStaffIds: List<UUID> = emptyList()
    ): Result<HealthcareTeamInfo, Throwable> = coResultOf {
        val digitalCareNurseStaffIds = healthcareTeam.digitalCareNurseStaffIds

        val staffs = staffService.findByList(
            listOfNotNull(
                healthcareTeam.physicianStaffId,
                healthcareTeam.nurseStaffId,
                healthcareTeam.careCoordNurseStaffId
            ) + digitalCareNurseStaffIds + multiStaffIds
        ).get()

        val physician = staffs.find { it.id == healthcareTeam.physicianStaffId }!!
        val nurse = staffs.find { it.id == healthcareTeam.nurseStaffId }
        val digitalCareNurses = digitalCareNurseStaffIds.map { nurseId ->
            staffs.find { it.id == nurseId }!!
        }
        val multiStaff = multiStaffIds.mapNotNull { staffId -> staffs.find { it.id == staffId } }
        val careCoordNurse = staffs.find { it.id == healthcareTeam.careCoordNurseStaffId }

        HealthcareTeamInfo(
            id = healthcareTeam.id,
            physicianInfo = StaffInfo.fromStaff(physician),
            nurseInfo = nurse?.let { StaffInfo.fromStaff(it) } ?: StaffInfo.fromStaff(physician),
            digitalCareNurses = digitalCareNurses.map { StaffInfo.fromStaff(it) },
            multiStaff = multiStaff.map { StaffInfo.fromStaff(it) },
            careCoordNurseInfo = careCoordNurse?.let { StaffInfo.fromStaff(it) }
        )
    }

    private suspend fun getPersonClinicalAccountByPersonId(personId: PersonId) =
        personClinicalAccountDataService.findOne {
            where {
                this.personId.eq(personId)
            }
        }

    @OptIn(WithFilterPredicateUsage::class, OrPredicateUsage::class)
    private suspend fun findByFilters(filters: HealthcareTeamFilters) =
        healthcareTeamDataService.find {
            where {
                this.active.eq(filters.active)
                    .withFilter(filters.ids) { this.id.inList(it) }
                    .withFilter(filters.type) { this.type.eq(HealthcareTeamModel.Type.valueOf(it.name)) }
                    .withFilter(filters.segment) { this.segment.eq(HealthcareTeamModel.Segment.valueOf(it.name)) }
                    .withFilter(filters.staffId) { this.physicianStaffId.eq(it).or(this.nurseStaffId.eq(it)) }
                    .withFilter(filters.staffIds) {
                        this.physicianStaffId.inList(it).or(this.nurseStaffId.inList(it))
                    }!!
            }.let { queryBuilder ->
                filters.range?.let { range ->
                    queryBuilder.orderBy { createdAt }
                        .sortOrder { asc }
                        .offset { range.first }
                        .limit { range.count() }
                } ?: queryBuilder
            }
        }

    private suspend fun HealthcareTeamModel.enrichAddress(withAddress: Boolean) =
        listOf(this).enrichAddress(withAddress).first()

    private suspend fun List<HealthcareTeamModel>.enrichAddress(withAddress: Boolean): List<HealthcareTeamModel> {
        if (!withAddress) return this

        val healthcareTeamMap = this.associateBy { it.id }

        val addressMapByTeamId = addressService
            .findByReferencedModelIds(healthcareTeamMap.keys.toList())
            .getOrElse { null }
            ?.associateBy { it.referencedModelId!! }
            ?: emptyMap()

        return healthcareTeamMap
            .values
            .map { team -> team.copy(address = addressMapByTeamId[team.id]) }
    }
}
