package br.com.alice.staff.consumers

import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.services.HealthCommunitySpecialistModelDataService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.converters.HealthProfessionalToCommunitySpecialistConverter
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class SyncHealthCommunitySpecialistConsumerTest : ConsumerTest() {

    private val healthCommunitySpecialistService: HealthCommunitySpecialistModelDataService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val consumer = SyncHealthCommunitySpecialistConsumer(
        healthCommunitySpecialistService,
        healthProfessionalService,
    )

    private val healthProfessional =
        TestModelFactory.buildHealthProfessional(name = "John Doe", type = StaffType.COMMUNITY_SPECIALIST)
    private val healthCommunitySpecialist = HealthProfessionalToCommunitySpecialistConverter.convert(healthProfessional)
    private val healthCommunitySpecialistModel = healthCommunitySpecialist.toModel()

    @Test
    fun `syncByHealthProfessionalUpdated should updated health community`() = runBlocking {
        val event = HealthProfessionalUpdatedEvent(healthProfessional.id)

        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { healthCommunitySpecialistService.findOne(queryEq { where { this.staffId.eq(healthProfessional.staffId) } }) } returns
                healthCommunitySpecialistModel.success()
        coEvery { healthCommunitySpecialistService.update(healthCommunitySpecialistModel) } returns healthCommunitySpecialistModel.success()

        val result = consumer.syncByHealthProfessionalUpdated(event)
        ResultAssert.assertThat(result).isSuccessWithData(healthCommunitySpecialistModel)

        coVerifyOnce { healthCommunitySpecialistService.update(any()) }
        coVerifyOnce { healthProfessionalService.get(any()) }
        coVerifyOnce { healthCommunitySpecialistService.findOne(any()) }
    }

    @Test
    fun `syncByHealthProfessionalUpdated should ignoring health community when type is Pitaya`() = runBlocking {

        val healthProfessional = healthProfessional.copy(type = StaffType.PITAYA)
        val event = HealthProfessionalUpdatedEvent(healthProfessional.id)

        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { healthCommunitySpecialistService.findOne(queryEq { where { this.staffId.eq(healthProfessional.staffId) } }) } returns
                NotFoundException().failure()
        val result = consumer.syncByHealthProfessionalUpdated(event)
        ResultAssert.assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthProfessionalService.get(any()) }
        coVerifyOnce { healthCommunitySpecialistService.findOne(any()) }
        coVerifyNone { healthCommunitySpecialistService.update(any()) }
        coVerifyNone { healthCommunitySpecialistService.add(any()) }
    }

    @Test
    fun `syncByHealthProfessionalUpdated should inactive health community when type is pitaya`() = runBlocking {
        val event = HealthProfessionalUpdatedEvent(healthProfessional.id)
        val healthProfessional = healthProfessional.copy(type = StaffType.PITAYA)
        val inactiveHealthSpecialist =
            healthCommunitySpecialist.copy(status = SpecialistStatus.INACTIVE)
        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { healthCommunitySpecialistService.findOne(queryEq { where { this.staffId.eq(healthProfessional.staffId) } }) } returns
                healthCommunitySpecialistModel.success()
        coEvery { healthCommunitySpecialistService.update(inactiveHealthSpecialist.toModel()) } returns inactiveHealthSpecialist.toModel().success()

        val result = consumer.syncByHealthProfessionalUpdated(event)
        ResultAssert.assertThat(result).isSuccessWithData(inactiveHealthSpecialist.toModel())

        coVerifyOnce { healthCommunitySpecialistService.update(any()) }
        coVerifyOnce { healthProfessionalService.get(any()) }
        coVerifyOnce { healthCommunitySpecialistService.findOne(any()) }
    }

    @Test
    fun `SyncByHealthProfessionalCreated should created health community specialist`() = runBlocking {
        val event = HealthProfessionalCreatedEvent(healthProfessional)

        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { healthCommunitySpecialistService.findOne(queryEq { where { this.staffId.eq(healthProfessional.staffId) } }) } returns
                NotFoundException().failure()
        coEvery { healthCommunitySpecialistService.add(healthCommunitySpecialistModel) } returns healthCommunitySpecialistModel.success()
        val result = consumer.syncByHealthProfessionalCreated(event)
        ResultAssert.assertThat(result).isSuccessWithData(healthCommunitySpecialistModel)

        coVerifyOnce { healthCommunitySpecialistService.add(any()) }
        coVerifyOnce { healthProfessionalService.get(any()) }
        coVerifyOnce { healthCommunitySpecialistService.findOne(any()) }
    }

    @Test
    fun `SyncByHealthProfessionalCreated should not create health community specialist when type is Pitaya`() =
        runBlocking {
            val healthProfessional = healthProfessional.copy(type = StaffType.PITAYA)
            val event = HealthProfessionalCreatedEvent(healthProfessional)

            coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
            coEvery { healthCommunitySpecialistService.findOne(queryEq { where { this.staffId.eq(healthProfessional.staffId) } }) } returns
                    NotFoundException().failure()
            val result = consumer.syncByHealthProfessionalCreated(event)
            ResultAssert.assertThat(result).isSuccessWithData(false)

            coVerifyOnce { healthProfessionalService.get(any()) }
            coVerifyOnce { healthCommunitySpecialistService.findOne(any()) }
            coVerifyNone { healthCommunitySpecialistService.add(any()) }
        }


}
