package br.com.alice.staff.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.coverage.client.AddressService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.HealthcareTeamModel
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.services.HealthcareTeamModelDataService
import br.com.alice.data.layer.services.PersonClinicalAccountDataService
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.InvalidRoleException
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.event.HealthcareTeamUpsertedEvent
import br.com.alice.staff.helpers.TestModelDomainFactory
import br.com.alice.staff.models.StaffInfo
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class HealthcareTeamServiceImplTest {
    private val healthcareTeamDataService: HealthcareTeamModelDataService = mockk()
    private val staffService: StaffService = mockk()
    private val personClinicalAccountDataService: PersonClinicalAccountDataService = mockk()
    private val addressService: AddressService = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()

    private val healthcareTeamService = HealthcareTeamServiceImpl(
        healthcareTeamDataService,
        staffService,
        personClinicalAccountDataService,
        addressService,
        kafkaProducer
    )

    private val personId = PersonId()
    private val healthcareTeamId = RangeUUID.generate()
    private val physician = TestModelFactory.buildHealthcareTeamPhysician()
    private val nurse = TestModelFactory.buildHealthcareTeamNurse()
    private val digitalCareNurse = TestModelFactory.buildDigitalCareNurse()
    private val healthcareTeamInfo = TestModelDomainFactory.buildHealthCareTeamInfo(
        id = healthcareTeamId,
        physician = physician,
        nurse = nurse,
        digitalCareNurse = digitalCareNurse
    )
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam(
        id = healthcareTeamId,
        physicianStaffId = physician.id,
        nurseStaffId = nurse.id,
        digitalCareNurseStaffIds = listOf(digitalCareNurse.id)
    )
    private val healthcareTeamModel = healthcareTeam.toModel()
    private val address = TestModelFactory.buildStructuredAddress(referencedModelId = healthcareTeamId)
    private val healthcareTeamWithAddress = healthcareTeam.copy(address = address)

    @Test
    fun `#get returns healthcare tem found by id with address`() = runBlocking {
        coEvery { healthcareTeamDataService.get(healthcareTeamId) } returns healthcareTeamModel.success()
        coEvery { addressService.findByReferencedModelIds(listOf(healthcareTeamId)) } returns listOf(address).success()

        val result = healthcareTeamService.get(healthcareTeamId, true)
        assertThat(result).isSuccessWithData(healthcareTeamWithAddress)

        coVerifyOnce { healthcareTeamDataService.get(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
        coVerify { staffService wasNot called }
        coVerify { personClinicalAccountDataService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#get returns healthcare tem found by with error to load address`() = runBlocking {
        coEvery { healthcareTeamDataService.get(healthcareTeamId) } returns healthcareTeamModel.success()
        coEvery {
            addressService.findByReferencedModelIds(listOf(healthcareTeamId))
        } returns NotFoundException().failure()

        val result = healthcareTeamService.get(healthcareTeamId, true)
        assertThat(result).isSuccessWithData(healthcareTeam)

        coVerifyOnce { healthcareTeamDataService.get(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
        coVerify { staffService wasNot called }
        coVerify { personClinicalAccountDataService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#update should update and notify when valid`() = runBlocking {
        val beforePhysician = TestModelFactory.buildHealthcareTeamPhysician()
        val healthcareTeamBefore = healthcareTeam.copy(physicianStaffId = beforePhysician.id)

        coEvery { staffService.get(healthcareTeam.nurseStaffId!!) } returns nurse.success()

        coEvery { staffService.get(healthcareTeam.physicianStaffId) } returns physician.success()

        coEvery {
            healthcareTeamDataService.get(healthcareTeamId)
        } returns healthcareTeamBefore.toModel().success()

        coEvery { healthcareTeamDataService.update(healthcareTeamModel) } returns healthcareTeamModel.success()

        coEvery {
            kafkaProducer.produce(
                HealthcareTeamUpsertedEvent(
                    healthcareTeam,
                    healthcareTeamBefore
                )
            )
        } returns ProducerResult(LocalDateTime.now(), "topic", 100)

        val result = healthcareTeamService.update(healthcareTeam)

        assertThat(result).isSuccess()

        coVerifyOnce { healthcareTeamDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any()) }
    }

    @Test
    fun `#update should not update when invalid nurse`() = runBlocking {
        val nurse = TestModelFactory.buildDigitalCareNurse()
        val healthcareTeam = HealthcareTeam(physician.id, nurse.id)

        coEvery {
            staffService.get(healthcareTeam.nurseStaffId!!)
        } returns nurse.success()

        coEvery {
            staffService.get(healthcareTeam.physicianStaffId)
        } returns physician.success()

        coEvery {
            healthcareTeamDataService.update(healthcareTeamModel)
        } returns healthcareTeamModel.success()

        val result = healthcareTeamService.update(healthcareTeam)

        assertThat(result).isFailureOfType(InvalidRoleException::class)
    }

    @Test
    fun `#update should not update when invalid physician`() = runBlocking {
        val physician = TestModelFactory.buildChiefPhysician()
        val nurse = TestModelFactory.buildHealthcareTeamNurse()
        val healthcareTeam = HealthcareTeam(physician.id, nurse.id)

        coEvery {
            staffService.get(healthcareTeam.nurseStaffId!!)
        } returns nurse.success()

        coEvery {
            staffService.get(healthcareTeam.physicianStaffId)
        } returns physician.success()

        coEvery {
            healthcareTeamDataService.update(healthcareTeamModel)
        } returns healthcareTeamModel.success()

        val result = healthcareTeamService.update(healthcareTeam)

        assertThat(result).isFailureOfType(InvalidRoleException::class)
    }

    @Test
    fun `#add should add and notify when valid`() = runBlocking {
        coEvery {
            staffService.get(healthcareTeam.nurseStaffId!!)
        } returns nurse.success()

        coEvery {
            staffService.get(healthcareTeam.physicianStaffId)
        } returns physician.success()

        coEvery {
            healthcareTeamDataService.add(any())
        } returns healthcareTeamModel.success()

        coEvery {
            kafkaProducer.produce(any())
        } returns ProducerResult(LocalDateTime.now(), "topic", 100)

        val result = healthcareTeamService.add(healthcareTeam)

        assertThat(result).isSuccess()

        coVerifyOnce { healthcareTeamDataService.add(healthcareTeamModel) }
        coVerifyOnce { kafkaProducer.produce(HealthcareTeamUpsertedEvent(healthcareTeam)) }
    }

    @Test
    fun `#add should not add when invalid nurse`() = runBlocking {
        val nurse = TestModelFactory.buildDigitalCareNurse()
        val healthcareTeam = HealthcareTeam(physician.id, nurse.id)

        coEvery {
            staffService.get(healthcareTeam.nurseStaffId!!)
        } returns nurse.success()

        coEvery {
            staffService.get(healthcareTeam.physicianStaffId)
        } returns physician.success()

        coEvery {
            healthcareTeamDataService.add(healthcareTeamModel)
        } returns healthcareTeamModel.success()

        val result = healthcareTeamService.add(healthcareTeam)

        assertThat(result).isFailureOfType(InvalidRoleException::class)
    }

    @Test
    fun `#add should not add when invalid physician`() = runBlocking {
        val physician = TestModelFactory.buildChiefPhysician()
        val nurse = TestModelFactory.buildHealthcareTeamNurse()
        val healthcareTeam = HealthcareTeam(physician.id, nurse.id)

        coEvery {
            staffService.get(healthcareTeam.nurseStaffId!!)
        } returns nurse.success()

        coEvery {
            staffService.get(healthcareTeam.physicianStaffId)
        } returns physician.success()

        coEvery {
            healthcareTeamDataService.add(healthcareTeamModel)
        } returns healthcareTeamModel.success()

        val result = healthcareTeamService.add(healthcareTeam)

        assertThat(result).isFailureOfType(InvalidRoleException::class)
    }

    @Test
    fun `#getHealthcareTeamByPerson should return HealthcareTeam by Person`() = runBlocking {
        val personId = personId

        coEvery {
            personClinicalAccountDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(personId)
                    }
                }
            )
        } returns PersonClinicalAccount(personId = personId, healthcareTeamId = healthcareTeamId).success()

        coEvery {
            healthcareTeamDataService.get(healthcareTeamId)
        } returns healthcareTeamModel.success()

        val result = healthcareTeamService.getHealthcareTeamByPerson(personId)
        assertThat(result).isSuccessWithData(healthcareTeam)

        coVerifyOnce { personClinicalAccountDataService.findOne(any()) }
        coVerifyOnce { healthcareTeamDataService.get(any()) }
        coVerify { staffService wasNot called }
        coVerify { kafkaProducer wasNot called }
    }

    @Test
    fun `#getHealthcareTeamByPerson should return NotFoundException when not find PersonClinicalAccount`() =
        runBlocking {
            val personId = personId

            coEvery {
                personClinicalAccountDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personId)
                        }
                    }
                )
            } returns NotFoundException().failure()

            val result = healthcareTeamService.getHealthcareTeamByPerson(personId)
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { personClinicalAccountDataService.findOne(any()) }
            coVerify { healthcareTeamDataService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { kafkaProducer wasNot called }
        }

    @Test
    fun `#getHealthcareTeamInformationByPersonId should return error when person doesn't have a clinical account`() =
        runBlocking {
            val personId = personId

            coEvery {
                personClinicalAccountDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personId)
                        }
                    }
                )
            } returns NotFoundException().failure()

            val result = healthcareTeamService.getHealthcareTeamInformationByPersonId(personId)

            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { personClinicalAccountDataService.findOne(any()) }
            coVerify { healthcareTeamDataService wasNot called }
            coVerify { staffService wasNot called }
            coVerify { kafkaProducer wasNot called }
        }

    @Test
    fun `#getHealthcareTeamInformationByPersonId should return healthcare team when person have a clinical account`() =
        runBlocking {
            val personId = personId
            val multiStaff1 = TestModelFactory.buildNutritionist().copy(id = RangeUUID.generate(0))
            val multiStaff2 = TestModelFactory.buildNutritionist().copy(id = RangeUUID.generate(0))
            coEvery {
                personClinicalAccountDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personId)
                        }
                    }
                )
            } returns PersonClinicalAccount(
                personId = personId,
                healthcareTeamId = healthcareTeamId,
                multiStaffIds = listOf(multiStaff1.id, multiStaff2.id)
            ).success()
            coEvery { healthcareTeamDataService.get(healthcareTeamId) } returns healthcareTeamModel.success()
            coEvery {
                staffService.findByList(
                    listOf(
                        healthcareTeam.physicianStaffId,
                        healthcareTeam.nurseStaffId!!,
                        healthcareTeamInfo.digitalCareNurses[0].id,
                        multiStaff1.id,
                        multiStaff2.id
                    )
                )
            } returns listOf(nurse, physician, digitalCareNurse, multiStaff1).success()

            val result = healthcareTeamService.getHealthcareTeamInformationByPersonId(personId)
            val expected = healthcareTeamInfo.copy(
                multiStaff = listOf(StaffInfo.fromStaff(multiStaff1))
            )
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { personClinicalAccountDataService.findOne(any()) }
            coVerifyOnce { healthcareTeamDataService.get(any()) }
            coVerifyOnce { staffService.findByList(any()) }
            coVerify { kafkaProducer wasNot called }
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findBy returns healthcare teams found by all filters with address`() = runBlocking {
        val staffId = RangeUUID.generate()
        val id = RangeUUID.generate()

        val filter = HealthcareTeamFilters(
            active = true,
            range = IntRange(0, 49),
            ids = listOf(id),
            staffId = staffId,
            staffIds = listOf(staffId),
            type = HealthcareTeam.Type.LEAN,
            segment = HealthcareTeam.Segment.DEFAULT,
            withAddress = true
        )

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true) and
                                this.id.inList(listOf(id)) and
                                this.type.eq(HealthcareTeamModel.Type.LEAN) and
                                this.segment.eq(HealthcareTeamModel.Segment.DEFAULT) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId)) and
                                (this.physicianStaffId.inList(listOf(staffId)) or this.nurseStaffId.inList(
                                    listOf(
                                        staffId
                                    )
                                ))
                    }.orderBy { createdAt }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 50 }
                }
            )
        } returns listOf(healthcareTeamModel).success()
        coEvery { addressService.findByReferencedModelIds(listOf(healthcareTeamId)) } returns listOf(address).success()

        val result = healthcareTeamService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(healthcareTeamWithAddress))

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
    }

    @Test
    fun `#findBy returns healthcare teams found only active`() = runBlocking {
        val filter = HealthcareTeamFilters()

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true)
                    }
                }
            )
        } returns listOf(healthcareTeamModel).success()

        val result = healthcareTeamService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(healthcareTeam))

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerify { addressService wasNot called }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findBy returns healthcare teams found by all filters without address`() = runBlocking {
        val staffId = RangeUUID.generate()
        val id = RangeUUID.generate()

        val filter = HealthcareTeamFilters(
            active = true,
            range = IntRange(0, 49),
            ids = listOf(id),
            staffId = staffId,
            staffIds = listOf(staffId),
            type = HealthcareTeam.Type.LEAN,
            segment = HealthcareTeam.Segment.DEFAULT
        )

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true) and
                                this.id.inList(listOf(id)) and
                                this.type.eq(HealthcareTeamModel.Type.LEAN) and
                                this.segment.eq(HealthcareTeamModel.Segment.DEFAULT) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId)) and
                                (this.physicianStaffId.inList(listOf(staffId)) or this.nurseStaffId.inList(
                                    listOf(
                                        staffId
                                    )
                                ))
                    }.orderBy { createdAt }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 50 }
                }
            )
        } returns listOf(healthcareTeamModel).success()

        val result = healthcareTeamService.findBy(filter)
        assertThat(result).isSuccessWithData(listOf(healthcareTeam))

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerify { addressService wasNot called }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findOneBy returns healthcare teams found by all filters with address`() = runBlocking {
        val staffId = RangeUUID.generate()
        val id = RangeUUID.generate()

        val filter = HealthcareTeamFilters(
            active = true,
            range = IntRange(0, 49),
            ids = listOf(id),
            staffId = staffId,
            staffIds = listOf(staffId),
            type = HealthcareTeam.Type.LEAN,
            segment = HealthcareTeam.Segment.DEFAULT,
            withAddress = true
        )

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true) and
                                this.id.inList(listOf(id)) and
                                this.type.eq(HealthcareTeamModel.Type.LEAN) and
                                this.segment.eq(HealthcareTeamModel.Segment.DEFAULT) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId)) and
                                (this.physicianStaffId.inList(listOf(staffId)) or this.nurseStaffId.inList(
                                    listOf(
                                        staffId
                                    )
                                ))
                    }.orderBy { createdAt }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 50 }
                }
            )
        } returns listOf(healthcareTeamModel).success()
        coEvery { addressService.findByReferencedModelIds(listOf(healthcareTeamId)) } returns listOf(address).success()

        val result = healthcareTeamService.findOneBy(filter)
        assertThat(result).isSuccessWithData(healthcareTeamWithAddress)

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerifyOnce { addressService.findByReferencedModelIds(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findOneBy returns healthcare teams found by all filters without address`() = runBlocking {
        val staffId = RangeUUID.generate()
        val id = RangeUUID.generate()

        val filter = HealthcareTeamFilters(
            active = true,
            range = IntRange(0, 49),
            ids = listOf(id),
            staffId = staffId,
            staffIds = listOf(staffId),
            type = HealthcareTeam.Type.LEAN,
            segment = HealthcareTeam.Segment.DEFAULT,
            withAddress = false
        )

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true) and
                                this.id.inList(listOf(id)) and
                                this.type.eq(HealthcareTeamModel.Type.LEAN) and
                                this.segment.eq(HealthcareTeamModel.Segment.DEFAULT) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId)) and
                                (this.physicianStaffId.inList(listOf(staffId)) or this.nurseStaffId.inList(
                                    listOf(
                                        staffId
                                    )
                                ))
                    }.orderBy { createdAt }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 50 }
                }
            )
        } returns listOf(healthcareTeamModel).success()

        val result = healthcareTeamService.findOneBy(filter)
        assertThat(result).isSuccessWithData(healthcareTeam)

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerify { addressService wasNot called }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findOneBy returns healthcare teams found by staff id filter`() = runBlocking {
        val staffId = RangeUUID.generate()

        val filter = HealthcareTeamFilters(staffId = staffId)

        coEvery {
            healthcareTeamDataService.find(
                queryEq {
                    where {
                        this.active.eq(true) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId))
                    }
                }
            )
        } returns listOf(healthcareTeamModel).success()

        val result = healthcareTeamService.findOneBy(filter)
        assertThat(result).isSuccessWithData(healthcareTeam)

        coVerifyOnce { healthcareTeamDataService.find(any()) }
        coVerify { addressService wasNot called }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#countBy returns number od healthcare teams found by all filters`() = runBlocking {
        val staffId = RangeUUID.generate()
        val id = RangeUUID.generate()

        val filter = HealthcareTeamFilters(
            active = true,
            range = IntRange(0, 49),
            ids = listOf(id),
            staffId = staffId,
            staffIds = listOf(staffId),
            type = HealthcareTeam.Type.LEAN,
            segment = HealthcareTeam.Segment.DEFAULT,
            withAddress = true
        )

        coEvery {
            healthcareTeamDataService.count(
                queryEq {
                    where {
                        this.active.eq(true) and
                                this.id.inList(listOf(id)) and
                                this.type.eq(HealthcareTeamModel.Type.LEAN) and
                                this.segment.eq(HealthcareTeamModel.Segment.DEFAULT) and
                                (this.physicianStaffId.eq(staffId) or this.nurseStaffId.eq(staffId)) and
                                (this.physicianStaffId.inList(listOf(staffId)) or this.nurseStaffId.inList(
                                    listOf(
                                        staffId
                                    )
                                ))
                    }
                }
            )
        } returns 1.success()

        val result = healthcareTeamService.countBy(filter)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { healthcareTeamDataService.count(any()) }
        coVerify { addressService wasNot called }
    }

    @Test
    fun `#countBy returns number of healthcare teams found only active`() = runBlocking {
        val filter = HealthcareTeamFilters()

        coEvery {
            healthcareTeamDataService.count(
                queryEq {
                    where {
                        this.active.eq(true)
                    }
                }
            )
        } returns 1.success()

        val result = healthcareTeamService.countBy(filter)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { healthcareTeamDataService.count(any()) }
        coVerify { addressService wasNot called }
    }

}
