package br.com.alice.staff.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.data.layer.services.StaffModelDataService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.models.BackfillResponse
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

class StaffBackfillControllerTest : ControllerTestHelper() {

    private val kafkaProducerService: KafkaProducerService = mockk()
    private val healthProfessionalDataService: HealthProfessionalModelDataService = mockk()
    private val staffDataService: StaffModelDataService = mockk()
    private val controller = StaffBackfillController(
        kafkaProducerService,
        healthProfessionalDataService, staffDataService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun tearDown() = confirmVerified(kafkaProducerService)

    @Test
    fun `should send event to change schedule day`() {
        val request = StaffBackfillController.ScheduleRequest(
            listOf(
                StaffBackfillController.ScheduleDaysPayload(
                    staffId = RangeUUID.generate(), scheduleAvailabilityDays = 1
                )
            )
        )
        val expected = BackfillResponse(
            quantitySuccess = 1, quantityFailure = 0
        )
        coEvery {
            kafkaProducerService.produce(any())
        } returns ProducerResult(
            producedAt = LocalDateTime.now(), topic = "topic", offset = 1
        )

        internalAuthentication {
            post(
                "/backfill/schedule_availability_days",
                body = request,
            ) { response ->
                assertThat(response).isOKWithData(expected)

                coVerifyOnce { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `should fill national id by email`() {
        val staff = TestModelFactory.buildStaff(
            nationalId = null,
            email = "<EMAIL>"
        )
        val staffModel = staff.toModel()
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            nationalId = null,
            staffId = staff.id
        )
        val healthProfessionalModel = healthProfessional.toModel()
        val nationalId = "123.456.789-01"
        val request = StaffBackfillController.NationalIdRequest(
            listOf(
                StaffBackfillController.NationalIdPayload(
                    email = staff.email,
                    nationalId = nationalId
                )
            )
        )
        val expected = BackfillResponse(
            quantitySuccess = 1, quantityFailure = 0
        )
        coEvery {
            staffDataService.findOne(queryEq { where { this.email.eq(staffModel.email) } })
        } returns staffModel.success()

        coEvery {
            healthProfessionalDataService.findOne(queryEq { where { this.staffId.eq(staffModel.id) } })
        } returns healthProfessionalModel.success()

        coEvery {
            staffDataService.update(staffModel.copy(nationalId = nationalId.onlyNumbers()))
        } returns staffModel.success()

        coEvery { healthProfessionalDataService.update(
            healthProfessionalModel.copy(nationalId = nationalId.onlyNumbers())) } returns healthProfessionalModel.success()

        coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
            producedAt = LocalDateTime.now(), topic = "topic", offset = 1
        )

        internalAuthentication {
            post(
                "/backfill/fill_national_id",
                body = request,
            ) { response ->
                assertThat(response).isOKWithData(expected)

                coVerifyOnce { staffDataService.findOne(any()) }
                coVerifyOnce { healthProfessionalDataService.findOne(any()) }
                coVerifyOnce { staffDataService.update(any()) }
                coVerifyOnce { healthProfessionalDataService.update(any()) }
                coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
            }
        }
    }
}
