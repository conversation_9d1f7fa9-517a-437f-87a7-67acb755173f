package br.com.alice.eventinder.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthEventTypeEnum
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.exec.indicator.events.EitaCoordinatedHealthEvent
import br.com.alice.exec.indicator.events.EitaCoordinatedHealthEventExecutedPayload
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EitaCoordinatedEventConsumerTest : ConsumerTest() {

    @AfterTest
    fun setup() = clearAllMocks()

    private val healthEventsService: HealthEventsService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer = EitaCoordinatedEventConsumer(healthEventsService, kafkaProducerService)
    private val healthEventsId = RangeUUID.generate()
    private val payload =
        EitaCoordinatedHealthEventExecutedPayload(
            healthEventsId = healthEventsId,
            executedAt = LocalDateTime.now(),
            executionReferences = listOf(
                EventReference(
                    id = RangeUUID.generate().toString(),
                    location = HealthEventLocationEnum.EXECUTION_GROUP
                )
            ),
            eventType = HealthEventTypeEnum.PS
        )

    @Test
    fun `#handle EitaCoordinatedHealthEvent should process event`() = runBlocking {
        val event = EitaCoordinatedHealthEvent(
            payload = payload
        )
        val healthEvents = TestModelFactory.buildHealthEvents()
        coEvery {
            healthEventsService.updateExecutedAtById(
                payload.healthEventsId,
                payload.executedAt,
                payload.executionReferences!!,
                payload.eventType
            )
        } answers {
            healthEvents.success()
        }
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        consumer.handleEitaCoordinatedHealthEvent(event)

        coVerify(exactly = 1) { healthEventsService.updateExecutedAtById(any(), any(), any(), any()) }

    }
}
