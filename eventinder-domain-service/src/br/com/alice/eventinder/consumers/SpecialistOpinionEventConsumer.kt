package br.com.alice.eventinder.consumers

import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.SpecialistOpinionStatus
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.eventinder.services.SpecialistOpinionProcessService
import br.com.alice.secondary.attention.events.SpecialistOpinionEvent
import com.github.kittinunf.result.success

class SpecialistOpinionEventConsumer(
    private val specialistOpinionProcessService: SpecialistOpinionProcessService,
    private val healthEventsService: HealthEventsService
) : Consumer() {

    suspend fun receiveSpecialistOpinion(event: SpecialistOpinionEvent) = withSubscribersEnvironment {
        val specialistOpinion = event.payload.specialistOpinion
        val specialistOpinionMessages = event.payload.specialistOpinionMessages

        val eventReference = EventReference(
            id = specialistOpinion.id.toString(),
            location = HealthEventLocationEnum.SPECIALIST_OPINION
        )

        if (specialistOpinion.status != SpecialistOpinionStatus.RESPONDED) {
            return@withSubscribersEnvironment true.success()
        }
        
        val healthEvent = healthEventsService.findByOriginReference(eventReference).getOrNullIfNotFound()

        if (healthEvent != null) {
            return@withSubscribersEnvironment true.success()
        }
       
       logger.info(
                "Processing SpecialistOpinion on eventinder",
                "specialistOpinion" to specialistOpinion,
                "specialistOpionionMessage" to specialistOpinionMessages
       )

        specialistOpinionProcessService.process(specialistOpinion, specialistOpinionMessages).success() 
        
    }
}
