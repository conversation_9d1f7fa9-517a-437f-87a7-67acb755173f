package br.com.alice.eventinder

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.EVENTINDER_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.HealthEventsModelDataService
import br.com.alice.data.layer.services.HealthEventsModelDataServiceClient
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.eventinder.backfills.HealthEventsBackfillController
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.eventinder.consumers.BackfillEitaEventOnEventinder
import br.com.alice.eventinder.consumers.CounterReferralCreatedEventConsumer
import br.com.alice.eventinder.consumers.EitaCancelExecutionHealthEventConsumer
import br.com.alice.eventinder.consumers.EitaCoordinatedEventConsumer
import br.com.alice.eventinder.consumers.EitaUncoordinatedEventConsumer
import br.com.alice.eventinder.consumers.HealthPlanTaskConsumer
import br.com.alice.eventinder.consumers.HealthPlanTaskGroupConsumer
import br.com.alice.eventinder.consumers.ProcessBasicEventConsumer
import br.com.alice.eventinder.consumers.SpecialistOpinionEventConsumer
import br.com.alice.eventinder.ioc.DataLayerServiceModule
import br.com.alice.eventinder.ioc.EventinderDomainClientModule
import br.com.alice.eventinder.routes.backFillRoutes
import br.com.alice.eventinder.routes.kafkaRoutes
import br.com.alice.eventinder.services.CounterReferralProcessService
import br.com.alice.eventinder.services.EitaUncoordinatedEventProcessService
import br.com.alice.eventinder.services.HealthEventsServiceImpl
import br.com.alice.eventinder.services.HealthPlanTaskProcessService
import br.com.alice.eventinder.services.SpecialistOpinionProcessService
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        EventinderDomainClientModule,
        KafkaProducerModule,
        ExecIndicatorDomainClientModule,
        StaffDomainClientModule,
        EhrDomainClientModule,
        HealthConditionDomainClientModule,
        HealthPlanDomainClientModule,
        ClinicalAccountDomainClientModule,

        module(createdAtStart = true) {
            single { config }

            // Controllers
            single { HealthController(EVENTINDER_ROOT_SERVICE_NAME) }

            //Load services
            loadServiceServers("br.com.alice.eventinder.services")

            single { HealthEventsBackfillController(get(), get(), get(), get()) }

            // Consumers
            single { ProcessBasicEventConsumer() }
            single { HealthPlanTaskConsumer(get(), get()) }
            single { EitaUncoordinatedEventConsumer(get(), get()) }
            single { EitaCoordinatedEventConsumer(get(), get()) }
            single { CounterReferralCreatedEventConsumer(get()) }
            single { BackfillEitaEventOnEventinder(get(), get()) }
            single { SpecialistOpinionEventConsumer(get(), get()) }
            single { HealthPlanTaskGroupConsumer(get(), get(), get()) }
            single { EitaCancelExecutionHealthEventConsumer(get(), get()) }

            // Data services
            single<HealthEventsModelDataService> { HealthEventsModelDataServiceClient(get()) }

            // Services
            single<HealthEventsService> { HealthEventsServiceImpl(get(), get(), get()) }
            single { HealthPlanTaskProcessService(get(), get(), get(), get()) }
            single { CounterReferralProcessService(get(), get(), get(), get()) }
            single { EitaUncoordinatedEventProcessService(get()) }
            single { SpecialistOpinionProcessService(get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
                       startRoutesSync: Boolean = true) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        featureConfigBootstrap(FeatureNamespace.EVENTINDER)

        routing {
            application.attributes.put(PolicyRootServiceKey, EVENTINDER_ROOT_SERVICE_NAME)
            backFillRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }
    }
}
