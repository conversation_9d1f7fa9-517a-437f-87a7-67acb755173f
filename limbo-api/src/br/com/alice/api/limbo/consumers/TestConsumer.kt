package br.com.alice.api.limbo.consumers

import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class TestConsumer {
    fun exampleTestEvent (event: TestEvent) : Result<Boolean, Throwable> {
        logger.info(
            "Handling kafka event to create an entity",
            "event" to event
        )
        return true.success()
    }
}

//copy of ExampleAPI TestEvent
data class TestEvent(val testPayload: TestPayload): NotificationEvent<TestPayload>(
    name = name,
    producer = "example-api",
    payload = testPayload
){
    companion object{
        const val name = "KAFKA-POC-TEST-EVENT"
    }
}

data class TestPayload(
    val producer: String,
    val reason: Reason
)

enum class Reason {
    OK,
    TEST
}
