package br.com.alice.api.limbo.logics

import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.DeadLetterStatus
import br.com.alice.data.layer.services.DeadletterQueueDataService
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PredicatesLogicTest{

    private val producer = "producer"
    private val topic = "topic"
    private val maxRetries = 3
    private val minRetries = 10
    
    @Test
    fun `#buildPredicates - with only required arguments`() {
        val result = PredicatesLogic.buildPredicates(null, null, null, null)
        
        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with producer`() {
        val result = PredicatesLogic.buildPredicates(producer, null, null, null)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.eq(DeadletterQueueDataService.FieldOptions().producer, producer)

        assertThat(result).isEqualTo(expected)
    }

    @OptIn(LikePredicateUsage::class)
    @Test
    fun `#buildPredicates - with producer likeable`() {
        val result = PredicatesLogic.buildPredicates(producer, null, null, null, true)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.like(DeadletterQueueDataService.FieldOptions().producer, producer)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with producer as empty string`() {
        val result = PredicatesLogic.buildPredicates("", null, null, null)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with topic`() {
        val result = PredicatesLogic.buildPredicates(null, topic, null, null)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.eq(DeadletterQueueDataService.FieldOptions().topic, topic.uppercase())

        assertThat(result).isEqualTo(expected)
    }

    @OptIn(LikePredicateUsage::class)
    @Test
    fun `#buildPredicates - with topic likeable`() {
        val result = PredicatesLogic.buildPredicates(null, topic, null, null, true)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.like(DeadletterQueueDataService.FieldOptions().topic, topic.uppercase())

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with topic as empty string`() {
        val result = PredicatesLogic.buildPredicates(null, "", null, null)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with minRetry`() {
        val result = PredicatesLogic.buildPredicates(null, null, null, minRetries)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.greaterEq(DeadletterQueueDataService.FieldOptions().retries, minRetries)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with maxRetry`() {
        val result = PredicatesLogic.buildPredicates(null, null, maxRetries, null)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.lessEq(DeadletterQueueDataService.FieldOptions().retries, maxRetries)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPredicates - with both maxRetry and minRetry, it must use maxRetries`() {
        val result = PredicatesLogic.buildPredicates(null, null, maxRetries, minRetries)

        val expected  = Predicate.eq(DeadletterQueueDataService.FieldOptions().status, DeadLetterStatus.QUEUED) and
                Predicate.lessEq(DeadletterQueueDataService.FieldOptions().retries, maxRetries)

        assertThat(result).isEqualTo(expected)
    }
}
