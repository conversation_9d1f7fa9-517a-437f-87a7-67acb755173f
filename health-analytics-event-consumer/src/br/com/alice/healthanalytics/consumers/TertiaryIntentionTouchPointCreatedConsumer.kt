package br.com.alice.healthanalytics.consumers

import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.ehr.event.TertiaryIntentionTouchPointCreatedEvent
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.map

class TertiaryIntentionTouchPointCreatedConsumer(
    private val personService: PersonService,
    private val providerUnitService: ProviderUnitService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker
) : Consumer() {

    suspend fun tertiaryIntentionTouchPointCreatedEvent(event: TertiaryIntentionTouchPointCreatedEvent) =
        withSubscribersEnvironment {
            val tertiaryIntention = event.payload.tertiaryIntention
            logger.info(
                "TertiaryIntentionTouchPointCreatedConsumer::tertiaryIntentionTouchPointCreatedEvent event received",
                "event_id" to event.messageId,
                "tertiary_intention_id" to tertiaryIntention.id
            )

            val heathInstitution = tertiaryIntention.providerUnitId?.let {
                providerUnitService.get(it).fold(
                    { providerUnit -> providerUnit.name },
                    { throwable ->
                        logger.error(
                            "TertiaryIntentionTouchPointCreatedConsumer::tertiaryIntentionTouchPointCreatedEvent error while getting provider unit",
                            "message" to throwable.message
                        )
                        ""
                    }
                )
            }.orEmpty()
            val personId = tertiaryIntention.personId
            val hospitalizationOrigin = tertiaryIntention.hospitalizationOrigin?.name.orEmpty()

            personService.get(personId).map { person ->
                val analyticsEvent = AnalyticsEvent(
                    name = AnalyticsEventName.TERTIARY_INTENTION_TOUCH_POINT,
                    properties = mapOf(
                        "source_id" to tertiaryIntention.id,
                        "source_type" to HealthFormAnswerSourceType.TERTIARY_INTENTION,
                        "tit_type" to tertiaryIntention.type.toString(),
                        "health_institution" to heathInstitution,
                        "hospitalization_origin" to hospitalizationOrigin,
                        "ended_at" to (tertiaryIntention.endedAt ?: ""),
                    )
                )
                crmAnalyticsTracker.sendEvent(
                    nationalId = person.nationalId,
                    event = analyticsEvent
                )
            }
        }

}
