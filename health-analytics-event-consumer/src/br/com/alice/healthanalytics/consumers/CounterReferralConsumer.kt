package br.com.alice.healthanalytics.consumers

import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.person.client.PersonService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class CounterReferralConsumer(
    private val personService: PersonService,
    private val healthProfessionalService: HealthProfessionalService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker
) : Consumer() {

    suspend fun counterReferralCreatedEvent(event: CounterReferralCreatedEvent) =
        withSubscribersEnvironment {
            val counterReferral = event.payload.counterReferral
            logger.info(
                "CounterReferralConsumer::counterReferralCreatedEvent event received",
                "event_id" to event.messageId,
                "counter_referral_id" to counterReferral.id,
                "not_occurred_reason" to counterReferral.notOccurredReason
            )
            if (counterReferral.notOccurredReason != null || counterReferral.appointmentId != null)
                return@withSubscribersEnvironment false.success()

            val staffId = counterReferral.staffId
            val healthProfessional = healthProfessionalService.findByStaffId(staffId).get()

            val eventProperties = mapOf(
                "source_id" to counterReferral.id,
                "source_type" to HealthFormAnswerSourceType.COMMUNITY,
                "hp_name" to healthProfessional.name,
            )

            personService.get(counterReferral.personId).map { person ->
                val analyticsEvent = AnalyticsEvent(
                    name = AnalyticsEventName.CSAT_HP_HC,
                    properties = eventProperties
                )
                crmAnalyticsTracker.sendEvent(
                    nationalId = person.nationalId,
                    event = analyticsEvent
                )
            }
        }
}
