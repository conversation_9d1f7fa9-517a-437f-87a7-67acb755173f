package br.com.alice.healthanalytics.consumers

import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.ContextType
import br.com.alice.data.layer.models.Csat
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.event.CsatCreatedEvent
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class CsatConsumer(
    private val personService: PersonService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker
): Consumer() {


    suspend fun csatOnboardingAnswered(event: CsatCreatedEvent) = withSubscribersEnvironment {
        event.payload.csat.takeIf { it.contextType == ContextType.MEMBER_ONBOARDING }?.let { csat ->
            personService.get(csat.personId).map {
                crmAnalyticsTracker.sendEvent(
                    it.nationalId,
                    csat.toEvent()
                )
            }
        } ?: false.success()
    }


    private fun Csat.toEvent() =
        AnalyticsEvent(
            name = AnalyticsEventName.TRIGGER_CSAT,
            properties = mapOf(
                "template_type" to "CSAT_NOVO_ONBOARDING",
                "context_type" to ContextType.MEMBER_ONBOARDING,
                "source_id" to this.contextId,
                "campaign_filter" to ContextType.MEMBER_ONBOARDING,
                "csat_answered" to true,
            )
        )

}
