package br.com.alice.businessrisk.consumers

import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.CompanyBeneficiariesOnboardingFinishedEvent
import br.com.alice.business.events.CompanyBeneficiariesVideoCallFinishedEvent
import br.com.alice.businessrisk.clients.MacoService
import br.com.alice.businessrisk.models.BeneficiaryMacoTransport
import br.com.alice.businessrisk.models.MacoCalculationTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class CompanyConsumerTest : BaseConsumerTest() {

    private val companyService: CompanyService = mockk()
    private val macoService: MacoService = mockk()

    private val consumer = CompanyConsumer(
        companyService,
        macoService
    )

    @Test
    fun `#should calculate the maco from company contract ids when the whole beneficiaries finished the onboarding`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val companyContractMaco = TestModelFactory.buildCompanyContractMaco()
                val beneficiaryMaco1 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )
                val beneficiaryMaco2 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )

                val event = CompanyBeneficiariesOnboardingFinishedEvent(company.id)

                val macoCalculation =
                    MacoCalculationTransport(companyContractMaco, listOf(beneficiaryMaco1, beneficiaryMaco2))

                coEvery { companyService.get(company.id) } returns company
                coEvery { macoService.calculate(contractId) } returns macoCalculation
                coEvery { macoService.saveMacos(macoCalculation) } returns macoCalculation

                val result = consumer.calculateMacoFromBeneficiariesOnboardingFinished(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    companyService.get(company.id)
                    macoService.calculate(contractId)
                    macoService.saveMacos(macoCalculation)
                }
            }
        }

    @Test
    fun `#should calculate the maco from company contract ids when the whole beneficiaries finished the video call`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.BUSINESS_RISK, "should_calculate_maco", true) {
                val contractId = RangeUUID.generate()
                val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))
                val companyContractMaco = TestModelFactory.buildCompanyContractMaco()
                val beneficiaryMaco1 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )
                val beneficiaryMaco2 = BeneficiaryMacoTransport(
                    TestModelFactory.buildBeneficiaryCompiledView(),
                    TestModelFactory.buildBeneficiaryMaco(),
                )

                val event = CompanyBeneficiariesVideoCallFinishedEvent(company.id)

                val macoCalculation =
                    MacoCalculationTransport(companyContractMaco, listOf(beneficiaryMaco1, beneficiaryMaco2))

                coEvery { companyService.get(company.id) } returns company
                coEvery { macoService.calculate(contractId) } returns macoCalculation
                coEvery { macoService.saveMacos(macoCalculation) } returns macoCalculation

                val result = consumer.calculateMacoFromBeneficiariesVideoCallFinished(event)

                assertThat(result).isSuccess()

                coVerifyOnce {
                    companyService.get(company.id)
                    macoService.calculate(contractId)
                    macoService.saveMacos(macoCalculation)
                }
            }
        }

    @Test
    fun `#should skip the calculation of maco when the feature flag is disabled`() =
        runBlocking {
            val contractId = RangeUUID.generate()
            val company = TestModelFactory.buildCompany(contractIds = listOf(contractId))

            val event = CompanyBeneficiariesVideoCallFinishedEvent(company.id)

            val result = consumer.calculateMacoFromBeneficiariesVideoCallFinished(event)

            assertThat(result).isSuccess()

            coVerifyNone {
                companyService.get(any())
                macoService.calculate(any())
                macoService.saveMacos(any())
            }
        }
}
