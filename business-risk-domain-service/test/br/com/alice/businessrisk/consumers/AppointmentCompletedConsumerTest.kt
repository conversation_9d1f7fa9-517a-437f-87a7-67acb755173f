package br.com.alice.businessrisk.consumers

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.businessrisk.clients.PersonHealthConditionContractualRiskService
import br.com.alice.businessrisk.events.PersonHealthConditionContractualRiskUpdatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentContractualRisk
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthConditionDescription
import br.com.alice.healthcondition.client.HealthConditionService
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentCompletedConsumerTest : BaseConsumerTest() {

    private val personHealthConditionContractualRiskService: PersonHealthConditionContractualRiskService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val healthConditionService: HealthConditionService = mockk()

    private val consumer = AppointmentCompletedConsumer(
        personHealthConditionContractualRiskService,
        kafkaProducerService,
        healthConditionService
    )

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()

    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personHealthConditionContractualRiskService,
        kafkaProducerService,
        healthConditionService
    )

    private val personId = PersonId()
    private val healthConditionId = RangeUUID.generate()
    private val producerResult = ProducerResult(LocalDateTime.now(), "topic", 100)

    @Test
    fun `should upsert person health condition contractual risk when appointment is completed`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.BUSINESS_RISK,
                "enable_appointment_completed_contractual_risk_consumer",
                true
            ) {
                val contractualRisk = AppointmentContractualRisk(
                    description = HealthConditionDescription(
                        id = healthConditionId,
                        type = Disease.Type.CID_10,
                        value = "I10"
                    ),
                    factor = 0,
                    baseRiskRating = 10,
                    finalRiskRating = 10,
                    reason = "Default text",
                    caseId = RangeUUID.generate(),
                )

                val appointment =
                    TestModelFactory.buildAppointment(
                        personId = personId,
                        contractualRisks = listOf(contractualRisk),
                        type = AppointmentType.STATEMENT_OF_HEALTH
                    )

                val healthCondition = TestModelFactory.buildHealthCondition(
                    id = healthConditionId,
                    name = "Diabetes"
                )

                val personHealthConditionContractualRiskFromAppointment =
                    TestModelFactory.buildPersonHealthConditionContractualRisk(
                        personId = personId,
                        healthConditionId = healthConditionId,
                        healthConditionDescription = healthCondition.name,
                        staffId = appointment.staffId,
                        baseRiskRating = contractualRisk.baseRiskRating,
                        finalRiskRating = contractualRisk.finalRiskRating,
                        factor = contractualRisk.factor,
                        reason = contractualRisk.reason
                    )

                val syncedRisks = listOf(
                    TestModelFactory.buildPersonHealthConditionContractualRisk(),
                    personHealthConditionContractualRiskFromAppointment
                )

                val event = AppointmentCompletedEvent(appointment)

                coEvery {
                    personHealthConditionContractualRiskService.syncWithPersonCase(
                        personId,
                        appointment.staffId
                    )
                } returns syncedRisks.success()
                coEvery { healthConditionService.get(contractualRisk.description.id) } returns healthCondition.success()
                coEvery {
                    personHealthConditionContractualRiskService.upsert(
                        personHealthConditionContractualRiskFromAppointment
                    )
                } returns personHealthConditionContractualRiskFromAppointment.success()
                coEvery { personHealthConditionContractualRiskService.findByPersonId(personId) } returns syncedRisks.success()

                coEvery {
                    kafkaProducerService.produce(
                        PersonHealthConditionContractualRiskUpdatedEvent(
                            personId = appointment.personId,
                            personHealthConditionContractualRisks = syncedRisks
                        )
                    )
                } returns producerResult

                consumer.upsertPersonHealthConditionContractualRiskWhenAppointmentIsCompleted(event)

                coVerifyOnce {
                    personHealthConditionContractualRiskService.syncWithPersonCase(
                        personId,
                        appointment.staffId
                    )
                }
                coVerifyOnce { personHealthConditionContractualRiskService.upsert(any()) }
                coVerifyOnce { healthConditionService.get(any()) }
                coVerifyOnce { personHealthConditionContractualRiskService.findByPersonId(any()) }

                coVerifyOnce { kafkaProducerService.produce(any()) }
            }
        }


    @Test
    fun `should not process appointment when type is not STATEMENT_OF_HEALTH`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.BUSINESS_RISK,
            "enable_appointment_completed_contractual_risk_consumer",
            true
        ) {
            val contractualRisk = AppointmentContractualRisk(
                description = HealthConditionDescription(
                    id = healthConditionId,
                    type = Disease.Type.CID_10,
                    value = "I10"
                ),
                factor = 0,
                baseRiskRating = 10,
                finalRiskRating = 10,
                reason = "Default text",
                caseId = RangeUUID.generate(),
            )

            val appointment =
                TestModelFactory.buildAppointment(
                    personId = personId,
                    contractualRisks = listOf(contractualRisk),
                    type = AppointmentType.ANNOTATION
                )

            val event = AppointmentCompletedEvent(appointment)

            val result = consumer.upsertPersonHealthConditionContractualRiskWhenAppointmentIsCompleted(event)

            assert(result.isSuccess())

            coVerify(exactly = 0) { personHealthConditionContractualRiskService.upsert(any()) }

            coVerify { kafkaProducerService wasNot Called }
        }
    }

    @Test
    fun `should not process appointment when feature flag is false`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.BUSINESS_RISK,
            "enable_appointment_completed_contractual_risk_consumer",
            false
        ) {
            val contractualRisk = AppointmentContractualRisk(
                description = HealthConditionDescription(
                    id = healthConditionId,
                    type = Disease.Type.CID_10,
                    value = "I10"
                ),
                factor = 0,
                baseRiskRating = 10,
                finalRiskRating = 10,
                reason = "Default text",
                caseId = RangeUUID.generate(),
            )

            val appointment =
                TestModelFactory.buildAppointment(
                    personId = personId,
                    contractualRisks = listOf(contractualRisk),
                    type = AppointmentType.STATEMENT_OF_HEALTH
                )

            val event = AppointmentCompletedEvent(appointment)

            val result = consumer.upsertPersonHealthConditionContractualRiskWhenAppointmentIsCompleted(event)

            assert(result.isSuccess())

            coVerify(exactly = 0) { personHealthConditionContractualRiskService.upsert(any()) }

            coVerify { kafkaProducerService wasNot Called }
        }
    }
}
