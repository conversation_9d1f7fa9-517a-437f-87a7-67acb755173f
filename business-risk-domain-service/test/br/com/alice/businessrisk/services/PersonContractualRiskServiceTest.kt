package br.com.alice.businessrisk.services

import br.com.alice.businessrisk.converters.toModel
import br.com.alice.businessrisk.events.PersonContractualRiskCreatedEvent
import br.com.alice.businessrisk.logics.PersonContractualRiskLogic
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.PersonContractualRiskModelDataService
import br.com.alice.person.client.PersonService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonContractualRiskServiceTest {
    private val personContractualRiskDataService: PersonContractualRiskModelDataService = mockk()
    private val personService: PersonService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service =
        PersonContractualRiskServiceImpl(personContractualRiskDataService, personService, kafkaProducerService)

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#get by id returns personContractualRisk`() = runBlocking {
        val personContractualRisk = TestModelFactory.buildPersonContractualRisk()
        val personContractualRiskModel = personContractualRisk.toModel()

        coEvery { personContractualRiskDataService.get(any()) } returns personContractualRiskModel

        val result = service.get(personContractualRisk.id)

        assertThat(result).isSuccessWithData(personContractualRisk)

        coVerifyOnce { personContractualRiskDataService.get(personContractualRisk.id) }
    }

    @Test
    fun `#add personContractualRisk successfully`() = runBlocking {
        val personContractualRisk = TestModelFactory.buildPersonContractualRisk()
        val personContractualRiskModel = personContractualRisk.toModel()

        coEvery { personContractualRiskDataService.add(any()) } returns personContractualRiskModel
        coEvery { kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId }) } returns mockk()

        val result = service.add(personContractualRisk)

        assertThat(result).isSuccessWithData(personContractualRisk)

        coVerifyOnce {
            personContractualRiskDataService.add(personContractualRiskModel)
            kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId })
        }
    }

    @Test
    fun `#update personContractualRisk successfully`() = runBlocking {
        val personContractualRisk = TestModelFactory.buildPersonContractualRisk()
        val personContractualRiskModel = personContractualRisk.toModel()

        coEvery { personContractualRiskDataService.update(any()) } returns personContractualRiskModel

        val result = service.update(personContractualRisk)

        assertThat(result).isSuccessWithData(personContractualRisk)

        coVerifyOnce { personContractualRiskDataService.update(personContractualRiskModel) }
    }

    @Test
    fun `#delete personContractualRisk successfully`() = runBlocking {
        val personContractualRisk = TestModelFactory.buildPersonContractualRisk()
        val personContractualRiskModel = personContractualRisk.toModel()

        coEvery { personContractualRiskDataService.delete(any()) } returns true

        val result = service.delete(personContractualRisk)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { personContractualRiskDataService.delete(personContractualRiskModel) }
    }

    @Nested
    inner class CreateByPersonHealthConditionContractualRiskList {
        @Test
        fun `#createByPersonHealthConditionContractualRiskList call buildPersonContractualRiskWhenCrContains3`() =
            runBlocking {
                val personId = PersonId()
                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = personId)
                val personContractualRiskModel = personContractualRisk.toModel()

                val phccrs = listOf(
                    TestModelFactory.buildPersonHealthConditionContractualRisk(personId = personId, finalRiskRating = 3)
                )
                mockkObject(PersonContractualRiskLogic)

                every {
                    PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContains3(any(), personId, any())
                } returns personContractualRisk
                coEvery { personContractualRiskDataService.add(personContractualRiskModel) } returns personContractualRiskModel
                coEvery { kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId }) } returns mockk()

                val result = service.createByPersonHealthConditionContractualRiskList(personId, phccrs)

                assertThat(result).isSuccessWithData(personContractualRisk)

                coVerifyOnce {
                    personContractualRiskDataService.add(any())
                    kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId })
                }
                verifyOnce { PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContains3(any(), any(), any()) }
            }

        @Test
        fun `#createByPersonHealthConditionContractualRiskList call buildPersonContractualRiskWeightWhenCrContains2`() =
            runBlocking {
                val personId = PersonId()
                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = personId)
                val personContractualRiskModel = personContractualRisk.toModel()

                val phccrs = listOf(
                    TestModelFactory.buildPersonHealthConditionContractualRisk(personId = personId, finalRiskRating = 2)
                )
                mockkObject(PersonContractualRiskLogic)

                every {
                    PersonContractualRiskLogic.buildPersonContractualRiskWeightWhenCrContains2(any(), personId, any())
                } returns personContractualRisk
                coEvery { personContractualRiskDataService.add(personContractualRiskModel) } returns personContractualRiskModel
                coEvery { kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId }) } returns mockk()

                val result = service.createByPersonHealthConditionContractualRiskList(personId, phccrs)

                assertThat(result).isSuccessWithData(personContractualRisk)

                coVerifyOnce {
                    personContractualRiskDataService.add(any())
                    kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId })
                }
                verifyOnce {
                    PersonContractualRiskLogic.buildPersonContractualRiskWeightWhenCrContains2(
                        any(),
                        any(),
                        any()
                    )
                }
            }

        @Test
        fun `#createByPersonHealthConditionContractualRiskList call buildPersonContractualRiskWhenCrContains1`() =
            runBlocking {
                val personId = PersonId()
                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = personId)
                val personContractualRiskModel = personContractualRisk.toModel()

                val phccrs = listOf(
                    TestModelFactory.buildPersonHealthConditionContractualRisk(personId = personId, finalRiskRating = 1)
                )
                mockkObject(PersonContractualRiskLogic)

                every {
                    PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContains1(any(), personId, any())
                } returns personContractualRisk
                coEvery { personContractualRiskDataService.add(personContractualRiskModel) } returns personContractualRiskModel
                coEvery { kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId }) } returns mockk()

                val result = service.createByPersonHealthConditionContractualRiskList(personId, phccrs)

                assertThat(result).isSuccessWithData(personContractualRisk)

                coVerifyOnce {
                    personContractualRiskDataService.add(any())
                    kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId })
                }
                verifyOnce { PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContains1(any(), any(), any()) }
            }

        @Test
        fun `#createByPersonHealthConditionContractualRiskList call buildPersonContractualRiskWhenCrContainsJust0`() =
            runBlocking {
                val person = TestModelFactory.buildPerson()
                val personContractualRisk = TestModelFactory.buildPersonContractualRisk(personId = person.id)
                val personContractualRiskModel = personContractualRisk.toModel()

                val phccrs = listOf(
                    TestModelFactory.buildPersonHealthConditionContractualRisk(
                        personId = person.id,
                        finalRiskRating = 0
                    )
                )
                mockkObject(PersonContractualRiskLogic)

                coEvery { personService.get(person.id) } returns person
                every {
                    PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContainsJust0(any(), person, any())
                } returns personContractualRisk
                coEvery { personContractualRiskDataService.add(personContractualRiskModel) } returns personContractualRiskModel
                coEvery { kafkaProducerService.produce(match<PersonContractualRiskCreatedEvent> { it.payload.personContractualRisk.personId == personContractualRisk.personId }) } returns mockk()

                val result = service.createByPersonHealthConditionContractualRiskList(person.id, phccrs)

                assertThat(result).isSuccessWithData(personContractualRisk)

                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { personContractualRiskDataService.add(any()) }
                verifyOnce {
                    PersonContractualRiskLogic.buildPersonContractualRiskWhenCrContainsJust0(
                        any(),
                        any(),
                        any()
                    )
                }
            }
    }

    @Test
    fun `#findCurrentByPersonIds returns latest PersonContractualRisk`() = runBlocking {
        val personId = PersonId()

        val currentTime = LocalDateTime.now()

        val personContractualRisk1 =
            TestModelFactory.buildPersonContractualRisk(
                personId = personId,
                createdAt = currentTime.minusDays(10)
            )
        val personContractualRiskModel1 = personContractualRisk1.toModel()
        val personContractualRisk2 =
            TestModelFactory.buildPersonContractualRisk(
                personId = personId,
                createdAt = currentTime.minusDays(100)
            )
        val personContractualRiskModel2 = personContractualRisk2.toModel()
        val latestPersonContractualRisk =
            TestModelFactory.buildPersonContractualRisk(
                personId = personId,
                createdAt = currentTime
            )
        val latestPersonContractualRiskModel = latestPersonContractualRisk.toModel()

        coEvery {
            personContractualRiskDataService.find(queryEq {
                where { this.personId.inList(listOf(personId)) }
            })
        } returns listOf(
            personContractualRiskModel1,
            personContractualRiskModel2,
            latestPersonContractualRiskModel
        )

        val result = service.findCurrentByPersonIds(listOf(personId))

        assertThat(result).isSuccessWithData(listOf(latestPersonContractualRisk))

        coVerifyOnce { personContractualRiskDataService.find(any()) }
    }
}
