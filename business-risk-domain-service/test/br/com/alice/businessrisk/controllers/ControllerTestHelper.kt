package br.com.alice.businessrisk.controllers

import br.com.alice.authentication.Authenticator
import br.com.alice.businessrisk.module
import br.com.alice.common.helpers.RoutesTestHelper
import io.ktor.server.application.Application
import io.mockk.every
import io.mockk.mockkObject
import org.koin.core.context.loadKoinModules
import kotlin.test.BeforeTest

abstract class ControllerTestHelper : RoutesTestHelper() {
    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }
    override val moduleFunction: Application.() -> Unit = { loadKoinModules(module) }

    @BeforeTest
    override fun setup() {
        super.setup()

        mockkObject(Authenticator)
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "idToken"
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
    }
}
