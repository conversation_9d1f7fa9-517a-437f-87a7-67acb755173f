plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.business-risk-domain-service"
version = aliceBusinessRiskDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:business-risk-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-service"))
    implementation(project(":common-kafka"))
    implementation(project(":data-layer-client"))
    implementation(project(":business-risk-domain-client"))
    implementation(project(":health-condition-domain-client"))
    implementation(project(":appointment-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":business-domain-client"))
    implementation(project(":data-packages:business-risk-domain-service-data-package"))
    implementation(project(":data-packages:business-risk-domain-service-model-package"))
    implementation(project(":data-packages:appointment-domain-service-data-package"))
    implementation(project(":data-packages:health-condition-domain-service-data-package"))
    implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:membership-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation("org.apache.commons:commons-csv:$commonsCsvVersion")

    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
