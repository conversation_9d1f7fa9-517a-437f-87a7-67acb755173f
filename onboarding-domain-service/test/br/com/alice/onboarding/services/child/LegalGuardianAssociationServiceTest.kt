package br.com.alice.onboarding.services.child

import br.com.alice.common.ApplicationTest
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.Person
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExpireLegalGuardianAssociationStart
import br.com.alice.data.layer.models.LegalGuardianAssociationStatus
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.changeStatus
import br.com.alice.data.layer.services.LegalGuardianAssociationModelDataService
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.onboarding.ApplicationModule
import br.com.alice.onboarding.SERVICE_NAME
import br.com.alice.onboarding.client.AssociationIsNotValidatedYetException
import br.com.alice.onboarding.client.LegalGuardianAssociationCantBeUpdatedException
import br.com.alice.onboarding.client.LegalGuardianAssociationService
import br.com.alice.onboarding.client.ResponsibilityTermAlreadySignedException
import br.com.alice.onboarding.converters.toModel
import br.com.alice.onboarding.event.ExpireLegalGuardianAssociationStartEvent
import br.com.alice.onboarding.module
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import org.koin.ktor.ext.getKoin
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Ignore
import kotlin.test.Test

class LegalGuardianAssociationServiceTest : ApplicationTest() {
    override val serviceName = SERVICE_NAME
    private val person = Person("<EMAIL>", PersonId())
    private val token = person.id.toString()
    private val data: LegalGuardianAssociationModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val personService: PersonService = mockk()
    private val contractRegistry: ContractRegistry = mockk()

    override val moduleFunction = { application: Application, module: Module ->
        val modules = ApplicationModule.dependencyInjectionModules + module +
                org.koin.dsl.module(createdAtStart = true) {
                    single { data }
                    single { kafkaProducerService }
                    single { personService }
                    single { contractRegistry }
                }
        application.module(modules)
    }

    @BeforeTest
    override fun setup(){
        super.setup()
        mockkStatic(LocalDateTime::class)
        val now = LocalDateTime.now()
        coEvery { LocalDateTime.now() } returns now
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }

    @Test
    fun `#valid should update with new status`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()
            val legalGuardianAssociationUpdated = legalGuardianAssociation.copy()
                .changeStatus(LegalGuardianAssociationStatusType.VALID, LocalDateTime.now())
            val legalGuardianAssociationUpdatedModel = legalGuardianAssociationUpdated.toModel()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()
            coEvery { data.update(legalGuardianAssociationUpdatedModel) } returns legalGuardianAssociationUpdatedModel.success()

            val result = service.valid(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociationUpdated)
        }
    }

    @Test
    fun `#valid should not update entity with status different from PENDING `() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.EXPIRED)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()

            val result = service.valid(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isFailureOfType(LegalGuardianAssociationCantBeUpdatedException::class)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#expire should update with new status`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()
            val legalGuardianAssociationUpdated = legalGuardianAssociation.copy()
                .changeStatus(LegalGuardianAssociationStatusType.EXPIRED, LocalDateTime.now())
            val legalGuardianAssociationUpdatedModel = legalGuardianAssociationUpdated.toModel()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()
            coEvery { data.update(legalGuardianAssociationUpdatedModel) } returns legalGuardianAssociationUpdatedModel.success()

            val result = service.expire(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociationUpdated)
        }
    }

    @Test
    fun `#expire should not update entity with status different from VALID `() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()

            val result = service.expire(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isFailureOfType(LegalGuardianAssociationCantBeUpdatedException::class)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#invalid should update with new status`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()
            val legalGuardianAssociationUpdated = legalGuardianAssociation.copy()
                .changeStatus(LegalGuardianAssociationStatusType.INVALID, LocalDateTime.now())
            val legalGuardianAssociationUpdatedModel = legalGuardianAssociationUpdated.toModel()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()
            coEvery { data.update(legalGuardianAssociationUpdatedModel) } returns legalGuardianAssociationUpdatedModel.success()

            val result = service.invalid(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociationUpdated)
        }
    }

    @Test
    fun `#invalid should not update entity with status different from PENDING`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery {
                data.findById(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel.success()

            val result = service.invalid(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isFailureOfType(LegalGuardianAssociationCantBeUpdatedException::class)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#archived should update with new status`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()
            val legalGuardianAssociationUpdated = legalGuardianAssociation.copy()
                .changeStatus(LegalGuardianAssociationStatusType.ARCHIVED, LocalDateTime.now())
            val legalGuardianAssociationUpdatedModel = legalGuardianAssociationUpdated.toModel()

            coEvery {
                data.findByIdOrNull(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel
            coEvery { data.update(legalGuardianAssociationUpdatedModel) } returns legalGuardianAssociationUpdatedModel.success()

            val result = service.archive(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociationUpdated)
        }
    }

    @Test
    fun `#archived should not update entity with status different from PENDING or VALID`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.EXPIRED)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery {
                data.findByIdOrNull(legalGuardianAssociation.personId)
            } returns legalGuardianAssociationModel

            val result = service.archive(legalGuardianAssociation.personId)
            ResultAssert.assertThat(result).isFailureOfType(LegalGuardianAssociationCantBeUpdatedException::class)

            coVerify(exactly = 0) { data.update(any()) }
        }
    }

    @Test
    fun `#create should save new LegalGuardianAssociation with pending status`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery { data.add(match { it.status == legalGuardianAssociationModel.status }) } returns legalGuardianAssociationModel.success()

            val result = service.create(
                personId = legalGuardianAssociation.personId,
                guardianId = legalGuardianAssociation.guardianId,
                degreeOfKinship = legalGuardianAssociation.degreeOfKinship,
                status = "PENDING",
            )
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociation)
        }
    }

    @Ignore
    @Test
    fun `#generateDateEighteenYearsAgo should generate two dates, the current month first and last day minus 18 years`() = runBlocking {
        authenticatedAs(token, person) {
            mockkStatic(LocalDate::class) {
                coEvery { LocalDate.now() } returns LocalDate.of(2022, 7, 5)
                val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

                val result = service.generateDateEighteenYearsAgo()
                ResultAssert.assertThat(result).isSuccessWithData(
                    LocalDateTime.of(2004, 7, 1, 0, 0) to
                            LocalDateTime.of(2004, 7, 31, 0, 0)
                )
            }
        }
    }

    @Test
    fun `#startExpireAssociation should set the correct date and start expire association event`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            val (beginningDateTime, endDateTime) = service.generateDateEighteenYearsAgo().get()

            coEvery {
                kafkaProducerService.produce(match { it: ExpireLegalGuardianAssociationStartEvent ->
                    it.payload.datesToSearch.first.year == beginningDateTime.year &&
                            it.payload.datesToSearch.first.month == beginningDateTime.month &&
                            it.payload.datesToSearch.first.dayOfMonth == beginningDateTime.dayOfMonth &&
                            it.payload.datesToSearch.second.year == endDateTime.year &&
                            it.payload.datesToSearch.second.month == endDateTime.month &&
                            it.payload.datesToSearch.second.dayOfMonth == endDateTime.dayOfMonth
                }, any())
            } returns mockk()

            val result = service.startExpireAssociation()
            ResultAssert.assertThat(result).isSuccess()
        }
    }

    @Test
    fun `#expireAssociationForAdult should set the correct date and start expire association event`() = runBlocking {
        authenticatedAs(token, person) {
            val associations = listOf(
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.INVALID)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.EXPIRED)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING)
                ),
                TestModelFactory.buildLegalGuardianAssociation(
                    person.id, LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
                ),
            ).map { it.toModel() }
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()
            val (beginningDateTime, endDateTime) = service.generateDateEighteenYearsAgo().get()
            val event = ExpireLegalGuardianAssociationStart(datesToSearch = beginningDateTime to endDateTime)

            coEvery { kafkaProducerService.produce(any()) } returns mockk()
            coEvery {
                personService.findByIdsAndDateOfBirth(
                    associations.map { it.id.toString() },
                    beginningDateTime,
                    endDateTime
                )
            } returns mockk()
            coEvery {
                data.find(queryEq {
                    where { this.status.eq(LegalGuardianAssociationStatusType.VALID.name) }
                })
            } returns associations.success()

            val result = service.expireAssociationForAdult(event)
            ResultAssert.assertThat(result).isSuccess()
        }
    }

    @Test
    fun `#signTerm should set the association as signed`() = runBlocking {
        authenticatedAs(token, person) {
            val personOnboarding = TestModelFactory.buildPersonOnboarding(
                personId = person.id,
                currentPhase = OnboardingPhase.CONTRACT,
            )
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID)
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery { data.findById(person.id) } returns legalGuardianAssociationModel.success()
            coEvery { data.update(match { it.isSigned == true }) } returns legalGuardianAssociationModel.success()
            coEvery { contractRegistry.start(any()) } returns personOnboarding.success()

            val result = service.signTerm(
                personId = legalGuardianAssociation.personId,
            )
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociation)
            coVerify(exactly = 1) { contractRegistry.start(any()) }
        }
    }

    @Test
    fun `#signTerm should throw ResponsibilityTermAlreadySignedException when the association is already signed`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.VALID),
                isSigned = true,
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery { data.findById(person.id) } returns legalGuardianAssociationModel.success()

            val result = service.signTerm(
                personId = legalGuardianAssociation.personId,
            )
            ResultAssert.assertThat(result).isFailureOfType(ResponsibilityTermAlreadySignedException::class)
            coVerify(exactly = 0) { contractRegistry.start(any()) }
        }
    }

    @Test
    fun `#signTerm should throw AssociationIsNotValidatedYetException when the association was not validated yet`() = runBlocking {
        authenticatedAs(token, person) {
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                person.id,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING),
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()
            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery { data.findById(person.id) } returns legalGuardianAssociationModel.success()

            val result = service.signTerm(
                personId = legalGuardianAssociation.personId,
            )
            ResultAssert.assertThat(result).isFailureOfType(AssociationIsNotValidatedYetException::class)
            coVerify(exactly = 0) { contractRegistry.start(any()) }
        }
    }

    @Test
    fun `#findByPersonIdAndStatuses should fetch the correct association accordingly to the person id and the statuses`() = runBlocking {
        authenticatedAs(token, person) {
            val personId = PersonId()
            val guardianId = PersonId()
            val legalGuardianAssociation = TestModelFactory.buildLegalGuardianAssociation(
                personId,
                LegalGuardianAssociationStatus(LegalGuardianAssociationStatusType.PENDING),
                guardianId = guardianId,
            )
            val legalGuardianAssociationModel = legalGuardianAssociation.toModel()

            val service = testEngine.application.getKoin().get<LegalGuardianAssociationService>()

            coEvery {
                data.findOne(
                    queryEq {
                        where {
                            this.personId.eq(personId) and
                                    this.status.inList(
                                        listOf(
                                            LegalGuardianAssociationStatusType.PENDING,
                                            LegalGuardianAssociationStatusType.VALID
                                        )
                                    )
                        }.orderBy { this.createdAt }.sortOrder { desc }
                    }
                )
            } returns legalGuardianAssociationModel.success()

            val result = service.findByPersonIdAndStatuses(
                personId = personId,
                statuses = listOf(LegalGuardianAssociationStatusType.PENDING, LegalGuardianAssociationStatusType.VALID),
            )
            ResultAssert.assertThat(result).isSuccessWithData(legalGuardianAssociation)
        }
    }
}
