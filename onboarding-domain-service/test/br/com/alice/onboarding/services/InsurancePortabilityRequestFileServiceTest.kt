package br.com.alice.onboarding.services

import br.com.alice.common.ApplicationTest
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.Person
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InsurancePortabilityRequestFile
import br.com.alice.data.layer.models.InsurancePortabilityRequestFileType
import br.com.alice.data.layer.services.InsurancePortabilityRequestFileModelDataService
import br.com.alice.onboarding.ApplicationModule
import br.com.alice.onboarding.SERVICE_NAME
import br.com.alice.onboarding.client.InsurancePortabilityRequestFileService
import br.com.alice.onboarding.converters.toModel
import br.com.alice.onboarding.module
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import org.koin.ktor.ext.getKoin
import java.time.LocalDateTime
import kotlin.test.Test

class InsurancePortabilityRequestFileServiceTest: ApplicationTest() {
    override val serviceName = SERVICE_NAME

    private val person = Person("<EMAIL>", PersonId())
    private val token = person.id.toString()
    private val data: InsurancePortabilityRequestFileModelDataService = mockk()
    private val fileVault: FileVaultStorage = mockk()
    private val portabilityFile = TestModelFactory.buildInsurancePortabilityRequestFile(personId = person.id)
    private val portabilityFileModel = portabilityFile.toModel()
    private val portability = TestModelFactory.buildInsurancePortabilityRequest(personId = person.id)
    private val aliceFileLetter = TestModelFactory.buildAliceFile(
        fileName = "LETTER_apple.pdf"
    )
    private val aliceFilePaymentReceipt = TestModelFactory.buildAliceFile(
        fileName = "RECEIPT_apple.pdf"
    )
    private val parameters = mapOf(
        "date" to LocalDateTime.now().toString()
    )

    override val moduleFunction = { application: Application, module: Module ->
        val modules = ApplicationModule.dependencyInjectionModules + module +
                org.koin.dsl.module(createdAtStart = true) {
                    single { data }
                    single { fileVault }
                }
        application.module(modules)
    }

    @Test
    fun `#findByPerson should get a portability that has the person id`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            coEvery { data.findByPerson(person.id) } returns portabilityFileModel.success()

            val result = service.findByPerson(person.id)
            assertThat(result).isSuccessWithData(portabilityFile)
        }
    }

    @Test
    fun `#findById should get a portability that has the id`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            coEvery { data.findById(portabilityFile.id) } returns portabilityFileModel.success()

            val result = service.findById(portabilityFile.id)
            assertThat(result).isSuccessWithData(portabilityFile)
        }
    }

    @Test
    fun `#update should update a health insurance`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            coEvery { data.update(portabilityFileModel) } returns portabilityFileModel.success()

            val result = service.update(portabilityFile)
            assertThat(result).isSuccessWithData(portabilityFile)
        }
    }

    @Test
    fun `#createFromInsurancePortabilityRequestFile should create a new insurance portability request file`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            coEvery { data.add(portabilityFileModel) } returns portabilityFileModel.success()

            val result = service.createFromInsurancePortabilityRequestFile(portabilityFile)
            assertThat(result).isSuccessWithData(portabilityFile)
        }
    }

    @Test
    fun `#create should create a new insurance portability request file with type LETTER`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            val requestParameters = InsurancePortabilityRequestFile(
                personId = person.id,
                insurancePortabilityRequestId = portability.id,
                file = aliceFileLetter,
                type = InsurancePortabilityRequestFileType.LETTER,
                createdAt = LocalDateTime.parse(parameters["date"]),
            )

            coEvery { data.add(any()) } returns portabilityFileModel.success()

            val result = service.create(
                portability,
                aliceFileLetter,
                parameters,
            )
            assertThat(result).isSuccessWithData(portabilityFile)

            coVerify(exactly = 1) {
                data.add(match {
                    it.type.name == InsurancePortabilityRequestFileType.LETTER.name &&
                            it.createdAt == requestParameters.createdAt
                })
            }
        }
    }

    @Test
    fun `#create should create a new insurance portability request file with type PAYMENT_RECEIPT`() = runBlocking {
        authenticatedAs(token, person) {
            val service = testEngine.application.getKoin().get<InsurancePortabilityRequestFileService>()

            val requestParameters = InsurancePortabilityRequestFile(
                personId = person.id,
                insurancePortabilityRequestId = portability.id,
                file = aliceFileLetter,
                type = InsurancePortabilityRequestFileType.PAYMENT_RECEIPT,
                createdAt = LocalDateTime.parse(parameters["date"]),
            )

            coEvery { data.add(any()) } returns portabilityFileModel.success()

            val result = service.create(
                portability,
                aliceFilePaymentReceipt,
                parameters,
            )
            assertThat(result).isSuccessWithData(portabilityFile)

            coVerify(exactly = 1) {
                data.add(match {
                    it.type.name == requestParameters.type.name &&
                            it.createdAt == requestParameters.createdAt
                })
            }
        }
    }
}
