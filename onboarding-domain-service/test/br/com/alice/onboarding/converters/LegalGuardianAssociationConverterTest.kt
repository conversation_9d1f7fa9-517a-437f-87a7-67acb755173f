package br.com.alice.onboarding.converters

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.DegreeOfKinship
import br.com.alice.data.layer.models.LegalGuardianAssociation
import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatus
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusType
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class LegalGuardianAssociationConverterTest {

    private val legalGuardianAssociation = LegalGuardianAssociation(
        personId = PersonId(),
        guardianId = PersonId(),
        degreeOfKinship = DegreeOfKinship.MOTHER,
        statusHistory = listOf(LegalGuardianAssociationStatus(
            legalGuardianAssociationStatusType = LegalGuardianAssociationStatusType.PENDING,
            updatedAt = LocalDateTime.now().minusDays(1),
        )),
        status = LegalGuardianAssociationStatusType.PENDING,
        isSigned = true,
    )

    private val legalGuardianAssociationModel = LegalGuardianAssociationModel(
        id = legalGuardianAssociation.id,
        version = legalGuardianAssociation.version,
        createdAt = legalGuardianAssociation.createdAt,
        updatedAt = legalGuardianAssociation.updatedAt,
        personId = legalGuardianAssociation.personId,
        guardianId = legalGuardianAssociation.guardianId,
        degreeOfKinship = DegreeOfKinship.MOTHER,
        statusHistory = listOf(LegalGuardianAssociationStatusModel(
            legalGuardianAssociationStatusType = LegalGuardianAssociationStatusType.PENDING,
            updatedAt = legalGuardianAssociation.statusHistory!!.first().updatedAt,
        )
        ),
        status = LegalGuardianAssociationStatusType.PENDING,
        isSigned = true,
    )

    @Test
    fun testToTransport() {
        assertThat(legalGuardianAssociationModel.toTransport()).isEqualTo(legalGuardianAssociation)
    }

    @Test
    fun testToModel() {
        assertThat(legalGuardianAssociation.toModel()).isEqualTo(legalGuardianAssociationModel)
    }

}
