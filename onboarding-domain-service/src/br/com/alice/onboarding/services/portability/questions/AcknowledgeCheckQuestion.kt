package br.com.alice.onboarding.services.portability.questions

import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason.DROPPED

object AcknowledgeCheckQuestion : InsurancePortabilityQuestion {
    override fun validate(answer: String) =
        if (answer.trim().lowercase() == "true")
            InsurancePortabilityValidation(valid = true)
        else
            InsurancePortabilityValidation(valid = false, declineReason = DROPPED)
}
