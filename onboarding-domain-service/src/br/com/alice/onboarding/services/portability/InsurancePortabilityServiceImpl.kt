package br.com.alice.onboarding.services.portability

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.orNull
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.InsurancePortabilityMissingDocumentFile
import br.com.alice.data.layer.models.InsurancePortabilityRequest
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswer
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswerV2
import br.com.alice.data.layer.models.InsurancePortabilityRequestApprovedPackage
import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason
import br.com.alice.data.layer.models.InsurancePortabilityRequestModel
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.data.layer.models.InsurancePortabilityRequestStep
import br.com.alice.data.layer.models.InsurancePortabilityRequestType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.OnboardingPhase.PORTABILITY
import br.com.alice.data.layer.models.OnboardingPhase.PORTABILITY_REVIEW
import br.com.alice.data.layer.services.InsurancePortabilityRequestModelDataService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.InsurancePortabilityNotRequestedException
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.onboarding.client.PortabilityCannotBeApprovedException
import br.com.alice.onboarding.client.PortabilityCannotBeCancelledException
import br.com.alice.onboarding.client.PortabilityCannotBeChangedException
import br.com.alice.onboarding.client.PortabilityCannotBeDeclinedException
import br.com.alice.onboarding.client.PortabilityCannotBeRequestedException
import br.com.alice.onboarding.client.PortabilityMissingDocumentsCannotBeEmptyException
import br.com.alice.onboarding.client.PortabilityRequirementsCannotBeChangedException
import br.com.alice.onboarding.converters.toModel
import br.com.alice.onboarding.converters.toTransport
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestApprovedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestDeclinedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestSubmittedEvent
import br.com.alice.onboarding.services.portability.questions.InsurancePortabilityQuestion
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class InsurancePortabilityServiceImpl(
    private val data: InsurancePortabilityRequestModelDataService,
    private val onboardingService: OnboardingService,
    private val kafkaProducerService: KafkaProducerService,
) : InsurancePortabilityService {

    override suspend fun findByPerson(personId: PersonId) =
        data.findByPerson(personId)
            .map { it.toTransport() }

    override suspend fun findByPersonAndArchivedPortability(personId: PersonId): Result<InsurancePortabilityRequest, Throwable> =
        data.findByPersonAndArchivedPortability(personId)
            .map { it.toTransport() }

    override suspend fun findLastByPerson(personId: PersonId): Result<InsurancePortabilityRequest, Throwable> =
        data.findOne {
            where { this.personId.eq(personId) }
            orderBy { this.createdAt }
                .sortOrder { desc }
                .limit { 1 }
        }.map { it.toTransport() }

    override suspend fun findById(id: UUID) =
        data.findById(id).map { it.toTransport() }

    override suspend fun listByPerson(personId: PersonId) =
        data.findAllByPerson(personId).mapEach { it.toTransport() }

    override suspend fun listByPersonAndArchivedPortability(personId: PersonId) =
        data.findAllByPersonAndArchivedPortability(personId).mapEach { it.toTransport() }

    override suspend fun findApprovedByPerson(personId: PersonId) =
        data.findApprovedByPerson(personId).map { it.toTransport() }

    @Deprecated("Version 3 requires approvedPackage, healthInsuranceCode and hospitalCompatibilityAnsCode")
    override suspend fun approve(request: InsurancePortabilityRequest, notes: String): Result<InsurancePortabilityRequest, Throwable> {
        if (request.approved) return request.success()
        val onboarding = onboardingService.findByPerson(request.personId).get()
        val canApprove = canChangePortabilityStatus(onboarding.currentPhase)

        if (!canApprove) {
            return PortabilityCannotBeChangedException(onboarding.currentPhase).failure()
        }

        val approvedRequest = request.approve(notes = notes.orNull())

        return data.update(approvedRequest.toModel())
            .map { it.toTransport() }
            .andThen { onboardingService.changePhaseTo(it.personId, OnboardingPhase.WAITING_FOR_REVIEW) }
            .then { logger.info("portability request approved", "request" to it) }
            .then { kafkaProducerService.produce(InsurancePortabilityRequestApprovedEvent(it)) }
    }

    override suspend fun approveSpecific(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> {
        if (request.isFinished) return PortabilityCannotBeApprovedException(request.status).failure()
        if (request.type != InsurancePortabilityRequestType.SPECIFIC) return PortabilityCannotBeApprovedException(request.status).failure()

        val approvedRequest = request
            .copy(
                status = InsurancePortabilityRequestStatus.APPROVED,
                finishedAt = LocalDateTime.now(),
            )
            .changeStep(InsurancePortabilityRequestStep.FINISHED)

        return data.update(approvedRequest.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(InsurancePortabilityRequestApprovedEvent(it)) }
    }

    override suspend fun approveV3(
        request: InsurancePortabilityRequest,
        approvedPackage: InsurancePortabilityRequestApprovedPackage,
        healthInsuranceCode: String,
        hospitalCompatibilityAnsCode: String
    ): Result<InsurancePortabilityRequest, Throwable> {
        if (request.isFinished) return PortabilityCannotBeApprovedException(request.status).failure()

        val approvedRequest = request
            .approve(approvedPackage, healthInsuranceCode, hospitalCompatibilityAnsCode)
            .changeStep(InsurancePortabilityRequestStep.FINISHED)


        return data.update(approvedRequest.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(InsurancePortabilityRequestApprovedEvent(it)) }
    }

    @Deprecated("Version 3 requires a list o reasons")
    override suspend fun decline(
        request: InsurancePortabilityRequest,
        declinedReason: InsurancePortabilityRequestDeclineReason,
        notes: String
    ): Result<InsurancePortabilityRequest, Throwable> {
        if (request.declined) return request.success()

        val onboarding = onboardingService.findByPerson(request.personId).get()
        val canDeclineRequest = canChangePortabilityStatus(onboarding.currentPhase)

        if (!canDeclineRequest) {
            return PortabilityCannotBeChangedException(onboarding.currentPhase).failure()
        }

        val declinedRequest = request.decline(declinedReason, notes.orNull())
        return data.update(declinedRequest.toModel())
            .map { it.toTransport() }
            .andThen { onboardingService.changePhaseTo(it.personId, PORTABILITY_REVIEW.nextPhase()) }
            .then { logger.info("portability request declined", "request" to it) }
            .then {
                val event = InsurancePortabilityRequestDeclinedEvent(it)
                kafkaProducerService.produce(event)
            }
    }

    override suspend fun declineV3(
        request: InsurancePortabilityRequest,
        declineReasons: List<InsurancePortabilityRequestDeclineReason>,
        approvedPackage: InsurancePortabilityRequestApprovedPackage?,
        notes: String?,
    ): Result<InsurancePortabilityRequest, Throwable> {
        if (request.isFinished) return PortabilityCannotBeDeclinedException(request.status).failure()

        val declinedRequest = request
            .decline(declineReasons, notes, approvedPackage)
            .changeStep(InsurancePortabilityRequestStep.FINISHED)

        return data.update(declinedRequest.toModel())
            .map { it.toTransport() }
    }

    override suspend fun submitToReview(
        request: InsurancePortabilityRequest,
        notes: String
    ): Result<InsurancePortabilityRequest, Throwable> {
        if (request.pending) return request.success()
        val finishedRequest = request.submitToReview(notes.orNull())

        return data.update(finishedRequest.toModel())
            .map { it.toTransport() }
            .andThen { onboardingService.changePhaseTo(it.personId, PORTABILITY_REVIEW) }
            .then { logger.info("portability request submitted", "request" to it) }
            .then {
                val event = InsurancePortabilityRequestSubmittedEvent(it)
                kafkaProducerService.produce(event)
            }
    }

    override suspend fun skip(personId: PersonId) = onboardingService
        .changePhaseTo(personId, PORTABILITY_REVIEW.nextPhase())

    override suspend fun cancel(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> {
        if (request.declined) return request.success()

        return validateCancel(request)
            .flatMap { update(it.cancel()) }
    }


    override suspend fun moveToUploadStep(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        cancel(request)
            .map {
                it.copy(
                    id = RangeUUID.generate(),
                    status = InsurancePortabilityRequestStatus.IN_PROGRESS,
                    step = if (it.adhesionContract) InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER_PAYMENT_RECEIPT else InsurancePortabilityRequestStep.DOCUMENT_UPLOAD_LETTER,
                    archived = false,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    declinedReasons = emptyList(),
                    missingDocuments = emptyList(),
                    notes = null,
                )
            }
            .then { onboardingService.changePhaseTo(request.personId, PORTABILITY) }
            .flatMap { data.add(it.toModel()) }
            .map { it.toTransport() }

    override suspend fun request(
        personId: PersonId,
        answers: List<InsurancePortabilityRequestAnswer>,
        answersV2: List<InsurancePortabilityRequestAnswerV2>,
        productId: UUID?
    ): Result<InsurancePortabilityRequest, Throwable> {
        val portability = InsurancePortabilityRequest(
            personId = personId,
            productId = productId,
            answers = answers,
            answersV2 = answersV2,
        )

        return data
            .findAllByPerson(personId)
            .mapEach { it.toTransport() }
            .flatMap { checkPreviousRequests(it, portability) }
            .flatMap { data.add(it.toModel()) }
            .map { it.toTransport() }
    }

    override suspend fun add(insurancePortabilityRequest: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        data.add(insurancePortabilityRequest.toModel())
            .map { it.toTransport() }

    override suspend fun create(personId: PersonId, type: InsurancePortabilityRequestType): Result<InsurancePortabilityRequest, Throwable> {
        val previous = data.findAllByPerson(personId).get()
        if (previous.any { it.pending }) return PortabilityCannotBeRequestedException("has pending requests").failure()

        return data.add(InsurancePortabilityRequestModel(
            personId = personId,
            status = InsurancePortabilityRequestStatus.PENDING,
            type = type,
            step = InsurancePortabilityRequestStep.ANALYSIS,
            pendingAt = LocalDateTime.now(),)
        ).map { it.toTransport() }
    }

    override suspend fun update(insurancePortabilityRequest: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        data.update(insurancePortabilityRequest.toModel())
            .map { it.toTransport() }

    override suspend fun updateRequirements(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        validateUpdateRequirements(request)
            .flatMap { update(it.updateSuggestion()) }

    private fun validateUpdateRequirements(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        if (request.status !in listOf(InsurancePortabilityRequestStatus.DECLINED, InsurancePortabilityRequestStatus.APPROVED)) request.success()
        else PortabilityRequirementsCannotBeChangedException(request).failure()

    private fun validateCancel(request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> =
        if (request.status in listOf(InsurancePortabilityRequestStatus.IN_PROGRESS, InsurancePortabilityRequestStatus.PENDING)) request.success()
        else PortabilityCannotBeCancelledException(request).failure()

    private fun checkPreviousRequests(previousRequests: List<InsurancePortabilityRequest>, request: InsurancePortabilityRequest): Result<InsurancePortabilityRequest, Throwable> {
        val hasPending = previousRequests.any { it.pending }
        val hasSameProductAndApproved = previousRequests.any { !it.archived && it.approved && it.productId == request.productId }

        if (hasPending) return PortabilityCannotBeRequestedException("has pending requests").failure()
        if (hasSameProductAndApproved) return PortabilityCannotBeRequestedException("request for product has already been approved").failure()

        return request.success()
    }

    override suspend fun answer(personId: PersonId, answer: InsurancePortabilityRequestAnswer): Result<InsurancePortabilityRequest, Throwable> =
        findByPerson(personId)
            .foldNotFound { InsurancePortabilityNotRequestedException(personId).failure() }
            .flatMap { portabilityRequest ->
                val question = InsurancePortabilityQuestionFactory.createQuestion(answer.questionType)
                val validation = question.validate(answer.answer)

                val updatedPortabilityRequest = portabilityRequest.answer(answer)

                if (!validation.valid)
                    decline(updatedPortabilityRequest, validation.declineReason!!)
                else if (updatedPortabilityRequest.nextQuestion() == null)
                    submitToReview(updatedPortabilityRequest)
                else
                    data.update(updatedPortabilityRequest.toModel())
                        .map { it.toTransport() }
                        .then { onboardingService.changePhaseTo(it.personId, PORTABILITY) }
            }

    override suspend fun answerV2(personId: PersonId, answer: InsurancePortabilityRequestAnswerV2): Result<InsurancePortabilityRequest, Throwable> =
        findByPerson(personId)
            .foldNotFound { InsurancePortabilityNotRequestedException(personId).failure() }
            .mapPair { InsurancePortabilityQuestionV2Factory.createQuestion(answer.questionType) }
            .flatMap { (question, portabilityRequest) -> answerQuestionV2(answer, question, portabilityRequest) }

    override suspend fun requestDocuments(
        insurancePortabilityRequest: InsurancePortabilityRequest,
        missingDocuments: List<InsurancePortabilityMissingDocumentFile>,
        notes: String?,
    ): Result<InsurancePortabilityRequest, Throwable> =
        validateRequestDocuments(insurancePortabilityRequest, missingDocuments)
            .map { it
                .requestDocuments(missingDocuments, notes)
                .changeStep(InsurancePortabilityRequestStep.FINISHED)
            }
            .flatMap { update(it) }

    private fun validateRequestDocuments(request: InsurancePortabilityRequest, missingDocuments: List<InsurancePortabilityMissingDocumentFile>): Result<InsurancePortabilityRequest, Throwable> =
        if (missingDocuments.isEmpty()) PortabilityMissingDocumentsCannotBeEmptyException(request).failure()
        else validateCancel(request)

    private suspend fun answerQuestionV2(
        answer: InsurancePortabilityRequestAnswerV2,
        question: InsurancePortabilityQuestion,
        portabilityRequest: InsurancePortabilityRequest
    ): Result<InsurancePortabilityRequest, Throwable> {
        val validation = question.validate(answer.answer)
        val isValid = validation.valid
        val updatedPortabilityRequest = portabilityRequest.answer(answer)
        val isPortabilityRequestFinished = updatedPortabilityRequest.nextQuestionV2() == null

        return when {
            !isValid -> decline(updatedPortabilityRequest, validation.declineReason!!)
            isPortabilityRequestFinished -> submitToReview(updatedPortabilityRequest)
            else -> data.update(updatedPortabilityRequest.toModel())
                .map { it.toTransport() }
                .then { onboardingService.changePhaseTo(it.personId, PORTABILITY) }
        }
    }

    override suspend fun archive(personId: PersonId) =
        findByPerson(personId)
            .foldNotFound { InsurancePortabilityNotRequestedException(personId).failure() }
            .map { it.archive() }
            .flatMap { data.update(it.toModel()) }
            .map { it.toTransport() }

    override suspend fun archiveAll(personId: PersonId) =
        data.findAllByPerson(personId)
            .foldNotFound { InsurancePortabilityNotRequestedException(personId).failure() }
            .mapEach { it.archive() }
            .flatMapEach { data.update(it) }
            .mapEach { it.toTransport() }

    override suspend fun archivePortability(portabilityRequest: InsurancePortabilityRequest) =
        data.update(portabilityRequest.toModel().archive())
            .map { it.toTransport() }

    override suspend fun unarchivePortability(id: UUID) =
            data
                .findOne { where { this.id.eq(id) } }
                .flatMap { data.update(it.unarchive()) }
                .map { it.toTransport() }

    private fun canChangePortabilityStatus(phase: OnboardingPhase) =
        OnboardingPhase.SHOPPING < phase && phase < OnboardingPhase.PAYMENT
}

