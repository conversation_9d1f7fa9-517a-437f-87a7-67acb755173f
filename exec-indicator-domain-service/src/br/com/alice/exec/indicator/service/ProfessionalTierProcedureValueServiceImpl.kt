package br.com.alice.exec.indicator.service

import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.ProfessionalTierProcedureValue
import br.com.alice.data.layer.services.ProfessionalTierProcedureValueModelDataService
import br.com.alice.data.layer.services.ProfessionalTierProcedureValueModelDataService.FieldOptions
import br.com.alice.exec.indicator.client.ProfessionalTierProcedureValueService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.util.UUID

class ProfessionalTierProcedureValueServiceImpl(
    private val data: ProfessionalTierProcedureValueModelDataService
) : ProfessionalTierProcedureValueService,
    Adder<ProfessionalTierProcedureValue>,
    Getter<ProfessionalTierProcedureValue>,
    Updater<ProfessionalTierProcedureValue> {

    override suspend fun get(id: UUID): Result<ProfessionalTierProcedureValue, Throwable> = useReadDatabase {
        data.get(id).map { it.toTransport() }
    }

    override suspend fun add(model: ProfessionalTierProcedureValue): Result<ProfessionalTierProcedureValue, Throwable> =
        data.add(model.toModel()).map { it.toTransport() }

    override suspend fun update(model: ProfessionalTierProcedureValue): Result<ProfessionalTierProcedureValue, Throwable> =
        data.update(model.toModel()).map { it.toTransport() }

    override suspend fun findByTussCodeList(tussCodes: List<String>) : Result<List<ProfessionalTierProcedureValue>, Throwable> =
        useReadDatabase {
            data.find { where {
                this.tussCode.inList(tussCodes)
            } }.map {
                it.toTransport()
            }
        }

    override suspend fun findByProfessionalTierProcedure(
        medicalSpecialtyId: UUID,
        tier: SpecialistTier,
        tussCode: String
    ): Result<List<ProfessionalTierProcedureValue>, Throwable> = useReadDatabase {
        data.find {
            where {
                this.tier.eq(tier) and this.tussCode.eq(tussCode) and this.medicalSpecialtyId.eq(medicalSpecialtyId)
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun getByRange(range: IntRange): Result<List<ProfessionalTierProcedureValue>, Throwable> =
        useReadDatabase {
            data.find {
                orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.last }
            }.map {
                it.toTransport()
            }
        }

    override suspend fun getActivesByRange(range: IntRange): Result<List<ProfessionalTierProcedureValue>, Throwable> =
        useReadDatabase {
            data.find {
                where {
                    currentVersion()
                }
                    .orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.last }
            }.map {
                it.toTransport()
            }
        }

    override suspend fun getBySpecialtyAndTier(
        medicalSpecialtyId: UUID?,
        tier: List<SpecialistTier>
    ): Result<List<ProfessionalTierProcedureValue>, Throwable> = useReadDatabase {
        if (medicalSpecialtyId == null && tier.isNotEmpty()) {
            data.find {
                where {
                    this.tier.inList(tier) and currentVersion()
                }
            }.map {
                it.toTransport()
            }
        } else if (medicalSpecialtyId != null && tier.isEmpty()) {
            data.find {
                where {
                    this.medicalSpecialtyId.eq(medicalSpecialtyId) and currentVersion()
                }
            }.map {
                it.toTransport()
            }
        } else {
            data.find {
                where {
                    this.medicalSpecialtyId.eq(medicalSpecialtyId!!) and this.tier.inList(tier) and currentVersion()
                }
            }.map {
                it.toTransport()
            }
        }
    }

    @OptIn(OrPredicateUsage::class)
    private fun currentVersion(): Predicate {
        val now = LocalDate.now()
        return FieldOptions().beginAt.lessEq(now) and scope(FieldOptions().endAt.greaterEq(now) or FieldOptions().endAt.isNull())
    }

}
