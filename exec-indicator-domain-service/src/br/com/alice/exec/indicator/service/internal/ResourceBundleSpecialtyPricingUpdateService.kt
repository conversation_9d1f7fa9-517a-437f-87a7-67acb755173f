package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingUpdateModelDataService
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class ResourceBundleSpecialtyPricingUpdateService(
    private val resourceBundleSpecialtyPricingUpdateDataService: ResourceBundleSpecialtyPricingUpdateModelDataService,
    private val kafkaProducerService: KafkaProducerService,
) {
    suspend fun getProcessingResourceBundleSpecialtyPricingUpdate(): Result<ResourceBundleSpecialtyPricingUpdate, Throwable> {
        return resourceBundleSpecialtyPricingUpdateDataService.findOne {
            where {
                this.completedAt.isNull()
            }
        }.map {
            it.toTransport()
        }
    }

    suspend fun get(id: UUID): Result<ResourceBundleSpecialtyPricingUpdate, Throwable> {
        return resourceBundleSpecialtyPricingUpdateDataService.findOne {
            where {
                this.id.eq(id)
            }
        }.map {
            it.toTransport()
        }
    }

    suspend fun update(model: ResourceBundleSpecialtyPricingUpdate) =
        resourceBundleSpecialtyPricingUpdateDataService.update(model.toModel()).map { it.toTransport() }

    suspend fun getResourceBundleSpecialtyPricingUpdateHistory(
        filters: PricingUpdateHistoryFilters,
        range: IntRange,
    ) = useReadDatabase {
        resourceBundleSpecialtyPricingUpdateDataService.find {
            where {
                this.processingAt.isNotNull()
                    .statusToQuery(filters.status)
                    .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                    .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
            }
                .orderBy { this.createdAt }
                .sortOrder { desc }
                .offset { range.first() }
                .limit { range.count() }
        }
            .map { it.map { model -> model.toTransport() } }
    }

    suspend fun add(
        resourceBundleSpecialtyPricingUpdate: ResourceBundleSpecialtyPricingUpdate,
    ): Result<ResourceBundleSpecialtyPricingUpdate, Throwable> =
        resourceBundleSpecialtyPricingUpdateDataService.add(resourceBundleSpecialtyPricingUpdate.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(ResourceBundleSpecialtyPricingUpdateCreatedEvent(it.id)) }

    suspend fun countResourceBundleSpecialtyPricingUpdateHistory(
        filters: PricingUpdateHistoryFilters,
    ) = useReadDatabase {
        resourceBundleSpecialtyPricingUpdateDataService.count {
            where {
                this.processingAt.isNotNull()
                    .statusToQuery(filters.status)
                    .and(filters.startDate?.let { this.createdAt.greaterEq(it.atBeginningOfTheDay()) })
                    .and(filters.endDate?.let { this.createdAt.lessEq(it.atEndOfTheDay()) })
            }
        }
    }


    @OptIn(OrPredicateUsage::class)
    private fun Predicate.statusToQuery(statuses: List<ResourceBundleSpecialtyPricingUpdateStatus>?): Predicate {
        if (statuses.isNullOrEmpty()) return this
        val combined = statuses.map {
            when (it) {
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING -> isProcessing()
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED -> isProcessed()
                ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED_WITH_ERRORS -> isProcessedWithErrors()
                ResourceBundleSpecialtyPricingUpdateStatus.PARSING_ERROR -> isParsingError()
            }
        }.reduce { acc, predicate -> acc.or(predicate) }
        return this.and(scope(combined))
    }

    private fun isProcessing() =
        scope(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().completedAt.isNull()
            .and(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().parsingError.isNull()))

    private fun isProcessed() =
        scope(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().completedAt.isNotNull()
            .and(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().parsingError.isNull())
            .and(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().failedRowsCount.eq(0)))

    private fun isProcessedWithErrors() =
        scope(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().completedAt.isNotNull()
            .and(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().parsingError.isNull())
            .and(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().failedRowsCount.greater(0)))

    private fun isParsingError() =
        scope(ResourceBundleSpecialtyPricingUpdateModelDataService.FieldOptions().parsingError.isNotNull())
}
