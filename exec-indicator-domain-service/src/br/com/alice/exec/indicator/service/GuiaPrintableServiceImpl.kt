package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toBrazilianTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.AnsHospitalizationRegime
import br.com.alice.data.layer.models.AttachmentStatus
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.GuiaPrintableService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.models.GuiaChemotherapyPrintable
import br.com.alice.exec.indicator.models.GuiaCommonPrintableData
import br.com.alice.exec.indicator.models.GuiaContentFetched
import br.com.alice.exec.indicator.models.GuiaHospitalizationExtensionPrintable
import br.com.alice.exec.indicator.models.GuiaHospitalizationPrintable
import br.com.alice.exec.indicator.models.GuiaOpmePrintable
import br.com.alice.exec.indicator.models.GuiaPrintable
import br.com.alice.exec.indicator.models.GuiaProceduresPrintable
import br.com.alice.exec.indicator.models.GuiaRadiotherapyPrintable
import br.com.alice.exec.indicator.models.GuiaRequestedOpmePrintable
import br.com.alice.exec.indicator.models.RequestedDrugsPrintable
import br.com.alice.exec.indicator.service.internal.AttachmentChemotherapyService
import br.com.alice.exec.indicator.service.internal.AttachmentOpmeService
import br.com.alice.exec.indicator.service.internal.AttachmentRadiotherapyService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.lang.Math.sqrt
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.time.LocalDateTime
import java.util.UUID

class GuiaPrintableServiceImpl(
    private val totvsGuiaService: TotvsGuiaService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val providerUnitService: ProviderUnitService,
    private val personService: PersonService,
    private val healthcareResourceService: HealthcareResourceService,
    private val hospitalizationInfoService: HospitalizationInfoService,
    private val attachmentOpmeService: AttachmentOpmeService,
    private val attachmentChemotherapyService: AttachmentChemotherapyService,
    private val attachmentRadiotherapyService: AttachmentRadiotherapyService,
) : GuiaPrintableService {

    private val ansNumber: Int = 421928
    private val authorizedAndExecutedStatuses = listOf(
        MvAuthorizedProcedureStatus.AUTHORIZED, MvAuthorizedProcedureStatus.EXECUTED
    )

    override suspend fun getGuiaHospitalizationPrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ): Result<List<GuiaHospitalizationPrintable>, Throwable> = coroutineScope {
        logger.info(
            "GuiaPrintableServiceImpl::getGuiaHospitalizationPrintable",
            "totvsGuiaId" to totvsGuiaId,
        )

        val results = mutableListOf<GuiaHospitalizationPrintable>()

        val mvAuthorizedProcedures = mvAuthorizedProcedureService.findByTotvsGuiaIdAndStatus(
            totvsGuiaId, authorizedAndExecutedStatuses
        ).getOrElse { emptyList() }

        if (mvAuthorizedProcedures.isEmpty()) return@coroutineScope emptyList<GuiaHospitalizationPrintable>().success()

        val guiaCommonDataDeferred = async { getGuiaCommonPrintableData(totvsGuiaId, providerUnitId, personId) }
        val healthcareResourcesDeferred = async { getDetailedProcedures(mvAuthorizedProcedures) }
        val hospitalizationInfoDeferred = async { hospitalizationInfoService.getByTotvsGuiaId(totvsGuiaId) }

        val guiaCommonData = guiaCommonDataDeferred.await()
        val healthcareResources = healthcareResourcesDeferred.await()
        val hospitalizationInfoResult = hospitalizationInfoDeferred.await()

        val groupedProcedures = groupedProcedures(mvAuthorizedProcedures, 12)

        groupedProcedures[totvsGuiaId]?.forEach { proceduresChunked ->
            hospitalizationInfoResult
                .map { hospitalizationInfo ->
                    results.add(
                        GuiaHospitalizationPrintable(
                            guiaCommonData = guiaCommonData,
                            suggestedDate = hospitalizationInfo.suggestedDate.toBrazilianDateFormat(),
                            attendanceCharacter = hospitalizationInfo.attendanceCharacter.code.toInt(),
                            type = hospitalizationInfo.type.code.toInt(),
                            attendanceRegime = AnsHospitalizationRegime.HOSPITAL.code.toInt(),
                            numberOfDays = hospitalizationInfo.numberOfDays,
                            opmeIndication = if (hospitalizationInfo.opmeIndication) "Sim" else "Nao",
                            chemotherapyIndication = if (hospitalizationInfo.chemotherapyIndication) "Sim" else "Nao",
                            clinicalIndication = hospitalizationInfo.clinicalIndication,
                            healthConditionCode = hospitalizationInfo.healthCondition.code,
                            accidentIndication = hospitalizationInfo.accidentIndication.code.toInt(),
                            procedures = proceduresChunked.map { item ->
                                val currentProcedure = healthcareResources.first { resource ->
                                    resource.code == item.procedureId
                                }
                                GuiaProceduresPrintable(
                                    code = item.procedureId!!,
                                    table = currentProcedure.tableType.orEmpty(),
                                    description = currentProcedure.description,
                                    quantity = item.extraGuiaInfo.quantity ?: 1,
                                    authorizedQuantity = item.extraGuiaInfo.quantity ?: 1,
                                    startTime = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianTimeFormat(),
                                    endTime = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianTimeFormat(),
                                )
                            },
                            admissionProbableDate = hospitalizationInfo.suggestedDate.toBrazilianDateFormat(),
                            authorizedNumberOfDays = hospitalizationInfo.numberOfDays,
                            requestedDate = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                            authorizedAt = hospitalizationInfo.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                        )
                    )
                }
        }

        return@coroutineScope Result.of { results.toList() }
    }

    override suspend fun getGuiaHospitalizationExtensionPrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ) = coroutineScope {
        coResultOf<List<GuiaHospitalizationExtensionPrintable>, Throwable> {
            logger.info(
                "GuiaPrintableServiceImpl::getGuiaHospitalizationExtensionPrintable",
                "totvsGuiaId" to totvsGuiaId,
            )

            val results = mutableListOf<GuiaHospitalizationExtensionPrintable>()

            val mvAuthorizedProcedures = mvAuthorizedProcedureService.findByTotvsGuiaIdAndStatus(
                totvsGuiaId, authorizedAndExecutedStatuses
            ).getOrElse { emptyList() }

            if (mvAuthorizedProcedures.isEmpty()) return@coResultOf emptyList<GuiaHospitalizationExtensionPrintable>()

            val guiaCommonDataDeferred = async { getGuiaCommonPrintableData(totvsGuiaId, providerUnitId, personId) }
            val healthcareResourcesDeferred = async { getDetailedProcedures(mvAuthorizedProcedures) }
            val hospitalizationInfoDeferred = async { hospitalizationInfoService.getByTotvsGuiaId(totvsGuiaId) }

            val guiaCommonData = guiaCommonDataDeferred.await()
            val healthcareResources = healthcareResourcesDeferred.await()
            val hospitalizationInfoResult = hospitalizationInfoDeferred.await()

            val groupedProcedures = groupedProcedures(mvAuthorizedProcedures, 9)

            groupedProcedures[totvsGuiaId]?.forEach { proceduresChunked ->
                hospitalizationInfoResult
                    .map { hospitalizationInfo ->
                        results.add(
                            GuiaHospitalizationExtensionPrintable(
                                guiaCommonData = guiaCommonData,
                                additionalNumberOfDays = hospitalizationInfo.numberOfDays,
                                clinicalIndication = hospitalizationInfo.clinicalIndication,
                                procedures = proceduresChunked.map { item ->
                                    val currentProcedure = healthcareResources.first { resource ->
                                        resource.code == item.procedureId
                                    }
                                    GuiaProceduresPrintable(
                                        code = item.procedureId!!,
                                        startTime = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianTimeFormat(),
                                        endTime = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianTimeFormat(),
                                        table = currentProcedure.tableType.orEmpty(),
                                        description = currentProcedure.description,
                                        quantity = item.extraGuiaInfo.quantity ?: 1,
                                    )
                                },
                                authorizedNumberOfDays = hospitalizationInfo.numberOfDays,
                                requestedDate = hospitalizationInfo.createdAt.toSaoPauloTimeZone()
                                    .toBrazilianDateFormat(),
                                authorizedAt = hospitalizationInfo.createdAt.toSaoPauloTimeZone()
                                    .toBrazilianDateFormat(),
                            )
                        )
                    }
            }

            results.toList()
        }
    }

    override suspend fun getGuiaOpmePrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ) = coResultOf<List<GuiaOpmePrintable>, Throwable> {
        logger.info(
            "GuiaPrintableServiceImpl::getGuiaOpmePrintable",
            "totvsGuiaId" to totvsGuiaId,
        )

        val results = mutableListOf<GuiaOpmePrintable>()

        val mvAuthorizedProcedures = totvsGuiaService.get(totvsGuiaId)
            .flatMap { totvsGuia ->
                mvAuthorizedProcedureService.findByTotvsGuiaIdAndStatus(
                    totvsGuia.referenceTotvsGuiaId!!, authorizedAndExecutedStatuses
                )
            }.getOrElse { emptyList() }

        if (mvAuthorizedProcedures.isEmpty()) return@coResultOf emptyList<GuiaOpmePrintable>()

        attachmentOpmeService.getByTotvsGuiaId(totvsGuiaId)
            .map { attachmentOpme ->
                if (attachmentOpme.status != AttachmentStatus.AUTHORIZED &&
                    attachmentOpme.status != AttachmentStatus.PARTIALLY_AUTHORIZED
                ) return@coResultOf emptyList<GuiaOpmePrintable>()

                val authorizedAndExecutedRequestedOpmes = attachmentOpme.requestedOpmes
                    .filter { it.status == MvAuthorizedProcedureStatus.AUTHORIZED || it.status == MvAuthorizedProcedureStatus.EXECUTED }

                if (authorizedAndExecutedRequestedOpmes.isEmpty()) return@coResultOf emptyList<GuiaOpmePrintable>()

                val guiaCommonData = getGuiaCommonPrintableData(totvsGuiaId, providerUnitId, personId)

                authorizedAndExecutedRequestedOpmes.chunked(6).map { requestedOpmeChunked ->
                    results.add(
                        GuiaOpmePrintable(
                            guiaCommonData = guiaCommonData,
                            technicalJustification = attachmentOpme.technicalJustification,
                            materialSpecification = attachmentOpme.materialSpecification,
                            observation = attachmentOpme.observation.orEmpty(),
                            requestedDate = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                            requestedOpmes = requestedOpmeChunked.map { chunk ->
                                GuiaRequestedOpmePrintable(
                                    table = chunk.opmeIdentification.table,
                                    code = chunk.opmeIdentification.code,
                                    description = chunk.opmeIdentification.description,
                                    requestedQuantity = chunk.requestedQuantity,
                                    requestedValue = chunk.requestedValue,
                                    authorizedRequestedQuantity = chunk.requestedQuantity,
                                    authorizedRequestedValue = chunk.requestedValue,
                                )
                            },
                            authorizedAt = attachmentOpme.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                        )
                    )
                }
            }

        results.toList()
    }

    override suspend fun getGuiaChemotherapyPrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ) = coResultOf<List<GuiaChemotherapyPrintable>, Throwable> {
        logger.info(
            "GuiaPrintableServiceImpl::getGuiaChemotherapyPrintable",
            "totvsGuiaId" to totvsGuiaId,
        )

        val results = mutableListOf<GuiaChemotherapyPrintable>()

        val mvAuthorizedProcedures = mvAuthorizedProcedureService.findByTotvsGuiaIdAndStatus(
            totvsGuiaId, authorizedAndExecutedStatuses
        ).getOrElse { emptyList() }

        if (mvAuthorizedProcedures.isEmpty()) return@coResultOf emptyList<GuiaChemotherapyPrintable>()

        totvsGuiaService.findByReferenceTotvsGuiaIdAndType(totvsGuiaId, MvUtil.TISS.CHEMOTHERAPY)
            .flatMap { referenceTotvsGuia -> attachmentChemotherapyService.getByTotvsGuiaId(referenceTotvsGuia.id) }
            .flatMapPair { totvsGuiaService.get(totvsGuiaId) }
            .map { (sadtTotvsGuia, attachmentChemotherapy) ->
                if (attachmentChemotherapy.status != AttachmentStatus.AUTHORIZED &&
                    attachmentChemotherapy.status != AttachmentStatus.PARTIALLY_AUTHORIZED
                ) return@coResultOf emptyList<GuiaChemotherapyPrintable>()

                val authorizedAndExecutedRequestedChemotherapy = attachmentChemotherapy.requestedDrugs
                    .filter { it.status == MvAuthorizedProcedureStatus.AUTHORIZED || it.status == MvAuthorizedProcedureStatus.EXECUTED }

                if (authorizedAndExecutedRequestedChemotherapy.isEmpty()) return@coResultOf emptyList<GuiaChemotherapyPrintable>()

                val guiaCommonData = getGuiaCommonPrintableData(
                    totvsGuiaId = attachmentChemotherapy.totvsGuiaId,
                    providerUnitId = providerUnitId,
                    personId = personId,
                    sadtTotvsGuia = sadtTotvsGuia
                )

                authorizedAndExecutedRequestedChemotherapy.chunked(8).map { requestedChemotherapyChunked ->
                    results.add(
                        GuiaChemotherapyPrintable(
                            guiaCommonData = guiaCommonData,
                            height = attachmentChemotherapy.height,
                            weight = attachmentChemotherapy.weight,
                            bodySurface = bodySurface(attachmentChemotherapy.height, attachmentChemotherapy.weight),
                            diagnosisDate = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.diagnosisDate.toBrazilianDateFormat(),
                            stage = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.stage.code.toInt(),
                            type = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.type.code.toInt(),
                            purpose = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.purpose.code.toInt(),
                            tumor = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.tumor.code.toInt(),
                            nodule = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.nodule.code.toInt(),
                            metastasis = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.metastasis.code.toInt(),
                            healthCondition = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.healthCondition.code.orEmpty(),
                            ecoGt = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.ecoGt.code.toInt(),
                            therapeuticPlan = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.therapeuticPlan.orEmpty(),
                            relevantInformations = attachmentChemotherapy.chemotherapyOncologicalDiagnosis.relevantInformations.orEmpty(),
                            requestedDrugs = requestedChemotherapyChunked.map { chunk ->
                                RequestedDrugsPrintable(
                                    table = chunk.drugsIdentification.table,
                                    code = chunk.drugsIdentification.code,
                                    description = chunk.drugsIdentification.description,
                                    startDate = chunk.startDate.toBrazilianDateFormat(),
                                    totalCycleDosage = chunk.totalCycleDosage.toString(),
                                    unitOfMeasurement = chunk.unitOfMeasurement.code,
                                    administrationRoute = chunk.administrationRoute.code.toInt(),
                                    frequency = chunk.frequency
                                )
                            },
                            observation = attachmentChemotherapy.observation.orEmpty(),
                            cyclesQuantity = attachmentChemotherapy.cyclesQuantity,
                            currentCycle = attachmentChemotherapy.currentCycle,
                            currentCycleDays = attachmentChemotherapy.currentCycleDays,
                            cyclesInterval = attachmentChemotherapy.cyclesInterval,
                            requestedDate = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
                            authorizedAt = attachmentChemotherapy.createdAt.toBrazilianDateFormat(),
                        )
                    )
                }
            }

        results.toList()
    }

    override suspend fun getGuiaCommonPrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ): Result<List<GuiaPrintable>, Throwable> {
        logger.info(
            "GuiaPrintableServiceImpl::getGuiaCommonPrintable",
            "totvsGuiaId" to totvsGuiaId,
        )

        val results = mutableListOf<GuiaPrintable>()

        val (guia, procedures, providers, person) = getFetchGuiaContent(totvsGuiaId, providerUnitId, personId)
        val healthcareResources = getDetailedProcedures(procedures)

        val groupedProcedures = procedures
            .groupBy { it.executedByProviderUnitId ?: providers.first().id }
            .map { entry -> entry.key to entry.value.chunked(5) }
            .toMap()

        providers.forEach { provider ->
            groupedProcedures[provider.id]?.forEach { proceduresChunked ->
                val guiaExecutionCode = proceduresChunked.first().guiaExecutionCode
                val currentProfessional = procedures
                    .first { procedure -> procedure.totvsGuiaId == guia.id }
                    .requestedByProfessional

                val currentTime = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianTimeFormat()

                results.add(
                    GuiaPrintable(
                        providerGuiaNumber = guiaExecutionCode ?: guia.externalCode ?: guia.code,
                        operatorGuiaNumber = guia.externalCode ?: guia.code,
                        mainGuiaNumber = guia.externalCode ?: guia.code,
                        ansNumber = ansNumber,
                        authorizationDate = guia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                        passcode = guia.passcode ?: guia.code,
                        passcodeExpirationDate = guia.createdAt.plusDays(60).toSaoPauloTimeZone()
                            .toBrazilianDateFormat(),
                        memberIdentityNumber = person.nationalId,
                        memberIdentityValidity = null,
                        name = person.fullRegisterName,
                        nationalHealthCard = person.cnsNumber.orEmpty(),
                        newBorn = if (proceduresChunked.first().extraGuiaInfo.newBorn == true) "Sim" else "Nao",
                        operatorCode = provider.cnpj ?: "",
                        contractedName = provider.name,
                        cnesCode = provider.cnes.orEmpty(),
                        requestingProfessionalName = proceduresChunked.first().requestedByProfessional.fullName.orEmpty(),
                        council = currentProfessional.council.name,
                        councilNumber = currentProfessional.councilNumber,
                        councilUf = currentProfessional.councilState.name,
                        cboCode = null,
                        careCharacter = 1,
                        requestDate = guia.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                        accidentIndication = 9,
                        consultationType = null,
                        procedures = proceduresChunked.map { item ->
                            val currentProcedure = healthcareResources.first { resource ->
                                resource.code == item.procedureId
                            }
                            GuiaProceduresPrintable(
                                code = item.procedureId!!,
                                startTime = currentTime,
                                endTime = currentTime,
                                table = currentProcedure.tableType.orEmpty(),
                                description = currentProcedure.description,
                                quantity = item.extraGuiaInfo.quantity ?: 1,
                            )
                        }
                    )
                )
            }
        }

        return Result.of { results.toList() }
    }

    override suspend fun getGuiaRadiotherapyPrintable(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId
    ): Result<List<GuiaRadiotherapyPrintable>, Throwable> {
        logger.info(
            "GuiaPrintableServiceImpl::getGuiaRadiotherapyPrintable",
            "totvsGuiaId" to totvsGuiaId,
        )
        return totvsGuiaService.findByReferenceTotvsGuiaIdAndType(totvsGuiaId, MvUtil.TISS.RADIOTHERAPY)
            .flatMap { referenceTotvsGuia -> attachmentRadiotherapyService.getByTotvsGuiaId(referenceTotvsGuia.id) }
            .flatMapPair { totvsGuiaService.get(totvsGuiaId) }
            .map { (sadtTotvsGuia, attachmentRadiotherapy) ->
                if (attachmentRadiotherapy.status != AttachmentStatus.AUTHORIZED &&
                    attachmentRadiotherapy.status != AttachmentStatus.PARTIALLY_AUTHORIZED
                ) return@map emptyList<GuiaRadiotherapyPrintable>()

                val guiaCommonData = getGuiaCommonPrintableData(
                    totvsGuiaId = attachmentRadiotherapy.totvsGuiaId,
                    providerUnitId = providerUnitId,
                    personId = personId,
                    sadtTotvsGuia = sadtTotvsGuia
                )

                listOf(
                    GuiaRadiotherapyPrintable(
                        guiaCommonData = guiaCommonData,
                        diagnosisDate = attachmentRadiotherapy.oncologicalDiagnosisRadio.diagnosisDate.toBrazilianDateFormat(),
                        healthCondition = attachmentRadiotherapy.oncologicalDiagnosisRadio.healthCondition.code.orEmpty(),
                        imageDiagnosis = attachmentRadiotherapy.oncologicalDiagnosisRadio.imageDiagnosis.code.toInt(),
                        stage = attachmentRadiotherapy.oncologicalDiagnosisRadio.stage.code.toInt(),
                        ecoGt = attachmentRadiotherapy.oncologicalDiagnosisRadio.ecoGt.code.toInt(),
                        purpose = attachmentRadiotherapy.oncologicalDiagnosisRadio.purpose.code.toInt(),
                        histopathological = attachmentRadiotherapy.oncologicalDiagnosisRadio.histopathological.orEmpty(),
                        relevantInformations = attachmentRadiotherapy.oncologicalDiagnosisRadio.relevantInformations.orEmpty(),
                        fieldsQuantity = attachmentRadiotherapy.fieldsQuantity,
                        fieldDose = attachmentRadiotherapy.fieldDose,
                        totalDose = attachmentRadiotherapy.totalDose,
                        daysQuantity = attachmentRadiotherapy.daysQuantity,
                        expectedStartDate = attachmentRadiotherapy.expectedStartDate.toBrazilianDateFormat(),
                        observation = attachmentRadiotherapy.observation.orEmpty(),
                        requestedDate = attachmentRadiotherapy.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                        authorizedAt = attachmentRadiotherapy.createdAt.toSaoPauloTimeZone().toBrazilianDateFormat(),
                    )
                )
            }

    }

    private suspend fun getGuiaCommonPrintableData(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
        sadtTotvsGuia: TotvsGuia? = null,
    ) = coroutineScope {
        val personDeferred = async { personService.get(personId) }
        val providerDeferred = async { providerUnitService.get(providerUnitId) }
        val totvsGuiaDeferred = async { totvsGuiaService.get(totvsGuiaId) }

        val provider = providerDeferred.await().get()
        val person = personDeferred.await().get()
        val totvsGuia = totvsGuiaDeferred.await().get()

        GuiaCommonPrintableData(
            providerGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
            operatorGuiaNumber = totvsGuia.externalCode ?: totvsGuia.code,
            mainGuiaNumber = if (sadtTotvsGuia != null)
                sadtTotvsGuia.externalCode ?: sadtTotvsGuia.code else totvsGuia.externalCode ?: totvsGuia.code,
            ansNumber = ansNumber,
            passcode = totvsGuia.passcode ?: totvsGuia.code,
            passcodeExpirationDate = totvsGuia.createdAt.plusDays(60).toSaoPauloTimeZone().toBrazilianDateFormat(),
            memberIdentityNumber = person.nationalId,
            name = person.fullRegisterName,
            socialName = person.fullSocialName,
            nationalHealthCard = person.cnsNumber.orEmpty(),
            newBorn = if (totvsGuia.newBorn) "Sim" else "Nao",
            age = person.age,
            sex = toSexConverter(person.sex).orEmpty(),
            operatorCode = provider.cnpj ?: "",
            contractedName = provider.name,
            cnesCode = provider.cnes.orEmpty(),
            providerCnpj = provider.cnpj,
            requestingProfessionalName = totvsGuia.requestedByProfessional.fullName.orEmpty(),
            council = totvsGuia.requestedByProfessional.council.name,
            councilNumber = totvsGuia.requestedByProfessional.councilNumber,
            councilUf = totvsGuia.requestedByProfessional.councilState.name,
            cboCode = totvsGuia.requestedByProfessional.cboCode,
            phone = totvsGuia.requestedByProfessional.phone.orEmpty(),
            email = totvsGuia.requestedByProfessional.email.orEmpty(),
        )
    }

    private suspend fun getFetchGuiaContent(
        totvsGuiaId: UUID,
        providerUnitId: UUID,
        personId: PersonId,
    ) = coroutineScope {
        val providerDeferred = async { providerUnitService.get(providerUnitId) }
        val person = async { personService.get(personId) }
        val guiaDeferred = async { totvsGuiaService.get(totvsGuiaId) }
        val proceduresDeferred = async {
            mvAuthorizedProcedureService.findByTotvsGuiaIdAndStatus(totvsGuiaId, authorizedAndExecutedStatuses)
        }

        val procedures = proceduresDeferred.await().getOrElse { emptyList() }
        val providersDeferred = async {
            providerUnitService.getByIds(procedures.mapNotNull { it.executedByProviderUnitId }.distinct())
        }

        val providerByCnpj = providersDeferred.await().getOrElse { emptyList() }
        val providers = providerByCnpj.ifEmpty { listOf(providerDeferred.await().get()) }

        GuiaContentFetched(
            guia = guiaDeferred.await().get(),
            procedures = procedures,
            provider = providers,
            person = person.await().get()
        )
    }

    private suspend fun getDetailedProcedures(
        procedures: List<MvAuthorizedProcedure>
    ) = coroutineScope {
        val proceduresCode = procedures.map { it.procedureId!! }.distinct()
        healthcareResourceService.findByCodes(proceduresCode, onlyActive = false).getOrElse { emptyList() }
    }

    private fun groupedProcedures(procedures: List<MvAuthorizedProcedure>, size: Int) =
        procedures
            .groupBy { it.totvsGuiaId }
            .map { entry -> entry.key to entry.value.chunked(size) }
            .toMap()

    private fun bodySurface(height: BigDecimal, weight: BigDecimal): BigDecimal {
        val mc = MathContext.DECIMAL128

        val heightMultiplyWeight = height.multiply(weight, mc)
        val divisor = BigDecimal(3600)
        val divisionResult = heightMultiplyWeight.divide(divisor, mc).toDouble()

        return BigDecimal(sqrt(divisionResult), mc).setScale(2, RoundingMode.HALF_UP)
    }

    private fun toSexConverter(sex: Sex?) = when (sex) {
        Sex.MALE -> "Masculino"
        Sex.FEMALE -> "Feminino"
        Sex.INTERSEX -> "Intersexo"
        else -> null
    }

}
