package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil.TipoAtendimento.SADT_SOLICITACAO
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.events.FailedProcedure
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.data.layer.models.ExecutionGroup
import br.com.alice.data.layer.models.ExecutionGroupModel
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.services.ExecutionGroupModelDataService
import br.com.alice.data.layer.services.ExecutionGroupModelDataService.FieldOptions
import br.com.alice.data.layer.services.ExecutionGroupModelDataService.OrderingOptions
import br.com.alice.exec.indicator.client.CreateExecutionGroupRequest
import br.com.alice.exec.indicator.client.EitaHealthEventService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.client.ExecutionGroupRequest
import br.com.alice.exec.indicator.client.ExecutionGroupService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.ProceduresExecutionService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.converters.toPayload
import br.com.alice.exec.indicator.events.DbIntegrationEvent
import br.com.alice.exec.indicator.models.ExecutionGroupExecutedPayload
import br.com.alice.exec.indicator.models.ProcedurePayload
import br.com.alice.exec.indicator.models.ProceduresExecutionRequest
import br.com.alice.exec.indicator.service.internal.InternalMvAuthorizedProcedureService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ExecutionGroupServiceImpl(
    private val executionGroupDataService: ExecutionGroupModelDataService,
    private val internalMvAuthorizedProcedureServiceImpl: InternalMvAuthorizedProcedureService,
    private val mvAuthorizedProcedure: MvAuthorizedProcedureService,
    private val kafkaProducerService: KafkaProducerService,
    private val authorizerService: ExecIndicatorAuthorizerService,
    private val providerUnitService: ProviderUnitService,
    private val eitaHealthEventService: EitaHealthEventService,
    private val proceduresExecutionService: ProceduresExecutionService
) : ExecutionGroupService,
    Finder<FieldOptions, OrderingOptions, ExecutionGroupModel> by executionGroupDataService {

    override suspend fun getOrCreate(executionGroup: ExecutionGroup): Result<ExecutionGroup, Throwable> =
        executionGroupDataService.get(executionGroup.id)
            .then {
                logger.info(
                    "ExecutionGroup successfully retrieve",
                    "class_name" to "ExecutionGroupServiceImpl",
                    "function_name" to "saveExecutionGroup",
                    "execution_group" to it
                )
            }
            .coFoldNotFound {
                executionGroupDataService.add(executionGroup.toModel()).then {
                    logger.info(
                        "ExecutionGroup successfully added",
                        "class_name" to "ExecutionGroupServiceImpl",
                        "function_name" to "saveExecutionGroup",
                        "execution_group" to it
                    )
                }
            }
            .thenError { ex ->
                logger.error(
                    "Error while adding or retrieving ExecutionGroup",
                    "class_name" to "ExecutionGroupServiceImpl",
                    "function_name" to "saveExecutionGroup",
                    "result" to "failure",
                    "reason" to ex.message,
                    ex
                )
            }
            .map {
                it.toTransport()
            }

    private suspend fun saveExecutionGroup(
        request: ExecutionGroupRequest,
        id: UUID,
    ): Result<ExecutionGroup, Throwable> {
        val executionGroup = ExecutionGroup(
            id = id,
            providerUnitId = request.providerUnitId,
            userEmail = request.userEmail,
            attachments = request.attachments.map { it.toString() }
        )

        return getOrCreate(executionGroup)
    }

    private suspend fun saveExecutionGroup(
        request: CreateExecutionGroupRequest,
    ): Result<ExecutionGroup, Throwable> {
        val executionGroup = ExecutionGroup(
            id = request.idempotencyId,
            providerUnitId = request.providerUnitId,
            userEmail = request.userEmail,
            attachments = request.attachments.map { it.toString() }
        )

        return getOrCreate(executionGroup)
    }

    override suspend fun get(id: UUID): Result<ExecutionGroup, Throwable> {
        return executionGroupDataService.get(id).map { it.toTransport() }
    }

    override suspend fun update(model: ExecutionGroup): Result<ExecutionGroup, Throwable> {
        return executionGroupDataService.update(model.toModel()).map { it.toTransport() }
    }

    override suspend fun delete(model: ExecutionGroup): Result<Boolean, Throwable> {
        return executionGroupDataService.delete(model.toModel())
    }

    override suspend fun getByCode(code: String): Result<ExecutionGroup, Throwable> =
        executionGroupDataService.findOne { where { this.code.eq(code) } }.map { it.toTransport() }

    override suspend fun createExecutionGroup(
        request: CreateExecutionGroupRequest
    ): Result<ExecutionGroup, Throwable> = coroutineScope {
        logger.info(
            "Execution Group creation",
            "class_name" to "ExecutionGroupServiceImpl",
            "function_name" to "addExecutionGroup",
            "user_email" to request.userEmail,
            "procedure_ids" to request.proceduresIds
        )
        val proceduresDeferred = async { mvAuthorizedProcedure.getByIds(request.proceduresIds) }
        val providerUnitDeferred = async { providerUnitService.get(request.providerUnitId) }
        val authorizerDeferred = async { authorizerService.getByProviderUnitIds(listOf(request.providerUnitId)) }

        val dataIntegration = request.dataIntegration.associate { it.id to it.dataIntegration }

        val providerUnit = providerUnitDeferred.await().get()
        val authorizer = authorizerDeferred.await().get().first()
        val procedures = proceduresDeferred.await().get().map {
            it.copy(dataIntegration = dataIntegration[it.id] ?: emptyList())
        }

        if (validateIfAlreadyExecuted(procedures)) {
            logger.error(
                "Could not create execution group with executed procedures",
                "procedure_ids" to request.proceduresIds,
                "execution_group_code" to request.idempotencyId
            )
            return@coroutineScope IllegalArgumentException("Could not create execution group with executed procedures").failure()
        }

        saveExecutionGroup(request)
            .flatMap { executionGroup ->
                proceduresExecutionService.execute(
                    ProceduresExecutionRequest(
                        providerUnit = providerUnit,
                        executionGroup = executionGroup,
                        procedures = procedures,
                        shouldExecuteExternally = request.shouldExecuteExternally,
                        origin = request.origin
                    )
                ).mapPair { executionGroup }
            }.map { (executionGroup, updatedProcedures) ->
                produceDbIntegrationEvent(
                    updatedProcedures.filter { it.isStatusExecuted() },
                    executionGroup,
                    authorizer
                )

                updatedProcedures.groupBy { it.totvsGuiaId }.map { (totvsGuiaId, procedures) ->
                    val location = if (totvsGuiaId != null) {
                        HealthEventLocationEnum.TOTVS_GUIA
                    } else HealthEventLocationEnum.EXECUTION_GROUP

                    eitaHealthEventService.publishExecutedProceduresHealthEvent(
                        procedures = procedures,
                        referenceId = totvsGuiaId ?: executionGroup.id,
                        location = location
                    )
                }

                executionGroup
            }

    }

    private fun validateIfAlreadyExecuted(procedures: List<MvAuthorizedProcedure>) =
        procedures.map { it.status }.contains(MvAuthorizedProcedureStatus.EXECUTED)

    override suspend fun addExecutionGroup(
        executionGroupRequest: ExecutionGroupRequest,
        shouldPublishExecution: Boolean,
        shouldExecuteProcedures: Boolean,
        shouldPublishHealthEvent: Boolean
    ): Result<ExecutionGroup, Throwable> {
        logger.info(
            "Execution Group creation",
            "class_name" to "ExecutionGroupServiceImpl",
            "function_name" to "addExecutionGroup",
            "authorizer_id" to executionGroupRequest.authorizerId,
            "idempotency_id" to executionGroupRequest.idempotencyId,
            "user_email" to executionGroupRequest.userEmail,
            "procedure_ids" to executionGroupRequest.procedures
        )
        val providerUnitIds = listOf(executionGroupRequest.providerUnitId)
        val authorizer = authorizerService.getByProviderUnitIds(providerUnitIds).get().first()

        val groupId = executionGroupRequest.idempotencyId

        return saveExecutionGroup(executionGroupRequest, groupId)
            .flatMap { executionGroup ->
                internalMvAuthorizedProcedureServiceImpl.saveExecutionGroupId(
                    executionGroupRequest.procedures.map { it.id },
                    groupId,
                    executionGroup.providerUnitId
                ).map { executionGroup }
            }
            .then { executionGroup ->

                if (shouldExecuteProcedures) {
                    internalMvAuthorizedProcedureServiceImpl.executeProceduresByIds(
                        executionGroupRequest.procedures,
                        groupId,
                        executionGroup.providerUnitId
                    )
                }

                if (shouldPublishExecution) {
                    val procedures = mvAuthorizedProcedure.findByExecutionGroupId(executionGroup.id).get()
                    produceDbIntegrationEvent(procedures.filter { it.isStatusExecuted() }, executionGroup, authorizer)
                }

                if (shouldPublishHealthEvent) {
                    eitaHealthEventService.publishExecutedHealthEvent(executionGroup.id)
                }

            }
    }

    override suspend fun listByIds(executionGroupIds: List<UUID>): Result<List<ExecutionGroup>, Throwable> =
        executionGroupDataService.find { where { this.id.inList(executionGroupIds) } }.then {
            logger.info(
                "Execution Group list found",
                "class_name" to "ExecutionGroupServiceImpl",
                "function_name" to "listByIds",
                "execution_groups_ids" to executionGroupIds.size,
                "execution_groups_retrieved" to it.size
            )
        }.map {
            it.toTransport()
        }

    override suspend fun getProviderUnitByCode(code: String): Result<ProviderUnit, Throwable> {
        return getByCode(code)
            .flatMap {
                providerUnitService.get(it.providerUnitId)
            }.thenError {
                logger.error(
                    "error for get provider unit by code",
                    "code" to code,
                    it
                )
            }
    }

    override suspend fun markDbIntegrationFailures(
        executionGroupId: UUID,
        failures: List<FailedProcedure>
    ): Result<Boolean, Throwable> {
        return coResultOf {
            internalMvAuthorizedProcedureServiceImpl.markDbIntegrationFailures(
                executionGroupId,
                failures
            )
        }
    }

    @OptIn(QueryAllUsage::class)
    override suspend fun listPaginated(
        page: Int,
        limit: Int,
        sort: SortOrder?,
        sortField: String?
    ): Result<Pair<List<ExecutionGroup>, Int>, Throwable> {
        return coroutineScope {
            val proceduresQuery = async {

                executionGroupDataService.find {
                    all().orderBy { orderBy(sortField) }.sortOrder { sort ?: SortOrder.Descending }
                        .offset { limit * (page - 1) }.limit { limit }
                }.map {
                    it.toTransport()
                }
            }
            val totalQuery = async {
                executionGroupDataService.count { all() }
            }

            val proceduresOrFailure = proceduresQuery.await()
            val totalOrFailure = totalQuery.await()

            coResultOf { Pair(proceduresOrFailure.get(), totalOrFailure.get()) }
        }
    }

    private fun OrderingOptions.orderBy(field: String?) =
        when (field) {
            "createAt" -> this.createdAt
            "code" -> this.code
            else -> this.createdAt
        }

    private fun List<MvAuthorizedProcedure>.generateExecutedPayload(
        executionGroup: ExecutionGroup,
        authorizer: ExecIndicatorAuthorizer
    ) = ExecutionGroupExecutedPayload(
        executionGroupCode = executionGroup.code,
        personId = this.first().personId,
        typeOfService = SADT_SOLICITACAO,
        procedures = this.map {
            ProcedurePayload(
                id = it.id,
                aliceCode = (it.procedureId ?: it.opmeId)!!,
                it.dataIntegration.map { data -> data.toPayload() }
            )
        },
        testRequestIds = this.mapNotNull { it.testRequestId },
        executorId = authorizer.mvCdPrestador,
        executionGroupId = executionGroup.id
    )

    private suspend fun produceDbIntegrationEvent(
        procedures: List<MvAuthorizedProcedure>,
        executionGroup: ExecutionGroup,
        authorizer: ExecIndicatorAuthorizer
    ): Result<Boolean, Throwable> = if (procedures.isNotEmpty()) {
        val payload = procedures.generateExecutedPayload(executionGroup, authorizer)
        logger.info(
            "Publishing ExecutionGroupExecuted",
            "person_id" to payload.personId,
            "execution_group_code" to payload.executionGroupCode,
            "procedure" to payload.procedures
        )
        kafkaProducerService.produce(DbIntegrationEvent(payload))
        true.success()
    } else {
        logger.warn(
            "Could not find executed procedures for execution group",
            "execution_group_code" to executionGroup.code
        )
        true.success()
    }

}
