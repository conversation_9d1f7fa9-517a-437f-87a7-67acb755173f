package br.com.alice.exec.indicator.service

import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.MagicNumbers
import br.com.alice.data.layer.models.MagicNumbersStatus
import br.com.alice.data.layer.services.MagicNumbersModelDataService
import br.com.alice.exec.indicator.client.MagicNumbersService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.events.MagicNumbersCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDateTime
import java.util.UUID

class MagicNumbersServiceImpl(
    private val data: MagicNumbersModelDataService,
    private val kafkaProducerService: KafkaProducerService
) : MagicNumbersService, Adder<MagicNumbers>,
    Getter<MagicNumbers>,
    Updater<MagicNumbers> {
    override suspend fun get(id: UUID): Result<MagicNumbers, Throwable> =
        data.get(id).map { it.toTransport() }


    override suspend fun add(model: MagicNumbers): Result<MagicNumbers, Throwable> = data.add(model.toModel())
        .map { it.toTransport() }
        .then {
            kafkaProducerService.produce(MagicNumbersCreatedEvent(model))
        }

    override suspend fun update(model: MagicNumbers): Result<MagicNumbers, Throwable> = data.update(model.toModel()).map {
        it.toTransport()
    }
    override suspend fun cancel(model: MagicNumbers): Result<MagicNumbers, Throwable> = data.update(
        model.copy(
            status = MagicNumbersStatus.CANCELED
        ).toModel()
    ).map {
        it.toTransport()
    }

    override suspend fun changeToAccessed(model: MagicNumbers): Result<MagicNumbers, Throwable> =
        data.update(
            model.copy(
                status = MagicNumbersStatus.ACCESSED,
                accessedByUser = true
            ).toModel()
        ).map {
            it.toTransport()
        }

    override suspend fun findAvailableCode(codeParam: String): Result<MagicNumbers, Throwable> {
        val now = LocalDateTime.now()
        return data.findOne {
            where {
                code.eq(codeParam) and
                        accessedByUser.eq(false) and
                        expirationAt.greater(now)
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun getAvailableMagicNumbers(codeParam: String, email: String): Result<MagicNumbers, Throwable> {
        val now = LocalDateTime.now()
        return data.findOne {
            where {
                code.eq(codeParam) and
                        accessedByUser.eq(false) and
                        expirationAt.greater(now) and
                        generateTo.eq(email) and
                        status.eq(MagicNumbersStatus.NEW)
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun getActiveCodeByUser(email: String): Result<List<MagicNumbers>, Throwable> = data.find {
        where {
            generateTo.eq(email) and
                    status.eq(MagicNumbersStatus.NEW)
        }
    }.mapEach { it.toTransport() }

}
