package br.com.alice.exec.indicator.service

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.exec.indicator.ServiceConfig
import br.com.alice.exec.indicator.client.MailerService
import br.com.alice.exec.indicator.exception.EitaAuthenticatorSendEmailError
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.mapError

class MailerServiceImpl(
    private val sender: EmailSender
) : MailerService {
    companion object {
        private val defaultSender = EmailAddress(
            name = ServiceConfig.Mailer.defaultSenderName,
            email = ServiceConfig.Mailer.defaultSenderEmail
        )
    }

    override suspend fun sendEmail(
        emailAddress: EmailAddress,
        templateName: String,
        replaceVariables: Map<String, String>
    ): Result<String, Throwable> = coResultOf<String, Throwable> {
        logger.info(
            "Sending email",
            "template" to templateName,
            "replaceVariables" to replaceVariables
        )

        val sendEmailRequest = SendEmailRequest(
            from = defaultSender,
            to = listOf(emailAddress),
            templateName = templateName,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            replaceVariables = replaceVariables
        )

        val emailReceipt = sender.send(sendEmailRequest)

        logger.info(
            "Email sent",
            "from" to "${defaultSender.name}: ${defaultSender.email}",
            "template" to templateName,
            "email_receipt_id" to emailReceipt.id
        )

        emailReceipt.id
    }.mapError {
        logger.error("MailerServiceImpl.sendEmail: error sending email", it)
        EitaAuthenticatorSendEmailError(customMessage = it.message ?: "")
    }
}


