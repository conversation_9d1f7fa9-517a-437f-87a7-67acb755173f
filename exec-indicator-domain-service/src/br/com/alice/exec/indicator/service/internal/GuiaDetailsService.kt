package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.core.extensions.then
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.GlossAuthorizationInfoData
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.RequestedDrugs
import br.com.alice.data.layer.models.RequestedOPME
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.models.ChemotherapyData
import br.com.alice.exec.indicator.models.GenericGloss
import br.com.alice.exec.indicator.models.GuiaDetailsResponse
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.HospitalizationData
import br.com.alice.exec.indicator.models.OpmeData
import br.com.alice.exec.indicator.models.RadiotherapyData
import br.com.alice.exec.indicator.models.TotvsGuiaStatusResponse
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class GuiaDetailsService(
    private val healthcareResourceService: HealthcareResourceService,
    private val attachmentOpmeService: AttachmentOpmeService,
    private val hospitalizationInfoService: HospitalizationInfoService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val attachmentChemotherapyService: AttachmentChemotherapyService,
    private val fileVaultActionService: FileVaultActionService,
    private val attachmentRadiotherapyService: AttachmentRadiotherapyService,
) {

    suspend fun getHospitalizationDataAndProcedure(totvsGuia: TotvsGuia) = coroutineScope {
        coResultOf<GuiaDetailsResponse, Throwable> {
            val hospitalizationInfoDeferred = async { hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id) }
            val filesDeferred = async { getFiles(totvsGuia.medicalRequestFileIds) }

            val proceduresAndGuiaProceduresDeferred = async {
                val procedures = mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id).get()
                val guiaProcedures = getGuiaProcedures(procedures).get()
                Pair(procedures, guiaProcedures)
            }

            val hospitalizationInfo = hospitalizationInfoDeferred.await().get()
            val files = filesDeferred.await().get()
            val (procedures, guiaProcedures) = proceduresAndGuiaProceduresDeferred.await()

            val hospitalizationData = HospitalizationData(
                attendanceCharacter = hospitalizationInfo.attendanceCharacter,
                accidentIndication = hospitalizationInfo.accidentIndication,
                type = hospitalizationInfo.type,
                numberOfDays = hospitalizationInfo.numberOfDays,
                suggestedDate = hospitalizationInfo.suggestedDate,
                suggestedDischargeDate = hospitalizationInfo.suggestedDischargeDate,
                chemotherapyIndication = hospitalizationInfo.chemotherapyIndication,
                opmeIndication = hospitalizationInfo.opmeIndication,
                clinicalIndication = hospitalizationInfo.clinicalIndication,
                healthCondition = hospitalizationInfo.healthCondition,
                startDate = hospitalizationInfo.startDate,
                dischargeDate = hospitalizationInfo.dischargeDate,
                dischargeMotive = hospitalizationInfo.dischargeMotive
            )

            val filteredProcedures = procedures.filter { it.procedureId != null && it.gloss != null }
            val glossMap = filteredProcedures.associate { it.procedureId!! to
                StatusAndGloss(
                    glossItems = it.gloss!!,
                    status = it.status
                )
            }
            val gloss = convertToGenericGloss(glossMap)

            createResponse(
                totvsGuia = totvsGuia,
                files = files,
                gloss = gloss,
                procedures = guiaProcedures,
                hospitalizationData = hospitalizationData
            )
        }
    }

    suspend fun getOpmeData(totvsGuia: TotvsGuia) = coroutineScope {
        coResultOf<GuiaDetailsResponse, Throwable> {
            val attachmentDeferred = async { attachmentOpmeService.getByTotvsGuiaId(totvsGuia.id) }
            val filesDeferred = async { getFiles(totvsGuia.medicalRequestFileIds) }

            val attachment = attachmentDeferred.await().get()
            val files = filesDeferred.await().get()

            val glossMap = attachment.requestedOpmes.associate { it.opmeIdentification.code to
                    StatusAndGloss(
                        glossItems = it.gloss,
                        status = it.status
                    )
            }
            val gloss = convertToGenericGloss(glossMap)

            val opmeData = OpmeData(
                technicalJustification = attachment.technicalJustification,
                materialSpecification = attachment.materialSpecification
            )

            createResponse(
                totvsGuia = totvsGuia,
                files = files,
                gloss = gloss,
                opmeData = opmeData,
                opmes = attachment.requestedOpmes
            )
        }
    }

    suspend fun getChemotherapyData(totvsGuia: TotvsGuia, referenceTotvsGuia: TotvsGuia) = coroutineScope {
        coResultOf<GuiaDetailsResponse, Throwable> {
            val attachmentDeferred = async { attachmentChemotherapyService.getByTotvsGuiaId(totvsGuia.id) }

            val filesDeferred = async {
                getFiles(
                    totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
                )
            }

            val proceduresAndGuiaProceduresDeferred = async {
                val procedures = mvAuthorizedProcedureService.findByTotvsGuiaIds(
                    listOf(totvsGuia.id, referenceTotvsGuia.id)
                ).get()
                val guiaProcedures = getGuiaProcedures(procedures).get()
                Pair(procedures, guiaProcedures)
            }

            val attachment = attachmentDeferred.await().get()
            val files = filesDeferred.await().get()
            val (procedures, guiaProcedures) = proceduresAndGuiaProceduresDeferred.await()

            val filteredProcedures = procedures.filter { it.procedureId != null && it.gloss != null }
            val proceduresGlossMap = filteredProcedures.associate { it.procedureId!! to
                    StatusAndGloss(
                        glossItems = it.gloss!!,
                        status = it.status
                    )
            }
            val drugsGlossMap = attachment.requestedDrugs.associate { it.drugsIdentification.code to
                    StatusAndGloss(
                        glossItems = it.gloss,
                        status = it.status
                    )
            }

            val proceduresGloss = convertToGenericGloss(proceduresGlossMap)
            val drugsGloss = convertToGenericGloss(drugsGlossMap)

            val gloss = proceduresGloss.plus(drugsGloss)

            val chemotherapyData = ChemotherapyData(
                cyclesQuantity = attachment.cyclesQuantity,
                currentCycle = attachment.currentCycle,
                currentCycleDays = attachment.currentCycleDays,
                cyclesInterval = attachment.cyclesInterval,
                oncologicalDiagnosis = attachment.chemotherapyOncologicalDiagnosis
            )

            createResponse(
                totvsGuia = referenceTotvsGuia,
                files = files,
                gloss = gloss,
                chemotherapyData = chemotherapyData,
                requestedDrugs = attachment.requestedDrugs,
                procedures = guiaProcedures
            )
        }
    }

    suspend fun getGenericDataAndProcedure(totvsGuia: TotvsGuia) = coroutineScope {
        coResultOf<GuiaDetailsResponse, Throwable> {
            val filesDeferred = async { getFiles(totvsGuia.medicalRequestFileIds) }

            val proceduresAndGuiaProceduresDeferred = async {
                val procedures = mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuia.id).get()
                val guiaProcedures = getGuiaProcedures(procedures).get()
                Pair(procedures, guiaProcedures)
            }

            val files = filesDeferred.await().get()
            val (procedures, guiaProcedures) = proceduresAndGuiaProceduresDeferred.await()

            val filteredProcedures = procedures.filter { it.procedureId != null && it.gloss != null }
            val glossMap = filteredProcedures.associate { it.procedureId!! to
                    StatusAndGloss(
                        glossItems = it.gloss!!,
                        status = it.status
                    )
            }
            val gloss = convertToGenericGloss(glossMap)

            val accidentIndication = procedures.first().extraGuiaInfo.accidentIndication?.let {
                AnsAccidentIndication.fromCode(it) ?: AnsAccidentIndication.valueOf(it)
            }

            createResponse(
                totvsGuia = totvsGuia,
                files = files,
                gloss = gloss,
                procedures = guiaProcedures,
                accidentIndication = accidentIndication
            )
        }
    }

    suspend fun getRadiotherapyData(totvsGuia: TotvsGuia, referenceTotvsGuia: TotvsGuia) = coroutineScope {
        coResultOf<GuiaDetailsResponse, Throwable> {
            val attachmentDeferred = async { attachmentRadiotherapyService.getByTotvsGuiaId(totvsGuia.id) }

            val filesDeferred = async {
                getFiles(
                    totvsGuia.medicalRequestFileIds.plus(referenceTotvsGuia.medicalRequestFileIds)
                )
            }

            val proceduresAndGuiaProceduresDeferred = async {
                val procedures = mvAuthorizedProcedureService.findByTotvsGuiaIds(
                    listOf(totvsGuia.id, referenceTotvsGuia.id)
                ).get()
                val guiaProcedures = getGuiaProcedures(procedures).get()
                Pair(procedures, guiaProcedures)
            }

            val attachment = attachmentDeferred.await().get()
            val files = filesDeferred.await().get()
            val (procedures, guiaProcedures) = proceduresAndGuiaProceduresDeferred.await()

            val filteredProcedures = procedures.filter { it.procedureId != null && it.gloss != null }
            val proceduresGlossMap = filteredProcedures.associate { it.procedureId!! to
                    StatusAndGloss(
                        glossItems = it.gloss!!,
                        status = it.status
                    )
            }

            val gloss = convertToGenericGloss(proceduresGlossMap)

            val radiotherapyData = RadiotherapyData(
                oncologicalDiagnosis = attachment.oncologicalDiagnosisRadio,
                numberOfFields = attachment.fieldsQuantity,
                dailyDoses = attachment.fieldDose,
                gy = attachment.totalDose,
                numberOfDays = attachment.daysQuantity,
                estimatedStartAdministration = attachment.expectedStartDate,
            )

            createResponse(
                totvsGuia = referenceTotvsGuia,
                files = files,
                gloss = gloss,
                radiotherapyData = radiotherapyData,
                procedures = guiaProcedures
            )
        }
    }

    private fun createResponse(
        totvsGuia: TotvsGuia,
        files: List<VaultResponse>,
        gloss: List<GenericGloss>,
        opmeData: OpmeData? = null,
        chemotherapyData: ChemotherapyData? = null,
        hospitalizationData: HospitalizationData? = null,
        accidentIndication: AnsAccidentIndication? = null,
        opmes: List<RequestedOPME> = emptyList(),
        procedures: List<GuiaProcedure> = emptyList(),
        requestedDrugs: List<RequestedDrugs> = emptyList(),
        radiotherapyData: RadiotherapyData? = null,
    ) = GuiaDetailsResponse(
        externalCode = totvsGuia.externalCode ?: totvsGuia.code,
        passcode = totvsGuia.passcode,
        type = totvsGuia.type,
        personId = totvsGuia.personId,
        status = totvsGuia.toResponseStatus(),
        files = files,
        gloss = getGloss(gloss, totvsGuia.status),
        accidentIndication = accidentIndication,
        requester = totvsGuia.requestedByProfessional,
        newBorn = totvsGuia.newBorn,
        opmeData = opmeData,
        chemotherapyData = chemotherapyData,
        radiotherapyData = radiotherapyData,
        hospitalizationData = hospitalizationData,
        opmes = opmes,
        procedures = procedures,
        requestedDrugs = requestedDrugs
    )

    private suspend fun getGuiaProcedures(procedures: List<MvAuthorizedProcedure>) =
        healthcareResourceService.findByCodes(procedures.mapNotNull { it.procedureId }.distinct(), onlyActive = false)
            .map { resources ->
                val resourcesMap = resources.associateBy { it.code }

                procedures.map {
                    GuiaProcedure(
                        code = it.procedureId!!,
                        description = resourcesMap[it.procedureId]?.description ?: "",
                        quantity = it.quantity,
                        table = resourcesMap[it.procedureId]?.tableType ?: "",
                        status = it.status
                    )
                }
            }.then {
                logger.info(
                    "GuiaDetailsService::getGuiaProcedures",
                    "procedures" to it
                )
            }

    private suspend fun getFiles(medicalRequestFileIds: List<UUID>) =
        fileVaultActionService.securedLinks(medicalRequestFileIds)
            .then {
                logger.info(
                    "GuiaDetailsService::getFiles",
                    "files" to it
                )
            }

    private fun convertToGenericGloss(glossMap: Map<String, StatusAndGloss>): List<GenericGloss> =
        glossMap.flatMap { (procedureId, statusAndGloss) ->
            if (
                statusAndGloss.status == MvAuthorizedProcedureStatus.PENDING ||
                statusAndGloss.status == MvAuthorizedProcedureStatus.UNAUTHORIZED
            ) {
                statusAndGloss.glossItems.map {
                    GenericGloss(
                        title = it.title,
                        code = it.code,
                        description = it.description,
                        procedureId = procedureId,
                        observation = it.observation
                    )
                }
            } else {
                emptyList()
            }
        }.then {
            logger.info(
                "GuiaDetailsService::convertToGenericGloss",
                "gloss" to it
            )
        }

    private fun TotvsGuia.toResponseStatus(): TotvsGuiaStatusResponse {
        val toleranceTime = FeatureService.get(
            FeatureNamespace.EXEC_INDICATOR,
            "totvs_integration_tolerance_time_minutes",
            5
        )

        val status = TotvsGuiaStatusResponse.fromTotvsGuiaStatus(this, toleranceTime.toLong())

        logger.info(
            "GuiaDetailsService::toResponseStatus",
            "tolerance_time" to toleranceTime,
            "status" to status
        )

        return status
    }

    private fun getGloss(gloss: List<GenericGloss>, status: TotvsGuiaStatus) =
        if (status == TotvsGuiaStatus.UNAUTHORIZED || status == TotvsGuiaStatus.PARTIALLY_AUTHORIZED) gloss
        else emptyList()
}

data class StatusAndGloss(
    val status: MvAuthorizedProcedureStatus,
    val glossItems: List<GlossAuthorizationInfoData>
)
