package br.com.alice.exec.indicator.metrics

import br.com.alice.common.observability.metrics.Metric
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import java.util.Locale


object Metrics {

    private const val GUIA_CREATED = "guia_created"
    private const val HOSPITALIZATION_CREATED_BY_SOURCE = "hospitalization_created_by_source"
    private const val ATTACHMENT_OPME = "attachment_opme"
    private const val ATTACHMENT_CHEMOTHERAPY_CREATED = "attachment_chemotherapy_created"
    private const val ATTACHMENT_RADIOTHERAPY_CREATED = "attachment_radiotherapy_created"

    enum class Status {
        SUCCESS, FAILURE
    }

    enum class HospitalizationSource {
        ORIGIN, EXTENSION
    }

    fun incrementGuiaCreated(status: Status, totvsGuiaOrigin: TotvsGuiaOrigin) =
        Metric.increment(
            GUIA_CREATED,
            "status" to status.name.lowercase(Locale.getDefault()),
            "totvs_guia_origin" to totvsGuiaOrigin.name.lowercase(Locale.getDefault()),
        )

    fun incrementHospitalizationCreated(status: Status, source: HospitalizationSource) =
        Metric.increment(
            HOSPITALIZATION_CREATED_BY_SOURCE,
            "status" to status.name.lowercase(Locale.getDefault()),
            "source" to source.name.lowercase(Locale.getDefault()),
        )

    fun incrementAttachmentOpmeCreated(status: Status) =
        Metric.increment(
            ATTACHMENT_OPME,
            "status" to status.name.lowercase(Locale.getDefault()),
        )

    fun incrementAttachmentChemotherapyCreated(status: Status) =
        Metric.increment(
            ATTACHMENT_CHEMOTHERAPY_CREATED,
            "status" to status.name.lowercase(Locale.getDefault()),
        )

    fun incrementAttachmentRadiotherapyCreated(status: Status) =
        Metric.increment(
            ATTACHMENT_RADIOTHERAPY_CREATED,
            "status" to status.name.lowercase(Locale.getDefault()),
        )

}
