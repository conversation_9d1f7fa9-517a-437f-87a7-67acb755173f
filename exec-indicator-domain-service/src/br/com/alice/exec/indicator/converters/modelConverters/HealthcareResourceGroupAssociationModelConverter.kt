package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HealthcareResourceGroupAssociation
import br.com.alice.data.layer.models.HealthcareResourceGroupAssociationModel

object HealthcareResourceGroupAssociationModelConverter: Converter<HealthcareResourceGroupAssociationModel, HealthcareResourceGroupAssociation>(
    HealthcareResourceGroupAssociationModel::class,
    HealthcareResourceGroupAssociation::class
)

fun HealthcareResourceGroupAssociation.toModel() = HealthcareResourceGroupAssociationModelConverter.unconvert(this)
fun HealthcareResourceGroupAssociationModel.toTransport() = HealthcareResourceGroupAssociationModelConverter.convert(this)

fun List<HealthcareResourceGroupAssociation>.toModel() = this.map { it.toModel() }
fun List<HealthcareResourceGroupAssociationModel>.toTransport() = this.map { it.toTransport() }
