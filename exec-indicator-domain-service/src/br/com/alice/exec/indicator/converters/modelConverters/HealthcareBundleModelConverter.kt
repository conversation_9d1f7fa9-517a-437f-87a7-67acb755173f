package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.data.layer.models.HealthcareBundleModel

object HealthcareBundleModelConverter: Converter<HealthcareBundleModel, HealthcareBundle>(
    HealthcareBundleModel::class,
    HealthcareBundle::class
)

fun HealthcareBundle.toModel() = HealthcareBundleModelConverter.unconvert(this)
fun HealthcareBundleModel.toTransport() = HealthcareBundleModelConverter.convert(this)

fun List<HealthcareBundle>.toModel() = this.map { it.toModel() }
fun List<HealthcareBundleModel>.toTransport() = this.map { it.toTransport() }
