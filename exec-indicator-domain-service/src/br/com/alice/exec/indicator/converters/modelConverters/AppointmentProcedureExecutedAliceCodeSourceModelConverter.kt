package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.AppointmentProcedureExecutedAliceCodeSource
import br.com.alice.data.layer.models.AppointmentProcedureExecutedAliceCodeSourceModel

object AppointmentProcedureExecutedAliceCodeSourceModelConverter:
    Converter<AppointmentProcedureExecutedAliceCodeSourceModel, AppointmentProcedureExecutedAliceCodeSource>(
        AppointmentProcedureExecutedAliceCodeSourceModel::class,
        AppointmentProcedureExecutedAliceCodeSource::class
    )


fun AppointmentProcedureExecutedAliceCodeSource.toModel() = AppointmentProcedureExecutedAliceCodeSourceModelConverter.unconvert(this)
fun AppointmentProcedureExecutedAliceCodeSourceModel.toTransport() = AppointmentProcedureExecutedAliceCodeSourceModelConverter.convert(this)

