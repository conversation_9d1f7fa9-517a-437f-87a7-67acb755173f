package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HospitalizationInfo
import br.com.alice.data.layer.models.HospitalizationInfoModel

object HospitalizationInfoModelConverter: Converter<HospitalizationInfoModel, HospitalizationInfo>(
    HospitalizationInfoModel::class,
    HospitalizationInfo::class
)

fun HospitalizationInfo.toModel() = HospitalizationInfoModelConverter.unconvert(this)
fun HospitalizationInfoModel.toTransport() = HospitalizationInfoModelConverter.convert(this)

fun List<HospitalizationInfo>.toModel() = this.map { it.toModel() }
fun List<HospitalizationInfoModel>.toTransport() = this.map { it.toTransport() }
