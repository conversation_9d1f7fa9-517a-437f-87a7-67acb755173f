package br.com.alice.exec.indicator.consumers

import br.com.alice.common.MvUtil
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.events.TotvsGuiaUpsertedEvent
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class TotvsGuiaUpsertedConsumer(
    private val personService: PersonService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
) : Consumer() {

    suspend fun sendCrmEvent(event: TotvsGuiaUpsertedEvent) = withSubscribersEnvironment {
        val totvsGuia = event.payload

        logger.info(
            "TotvsGuiaUpsertedConsumer::sendCrmEvent TotvsGuiaCreatedEvent received",
            "event" to event,
            "payload" to totvsGuia
        )

        if (totvsGuia.origin == TotvsGuiaOrigin.COUNTER_REFERRAL) {
            logger.info(
                "TotvsGuiaUpsertedEvent ignored because origin is COUNTER_REFERRAL",
                "totvs_guia" to totvsGuia
            )
            return@withSubscribersEnvironment this.success()
        }


        if (totvsGuia.version == 0) sendCreatedAnalytics(totvsGuia)
        else sendUpdatedAnalytics(totvsGuia)

        this.success()
    }

    private suspend fun sendCreatedAnalytics(payload: TotvsGuia): Result<Boolean, Throwable> {
        val totvsGuiaStatus = payload.status
        val totvsGuiaType = payload.type.title

        logger.info(
            "TotvsGuiaUpsertedConsumer::sendCreatedAnalytics TotvsGuiaUpsertedEvent started",
            "personId" to payload.personId,
            "totvsGuiaId" to payload.id,
            "totvsGuiaStatus" to totvsGuiaStatus,
            "totvsGuiaType" to totvsGuiaType
        )

        personService.get(payload.personId).map {
            logger.info(
                "sendTotvsGuiaCreatedEvent: begin process",
                "person_id" to it.id,
                "totvs_guia" to payload.id
            )

            val analyticsEvent = AnalyticsEvent(
                name = AnalyticsEventName.PROCEDURE_AUTHORIZATION_CREATED,
                properties = mapOf(
                    "totvsGuiaId" to payload.id,
                    "totvsGuiaStatus" to totvsGuiaStatus.value,
                    "totvsGuiaType" to totvsGuiaType
                )
            )

            crmAnalyticsTracker.sendEvent(nationalId = it.nationalId, event = analyticsEvent)

            if (totvsGuiaStatus == TotvsGuiaStatus.PENDING) {
                val analyticsEventStatusChanged = AnalyticsEvent(
                    name = AnalyticsEventName.PROCEDURE_AUTHORIZATION_STATUS_CHANGED,
                    properties = mapOf(
                        "totvsGuiaId" to payload.id,
                        "totvsGuiaStatus" to totvsGuiaStatus.value,
                        "totvsGuiaType" to totvsGuiaType
                    )
                )

                crmAnalyticsTracker.sendEvent(nationalId = it.nationalId, event = analyticsEventStatusChanged)
            }

            logger.info(
                "sendTotvsGuiaCreatedEvent: event sent to analytics",
                "person_id" to it.id,
                "totvs_guia" to payload.id
            )

        }

        return true.success()
    }

    private suspend fun sendUpdatedAnalytics(payload: TotvsGuia): Result<Boolean, Throwable> {
        val totvsGuiaStatus = payload.status
        val totvsGuiaType = payload.type.title

        logger.info(
            "TotvsGuiaUpsertedConsumer::sendUpdatedAnalytics TotvsGuiaUpsertedEvent started",
            "personId" to payload.personId,
            "totvsGuiaId" to payload.id,
            "totvsGuiaStatus" to totvsGuiaStatus,
            "totvsGuiaType" to totvsGuiaType
        )

        if (ignoreTotvsGuia(totvsGuiaStatus, totvsGuiaType)) return true.success()

        personService.get(payload.personId).map {
            logger.info(
                "sendTotvsGuiaUpdatedEvent: begin process",
                "person_id" to it.id,
                "totvs_guia" to payload.id
            )

            val analyticsEvent = AnalyticsEvent(
                name = AnalyticsEventName.PROCEDURE_AUTHORIZATION_STATUS_CHANGED,
                properties = mapOf(
                    "totvsGuiaId" to payload.id,
                    "totvsGuiaStatus" to totvsGuiaStatus.value,
                    "totvsGuiaType" to totvsGuiaType
                )
            )

            crmAnalyticsTracker.sendEvent(nationalId = it.nationalId, event = analyticsEvent)

            logger.info(
                "sendTotvsGuiaUpdatedEvent: event sent to analytics",
                "person_id" to it.id,
                "totvs_guia" to payload.id
            )

        }

        return true.success()
    }

    private fun ignoreTotvsGuia(totvsGuiaStatus: TotvsGuiaStatus, totvsGuiaType: String) =
        totvsGuiaStatus != TotvsGuiaStatus.PENDING
                || totvsGuiaType == MvUtil.TISS.EXTENSION.title
                || totvsGuiaType == MvUtil.TISS.HOSPITALIZATION.title
}
