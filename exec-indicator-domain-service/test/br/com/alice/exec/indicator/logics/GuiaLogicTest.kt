package br.com.alice.exec.indicator.logics

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.models.CouncilType
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.eita.nullvs.client.NullvsProcedureAuthorizationResponse
import br.com.alice.exec.indicator.models.Item
import br.com.alice.exec.indicator.models.ItemSearchType
import br.com.alice.exec.indicator.models.MvAuthorizedProcedureData
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class GuiaLogicTest {

    private val personId = PersonId()
    private val specialty = TestModelFactory.buildMedicalSpecialty()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(specialtyId = specialty.id)
    private val healthEvent = TestModelFactory.buildHealthEvents(
        healthProfessionalId = healthProfessional.id,
        personId = personId,
    )
    private val healthProfessionals = mapOf(healthProfessional.id to healthProfessional)
    private val specialties = mapOf(specialty.id to specialty)

    private val professionalIdentification = ProfessionalIdentification(
        councilNumber = healthProfessional.council.number,
        councilState = healthProfessional.council.state,
        council = CouncilType.fromName(healthProfessional.councilName) ?: CouncilType.CRM,
        fullName = healthProfessional.staff?.fullName,
        specialty = ProfessionalSpecialty(name = specialty.name, id = 0L)
    )

    @Test
    fun `#buildProfessionalIdentification should build professional identification correctly`() {
        val result = GuiaLogic.buildProfessionalIdentification(healthEvent, healthProfessionals, specialties)

        assertThat(result).isEqualTo(professionalIdentification)
    }

    @Test
    fun `#buildProfessionalIdentification should build default when healthProfessionalId is null`() {
        val healthEvent = healthEvent.copy(healthProfessionalId = null)

        val result = GuiaLogic.buildProfessionalIdentification(healthEvent, healthProfessionals, specialties)

        assertThat(result).isEqualTo(
            ProfessionalIdentification.DEFAULT_PROFESSIONAL_IDENTIFICATION
        )
    }

    @Test
    fun `#separateProceduresByAuthorization should separate procedures correctly`() {
        val authorizationData = listOf(
            NullvsProcedureAuthorizationResponse(true, "123", "98"),
            NullvsProcedureAuthorizationResponse(true, "321", "98"),
            NullvsProcedureAuthorizationResponse(false, "122", "98"),
            )
        val healthEvents = listOf(
            healthEvent.copy(procedureIds = listOf("123")),
            healthEvent.copy(procedureIds = listOf("321")),
            healthEvent.copy(procedureIds = listOf("122")),
        )

        val result = GuiaLogic.separateProceduresByAuthorization(authorizationData, healthEvents)

        assertThat(result).isEqualTo(
            Pair(
                listOf(healthEvents[2]),
                listOf(healthEvents[0], healthEvents[1])
            )
        )
    }

    @Test
    fun `#buildMvAuthorizedProcedureData should build MvAuthorizedProcedureData`() {
        val authorizerId = RangeUUID.generate()
        val mvAuthorizedProcedureData = MvAuthorizedProcedureData(
            professional = professionalIdentification,
            extraGuiaInfo = ExtraGuiaInfo(
                procedureType = MvUtil.TISS.EXAM,
                dateAttendance = healthEvent.requestedAt.toLocalDate(),
                quantity = 1
            ),
            testRequestId = null,
            items = listOf(Item(aliceCode = "123", quantity = 1, type = ItemSearchType.PROCEDURE)),
            userEmail = "<EMAIL>",
            authorizerId = authorizerId,
            personId = personId,
            )

        val result = GuiaLogic.buildMvAuthorizedProcedureData(
            healthEvent,
            healthProfessionals,
            specialties,
            authorizerId,
        )

        assertThat(result).isEqualTo(mvAuthorizedProcedureData)
    }
}
