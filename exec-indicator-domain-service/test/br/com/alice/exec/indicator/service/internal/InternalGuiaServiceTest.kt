package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.MvAuthorizedProcedureOrigin
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.models.GuiaCreationProcedure
import br.com.alice.exec.indicator.models.GuiaCreationRequest
import br.com.alice.person.client.PersonService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InternalGuiaServiceTest {
    private val personService: PersonService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()

    private val service = InternalGuiaService(
        personService,
        mvAuthorizedProcedureService,
        totvsGuiaService,
    )

    @AfterTest
    fun setup() = clearAllMocks()

    private val person = TestModelFactory.buildPerson()
    private val professionalIdentification = ProfessionalIdentification(
        fullName = "Fulano de Tal",
        email = "<EMAIL>",
        phone = "(21) 9999-9999",
        councilNumber = "123456",
        council = CouncilType.CRM,
        councilState = State.CE,
        cboCode = "9999",
        specialty = ProfessionalSpecialty(
            id = 1L,
            name = "Cardiologia"
        )
    )
    private val guiaCreationRequest = GuiaCreationRequest(
        type = MvUtil.TISS.EXAM,
        accidentIndication = AnsAccidentIndication.NOT_ACCIDENT,
        requestedAt = LocalDate.now(),
        newBorn = false,
        requester = professionalIdentification,
        procedures = listOf(
            GuiaCreationProcedure(
                procedureId = "123456",
                quantity = 1
            )
        ),
        personId = person.id.toUUID(),
        creatorCnpj = "12345678901234",
    )

    @Test
    fun `#getPersonAndCreateTotvsGuia should get person and create guia`() = runBlocking {
        val origin = TotvsGuiaOrigin.EITA

        val totvsGuia = TestModelFactory.buildTotvsGuia(personId = person.id)

        coEvery { personService.get(person.id) } returns person
        coEvery { totvsGuiaService.add(any()) } returns totvsGuia

        val result = service.getPersonAndCreateTotvsGuia(guiaCreationRequest, origin)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce { totvsGuiaService.add(match { it.personId == totvsGuia.personId }) }
    }

    @Test
    fun `#getPersonAndCreateTotvsGuiaAndProcedures should get person and create guia and procedures`() = runBlocking {
        val totvsGuia = TestModelFactory.buildTotvsGuia(
            personId = person.id,
        )
        val mvAuthorizedProcedures = listOf(TestModelFactory.buildMvAuthorizedProcedure(procedureId = "123456"))
        val origin = TotvsGuiaOrigin.EITA

        coEvery { personService.get(person.id) } returns person
        coEvery { totvsGuiaService.add(any()) } returns totvsGuia
        coEvery { mvAuthorizedProcedureService.addList(any()) } returns mvAuthorizedProcedures

        val result = service.getPersonAndCreateTotvsGuiaAndProcedures(guiaCreationRequest, origin)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce { totvsGuiaService.add(
            match {
                it.personId == person.id &&
                        it.type == guiaCreationRequest.type &&
                        it.origin == origin &&
                        it.requestedByProfessional == professionalIdentification &&
                        it.status == TotvsGuiaStatus.PENDING
            }
        ) }
        coVerifyOnce { mvAuthorizedProcedureService.addList(match { procedures ->
            procedures.first().procedureId == "123456" &&
                    procedures.first().opmeId == null &&
                    procedures.first().testRequestId == null &&
                    procedures.first().status == MvAuthorizedProcedureStatus.PENDING &&
                    procedures.first().requestedByProfessional == guiaCreationRequest.requester &&
                    procedures.first().authorizedByStaff == null &&
                    procedures.first().healthEventId == null &&
                    procedures.first().extraGuiaInfo.newBorn == guiaCreationRequest.newBorn &&
                    procedures.first().extraGuiaInfo.accidentIndication == guiaCreationRequest.accidentIndication.toString() &&
                    procedures.first().extraGuiaInfo.procedureType == guiaCreationRequest.type &&
                    procedures.first().origin == MvAuthorizedProcedureOrigin.EITA &&
                    procedures.first().totvsGuiaId == totvsGuia.id
        }) }
    }

    @Test
    fun `#getPersonAndCreateTotvsGuiaAndProcedures should get person and create guia and procedures with executor data`() = runBlocking {
        val totvsGuia = TestModelFactory.buildTotvsGuia(
            personId = person.id,
        )
        val mvAuthorizedProcedures = listOf(TestModelFactory.buildMvAuthorizedProcedure(procedureId = "123456"))
        val origin = TotvsGuiaOrigin.EITA
        val providerUnitId = RangeUUID.generate()

        coEvery { personService.get(person.id) } returns person
        coEvery { totvsGuiaService.add(any()) } returns totvsGuia
        coEvery { mvAuthorizedProcedureService.addList(any()) } returns mvAuthorizedProcedures

        val result = service.getPersonAndCreateTotvsGuiaAndProcedures(
            guiaCreationRequest.copy(executorCnpj = "12345678901234", providerUnitId = providerUnitId),
            origin
        )
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce { totvsGuiaService.add(
            match {
                it.personId == person.id &&
                        it.type == guiaCreationRequest.type &&
                        it.origin == origin &&
                        it.requestedByProfessional == professionalIdentification &&
                        it.status == TotvsGuiaStatus.PENDING
            }
        ) }
        coVerifyOnce { mvAuthorizedProcedureService.addList(match { procedures ->
            procedures.first().procedureId == "123456" &&
                    procedures.first().opmeId == null &&
                    procedures.first().testRequestId == null &&
                    procedures.first().status == MvAuthorizedProcedureStatus.EXECUTED &&
                    procedures.first().requestedByProfessional == guiaCreationRequest.requester &&
                    procedures.first().authorizedByStaff == null &&
                    procedures.first().healthEventId == null &&
                    procedures.first().extraGuiaInfo.newBorn == guiaCreationRequest.newBorn &&
                    procedures.first().extraGuiaInfo.accidentIndication == guiaCreationRequest.accidentIndication.toString() &&
                    procedures.first().extraGuiaInfo.procedureType == guiaCreationRequest.type &&
                    procedures.first().origin == MvAuthorizedProcedureOrigin.EITA &&
                    procedures.first().totvsGuiaId == totvsGuia.id &&
                    procedures.first().executedByProviderUnitId == providerUnitId &&
                    procedures.first().executorCnpj == "12345678901234"
        }) }
    }

    @Test
    fun `#createTotvsGuia - should create totvsGuia`() = runBlocking {
        val totvsGuiaModel = TotvsGuia(
            type = MvUtil.TISS.EXAM,
            origin = TotvsGuiaOrigin.EITA,
            requestedByProfessional = professionalIdentification,
            personId = person.id,
            status = TotvsGuiaStatus.PENDING,
            requestedAt = LocalDate.now(),
            executedOnCreation = false,
        )
        val totvsGuia = TestModelFactory.buildTotvsGuia()

        coEvery { totvsGuiaService.add(totvsGuiaModel) } returns totvsGuia

        val result = service.createTotvsGuia(totvsGuiaModel)

        ResultAssert.assertThat(result).isSuccessWithData(totvsGuia)

        coVerifyOnce { totvsGuiaService.add(totvsGuiaModel) }
    }
}
