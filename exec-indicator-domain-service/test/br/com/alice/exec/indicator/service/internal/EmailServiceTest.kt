package br.com.alice.exec.indicator.service.internal

import br.com.alice.exec.indicator.client.MailerService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EmailServiceTest {
    private val mailerService: MailerService = mockk()
    private val emailService: EmailService = EmailService(mailerService)
    private val email = "<EMAIL>"
    private val url = "https://www.alice.com.br"
    private val app = "EITA"
    private val pin = "0123456"

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#sendPinAuthenticationEmail when send email link successfully`() = runBlocking {

        coEvery {
            mailerService.sendEmail(any(), any(), any())
        } returns "email_receipt_id".success()

        val result = emailService.sendPinAuthenticationEmail(email = email, recipient = email, pin = pin, app = app)
        result.success()

        coVerify(exactly = 1) {
            mailerService.sendEmail(any(), any(), any())
        }

    }

    @Test
    fun `#sendPinAuthenticationEmail when error to send email link`() = runBlocking {

        coEvery {
            mailerService.sendEmail(any(), any(), any())
        } returns Throwable().failure()

        val result = emailService.sendPinAuthenticationEmail(email = email, recipient = email, pin = pin, app = app)
        result.isFailure()

        coVerify(exactly = 1) {
            mailerService.sendEmail(any(), any(), any())
        }


    }

}
