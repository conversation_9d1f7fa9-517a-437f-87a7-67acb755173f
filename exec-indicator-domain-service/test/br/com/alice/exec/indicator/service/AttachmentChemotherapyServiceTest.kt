package br.com.alice.exec.indicator.service

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.AttachmentChemotherapyModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.service.internal.AttachmentChemotherapyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AttachmentChemotherapyServiceTest {

    @AfterTest
    fun clear() = clearAllMocks()

    private val attachmentChemotherapyDataService: AttachmentChemotherapyModelDataService = mockk()
    private val service = AttachmentChemotherapyService(attachmentChemotherapyDataService)
    private val attachmentChemotherapy = TestModelFactory.buildAttachmentChemotherapy()

    @Test
    fun `should get attachment Chemotherapy by totvs guia id`() {
        val totvsGuiaId = attachmentChemotherapy.totvsGuiaId
        coEvery {
            attachmentChemotherapyDataService.findOne(queryEq { where { this.totvsGuiaId.eq(totvsGuiaId) } })
        } returns attachmentChemotherapy.toModel()
        runBlocking {
            val result = service.getByTotvsGuiaId(totvsGuiaId)
            assertThat(result).isSuccessWithData(attachmentChemotherapy)
        }
    }

    @Test
    fun `should add attachment Chemotherapy`() {
        coEvery { attachmentChemotherapyDataService.add(attachmentChemotherapy.toModel()) } returns attachmentChemotherapy.toModel()
        runBlocking {
            val result = service.add(attachmentChemotherapy)
            assertThat(result).isSuccessWithData(attachmentChemotherapy)
        }
    }

    @Test
    fun `#update should update attachment Chemotherapy`() = runBlocking {
        coEvery {
            attachmentChemotherapyDataService.update(attachmentChemotherapy.toModel())
        } returns attachmentChemotherapy.toModel()

        val result = service.update(attachmentChemotherapy)
        assertThat(result).isSuccessWithData(attachmentChemotherapy)
    }

    @Test
    fun `#upsert should create a attachment Chemotherapy`() = runBlocking {
        coEvery {
            attachmentChemotherapyDataService.findOne(queryEq {
                where { this.totvsGuiaId.eq(attachmentChemotherapy.totvsGuiaId) }
            })
        } returns attachmentChemotherapy.toModel().success()

        coEvery {
            attachmentChemotherapyDataService.update(attachmentChemotherapy.toModel())
        } returns NotFoundException().failure()

        coEvery {
            attachmentChemotherapyDataService.add(attachmentChemotherapy.toModel())
        } returns attachmentChemotherapy.toModel().success()

        val result = service.upsert(attachmentChemotherapy)
        assertThat(result).isSuccessWithData(attachmentChemotherapy)

        coVerifyOnce { attachmentChemotherapyDataService.add(any()) }
        coVerifyOnce { attachmentChemotherapyDataService.update(any()) }
        coVerifyOnce { attachmentChemotherapyDataService.findOne(any()) }
    }

    @Test
    fun `#upsert should update a attachment Chemotherapy`() = runBlocking {
        coEvery {
            attachmentChemotherapyDataService.findOne(queryEq {
                where { this.totvsGuiaId.eq(attachmentChemotherapy.totvsGuiaId) }
            })
        } returns attachmentChemotherapy.toModel().success()

        coEvery {
            attachmentChemotherapyDataService.update(attachmentChemotherapy.toModel())
        } returns attachmentChemotherapy.toModel().success()

        val result = service.upsert(attachmentChemotherapy)
        assertThat(result).isSuccessWithData(attachmentChemotherapy)

        coVerifyNone { attachmentChemotherapyDataService.add(any()) }
        coVerifyOnce { attachmentChemotherapyDataService.update(any()) }
        coVerifyOnce { attachmentChemotherapyDataService.findOne(any()) }
    }

}
