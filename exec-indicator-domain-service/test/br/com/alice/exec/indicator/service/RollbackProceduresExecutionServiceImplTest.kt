package br.com.alice.exec.indicator.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.exec.indicator.client.ExecutionGroupService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RollbackProceduresExecutionServiceImplTest {
    private val executionGroupService: ExecutionGroupService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val service = RollbackProceduresExecutionServiceImpl(executionGroupService, mvAuthorizedProcedureService)

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#rollbackProceduresExecution should rollback procedures and delete execution group`() = runBlocking {
        val executionGroup = TestModelFactory.buildExecutionGroup(id = RangeUUID.generate())
        val preExecutedProcedure1 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PRE_EXECUTED, executionGroupId = executionGroup.id)
        val preExecutedProcedure2 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PRE_EXECUTED, executionGroupId = executionGroup.id)
        val notPreExecutedProcedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.AUTHORIZED, executionGroupId = executionGroup.id)
        val procedures = listOf(preExecutedProcedure1, preExecutedProcedure2, notPreExecutedProcedure)
        val proceduresRollbacked = listOf(preExecutedProcedure1, preExecutedProcedure2).proceduresRollbacked()


        coEvery { executionGroupService.get(executionGroup.id) } returns executionGroup.success()
        coEvery { mvAuthorizedProcedureService.countProceduresForStatusWithinExecutionGroup(any(), executionGroup.id) } returns 2.success()
        coEvery { executionGroupService.delete(executionGroup) } returns true.success()
        coEvery { mvAuthorizedProcedureService.updateList(any()) } returns proceduresRollbacked.success()

        val result = service.rollback(procedures, executionGroup.id)

        assertEquals(proceduresRollbacked, result.get())
        assertEquals(MvAuthorizedProcedureStatus.AUTHORIZED, notPreExecutedProcedure.status)

        coVerifyOnce {
            executionGroupService.delete(executionGroup)
            mvAuthorizedProcedureService.updateList(proceduresRollbacked)
        }
    }

    @Test
    fun `#rollbackProceduresExecution should rollback procedures and should not delete execution group`() = runBlocking {
        val executionGroup = TestModelFactory.buildExecutionGroup(id = RangeUUID.generate())
        val preExecutedProcedure1 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PRE_EXECUTED, executionGroupId = executionGroup.id)
        val preExecutedProcedure2 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PRE_EXECUTED, executionGroupId = executionGroup.id)
        val notPreExecutedProcedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.AUTHORIZED, executionGroupId = executionGroup.id)
        val proceduresRollbacked = listOf(preExecutedProcedure1).proceduresRollbacked()


        coEvery { executionGroupService.get(executionGroup.id) } returns executionGroup.success()
        coEvery { mvAuthorizedProcedureService.countProceduresForStatusWithinExecutionGroup(any(), executionGroup.id) } returns 2.success()
        coEvery { mvAuthorizedProcedureService.updateList(any()) } returns proceduresRollbacked.success()

        val result = service.rollback(listOf(preExecutedProcedure1), executionGroup.id)

        assertEquals(proceduresRollbacked, result.get())
        assertEquals(MvAuthorizedProcedureStatus.AUTHORIZED, notPreExecutedProcedure.status)
        assertEquals(MvAuthorizedProcedureStatus.PRE_EXECUTED, preExecutedProcedure2.status)

        coVerifyNone {
            executionGroupService.delete(executionGroup)
        }

        coVerifyOnce {
            mvAuthorizedProcedureService.updateList(proceduresRollbacked)
        }
    }

    @Test
    fun `#rollbackProceduresExecution should not rollback procedures with status EXECUTED`() = runBlocking {
        val executionGroup = TestModelFactory.buildExecutionGroup(id = RangeUUID.generate())
        val preExecutedProcedure1 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.EXECUTED, executionGroupId = executionGroup.id)
        val preExecutedProcedure2 = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.EXECUTED, executionGroupId = executionGroup.id)
        val notPreExecutedProcedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.AUTHORIZED, executionGroupId = executionGroup.id)
        val procedures = listOf(preExecutedProcedure1, preExecutedProcedure2, notPreExecutedProcedure)
        val proceduresRollbacked = listOf(preExecutedProcedure1, preExecutedProcedure2).proceduresRollbacked()


        coEvery { executionGroupService.get(executionGroup.id) } returns executionGroup.success()
        coEvery { mvAuthorizedProcedureService.countProceduresForStatusWithinExecutionGroup(any(), executionGroup.id) } returns 2.success()

        assertFailsWith<IllegalArgumentException> {
            service.rollback(procedures, executionGroup.id)
        }

        coVerifyNone {
            executionGroupService.delete(executionGroup)
            mvAuthorizedProcedureService.updateList(proceduresRollbacked)
        }
    }

    private fun List<MvAuthorizedProcedure>.proceduresRollbacked() =
        this.map { it.copy(
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            executionGroupId = null,
            executorId = null,
            executedAt = null
        ) }
}
