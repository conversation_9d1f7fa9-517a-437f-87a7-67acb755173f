package br.com.alice.exec.indicator.service

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProcedureAlreadyCriticized
import br.com.alice.data.layer.services.MvAuthorizedProcedureModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEvent
import br.com.alice.exec.indicator.models.RecentExecutionGroupResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MvAuthorizedProcedureServiceImplTest {
    private val data: MvAuthorizedProcedureModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = MvAuthorizedProcedureServiceImpl(data, kafkaProducerService)

    @AfterTest
    fun setup() = clearAllMocks()

    private val authorizedProcedure = TestModelFactory.buildMvAuthorizedProcedure()
    private val authorizedProcedures = listOf(authorizedProcedure)
    private val person = TestModelFactory.buildPerson()
    private val testRequestId = RangeUUID.generate()
    private val anotherPerson = TestModelFactory.buildPerson()
    private val executionGroupId = RangeUUID.generate()
    private val authorizerId = RangeUUID.generate()

    private val authorizedProcedureBatch = listOf(
        TestModelFactory.buildMvAuthorizedProcedure(
            personId = person.id,
            procedureId = "10",
            testRequestId = testRequestId,
            executionGroupId = executionGroupId
        ),
        TestModelFactory.buildMvAuthorizedProcedure(
            personId = person.id,
            procedureId = "20",
            executionGroupId = executionGroupId
        ),
        TestModelFactory.buildMvAuthorizedProcedure(
            personId = anotherPerson.id,
            procedureId = "30",
            executionGroupId = executionGroupId
        )
    )

    @Test
    fun `#add should add procedure`() = runBlocking {

        coEvery { data.add(authorizedProcedure.toModel()) } returns authorizedProcedure.toModel().success()

        val result = service.add(authorizedProcedure)

        assertThat(result).isSuccess()
        coVerify(exactly = 1) { data.add(authorizedProcedure.toModel()) }
    }

    @Test
    fun `#add should add procedure with totvsGuiaId`() = runBlocking {
        val authorizedProcedureWithTotvsGuia = authorizedProcedure.copy(totvsGuiaId = RangeUUID.generate())
        coEvery {
            data.add(authorizedProcedureWithTotvsGuia.toModel())
        } returns authorizedProcedureWithTotvsGuia.toModel().success()

        coEvery {
            kafkaProducerService.produce(any(), any())
        } returns mockk()

        val result = service.add(authorizedProcedureWithTotvsGuia)

        assertThat(result).isSuccess()
        coVerify(exactly = 1) { data.add(authorizedProcedureWithTotvsGuia.toModel()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any(),any()) }
    }

    @Test
    fun `#add should fail when data returns exception`() = runBlocking {

        coEvery { data.add(authorizedProcedure.toModel()) } returns Throwable().failure()

        val result = service.add(authorizedProcedure)

        assertThat(result).isFailure()
        coVerify(exactly = 1) { data.add(authorizedProcedure.toModel()) }
    }

    @Test
    fun `#addList should add a procedures list and produce GuiaWithProceduresUpsertedEvent`(): Unit = runBlocking {
        coEvery { data.addList(listOf(authorizedProcedure.toModel())) } returns listOf(authorizedProcedure.toModel()).success()

        coEvery { kafkaProducerService.produce(any<GuiaWithProceduresUpsertedEvent>()) } returns mockk()

        val result = data.addList(listOf(authorizedProcedure.toModel()))
        assertThat(result).isSuccess()
    }

    @Test
    fun `#update should update procedure`() = runBlocking {

        coEvery { data.update(authorizedProcedure.toModel()) } returns authorizedProcedure.toModel().success()

        val result = service.update(authorizedProcedure)

        assertThat(result).isSuccess()
        coVerify(exactly = 1) { data.update(authorizedProcedure.toModel()) }
    }

    @Test
    fun `#update should update procedure with TotvsGuiaId`() = runBlocking {
        val authorizedProcedureWithTotvsGuia = authorizedProcedure.copy(totvsGuiaId = RangeUUID.generate())

        coEvery {
            data.update(authorizedProcedureWithTotvsGuia.toModel())
        } returns authorizedProcedureWithTotvsGuia.toModel().success()

        coEvery {
            kafkaProducerService.produce(any(), any())
        } returns mockk()

        val result = service.update(authorizedProcedureWithTotvsGuia)

        assertThat(result).isSuccess()
        coVerify(exactly = 1) { data.update(authorizedProcedureWithTotvsGuia.toModel()) }
        coVerify(exactly = 1) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#update should fail when data returns exception`() = runBlocking {

        coEvery { data.update(authorizedProcedure.toModel()) } returns Throwable().failure()

        val result = service.update(authorizedProcedure)

        assertThat(result).isFailure()
        coVerify(exactly = 1) { data.update(authorizedProcedure.toModel()) }
    }

    @Test
    fun `#updateList should update a procedures list and produce GuiaWithProceduresUpsertedEvent`(): Unit = runBlocking {
        coEvery { data.updateList(listOf(authorizedProcedure.toModel())) } returns listOf(authorizedProcedure.toModel()).success()

        coEvery { kafkaProducerService.produce(any<GuiaWithProceduresUpsertedEvent>()) } returns mockk()

        val result = data.updateList(listOf(authorizedProcedure.toModel()))
        assertThat(result).isSuccess()
    }

    @Test
    fun `#findByExecutionGroupId - calls dataservice filtering by executionGroupId`() = runBlocking {
        val executionGroupId = RangeUUID.generate()

        coEvery { data.find(any()) } returns authorizedProcedures.toModel().success()

        val result = service.findByExecutionGroupId(executionGroupId)

        assertThat(result).isSuccessWithData(authorizedProcedures)
        coVerify(exactly = 1) { data.find(queryEq { where { this.executionGroupId.eq(executionGroupId) } }) }
    }

    @Test
    fun `#findExecutedProceduresWithoutProviderRestrictions - should return all executed procedures`() = runBlocking {
        val personId = PersonId()

        coEvery {
            data.find(queryEq {
                where {
                    this.personId.eq(personId)
                        .and(
                            this.status.inList(
                                listOf(
                                    MvAuthorizedProcedureStatus.ACTIVE,
                                    MvAuthorizedProcedureStatus.EXECUTED,
                                    MvAuthorizedProcedureStatus.PRE_EXECUTED
                                )
                            )
                        )
                }
            })
        } returns authorizedProcedures.toModel().success()

        val result = service.findExecutedProceduresWithoutProviderRestrictions(personId)

        assertThat(result).isSuccessWithData(authorizedProcedures)
    }

    @Test
    fun `#findExecutedProceduresByAuthorizer - should return all executed procedures by authorizer`() = runBlocking {
        val personId = PersonId()

        coEvery {
            data.find(queryEq {
                where {
                    this.personId.eq(personId)
                        .and(
                            this.status.inList(
                                listOf(
                                    MvAuthorizedProcedureStatus.EXECUTED,
                                    MvAuthorizedProcedureStatus.PRE_EXECUTED
                                )
                            )
                        )
                        .and(this.executorId.eq(authorizerId))
                }
            })
        } returns authorizedProcedures.toModel().success()

        val result = service.findExecutedProceduresByAuthorizer(personId, authorizerId)

        assertThat(result).isSuccessWithData(authorizedProcedures)
    }

    @Test
    fun `#findAuthorizedProcedures - calls dataService getting only authorized procedures - bypass_mv_authorization true`() =
        runBlocking {
            val personId = PersonId()

            coEvery { data.find(any()) } returns listOf(authorizedProcedure.toModel()).success()

            val result = service.findAuthorizedProcedures(personId)
            assertThat(result).isSuccessWithData(listOf(authorizedProcedure))

            coVerify {
                data.find(queryEq {
                    where {
                        this.personId.eq(personId)
                            .and(
                                this.status.inList(
                                    listOf(
                                        MvAuthorizedProcedureStatus.ACTIVE,
                                        MvAuthorizedProcedureStatus.PENDING,
                                        MvAuthorizedProcedureStatus.AUTHORIZED
                                    )
                                )
                            )
                            .and(this.authorizedAt.isNotNull())
                            .and(this.executedAt.isNull())
                    }
                })
            }
        }

    @Test
    fun `#findAuthorizedProcedures - calls dataService getting only authorized procedures - bypass_mv_authorization false`() =
        runBlocking {
            val personId = PersonId()

            coEvery { data.find(any()) } returns listOf(authorizedProcedure.toModel()).success()

            val result = service.findAuthorizedProcedures(personId)
            assertThat(result).isSuccessWithData(listOf(authorizedProcedure))

            coVerify {
                data.find(queryEq {
                    where {
                        this.personId.eq(personId)
                            .and(
                                this.status.inList(
                                    listOf(
                                        MvAuthorizedProcedureStatus.ACTIVE,
                                        MvAuthorizedProcedureStatus.PENDING,
                                        MvAuthorizedProcedureStatus.AUTHORIZED
                                    )
                                )
                            )
                            .and(this.authorizedAt.isNotNull())
                            .and(this.executedAt.isNull())
                    }
                })
            }
        }

    @Test
    fun `#findAvailablesByIds should fail when found procedures that belong to different persons`() =
        runBlocking {
            val proceduresIds = authorizedProcedureBatch.map { it.id }

            coEvery {
                data.find(queryEq {
                    where {
                        this.id.inList(proceduresIds) and
                                this.executedAt.isNull() and
                                this.status.eq(MvAuthorizedProcedureStatus.AUTHORIZED)
                    }.orderBy { id }
                })
            } returns authorizedProcedureBatch.toModel().success()

            val result = service.findAvailablesByIds(proceduresIds)

            assertThat(result).isFailureOfType(InvalidArgumentException::class)
        }

    @Test
    fun `#findAvailablesByIds should call internal`() = runBlocking {
        val authorizedProcedureBatch = authorizedProcedureBatch.filter { it.personId == person.id }
        val proceduresIds = authorizedProcedureBatch.filter { it.personId == person.id }.map { it.id }

        coEvery {
            data.find(queryEq {
                where {
                    this.id.inList(proceduresIds) and
                            this.executedAt.isNull() and
                            this.status.eq(MvAuthorizedProcedureStatus.AUTHORIZED)
                }.orderBy { id }
            })
        } returns authorizedProcedureBatch.toModel().success()

        val result = service.findAvailablesByIds(proceduresIds)

        assertThat(result).isSuccessWithData(authorizedProcedureBatch)
    }

    @Test
    fun `#findByStatusPaginate should return procedures and total successfully`() = runBlocking {
        coEvery { data.find(any()) } returns listOf(authorizedProcedure.toModel()).success()
        coEvery { data.count(any()) } returns 1.success()


        val status = MvAuthorizedProcedureStatus.ACTIVE
        val page = 1
        val limit = 1
        val sort = SortOrder.Ascending

        val result = service.findByStatusPaginated(status, page, limit, sort)
        assertThat(result).isSuccessWithData(Pair(listOf(authorizedProcedure), 1))
        coVerify(exactly = 1) {
            data.find(queryEq {
                where { this.status.eq(status) }.orderBy { createdAt }.sortOrder { sort }.offset { limit * (page - 1) }
                    .limit { limit }
            })
        }
        coVerify(exactly = 1) {
            data.count(queryEq {
                where { this.status.eq(status) }
            })
        }
    }

    @Test
    fun `#findByStatusPaginate with custom order field should return procedures and total successfully`() =
        runBlocking {
            coEvery { data.find(any()) } returns listOf(authorizedProcedure.toModel()).success()
            coEvery { data.count(any()) } returns 1.success()


            val status = MvAuthorizedProcedureStatus.ACTIVE
            val page = 1
            val limit = 1
            val sort = SortOrder.Ascending
            val sortOrder = SortOrder.Descending
            val sortField = "authorizedAt"

            val result = service.findByStatusPaginated(status, page, limit, sort, sortOrder, sortField)
            assertThat(result).isSuccessWithData(Pair(listOf(authorizedProcedure), 1))
            coVerify(exactly = 1) {
                data.find(queryEq {
                    where { this.status.eq(status) }.orderBy { authorizedAt }.sortOrder { sortOrder }
                        .offset { limit * (page - 1) }
                        .limit { limit }
                })
            }
            coVerify(exactly = 1) {
                data.count(queryEq {
                    where { this.status.eq(status) }
                })
            }
        }

    @Test
    fun `#findByStatusPaginate without pagination should return procedures and total successfully`() = runBlocking {
        coEvery { data.find(any()) } returns listOf(authorizedProcedure, authorizedProcedure).toModel().success()


        val status = MvAuthorizedProcedureStatus.ACTIVE
        val page = 1
        val limit = -1
        val sort = SortOrder.Ascending
        val sortOrder = SortOrder.Descending
        val sortField = "authorizedAt"

        val result = service.findByStatusPaginated(status, page, limit, sort, sortOrder, sortField)
        assertThat(result).isSuccessWithData(Pair(listOf(authorizedProcedure, authorizedProcedure), 2))
        coVerify(exactly = 1) {
            data.find(queryEq {
                where { this.status.eq(status) }
            })
        }
    }

    @Test
    fun `#criticizeProcedureWithStaff should change status of procedure and persist it`() = runBlocking {
        val procedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PENDING)
        coEvery { data.get(any()) } returns procedure.toModel().success()
        coEvery { data.update(any()) } returns procedure.toModel().success()

        val procedureId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val status = MvAuthorizedProcedureStatus.AUTHORIZED

        val result = service.criticizeProcedureWithStaff(procedureId, staffId, status).get()

        assert(result)
        coVerify(exactly = 1) {
            data.update(withArg {
                assertEquals(it.status, MvAuthorizedProcedureStatus.AUTHORIZED)
                assertEquals(it.authorizedByStaff, staffId)
            })
        }
    }

    @Test
    fun `#criticizeProcedureWithStaff should execute procedure and persist it, if procedure type is EXTENSION`() =
        runBlocking {
            val procedure = TestModelFactory.buildMvAuthorizedProcedure(
                status = MvAuthorizedProcedureStatus.PENDING,
                extraGuiaInfo = ExtraGuiaInfo(procedureType = MvUtil.TISS.EXTENSION)
            )
            coEvery { data.get(any()) } returns procedure.toModel().success()
            coEvery { data.update(any()) } returns procedure.toModel().success()

            val procedureId = RangeUUID.generate()
            val staffId = RangeUUID.generate()
            val status = MvAuthorizedProcedureStatus.AUTHORIZED

            val result = service.criticizeProcedureWithStaff(procedureId, staffId, status).get()

            assert(result)
            coVerify(exactly = 1) {
                data.update(withArg {
                    assertEquals(it.status, MvAuthorizedProcedureStatus.EXECUTED)
                    assertEquals(it.authorizedByStaff, staffId)
                })
            }
        }

    @Test
    fun `#criticizeProcedureWithStaff should not change status and return false if procedure is already in a final status`() =
        runBlocking {
            val procedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.AUTHORIZED)
            coEvery { data.get(any()) } returns procedure.toModel().success()

            val procedureId = RangeUUID.generate()
            val staffId = RangeUUID.generate()
            val status = MvAuthorizedProcedureStatus.UNAUTHORIZED

            assertFailsWith<ProcedureAlreadyCriticized> {
                service.criticizeProcedureWithStaff(procedureId, staffId, status).get()
            }

            coVerify(exactly = 0) {
                data.update(any())
            }
        }

    @Test
    fun `#criticizeProcedureWithStaff should return failure if update failed`() = runBlocking {
        mockLocalDateTime { dateTime ->
            val procedureId = RangeUUID.generate()
            val staffId = RangeUUID.generate()
            val status = MvAuthorizedProcedureStatus.AUTHORIZED

            val procedure = TestModelFactory.buildMvAuthorizedProcedure(status = MvAuthorizedProcedureStatus.PENDING)
            coEvery { data.get(any()) } returns procedure.toModel().success()
            coEvery { data.update(any()) } returns Throwable("Update failure").failure()

            val result = service.criticizeProcedureWithStaff(procedureId, staffId, status)

            assertThat(result).isFailure()
            coVerify(exactly = 1) {
                data.update(withArg {
                    assertEquals(it.status, MvAuthorizedProcedureStatus.AUTHORIZED)
                    assertEquals(it.authorizedByStaff, staffId)
                })
            }
        }
    }

    @Test
    fun `#updateStatusFromExecutionGroupId should update procedure status`() = runBlocking {
        val procedures = authorizedProcedures.map {
            it.copy(
                executionGroupId = executionGroupId,
                status = MvAuthorizedProcedureStatus.EXECUTED
            )
        }

        coEvery { service.findByExecutionGroupId(executionGroupId) } returns procedures.success()
        coEvery { data.find(any()) } returns procedures.toModel().success()
        coEvery { data.update(any()) } returns procedures.toModel().first().success()

        val status = MvAuthorizedProcedureStatus.AUTHORIZED

        val result = service.updateStatusFromExecutionGroupId(executionGroupId, status).get()

        assert(result)
        coVerify(exactly = 1) {
            data.update(withArg {
                assertEquals(it.status, MvAuthorizedProcedureStatus.AUTHORIZED)
            })
        }
    }

    @Test
    fun `#markProceduresAsExecutedFromExecutionGroup should update procedure status and executor in procedures from execution group`() =
        runBlocking {
            val procedures = authorizedProcedures.map {
                it.copy(
                    executionGroupId = executionGroupId,
                    status = MvAuthorizedProcedureStatus.AUTHORIZED
                )
            }

            coEvery { service.findByExecutionGroupId(executionGroupId) } returns procedures.success()
            coEvery { data.find(any()) } returns procedures.toModel().success()
            coEvery { data.update(any()) } returns procedures.toModel().first().success()

            val result = service.markProceduresAsExecutedFromExecutionGroup(executionGroupId, authorizerId).get()

            assert(result)
            coVerify(exactly = 1) {
                data.update(withArg {
                    assertEquals(it.status, MvAuthorizedProcedureStatus.EXECUTED)
                    assertEquals(it.executorId, authorizerId)
                })
            }
        }

    @Test
    fun `#markProceduresAsExecutedFromExecutionGroup should not update procedure where status is unauthorized`() =
        runBlocking {
            val procedures = authorizedProcedures.map {
                it.copy(
                    executionGroupId = executionGroupId,
                    status = MvAuthorizedProcedureStatus.UNAUTHORIZED
                )
            }

            coEvery { service.findByExecutionGroupId(executionGroupId) } returns procedures.success()
            coEvery { data.find(any()) } returns procedures.toModel().success()
            coEvery { data.update(any()) } returns procedures.toModel().first().success()

            val result = service.markProceduresAsExecutedFromExecutionGroup(executionGroupId, authorizerId).get()

            assert(result)
            coVerify(exactly = 0) {
                data.update(any())
            }
        }

    @Test
    fun `#findByPersonIdAndTypeAndStatus should return procedures by status, person and type`() = runBlocking {
        val personId = person.id
        val procedureType = MvUtil.TISS.EXAM
        val status = listOf(
            MvAuthorizedProcedureStatus.AUTHORIZED
        )

        coEvery { data.find(any()) } returns authorizedProcedures.toModel().success()

        val result = service.findByPersonIdAndTypeAndStatus(
            personId,
            procedureType,
            status
        )
        assertThat(result).isSuccessWithData(authorizedProcedures)

        coVerify(exactly = 1) {
            data.find(queryEq {
                where {
                    this.personId.eq(personId)
                        .and(this.status.inList(status))
                        .and(this.procedureType.eq(procedureType))
                        .and(br.com.alice.common.service.data.dsl.not(this.executionGroupId.isNull()))
                }.orderBy { createdAt }
                    .sortOrder { SortOrder.Descending }
            })
        }


    }

    @Test
    fun `#findRecentExecutionGroupByProceduresAndProvider should return recent execution group`() = runBlocking {
        val person = person.id
        val date = LocalDateTime.now().minusMinutes(10L)
        val aliceCodes = authorizedProcedureBatch.mapNotNull { it.procedureId }

        val expectedResponse = RecentExecutionGroupResponse(true, executionGroupId)

        coEvery { data.find(any()) } returns authorizedProcedureBatch.toModel().success()

        val result = service.findRecentExecutionGroupByProceduresAndProvider(
            person = person,
            date = date,
            aliceCodes = aliceCodes,
            authorizerId = authorizerId
        )
        assertThat(result).isSuccessWithData(expectedResponse)

        coVerify(exactly = 1) {
            data.find(queryEq {
                where {
                    this.createdAt.greater(date).and(this.createdByAuthorizer.eq(authorizerId))
                        .and(this.personId.eq(person))
                }.orderBy { this.createdAt }.sortOrder { desc }
            })
        }

    }

    @Test
    fun `#findRecentExecutionGroupByProceduresAndProvider should return no values (recent execution group not exists)`() =
        runBlocking {
            val person = person.id
            val date = LocalDateTime.now().minusMinutes(10L)
            val aliceCodes = authorizedProcedureBatch.mapNotNull { it.procedureId }

            val expectedResponse = RecentExecutionGroupResponse(false, null)

            coEvery { data.find(any()) } returns emptyList<MvAuthorizedProcedure>().toModel().success()

            val result = service.findRecentExecutionGroupByProceduresAndProvider(
                person = person,
                date = date,
                aliceCodes = aliceCodes,
                authorizerId = authorizerId
            )
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerify(exactly = 1) {
                data.find(queryEq {
                    where {
                        this.createdAt.greater(date).and(this.createdByAuthorizer.eq(authorizerId))
                            .and(this.personId.eq(person))
                    }.orderBy { this.createdAt }.sortOrder { desc }
                })
            }

        }

    @Test
    fun `#countProceduresForStatusWithinExecutionGroup returns quantity of procedures with given status and executionGroupId`() =
        runBlocking {
            val executionGroupId = RangeUUID.generate()
            val count = 1
            val status = listOf(MvAuthorizedProcedureStatus.PRE_EXECUTED, MvAuthorizedProcedureStatus.PRE_EXECUTED)

            coEvery { data.count(any()) } returns count.success()

            val result = service.countProceduresForStatusWithinExecutionGroup(status, executionGroupId)

            assertThat(result).isSuccessWithData(count)

            coVerifyOnce {
                data.count(queryEq {
                    where {
                        this.status.inList(status).and(this.executionGroupId.eq(executionGroupId))
                    }
                })
            }
        }

    @Test
    fun `#findByStatusAndCreatedAt - should return procedures giving status and greater than created at`() =
        runBlocking {
            val executedAt = LocalDateTime.now().minusMinutes(30)
            val status = MvAuthorizedProcedureStatus.PRE_EXECUTED

            coEvery {
                data.find(queryEq {
                    where {
                        this.status.eq(status).and(this.executedAt.lessEq(executedAt))
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findByStatusAndExecutedAt(status, executedAt)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findUnSyncProceduresByRange - should return procedures that were not synchronized in the eventinder`() =
        runBlocking {
            val start = LocalDateTime.now().minusMinutes(30)
            val end = LocalDateTime.now()
            val status = MvAuthorizedProcedureStatus.EXECUTED

            coEvery {
                data.find(queryEq {
                    where {
                        this.status.eq(status)
                            .and(this.createdAt.greaterEq(start))
                            .and(this.createdAt.lessEq(end))
                            .and(this.healthEventId.isNull())
                            .and(this.executionGroupId.isNotNull())
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findUnSyncProceduresByRange(start, end)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findByTotvsGuiaIds - should return procedures by totvs guia ids`() =
        runBlocking {
            val totvsGuiaIds = listOf(RangeUUID.generate())

            coEvery {
                data.find(queryEq {
                    where {
                        this.totvsGuiaId.inList(totvsGuiaIds)
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findByTotvsGuiaIds(totvsGuiaIds)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findExecutedProceduresByProviderUnit - should return executed procedures by provider unit`() =
        runBlocking {
            val person = person.id
            val providerUnit = RangeUUID.generate()
            val range = IntRange(0, 10)
            val status = listOf(MvAuthorizedProcedureStatus.EXECUTED, MvAuthorizedProcedureStatus.PRE_EXECUTED)

            coEvery {
                data.find(queryEq {
                    where {
                        this.personId.eq(person)
                            .and(this.status.inList(status))
                            .and(this.executedByProviderUnitId.eq(providerUnit))
                    }.orderBy { createdAt }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.count() }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findExecutedProceduresByProviderUnit(person, providerUnit, range)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findByPersonIdAndStatus - should return procedures by personId and status`() =
        runBlocking {
            val personId = PersonId()
            val status = listOf(MvAuthorizedProcedureStatus.AUTHORIZED)
            val createdAt = LocalDateTime.now().minusDays(90)

            coEvery {
                data.find(queryEq {
                    where {
                        this.personId.eq(personId)
                            .and(this.status.inList(status))
                            .and(this.createdAt.greaterEq(createdAt))
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findByPersonIdAndStatus(personId, status, createdAt)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findByTotvsGuiaIdsAndStatus - should return procedures by totvsGuiaId and status`() =
        runBlocking {
            val totvsGuiaIds = listOf(RangeUUID.generate())
            val status = listOf(MvAuthorizedProcedureStatus.AUTHORIZED)

            coEvery {
                data.find(queryEq {
                    where {
                        this.totvsGuiaId.inList(totvsGuiaIds)
                            .and(this.status.inList(status))
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findByTotvsGuiaIdsAndStatus(totvsGuiaIds, status)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }

    @Test
    fun `#findByTotvsGuiaIdAndStatus - should return procedures by totvsGuiaId and status`() =
        runBlocking {
            val totvsGuiaId = RangeUUID.generate()
            val status = listOf(MvAuthorizedProcedureStatus.AUTHORIZED)

            coEvery {
                data.find(queryEq {
                    where {
                        this.totvsGuiaId.eq(totvsGuiaId)
                            .and(this.status.inList(status))
                    }
                })
            } returns authorizedProcedures.toModel().success()

            val result = service.findByTotvsGuiaIdAndStatus(totvsGuiaId, status)

            assertThat(result).isSuccessWithData(authorizedProcedures)
        }
}


