package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.eita.nullvs.models.guia.BeneficiaryGuiaResponse
import br.com.alice.eita.nullvs.models.guia.GuiaGlossResponse
import br.com.alice.eita.nullvs.models.guia.OpmeResponse
import br.com.alice.eita.nullvs.models.guia.RequesterSimpleDataResponse
import br.com.alice.eita.nullvs.models.guia.SolicitationOpmeDataResponse
import br.com.alice.eita.nullvs.models.guia.TotvsOpmeAttachmentResponse
import br.com.alice.exec.indicator.client.GlossAuthorizationInfoService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class UpsertAttachmentOpmeServiceTest {

    private val personService: PersonService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val attachmentOpmeService: AttachmentOpmeService = mockk()
    private val glossAuthorizationInfoService: GlossAuthorizationInfoService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val glossService: GlossService = mockk()

    private val service = UpsertAttachmentOpmeService(
        personService,
        totvsGuiaService,
        attachmentOpmeService,
        glossAuthorizationInfoService,
        kafkaProducerService,
        glossService
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#should upsert attachment opme and create totvs guia without problems`() =
        mockLocalDateTime {
            val payload = buildWebhookPayload(
                glossCode = "0",
                glossDescription = "Descrição Bacana",
                procedureStatus = "UNAUTHORIZED"
            )
            val person = TestModelFactory.buildPerson(nationalId = payload.beneficiary.nationalId)
            val totvsGuia = TestModelFactory.buildTotvsGuia()
            val referenceTotvsGuia = TestModelFactory.buildTotvsGuia(externalCode = payload.referenceGuiaNumber)
            val attachmentOpme = TestModelFactory.buildAttachmentOpme()
            val glossAuthorization = TestModelFactory.buildGlossAuthorizationInfo()

            coEvery {
                personService.findByNationalId(payload.beneficiary.nationalId)
            } returns person.success()

            coEvery {
                totvsGuiaService.findByCodeOrExternalCode(
                    payload.guiaNumber,
                    payload.guiaNumber
                )
            } returns NotFoundException().failure()

            coEvery {
                totvsGuiaService.findByExternalCode(payload.referenceGuiaNumber)
            } returns referenceTotvsGuia.success()

            coEvery {
                totvsGuiaService.getOrAdd(match { it.externalCode == payload.guiaNumber })
            } returns totvsGuia.success()

            coEvery {
                glossAuthorizationInfoService.findByCodes(listOf("0"))
            } returns listOf(glossAuthorization).success()

            coEvery {
                attachmentOpmeService.upsert(match { it.totvsGuiaId == totvsGuia.id })
            } returns attachmentOpme.success()

            coEvery {
                glossService.getGlossRelatedToGuiaGlossResponse(
                    payload.opmes[0].gloss,
                    payload.opmes[0].code,
                    MvAuthorizedProcedureStatus.UNAUTHORIZED,
                )
            } returns emptyList()

            val result = service.upsert(payload)

            ResultAssert.assertThat(result).isSuccess()

            coVerifyOnce { personService.findByNationalId(payload.beneficiary.nationalId) }
            coVerifyOnce { totvsGuiaService.findByExternalCode(any()) }
            coVerifyOnce { totvsGuiaService.findByCodeOrExternalCode(any(), any()) }
            coVerifyOnce { totvsGuiaService.getOrAdd(match { it.externalCode == payload.guiaNumber }) }
            coVerifyOnce { attachmentOpmeService.upsert(match { it.totvsGuiaId == totvsGuia.id }) }
            coVerifyOnce { glossService.getGlossRelatedToGuiaGlossResponse(any(), any(), any()) }
        }

    @Test
    fun `#should upsert attachment opme and update totvs guia without problems`() =
        mockLocalDateTime {
            val payload = buildWebhookPayload()
            val person = TestModelFactory.buildPerson(nationalId = payload.beneficiary.nationalId)
            val totvsGuia = TestModelFactory.buildTotvsGuia()
            val referenceTotvsGuia = TestModelFactory.buildTotvsGuia(externalCode = payload.referenceGuiaNumber)
            val attachmentOpme = TestModelFactory.buildAttachmentOpme()

            coEvery {
                personService.findByNationalId(payload.beneficiary.nationalId)
            } returns person.success()

            coEvery {
                totvsGuiaService.findByCodeOrExternalCode(
                    payload.guiaNumber,
                    payload.guiaNumber
                )
            } returns totvsGuia.success()

            coEvery {
                totvsGuiaService.findByExternalCode(payload.referenceGuiaNumber)
            } returns referenceTotvsGuia.success()

            coEvery {
                totvsGuiaService.update(match { it.externalCode == payload.guiaNumber && it.cnpj == payload.cnpj })
            } returns totvsGuia.success()

            coEvery {
                attachmentOpmeService.upsert(match { it.totvsGuiaId == totvsGuia.id })
            } returns attachmentOpme.success()
            coEvery {
                glossService.getGlossRelatedToGuiaGlossResponse(
                    emptyList(),
                    payload.opmes.first().code,
                    MvAuthorizedProcedureStatus.AUTHORIZED,
                )
            } returns emptyList()

            val result = service.upsert(payload)

            ResultAssert.assertThat(result).isSuccess()

            coVerifyOnce { personService.findByNationalId(payload.beneficiary.nationalId) }
            coVerifyOnce { totvsGuiaService.findByExternalCode(any()) }
            coVerifyOnce { totvsGuiaService.findByCodeOrExternalCode(any(), any()) }
            coVerifyOnce { totvsGuiaService.update(match { it.externalCode == payload.guiaNumber }) }
            coVerifyOnce { attachmentOpmeService.upsert(match { it.totvsGuiaId == totvsGuia.id }) }
            coVerifyOnce { glossService.getGlossRelatedToGuiaGlossResponse(any(), any(), any()) }
        }

    private fun buildWebhookPayload(
        guiaNumber: String = "000120240400000146",
        guiaStatus: String = "PENDING",
        passcode: String = "14873975357019928399",
        guiaType: String = "09",
        procedureCode: String = "40101010",
        procedureStatus: String = "AUTHORIZED",
        glossCode: String? = null,
        glossDescription: String? = null,
        opmes: List<OpmeResponse> = listOf(
            buildOpmeResponse(
                procedureCode,
                procedureStatus,
                glossCode,
                glossDescription
            )
        )
    ) =
        TotvsOpmeAttachmentResponse(
            action = "CREATED",
            guiaNumber = guiaNumber,
            referenceGuiaNumber = "000120240200000119",
            providerGuiaNumber = guiaNumber,
            status = guiaStatus,
            authorizationDate = LocalDate.now(),
            guiaType = guiaType,
            passcode = passcode,
            beneficiary = BeneficiaryGuiaResponse(
                name = "RODRIGO RAMOS CASAGRANDE",
                identificationCardNumber = "00010004006181004",
                nationalId = "10763435627",
                isNewBorn = false
            ),
            requester = RequesterSimpleDataResponse(
                name = "ALICE SERVICOS MEDICOS",
                phone = "31999999999",
                email = "<EMAIL>"
            ),
            solicitation = SolicitationOpmeDataResponse(
                solicitationDate = LocalDate.now(),
                observation = "Observation",
                specification = "Specification",
                justification = "Justification"
            ),
            opmes = opmes,
            cnpj = "00000000000191"
        )

    private fun buildOpmeResponse(
        procedureCode: String = "77853598",
        procedureStatus: String = "AUTHORIZED",
        glossCode: String? = null,
        glossDescription: String? = null
    ) =
        OpmeResponse(
            table = "19",
            code = procedureCode,
            description = "AGE CERVICAL - 03-090145 CAGE CERVICAL 14X5MM",
            authorizedQuantity = 1,
            requestedQuantity = 1,
            unitValue = 15000.0,
            status = procedureStatus,
            gloss = if (glossCode !== null && glossDescription !== null) listOf(
                GuiaGlossResponse(
                    code = glossCode,
                    description = glossDescription
                )
            ) else emptyList()

        )

}
