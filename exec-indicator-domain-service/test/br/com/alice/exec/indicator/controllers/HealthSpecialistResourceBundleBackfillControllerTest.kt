package br.com.alice.exec.indicator.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HealthSpecialistResourceBundleBackfillControllerTest : RecurrentControllerTestHelper() {

    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()

    private val controller = HealthSpecialistResourceBundleBackfillController(
        healthSpecialistResourceBundleService,
        healthcareResourceService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#deleteBatch should return OK if payload is empty`() = runBlocking {
        val request = HealthSpecialistResourceBundleDeleteRequest(emptyList<UUID>())

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle/delete", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyNone { healthSpecialistResourceBundleService.findByIds(any()) }
        coVerifyNone { healthSpecialistResourceBundleService.delete(any()) }
    }

    @Test
    fun `#deleteBatch should return OK if could delete bundles`() = runBlocking {
        val id = RangeUUID.generate()
        val bundle = TestModelFactory.buildHealthSpecialistResourceBundle(id = id)

        coEvery {
            healthSpecialistResourceBundleService.findByIds(listOf(id))
        } returns listOf(bundle, bundle).success()

        coEvery {
            healthSpecialistResourceBundleService.delete(bundle)
        } returns true.success()

        val request = HealthSpecialistResourceBundleDeleteRequest(listOf(id))

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle/delete", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.findByIds(listOf(id)) }
        coVerify(exactly = 2) { healthSpecialistResourceBundleService.delete(bundle) }
    }

    @Test
    fun `#updateBatch should return OK if payload is empty`() = runBlocking {
        val request = HealthSpecialistResourceBundleUpdateRequest(emptyList())

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyNone { healthSpecialistResourceBundleService.findByIds(any()) }
        coVerifyNone { healthSpecialistResourceBundleService.findByPrimaryTussList(any()) }
    }

    @Test
    fun `#updateBatch should return empty if could not find bundles to update`() = runBlocking {
        val id = RangeUUID.generate()
        val primaryTuss = "10101012"

        val request = HealthSpecialistResourceBundleUpdateRequest(
            listOf(
                HealthSpecialistResourceBundleUpdatePayload(id = id),
                HealthSpecialistResourceBundleUpdatePayload(primaryTuss = primaryTuss)
            )
        )

        coEvery {
            healthSpecialistResourceBundleService.findByIds(listOf(id))
        } returns emptyList<HealthSpecialistResourceBundle>().success()

        coEvery {
            healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss))
        } returns emptyList<HealthSpecialistResourceBundle>().success()

        val expectedResponse = HealthSpecialistResourceBundleUpdateResponse(0, listOf(primaryTuss), emptyList())

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.findByIds(listOf(id)) }
        coVerifyOnce { healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss)) }

        coVerifyNone { healthcareResourceService.findByCodesAndTableType(any(), any()) }
        coVerifyNone { healthSpecialistResourceBundleService.update(any()) }
    }

    @Test
    fun `#updateBatch should return ignore if return more than one bundle with same primary tuss`() = runBlocking {
        val primaryTuss = "10101012"
        val bundle = TestModelFactory.buildHealthSpecialistResourceBundle(primaryTuss = primaryTuss)

        val request = HealthSpecialistResourceBundleUpdateRequest(
            listOf(HealthSpecialistResourceBundleUpdatePayload(primaryTuss = primaryTuss))
        )

        coEvery {
            healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss))
        } returns listOf(bundle, bundle).success()

        val expectedResponse = HealthSpecialistResourceBundleUpdateResponse(
            updated = 0,
            primaryTussNotFound = emptyList(),
            multiplePrimaryTuss = listOf(bundle, bundle)
        )

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss)) }

        coVerifyNone { healthSpecialistResourceBundleService.findByIds(any()) }
        coVerifyNone { healthcareResourceService.findByCodesAndTableType(any(), any()) }
        coVerifyNone { healthSpecialistResourceBundleService.update(any()) }
    }

    @Test
    fun `#updateBatch should return ignore one and save other if one has multiple tuss`() = runBlocking {
        val primaryTuss = "10101012"
        val bundle = TestModelFactory.buildHealthSpecialistResourceBundle(primaryTuss = primaryTuss)

        val otherPrimaryTuss = "10101013"
        val otherBundle = TestModelFactory.buildHealthSpecialistResourceBundle(primaryTuss = otherPrimaryTuss)

        val anotherPrimaryTuss = "10101014"

        val request = HealthSpecialistResourceBundleUpdateRequest(
            listOf(
                HealthSpecialistResourceBundleUpdatePayload(primaryTuss = primaryTuss),
                HealthSpecialistResourceBundleUpdatePayload(primaryTuss = otherPrimaryTuss),
                HealthSpecialistResourceBundleUpdatePayload(primaryTuss = anotherPrimaryTuss)
            )
        )

        coEvery {
            healthSpecialistResourceBundleService.findByPrimaryTussList(
                listOf(primaryTuss, otherPrimaryTuss, anotherPrimaryTuss)
            )
        } returns listOf(bundle, bundle, otherBundle).success()

        coEvery {
            healthSpecialistResourceBundleService.update(match {
                it.primaryTuss == otherPrimaryTuss
            })
        } returns otherBundle.success()

        val expectedResponse = HealthSpecialistResourceBundleUpdateResponse(
            updated = 1,
            primaryTussNotFound = listOf(anotherPrimaryTuss),
            multiplePrimaryTuss = listOf(bundle, bundle)
        )

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByPrimaryTussList(
                listOf(primaryTuss, otherPrimaryTuss, anotherPrimaryTuss)
            )
        }
        coVerifyOnce { healthSpecialistResourceBundleService.update(any()) }

        coVerifyNone { healthSpecialistResourceBundleService.findByIds(any()) }
        coVerifyNone { healthcareResourceService.findByCodesAndTableType(any(), any()) }
    }

    @Test
    fun `#updateBatch should return updated bundles`() = runBlocking {
        val id = RangeUUID.generate()
        val secondaryResourceCode = "10101013"
        val secondaryResource = TestModelFactory.buildHealthcareResource(code = secondaryResourceCode)

        val primaryTuss = "10101012"
        val executionAmount = 10

        val firstBundle = TestModelFactory.buildHealthSpecialistResourceBundle(id = id)
        val secondBundle = TestModelFactory.buildHealthSpecialistResourceBundle(primaryTuss = primaryTuss)

        val request = HealthSpecialistResourceBundleUpdateRequest(
            listOf(
                HealthSpecialistResourceBundleUpdatePayload(
                    id = id,
                    secondaryResources = listOf(secondaryResourceCode)
                ),
                HealthSpecialistResourceBundleUpdatePayload(
                    primaryTuss = primaryTuss,
                    executionAmount = executionAmount
                )
            )
        )

        coEvery {
            healthSpecialistResourceBundleService.findByIds(listOf(id))
        } returns listOf(firstBundle).success()

        coEvery {
            healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss))
        } returns listOf(secondBundle).success()

        coEvery {
            healthcareResourceService.findByCodesAndTableType(
                listOf(secondaryResourceCode),
                HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE
            )
        } returns listOf(secondaryResource).success()

        coEvery {
            healthSpecialistResourceBundleService.update(match {
                it.id == id && it.secondaryResources == listOf(secondaryResource.id)
            })
        } returns firstBundle.success()

        coEvery {
            healthSpecialistResourceBundleService.update(match {
                it.primaryTuss == primaryTuss && it.executionAmount == executionAmount
            })
        } returns secondBundle.success()

        val expectedResponse = HealthSpecialistResourceBundleUpdateResponse(
            updated = 2,
            primaryTussNotFound = emptyList(),
            multiplePrimaryTuss = emptyList()
        )

        internalAuthentication {
            post("/backfill/health_specialist_resource_bundle", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerify(exactly = 2) { healthSpecialistResourceBundleService.update(any()) }

        coVerifyOnce { healthSpecialistResourceBundleService.findByIds(listOf(id)) }
        coVerifyOnce { healthSpecialistResourceBundleService.findByPrimaryTussList(listOf(primaryTuss)) }
        coVerifyOnce {
            healthcareResourceService.findByCodesAndTableType(
                listOf(secondaryResourceCode), HealthcareResourceService.DEFAULT_PRIMARY_TUSS_TABLE
            )
        }
    }

}
