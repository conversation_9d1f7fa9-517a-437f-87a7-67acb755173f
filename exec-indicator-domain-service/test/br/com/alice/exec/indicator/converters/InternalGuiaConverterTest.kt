package br.com.alice.exec.indicator.converters

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.MvAuthorizedProcedureOrigin
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import br.com.alice.exec.indicator.converters.InternalGuiaConverter.toMvAuthorizedProcedureData
import br.com.alice.exec.indicator.converters.InternalGuiaConverter.toMvAuthorizedProcedures
import br.com.alice.exec.indicator.converters.InternalGuiaConverter.toTotvsGuia
import br.com.alice.exec.indicator.models.GuiaCreationProcedure
import br.com.alice.exec.indicator.models.GuiaCreationRequest
import br.com.alice.exec.indicator.models.Item
import br.com.alice.exec.indicator.models.ItemSearchType
import br.com.alice.exec.indicator.models.MvAuthorizedProcedureData
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class InternalGuiaConverterTest {

    private val professionalIdentification = ProfessionalIdentification(
        fullName = "Fulano de Tal",
        email = "<EMAIL>",
        phone = "(21) 9999-9999",
        councilNumber = "123456",
        council = CouncilType.CRM,
        councilState = State.CE,
        cboCode = "9999",
        specialty = ProfessionalSpecialty(
            id = 1L,
            name = "Cardiologia"
        )
    )

    private val personId = PersonId().toUUID()
    private val procedures = listOf(GuiaCreationProcedure(procedureId = "123456", quantity = 1))
    private val origin = TotvsGuiaOrigin.EITA

    private val guiaCreationRequest = GuiaCreationRequest(
        type = MvUtil.TISS.HOSPITALIZATION,
        requestedAt = LocalDate.now(),
        newBorn = false,
        requester = professionalIdentification,
        procedures = procedures,
        personId = personId,
        medicalRequestFileIds = emptyList(),
        accidentIndication = AnsAccidentIndication.WORK,
        creatorCnpj = "12345678901234",
    )
    private val mvAuthorizedProcedureData = MvAuthorizedProcedureData(
        professional = professionalIdentification,
        extraGuiaInfo = ExtraGuiaInfo(
            procedureType = MvUtil.TISS.HOSPITALIZATION,
            dateAttendance = LocalDate.now(),
            accidentIndication = AnsAccidentIndication.WORK.toString(),
            newBorn = false,
        ),
        items = listOf(Item(aliceCode = "123456", quantity = 1, type = ItemSearchType.PROCEDURE)),
        userEmail = "<EMAIL>",
        personId = personId.toPersonId(),
    )

    @Test
    fun `#toTotvsGuia - should converter GuiaCreationRequest to TotvsGuia`() {
        val totvsGuia = TestModelFactory.buildTotvsGuia(
            type = MvUtil.TISS.HOSPITALIZATION,
            origin = TotvsGuiaOrigin.EITA,
            requestedByProfessional = professionalIdentification,
            personId = personId.toPersonId(),
            requestedAt = LocalDate.now(),
            cnpj = "12345678901234"
        )

        val result = guiaCreationRequest.toTotvsGuia(origin).success()

        ResultAssert.assertThat(result).isSuccessWithDataIgnoringGivenFields(totvsGuia, "id", "createdAt", "updatedAt", "code")
    }

    @Test
    fun `#toMvAuthorizedProcedureData - should converter GuiaCreationRequest to MvAuthorizedProcedureData`() {
        val result = guiaCreationRequest.toMvAuthorizedProcedureData().success()

        ResultAssert.assertThat(result).isSuccessWithData(mvAuthorizedProcedureData)
    }

    @Test
    fun `#toMvAuthorizedProcedures - should converter MvAuthorizedProcedureData to MvAuthorizedProcedures`() {
        val totvsGuiaId = RangeUUID.generate()
        val mvAuthorizedProcedure = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                personId = personId.toPersonId(),
                procedureId = "123456",
                authorizedAt = LocalDateTime.now(),
                status = MvAuthorizedProcedureStatus.PENDING,
                requestedByProfessional = professionalIdentification,
                extraGuiaInfo = ExtraGuiaInfo(
                    procedureType = MvUtil.TISS.HOSPITALIZATION,
                    accidentIndication = AnsAccidentIndication.WORK.toString(),
                    newBorn = false,
                ),
                origin = MvAuthorizedProcedureOrigin.EITA,
                totvsGuiaId = totvsGuiaId,
                type = MvUtil.TISS.HOSPITALIZATION,
            )
        )

        val result = mvAuthorizedProcedureData.toMvAuthorizedProcedures(
            totvsGuiaId,
            MvAuthorizedProcedureStatus.PENDING,
            MvAuthorizedProcedureOrigin.EITA,
        ).success()

        ResultAssert.assertThat(result)
            .isSuccessWithDataIgnoringGivenFields(mvAuthorizedProcedure, "id", "createdAt", "updatedAt", "authorizedAt")
    }
}
