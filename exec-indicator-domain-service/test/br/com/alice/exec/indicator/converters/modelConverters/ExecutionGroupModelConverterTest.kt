package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ExecutionGroup
import br.com.alice.data.layer.models.ExecutionGroupModel
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.util.UUID
import kotlin.random.Random
import kotlin.test.Test

class ExecutionGroupModelConverterTest {
    private val executionGroupd = ExecutionGroup(
        providerUnitId = RangeUUID.generate(),
        code = "123456789",
        userEmail = "asdasda",
        tags = listOf("tag1", "tag2"),
        attachments = listOf("attachment1", "attachment2"),
        version = 1,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    private val executionGroupModel = ExecutionGroupModel(
        providerUnitId = executionGroupd.providerUnitId,
        code = "123456789",
        userEmail = "asdasda",
        tags = listOf("tag1", "tag2"),
        attachments = listOf("attachment1", "attachment2"),
        version = 1,
        createdAt = executionGroupd.createdAt,
        updatedAt = executionGroupd.updatedAt,
        id = executionGroupd.id
    )

    @Test
    fun testToTransport() {
        assertThat(executionGroupModel.toTransport())
            .isEqualTo(executionGroupd)
    }

    @Test
    fun testToModel() {
        assertThat(executionGroupd.toModel())
            .isEqualTo(executionGroupModel)
    }
}
