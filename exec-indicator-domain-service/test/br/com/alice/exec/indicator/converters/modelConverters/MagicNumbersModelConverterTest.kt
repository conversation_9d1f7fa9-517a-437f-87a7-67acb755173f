package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.data.layer.models.MagicNumbers
import br.com.alice.data.layer.models.MagicNumbersModel
import br.com.alice.data.layer.models.MagicNumbersStatus
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class MagicNumbersModelConverterTest {
    private val magicNumbers = MagicNumbers(
        id = UUID.randomUUID(),
        version = 1,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        expirationAt = LocalDateTime.now().plusDays(1),
        link = "http://example.com",
        code = "123456",
        status = MagicNumbersStatus.NEW,
        accessedByUser = false,
        generateTo = "<EMAIL>"
    )

    private val magicNumbersModel = MagicNumbersModel(
        id = magicNumbers.id,
        version = magicNumbers.version,
        createdAt = magicNumbers.createdAt,
        updatedAt = magicNumbers.updatedAt,
        expirationAt = magicNumbers.expirationAt,
        link = magicNumbers.link,
        code = magicNumbers.code,
        status = magicNumbers.status,
        accessedByUser = magicNumbers.accessedByUser,
        generateTo = magicNumbers.generateTo
    )

    @Test
    fun testToModel() {
        assertThat(magicNumbers.toModel())
            .isEqualTo(magicNumbersModel)
    }

    @Test
    fun testToTransport() {
        assertThat(magicNumbersModel.toTransport())
            .isEqualTo(magicNumbers)
    }
}
