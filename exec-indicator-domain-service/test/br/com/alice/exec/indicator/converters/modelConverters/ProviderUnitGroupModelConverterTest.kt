package br.com.alice.exec.indicator.converters.modelConverters

import br.com.alice.data.layer.models.ProviderUnitGroup
import br.com.alice.data.layer.models.ProviderUnitGroupModel
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class ProviderUnitGroupModelConverterTest {
    private val providerUnitGroup = ProviderUnitGroup(
        id = UUID.randomUUID(),
        title = "Provider Unit Group Title",
        version = 1,
        cnpj = "12345678901234",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    private val providerUnitGroupModel = ProviderUnitGroupModel(
        id = providerUnitGroup.id,
        title = providerUnitGroup.title,
        version = providerUnitGroup.version,
        cnpj = providerUnitGroup.cnpj,
        createdAt = providerUnitGroup.createdAt,
        updatedAt = providerUnitGroup.updatedAt
    )

    @Test
    fun testToModel() {
        assertThat(providerUnitGroup.toModel())
            .isEqualTo(providerUnitGroupModel)
    }

    @Test
    fun testToTransport() {
        assertThat(providerUnitGroupModel.toTransport())
            .isEqualTo(providerUnitGroup)
    }
}
