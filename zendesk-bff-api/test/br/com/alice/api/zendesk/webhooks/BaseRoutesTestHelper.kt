package br.com.alice.api.zendesk.webhooks

import br.com.alice.api.zendesk.module
import br.com.alice.common.helpers.RoutesTestHelper
import io.ktor.server.application.Application
import io.mockk.clearAllMocks
import org.junit.jupiter.api.TestInstance
import org.koin.core.context.loadKoinModules
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class BaseRoutesTestHelper : RoutesTestHelper() {

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(module)
    }

    @BeforeTest
    override fun setup() {
        super.setup()
    }

    @AfterTest
    fun clear() = clearAllMocks()

}

