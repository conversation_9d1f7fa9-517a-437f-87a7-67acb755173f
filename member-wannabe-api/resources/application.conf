ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ br.com.alice.api.memberwannabe.ApplicationKt.module ]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    accreditedNetwork {
        defaultIconLocation = "https://s3.amazonaws.com/gas-dev2.assets.dev.alice.com.br/gas/accredited-establishments"
    }
    googleMaps {
        key = ""
    }
}

test {
    AWS_ACCESS_KEY_ID = "ACCESS_KEY_HERE"
    AWS_SECRET_ACCESS_KEY = "SECRET_ACESS_KEY"
    AWS_DEFAULT_REGION = "us-east-1"

    accreditedNetwork {
        defaultIconLocation = "https://s3.amazonaws.com/web.assets.staging.alice.com.br/gas/accredited-establishments"
    }
    googleMaps {
        key = "test_key"
    }
}

production {
    accreditedNetwork {
        defaultIconLocation = ""
        defaultIconLocation = ${?ACCREDITED_NETWORK_DEFAULT_ICON_LOCATION}
    }
    googleMaps {
        key = ""
        key = ${?GOOGLE_MAPS_API_KEY}
    }
}
