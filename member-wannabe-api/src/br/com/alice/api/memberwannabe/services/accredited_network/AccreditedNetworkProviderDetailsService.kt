package br.com.alice.api.memberwannabe.services.accredited_network

import br.com.alice.api.memberwannabe.ServiceConfig
import br.com.alice.api.memberwannabe.converters.AccreditedNetworkProviderResponseConverter
import br.com.alice.api.memberwannabe.models.AccreditedNetworkProviderResponse
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.coverage.client.AddressService
import br.com.alice.data.layer.models.Provider
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class AccreditedNetworkProviderDetailsService(
    private val providerUnitService: ProviderUnitService,
    private val providerService: ProviderService,
    private val addressService: AddressService
) {
    private val defaultIconLocation = ServiceConfig.AccreditedNetwork.defaultIconLocation

    suspend fun getProviderById(id: UUID) = coroutineScope {
        val provider = providerService.get(id).getOrNullIfNotFound()
            ?: return@coroutineScope getCassiDetail(id)

        getAliceDetail(provider)
    }

    private suspend fun getAliceDetail(provider: Provider) : AccreditedNetworkProviderResponse {
        val providerUnits =  providerUnitService.getByProviderIds(listOf(provider.id)).get()
        val providerUnitIds = providerUnits.map { it.id }

        logger.info(
            "ProviderDetailsService.getProviderById# Get Alice detail",
            "provider_id" to provider.id,
            "units" to providerUnitIds,
        )

        val addresses = addressService.findByReferencedModelIds(providerUnitIds).get()

        logger.info(
            "ProviderDetailsService.getProviderById# Addresses found addresses for provider units",
            "units" to providerUnitIds,
            "addresses" to addresses.map { it.id }
        )

        return AccreditedNetworkProviderResponseConverter.converter(provider, providerUnits, addresses, defaultIconLocation)
    }

    private suspend fun getCassiDetail(id: UUID): AccreditedNetworkProviderResponse {
        logger.info(
            "ProviderDetailsService.getProviderById# Get Cassi detail",
            "provider_unit_id" to id,
        )

        val providerUnit =  providerUnitService.get(id).get()

        logger.info(
            "ProviderDetailsService.getProviderById# Get Cassi detail for unit",
            "provider_unit_id" to providerUnit,
        )

        val addresses = addressService.findByReferencedModelIds(listOf(providerUnit.id)).get()

        logger.info(
            "ProviderDetailsService.getProviderById# Get Cassi address for unit",
            "provider_unit_id" to providerUnit,
            "address" to addresses.first().formattedAddress(),
        )

        return AccreditedNetworkProviderResponseConverter.cassiConverter(providerUnit, addresses, defaultIconLocation)
    }
}
