package br.com.alice.api.memberwannabe.converters

import br.com.alice.api.memberwannabe.models.AccreditedNetworkSpecialistResponse
import br.com.alice.data.layer.models.HealthCommunitySpecialist
import br.com.alice.data.layer.models.MedicalSpecialty
import java.util.UUID

object AccreditedNetworkSpecialistResponseConverter {
    fun converter(
        specialist: HealthCommunitySpecialist,
        specialties: List<MedicalSpecialty>,
        address: List<String>,
        defaultIconLocation: String,
    ): AccreditedNetworkSpecialistResponse {

        return AccreditedNetworkSpecialistResponse(
            name = specialist.name,
            specialty = getSpecialty(specialist.specialtyId, specialties)?.name,
            specialtyUrlSlug = getSpecialty(specialist.specialtyId, specialties)?.urlSlug,
            subSpecialties = buildSubSpecialties(specialist.subSpecialtyIds, specialties),
            addresses = address,
            email = specialist.email,
            phones = specialist.phones.map { it.phone },
            education = specialist.education,
            council = specialist.council.toString(),
            imageUrl = getImage(specialist.imageUrl, defaultIconLocation),
            curiosity = specialist.curiosity,
            appointmentTypes = specialist.appointmentTypes.map { it.description },
            qualifications = specialist.qualifications.map { it.description },
        )
    }

    private fun getImage(imageUrl: String?, defaultIconLocation: String): String {
        if (imageUrl === null) return "$defaultIconLocation/default-specialist-icon.png"
        if (imageUrl.contains("drive.google.com")) return "$defaultIconLocation/default-specialist-icon.png"
        return imageUrl
    }

    private fun getSpecialty(specialtyId: UUID?, specialties: List<MedicalSpecialty>): MedicalSpecialty? {
        return specialties.find { it.id == specialtyId }
    }

    private fun buildSubSpecialties(subSpecialtyIds: List<UUID>, specialties: List<MedicalSpecialty>): List<String> {
        val subSpecialties = subSpecialtyIds.map { subSpecialtyId -> getSpecialty(subSpecialtyId, specialties)?.name }
        return subSpecialties.filterNotNull()
    }
}
