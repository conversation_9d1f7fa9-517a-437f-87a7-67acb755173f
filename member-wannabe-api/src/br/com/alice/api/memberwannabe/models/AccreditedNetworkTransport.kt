package br.com.alice.api.memberwannabe.models

import br.com.alice.data.layer.models.ProviderUnit

data class AccreditedNetworkProductInfoResponse(
    val id: String,
    val title: String? = null,
    val ansCode: String? = null,
    val ansStatus: String? = null,
    val hospitalCount: Int = 0,
    val laboratoryCount: Int = 0,
    val specialistCount: Int = 0
)

data class AccreditedNetworkItemFilter(
    val title: String,
    val value: String,
)

data class AccreditedNetworkFilter(
    val items: List<AccreditedNetworkItemFilter>,
    val defaultValue: AccreditedNetworkItemFilter? = null,
)

enum class AccreditedNetworkEstablishmentTypeOptions(val label: String, val value: String) {
    HOSPITAL("Hospitais", "HOSPITAL"),
    LABORATORY("Laboratórios", "LABORATORY"),
    SPECIALIST("Especialistas", "SPECIALIST"),
}

enum class AccreditedNetworkHospitalCategories(val label: String, val value: String) {
    HOSPITAL("Hospital", ProviderUnit.Type.HOSPITAL.description),
    EMERGENCY_UNITY("Pronto-socorro", ProviderUnit.Type.EMERGENCY_UNITY.description),
    MATERNITY("Maternidade", ProviderUnit.Type.MATERNITY.description),
}

enum class AccreditedNetworkLaboratoryCategories(val label: String, val value: String) {
    LABORATORY("Laboratório", ProviderUnit.Type.LABORATORY.description),
    ALICE_HOUSE("Casa Alice", ProviderUnit.Type.ALICE_HOUSE.description),
}

enum class AccreditedNetworkServicesOptions(val label: String, val value: String) {
    HOSPITAL("Hospitais", "HOSPITAL"),
    LABORATORY("Laboratórios", "LABORATORY"),
    SPECIALIST("Especialistas", "SPECIALIST"),
}

enum class States {
    AC,
    AL,
    AP,
    AM,
    BA,
    CE,
    DF,
    ES,
    GO,
    MA,
    MT,
    MS,
    MG,
    PA,
    PB,
    PR,
    PE,
    PI,
    RJ,
    RN,
    RS,
    RO,
    RR,
    SC,
    SP,
    SE,
    TO,
}

data class AccreditedNetworkEstablishmentListResponse(
    val currentOffset: Int = 0,
    val limit: Int = 0,
    val itemCount: Int = 0,
    val items: List<AccreditedNetworkEstablishmentListItemResponse> = emptyList(),
)

data class AccreditedNetworkEstablishmentListItemResponse(
    val id: String,
    val title: String? = null,
    val icon: String? = null,
    val type: String,
    val categories: List<String>? = emptyList(),
)

data class AccreditedNetworkProviderResponse(
    val title: String? = null,
    val about: String? = null,
    val imageUrl: String? = null,
    val types: List<String> = emptyList(),
    val cnpj: String? = null,
    val units: List<AccreditedNetworkUnitResponse> = emptyList(),
)

data class AccreditedNetworkUnitResponse(
    val title: String,
    val address: String,
    val phones: List<String>,
    val type: String?
)

data class AccreditedNetworkSpecialistResponse(
    val name: String? = null,
    val specialty: String? = null,
    val specialtyUrlSlug: String? = null,
    val subSpecialties: List<String> = emptyList(),
    val addresses: List<String> = emptyList(),
    val email: String? = null,
    val phones: List<String> = emptyList(),
    val education: List<String> = emptyList(),
    val council: String? = null,
    val imageUrl: String? = null,
    val curiosity: String? = null,
    val qualifications: List<String>? = null,
    val appointmentTypes: List<String> = emptyList(),
)

data class AccreditedEstablishmentDetailsResponse(
    val establishmentType: String,
    val provider: AccreditedNetworkProviderResponse? = null,
    val specialist: AccreditedNetworkSpecialistResponse? = null
)
