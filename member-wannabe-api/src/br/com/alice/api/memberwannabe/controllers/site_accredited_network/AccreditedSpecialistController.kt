package br.com.alice.api.memberwannabe.controllers.site_accredited_network

import br.com.alice.api.memberwannabe.controllers.SiteController
import br.com.alice.atlas.services.AccreditedSpecialistService
import br.com.alice.common.foldResponse
import java.util.UUID

class AccreditedSpecialistController(
    private val accreditedSpecialistService: AccreditedSpecialistService
) : SiteController() {
    suspend fun getSpecialistDetails(referencedId: UUID) = withAliceWebsiteEnvironment {
        accreditedSpecialistService.getSpecialistDetails(referencedId).foldResponse()
    }
}
