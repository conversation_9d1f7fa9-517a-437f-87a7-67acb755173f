package br.com.alice.api.memberwannabe.builders

import br.com.alice.api.memberwannabe.functions.ProviderUnitUtils
import br.com.alice.api.memberwannabe.models.FinishedSimulationProvider
import br.com.alice.api.memberwannabe.models.FinishedSimulationResponse
import br.com.alice.api.memberwannabe.models.FinishedSimulationWithLeadResponse
import br.com.alice.api.memberwannabe.models.ProductRecommendationsResponse
import br.com.alice.api.memberwannabe.models.SimulationValidityResponse
import br.com.alice.bottini.buildProductRecommendation
import br.com.alice.bottini.models.FinishedSimulation
import br.com.alice.bottini.models.QualifiedResult
import br.com.alice.bottini.models.SimulationValidity
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.ProviderType
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

class FinishedSimulationResponseBuilderTest {
    @BeforeTest
    fun setup() {
        mockkObject(ProviderUnitUtils)
        every {
            ProviderUnitUtils.countUnitsPerPhysicalSpace(any())
        } returns 2
        every {
            ProviderUnitUtils.getUniqueUnitsPerPhysicalSpace(any())
        } returns listOf(TestModelFactory.buildProviderUnit())
    }

    @AfterTest
    fun after() = unmockkAll()

    @Test
    fun `#FinishedSimulationWithLeadResponse Should build finished simulation with lead if is in abVicNewProductSelectionModal test`() {
        val expiresAt = LocalDateTime.of(2019, 1, 1, 10, 10, 10)
        val hospital1 = TestModelFactory.buildProvider().copy(
            id = RangeUUID.generate(),
            name = "provider",
            type = ProviderType.HOSPITAL,
            icon = "icon",
            cnpj = "",
            phones = emptyList(),
            imageUrl = "",
            site = ""
        )
        val hospital2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.HOSPITAL)
        val laboratory1 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val laboratory2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val productRecommendations = listOf(
            TestModelFactory.buildProductRecommendation().copy(
                hospitals = listOf(hospital1, hospital2),
                laboratories = listOf(laboratory1, laboratory2),
                hospitalsCount = 2,
                laboratoriesCount = 3
            )
        )
        val lead = TestModelFactory.buildLead()
        val leadInformation = LeadInformationBuilder.build(lead, QualifiedResult(true, true))
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        val finishedSimulation = FinishedSimulation(
            simulationId = RangeUUID.generate(),
            simulatorVersion = HealthProductSimulationSimulatorVersion.V5,
            simulationType = HealthProductSimulationType.CONSUMER,
            simulationTypeHistory = listOf(
                HealthProductSimulationType.MICRO_COMPANY,
                HealthProductSimulationType.CONSUMER
            ),
            leadId = lead.id,
            productRecommendations = productRecommendations,
            validity = SimulationValidity(expiresAt = expiresAt, expired = true),
        )
        val expectedResult = FinishedSimulationWithLeadResponse(
            simulationId = finishedSimulation.simulationId,
            simulationType = finishedSimulation.simulationType,
            simulatorVersion = finishedSimulation.simulatorVersion,
            productRecommendations = ProductRecommendationsResponseBuilder.build(
                productRecommendations,
                providerUnits
            ),
            validity = SimulationValidityResponse(
                finishedSimulation.validity.expiresAt,
                finishedSimulation.validity.expired
            ),
            leadInformation = leadInformation,
        )

        val result =
            FinishedSimulationResponseBuilder.buildFinishedSimulationWithLead(
                finishedSimulation,
                leadInformation,
                providerUnits
            )

        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#FinishedSimulationResponse Should build finished simulation if is in abVicNewProductSelectionModal test`() {
        val expiresAt = LocalDateTime.of(2019, 1, 1, 10, 10, 10)
        val hospital1 = TestModelFactory.buildProvider().copy(
            id = RangeUUID.generate(),
            name = "provider",
            type = ProviderType.HOSPITAL,
            icon = "icon",
            cnpj = "",
            phones = emptyList(),
            imageUrl = "",
            site = ""
        )
        val hospital2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.HOSPITAL)
        val laboratory1 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val laboratory2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val productRecommendations = listOf(
            TestModelFactory.buildProductRecommendation().copy(
                hospitals = listOf(hospital1, hospital2),
                laboratories = listOf(laboratory1, laboratory2)
            )
        )
        val lead = TestModelFactory.buildLead()
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())
        val finishedSimulation = FinishedSimulation(
            simulationId = RangeUUID.generate(),
            simulatorVersion = HealthProductSimulationSimulatorVersion.V5,
            simulationType = HealthProductSimulationType.CONSUMER,
            simulationTypeHistory = listOf(
                HealthProductSimulationType.MICRO_COMPANY,
                HealthProductSimulationType.CONSUMER
            ),
            leadId = lead.id,
            productRecommendations = productRecommendations,
            validity = SimulationValidity(expiresAt = expiresAt, expired = true),
        )
        val expectedResult = FinishedSimulationResponse(
            simulationId = finishedSimulation.simulationId,
            simulationType = finishedSimulation.simulationType,
            simulatorVersion = finishedSimulation.simulatorVersion,
            productRecommendations = ProductRecommendationsResponseBuilder.build(
                productRecommendations,
                providerUnits
            ),
            validity = SimulationValidityResponse(
                finishedSimulation.validity.expiresAt,
                finishedSimulation.validity.expired
            ),
        )

        val result =
            FinishedSimulationResponseBuilder.buildFinishedSimulation(
                finishedSimulation,
                providerUnits
            )

        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#ProductRecommendationsResponse Should build product recommendations if is in abVicNewProductSelectionModal test`() {
        val hospital1 = TestModelFactory.buildProvider().copy(
            id = RangeUUID.generate(),
            name = "provider",
            type = ProviderType.HOSPITAL,
            icon = "icon",
            cnpj = "",
            phones = emptyList(),
            imageUrl = "",
            site = ""
        )
        val hospital2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.HOSPITAL)
        val laboratory1 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val laboratory2 = hospital1.copy(id = RangeUUID.generate(), type = ProviderType.LABORATORY)
        val productRecommendations = listOf(
            TestModelFactory.buildProductRecommendation().copy(
                hospitals = listOf(hospital1, hospital2),
                laboratories = listOf(laboratory1, laboratory2),
                hospitalsCount = 2,
                laboratoriesCount = 3
            )
        )
        val providerUnits = listOf(TestModelFactory.buildProviderUnit())

        val hospitals = listOf(hospital1, hospital2)
        val laboratories = listOf(laboratory1, laboratory2)

        val expectedResult = productRecommendations.map { productRecommendation ->
            ProductRecommendationsResponse(
                title = productRecommendation.title,
                name = productRecommendation.name,
                totalPrice = productRecommendation.totalPrice,
                pricesByAge = productRecommendation.pricesByAge,
                isSelected = productRecommendation.isSelected,
                hospitals = hospitals.map {
                    FinishedSimulationProvider(
                        id = it.id,
                        name = it.name,
                        type = it.type,
                        icon = it.icon,
                        providerUnitsCount = 2
                    )
                },
                laboratories = laboratories.map {
                    FinishedSimulationProvider(
                        id = it.id,
                        name = it.name,
                        type = it.type,
                        icon = it.icon,
                        providerUnitsCount = 2
                    )
                },
                anchor = productRecommendation.anchor,
                id = productRecommendation.id,
                accommodation = productRecommendation.accommodation,
                coPayment = productRecommendation.coPayment == CoPaymentType.FULL,
                bundles = productRecommendation.bundles,
                hospitalsCount = 2,
                laboratoriesCount = 3
            )
        }

        val result = ProductRecommendationsResponseBuilder.build(productRecommendations, providerUnits)

        assertThat(result).isEqualTo(expectedResult)
    }
}
