package br.com.alice.api.memberwannabe.api

import br.com.alice.api.memberwannabe.controllers.CacheController
import br.com.alice.api.memberwannabe.models.AccreditedWithLocationAndRemotesResponse
import br.com.alice.api.memberwannabe.models.CacheInvalidationResponse
import br.com.alice.api.memberwannabe.services.site_accredited_network.AccreditedService
import br.com.alice.bottini.client.HealthProductSimulationService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.redis.GenericCache
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class CacheControllerTest: RoutesTestHelper()  {
    private val cache: GenericCache = mockk()
    private val healthProductSimulationService: HealthProductSimulationService = mockk()
    private val accreditedService: AccreditedService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            CacheController(
                cache = cache,
                healthProductSimulationService = healthProductSimulationService,
                accreditedService = accreditedService,
            )
        }
    }

    @Test
    fun `#invalidateProductCache should invalidate product cache keys`() {
        val cacheResponse = CacheInvalidationResponse(
            setOf("product:1", "product:2")
        )

        coEvery {
            cache.invalidateAndReturnKeys("product:*")
        } returns mutableSetOf("product:1")

        coEvery {
            healthProductSimulationService.invalidateProductCache()
        } returns mutableSetOf("product:2").success()

        delete("/cache/product") { response ->
            ResponseAssert.assertThat(response).isOKWithData(cacheResponse)
        }
    }

    @Test
    fun `#invalidateAccreditedCache should invalidate accredited cache keys`() {
        val cacheResponse = CacheInvalidationResponse(
            setOf("accredited:1")
        )

        coEvery {
            cache.invalidateAndReturnKeys("accredited:*")
        } returns mutableSetOf("accredited:1")

        coEvery {
            accreditedService.findBy(
                radiusInMeters = 5000,
                isDefaultSearch = true,
                lat = "-23.566063104728556",
                lon = "-46.65711918252563",
                specialtyId = null,
                categories = null,
                siteAccreditedNetworkId = null,
                placeId = null,
            )
        } returns AccreditedWithLocationAndRemotesResponse(
            items = emptyList(),
            defaultItems = emptyList(),
        ).success()

        delete("/cache/accredited") { response ->
            ResponseAssert.assertThat(response).isOKWithData(cacheResponse)
        }
    }
}
