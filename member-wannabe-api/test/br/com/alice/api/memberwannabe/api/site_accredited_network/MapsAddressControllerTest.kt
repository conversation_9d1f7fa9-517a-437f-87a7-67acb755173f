package br.com.alice.api.memberwannabe.api.site_accredited_network

import br.com.alice.api.memberwannabe.controllers.site_accredited_network.MapsAddressController
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.googlemaps.services.GoogleMapsService
import br.com.alice.api.memberwannabe.api.RoutesTestHelper
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.api.memberwannabe.models.site_accredited_network.SiteAccreditedNetworkItemFilter
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.googlemaps.models.GoogleTypes
import br.com.alice.common.googlemaps.services.GoogleMapsHttpService
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.util.*
import kotlin.test.BeforeTest

class MapsAddressControllerTest : RoutesTestHelper() {
    private val mapsService: GoogleMapsService = mockk()
    private val mapsHttpService: GoogleMapsHttpService = mockk()
    private val session = "session".toSafeUUID()
    private val placeId = "place_id"
    private val autocomplete = AutocompleteTransport(
        placeId = placeId,
        description = "Av. Rebouças, 3506 - Pinheiros, São Paulo - SP, 05402-600, Brasil",
        mainText = "Av. Rebouças, 3506",
        secondaryText = "Pinheiros, São Paulo - SP, 05402-600, Brasil"
    )
    private val lat = -23.5718116
    private val lng = -46.69273700000001
    private val addressMapsTransport = Address(
        id = "place_id",
        street = "Avenida Rebouças",
        number = "3506",
        neighbourhood = "Pinheiros",
        postalCode = "05402-600",
        city = "São Paulo",
        state = "SP",
        country = "place_id",
        lat = lat,
        lng = lng,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { MapsAddressController(mapsService, mapsHttpService) }
    }

    @Test
    fun `autocompleteAddress should return autocomplete response`() {
        val query = "av briga"
        coEvery { mapsService.autocompleteByText(query, session) } returns listOf(autocomplete).success()

        val headers = mapOf("Session-Id" to session.toString())
        get("/site_accredited_network/maps/address/search?q=av%20briga", headers) { response ->
            ResponseAssert.assertThat(response).isSuccessfulJson()
        }

        coVerifyOnce { mapsService.autocompleteByText(query, session) }
    }

    @Test
    fun `autocompleteAddress should return http autocomplete response when feature flag is active`() = runBlocking {
        val query = "av briga"
        val types = listOf(
            GoogleTypes.POINT_OF_INTEREST,
            GoogleTypes.ROUTE,
            GoogleTypes.LOCALITY,
            GoogleTypes.SUBLOCALITY,
            GoogleTypes.POSTAL_CODE
        )
        coEvery { mapsHttpService.autocompleteByText(query, types, session) } returns listOf(autocomplete).success()
        val headers = mapOf("Session-Id" to session.toString())

        withFeatureFlag(FeatureNamespace.MEMBER_WANNABE, "search_without_country_and_state_on_accredited_network", true) {
            get("/site_accredited_network/maps/address/search?q=av%20briga", headers) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { mapsHttpService.autocompleteByText(query, types, session) }
    }

    @Test
    fun `autocompleteAddress should handle http api not found`() = runBlocking {
        val query = "av briga"
        val types = listOf(
            GoogleTypes.POINT_OF_INTEREST,
            GoogleTypes.ROUTE,
            GoogleTypes.LOCALITY,
            GoogleTypes.SUBLOCALITY,
            GoogleTypes.POSTAL_CODE
        )
        coEvery { mapsHttpService.autocompleteByText(query, types, session) }  returns NotFoundException().failure()
        val headers = mapOf("Session-Id" to session.toString())

        withFeatureFlag(FeatureNamespace.MEMBER_WANNABE, "search_without_country_and_state_on_accredited_network", true) {
            get("/site_accredited_network/maps/address/search?q=av%20briga", headers) { response ->
                ResponseAssert.assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { mapsHttpService.autocompleteByText(query, types, session) }
    }

    @Test
    fun `autocompleteAddress should return bad request when missing parameters`() {
        get("/site_accredited_network/maps/address/search") { response ->
            ResponseAssert.assertThat(response).isBadRequest()
        }

        coVerifyNone { mapsService.autocompleteByText(any(), any()) }
    }

    @Test
    fun `getByPlaceId should return address by place id`() {
        coEvery { mapsService.getAddressById(placeId) } returns addressMapsTransport.success()

        get("/site_accredited_network/maps/address/$placeId") { response ->
            ResponseAssert.assertThat(response).isOKWithData(addressMapsTransport)
        }

        coVerifyOnce { mapsService.getAddressById(any()) }
    }
}
