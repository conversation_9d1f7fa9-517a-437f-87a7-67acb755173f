package br.com.alice.api.memberwannabe.converters

import br.com.alice.api.memberwannabe.models.AccreditedLightweight
import br.com.alice.atlas.model.Accredited
import br.com.alice.atlas.model.AccreditedCategory
import br.com.alice.common.RangeUUID
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedLightweightConverterTest {

    @Test
    fun `#fromAccredited should convert Accredited to AccreditedLightweight correctly`() = runBlocking {
        val referencedId = RangeUUID.generate()
        val accredited = Accredited(
            id = RangeUUID.generate(),
            name = "Hospital",
            category = AccreditedCategory.HOSPITAL,
            referencedId = referencedId,
        )

        val accreditedLightweight = AccreditedLightweightConverter.fromAccredited(accredited)

        assertEquals(
            AccreditedLightweight(
                lat = null,
                lon = null,
                referencedId = referencedId,
                icon = null,
                category = AccreditedCategory.HOSPITAL
            ),
        accreditedLightweight)
    }
}
