package br.com.alice.api.memberwannabe.converters

import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionOptionResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationQuestionResponse
import br.com.alice.api.memberwannabe.models.HealthProductSimulationResponse
import br.com.alice.api.memberwannabe.models.SimulationAnswerResponse
import br.com.alice.bottini.models.HealthProductSimulationAnswerResponse
import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.bottini.models.HealthProductSimulationQuestionOption
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SimulationAnswerResponseConverterTest {

    @Test
    fun `#convert should convert and return as expected`() {
        val simulationId = RangeUUID.generate()
        val simulation = TestModelFactory.buildHealthProductSimulation(
            id = simulationId,
            answers = listOf(
                HealthProductSimulationAnswer("28", HealthProductSimulationQuestionType.HAS_CNPJ)
            ),
            type = HealthProductSimulationType.MICRO_COMPANY
        )
        val question = HealthProductSimulationQuestion(
            type = HealthProductSimulationQuestionType.PEOPLE_NUMBER,
            options = listOf(
                HealthProductSimulationQuestionOption(text = "Entre 6 e 29 pessoas", value = "6-29"),
                HealthProductSimulationQuestionOption(text = "Entre 30 e 99 pessoas", value = "30-99"),
                HealthProductSimulationQuestionOption(text = "Entre 100 e 499 pessoas", value = "100-499"),
                HealthProductSimulationQuestionOption(text = "Mais de 500 pessoas", value = "500+")
            )
        )
        val answerResponse = HealthProductSimulationAnswerResponse(
            simulation = simulation,
            question = question
        )
        val expected = SimulationAnswerResponse(
            simulation = HealthProductSimulationResponse(
                id = simulationId,
                answers = listOf(
                    HealthProductSimulationAnswer("28", HealthProductSimulationQuestionType.HAS_CNPJ)
                ),
                simulationType = HealthProductSimulationType.MICRO_COMPANY,
                progressPercent = 0
            ),
            question = HealthProductSimulationQuestionResponse(
                type = HealthProductSimulationQuestionType.PEOPLE_NUMBER,
                options = listOf(
                    HealthProductSimulationQuestionOptionResponse(text = "Entre 6 e 29 pessoas", value = "6-29"),
                    HealthProductSimulationQuestionOptionResponse(text = "Entre 30 e 99 pessoas", value = "30-99"),
                    HealthProductSimulationQuestionOptionResponse(text = "Entre 100 e 499 pessoas", value = "100-499"),
                    HealthProductSimulationQuestionOptionResponse(text = "Mais de 500 pessoas", value = "500+")
                ),
            )
        )

        val actual = SimulationAnswerResponseConverter.convert(answerResponse)
        assertThat(actual).isEqualTo(expected)
    }
}
