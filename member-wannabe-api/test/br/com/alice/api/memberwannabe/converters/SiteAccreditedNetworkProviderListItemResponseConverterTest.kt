package br.com.alice.api.memberwannabe.converters

import br.com.alice.api.memberwannabe.converters.site_accredited_network.SiteAccreditedNetworkProviderListItemResponseConverter
import br.com.alice.api.memberwannabe.models.site_accredited_network.SiteAccreditedNetworkProviderListItemResponse
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderAddress
import br.com.alice.data.layer.models.SiteAccreditedNetworkProviderType
import org.assertj.core.api.Assertions
import kotlin.test.Test

class SiteAccreditedNetworkProviderListItemResponseConverterTest {
    private val defaultIconLocation = "https://s3.amazonaws.com/web.assets.staging.alice.com.br/gas/accredited-establishments"

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return as expected (laboratory)`() {
        val address = ProviderAddress(
            addressId = RangeUUID.generate(),
            city = "São Paulo",
            state = "SP",
            neighborhood = "Vila Mariana",
            formattedAddress = "Vila Mariana, São Paulo - SP"
        )
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.LABORATORY,
            addresses = listOf(address),
        )
        val categories = listOf("Laboratórios Parceiros")

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "${defaultIconLocation}/default-laboratory-icon.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = "Vila Mariana, São Paulo - SP",
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return as expected (hospital)`() {
        val address = ProviderAddress(
            addressId = RangeUUID.generate(),
            city = "São Paulo",
            state = "SP",
            neighborhood = "Vila Mariana",
            formattedAddress = "Vila Mariana, São Paulo - SP"
        )
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.HOSPITAL,
            addresses = listOf(address),
        )
        val categories = listOf("Hospital Infantil", "Pronto Socorro Infantil")

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "${defaultIconLocation}/default-hospital-icon.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = "Vila Mariana, São Paulo - SP",
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return as expected (specialist)`() {
        val address = ProviderAddress(
            addressId = RangeUUID.generate(),
            city = "São Paulo",
            state = "SP",
            neighborhood = "Vila Mariana",
            formattedAddress = "Vila Mariana, São Paulo - SP"
        )
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.SPECIALIST,
            addresses = listOf(address),
        )
        val categories = listOf(RangeUUID.generate().toString())

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "${defaultIconLocation}/default-specialist-icon.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = "Vila Mariana, São Paulo - SP",
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return as expected when there is no address`() {
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.LABORATORY,
            addresses = emptyList(),
        )
        val categories = listOf("Laboratórios Parceiros")

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "${defaultIconLocation}/default-laboratory-icon.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = null,
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return the default icon when image url contains drive`() {
        val address = ProviderAddress(
            addressId = RangeUUID.generate(),
            city = "São Paulo",
            state = "SP",
            neighborhood = "Vila Mariana",
            formattedAddress = "Vila Mariana, São Paulo - SP"
        )
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.HOSPITAL,
            addresses = listOf(address),
        ).copy(icon = "https://drive.google.com/any-image.png")
        val categories = listOf("Hospital Infantil", "Pronto Socorro Infantil")

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "${defaultIconLocation}/default-hospital-icon.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = "Vila Mariana, São Paulo - SP",
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#convertFromSiteAccreditedNetworkProviderList should convert and return the image url when it is valid`() {
        val address = ProviderAddress(
            addressId = RangeUUID.generate(),
            city = "São Paulo",
            state = "SP",
            neighborhood = "Vila Mariana",
            formattedAddress = "Vila Mariana, São Paulo - SP"
        )
        val siteAccreditedNetworkProvider = TestModelFactory.buildSiteAccreditedNetworkProvider(
            type = SiteAccreditedNetworkProviderType.HOSPITAL,
            addresses = listOf(address),
        ).copy(icon = "https://s3.amazonaws.com/web.assets.staging.alice.com.br/gas/accredited-establishments/any-image.png")

        val categories = listOf("Hospital Infantil", "Pronto Socorro Infantil")

        val expectedResult = SiteAccreditedNetworkProviderListItemResponse(
            id = siteAccreditedNetworkProvider.referencedModelId.toString(),
            title = siteAccreditedNetworkProvider.name,
            icon = "https://s3.amazonaws.com/web.assets.staging.alice.com.br/gas/accredited-establishments/any-image.png",
            type = siteAccreditedNetworkProvider.type.toString(),
            formattedAddress = "Vila Mariana, São Paulo - SP",
            categories = categories,
        )

        val result = SiteAccreditedNetworkProviderListItemResponseConverter.convertFromSiteAccreditedNetworkProviderList(
            siteAccreditedNetworkProvider,
            defaultIconLocation,
            categories,
        )

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }
}
