plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.member-wannabe-api"
version = aliceMemberWannabeApiVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:member-wannabe-api")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-service"))
    implementation(project(":common-redis"))
    implementation(project(":common-google-maps"))
    implementation(project(":communication"))
    implementation(project(":data-layer-client"))
    implementation(project(":data-packages:atlas-domain-service-data-package"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:membership-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:provider-domain-service-data-package"))
	implementation(project(":data-packages:bottini-domain-service-data-package"))
	implementation(project(":data-packages:coverage-domain-service-data-package"))
	implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":atlas-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":bottini-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":ehr-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":coverage-domain-client"))
    implementation(project(":product-domain-client"))

    implementation("io.ktor:ktor-server-cors:$ktor2Version")
    implementation("io.ktor:ktor-server-swagger:$ktor2Version")
    implementation(project(mapOf("path" to ":atlas-domain-client")))
    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
