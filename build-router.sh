set -euxo pipefail

output_old="folders_changed_old.log"
output_new="folders_changed.log"
output_diff="folders_changed_diff.log"
output_found="found_files.log"
build_file="build.gradle.kts"

touch "$output_old"
touch "$output_found"
touch "$output_diff"
git diff --name-only HEAD~ | { grep -v "^serverless/"  || true; } | { grep "/" || true; } | cut -d"/" -f1 | sort -u > "$output_new"

same_file() {
    diff "$output_old" "$output_new" > /dev/null
}

module_diff() {
    rm "$output_diff"
    diff "$output_old" "$output_new" | grep '>' | cut -d " " -f2 > "$output_diff" || echo "Some diff"
}

until same_file; do
    module_diff
    cat "$output_new" > "$output_old"

    while read -r module; do
        grep "\":$module\"" */$build_file | cut -d '/' -f1 >> "$output_found" || echo "$module not found"
    done < "$output_diff"

    cat "$output_old" "$output_found" | sort -u  > "$output_new"
done;

rm "$output_found"
rm "$output_old"
rm "$output_diff"

git diff --name-only HEAD~ | { grep "^serverless/" || true; } | cut -d"/" -f1,2 | sort -u >> "$output_new" || echo "No serverless changes"

while read -r project
do
  git branch -D deploy/current/$project || echo branch deploy/current/$project does not exist
  git checkout master
  git checkout -b "deploy/current/$project"
  git push -f origin "deploy/current/$project:deploy/current/$project"
done < "$output_new"
