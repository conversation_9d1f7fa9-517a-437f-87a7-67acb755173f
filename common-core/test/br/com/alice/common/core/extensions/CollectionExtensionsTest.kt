package br.com.alice.common.core.extensions

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class CollectionExtensionsTest {

    @Test
    fun `#replace should replace all matched elements`() {
        val list = listOf(1, 2, 3, 1)
        val actual = list.replace(4) { it == 1 }
        val expected = listOf(4, 2, 3, 4)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#replace should not replace when no elements match`() {
        val list = listOf(1, 2, 3, 1)
        val actual = list.replace(4) { it == 5 }

        assertThat(actual).isEqualTo(list)
    }

    @Test
    fun `#replace should do nothing on empty list`() {
        val list = emptyList<Int>()
        val actual = list.replace(4) { it == 1 }
        val expected = emptyList<Int>()

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#ifNullOrEmpty should return default value when null`() {
        val default = "default"
        val list: List<String>? = null

        val actual = list.ifNullOrEmpty { default }
        val expected = listOf(default)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#ifNullOrEmpty should return default value when empty`() {
        val default = "default"
        val list = emptyList<String>()

        val actual = list.ifNullOrEmpty { default }
        val expected = listOf(default)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#ifNullOrEmpty should return list when not null nor empty`() {
        val default = "default"
        val list = listOf("value")

        val actual = list.ifNullOrEmpty { default }
        val expected = list

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#isSameAs should return true when two lists has the same elements but in different order`() {
        val list = listOf(1, 2, 3)
        val otherList = listOf(3, 2, 1)

        val actual = list.isSameAs(otherList)
        assertThat(actual).isTrue()
    }

    @Test
    fun `#isSameAs should return false when two lists have the same count but not the same elements`() {
        val list = listOf(1, 2, 3)
        val otherList = listOf(3, 2, 0)

        val actual = list.isSameAs(otherList)
        assertThat(actual).isFalse()
    }

    @Test
    fun `#isSameAs should return false when the first list have one element more`() {
        val list = listOf(1, 2, 3, 4)
        val otherList = listOf(1, 2, 3)

        val actual = list.isSameAs(otherList)
        assertThat(actual).isFalse()
    }

    @Test
    fun `#isSameAs should return false when the second list have one element more`() {
        val list = listOf(1, 2, 3)
        val otherList = listOf(1, 2, 3, 4)

        val actual = list.isSameAs(otherList)
        assertThat(actual).isFalse()
    }

    @Test
    fun `#isSameAs should return false when the case are different`() {
        val list = listOf("a", "b", "c")
        val otherList = listOf("A", "B", "C")

        val actual = list.isSameAs(otherList)
        assertThat(actual).isFalse()
    }

    @Test
    fun `#isSameAs should return true when the case are different by transformed`() {
        val list = listOf("a", "b", "c")
        val otherList = listOf("A", "B", "C")

        val actual = list.isSameAs(otherList) { it.lowercase() }
        assertThat(actual).isTrue()
    }

    @Test
    fun `#joinToStringOrNull - collection is not empty`() {
        val result = listOf("value1", "value2").joinToStringOrNull(" - ")
        assertThat(result).isEqualTo("value1 - value2")
    }

    @Test
    fun `#joinToStringOrNull - collection is empty`() {
        val result = emptyList<String>().joinToStringOrNull(" - ")
        assertThat(result).isEqualTo(null)
    }

    @Test
    fun `#differenceFromList - collection is empty`() {
        val list = emptyList<String>()
        val result = list.differenceFromList(emptyList())
        assertThat(result).isEqualTo(list)
    }

    @Test
    fun `#differenceFromList - Collection is not empty `() {
        val listA = listOf("value1", "value2", "value3")
        val listB = listOf("value1", "value2")
        val result = listA.differenceFromList(listB)
        assertThat(result).isEqualTo(listOf("value3"))
    }

    @Test
    fun `#differenceFromList - Collection A with less items `() {
        val listA = listOf("value1")
        val listB = listOf("value1", "value2")
        val result = listA.differenceFromList(listB)
        assertThat(result).isEqualTo(emptyList<String>())
    }

    @Test
    fun `#differenceFromList - Operation with no effect `() {
        val listA = listOf("value3")
        val listB = listOf("value1", "value2")
        val result = listA.differenceFromList(listB)
        assertThat(result).isEqualTo(listOf("value3"))
    }

    @Test
    fun `#isNotNullOrEmpty - should return right if list is not null or empty`() {
        val listA = listOf("value3")
        val listB = listOf<String>()
        val listC: List<String>? = null

        assertThat(listA.isNotNullOrEmpty()).isTrue
        assertThat(listB.isNotNullOrEmpty()).isFalse
        assertThat(listC.isNotNullOrEmpty()).isFalse
    }

}
