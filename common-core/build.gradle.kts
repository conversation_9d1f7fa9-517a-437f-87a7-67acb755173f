plugins {
    kotlin
    `kotlin-kapt`
    id("org.sonarqube")
}

group = "br.com.alice.alice-common-core"
version = aliceCommonVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

sonarqube {
    properties {
        property("sonar.projectKey", "mono:common-core")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

publishing {
    publications {
        create<MavenPublication>("nexus") {
            from(components["java"])
        }
    }

    repositories {
        maven {
            name = "nexus"
            url = uri("https://nexus.devtools.alice.tools/repository/releases/")
            credentials {
                username = System.getenv()["NEXUS_USER"] ?: ""
                password = System.getenv()["NEXUS_PASSWORD"] ?: ""
            }
        }
    }
}

dependencies {
    ktor2Dependencies()
    test2Dependencies()
    implementation("com.fasterxml.uuid:java-uuid-generator:5.1.0")
}

