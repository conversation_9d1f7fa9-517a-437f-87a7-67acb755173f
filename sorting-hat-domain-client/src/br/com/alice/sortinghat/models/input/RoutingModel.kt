package br.com.alice.sortinghat.models.input

import br.com.alice.common.core.PersonId
import br.com.alice.sortinghat.models.RoutingTypify
import br.com.alice.sortinghat.models.Serializable

interface RoutingModel : RoutingTypify, Serializable<RoutingModel> {
    val id: String
    val personId: PersonId

    companion object {
        fun deserialize(source: String): RoutingModel = Serializable.deserialize(source)
    }
}
