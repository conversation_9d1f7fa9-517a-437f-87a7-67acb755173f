package br.com.alice.sortinghat.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.sortinghat.SortingHatDomainClientConfiguration
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.client.PersonTeamAssociationServiceClient
import br.com.alice.sortinghat.client.RoutingClient
import br.com.alice.sortinghat.client.RoutingHistoryService
import br.com.alice.sortinghat.client.RoutingHistoryServiceClient
import br.com.alice.sortinghat.client.RoutingRuleService
import br.com.alice.sortinghat.client.RoutingRuleServiceClient
import br.com.alice.sortinghat.client.RoutingService
import br.com.alice.sortinghat.client.RoutingServiceClient
import org.koin.dsl.module

val SortingHatDomainClientModule = module(createdAtStart = true) {

    val baseUrl = SortingHatDomainClientConfiguration.baseUrl()
    val invoker = HttpInvoker(DefaultHttpClient(), "$baseUrl/rfc")

    single<PersonTeamAssociationService> { PersonTeamAssociationServiceClient(invoker) }
    single<RoutingRuleService> { RoutingRuleServiceClient(invoker) }
    single<RoutingHistoryService> { RoutingHistoryServiceClient(invoker) }

    // don't expose this service
    val routingService: RoutingService = RoutingServiceClient(invoker)

    // for routing use this client
    single { RoutingClient(routingService) }
}
