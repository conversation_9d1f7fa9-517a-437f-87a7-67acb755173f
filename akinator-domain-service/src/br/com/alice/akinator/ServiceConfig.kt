package br.com.alice.akinator

import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(config = HoconApplicationConfig(ConfigFactory.load("application.conf"))) {
    val chatGptCredentials = ChatGptCredentials(
        baseUrl = config("openai.baseUrl"),
        basicAuthToken = config("openai.basicAuthToken")
    )
    val edenAIConfig = EdenAIConfig(
        baseUrl = config("edenai.baseUrl"),
        apiKey = config("edenai.apiKey")
    )
    val chatGptVersion = config("openai.modelVersion")
    val appointmentSummaryGptModelVersion = config("openai.appointmentSummaryModelVersion")

    fun getEnv(name: String?) : String? = System.getenv(name)
}

data class ChatGptCredentials(
    val basicAuthToken: String,
    val baseUrl: String,
)

data class EdenAIConfig(
    val baseUrl: String,
    val apiKey: String,
)
