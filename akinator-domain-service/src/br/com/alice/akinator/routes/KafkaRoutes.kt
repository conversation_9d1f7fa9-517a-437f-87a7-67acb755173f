package br.com.alice.akinator.routes

import br.com.alice.akinator.consumers.AsyncAIInferenceRequestConsumer
import br.com.alice.akinator.consumers.AsyncAssistantRequestConsumer
import br.com.alice.akinator.consumers.InferenceCreatedConsumer
import br.com.alice.akinator.events.AITextInferenceCreatedEvent
import br.com.alice.akinator.events.AsyncAIInferenceRequestEvent
import br.com.alice.akinator.events.AsyncAssistantRequestEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob

fun ConsumerJob.Configuration.kafkaRoutes() {

    val inferenceCreatedConsumer by inject<InferenceCreatedConsumer>()
    consume("akinator-inference-created", AITextInferenceCreatedEvent.name, inferenceCreatedConsumer::process)

    val asyncAssistantRequestConsumer by inject<AsyncAssistantRequestConsumer>()
    consume("process-async-assistant-request", AsyncAssistantRequestEvent.NAME, asyncAssistantRequestConsumer::process)

    val asyncAIInferenceRequestConsumer by inject<AsyncAIInferenceRequestConsumer>()
    consume("process-async-ai-inference-request", AsyncAIInferenceRequestEvent.NAME, asyncAIInferenceRequestConsumer::process)
}
