package br.com.alice.akinator.services

import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.AIModel
import br.com.alice.data.layer.models.AIOperationType
import br.com.alice.data.layer.services.AIModelDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import java.util.UUID

class AIModelService(
    private val dataService: AIModelDataService
) {

    suspend fun getAIModelsByOperation(operationType: AIOperationType, interfaceHash: String? = null) = dataService.find {
        where {
            val pred = this.operationType.eq(operationType)
            if (interfaceHash != null) {
                pred and this.interfaceHash.eq(interfaceHash)
            } else {
                pred
            }
        }
    }

    suspend fun getAIModel(operationType: AIOperationType, provider: String, name: String) = dataService.findOneOrNull {
        where {
            this.operationType.eq(operationType) and
                    this.provider.eq(provider) and
                    this.name.eq(name)
        }
    }

    suspend fun getAIModelById(id: UUID) = dataService.get(id)

    suspend fun addModel(model: AIModel): Result<AIModel, Throwable> {
        this.getAIModel(model.operationType, model.provider, model.name)?.let {
            return IllegalArgumentException("Model with operationType ${model.operationType}, provider ${model.provider} and name ${model.name} already exists").failure()
        }
        return dataService.add(model)
    }

    suspend fun updateModel(model: AIModel): Result<AIModel, Throwable> {
        this.getAIModel(model.operationType, model.provider, model.name)?.let {
            if (it.id != model.id) {
                return IllegalArgumentException("Model with operationType ${model.operationType}, provider ${model.provider} and name ${model.name} already exists").failure()
            }
        }
        return dataService.update(model)
    }

    suspend fun deleteModel(model: AIModel) = dataService.softDelete(model)
}
