package br.com.alice.akinator.services

import br.com.alice.akinator.client.AsyncAIInferenceInternalRequest
import br.com.alice.akinator.client.AsyncAIInferenceService
import br.com.alice.akinator.client.AsyncAIInferenceServiceClient
import br.com.alice.akinator.client.AsyncAITextInferenceRequest
import br.com.alice.akinator.client.transfer.AIInferenceRequestInternalDTO
import br.com.alice.akinator.client.transfer.AIInferenceRequestTextDTO
import br.com.alice.akinator.events.AsyncAIInferenceRequestEvent
import br.com.alice.akinator.events.AsyncInferenceRequestAction.INTERNAL_INFERENCE
import br.com.alice.akinator.events.AsyncInferenceRequestAction.TEXT_INFERENCE
import br.com.alice.akinator.module
import br.com.alice.authentication.TestFactory
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.rfc.ServiceServer
import br.com.alice.common.rfc.TestInvoker
import br.com.alice.common.serialization.gson
import br.com.alice.common.withUnauthenticatedToken
import br.com.alice.featureconfig.core.FeaturePopulateService
import io.ktor.server.testing.TestApplicationEngine
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import kotlin.test.AfterTest
import kotlin.test.Test

class AsyncAIInferenceServiceImplRFCTest {

    companion object {
        private val featurePopulateService: FeaturePopulateService = mockk()
        var testEngine: TestApplicationEngine = TestApplicationEngine()
        val kafkaProducerService: KafkaProducerService = mockk()

        @AfterAll
        @JvmStatic
        fun tearDown() {
            testEngine.stop(0, 0)
        }

        @BeforeAll
        @JvmStatic
        fun init() {
            testEngine = TestApplicationEngine()
            val applicationEngine = testEngine.start()

            val modules = listOf(
                org.koin.dsl.module(createdAtStart = true) {
                    single { featurePopulateService }
                    single<KafkaProducerService> { kafkaProducerService }
                    single<AsyncAIInferenceService> { AsyncAIInferenceServiceImpl(get()) }
                    single<ServiceServer> { AsyncAIInferenceServiceServer(get()) }
                }
            )

            applicationEngine.application.module(modules)
        }
    }

    private val httpInvoker = TestInvoker(testEngine, "rfc")
    private val aiInferenceServiceClient = AsyncAIInferenceServiceClient(httpInvoker)

    private val setupId = RangeUUID.generate()

    @AfterTest
    fun confirmMocks() {
        confirmVerified(kafkaProducerService)
        clearAllMocks()
    }

    @Test
    fun `#text2TextInference returns Unit and send event successfully`() = runBlocking {
        val request = AsyncAITextInferenceRequest(
            id = "id",
            responseTopic = "responseTopic",
            setupId = setupId,
            request = AIInferenceRequestTextDTO(
                context = "context",
                caller = "caller"
            ),
        )

        coEvery {
            kafkaProducerService.produce(
                event = AsyncAIInferenceRequestEvent(TEXT_INFERENCE, gson.toJson(request)),
                partitionKey = request.id
            )
        } returns mockk()

        withUnauthenticatedToken(TestFactory.environmentToken) {
            val result = aiInferenceServiceClient.text2TextInference(request)
            assertThat(result).isSuccessOfType(Unit::class)
        }

        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#dalyaInference returns Unit and send event successfully`() = runBlocking {
        val request = AsyncAIInferenceInternalRequest(
            id = "id",
            responseTopic = "responseTopic",
            setupId = setupId,
            request = AIInferenceRequestInternalDTO(
                context = gson.toJsonTree(mapOf("data" to true)).asJsonObject,
                caller = "caller"
            )
        )

        coEvery {
            kafkaProducerService.produce(
                event = AsyncAIInferenceRequestEvent(INTERNAL_INFERENCE, gson.toJson(request)),
                partitionKey = request.id
            )
        } returns mockk()

        withUnauthenticatedToken(TestFactory.environmentToken) {
            val result = aiInferenceServiceClient.dalyaInference(request)
            assertThat(result).isSuccessOfType(Unit::class)
        }

        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

}
