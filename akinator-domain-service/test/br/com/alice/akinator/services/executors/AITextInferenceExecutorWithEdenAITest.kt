package br.com.alice.akinator.services.executors

import br.com.alice.akinator.ServiceConfig
import br.com.alice.akinator.client.transfer.AIInferenceRequestDTO
import br.com.alice.akinator.client.transfer.AIInferenceRequestTextDTO
import br.com.alice.akinator.clients.EdenAIChatClient
import br.com.alice.akinator.clients.model.EdenAIChatRequest
import br.com.alice.akinator.clients.model.EdenAIChatResponse
import br.com.alice.akinator.clients.model.EdenAIClientException
import br.com.alice.akinator.clients.model.EdenAIError
import br.com.alice.akinator.clients.model.EdenAIErrorResponse
import br.com.alice.akinator.clients.model.EdenAIResponseStatus
import br.com.alice.akinator.clients.model.ErrorClassification
import br.com.alice.akinator.clients.model.ProviderOutput
import br.com.alice.akinator.services.AIModelService
import br.com.alice.akinator.services.executors.strategies.EdenAIChatStrategy
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AIInferenceResult
import br.com.alice.data.layer.models.AIModelInstance
import br.com.alice.data.layer.models.AITextInference
import br.com.alice.data.layer.models.AITextInferenceOutput
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.koin.java.KoinJavaComponent
import org.koin.java.KoinJavaComponent.getKoin
import kotlin.test.BeforeTest
import kotlin.test.assertEquals

class AITextInferenceExecutorWithEdenAITest {

    private val edenAIChatClient: EdenAIChatClient = mockk()
    private val edenAIChatStrategy = EdenAIChatStrategy(edenAIChatClient)

    private val aiModelService: AIModelService = mockk()
    private val personService: PersonService = mockk()
    private val aiTextInferenceExecutor = AITextInferenceExecutor(aiModelService, personService)

    private val testModel1 = TestModelFactory.buildAIModel(provider = "edenai", name = "openai/model1")
    private val testModel2 = TestModelFactory.buildAIModel(provider = "edenai", name = "openai/model2")
    private val testModel3 = TestModelFactory.buildAIModel(provider = "edenai", name = "openai/model3")
    private val testSetup = TestModelFactory.buildAISetup(prompt = "What is the user talking about?",
        models = listOf(
            AIModelInstance(testModel1.provider, testModel1.name),
            AIModelInstance(testModel2.provider, testModel2.name),
            AIModelInstance(testModel3.provider, testModel3.name)
        )
    )
    private val requestDTO = AIInferenceRequestTextDTO("Flying pigs on a sunny day", "CLIENT-DOMAIN-SERVICE")
    private val context = requestDTO.context

    @BeforeTest
    fun setup() {
        mockkStatic(KoinJavaComponent::class)
        coEvery { getKoin().get<EdenAIChatStrategy>() } returns edenAIChatStrategy
    }

    @Test
    fun `#edenAIChatStrategy_createRequest should create an expected request`() = runBlocking {
        assertEquals(
            EdenAIChatRequest(
                providers = testModel1.name.split("/")[0],
                fallbackProviders = null,
                text = context,
                chatbotGlobalAction = testSetup.prompt,
                temperature = EdenAIChatStrategy.DEFAULT_TEMPERATURE,
                maxTokens = EdenAIChatStrategy.DEFAULT_MAX_TOKENS,
                settings = mapOf(testModel1.name.split("/")[0] to testModel1.name.split("/")[1])
            ),
            edenAIChatStrategy.createRequest(testSetup, testSetup.models[0], context)
        )
    }

    @Test
    fun `#execute should call each of the setup models until a success happens`() = runBlocking {
        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns testModel1
        val edenAIChatRequest1 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[0], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest1) } throws
                createClientException(ErrorClassification.CLIENT_ERROR, "Client Request Error") //general call error

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel2.provider, testModel2.name) } returns testModel2
        val edenAIChatRequest2 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[1], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest2) } returns EdenAIChatResponse(
            outputs = mapOf(
                testModel2.name to ProviderOutput(status = EdenAIResponseStatus.FAIL, generatedText = "sorry, no can do", cost = 0.00)
            )
        ) //specific provider error

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel3.provider, testModel3.name) } returns testModel3
        val edenAIChatRequest3 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[2], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest3) } returns EdenAIChatResponse(
            outputs = mapOf(
                testModel3.name to ProviderOutput(status = EdenAIResponseStatus.SUCCESS, generatedText = "No clue LOL", cost = 0.01)
            )
        )

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(AITextInference(
            setupId = testSetup.id,
            setupName = testSetup.name,
            context = context,
            caller = requestDTO.caller,
            referenceModel = requestDTO.referenceModel,
            result = AIInferenceResult.SUCCESS,
            estimatedCost = 0.01.toBigDecimal(),
            outputs = listOf(
                AITextInferenceOutput(
                    model = testModel3.provider + " " + testModel3.name,
                    result = AIInferenceResult.SUCCESS,
                    output = "No clue LOL",
                    cost = 0.01.toBigDecimal()
                )
            )
        ), AITextInference::id.name, AITextInference::createdAt.name, AITextInference::updatedAt.name)

        coVerify(exactly = 3) { aiModelService.getAIModel(any(), any(), any()) }
        coVerify(exactly = 3) { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should get API key from ENV when apiKeyEnvVar is specified in AIModelInstance`() = runBlocking {
        val testModel1 = testModel1.copy(apiUrl = "customUrl")
        val testSetup = testSetup.copy(
            models = listOf(
                AIModelInstance(testModel1.provider, testModel1.name, apiKeyEnvVar = "CUSTOM_ENV_VAR")
            )
        )

        mockkObject(ServiceConfig) {
            every { ServiceConfig.getEnv("CUSTOM_ENV_VAR") } returns "customApiKey"
            coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns testModel1
            val edenAIChatRequest1 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[0], context)
            coEvery { edenAIChatClient.post(edenAIChatRequest1, "customUrl", "customApiKey") } returns EdenAIChatResponse(
                outputs = mapOf(
                    testModel1.name to ProviderOutput(status = EdenAIResponseStatus.SUCCESS, generatedText = "No clue LOL", cost = 0.01)
                )
            )

            val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
            assertThat(result).isSuccessWithDataIgnoringGivenFields(AITextInference(
                setupId = testSetup.id,
                setupName = testSetup.name,
                context = context,
                caller = requestDTO.caller,
                referenceModel = requestDTO.referenceModel,
                result = AIInferenceResult.SUCCESS,
                estimatedCost = 0.01.toBigDecimal(),
                outputs = listOf(
                    AITextInferenceOutput(
                        model = testModel1.provider + " " + testModel1.name,
                        result = AIInferenceResult.SUCCESS,
                        output = "No clue LOL",
                        cost = 0.01.toBigDecimal()
                    )
                )
            ), AITextInference::id.name, AITextInference::createdAt.name, AITextInference::updatedAt.name)

            coVerifyOnce { aiModelService.getAIModel(any(), any(), any()) }
            coVerifyOnce { edenAIChatClient.post(any(), any(), any()) }
        }
    }

    @Test
    fun `#execute should fail when apiKeyEnvVar is specified in AIModelInstance but it isn't set in the system`() = runBlocking {
        val testSetup = testSetup.copy(
            models = listOf(
                AIModelInstance(testModel1.provider, testModel1.name, apiKeyEnvVar = "CUSTOM_ENV_VAR")
            )
        )

        mockkObject(ServiceConfig) {
            every { ServiceConfig.getEnv("CUSTOM_ENV_VAR") } returns null
            coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns testModel1

            val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
            assertThat(result).isFailureOfType(AkinatorInferenceException::class)
            assertEquals( "[edenai openai/model1] failed: API key for CUSTOM_ENV_VAR not found", result.failure().message)

            coVerifyOnce { aiModelService.getAIModel(any(), any(), any()) }
            coVerifyNone { edenAIChatClient.post(any(), any(), any()) }
        }
    }

    @Test
    fun `#execute should de-identify context if personId is provided`() = runBlocking {
        val context = "José da Silva, conhecido como Zé, está com fome"
        val deIdentifiedContext = "@firstname @lastname, conhecido como @nickname, está com fome"
        val requestDTO = AIInferenceRequestDTO(context, "CLIENT-DOMAIN-SERVICE", RangeUUID.generate(RangeUUID.PERSON_ID_RANGE))

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns testModel1
        coEvery { personService.get(PersonId(requestDTO.personId!!)) } returns TestModelFactory.buildPerson().success()

        val edenAIChatRequest1 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[0], deIdentifiedContext)
        coEvery { edenAIChatClient.post(edenAIChatRequest1) } returns EdenAIChatResponse(
            outputs = mapOf(
                testModel1.name to ProviderOutput(status = EdenAIResponseStatus.SUCCESS, generatedText = "No clue LOL", cost = 0.01)
            )
        )

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(AITextInference(
            setupId = testSetup.id,
            setupName = testSetup.name,
            context = deIdentifiedContext,
            caller = requestDTO.caller,
            referenceModel = requestDTO.referenceModel,
            result = AIInferenceResult.SUCCESS,
            estimatedCost = 0.01.toBigDecimal(),
            outputs = listOf(
                AITextInferenceOutput(
                    model = testModel1.provider + " " + testModel1.name,
                    result = AIInferenceResult.SUCCESS,
                    output = "No clue LOL",
                    cost = 0.01.toBigDecimal()
                )
            )
        ), AITextInference::id.name, AITextInference::createdAt.name, AITextInference::updatedAt.name)

        coVerifyOnce { aiModelService.getAIModel(any(), any(), any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should skip unknown models`() = runBlocking {
        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns null
        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel2.provider, testModel2.name) } returns null

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel3.provider, testModel3.name) } returns testModel3
        val edenAIChatRequest3 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[2], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest3) } returns EdenAIChatResponse(
            outputs = mapOf(
                testModel3.name to ProviderOutput(status = EdenAIResponseStatus.SUCCESS, generatedText = "No clue LOL", cost = 0.01)
            )
        )

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isSuccessWithDataIgnoringGivenFields(AITextInference(
            setupId = testSetup.id,
            setupName = testSetup.name,
            context = context,
            caller = requestDTO.caller,
            referenceModel = requestDTO.referenceModel,
            result = AIInferenceResult.SUCCESS,
            estimatedCost = 0.01.toBigDecimal(),
            outputs = listOf(
                AITextInferenceOutput(
                    model = testModel3.provider + " " + testModel3.name,
                    result = AIInferenceResult.SUCCESS,
                    output = "No clue LOL",
                    cost = 0.01.toBigDecimal()
                )
            )
        ), AITextInference::id.name, AITextInference::createdAt.name, AITextInference::updatedAt.name)

        coVerify(exactly = 3) { aiModelService.getAIModel(any(), any(), any()) }
        coVerifyOnce { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should throw AkinatorInferenceException if all models fail`() = runBlocking {
        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel1.provider, testModel1.name) } returns testModel1
        val edenAIChatRequest1 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[0], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest1) } throws
                createClientException(ErrorClassification.CLIENT_ERROR, "Client Request Error")

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel2.provider, testModel2.name) } returns testModel2
        val edenAIChatRequest2 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[1], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest2) } throws
                createClientException(ErrorClassification.TIMEOUT, "Request Timeout")

        coEvery { aiModelService.getAIModel(testSetup.operationType, testModel3.provider, testModel3.name) } returns testModel3
        val edenAIChatRequest3 = edenAIChatStrategy.createRequest(testSetup, testSetup.models[2], context)
        coEvery { edenAIChatClient.post(edenAIChatRequest3) } throws
                createClientException(ErrorClassification.SERVER_ERROR, "Internal Server Error")

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isFailureOfType(AkinatorInferenceException::class)
        val exception = result.failure() as AkinatorInferenceException
        assertEquals("[edenai openai/model3] failed: EdenAIErrorResponse(error=EdenAIError(type=Internal Server Error, message=Internal Server Error))", exception.message)
        assertEquals("inference_failure", exception.code)

        coVerify(exactly = 3) { aiModelService.getAIModel(any(), any(), any()) }
        coVerify(exactly = 3) { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should fail if model name is not in expected EdenAI format`() = runBlocking {
        val invalidModel = TestModelFactory.buildAIModel(provider = "edenai", name = "model1")

        val result = aiTextInferenceExecutor.execute(
            testSetup.copy(models = listOf(AIModelInstance(invalidModel.provider, invalidModel.name))),
            requestDTO
        )
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { aiModelService.getAIModel(any(), any(), any()) }
        coVerifyNone { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should fail if a bad personId is provided`() = runBlocking {
        val context = "José da Silva, conhecido como Zé, está com fome"
        val requestDTO = AIInferenceRequestDTO(context, "CLIENT-DOMAIN-SERVICE", RangeUUID.generate(RangeUUID.DEFAULT_RANGE))

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyNone { personService.get(any()) }
        coVerifyNone { aiModelService.getAIModel(any(), any(), any()) }
        coVerifyNone { edenAIChatClient.post(any()) }
    }

    @Test
    fun `#execute should fail if personId is provided but person get fails`() = runBlocking {
        val context = "José da Silva, conhecido como Zé, está com fome"
        val requestDTO = AIInferenceRequestDTO(context, "CLIENT-DOMAIN-SERVICE", RangeUUID.generate(RangeUUID.PERSON_ID_RANGE))

        coEvery { personService.get(PersonId(requestDTO.personId!!)) } returns NotFoundException("OH GOSH WHERE IS IT").failure()

        val result = aiTextInferenceExecutor.execute(testSetup, requestDTO)
        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { personService.get(any()) }
        coVerifyNone { aiModelService.getAIModel(any(), any(), any()) }
        coVerifyNone { edenAIChatClient.post(any()) }
    }

    private fun createClientException(classification: ErrorClassification, type: String, message: String = type) =
        EdenAIClientException(classification, EdenAIErrorResponse(EdenAIError(type = type, message = message)))
}
