package br.com.alice.akinator.controllers

import br.com.alice.akinator.controllers.transfer.AIFeedbackRequestDTO
import br.com.alice.akinator.controllers.transfer.toDTO
import br.com.alice.akinator.module
import br.com.alice.akinator.services.AIFeedbackService
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AITextInferenceFeedback
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import kotlin.test.assertContains

class AIFeedbackControllerTest : RoutesTestHelper() {

    private val aiFeedbackService : AIFeedbackService = mockk()
    private val aiFeedbackController = AIFeedbackController(aiFeedbackService)

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { aiFeedbackController } }
            )
        )
    }
    private val testInference = TestModelFactory.buildAITextInference()
    private val expectedFeedback = AITextInferenceFeedback(testInference.id, "expected output")

    @Test
    fun `should accept feedback and return 200 OK`() {
        coEvery { aiFeedbackService.upsertFeedback(testInference.id, "expected output") } returns expectedFeedback.success()

        post("/feedback/${testInference.id}", AIFeedbackRequestDTO("expected output")) {
            assertThat(it).isOKWithData(expectedFeedback.toDTO())
        }

        coVerifyOnce { aiFeedbackService.upsertFeedback(any(), any()) }
    }

    @Test
    fun `should return 400 when service raises a expected failure`() {
        coEvery { aiFeedbackService.upsertFeedback(testInference.id, "expected output") } returns
                IllegalArgumentException("Cannot provide feedback for failed inference").failure()

        post("/feedback/${testInference.id}", AIFeedbackRequestDTO("expected output")) {
            assertThat(it).isBadRequest()
            assertContains(it.bodyAsText(), "Cannot provide feedback for failed inference")
        }

        coVerifyOnce { aiFeedbackService.upsertFeedback(any(), any()) }
    }
}
