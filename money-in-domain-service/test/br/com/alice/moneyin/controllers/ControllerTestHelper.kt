package br.com.alice.moneyin.controllers

import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.moneyin.module
import io.ktor.server.application.Application
import org.koin.core.context.loadKoinModules

abstract class ControllerTestHelper : RoutesTestHelper() {

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }
    override val moduleFunction: Application.() -> Unit = { loadKoinModules(module) }
}
