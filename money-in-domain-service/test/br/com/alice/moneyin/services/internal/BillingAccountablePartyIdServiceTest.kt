package br.com.alice.moneyin.services.internal

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.PrimaryAttentionType
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.services.PersonBillingAccountablePartyModelDataService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.model.InvalidBillingAccountablePartyIdException
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BillingAccountablePartyIdServiceTest {

    private val personService: PersonService = mockk()
    private val personBillingAccountablePartyDataService: PersonBillingAccountablePartyModelDataService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val companyService: CompanyService = mockk()

    private val service = BillingAccountablePartyIdService(
        personService,
        personBillingAccountablePartyDataService,
        beneficiaryService,
        companySubContractService,
        memberService,
        productService,
        companyService
    )

    private val personId = PersonId()
    private var person = TestModelFactory.buildPerson(personId = personId)
    private var product = TestModelFactory.buildProduct()
    private val member = TestModelFactory.buildMember(
        personId = personId,
        selectedProduct = MemberProduct(product.id, product.prices, type = product.type),
    )
    private val companySubContractId = RangeUUID.generate()
    private var beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = null)
    private var companySubContract = TestModelFactory.buildCompanySubContract(id = companySubContractId)
    private val billingAccountablePartyId = RangeUUID.generate()
    private var company = TestModelFactory.buildCompany()
    private val personBillingAccountableParty =
        TestModelFactory.buildPersonBillingAccountableParty(billingAccountablePartyId = billingAccountablePartyId)

    @Nested
    inner class GetFromPersonId {

        @Nested
        inner class ProductB2C {
            @Test
            fun `#getFromPersonId should return successfully when person has product_info and product_type is B2C`() =
                runBlocking {
                    person = person.copy(
                        productInfo = ProductInfo(
                            brand = Brand.ALICE,
                            primaryAttention = PrimaryAttentionType.ALICE,
                            tier = TierType.TIER_1,
                            coPayment = CoPaymentType.NONE,
                            healthcareModelType = HealthcareModelType.V3,
                            refund = RefundType.FULL,
                            productType = ProductType.B2C
                        )
                    )

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        personBillingAccountablePartyDataService.findOne(queryEq {
                            where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                        })
                    } returns personBillingAccountableParty.toModel().success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isSuccessWithData(billingAccountablePartyId)

                    coVerifyOnce {
                        personService.get(any())
                        personBillingAccountablePartyDataService.findOne(queryEq {
                            where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                        })
                    }

                    coVerifyNone {
                        beneficiaryService.findByPersonId(any())
                        companySubContractService.get(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                        companyService.get(any())
                    }
                }

            @Test
            fun `#getFromPersonId should return successfully when person does not have product_info and product_type is B2C`() =
                runBlocking {

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        memberService.getCurrent(personId)
                    } returns member.success()

                    coEvery {
                        productService.getProduct(member.selectedProduct.id)
                    } returns product.success()

                    coEvery {
                        personBillingAccountablePartyDataService.findOne(queryEq {
                            where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                        })
                    } returns personBillingAccountableParty.toModel().success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isSuccessWithData(billingAccountablePartyId)

                    coVerifyOnce {
                        personService.get(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                        personBillingAccountablePartyDataService.findOne(queryEq {
                            where { this.personId.eq(person.id).and(this.endDate.isNull()) }
                        })
                    }

                    coVerifyNone {
                        beneficiaryService.findByPersonId(any())
                        companySubContractService.get(any())
                        companyService.get(any())
                    }

                }
        }

        @Nested
        inner class ProductB2B {
            @Test
            fun `#getFromPersonId should return successfully when person has product_info and product_type is B2B`() =
                runBlocking {
                    person = person.copy(
                        productInfo = ProductInfo(
                            brand = Brand.ALICE,
                            primaryAttention = PrimaryAttentionType.ALICE,
                            tier = TierType.TIER_1,
                            coPayment = CoPaymentType.NONE,
                            healthcareModelType = HealthcareModelType.V3,
                            refund = RefundType.FULL,
                            productType = ProductType.B2B
                        )
                    )

                    beneficiary = beneficiary.copy(
                        companySubContractId = companySubContractId
                    )

                    companySubContract = companySubContract.copy(
                        billingAccountablePartyId = billingAccountablePartyId
                    )

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        beneficiaryService.findByPersonId(personId)
                    } returns beneficiary.success()

                    coEvery {
                        companySubContractService.get(companySubContractId)
                    } returns companySubContract.success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isSuccessWithData(billingAccountablePartyId)

                    coVerifyOnce {
                        personService.get(any())
                        beneficiaryService.findByPersonId(any())
                        companySubContractService.get(any())
                    }

                    coVerifyNone {
                        personBillingAccountablePartyDataService.findOne(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                        companyService.get(any())
                    }
                }

            @Test
            fun `#getFromPersonId should return successfully when person does not have product_info and product_type is B2B`() =
                runBlocking {

                    product = product.copy(
                        type = ProductType.B2B
                    )

                    beneficiary = beneficiary.copy(
                        companySubContractId = companySubContractId
                    )

                    companySubContract = companySubContract.copy(
                        billingAccountablePartyId = billingAccountablePartyId
                    )

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        memberService.getCurrent(personId)
                    } returns member.success()

                    coEvery {
                        productService.getProduct(member.selectedProduct.id)
                    } returns product.success()

                    coEvery {
                        beneficiaryService.findByPersonId(personId)
                    } returns beneficiary.success()

                    coEvery {
                        companySubContractService.get(companySubContractId)
                    } returns companySubContract.success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isSuccessWithData(billingAccountablePartyId)

                    coVerifyOnce {
                        personService.get(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                        beneficiaryService.findByPersonId(any())
                        companySubContractService.get(any())
                    }

                    coVerifyNone {
                        personBillingAccountablePartyDataService.findOne(any())
                        companyService.get(any())
                    }
                }

            @Test
            fun `#getFromPersonId should return successfully when company has billingAccountablePartyId`() =
                runBlocking {
                    person = person.copy(
                        productInfo = ProductInfo(
                            brand = Brand.ALICE,
                            primaryAttention = PrimaryAttentionType.ALICE,
                            tier = TierType.TIER_1,
                            coPayment = CoPaymentType.NONE,
                            healthcareModelType = HealthcareModelType.V3,
                            refund = RefundType.FULL,
                            productType = ProductType.B2B
                        )
                    )

                    company = company.copy(
                        billingAccountablePartyId = billingAccountablePartyId
                    )

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        beneficiaryService.findByPersonId(personId)
                    } returns beneficiary.success()

                    coEvery {
                        companyService.get(beneficiary.companyId)
                    } returns company.success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isSuccessWithData(billingAccountablePartyId)

                    coVerifyOnce {
                        personService.get(any())
                        beneficiaryService.findByPersonId(any())
                        companyService.get(any())
                    }

                    coVerifyNone {
                        personBillingAccountablePartyDataService.findOne(any())
                        companySubContractService.get(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                    }
                }

            @Test
            fun `#getFromPersonId should fails when beneficiary does not have billingAccountablePartyId associated with`() =
                runBlocking {
                    person = person.copy(
                        productInfo = ProductInfo(
                            brand = Brand.ALICE,
                            primaryAttention = PrimaryAttentionType.ALICE,
                            tier = TierType.TIER_1,
                            coPayment = CoPaymentType.NONE,
                            healthcareModelType = HealthcareModelType.V3,
                            refund = RefundType.FULL,
                            productType = ProductType.B2B
                        )
                    )

                    company = company.copy(
                        billingAccountablePartyId = null
                    )

                    coEvery {
                        personService.get(personId)
                    } returns person.success()

                    coEvery {
                        beneficiaryService.findByPersonId(personId)
                    } returns beneficiary.success()

                    coEvery {
                        companyService.get(beneficiary.companyId)
                    } returns company.success()

                    val result = service.getFromPersonId(personId)

                    ResultAssert.assertThat(result).isFailureOfType(InvalidBillingAccountablePartyIdException::class)

                    coVerifyOnce {
                        personService.get(any())
                        beneficiaryService.findByPersonId(any())
                        companyService.get(any())
                    }

                    coVerifyNone {
                        personBillingAccountablePartyDataService.findOne(any())
                        companySubContractService.get(any())
                        memberService.getCurrent(any())
                        productService.getProduct(any())
                    }
                }
            
        }

    }
}
