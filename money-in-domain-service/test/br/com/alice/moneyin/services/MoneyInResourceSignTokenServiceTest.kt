package br.com.alice.moneyin.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ResourceSignTokenType
import br.com.alice.data.layer.services.impl.ResourceSignTokenServiceImpl
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.Duration
import kotlin.test.Test

class MoneyInResourceSignTokenServiceTest {

    private val resourceSignTokenServiceImpl: ResourceSignTokenServiceImpl = mockk()
    private val service = MoneyInResourceSignTokenServiceImpl(
        resourceSignTokenServiceImpl
    )

    @Test
    fun `#createSignTokenForMoneyInBff should retrieve a sign token`() = runBlocking {
        val invoicePaymentId = RangeUUID.generate()
        val signUuid = RangeUUID.generateUUIDv7()
        val resourceSignToken = TestModelFactory.buildResourceSignToken(signUuid = signUuid)

        coEvery {
            resourceSignTokenServiceImpl.generateResourceSignToken(
                resourceId = invoicePaymentId.toString(),
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
            )
        } returns resourceSignToken

        val result = service.createSignTokenForMoneyInBff(invoicePaymentId)

        assertThat(result).isSuccessWithData(resourceSignToken.signUuid)

        coVerifyOnce { resourceSignTokenServiceImpl.generateResourceSignToken(any(), any()) }
    }

    @Test
    fun `#createSignTokenForMoneyInBff should generate a sign token`() = runBlocking {
        val invoicePaymentId = RangeUUID.generate()
        val signUuid = RangeUUID.generateUUIDv7()
        val resourceSignToken = TestModelFactory.buildResourceSignToken(signUuid = signUuid)

        coEvery {
            resourceSignTokenServiceImpl.generateResourceSignToken(
                resourceId = invoicePaymentId.toString(),
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
            )
        } returns resourceSignToken

        val result = service.createSignTokenForMoneyInBff(invoicePaymentId)

        assertThat(result).isSuccessWithData(resourceSignToken.signUuid)

        coVerifyOnce { resourceSignTokenServiceImpl.generateResourceSignToken(any(), any()) }
    }

    @Test
    fun `#isSignTokenValidForMoneyInBff should be valid`() = runBlocking {
        val invoicePaymentId = RangeUUID.generate()
        val signUuid = RangeUUID.generateUUIDv7()
        coEvery {
            resourceSignTokenServiceImpl.isSignTokenValid(
                signUuid = signUuid,
                resource = ResourceSignTokenType.INVOICE_PAYMENT,
                expirationDate = Duration.ofDays(90),
                resourceId = invoicePaymentId.toString(),
            )
        } returns true

        val result = service.isSignTokenValidForMoneyInBff(invoicePaymentId, signUuid)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { resourceSignTokenServiceImpl.isSignTokenValid(any(), any(), any(), any()) }
    }

    @Test
    fun `#getSignTokenForMoneyInBff should return signToken`() = runBlocking {
        val invoicePaymentId = RangeUUID.generate()
        val signUuid = RangeUUID.generateUUIDv7()
        val resourceSignToken = TestModelFactory.buildResourceSignToken(signUuid = signUuid)

        coEvery {
            resourceSignTokenServiceImpl.findSignTokenByResourceIdAndResourceType(
                resourceId = invoicePaymentId.toString(),
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
            )
        } returns resourceSignToken

        val result = service.getSignTokenForMoneyInBff(invoicePaymentId)

        assertThat(result).isSuccessWithData(resourceSignToken.signUuid)

        coVerifyOnce { resourceSignTokenServiceImpl.findSignTokenByResourceIdAndResourceType(any(), any()) }
    }

    @Test
    fun `#softDeleteSignToken should delete sign token`() = runBlocking {
        val resourceSignTokenId = RangeUUID.generate()
        coEvery {
            resourceSignTokenServiceImpl.softDeleteSignToken(resourceSignTokenId)
        } returns true

        val result = service.softDeleteSignToken(resourceSignTokenId)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { resourceSignTokenServiceImpl.softDeleteSignToken(any()) }
    }
}
