package br.com.alice.moneyin.services

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.services.internal.AcquirerOrchestratorService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoicePaymentProcessorTest {

    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val acquirerOrchestratorService: AcquirerOrchestratorService = mockk()
    private val sutInvoiceProcessor = InvoicePaymentProcessor(invoicePaymentService, acquirerOrchestratorService)

    private val externalId = "A6A3B6B89B9140BABC9AED2D128FD94B"
    private val payment = TestModelFactory.buildInvoicePayment(externalId = externalId)

    @AfterTest
    fun setup() = clearAllMocks()

    @Nested
    inner class Approved {
        @Test
        fun `#processInvoicePayment - given a 'APPROVED' payment status, should call 'approve' method with fines`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.APPROVED
                val payment = payment.copy(amount = BigDecimal("800.20"), status = InvoicePaymentStatus.APPROVED)
                val paidAt = LocalDateTime.of(2024, 2, 2, 7, 17, 13)

                coEvery {
                    invoicePaymentService.approve(
                        payment.id,
                        approvedAt = paidAt,
                        amountPaid = BigDecimal("816.72"),
                        fine = BigDecimal("16.00"),
                        interest = BigDecimal("0.52")
                    )

                } returns payment.success()

                coEvery { acquirerOrchestratorService.get(payment) } returns AcquirerGetPaymentResponse(
                    id = externalId,
                    status = PaymentStatus.APPROVED,
                    totalCents = 80020,
                    paidAt = LocalDateTime.of(2023, 8, 11, 7, 17),
                    perDayInterestCents = 26,
                    perDayInterest = true,
                    perDayInterestValue = 1,
                    latePaymentFineCents = null,
                    latePaymentFine = 2,
                    paidCents = 81672,
                    dueDate = LocalDate.of(2024, 1, 31),
                    externalUrl = null,
                    finesOnOccurrenceDayCents = 1652
                ).success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus, paidAt)

                coVerify(exactly = 1) { acquirerOrchestratorService.get(any()) }
                coVerify(exactly = 1) {
                    invoicePaymentService.approve(
                        payment.id,
                        approvedAt = paidAt,
                        amountPaid = BigDecimal("816.72"),
                        fine = BigDecimal("16.00"),
                        interest = BigDecimal("0.52")
                    )
                }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
            }

        @Test
        fun `#processInvoicePayment - given a 'APPROVED' payment status, should call 'approve' method without fines`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.APPROVED
                val payment = payment.copy(amount = BigDecimal("800.20"), status = InvoicePaymentStatus.APPROVED)
                val paidAt = LocalDateTime.of(2024, 2, 2, 7, 17, 13)

                coEvery {
                    invoicePaymentService.approve(
                        payment.id,
                        approvedAt = paidAt,
                        amountPaid = BigDecimal("800.20"),
                        fine = BigDecimal("0.00"),
                        interest = BigDecimal("0.00")
                    )

                } returns payment.success()

                coEvery { acquirerOrchestratorService.get(payment) } returns AcquirerGetPaymentResponse(
                    id = externalId,
                    status = PaymentStatus.APPROVED,
                    totalCents = 80020,
                    paidAt = LocalDateTime.of(2023, 8, 11, 7, 17),
                    perDayInterestCents = 26,
                    perDayInterest = true,
                    perDayInterestValue = 1,
                    latePaymentFineCents = null,
                    latePaymentFine = 2,
                    paidCents = 80020,
                    dueDate = LocalDate.of(2024, 1, 31),
                    externalUrl = null,
                    finesOnOccurrenceDayCents = 0
                ).success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus, paidAt)

                coVerify(exactly = 1) { acquirerOrchestratorService.get(any()) }
                coVerify(exactly = 1) {
                    invoicePaymentService.approve(
                        payment.id,
                        approvedAt = paidAt,
                        amountPaid = BigDecimal("800.20"),
                        fine = BigDecimal("0.00"),
                        interest = BigDecimal("0.00")
                    )
                }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
            }

        @Test
        fun `#processInvoicePayment - given a 'APPROVED' payment status without paid at, should call 'approve' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.APPROVED
                val paidAt = LocalDateTime.of(2023, 8, 5, 5, 39, 39)

                coEvery { acquirerOrchestratorService.get(payment) } returns AcquirerGetPaymentResponse(
                    id = externalId,
                    status = PaymentStatus.APPROVED,
                    totalCents = 39816,
                    paidAt = paidAt,
                    perDayInterestCents = 13,
                    perDayInterest = true,
                    perDayInterestValue = 1,
                    latePaymentFineCents = null,
                    latePaymentFine = 2,
                    paidCents = 40664,
                    dueDate = LocalDate.of(2023, 7, 31),
                    externalUrl = null,
                    finesOnOccurrenceDayCents = 848
                ).success()


                coEvery {
                    invoicePaymentService.approve(
                        payment.id,
                        approvedAt = paidAt,
                        fine = BigDecimal("7.96"),
                        interest = BigDecimal("0.52"),
                        amountPaid = BigDecimal("406.64"),
                    )
                } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus, paidAt = null)

                coVerify(exactly = 1) { acquirerOrchestratorService.get(any()) }
                coVerify(exactly = 1) {
                    invoicePaymentService.approve(
                        payment.id, approvedAt = paidAt, fine = BigDecimal("7.96"),
                        interest = BigDecimal("0.52"),
                        amountPaid = BigDecimal("406.64"),
                    )
                }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
            }
    }

    @Nested
    inner class Error {
        @Test
        fun `#processInvoicePayment - given a 'ERROR' payment status, should call 'fail' method`() = runBlocking<Unit> {
            val paymentStatus = PaymentStatus.ERROR
            val reason = "No reason provided"

            coEvery { invoicePaymentService.fail(payment.id, reason) } returns payment.success()

            sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

            coVerify(exactly = 1) { invoicePaymentService.fail(payment.id, reason) }
            coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
            coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
            coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
        }

        @Test
        fun `#processInvoicePayment - given a 'ERROR' payment status and paymentId, should call 'fail' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.ERROR
                val reason = "No reason provided"

                coEvery { invoicePaymentService.fail(payment.id, reason) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.fail(payment.id, reason) }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
            }

    }

    @Nested
    inner class Canceled {
        @Test
        fun `#processInvoicePayment - given a 'CANCELED' payment status, should call 'cancel' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.CANCELED
                val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED

                coEvery { invoicePaymentService.cancel(payment.id, reason) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.cancel(payment.id, reason) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }

        @Test
        fun `#processInvoicePayment - given a 'CANCELED' payment status and paymentId, should call 'cancel' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.CANCELED
                val reason = CancellationReason.PAYMENT_PROCESSOR_CANCELED

                coEvery { invoicePaymentService.cancel(payment.id, reason) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.cancel(payment.id, reason) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }

    }

    @Nested
    inner class Declined {
        @Test
        fun `#processInvoicePayment - given a 'DECLINED' payment status, should call 'decline' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.DECLINED

                coEvery { invoicePaymentService.decline(payment.id) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.decline(payment.id) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }

        @Test
        fun `#processInvoicePayment - given a 'DECLINED' payment status and paymentId, should call 'decline' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.DECLINED

                coEvery { invoicePaymentService.decline(payment.id) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.decline(payment.id) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }
    }

    @Nested
    inner class Pending {
        @Test
        fun `#processInvoicePayment - given a 'PENDING' payment status, should not call any method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.PENDING

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }


        @Test
        fun `#processInvoicePayment - given a 'PENDING' payment status and paymentId, should not call any method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.PENDING

                coEvery { invoicePaymentService.get(payment.id, false) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.decline(any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }

    }


    @Nested
    inner class Expired {
        @Test
        fun `#processInvoicePayment - given a 'EXPIRED' payment status and paymentId, should call 'expire' method`() =
            runBlocking<Unit> {
                val paymentStatus = PaymentStatus.EXPIRED

                coEvery { invoicePaymentService.expire(payment.id) } returns payment.success()

                sutInvoiceProcessor.processInvoicePayment(payment, paymentStatus)

                coVerify(exactly = 1) { invoicePaymentService.expire(payment.id) }
                coVerify(exactly = 0) { invoicePaymentService.decline(payment.id) }
                coVerify(exactly = 0) { invoicePaymentService.approve(any()) }
                coVerify(exactly = 0) { invoicePaymentService.cancel(any(), any()) }
                coVerify(exactly = 0) { invoicePaymentService.fail(any(), any()) }
            }
    }

}
