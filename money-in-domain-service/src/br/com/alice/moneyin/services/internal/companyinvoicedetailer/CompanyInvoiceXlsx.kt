package br.com.alice.moneyin.services.internal.companyinvoicedetailer

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File

class CompanyInvoiceXlsx(private val filename: String) {
    lateinit var temporaryInvoiceDetailsFile: File;

    suspend fun getWorkbook() = coroutineScope {
        val templateFile = javaClass.classLoader.getResourceAsStream(filename)

        temporaryInvoiceDetailsFile = withContext(Dispatchers.IO) {
            val temp = File.createTempFile("InvoiceDetailsB2B", ".xlsx")
            templateFile?.transferTo(temp.outputStream())
            temp
        }

        XSSFWorkbook(temporaryInvoiceDetailsFile.inputStream())
    }

    fun getTemporaryDetailsFile() = temporaryInvoiceDetailsFile
}
