package br.com.alice.moneyin.services

import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.exceptions.CompanyNotFoundException
import br.com.alice.business.exceptions.SubContractDoesNotBelongToCompanyException
import br.com.alice.business.exceptions.SubContractNotFoundException
import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoicePriceType
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PreActivationPayment
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentType
import br.com.alice.data.layer.services.PreActivationPaymentModelDataService
import br.com.alice.moneyin.client.InvalidAmountException
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import br.com.alice.moneyin.event.PreActivationPaymentCanceledEvent
import br.com.alice.moneyin.event.PreActivationPaymentCreatedEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import br.com.alice.moneyin.model.MemberInvoicesIsEmptyException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class PreActivationPaymentServiceImpl(
    private val preActivationPaymentModelDataService: PreActivationPaymentModelDataService,
    private val companyService: CompanyService,
    private val companySubContractService: CompanySubContractService,
    private val invoicesService: InvoicesService,
    private val invoiceItemService: InvoiceItemService,
    private val invoicePaymentService: InvoicePaymentService,
    private val kafkaProducerService: KafkaProducerService,
) : PreActivationPaymentService {

    override suspend fun get(id: UUID) = preActivationPaymentModelDataService.get(id).map { it.toTransport() }

    override suspend fun generateForB2B(
        companyId: UUID,
        companySubContractId: UUID,
        members: List<Member>,
        billingAccountablePartyId: UUID,
        referenceDate: LocalDate,
        dueDate: LocalDate,
        paymentMethod: PaymentMethod,
        paymentOrigin: InvoicePaymentOrigin,
    ): Result<PreActivationPayment, Throwable> = coResultOf {
        coroutineScope {

            val subcontract = getContractAndSubcontract(companyId, companySubContractId).second

            val status = listOf(
                PreActivationPaymentStatus.PAID,
                PreActivationPaymentStatus.PROCESSED,
            )

            findBySubcontract(
                companySubContractId,
                referenceDate,
                status,
            ).getOrNullIfNotFound()
                ?.let { return@coroutineScope it.toTransport() }

            val activeInvoiceItemsDeferred = async {
                invoiceItemService.listActiveInvoiceItemsBySubcontractId(
                    companySubContractId,
                    referenceDate
                )
                    .getOrElse { emptyList() }
            }

            val memberInvoicesDeferred =
                async { generateMemberInvoices(subcontract, members, referenceDate, dueDate, paymentMethod) }

            val activeInvoiceItems = activeInvoiceItemsDeferred.await()
            val memberInvoices = memberInvoicesDeferred.await()

            val totalAmount =
                memberInvoices.sumOf { it.totalAmount.money }
                    .let { totalAmount ->
                        activeInvoiceItems.sumOf { it.resolveValue(totalAmount) }.plus(totalAmount)
                    }

            val preActivationPayment = PreActivationPayment(
                billingAccountablePartyId = billingAccountablePartyId,
                referenceDate = referenceDate,
                dueDate = dueDate,
                status = PreActivationPaymentStatus.PROCESSING,
                externalId = null,
                type = PreActivationPaymentType.B2B,
                totalAmount = totalAmount,
                globalItems = activeInvoiceItems,
                companyId = companyId,
                companySubContractId = companySubContractId,
            )

            add(preActivationPayment)
                .flatMap { preActivationPayment ->
                    logger.info(
                        "Creating the list of MemberInvoices",
                        "member_invoice_ids" to memberInvoices.map { it.id })

                    val memberInvoices =
                        memberInvoices.map {
                            it.copy(
                                preActivationPaymentId = preActivationPayment.id,
                                type = MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT
                            )
                        }

                    validateAndCreateMemberInvoices(preActivationPayment, memberInvoices)
                        .flatMap { update(preActivationPayment.copy(memberInvoiceIds = it.map { memberInvoice -> memberInvoice.id })) }
                        .andThen {
                            createInvoicePayment(
                                it,
                                paymentMethod,
                                paymentOrigin,
                                dueDate,
                                syncProcess = it.memberInvoiceIds.size < 30
                            )
                        }
                        .flatMap {
                            update(it.markAsProcessed())
                                .then {
                                    logger.info(
                                        "The PreActivationPayment is marked as processed",
                                        "pre_activation_payment_id" to it.id
                                    )
                                }.thenError { err ->
                                    logger.info(
                                        "Something was wrong when tried to mark the PreActivationPayment as processed",
                                        "pre_activation_payment_id" to preActivationPayment.id,
                                        "ex" to err
                                    )
                                }
                        }
                }.get()
        }
    }


    private suspend fun createInvoicePayment(
        preActivationPayment: PreActivationPayment,
        paymentMethod: PaymentMethod,
        origin: InvoicePaymentOrigin,
        dueDate: LocalDate,
        syncProcess: Boolean = false
    ) = invoicePaymentService
        .createFromPreActivationPayment(
            preActivationPayment,
            paymentMethod,
            dueDate = dueDate,
            origin = origin,
            syncProcess = syncProcess,
        )
        .thenError { ex ->
            logger.error(
                "Something is wrong when trying to create the invoice payment for the pre activation payment",
                "pre_activation_payment_id" to preActivationPayment.id,
                "ex" to ex
            )

            cancelInvoicePayment(preActivationPayment)
        }.then { invoicePayment ->
            logger.info(
                "The invoice payment was created with the pre activation payment",
                "invoice_payment_id" to invoicePayment.id
            )
        }

    private suspend fun validateAndCreateMemberInvoices(
        preActivationPayment: PreActivationPayment,
        memberInvoices: List<MemberInvoice>
    ) = invoicesService.createInvoices(memberInvoices)
        .thenError { error ->
            logger.error(
                "Error during memberInvoices validation, reverting preActivationPayment ${preActivationPayment.id}",
                "error_message" to error.message,
            )

            cancelPreActivationPayment(preActivationPayment)
        }.flatMap {
            if (it.isEmpty()) {
                logger.error("List of member invoices is empty, reverting preActivationPayment ${preActivationPayment.id}")
                MemberInvoicesIsEmptyException().failure()
            } else it.success()
        }

    private suspend fun cancelPreActivationPayment(preActivationPayment: PreActivationPayment) =
        preActivationPayment.cancel().success()
            .flatMap { update(it) }
            .andThen { kafkaProducerService.produce(PreActivationPaymentCanceledEvent(it)).success() }

    private suspend fun cancelInvoicePayment(preActivationPayment: PreActivationPayment) =
        invoicePaymentService.cancelByPreActivationPaymentId(
            preActivationPayment.id,
            CancellationReason.PAYMENT_PROCESSOR_CANCELED
        )
            .andThen { cancelMemberInvoices(preActivationPayment, CancellationReason.PAYMENT_PROCESSOR_CANCELED) }
            .then {
                logger.info(
                    "Invoices payment from group canceled",
                    "pre_activaiton_payment_id" to preActivationPayment.id,
                    "invoice_payment_ids" to it.map { invoicePayment -> invoicePayment.id })
            }.mapError {
                logger.info(
                    "Something is wrong when it tried to cancel the invoice payment from pre activation payment",
                    "pre_activaiton_payment_id" to preActivationPayment.id,
                    "ex" to it
                )

                it
            }

    private suspend fun findMemberInvoicesFromPreActivationPayment(preActivationPayment: PreActivationPayment) =
        invoicesService.listByPreActivationPaymentId(preActivationPayment.id).then {
            logger.info(
                "findMemberInvoicesFromPreActivationPayment: find using pap id",
                "pre_activation_payment_id" to preActivationPayment.id,
            )
        }.map {
            it.ifEmpty {
                logger.info(
                    "Finding member invoices by member_invoice_ids from PreActivationPayment",
                    "pre_activation_payment_id" to preActivationPayment.id,
                    "member_invoice_ids" to preActivationPayment.memberInvoiceIds
                )

                invoicesService.findInvoicesByIds(preActivationPayment.memberInvoiceIds).get()
            }
        }

    private suspend fun cancelMemberInvoices(preActivationPayment: PreActivationPayment, reason: CancellationReason) =
        coroutineScope {
            findMemberInvoicesFromPreActivationPayment(preActivationPayment)
                .flatMap { memberInvoices ->
                    logger.info(
                        "Canceling current MemberInvoice list",
                        "pre_activaiton_payment_id" to preActivationPayment.id,
                        "member_invoice_ids" to memberInvoices.map { it.id },
                    )
                    memberInvoices.map { memberInvoice ->
                        async {
                            invoicesService.cancel(memberInvoice.id, reason)
                                .then {
                                    logger.info(
                                        "MemberInvoice cancellation is successfully",
                                        "member_id" to it.memberId,
                                        "total_amount" to it.totalAmount,
                                        "due_date" to it.dueDate,
                                        "reason" to reason,
                                    )
                                }
                                .mapError {

                                    logger.error(
                                        "MemberInvoice cancellation is failed",
                                        "error" to it.message,
                                        "member_invoice_id" to memberInvoice.id,
                                        "reason" to reason,
                                    )
                                    it
                                }
                        }
                    }.awaitAll()
                        .lift()
                }
                .andThen { cancelPreActivationPayment(preActivationPayment) }
        }
            .mapError {
                logger.error(
                    "Something is wrong when trying to cancel the member invoices",
                    "pre_activaiton_payment_id" to preActivationPayment.id,
                    "external_id" to preActivationPayment.externalId,
                    "error" to it.message,
                    "ex" to it
                )

                it
            }

    private suspend fun validateMemberInvoices(
        preActivationPayment: PreActivationPayment,
        memberInvoices: List<MemberInvoice>
    ) =
        invoicesService.validateInvoices(memberInvoices)
            .then { logger.info("MemberInvoice validation is successfully") }
            .mapError {
                logger.error(
                    "Something is wrong when trying to validate the member invoices to create the pre activation payment",
                    "pre_activation_payment_id" to preActivationPayment.id,
                    "external_id" to preActivationPayment.externalId,
                    "error" to it.message,
                    "ex" to it
                )

                it
            }

    private fun validatePreActivationPayment(preActivationPayment: PreActivationPayment): Result<PreActivationPayment, Throwable> {
        logger.info("Validating pre_activation_payment", "pre_activation_payment_id" to preActivationPayment.id)
        return if (!preActivationPayment.hasPositiveTotalAmount) {
            InvalidAmountException("Invalid invoice amount: the value must be positive").failure()
        } else {
            preActivationPayment.success()
        }
    }


    suspend fun add(preActivationPayment: PreActivationPayment): Result<PreActivationPayment, Throwable> =
        validatePreActivationPayment(preActivationPayment)
            .flatMap { preActivationPaymentModelDataService.add(preActivationPayment.toModel()) }
            .map { it.toTransport() }
            .andThen { kafkaProducerService.produce(PreActivationPaymentCreatedEvent(it)).success() }

    override suspend fun update(model: PreActivationPayment) =
        validatePreActivationPayment(model)
            .flatMap { preActivationPaymentModelDataService.update(it.toModel()) }
            .map { it.toTransport() }

    private suspend fun generateMemberInvoices(
        companySubContract: CompanySubContract,
        members: List<Member>,
        referenceDate: LocalDate,
        dueDate: LocalDate,
        paymentMethod: PaymentMethod,
    ) =
        invoicesService.generateForB2B(
            companySubContract,
            members,
            referenceDate.atBeginningOfTheMonth(),
            dueDate.atStartOfDay(),
            paymentMethod,
            MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
            MemberInvoicePriceType.FULL,
        ).get()

    private suspend fun getContractAndSubcontract(companyId: UUID, companySubContractId: UUID) = coroutineScope {
        val companyDeferred = async {
            companyService.get(companyId)
                .coFoldNotFound {
                    CompanyNotFoundException("Company with ID $companyId not found").failure()
                }
        }

        val subcontractDeferred = async {
            companySubContractService.get(companySubContractId)
                .flatMap {
                    if (it.companyId != companyId)
                        SubContractDoesNotBelongToCompanyException("Subcontract with ID $companySubContractId does not belong to the company with ID $companyId").failure()
                    else it.success()
                }
                .coFoldNotFound {
                    SubContractNotFoundException("Subcontract with ID $companySubContractId not found").failure()
                }
        }

        val company = companyDeferred.await().get()
        val subcontract = subcontractDeferred.await().get()

        Pair(company, subcontract)
    }

    private suspend fun findBySubcontract(
        subcontractId: UUID,
        referenceDate: LocalDate,
        status: List<PreActivationPaymentStatus>
    ) = run {
        logger.info(
            "Find findBySubcontract",
            "subcontract_id" to subcontractId,
            "reference_date" to referenceDate,
            "status" to status
        )
        preActivationPaymentModelDataService.findOne {
            where {
                this.companySubContractId.eq(subcontractId) and this.referenceDate.eq(
                    referenceDate
                ) and this.status.inList(status)
            }
        }
    }

    override suspend fun markAsPaid(
        preActivationPayment: PreActivationPayment,
        invoicePayment: InvoicePayment
    ) = preActivationPayment.let { invoice ->
        logger.info(
            "PreActivationPayment::markAsPaid",
            "id" to invoice.id,
            "due_date" to invoice.dueDate,
            "approved_at" to invoicePayment.approvedAt,
        )

        if (invoice.isPaid) {
            logger.info(
                "PreActivationPayment::markAsPaid - preActivationPayment is already paid",
                "id" to invoice.id,
                "due_date" to invoice.dueDate,
                "approved_at" to invoice.statusHistory,
            )

            return preActivationPayment.success()
        }

        val updatedInvoice = invoice.markAsPaid()

        preActivationPaymentModelDataService.update(updatedInvoice.toModel())
            .map { it.toTransport() }
            .then { logger.info("PreActivationPayment paid", "preActivationPaymentId" to it.id) }
            .then { kafkaProducerService.produce(PreActivationPaymentPaidEvent(it, invoicePayment)) }
    }

    override suspend fun cancelById(id: UUID) =
        preActivationPaymentModelDataService.get(id)
            .flatMap { cancelPreActivationPayment(it.toTransport()) }
            .andThen { cancelInvoicePayment(it) }
            .thenError { logger.error("Error cancelling PreActivationPayment $id") }

    override suspend fun getBySubcontractId(subContractId: UUID, options: PreActivationPaymentService.FindOptions) =
        preActivationPaymentModelDataService.find {
            where {
                this.companySubContractId.eq(subContractId).and(options.status?.let {
                    this.status.inList(it)
                }).and(options.type?.let { this.type.inList(it) })
            }
        }.mapEach { it.toTransport() }

    override suspend fun listByCompanyId(companyId: UUID, options: PreActivationPaymentService.FindOptions) =
        preActivationPaymentModelDataService.find {
            where {
                this.companyId.eq(companyId).and(options.status?.let {
                    this.status.inList(it)
                })
            }
        }.mapEach { it.toTransport() }

    override suspend fun listByIds(
        preActivationPaymentIds: List<UUID>
    ) =
        preActivationPaymentModelDataService.find {
            where {
                this.id.inList(preActivationPaymentIds)
            }
        }.mapEach { it.toTransport() }
}
