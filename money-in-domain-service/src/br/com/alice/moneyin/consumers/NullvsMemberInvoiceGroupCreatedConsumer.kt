package br.com.alice.moneyin.consumers

import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.nullvs.events.NullvsMemberInvoiceGroupCreatedEvent
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class NullvsMemberInvoiceGroupCreatedConsumer(
    private val invoicePaymentService: InvoicePaymentService,
) : Consumer() {

    suspend fun createInvoicePaymentForMemberInvoiceGroup(event: NullvsMemberInvoiceGroupCreatedEvent) =
        withSubscribersEnvironment {
            logger.info("NullvsMemberInvoiceGroupCreatedEvent received - closeInvoice", "event" to event)

            val memberInvoiceGroup = event.payload.memberInvoiceGroup
            val businessType = event.payload.businessType
            val paymentMethod =
                if (businessType == NullvsMemberInvoiceGroupCreatedEvent.MemberInvoiceGroupBusinessType.B2B)
                    PaymentMethod.BOLEPIX
                else
                    PaymentMethod.BOLETO

            val paymentReason =
                if (businessType == NullvsMemberInvoiceGroupCreatedEvent.MemberInvoiceGroupBusinessType.B2B)
                    PaymentReason.B2B_REGULAR_PAYMENT
                else
                    PaymentReason.REGULAR_PAYMENT

            val shouldCreateInvoicePayment = invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id))
                .map { payments -> payments.count { it.isPending } == 0 }.get()

            if (shouldCreateInvoicePayment) {
                invoicePaymentService.createFromMemberInvoiceGroup(
                    memberInvoiceGroup,
                    paymentMethod = paymentMethod,
                    reason = paymentReason,
                    origin = InvoicePaymentOrigin.ISSUED_BY_NULLVS,
                ).then {
                    logger.info(
                        "Invoice payment created for member invoice group",
                        "invoice_payment_id" to it.id,
                        "member_invoice_group_id" to memberInvoiceGroup.id
                    )
                }
            } else {
                logger.info("The MemberInvoiceGroup already has a payment")

                true.success()
            }
        }
}
