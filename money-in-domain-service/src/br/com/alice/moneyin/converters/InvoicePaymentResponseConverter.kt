package br.com.alice.moneyin.converters

import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.moneyin.models.InvoicePaymentSecondCopyResponse
import br.com.alice.moneyin.models.PaymentDetailsSerialized

object InvoicePaymentResponseConverter {

    fun fromInvoicePayment(invoicePayment: InvoicePayment, monthReference: String) = InvoicePaymentSecondCopyResponse(
        invoicePaymentStatus = invoicePayment.status,
        amount = if (!invoicePayment.isCanceled) invoicePayment.amount else null,
        month = monthReference,
        paymentUrl = if (!invoicePayment.isCanceled) invoicePayment.paymentDetailString?.let {
            gson.fromJson(it, PaymentDetailsSerialized::class.java).paymentUrl
        } else null
    )
}
