package br.com.alice.moneyin.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemModel

object InvoiceItemConverter : Converter<InvoiceItemModel, InvoiceItem>(
    InvoiceItemModel::class,
    InvoiceItem::class,
)

fun InvoiceItem.toModel() = InvoiceItemConverter.unconvert(this)
fun InvoiceItemModel.toTransport() = InvoiceItemConverter.convert(this)
