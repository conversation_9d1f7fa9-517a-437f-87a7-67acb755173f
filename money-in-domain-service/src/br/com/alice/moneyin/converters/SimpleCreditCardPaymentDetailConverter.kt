package br.com.alice.moneyin.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetail
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetailModel

object SimpleCreditCardPaymentDetailConverter : Converter<SimpleCreditCardPaymentDetailModel, SimpleCreditCardPaymentDetail>(
    SimpleCreditCardPaymentDetailModel::class,
    SimpleCreditCardPaymentDetail::class,
)

fun SimpleCreditCardPaymentDetail.toModel() = SimpleCreditCardPaymentDetailConverter.unconvert(this)
fun SimpleCreditCardPaymentDetailModel.toTransport() = SimpleCreditCardPaymentDetailConverter.convert(this)
