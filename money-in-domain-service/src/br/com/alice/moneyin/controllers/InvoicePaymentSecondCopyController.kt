package br.com.alice.moneyin.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.MONEY_IN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.converters.InvoicePaymentResponseConverter
import com.github.kittinunf.result.map
import java.time.format.TextStyle
import java.util.Locale

class InvoicePaymentSecondCopyController(
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
) : Controller() {
    private suspend fun withMoneyInEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(MONEY_IN_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(MONEY_IN_ROOT_SERVICE_NAME) {
                func.invoke()
            }
        }

    suspend fun generateSecondCopyForExpiredInvoicePayment(personId: PersonId) = withMoneyInEnvironment {
        memberInvoiceGroupService.generateInvoicePaymentFromMemberInvoiceGroup(personId).map { invoicePayments ->
            memberInvoiceGroupService.getByIds(invoicePayments.mapNotNull { it.invoiceGroupId })
                .map { memberInvoiceGroups ->
                    val memberInvoiceGroupsAssociated = memberInvoiceGroups.associateBy { it.id }

                    invoicePayments.map { invoicePayment ->
                        val memberInvoiceGroup = memberInvoiceGroupsAssociated[invoicePayment.invoiceGroupId]!!
                        getInvoicePaymentResponse(invoicePayment, memberInvoiceGroup)
                    }
                }.get()
        }.foldResponse()
    }

    private fun getInvoicePaymentResponse(
        invoicePayment: InvoicePayment,
        memberInvoiceGroup: MemberInvoiceGroup
    ) =
        InvoicePaymentResponseConverter.fromInvoicePayment(
            invoicePayment = invoicePayment,
            monthReference = memberInvoiceGroup.referenceDate.month.getDisplayName(
                TextStyle.FULL, Locale("pt", "BR")
            )
        )
}

