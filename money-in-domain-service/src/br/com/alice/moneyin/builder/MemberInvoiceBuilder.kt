package br.com.alice.moneyin.builder

import br.com.alice.data.layer.models.InvoiceBreakdown
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceType
import java.time.LocalDate
import java.time.LocalDateTime

object MemberInvoiceBuilder {

    fun build(
        member: Member,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        invoiceBreakdown: InvoiceBreakdown,
        invoiceItems: List<InvoiceItem>? = null,
        type: MemberInvoiceType,
    ): MemberInvoice =
        MemberInvoice(
            personId = member.personId,
            memberId = member.id,
            totalAmount = invoiceBreakdown.totalAmount,
            referenceDate = referenceDate,
            dueDate = dueDate,
            invoiceItems = invoiceItems,
            invoiceBreakdown = invoiceBreakdown,
            type = type,
        )
}
