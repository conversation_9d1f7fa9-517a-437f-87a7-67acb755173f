package br.com.alice.action.plan.extensions

import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.StartType

fun ActionPlanTask.onValidInitState(): Boolean =
    initiatedByMemberAt == null &&
            isActive() &&
            hasTemporalStart() &&
            typeAllowedForMemberInit()

private fun ActionPlanTask.typeAllowedForMemberInit() =
    listOf(ActionPlanTaskType.PRESCRIPTION,
        ActionPlanTaskType.EATING,
        ActionPlanTaskType.PHYSICAL_ACTIVITY,
        ActionPlanTaskType.SLEEP,
        ActionPlanTaskType.MOOD,
        ActionPlanTaskType.OTHERS).contains(type)

private fun ActionPlanTask.hasTemporalStart() =
    start?.type == StartType.IMMEDIATE ||
            start?.type == StartType.CONDITIONAL
