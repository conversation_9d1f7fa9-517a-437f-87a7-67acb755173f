package br.com.alice.action.plan.ioc

import br.com.alice.action.plan.ActionPlanDomainConfiguration
import br.com.alice.action.plan.SERVICE_NAME
import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.client.ActionPlanTaskServiceClient
import br.com.alice.action.plan.client.DemandActionPlanService
import br.com.alice.action.plan.client.DemandActionPlanServiceClient
import br.com.alice.action.plan.client.HealthConditionGroupService
import br.com.alice.action.plan.client.HealthConditionGroupServiceClient
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import org.koin.core.qualifier.named
import org.koin.dsl.module

val ActionPlanDomainClientModule = module(createdAtStart = true) {

    val baseUrl = ActionPlanDomainConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<ActionPlanTaskService> { ActionPlanTaskServiceClient(invoker) }
    single<DemandActionPlanService> { DemandActionPlanServiceClient(invoker) }
    single<HealthConditionGroupService> { HealthConditionGroupServiceClient(invoker) }
}
