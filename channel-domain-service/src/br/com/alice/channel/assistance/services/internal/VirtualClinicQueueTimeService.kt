package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.assistance.services.internal.firestore.VirtualClinicQueueFirestoreService
import br.com.alice.channel.extensions.toLocalDateTime
import br.com.alice.common.logging.logger
import com.github.kittinunf.result.map
import java.time.Duration
import java.time.LocalDateTime

class VirtualClinicQueueTimeService(
    private val virtualClinicQueueFirestoreService: VirtualClinicQueueFirestoreService
) {

    suspend fun checkWaitingTime() =
        virtualClinicQueueFirestoreService.getQueue()
            .map { queue ->
                val now = LocalDateTime.now()

                val queueWithWaitingTime = queue.map { document ->
                    document to Duration.between(document.queuedAt.toLocalDateTime(), now).toMinutes()
                }

                val worstCase = queueWithWaitingTime.maxOfOrNull { (_, waitingTime) -> waitingTime }

                // Please do not remove this log.
                // Its being used to send alerts in the #alerts-ops-pa-virtual Slack channel
                logger.info(
                    "VirtualClinicQueueTimeService.checkWaitingTime",
                    "queue_size" to queueWithWaitingTime.size,
                    "max_time_in_minutes" to worstCase
                )

                queueWithWaitingTime
            }
}
