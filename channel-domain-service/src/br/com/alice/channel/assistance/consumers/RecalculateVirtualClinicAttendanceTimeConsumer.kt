package br.com.alice.channel.assistance.consumers

import br.com.alice.channel.assistance.services.internal.VirtualClinicDigitalCareQueueModalInfoService
import br.com.alice.channel.core.consumers.Consumer
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.models.ChannelSegment
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.MemberDropVirtualClinicEvent
import br.com.alice.channel.notifier.StaffDocumentUpdatedEvent
import br.com.alice.common.core.Role
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class RecalculateVirtualClinicAttendanceTimeConsumer(
    private val queueModalInfoService: VirtualClinicDigitalCareQueueModalInfoService,
    private val staffService: StaffService
) : Consumer() {

    companion object {
        private val VALID_CHANNEL_ACTIONS = listOf(
            ChannelChangeAction.VIRTUAL_CLINIC_CAUGHT,
            ChannelChangeAction.MEMBER_QUEUE_EXPERIENCE_VIRTUAL_CLINIC_CAUGHT,
        )

        private val VALID_ROLES = listOf(
            Role.VIRTUAL_CLINIC_PHYSICIAN,
            Role.VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN,
        )
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun onChannelUpserted(event: ChannelUpsertedEvent) =
        span("onChannelUpserted") { span ->
            withSubscribersEnvironment {
                val channelId = event.payload.channelId
                val action = event.payload.action
                val segment = event.payload.segment

                span.setAttribute("channel_id", channelId)
                span.setAttribute("action", action.toString())
                span.setAttribute("segment", segment.toString())

                if (action.isInvalid())
                    return@withSubscribersEnvironment false.success()

                queueModalInfoService.recalculate(segment)
                    .recordResult(span)
            }
        }

    @OptIn(FirestoreContextUsage::class)
    suspend fun onMemberDrop(event: MemberDropVirtualClinicEvent) =
        span("onMemberDrop") { span ->
            withSubscribersEnvironment {
                val channelId = event.payload.channel.id()
                val segment = event.payload.channel.segment

                span.setAttribute("channel_id", channelId)
                span.setAttribute("segment", segment.toString())

                queueModalInfoService.recalculate(segment)
                    .recordResult(span)
            }
        }

    @OptIn(FirestoreContextUsage::class)
    suspend fun onStaffUpdated(event: StaffDocumentUpdatedEvent) =
        span("onStaffUpdated") { span ->
            withSubscribersEnvironment {
                val staffId = event.payload.staffId
                span.setAttribute("staff_id", staffId.toString())

                staffService.get(staffId)
                    .flatMap { staff ->
                        if (staff.isInvalidRole()) return@flatMap false.success()

                        val segment = staff.getSegment()
                        span.setAttribute("segment", segment.toString())

                        queueModalInfoService.recalculate(segment)
                    }
                    .recordResult(span)
            }
        }

    private fun Staff.isInvalidRole() = !VALID_ROLES.contains(this.role)
    private fun Staff.getSegment() =
        when (role) {
            Role.VIRTUAL_CLINIC_PHYSICIAN -> ChannelSegment.ADULT
            Role.VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN -> ChannelSegment.PEDIATRIC
            else -> null
        }

    private fun ChannelChangeAction.isInvalid() = !VALID_CHANNEL_ACTIONS.contains(this)
}
