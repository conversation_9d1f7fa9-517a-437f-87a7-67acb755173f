package br.com.alice.channel.assistance.consumers

import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.core.consumers.Consumer
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.VirtualClinicQueueDocument
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelSubCategory
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success

class VirtualClinicQueueConsumer(
    private val virtualClinicQueueService: VirtualClinicQueueService
) : Consumer() {

    @OptIn(FirestoreContextUsage::class)
    suspend fun addOnQueue(event: ChannelUpsertedEvent) = span("addOnQueue") { span ->
        span.setChannel(event)
        withSubscribersEnvironment {
            if (event.isValidToQueue()) {
                queueChannel(event)
            } else false.success()
        }.recordResult(span)
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun removeFromQueue(event: ChannelUpsertedEvent) =
        span("removeFromQueue") { span ->
            span.setChannel(event)

            withSubscribersEnvironment {
                if(!event.shouldRemoveFromQueue()) return@withSubscribersEnvironment false.success()

                virtualClinicQueueService.removeFromQueue(event.payload.channelId)
                    .flatMapError { false.success() }
            }.recordResult(span)
        }

    @FirestoreContextUsage
    private suspend fun queueChannel(event: ChannelUpsertedEvent) =
        virtualClinicQueueService.addOnQueue(event.toQueueDocument())

    private fun ChannelUpsertedEvent.toQueueDocument() =
        payload.let {
            VirtualClinicQueueDocument(
                id = it.channelId,
                createdAt = it.createdAt.toTimestamp(),
                segment = it.segment
            )
        }

    private fun ChannelUpsertedEvent.isValidToQueue() =
        payload.let {
            it.subCategory == ChannelSubCategory.VIRTUAL_CLINIC &&
                    (it.action == ChannelChangeAction.RE_ROUTING || it.action == ChannelChangeAction.CREATE_CHANNEL)
        }

    private fun ChannelUpsertedEvent.shouldRemoveFromQueue() =
        payload.action == ChannelChangeAction.ARCHIVE && payload.subCategory == ChannelSubCategory.VIRTUAL_CLINIC
}
