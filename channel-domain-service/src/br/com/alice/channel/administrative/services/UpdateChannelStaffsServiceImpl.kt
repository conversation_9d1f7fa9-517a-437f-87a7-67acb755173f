package br.com.alice.channel.administrative.services

import br.com.alice.channel.client.UpdateChannelStaffsService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.context.firestoreTransactional
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.UpdateChannelStaffsRequest
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.google.cloud.firestore.FieldValue

class UpdateChannelStaffsServiceImpl(
    private val staffService: StaffService,
    private val internalChannelService: ChannelService
) : UpdateChannelStaffsService, Spannable {
    @OptIn(FirestoreContextUsage::class)
    override suspend fun updateChannelStaffs(updateChannelStaffsRequest: UpdateChannelStaffsRequest)
            : Result<Boolean, Throwable> = span("updateChannelStaffs") { span ->
        firestoreTransactional {
            internalChannelService.getChannel(updateChannelStaffsRequest.channelId)
                .map { channelDocument ->
                    if (!channelDocument.isAdministrative()) {
                        throw BadRequestException()
                    }

                    channelDocument.staffIds?.forEach {
                        internalChannelService.removeStaff(
                            channelDocument,
                            it,
                            it,
                            notify = false,
                            checkOwner = false,
                            checkSize = false
                        )
                    }

                    updateChannelStaffsRequest.staffEmails.forEach { staffEmail ->
                        staffService.findByEmail(staffEmail).flatMap { staff ->
                            val staffToAdd = ChannelStaffInfo(
                                id = staff.id.toString(),
                                name = staff.firstName,
                                firstName = staff.firstName,
                                lastName = staff.lastName,
                                description = StaffGenderDescriptionConverter.convert(staff),
                                profileImageUrl = staff.profileImageUrl.orEmpty(),
                                owner = updateChannelStaffsRequest.ownerEmail == staff.email
                            )

                            val paramsToUpdate = mapOf(
                                ChannelDocument::staff to mapOf(staffToAdd.id to staffToAdd),
                                ChannelDocument::staffHistory to mapOf(staffToAdd.id to FieldValue.delete()),
                                ChannelDocument::staffIds to FieldValue.arrayUnion(staffToAdd.id),
                                ChannelDocument::onCallAction to FieldValue.delete(),
                            )

                            internalChannelService.updateFields(channelDocument.id!!, paramsToUpdate)
                        }
                    }

                    true
                }.recordResult(span)
        }
    }
}
