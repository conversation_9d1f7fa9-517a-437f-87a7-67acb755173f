package br.com.alice.channel.administrative.consumers

import br.com.alice.akinator.client.AIInferenceService
import br.com.alice.akinator.client.transfer.AIInferenceRequestDTO
import br.com.alice.channel.core.consumers.Consumer
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.logics.ChannelMessageSanitizeService
import br.com.alice.channel.event.ShouldAddGptTagEvent
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelWithMessagesResponse
import br.com.alice.channel.models.MessageType
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.models.AIInferenceResult
import br.com.alice.data.layer.models.ChatGPTMacroTags
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import com.google.cloud.firestore.FieldValue.arrayUnion

class AdministrativeChannelsClassifyConsumer(
    private val channelMessageSanitizeService: ChannelMessageSanitizeService,
    private val aiInferenceService: AIInferenceService,
    private val channelFirestoreService: ChannelFirestoreService,
    private val personInternalReferenceService: PersonInternalReferenceService
) : Consumer() {

    private val akinatorSetupId = "adm_chat_classification_setup_id" // TODO: update config with correct uuid
    private val SERVICE_NAME = "channel-domain-service"

    suspend fun backfillClassifyAdministrativeChat(event: ShouldAddGptTagEvent) = withSubscribersEnvironment {
        span("backfillClassifyAdministrativeChat") { span ->
            val channelIdAndInternalCode = event.payload
            personInternalReferenceService.getByInternalCode(channelIdAndInternalCode.internalCode).flatMap {
                getChannelAndAddGptTag(channelIdAndInternalCode.channelId, it.personId)
            }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    private suspend fun getChannelAndAddGptTag(channelId: String, personId: PersonId): Result<Any, Throwable> {
        return channelMessageSanitizeService.getMessages(channelId, personId)
            .flatMap { channelWithMessages ->
                aiInferenceService.text2TextInference(
                    akinatorSetupIp(),
                    AIInferenceRequestDTO(
                        context = buildContextFrom(channelWithMessages),
                        caller = SERVICE_NAME,
                        personId = personId.id
                    )
                ).flatMap { aiResponse ->
                    if (aiResponse.result == AIInferenceResult.SUCCESS && aiResponse.outputs.isNotEmpty()) {
                        val macroTag = toMacroTag(aiResponse.outputs[0].output)
                        channelFirestoreService.updateFields(
                            channelId = channelId,
                            mapOf(ChannelDocument::tags to arrayUnion(macroTag))
                        )
                    } else {
                        false.success()
                    }
                }
            }
    }


    private inline fun <reified T> getFeatureFlagValue(key: String, defaultValue: T) =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = key,
            defaultValue = defaultValue
        )

    private fun akinatorSetupIp() =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = akinatorSetupId,
            defaultValue = "0f35744a-2192-4136-b9b6-f841808fc60"
        ).toUUID()

    private fun toMacroTag(aiResponse: String): String { //aiResponse format: "{\"categoria\":7}"
        val match = "[0-9]+".toRegex().find(aiResponse)
        val responseNumber = match?.groupValues?.first() ?: "17" // gpt_outros

        return ChatGPTMacroTags.values()
            .firstOrNull { it.categoryNumber == responseNumber }
            ?.name?.lowercase() ?: "gpt_outros"
    }

    private fun buildContextFrom(channelWithMessages: ChannelWithMessagesResponse): String =
        channelWithMessages
            .messages
            .filter { it.type == MessageType.TEXT }
            .sortedBy { it.createdAt }
            .mapNotNull { it.content }
            .joinToString(" ")
}
