package br.com.alice.channel.core.controllers

import br.com.alice.channel.core.services.internal.ChannelBackfillServiceImpl
import br.com.alice.common.Response
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import java.time.LocalDate

class BackfillController(
    private val channelBackfillServiceImpl: ChannelBackfillServiceImpl,
) : ChannelController() {

    suspend fun updateChannels(request: UpdateChannelRequest): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.updateChannels(request).foldResponse()
        }

    suspend fun updateChannelStaffs(request: UpdateChannelStaffRequest): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.updateChannelStaffs(request).foldResponse()
        }

    suspend fun updateChannelNameByType(request: UpdateChannelNameByTypeRequest): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.updateChannelNameByType(request).foldResponse()
        }

    @Deprecated("Move to InternalFeaturesController")
    suspend fun sendMessageToChannel(request: SendMessageToChannelRequest): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.sendMessageToChannel(request).foldResponse()
        }

    suspend fun forceStaffUpdateOnChannels(request: StaffUpdateOnChannelsRequest): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.forceStaffUpdateOnChannels(request).foldResponse()
        }

    suspend fun addGptag(request: AddGptagRequest): Response =  withChannelEnvironment {
        channelBackfillServiceImpl.addGptag(request).foldResponse()
    }

    suspend fun addProtocolAns(request: ChannelGenerateProtocolANS): Response =
        withChannelEnvironment {
            channelBackfillServiceImpl.addProtocolAns(request).foldResponse()
        }
}

data class AddGptagRequest(
    val execute: Boolean = false,
    val channelAndInternalCodes: List<ChannelIdAndInternalCode> = emptyList(),
)

data class ChannelIdAndInternalCode(
    val channelId: String,
    val internalCode: String,
)

data class UpdateChannelRequest(
    val execute: Boolean = false,
    val channelsToBeUpdated: List<UpdateChannel>? = emptyList(),
)

data class UpdateChannel(
    val id: String,
    val type: String?,
    val name: String?,
    val status: String?,
    val staffs: UpdateChannelStaffs?,
    val kind: ChannelKind?,
    val category: ChannelCategory?,
    val subCategory: ChannelSubCategory?,
    val subCategoryClassifier: ChannelSubCategoryClassifier?,
    val tags: List<UpdateChannelTag>? = null,
    val timeLastMessage: String? = null,
    val canBeArchived: Boolean? = null,
)

data class UpdateChannelStaffs(
    val notify: Boolean = false,
    val notifyRemovedStaffs: Boolean = true,
    val notifyAddedStaffs: Boolean = true,
    val staffIds: List<String>,
    val owner: String,
)

data class UpdateChannelTag(
    val name: String,
    val action: UpdateChannelTagAction
)

data class StaffUpdateOnChannelsRequest(
    val healthcareTeamIds: List<String>
)
enum class UpdateChannelTagAction {
    ADD, REMOVE
}

data class UpdateChannelStaffRequest(
    val offset: Int,
    val limit: Int,
    val staffIdToBeReplaced: String,
    val newStaffId: String,
    val channelPersonIds: List<String>,
    val channelType: String? = null,
    val execute: Boolean = false,
)

data class UpdateChannelNameByTypeRequest(
    val offset: Int,
    val limit: Int,
    val channelNewName: String,
    val channelTypeToBeRenamed: String,
    val channelPersonIds: List<String>? = emptyList(),
    val execute: Boolean = false,
)

data class SendMessageToChannelRequest(
    val execute: Boolean = false,
    val channels: List<MessageToChannel>? = emptyList(),
)

data class MessageToChannel(
    val channelId: String,
    val staffId: String?,
    val message: String,
)

data class ClearTypingFieldRequest(
    val execute: Boolean = false,
    val channelId: String,
)

data class ChannelGenerateProtocolANS(
    val channelIds: List<String>,
    val date: LocalDate,
)
