package br.com.alice.channel.core.consumers

import br.com.alice.channel.client.StaffChannelHistoryService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.models.StaffDocument
import br.com.alice.channel.models.StaffDocumentStatus
import br.com.alice.channel.notifier.StaffDocumentUpdatedEvent
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.StaffChannelHistory
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import br.com.alice.staff.event.StaffCreatedEvent
import br.com.alice.staff.event.StaffUpdatedEvent
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlin.reflect.KProperty1

class StaffConsumer(
    private val staffFirestoreService: StaffFirestoreService,
    private val staffService: StaffService,
    private val staffChannelHistoryService: StaffChannelHistoryService
) : Consumer() {

    suspend fun processCreatedStaff(event: StaffCreatedEvent) = span("processCreatedStaff") { span ->
        withSubscribersEnvironment {
            val staff = event.payload.staff

            span.setAttribute("staff_id", staff.id)

            staffFirestoreService.createIfNotExists(staff).recordResult(span)
        }
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun processUpdatedStaff(event: StaffUpdatedEvent) = span("processUpdatedStaff") { span ->
        withSubscribersEnvironment {
            val staffId = event.payload.staffId
            span.setAttribute("staff_id", staffId)

            staffService.get(staffId).flatMap { staff ->
                span.setAttribute("staff_active", staff.active)

                val baseParams = mapOf<KProperty1<StaffDocument, Any?>, Any?>(
                    StaffDocument::id to staff.id,
                    StaffDocument::name to staff.fullName,
                    StaffDocument::role to staff.role,
                    StaffDocument::profileImageUrl to staff.profileImageUrl,
                    StaffDocument::description to StaffGenderDescriptionConverter.convert(staff),
                    StaffDocument::active to staff.active
                )

                val paramsToUpdate = if (!staff.active) {
                    baseParams
                        .plus(StaffDocument::status to StaffDocumentStatus.UNAVAILABLE)
                        .plus(StaffDocument::isOnCall to false)
                } else baseParams

                staffFirestoreService.updateFields(staffId.toString(), paramsToUpdate)
            }.recordResult(span)
        }
    }

    suspend fun addStaffDocumentUpdatedInHistory(event: StaffDocumentUpdatedEvent) =
        span("addStaffDocumentUpdatedInHistory") { span ->
        withSubscribersEnvironment {
            event.payload.let { payload ->
                span.setAttribute("staff_id", payload.staffId)
                span.setAttribute("status", payload.status)
                span.setAttribute("on_call", payload.onCall)
                span.setAttribute("updated_by", payload.updatedBy)

                staffChannelHistoryService.create(
                    StaffChannelHistory(
                        staffId = payload.staffId,
                        status = payload.status.name,
                        changedDate = event.eventDate,
                        onCall = payload.onCall,
                        updatedBy = payload.updatedBy
                    )
                ).coFoldDuplicated {false.success() }
                    .recordResult(span)
            }
        }
    }
}
