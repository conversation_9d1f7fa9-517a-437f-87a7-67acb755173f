package br.com.alice.channel.core.consumers

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.RoutingService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelChangeAction.RE_ROUTING
import br.com.alice.data.layer.models.ChannelChangeAction.SCREENING
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp

class RoutingConsumer(
    private val channelFirestoreService: ChannelFirestoreService,
    private val routingService: RoutingService
) : Consumer() {

    @OptIn(FirestoreContextUsage::class)
    suspend fun routeChat(event: ChannelUpsertedEvent) = span("routeChat") { span ->
        withSubscribersEnvironment {
            span.setChannel(event)

            val payload = event.payload

            if (!payload.isValidToRouting()) return@withSubscribersEnvironment false.success()

            channelFirestoreService.getChannel(event.payload.channelId).flatMap { chat ->
                val createdAt = getDate(event.payload.action, chat)

                routingService.routeChat(chat.copy(createdAt = createdAt))
            }
        }.recordResult(span)
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun clearChatCaught(event: ChannelUpsertedEvent) = span("clearChatCaught") { span ->
        withSubscribersEnvironment {
            span.setChannel(event)

            if (!event.payload.canClear()) return@withSubscribersEnvironment false.success()

            routingService.clearChatCaught(event.payload.channelId)
        }.recordResult(span)
    }

    private fun ChannelUpsertedPayload.canClear(): Boolean =
        if (this.action != ChannelChangeAction.ADD_STAFF) false
        else this.staffIds.size <= 1

    private fun ChannelUpsertedPayload.isValidToRouting() =
        this.action == RE_ROUTING || this.action == SCREENING

    /**
     * Force new createdAt for action of RE_ROUTING
     * for routing not apply third rule and route the chat to nobody
     */
    private fun getDate(action: ChannelChangeAction, chat: ChannelDocument) =
        if (action == RE_ROUTING) Timestamp.now()
        else chat.createdAt

}
