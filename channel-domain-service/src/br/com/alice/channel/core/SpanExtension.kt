package br.com.alice.channel.core

import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.AdministrativeDetails
import br.com.alice.data.layer.models.Channel
import io.opentelemetry.api.trace.Span

fun Span.setChannel(channel: ChannelDocument) {
    this.setAttribute("channel_id", channel.id ?: "unknown_id")
    this.setAttribute("status", channel.status)
    this.setAttribute("channel_kind", channel.kind)
    this.setAttribute("category", channel.category)
    this.setAttribute("sub_category", channel.subCategory)
    this.setAttribute("sub_category_classifier", channel.subCategoryClassifier)
    this.setAttribute("staff_id", channel.staff.keys.joinToString())
    this.setAttribute("app_version", channel.appVersion.orEmpty())
    this.setAttribute("person_id", channel.personId)
}

fun Span.setChannel(channel: Channel) {
    this.setAttribute("channel_id", channel.channelId)
    this.setAttribute("status", channel.status)
    this.setAttribute("channel_kind", channel.kind)
    this.setAttribute("category", channel.category)
    this.setAttribute("sub_category", channel.subCategory)
    this.setAttribute("sub_category_classifier", channel.subCategoryClassifier)
    this.setAttribute("staff_id", channel.staff.keys.joinToString())
    this.setAttribute("app_version", channel.appVersion.orEmpty())
    this.setAttribute("person_id", channel.personId)
}

fun Span.setMessage(message: MessageDocument) {
    this.setAttribute("message_id", message.id ?: "unknown_id")
    this.setAttribute("message_from", message.userId)
    this.setAttribute("type", message.type)
    this.setAttribute("app_version", message.appVersion.orEmpty())
}

fun Span.setAdministrativeDetails(administrativeDetails: AdministrativeDetails?) {
    this.setAttribute("member_type", administrativeDetails?.memberType)
    this.setAttribute("company_lifes_amount", administrativeDetails?.companyLifesAmount)
    this.setAttribute("is_financial_representative", administrativeDetails?.isFinancialRepresentative)
    this.setAttribute("administrative_status", administrativeDetails?.status)
}


