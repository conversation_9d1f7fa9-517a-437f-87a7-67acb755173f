package br.com.alice.channel.core.services.internal.processors

import br.com.alice.channel.client.ChannelHistoryService
import br.com.alice.channel.core.logics.ChannelHistoryConverter
import br.com.alice.channel.extensions.toLocalDateTime
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.data.layer.models.ChannelChangeAction.FIRST_HEALTH_PROFESSIONAL_MESSAGE
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class ChannelHistoryProcessor(
    private val channelHistoryService: ChannelHistoryService
) : PublicMessageProcessor {

    override suspend fun process(channel: ChannelDocument, message: MessageDocument): Result<Boolean, Throwable> =
        span {
            if (channel.staff.keys.contains(message.userId)) {
                channelHistoryService
                    .findChatByChannelIdAndAction(channel.id!!, FIRST_HEALTH_PROFESSIONAL_MESSAGE)
                    .coFoldNotFound {
                        channelHistoryService.create(
                            ChannelHistoryConverter.convert(
                                channel,
                                FIRST_HEALTH_PROFESSIONAL_MESSAGE,
                                message.createdAt.toLocalDateTime()
                            )
                        )
                    }
            }

            true.success()
        }
}
