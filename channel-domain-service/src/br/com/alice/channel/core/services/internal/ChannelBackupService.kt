package br.com.alice.channel.core.services.internal

import br.com.alice.channel.core.setChannel
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.reduce
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.Channel
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.services.ChannelDataService
import br.com.alice.healthcondition.client.HealthConditionService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class ChannelBackupService(
    private val channelDataService: ChannelDataService,
    private val healthConditionService: HealthConditionService
) : Spannable {

    suspend fun saveChannelBackup(channel: Channel) =
        channelDataService.getByChannelId(channel.channelId)
            .map { channelFound ->
                channelFound to updateChannel(channelFound, channel).get()
            }.coFoldNotFound {
                channelDataService.add(channel).map { null to it }
            }

    suspend fun get(channelId: String) =
        channelDataService.findOne {
            where { this.channelId.eq(channelId) }
        }

    suspend fun getActivesChats() =
        channelDataService.find {
            where {
                this.kind.eq(ChannelKind.CHAT) and
                        this.status.eq(ChannelStatus.ACTIVE)
            }
        }

    suspend fun getActivesAndAcuteChannelsByChannelIds(ids: List<String>, date: LocalDateTime) =
        channelDataService.find {
            where {
                this.kind.eq(ChannelKind.CHANNEL) and
                        this.status.eq(ChannelStatus.ACTIVE) and
                        this.category.eq(ChannelCategory.ASSISTANCE) and
                        this.subCategory.eq(ChannelSubCategory.ACUTE) and
                        this.channelId.inList(ids) and
                        this.timeLastMessage.lessEq(date)
            }
        }

    suspend fun getArchivedAssistanceGroupedByHealthCondition(personId: PersonId, from: LocalDateTime) =
        channelDataService.find {
            where {
                this.personId.eq(personId) and
                        this.status.eq(ChannelStatus.ARCHIVED) and
                        this.category.eq(ChannelCategory.ASSISTANCE) and
                        this.channelCreatedAt.greaterEq(from)
            }
        }.reduce(mapOf<UUID, List<Channel>>()) { acc, channel ->
            channel.demands
                ?.filter { it.description.id != null }
                ?.fold(acc) { acc2, demand ->
                    val channelList = acc2[demand.description.id!!] ?: emptyList()
                    acc2.plus(demand.description.id!! to channelList.plus(channel))
                } ?: acc
        }.flatMapPair { channelsByHealthCondition ->
            healthConditionService.getAll(channelsByHealthCondition.keys.toList())
                .map { conditions -> conditions.filter { it.memberFriendlyName != null } }
                .map { conditions -> conditions.associateBy { it.id } }
        }.map { (conditionsById, channelsByHealthCondition) ->
            channelsByHealthCondition.mapNotNull { (healthConditionId, channels) ->
                conditionsById[healthConditionId]?.let { condition ->
                    condition to channels
                }
            }.toMap()
        }

    suspend fun findByFilters(filters: ChannelBackupServiceFilters) = span("findByFilters") { span ->
        span.setAttribute("filters", filters.toString())

        catchResult {
            filters.checkFilters()

            channelDataService.find {
                where {
                    filters.build(this)
                }
            }.recordResult(span)
        }
    }

    private suspend fun updateChannel(old: Channel, new: Channel) = span("updateChannel") { span ->
        span.setChannel(new)

        channelDataService
            .update(new.copy(id = old.id, version = old.version))
            .recordResult("result_update", span)
            .foldNotFound { new.success() }
    }

}

data class ChannelBackupServiceFilters(
    val personId: PersonId? = null,
    val statuses: List<ChannelStatus>? = null,
    val kind: ChannelKind? = null,
    val category: ChannelCategory? = null,
    val subCategory: ChannelSubCategory? = null,
    val channelIds: List<String>? = null,
    val timeLastMessageUntil: LocalDateTime? = null,
    val timeLastMessageSince: LocalDateTime? = null,
    val name: String? = null,
    val origin: String? = null,
    val subCategories: List<ChannelSubCategory>? = null,
    val screeningNavigationId: UUID? = null,
    val healthConditionIds: List<UUID>? = null
) {

    @OptIn(WithFilterPredicateUsage::class)
    fun build(fieldOptions: ChannelDataService.FieldOptions): Predicate {
        checkFilters()

        return basePredicateForFilters()
            .withFilter(personId) { fieldOptions.personId.eq(it) }
            .withFilter(statuses) { fieldOptions.status.inList(it) }
            .withFilter(kind) { fieldOptions.kind.eq(it) }
            .withFilter(category) { fieldOptions.category.eq(it) }
            .withFilter(subCategory) { fieldOptions.subCategory.eq(it) }
            .withFilter(channelIds) { fieldOptions.channelId.inList(it) }
            .withFilter(timeLastMessageUntil) { fieldOptions.timeLastMessage.lessEq(it) }
            .withFilter(timeLastMessageSince) { fieldOptions.timeLastMessage.greaterEq(it) }
            .withFilter(name) { fieldOptions.name.eq(it) }
            .withFilter(origin) { fieldOptions.origin.eq(it) }
            .withFilter(subCategories) { fieldOptions.subCategory.inList(it) }
            .withFilter(screeningNavigationId) { fieldOptions.screeningNavigationId.eq(it) }
            .withFilter(healthConditionIds) { fieldOptions.demands.inList(it) }!!
    }

    fun checkFilters(): Unit =
        when {
            personId != null -> Unit
            statuses.isNotNullOrEmpty() -> Unit
            kind != null -> Unit
            category != null -> Unit
            subCategory != null -> Unit
            channelIds.isNotNullOrEmpty() -> Unit
            timeLastMessageUntil != null -> Unit
            timeLastMessageSince != null -> Unit
            name != null -> Unit
            origin != null -> Unit
            subCategories.isNotNullOrEmpty() -> Unit
            screeningNavigationId != null -> Unit
            healthConditionIds.isNotNullOrEmpty() -> Unit
            else -> throw InvalidArgumentException(code = "empty_filters", message = "Filters cannot be empty")
        }

}
