package br.com.alice.channel.core.services

import br.com.alice.channel.client.ChannelDeIdentifiedService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.services.internal.logics.DeIdentificationMessage
import br.com.alice.channel.models.MessageResponse
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ChannelDeIdentifiedServiceImpl(
    private val channelFirestoreService: ChannelFirestoreService,
    private val messageFirestoreService: MessageFirestoreService,
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val personService: PersonService,
) : ChannelDeIdentifiedService {

    override suspend fun getMessagesByChannelId(channelId: String): Result<List<MessageResponse>, Throwable> =
        coroutineScope {
            val channelDeferred = async { channelFirestoreService.getChannel(channelId).get() }
            val messagesDeferred = async { messageFirestoreService.getMessages(channelId).get() }

            val channel = channelDeferred.await()

            personInternalReferenceService.getByChannelPersonId(channel.channelPersonId.toUUID()).flatMap {
                personService.get(it.personId).map { person ->
                    DeIdentificationMessage.deIdentifierAndRemoveHtml(messagesDeferred.await(), person, channel.channelPersonId,true)
                }
            }
        }

}
