package br.com.alice.channel.assistance.clients

import br.com.alice.channel.assistance.logics.CreateMediaConcatenationPipelineBuilder
import br.com.alice.channel.core.ServiceConfig
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.PersonId
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.FeatureNamespace
import io.mockk.called
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.chimesdkmediapipelines.ChimeSdkMediaPipelinesClient
import software.amazon.awssdk.services.chimesdkmediapipelines.model.CreateMediaCapturePipelineRequest
import software.amazon.awssdk.services.chimesdkmediapipelines.model.CreateMediaCapturePipelineResponse
import software.amazon.awssdk.services.chimesdkmediapipelines.model.CreateMediaConcatenationPipelineRequest
import software.amazon.awssdk.services.chimesdkmediapipelines.model.CreateMediaConcatenationPipelineResponse
import software.amazon.awssdk.services.chimesdkmediapipelines.model.DeleteMediaCapturePipelineRequest
import software.amazon.awssdk.services.chimesdkmediapipelines.model.MediaCapturePipeline
import software.amazon.awssdk.services.chimesdkmediapipelines.model.MediaConcatenationPipeline
import software.amazon.awssdk.services.chimesdkmediapipelines.model.MediaPipelineSinkType
import software.amazon.awssdk.services.chimesdkmediapipelines.model.MediaPipelineSourceType
import software.amazon.awssdk.services.chimesdkmeetings.ChimeSdkMeetingsClient
import software.amazon.awssdk.services.chimesdkmeetings.model.*
import kotlin.test.AfterTest
import kotlin.test.Test

class AmazonChimeClientTest {

    private val chimeSdkMeetingsClient: ChimeSdkMeetingsClient = mockk()
    private val chimeSdkMediaPipelinesClient: ChimeSdkMediaPipelinesClient = mockk()
    private val chimeClient =
        AmazonChimeClient(chimeSdkMeetingsClient, chimeSdkMediaPipelinesClient)

    private val meetingId = RangeUUID.generate()
    private val attendeeId = RangeUUID.generate()

    private val meeting = Meeting.builder().meetingId(meetingId.toString()).build()
    private val attendee = Attendee.builder().attendeeId(attendeeId.toString()).build()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        chimeSdkMeetingsClient,
        chimeSdkMediaPipelinesClient
    )

    @Test
    fun `#createMeeting create and return a new meeting`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = true
        ) {

            every {
                chimeSdkMeetingsClient.getMeeting(eq(GetMeetingRequest.builder().meetingId(meetingId.toString()).build()))
            } throws NotFoundException.builder().build()

            every {
                chimeSdkMeetingsClient.createMeeting(
                    eq(
                        CreateMeetingRequest.builder()
                            .mediaRegion(Region.US_EAST_1.toString())
                            .clientRequestToken(meetingId.toString())
                            .externalMeetingId(meetingId.toString())
                            .build()
                    )
                )
            } returns CreateMeetingResponse.builder().meeting(meeting).build()

            every {
                chimeSdkMeetingsClient.startMeetingTranscription(
                    StartMeetingTranscriptionRequest.builder()
                        .meetingId(meetingId.toString())
                        .transcriptionConfiguration(
                            TranscriptionConfiguration.builder()
                                .engineTranscribeSettings(
                                    EngineTranscribeSettings.builder()
                                        .partialResultsStability(TranscribePartialResultsStability.LOW)
                                        .languageCode(TranscribeLanguageCode.PT_BR)
                                        .region(TranscribeRegion.US_EAST_1)
                                        .build()
                                )
                                .build()
                        )
                        .build()
                )
            } returns StartMeetingTranscriptionResponse.builder().build()

            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyOnce { chimeSdkMeetingsClient.createMeeting(any<CreateMeetingRequest>()) }
            coVerifyOnce { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }

    @Test
    fun `#createMeeting create and return a new meeting and NOT start Transcription`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = false
        ) {

            every {
                chimeSdkMeetingsClient.getMeeting(eq(GetMeetingRequest.builder().meetingId(meetingId.toString()).build()))
            } throws NotFoundException.builder().build()

            every {
                chimeSdkMeetingsClient.createMeeting(
                    eq(
                        CreateMeetingRequest.builder()
                            .mediaRegion(Region.US_EAST_1.toString())
                            .clientRequestToken(meetingId.toString())
                            .externalMeetingId(meetingId.toString())
                            .build()
                    )
                )
            } returns CreateMeetingResponse.builder().meeting(meeting).build()

            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyOnce { chimeSdkMeetingsClient.createMeeting(any<CreateMeetingRequest>()) }
            coVerifyNone { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }

    @Test
    fun `#createMeeting returns a existent meeting`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = true
        ) {
            every {
                chimeSdkMeetingsClient.getMeeting(
                    eq(
                        GetMeetingRequest.builder().meetingId(meetingId.toString()).build()
                    )
                )
            } returns GetMeetingResponse.builder().meeting(meeting).build()

            every {
                chimeSdkMeetingsClient.startMeetingTranscription(
                    StartMeetingTranscriptionRequest.builder()
                        .meetingId(meetingId.toString())
                        .transcriptionConfiguration(
                            TranscriptionConfiguration.builder()
                                .engineTranscribeSettings(
                                    EngineTranscribeSettings.builder()
                                        .partialResultsStability(TranscribePartialResultsStability.LOW)
                                        .languageCode(TranscribeLanguageCode.PT_BR)
                                        .region(TranscribeRegion.US_EAST_1)
                                        .build()
                                )
                                .build()
                        )
                        .build()
                )
            } returns StartMeetingTranscriptionResponse.builder().build()


            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyNone { chimeSdkMeetingsClient.createMeeting(any<CreateMeetingRequest>()) }
            coVerifyOnce { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }

    @Test
    fun `#createMeeting returns a existent meeting and NOT start Transcription`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = false
        ) {
            every {
                chimeSdkMeetingsClient.getMeeting(
                    eq(
                        GetMeetingRequest.builder().meetingId(meetingId.toString()).build()
                    )
                )
            } returns GetMeetingResponse.builder().meeting(meeting).build()

            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyNone { chimeSdkMeetingsClient.createMeeting(any<CreateMeetingRequest>()) }
            coVerifyNone { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }


    @Test
    fun `#createAttendee create and return a new attendee`() = runBlocking {
        every {
            chimeSdkMeetingsClient.getAttendee(
                eq(
                    GetAttendeeRequest.builder()
                        .meetingId(meetingId.toString())
                        .attendeeId(attendeeId.toString())
                        .build()
                )
            )
        } throws NotFoundException.builder().build()

        every {
            chimeSdkMeetingsClient.createAttendee(
                eq(
                    CreateAttendeeRequest.builder()
                        .meetingId(meetingId.toString())
                        .externalUserId(attendeeId.toString())
                        .build()
                )
            )
        } returns CreateAttendeeResponse.builder().attendee(attendee).build()

        val attendeeCreated = chimeClient.createAttendee(meetingId, attendeeId)
        assertThat(attendeeCreated).isEqualTo(attendee)

        coVerifyOnce { chimeSdkMeetingsClient.getAttendee(any<GetAttendeeRequest>()) }
        coVerifyOnce { chimeSdkMeetingsClient.createAttendee(any<CreateAttendeeRequest>()) }
        coVerify { chimeSdkMediaPipelinesClient wasNot called }
    }

    @Test
    fun `#createAttendee returns a existent attendee`() = runBlocking {
        every {
            chimeSdkMeetingsClient.getAttendee(
                eq(
                    GetAttendeeRequest.builder()
                        .meetingId(meetingId.toString())
                        .attendeeId(attendeeId.toString())
                        .build()
                )
            )
        } returns GetAttendeeResponse.builder().attendee(attendee).build()

        val attendeeCreated = chimeClient.createAttendee(meetingId, attendeeId)
        assertThat(attendeeCreated).isEqualTo(attendee)

        coVerifyOnce { chimeSdkMeetingsClient.getAttendee(any<GetAttendeeRequest>()) }
        coVerifyNone { chimeSdkMeetingsClient.createAttendee(any<CreateAttendeeRequest>()) }
        coVerify { chimeSdkMediaPipelinesClient wasNot called }
    }

    @Test
    fun `#endMeeting should end meeting in Chime`() = runBlocking {
        every {
            chimeSdkMeetingsClient.deleteMeeting(
                eq(
                    DeleteMeetingRequest.builder()
                        .meetingId(meetingId.toString())
                        .build()
                )
            )
        } returns DeleteMeetingResponse.builder().build()

        chimeClient.endMeeting(meetingId)

        coVerifyOnce { chimeSdkMeetingsClient.deleteMeeting(any<DeleteMeetingRequest>()) }
        coVerify { chimeSdkMediaPipelinesClient wasNot called }
    }

    @Test
    fun `#removeAttendee should delete attendee from meeting in Chime`() = runBlocking {
        every {
            chimeSdkMeetingsClient.deleteAttendee(
                eq(
                    DeleteAttendeeRequest.builder()
                        .meetingId(meetingId.toString())
                        .attendeeId(attendeeId.toString())
                        .build()
                )
            )
        } returns DeleteAttendeeResponse.builder().build()

        chimeClient.removeAttendee(meetingId, attendeeId)

        coVerifyOnce { chimeSdkMeetingsClient.deleteAttendee(any<DeleteAttendeeRequest>()) }
        coVerify { chimeSdkMediaPipelinesClient wasNot called }
    }

    @Test
    fun `#getMeeting returns a existent meeting`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = true
        ) {
            every {
                chimeSdkMeetingsClient.getMeeting(
                    eq(
                        GetMeetingRequest.builder().meetingId(meetingId.toString()).build()
                    )
                )
            } returns GetMeetingResponse.builder().meeting(meeting).build()

            every {
                chimeSdkMeetingsClient.startMeetingTranscription(
                    StartMeetingTranscriptionRequest.builder()
                        .meetingId(meetingId.toString())
                        .transcriptionConfiguration(
                            TranscriptionConfiguration.builder()
                                .engineTranscribeSettings(
                                    EngineTranscribeSettings.builder()
                                        .partialResultsStability(TranscribePartialResultsStability.LOW)
                                        .languageCode(TranscribeLanguageCode.PT_BR)
                                        .region(TranscribeRegion.US_EAST_1)
                                        .build()
                                )
                                .build()
                        )
                        .build()
                )
            } returns StartMeetingTranscriptionResponse.builder().build()

            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyOnce { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }

    @Test
    fun `#getMeeting returns a existent meeting and NOT start Transcription`() = runBlocking {
        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_using_media_capture_in_video_call",
            value = false
        ) {
            every {
                chimeSdkMeetingsClient.getMeeting(
                    eq(
                        GetMeetingRequest.builder().meetingId(meetingId.toString()).build()
                    )
                )
            } returns GetMeetingResponse.builder().meeting(meeting).build()

            val meetingCreated = chimeClient.createMeeting(meetingId)
            assertThat(meetingCreated).isEqualTo(meeting)

            coVerifyOnce { chimeSdkMeetingsClient.getMeeting(any<GetMeetingRequest>()) }
            coVerifyNone { chimeSdkMeetingsClient.startMeetingTranscription(any<StartMeetingTranscriptionRequest>()) }
            coVerify { chimeSdkMediaPipelinesClient wasNot called }
        }
    }


    @Test
    fun `#listAttendees returns existing meeting attendees`() = runBlocking {
        every {
            chimeSdkMeetingsClient.listAttendees(
                eq(
                    ListAttendeesRequest.builder().meetingId(meetingId.toString()).build()
                )
            )
        } returns ListAttendeesResponse.builder().attendees(attendee).build()

        val listAttendees = chimeClient.listAttendees(meetingId)
        assertThat(listAttendees).isEqualTo(listOf(attendee))

        coVerifyOnce { chimeSdkMeetingsClient.listAttendees(any<ListAttendeesRequest>()) }
        coVerify { chimeSdkMediaPipelinesClient wasNot called }
    }

    @Test
    fun `#createMediaCapturePipeline create a media capture pipeline for meeting and return the id`() = runBlocking {
        val personId = PersonId()
        val channelId = "xpto"
        val mediaCaptureId = RangeUUID.generate().toString()
        val mediaCapturePipeline = MediaCapturePipeline.builder().mediaPipelineId(mediaCaptureId)
            .mediaPipelineArn("arn:aws:chime::${BaseConfig.instance.awsAccount}:meeting:$meetingId").build()
        every {
            chimeSdkMediaPipelinesClient.createMediaCapturePipeline(
                eq(
                    CreateMediaCapturePipelineRequest
                        .builder()
                        .sourceType(MediaPipelineSourceType.CHIME_SDK_MEETING)
                        .sourceArn("arn:aws:chime::${BaseConfig.instance.awsAccount}:meeting:$meetingId")
                        .sinkType(MediaPipelineSinkType.S3_BUCKET)
                        .sinkArn("arn:aws:s3:::${ServiceConfig.VIDEO_CALL_BUCKET}/$personId/$channelId/$meetingId")
                        .build()
                )
            )
        } returns CreateMediaCapturePipelineResponse.builder().mediaCapturePipeline(
            mediaCapturePipeline
        ).build()

        val mediaCaptureIdResult = chimeClient.createMediaCapturePipeline(meetingId, personId, channelId)
        assertThat(mediaCaptureIdResult).isEqualTo(mediaCapturePipeline)

        coVerifyOnce { chimeSdkMediaPipelinesClient.createMediaCapturePipeline(any<CreateMediaCapturePipelineRequest>()) }
        coVerify { chimeSdkMeetingsClient wasNot called }
    }

    @Test
    fun `#deleteMediaCapturePipeline delete the media capture pipeline`() = runBlocking {
        val mediaCaptureId = RangeUUID.generate()
        every {
            chimeSdkMediaPipelinesClient.deleteMediaCapturePipeline(
                eq(
                    DeleteMediaCapturePipelineRequest.builder()
                        .mediaPipelineId(mediaCaptureId.toString())
                        .build()
                )
            )
        } answers { nothing }

        chimeClient.deleteMediaCapturePipeline(mediaCaptureId)

        coVerifyOnce { chimeSdkMediaPipelinesClient.deleteMediaCapturePipeline(any<DeleteMediaCapturePipelineRequest>()) }
        coVerify { chimeSdkMeetingsClient wasNot called }
    }

    @Test
    fun `#createMediaConcatenationPipeline create a media capture pipeline for meeting and return the id`() = runBlocking {
        val personId = PersonId()
        val channelId = "xpto"
        val mediaCaptureId = RangeUUID.generate().toString()
        val mediaCapturePipeline = MediaCapturePipeline.builder().mediaPipelineId(mediaCaptureId)
            .mediaPipelineArn("arn:aws:chime::${BaseConfig.instance.awsAccount}:meeting:$meetingId")
            .sinkArn("arn:aws:s3:::${ServiceConfig.VIDEO_CALL_BUCKET}/$personId/$channelId/$meetingId").build()

        val captureConcatenation = CreateMediaConcatenationPipelineBuilder.build(
            mediaCapturePipeline.mediaPipelineId(),
            mediaCapturePipeline.mediaPipelineArn(),
            mediaCapturePipeline.sinkArn()
        )


        every {
            chimeSdkMediaPipelinesClient.createMediaCapturePipeline(
                eq(
                    CreateMediaCapturePipelineRequest
                        .builder()
                        .sourceType(MediaPipelineSourceType.CHIME_SDK_MEETING)
                        .sourceArn("arn:aws:chime::${BaseConfig.instance.awsAccount}:meeting:$meetingId")
                        .sinkType(MediaPipelineSinkType.S3_BUCKET)
                        .sinkArn("arn:aws:s3:::${ServiceConfig.VIDEO_CALL_BUCKET}/$personId/$channelId/$meetingId")
                        .build()
                )
            )
        } returns CreateMediaCapturePipelineResponse.builder().mediaCapturePipeline(
            mediaCapturePipeline
        ).build()

        every {
            chimeSdkMediaPipelinesClient.createMediaConcatenationPipeline(captureConcatenation)
        } returns CreateMediaConcatenationPipelineResponse.builder().mediaConcatenationPipeline(
            MediaConcatenationPipeline.builder().mediaPipelineId(mediaCaptureId).build()
        ).build()

        val mediaCaptureIdResult = chimeClient.createMediaConcatenationPipeline(meetingId, personId, channelId)
        assertThat(mediaCaptureIdResult).isEqualTo(mediaCaptureId)

        coVerifyOnce { chimeSdkMediaPipelinesClient.createMediaConcatenationPipeline(any<CreateMediaConcatenationPipelineRequest>()) }
        coVerifyOnce { chimeSdkMediaPipelinesClient.createMediaCapturePipeline(any<CreateMediaCapturePipelineRequest>()) }
    }
}
