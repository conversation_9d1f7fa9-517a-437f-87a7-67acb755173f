package br.com.alice.channel.core.consumers

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.RoutingService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction.ADD_STAFF
import br.com.alice.data.layer.models.ChannelChangeAction.CREATE_CHAT
import br.com.alice.data.layer.models.ChannelChangeAction.RE_ROUTING
import br.com.alice.data.layer.models.ChannelChangeAction.SCREENING
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelType
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class RoutingConsumerTest : ConsumerTest() {

    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val routingService: RoutingService = mockk()

    private val routingConsumer = RoutingConsumer(
        channelFirestoreService,
        routingService
    )

    private val personId = PersonId()
    private val channelId = "channelId"
    private val payload = ChannelUpsertedPayload(
        channelId = channelId,
        personId = personId,
        type = ChannelType.CHAT,
        kind = ChannelKind.CHAT,
        category = ChannelCategory.ASSISTANCE,
        status = ChannelStatus.ACTIVE,
        action = SCREENING,
        staffIds = listOf(RangeUUID.generate()),
        appVersion = "3.0.0"
    )
    private val event = ChannelUpsertedEvent(payload)

    private val chat = ChannelDocument(
        channelPersonId = "channelPersonId",
        id = event.payload.channelId,
        personId = event.payload.personId.toString(),
        type = event.payload.type,
        kind = event.payload.kind,
        category = event.payload.category,
        status = event.payload.status,
        staffIds = event.payload.staffIds.map { it.toString() },
        appVersion = "9.0.0"
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        channelFirestoreService,
        routingService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#routeChat returns channel id after routing chat`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns chat.success()
        coEvery { routingService.routeChat(chat) } returns channelId.success()

        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { routingService.routeChat(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#routeChat returns channel id after routing chat is acute`() = runBlocking {
        val event = ChannelUpsertedEvent(payload.copy(subCategory = ChannelSubCategory.ACUTE))

        coEvery { channelFirestoreService.getChannel(channelId) } returns chat.success()
        coEvery { routingService.routeChat(chat) } returns channelId.success()

        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { routingService.routeChat(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#routeChat returns channel id after routing chat by RE_ROUTING event`() = runBlocking {
        val now = Timestamp.now()
        val event = event.copy(channelUpserted = event.payload.copy(action = RE_ROUTING))
        val chatToRouting = chat.copy(createdAt = now)

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns now
            coEvery { channelFirestoreService.getChannel(channelId) } returns chat.success()
            coEvery { routingService.routeChat(chatToRouting) } returns channelId.success()

            val result = routingConsumer.routeChat(event)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelFirestoreService.getChannel(any()) }
            coVerifyOnce { routingService.routeChat(any()) }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#routeChat returns channel id after routing chat screened`() = runBlocking {
        val event = event.copy(channelUpserted = event.payload)

        coEvery { channelFirestoreService.getChannel(channelId) } returns chat.success()
        coEvery { routingService.routeChat(chat) } returns channelId.success()

        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { routingService.routeChat(any()) }
    }

    @Test
    fun `#routeChat returns false when event is CREATE_CHAT`() = runBlocking {
        val event = event.copy(channelUpserted = event.payload.copy(action = CREATE_CHAT))
        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { channelFirestoreService wasNot called }
        coVerify { routingService wasNot called }
    }

    @Test
    fun `#routeChat returns false when event is CREATE_CHAT and chat is LONGITUDINAL`() = runBlocking {
        val event = event.copy(
            channelUpserted = event.payload.copy(
                action = CREATE_CHAT,
                subCategory = ChannelSubCategory.LONGITUDINAL
            )
        )
        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { channelFirestoreService wasNot called }
        coVerify { routingService wasNot called }
    }

    @Test
    fun `#routeChat returns false when event action not is CREATE_CHAT`() = runBlocking {
        val event = event.copy(channelUpserted = event.payload.copy(action = ADD_STAFF))

        val result = routingConsumer.routeChat(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { channelFirestoreService wasNot called }
        coVerify { routingService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#routeChat returns channel Id when an error occurs to routing chat`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns chat.success()
        coEvery { routingService.routeChat(chat) } returns Exception().failure()

        val result = routingConsumer.routeChat(event)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerifyOnce { routingService.routeChat(any()) }
    }

    @Test
    fun `#routeChat returns error when an error occurs to get chat`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(channelId) } returns Exception().failure()

        val result = routingConsumer.routeChat(event)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { channelFirestoreService.getChannel(any()) }
        coVerify { routingService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#clearChatCaught returns channel id and remove values from routingTo`() = runBlocking {
        val event = event.copy(channelUpserted = event.payload.copy(action = ADD_STAFF))

        coEvery { routingService.clearChatCaught(channelId) } returns channelId.success()

        val result = routingConsumer.clearChatCaught(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { routingService.clearChatCaught(any()) }
        coVerify { channelFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#clearChatCaught returns error when an error occurs to clear`() = runBlocking {
        val event = event.copy(channelUpserted = event.payload.copy(action = ADD_STAFF))

        coEvery { routingService.clearChatCaught(channelId) } returns Exception().failure()

        val result = routingConsumer.clearChatCaught(event)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { routingService.clearChatCaught(any()) }
        coVerify { channelFirestoreService wasNot called }
    }

    @Test
    fun `#clearChatCaught returns false when event action is invalid`() = runBlocking {
        val result = routingConsumer.clearChatCaught(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { channelFirestoreService wasNot called }
        coVerify { routingService wasNot called }
    }

}
