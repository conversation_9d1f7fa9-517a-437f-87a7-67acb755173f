package br.com.alice.channel.core.consumers

import br.com.alice.channel.client.ChannelHistoryService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.zendesk.events.ZendeskTicketCreatedByChannelEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test
import br.com.alice.channel.core.services.internal.ChannelService as InternalChannelService

class ChannelConsumerTest : ConsumerTest() {

    private val channelHistoryService: ChannelHistoryService = mockk()
    private val internalChannelService: InternalChannelService = mockk()

    private val consumer = ChannelConsumer(
        channelHistoryService,
        internalChannelService
    )
    private val channelId = "channel_id"
    private val personId = PersonId()
    private val staffIds = listOf(RangeUUID.generate().toString())
    private val event = ChannelUpsertedEvent(
        ChannelUpsertedPayload(
            channelId = channelId,
            personId = personId,
            type = ChannelType.HEALTH_PLAN,
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
            subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
            status = ChannelStatus.ACTIVE,
            action = ChannelChangeAction.MERGE,
            staffIds = staffIds.map { it.toUUID() },
            appVersion = "3.30.1"
        )
    )

    private val lastStaff = TestModelFactory.buildStaff()
    private val lastStaffId = lastStaff.id.toString()
    private val channelStaffInfo = ChannelStaffInfo(
        id = lastStaffId,
        firstName = lastStaff.firstName,
        lastName = lastStaff.lastName,
        description = "xpto",
        profileImageUrl = lastStaff.profileImageUrl.toString()
    )

    val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = personId.toString(),
        personId = personId.toString(),
        name = "channelName",
        type = ChannelType.CHAT,
        status = ChannelStatus.ACTIVE,
        kind = ChannelKind.CHANNEL,
        staffIds = listOf(),
        staffHistory = mutableMapOf(lastStaffId to channelStaffInfo)
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        channelHistoryService,
        internalChannelService
    )

    @Test
    fun `#channelUpsertedEvent should save channel history`() = mockRangeUUID { uuid ->
        val channelHistory = event.payload.convertTo(ChannelHistory::class).copy(id = uuid, eventDate = event.eventDate)

        coEvery { channelHistoryService.create(channelHistory) } returns channelHistory.success()

        val result = consumer.createChannelHistory(event)
        assertThat(result).isSuccessWithData(channelHistory)

        coVerifyOnce { channelHistoryService.create(any()) }
    }

    @Test
    fun `#channelUpsertedEvent return false when duplicated`() = mockRangeUUID { uuid ->
        val channelHistory = event.payload.convertTo(ChannelHistory::class).copy(id = uuid, eventDate = event.eventDate)

        coEvery { channelHistoryService.create(channelHistory) } returns DuplicatedItemException("").failure()

        val result = consumer.createChannelHistory(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { channelHistoryService.create(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#updateChannelWithZendeskTicket should update channel`() = runBlocking {
        val event = ZendeskTicketCreatedByChannelEvent("channelId", "zendeskTicketId")
        coEvery {
            internalChannelService.updateWithZendeskTicketData(
                event.payload.channelId,
                event.payload.zendeskTicketId
            )
        } returns "12345".success()

        val result = consumer.updateChannelWithZendeskTicket(event)
        assertThat(result).isSuccessWithData("12345")

        coVerifyOnce { internalChannelService.updateWithZendeskTicketData(any(), any()) }
    }

}
