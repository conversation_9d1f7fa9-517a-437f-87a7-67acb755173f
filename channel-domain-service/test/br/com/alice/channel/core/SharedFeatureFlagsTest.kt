package br.com.alice.channel.core

import br.com.alice.channel.core.util.canUseAiTriageAssistanceNewFlow
import br.com.alice.channel.core.util.getAiAssistantIdForAssistantChat
import br.com.alice.channel.core.util.getAiDropPhrasesForAssistantChat
import br.com.alice.channel.core.util.getMaxAiMessagesCountToDropForAssistantChat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.data.layer.models.FeatureNamespace
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SharedFeatureFlagsTest {


    @Test
    fun `#getAiAssistantIdForAssistantChat returns value from feature flag`() = runBlocking {
        assertThat(getAiAssistantIdForAssistantChat()).isEqualTo("")

        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "triage_ai_assistant_id",
            value = "assistant_id_123"
        ) {
            assertThat(getAiAssistantIdForAssistantChat()).isEqualTo("assistant_id_123")
        }
    }

    @Test
    fun `#getAiDropPhrasesForAssistantChat returns value from feature flag`() = runBlocking {
        assertThat(getAiDropPhrasesForAssistantChat()).isEqualTo(emptyList<String>())

        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "triage_ai_drop_phrases",
            value = listOf("phrase_1", "phrase_2")
        ) {
            assertThat(getAiDropPhrasesForAssistantChat()).isEqualTo(listOf("phrase_1", "phrase_2"))
        }
    }

    @Test
    fun `#getMaxAiMessagesCountToDropForAssistantChat returns value from feature flag`() = runBlocking {
        assertThat(getMaxAiMessagesCountToDropForAssistantChat()).isEqualTo(10)

        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "triage_ai_max_message_count_to_drop",
            value = 15
        ) {
            assertThat(getMaxAiMessagesCountToDropForAssistantChat()).isEqualTo(15)
        }
    }

    @Test
    fun `#canUseAiTriageAssistanceNewFlow returns value from feature flag`() = runBlocking {
        assertThat(canUseAiTriageAssistanceNewFlow()).isFalse

        withFeatureFlag(
            namespace = FeatureNamespace.CHANNELS,
            key = "enable_triage_ai_assistance_new_flow",
            value = true
        ) {
            assertThat(canUseAiTriageAssistanceNewFlow()).isTrue
        }
    }
}
