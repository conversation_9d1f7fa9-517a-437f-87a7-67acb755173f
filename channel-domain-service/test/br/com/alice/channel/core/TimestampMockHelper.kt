package br.com.alice.channel.core

import com.google.cloud.Timestamp
import io.mockk.every
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking

fun mockTimestamp(timestamp: Timestamp, test: suspend () -> Unit) = runBlocking {
    mockkStatic(Timestamp::class) {
        every { Timestamp.now() } returns timestamp
        test()
    }
}

fun mockTimestamp(test: suspend (timestamp: Timestamp) -> Unit) = runBlocking {
    val timestamp = Timestamp.now()
    mockkStatic(Timestamp::class) {
        every { Timestamp.now() } returns timestamp
        test(timestamp)
    }
}
