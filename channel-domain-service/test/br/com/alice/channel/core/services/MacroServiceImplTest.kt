package br.com.alice.channel.core.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.models.ChannelMacro
import br.com.alice.data.layer.services.ChannelMacroDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class MacroServiceImplTest {

    private val channelMacroDataService: ChannelMacroDataService = mockk()
    private val service = MacroServiceImpl(channelMacroDataService)
    private val shortcutString = "shortcut"
    private val macro = ChannelMacro(shortcutString, "Oi @userName, como podemos te ajudar hoje?")
    private val macros = listOf(
        macro,
        ChannelMacro(shortcut = "channel_macro", message = "message"),
        ChannelMacro(shortcut = "channel_macro_again", message = "message")
    )

    @Test
    fun `#find should return empty list when not found by shortcut`() = runBlocking {

        coEvery {
            channelMacroDataService.find(queryEq { where { this.shortcut.like(shortcutString) } })
        } returns Result.success(listOf())

        val actual = service.find(shortcutString)

        assertThat(actual).isSuccess()
        assertThat(actual.get()).isEmpty()
    }

    @Test
    fun `#find should return list when found by shortcut`() = runBlocking<Unit> {
        coEvery {
            channelMacroDataService.find(queryEq { where { this.shortcut.like(shortcutString) } })
        } returns Result.success(listOf(macro))

        val actual = service.find(shortcutString)

        assertThat(actual).isSuccess()
        assertThat(actual.get().first()).isEqualTo(macro)
    }

    @Test
    fun `#getByRange should return success`() = runBlocking<Unit> {
        val range = IntRange(0, 19)

        coEvery {
            channelMacroDataService.find(queryEq {
                orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(macro).success()

        val result = service.getByRange(range)

        assertThat(result.get()).containsOnly(macro)
    }

    @Test
    fun `#getByFilterAndRange should return success`() = runBlocking<Unit> {
        val range = IntRange(0, 19)
        val filter = "channel_macro"
        val filteredMacros = macros.filter { it.shortcut.startsWith("channel_macro") }

        coEvery {
            channelMacroDataService.find(queryEq {
                where { shortcut.like(filter) }
                    .orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns filteredMacros.success()

        val result = service.getByFilterAndRange(range, filter)

        assertThat(result.get()).containsAll(filteredMacros)
    }

    @Test
    fun `#get should find macro and return success`() = runBlocking {
        coEvery { channelMacroDataService.get(macro.id) } returns macro.success()

        val result = service.get(macro.id.toString())

        assertThat(result).isSuccessWithData(macro)
    }

    @Test
    fun `#get should not find macro and return failure`() = runBlocking {
        val id = RangeUUID.generate()

        coEvery { channelMacroDataService.get(id) } returns NotFoundException("Macro '$id' not found").failure()

        val result = service.get(id.toString())

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#add should create new macro and return success`() = runBlocking {
        coEvery { channelMacroDataService.add(macro) } returns macro.success()

        val result = service.create(macro)

        assertThat(result).isSuccessWithData(macro)
    }

    @Test
    fun `#update should update macro and return success`() = runBlocking {
        val updated = macro.copy(message = "new message")

        coEvery { channelMacroDataService.update(macro) } returns Result.success(updated)

        val result = service.update(macro)

        assertThat(result).isSuccessWithData(updated)
    }

    @Test
    fun `#count should count all`() = runBlocking<Unit> {
        coEvery { channelMacroDataService.count(queryEq { all() }) } returns 1.success()

        val result = service.count()

        assertThat(result.get()).isEqualTo(1)
    }

    @Test
    fun `#getAll should return all`() = runBlocking {
        coEvery { channelMacroDataService.find(queryEq { all() }) } returns listOf(macro).success()

        val result = service.getAll()

        assertThat(result).isSuccessWithData(listOf(macro))
    }

    @Test
    fun `#delete should delete macro and return true`() = runBlocking {
        coEvery { channelMacroDataService.delete(macro) } returns true.success()

        val result = service.delete(macro)

        assertThat(result).isSuccessWithData(true)
    }
}
