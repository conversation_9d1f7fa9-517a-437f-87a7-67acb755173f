package br.com.alice.channel.core.services.internal.logics

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageChannelMergedFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.event.MergedChannelDemandEvent
import br.com.alice.channel.event.MergedChannelDemandEventPayload
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelStatus.MERGED
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.Demand
import br.com.alice.common.Disease
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelMergeServiceTest {

    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val messageChannelMergedFirestoreService: MessageChannelMergedFirestoreService = mockk()
    private val channelNotificationService: ChannelNotificationService = mockk()

    private val channelMergeService = ChannelMergeService(
        channelFirestoreService,
        messageFirestoreService,
        messageChannelMergedFirestoreService,
        channelNotificationService
    )

    private val now = Timestamp.now()
    private val personId = PersonId().toString()
    private val channelPersonId = RangeUUID.generate().toString()
    private val staffId1 = RangeUUID.generate().toString()
    private val staffId2 = RangeUUID.generate().toString()
    private val originChannelId = "originChannelId"
    private val destinationChannelId = "destinationChannelId"
    private val messageId = "messageId"

    private val staff1 = ChannelStaffInfo(
        id = staffId1,
        name = "Felipe",
        firstName = "Felipe",
        lastName = "Praça",
        description = "médico",
        profileImageUrl = ""
    )

    private val staff2 = ChannelStaffInfo(
        id = staffId2,
        name = "Agua",
        firstName = "Agua",
        lastName = "Luza",
        description = "médico",
        profileImageUrl = ""
    )

    private val demands = listOf(
        Demand(
            caseId = RangeUUID.generate(),
            startedAt = LocalDateTime.now(),
            description = Disease(
                type = Disease.Type.CID_10,
                value = "A10",
                description = "CID A10"
            ),
            severity = CaseSeverity.DECOMPENSATED
        )
    )

    private val appointmentIds = listOf(RangeUUID.generate())

    private val origin = ChannelDocument(
        id = originChannelId,
        channelPersonId = channelPersonId,
        personId = personId,
        status = ChannelStatus.ACTIVE,
        kind = ChannelKind.CHAT,
        subCategoryClassifier = null,
        staff = mutableMapOf(staffId1 to staff1),
        demands = demands,
        appointmentIds = appointmentIds
    )

    private val destination = ChannelDocument(
        id = destinationChannelId,
        channelPersonId = channelPersonId,
        personId = personId,
        status = ChannelStatus.ACTIVE,
        kind = ChannelKind.CHANNEL,
        subCategoryClassifier = null,
        staff = mutableMapOf(staffId2 to staff2)
    )

    private val messageDocument = MessageDocument(
        id = messageId,
        aliceId = staffId1,
        userId = staffId1,
        content = "content",
        type = MessageType.TEXT
    )

    @BeforeTest
    fun setup() {
        mockkStatic(Timestamp::class)
        every { Timestamp.now() } returns now
    }

    @AfterTest
    fun confirmVerified() = confirmVerified(
        channelFirestoreService,
        messageFirestoreService,
        messageChannelMergedFirestoreService,
        channelNotificationService
    )

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id, copy messages, update fields and publish event`() = runBlocking {
        val messageToCreate = messageDocument.copy(originChannelId = originChannelId)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        coEvery {
            messageFirestoreService.getMessages(
                originChannelId,
                false
            )
        } returns listOf(messageDocument).success()
        coEvery { messageFirestoreService.getMessages(originChannelId, true) } returns listOf(messageDocument).success()

        coEvery {
            channelFirestoreService.updateFields(
                originChannelId,
                mapOf(
                    ChannelDocument::status to MERGED,
                    ChannelDocument::mergedWith to destinationChannelId
                )
            )
        } returns originChannelId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, false)
        } returns messageId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, true)
        } returns messageId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::timeLastMessage to messageToCreate.createdAt,
                    ChannelDocument::lastPreviewableMessage to messageToCreate
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::demands to demands,
                    ChannelDocument::appointmentIds to appointmentIds,
                    ChannelDocument::staff to mutableMapOf(staffId2 to staff2, staffId1 to staff1),
                    ChannelDocument::staffHistory to emptyMap<String, ChannelStaffInfo>(),
                    ChannelDocument::staffIds to listOf(staffId2, staffId1),
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            messageFirestoreService.add(
                destinationChannelId,
                MessageDocument(
                    aliceId = staffId1,
                    userId = staffId1,
                    content = MessageType.CHANNEL_MERGED.description,
                    type = MessageType.CHANNEL_MERGED,
                    createdAt = now
                )
            )
        } returns messageId.success()

        coEvery {
            channelNotificationService.produceGenericEvent(match { event: MergedChannelDemandEvent ->
                event.payload == MergedChannelDemandEventPayload(
                    originChannelId,
                    destinationChannelId,
                    demands.map { it.caseId },
                    appointmentIds
                )
            })
        } returns mockk()

        coEvery {
            channelNotificationService.notify(
                origin.copy(status = MERGED, mergedWith = destinationChannelId),
                ChannelChangeAction.MERGE
            )
        } returns mockk()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isSuccessWithData(destinationChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 3) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 3) { messageFirestoreService.add(any(), any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any(), any()) }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error to find origin channel`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(originChannelId) } returns Exception().failure()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(Exception::class)

        coVerify(exactly = 1) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error to find destination channel`() = runBlocking {
        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns Exception().failure()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(Exception::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error when destination is not a channel`() = runBlocking {
        val destination = destination.copy(kind = ChannelKind.CHAT)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error when destination is not active`() = runBlocking {
        val destination = destination.copy(status = MERGED)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error when origin is not active`() = runBlocking {
        val origin = origin.copy(status = MERGED)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error when origin has subCategoryClassifier`() = runBlocking {
        val origin = origin.copy(subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns error when the members are different`() = runBlocking {
        val origin = origin.copy(channelPersonId = "")

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 0) { channelFirestoreService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
        coVerify { channelNotificationService wasNot called }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id, copy messages, update fields but not publish event when origin not have demand`() = runBlocking {
        val origin = origin.copy(demands = emptyList())
        val messageToCreate = messageDocument.copy(originChannelId = originChannelId)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        coEvery {
            messageFirestoreService.getMessages(
                originChannelId,
                false
            )
        } returns listOf(messageDocument).success()
        coEvery { messageFirestoreService.getMessages(originChannelId, true) } returns listOf(messageDocument).success()

        coEvery {
            channelFirestoreService.updateFields(
                originChannelId,
                mapOf(
                    ChannelDocument::status to MERGED,
                    ChannelDocument::mergedWith to destinationChannelId
                )
            )
        } returns originChannelId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, false)
        } returns messageId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, true)
        } returns messageId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::timeLastMessage to messageToCreate.createdAt,
                    ChannelDocument::lastPreviewableMessage to messageToCreate
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::demands to emptyList<Demand>(),
                    ChannelDocument::appointmentIds to appointmentIds,
                    ChannelDocument::staff to mutableMapOf(staffId2 to staff2, staffId1 to staff1),
                    ChannelDocument::staffHistory to emptyMap<String, ChannelStaffInfo>(),
                    ChannelDocument::staffIds to listOf(staffId2, staffId1),
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            messageFirestoreService.add(
                destinationChannelId,
                MessageDocument(
                    aliceId = staffId1,
                    userId = staffId1,
                    content = MessageType.CHANNEL_MERGED.description,
                    type = MessageType.CHANNEL_MERGED,
                    createdAt = now
                )
            )
        } returns messageId.success()

        coEvery {
            channelNotificationService.notify(
                origin.copy(status = MERGED, mergedWith = destinationChannelId),
                ChannelChangeAction.MERGE
            )
        } returns mockk()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isSuccessWithData(destinationChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 3) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 3) { messageFirestoreService.add(any(), any(), any()) }
        coVerify(exactly = 1) { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id but not send merged message`() = runBlocking {
        val messageToCreate = messageDocument.copy(originChannelId = originChannelId)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        coEvery {
            messageFirestoreService.getMessages(
                originChannelId,
                false
            )
        } returns listOf(messageDocument).success()
        coEvery { messageFirestoreService.getMessages(originChannelId, true) } returns listOf(messageDocument).success()

        coEvery {
            channelFirestoreService.updateFields(
                originChannelId,
                mapOf(
                    ChannelDocument::status to MERGED,
                    ChannelDocument::mergedWith to destinationChannelId
                )
            )
        } returns originChannelId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, false)
        } returns messageId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, true)
        } returns messageId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::timeLastMessage to messageToCreate.createdAt,
                    ChannelDocument::lastPreviewableMessage to messageToCreate
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::demands to demands,
                    ChannelDocument::appointmentIds to appointmentIds,
                    ChannelDocument::staff to mutableMapOf(staffId2 to staff2, staffId1 to staff1),
                    ChannelDocument::staffHistory to emptyMap<String, ChannelStaffInfo>(),
                    ChannelDocument::staffIds to listOf(staffId2, staffId1),
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            channelNotificationService.produceGenericEvent(match { event: MergedChannelDemandEvent ->
                event.payload == MergedChannelDemandEventPayload(
                    originChannelId,
                    destinationChannelId,
                    demands.map { it.caseId },
                    appointmentIds
                )
            })
        } returns mockk()

        coEvery {
            channelNotificationService.notify(
                origin.copy(status = MERGED, mergedWith = destinationChannelId),
                ChannelChangeAction.MERGE
            )
        } returns mockk()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, RangeUUID.generate().toString())
        assertThat(result).isSuccessWithData(destinationChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 3) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 2) { messageFirestoreService.add(any(), any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any(), any()) }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id but not update lastPreviewableMessage`() = runBlocking {
        val destination = destination.copy(lastPreviewableMessage = messageDocument)
        val messageToCreate = messageDocument.copy(originChannelId = originChannelId)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        coEvery {
            messageFirestoreService.getMessages(
                originChannelId,
                false
            )
        } returns listOf(messageDocument).success()
        coEvery { messageFirestoreService.getMessages(originChannelId, true) } returns listOf(messageDocument).success()

        coEvery {
            channelFirestoreService.updateFields(
                originChannelId,
                mapOf(
                    ChannelDocument::status to MERGED,
                    ChannelDocument::mergedWith to destinationChannelId
                )
            )
        } returns originChannelId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, false)
        } returns messageId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, true)
        } returns messageId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::demands to demands,
                    ChannelDocument::appointmentIds to appointmentIds,
                    ChannelDocument::staff to mutableMapOf(staffId2 to staff2, staffId1 to staff1),
                    ChannelDocument::staffHistory to emptyMap<String, ChannelStaffInfo>(),
                    ChannelDocument::staffIds to listOf(staffId2, staffId1),
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            messageFirestoreService.add(
                destinationChannelId,
                MessageDocument(
                    aliceId = staffId1,
                    userId = staffId1,
                    content = MessageType.CHANNEL_MERGED.description,
                    type = MessageType.CHANNEL_MERGED,
                    createdAt = now
                )
            )
        } returns messageId.success()

        coEvery {
            channelNotificationService.produceGenericEvent(match { event: MergedChannelDemandEvent ->
                event.payload == MergedChannelDemandEventPayload(
                    originChannelId,
                    destinationChannelId,
                    demands.map { it.caseId },
                    appointmentIds
                )
            })
        } returns mockk()

        coEvery {
            channelNotificationService.notify(
                origin.copy(status = MERGED, mergedWith = destinationChannelId),
                ChannelChangeAction.MERGE
            )
        } returns mockk()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isSuccessWithData(destinationChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 2) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 3) { messageFirestoreService.add(any(), any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any(), any()) }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#merge returns destination channel id but not copy add staff message`() = runBlocking {
        val messageAddStaff = messageDocument.copy(type = MessageType.ADD_PARTICIPANT)
        val messageToCreate = messageDocument.copy(originChannelId = originChannelId)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns origin.success()
        coEvery { channelFirestoreService.getChannel(destinationChannelId) } returns destination.success()

        coEvery {
            messageFirestoreService.getMessages(originChannelId, false)
        } returns listOf(messageAddStaff).success()
        coEvery {
            messageFirestoreService.getMessages(originChannelId, true)
        } returns listOf(messageDocument).success()

        coEvery {
            channelFirestoreService.updateFields(
                originChannelId,
                mapOf(
                    ChannelDocument::status to MERGED,
                    ChannelDocument::mergedWith to destinationChannelId
                )
            )
        } returns originChannelId.success()

        coEvery {
            messageFirestoreService.add(destinationChannelId, messageToCreate, true)
        } returns messageId.success()

        coEvery {
            channelFirestoreService.updateFields(
                destinationChannelId,
                mapOf(
                    ChannelDocument::demands to demands,
                    ChannelDocument::appointmentIds to appointmentIds,
                    ChannelDocument::staff to mutableMapOf(staffId2 to staff2, staffId1 to staff1),
                    ChannelDocument::staffHistory to emptyMap<String, ChannelStaffInfo>(),
                    ChannelDocument::staffIds to listOf(staffId2, staffId1),
                )
            )
        } returns destinationChannelId.success()

        coEvery {
            messageFirestoreService.add(
                destinationChannelId,
                MessageDocument(
                    aliceId = staffId1,
                    userId = staffId1,
                    content = MessageType.CHANNEL_MERGED.description,
                    type = MessageType.CHANNEL_MERGED,
                    createdAt = now
                )
            )
        } returns messageId.success()

        coEvery {
            channelNotificationService.produceGenericEvent(match { event: MergedChannelDemandEvent ->
                event.payload == MergedChannelDemandEventPayload(
                    originChannelId,
                    destinationChannelId,
                    demands.map { it.caseId },
                    appointmentIds
                )
            })
        } returns mockk()

        coEvery {
            channelNotificationService.notify(
                origin.copy(status = MERGED, mergedWith = destinationChannelId),
                ChannelChangeAction.MERGE
            )
        } returns mockk()

        val result = channelMergeService.merge(originChannelId, destinationChannelId, staffId1)
        assertThat(result).isSuccessWithData(destinationChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 2) { channelFirestoreService.updateFields(any(), any()) }
        coVerify(exactly = 2) { messageFirestoreService.add(any(), any(), any()) }
        coVerifyOnce { channelNotificationService.notify(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any(), any()) }
    }

    @Test
    @OptIn(FirestoreContextUsage::class)
    fun `#moveMergedChannel`() = runBlocking {
        val channel = origin.copy(status = MERGED)

        coEvery { channelFirestoreService.getChannel(originChannelId) } returns channel.success()

        coEvery { channelFirestoreService.add(channel, originChannelId) } returns originChannelId

        coEvery {
            messageFirestoreService.getMessages(originChannelId, false)
        } returns listOf(messageDocument).success()

        coEvery {
            messageFirestoreService.getMessages(originChannelId, true)
        } returns listOf(messageDocument).success()

        coEvery {
            messageChannelMergedFirestoreService.add(originChannelId, messageId, messageDocument, false)
        } returns messageId.success()

        coEvery {
            messageChannelMergedFirestoreService.add(originChannelId, messageId, messageDocument, true)
        } returns messageId.success()

        coEvery { channelFirestoreService.getChannel(originChannelId, true) } returns channel.success()

        coEvery {
            channelFirestoreService.recursiveDelete(originChannelId)
        } returns originChannelId.success()

        val result = channelMergeService.moveMergedChannel(originChannelId)
        assertThat(result).isSuccessWithData(originChannelId)

        coVerify(exactly = 2) { channelFirestoreService.getChannel(any(), any()) }
        coVerify(exactly = 1) { channelFirestoreService.add(any(), any()) }
        coVerify(exactly = 2) { messageFirestoreService.getMessages(any(), any()) }
        coVerify(exactly = 2) { messageChannelMergedFirestoreService.add(any(), any(), any(), any()) }
        coVerify(exactly = 1) { channelFirestoreService.recursiveDelete(any()) }
        coVerify { channelNotificationService wasNot called }
    }
}
