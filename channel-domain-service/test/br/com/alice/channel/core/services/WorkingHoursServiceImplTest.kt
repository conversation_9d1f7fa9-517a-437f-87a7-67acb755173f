package br.com.alice.channel.core.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Overrides
import br.com.alice.data.layer.models.WorkingHours
import br.com.alice.data.layer.services.AliceAgoraWorkingHoursDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.Test

class WorkingHoursServiceImplTest {
    private val aliceAgoraWorkingHoursDataService: AliceAgoraWorkingHoursDataService = mockk()
    private val service = WorkingHoursServiceImpl(aliceAgoraWorkingHoursDataService)
    private val aliceAgoraWorkingHours = TestModelFactory.buildAliceAgoraWorkingHours()
    private val invalidWorkingHours = WorkingHours(WeekDay.SUNDAY, LocalTime.of(10,0), LocalTime.of(9, 0))
    private val invalidOverrides = Overrides(LocalDate.of(2020,1,1), LocalTime.of(10,0), LocalTime.of(9, 0))

    @Test
    fun `#create should add new Alice Agora Working Hours`() = runBlocking<Unit> {
        coEvery { aliceAgoraWorkingHoursDataService.add(aliceAgoraWorkingHours) } returns aliceAgoraWorkingHours.success()

        val actual = service.create(aliceAgoraWorkingHours)

        assertThat(actual).isSuccess()
        assertThat(actual.get()).isEqualTo(aliceAgoraWorkingHours)
    }

    @Test
    fun `#update should update existing Alice Agora Working Hours`() = runBlocking<Unit> {
        coEvery { aliceAgoraWorkingHoursDataService.update(aliceAgoraWorkingHours) } returns aliceAgoraWorkingHours.success()

        val actual = service.update(aliceAgoraWorkingHours)

        assertThat(actual).isSuccess()
        assertThat(actual.get()).isEqualTo(aliceAgoraWorkingHours)
    }

    @Test
    fun `#create should return error on failure to add new Alice Agora Working Hours`() = runBlocking<Unit> {
        coEvery {
            aliceAgoraWorkingHoursDataService.add(aliceAgoraWorkingHours)
        } returns AuthorizationException("not allowed to perform this action").failure()

        val actual = service.create(aliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#create should return error if a working hours time interval is invalid`() = runBlocking<Unit> {
        val invalidAliceAgoraWorkingHours = aliceAgoraWorkingHours.copy(
            workingHours = aliceAgoraWorkingHours.workingHours.plus(invalidWorkingHours)
        )

        val actual = service.create(invalidAliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#create should return error if a overrides time interval is invalid`() = runBlocking<Unit> {
        val invalidAliceAgoraWorkingHours = aliceAgoraWorkingHours.copy(
            overrides = aliceAgoraWorkingHours.overrides.plus(invalidOverrides)
        )

        val actual = service.create(invalidAliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#update should return error on failure to update existing Alice Agora Working Hours`() = runBlocking<Unit> {
        coEvery {
            aliceAgoraWorkingHoursDataService.update(aliceAgoraWorkingHours)
        } returns AuthorizationException("not allowed to perform this action").failure()

        val actual = service.update(aliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#update should return error if a working hours time interval is invalid`() = runBlocking<Unit> {
        val invalidAliceAgoraWorkingHours = aliceAgoraWorkingHours.copy(
            workingHours = aliceAgoraWorkingHours.workingHours.plus(invalidWorkingHours)
        )

        val actual = service.update(invalidAliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#update should return error if a overrides time interval is invalid`() = runBlocking<Unit> {
        val invalidAliceAgoraWorkingHours = aliceAgoraWorkingHours.copy(
            overrides = aliceAgoraWorkingHours.overrides.plus(invalidOverrides)
        )

        val actual = service.update(invalidAliceAgoraWorkingHours)

        assertThat(actual).isFailure()
    }

    @Test
    fun `#getByRange should return success`() = runBlocking<Unit> {
        val range = IntRange(0, 19)

        coEvery { aliceAgoraWorkingHoursDataService.find(queryEq {
            orderBy { createdAt }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }) } returns listOf(aliceAgoraWorkingHours).success()

        val result = service.getByRange(range)

        assertThat(result.get()).containsOnly(aliceAgoraWorkingHours)
    }

    @Test
    fun `#get should found working hours and return success`() = runBlocking {
        coEvery { aliceAgoraWorkingHoursDataService.get(aliceAgoraWorkingHours.id) } returns aliceAgoraWorkingHours.success()

        val result = service.get(aliceAgoraWorkingHours.id.toString())

        assertThat(result).isSuccessWithData(aliceAgoraWorkingHours)
    }

    @Test
    fun `#get should not find working hours and return failure`() = runBlocking {
        val id = RangeUUID.generate()

        coEvery { aliceAgoraWorkingHoursDataService.get(id) } returns NotFoundException("Working hours not found").failure()

        val result = service.get(id.toString())

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#count should count all`() = runBlocking<Unit> {
        coEvery { aliceAgoraWorkingHoursDataService.count(queryEq { all() }) } returns 1.success()

        val result = service.count()

        assertThat(result.get()).isEqualTo(1)
    }
}
