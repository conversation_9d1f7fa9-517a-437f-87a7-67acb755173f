package br.com.alice.channel.core.services

import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelChangeAction.CREATE_CHAT
import br.com.alice.data.layer.models.ChannelHistory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.services.ChannelHistoryDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class ChannelHistoryServiceImplTest {

    private val channelHistoryDataService: ChannelHistoryDataService = mockk()
    private val channelHistoryService = ChannelHistoryServiceImpl(channelHistoryDataService)

    private val personId = PersonId()
    private val channelId = "channel_id"
    private val event = ChannelUpsertedEvent(
        ChannelUpsertedPayload(
            channelId = channelId,
            personId = personId,
            type = ChannelType.HEALTH_PLAN,
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
            subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
            status = ChannelStatus.ACTIVE,
            action = ChannelChangeAction.CREATE_CHANNEL,
            appVersion = "3.0.0"
        )
    )

    private val channelHistory = event.payload.convertTo(ChannelHistory::class).copy(eventDate = event.eventDate)

    @AfterTest
    fun confirmMocks() = confirmVerified(channelHistoryDataService)

    @Test
    fun `#create returns created channel history`() = runBlocking {
        val channelId = channelId

        coEvery {
            channelHistoryDataService.exists(
                queryEq {
                    where {
                        this.channelId.eq(channelId) and
                                this.action.eq(ChannelChangeAction.CREATE_CHANNEL) and
                                this.eventDate.eq(event.eventDate)
                    }
                }
            )
        } returns false.success()
        coEvery { channelHistoryDataService.add(channelHistory) } returns channelHistory.success()

        val result = channelHistoryService.create(channelHistory)
        assertThat(result).isSuccessWithData(channelHistory)

        coVerifyOnce { channelHistoryDataService.exists(any()) }
        coVerifyOnce { channelHistoryDataService.add(any()) }
    }

    @Test
    fun `#create returns the same channel history when it already exists`() = runBlocking {
        val channelId = channelId

        coEvery {
            channelHistoryDataService.exists(
                queryEq {
                    where {
                        this.channelId.eq(channelId) and
                                this.action.eq(ChannelChangeAction.CREATE_CHANNEL) and
                                this.eventDate.eq(event.eventDate)
                    }
                }
            )
        } returns true.success()

        val result = channelHistoryService.create(channelHistory)
        assertThat(result).isSuccessWithData(channelHistory)

        coVerifyOnce { channelHistoryDataService.exists(any()) }
        coVerifyNone { channelHistoryDataService.add(any()) }
    }

    @Test
    fun `#getFirstByActionAndPersonId returns one channel history with correct params`() = runBlocking {
        val person = personId

        coEvery {
            channelHistoryDataService.findOne(
                queryEq {
                    where { this.action.eq(CREATE_CHAT) and this.personId.eq(person) }
                        .orderBy { createdAt }
                        .sortOrder { asc }
                }
            )
        } returns channelHistory.success()

        val result = channelHistoryService.getFirstByActionAndPersonId(CREATE_CHAT, personId)
        assertThat(result).isSuccessWithData(channelHistory)

        coVerifyOnce { channelHistoryDataService.findOne(any()) }
    }

    @Test
    fun `#findByChannelId returns history by channel`() = runBlocking {
        val channelId = channelId

        coEvery {
            channelHistoryDataService.find(
                queryEq {
                    where { this.channelId.eq(channelId) }
                        .orderBy { createdAt }
                        .sortOrder { asc }
                }
            )
        } returns listOf(channelHistory).success()

        val result = channelHistoryService.findByChannelId(channelId)
        assertThat(result).isSuccessWithData(listOf(channelHistory))

        coVerifyOnce { channelHistoryDataService.find(any()) }
    }

    @Test
    fun `#findChatByChannelIdAndAction returns one channel history found by action`() = runBlocking {
        val channelId = "channel_id"
        val action = CREATE_CHAT

        coEvery {
            channelHistoryDataService.findOne(
                queryEq {
                    where {
                        this.channelId.eq(channelId) and this.action.eq(action)
                    }
                }
            )
        } returns channelHistory.success()

        val result = channelHistoryService.findChatByChannelIdAndAction(channelId, action)
        assertThat(result).isSuccessWithData(channelHistory)

        coVerifyOnce { channelHistoryDataService.findOne(any()) }
    }
}
