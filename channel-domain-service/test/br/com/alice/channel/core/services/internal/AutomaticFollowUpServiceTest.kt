package br.com.alice.channel.core.services.internal

import br.com.alice.channel.client.ChannelFupService
import br.com.alice.channel.core.services.ChannelFollowUp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.Origin
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelFollowUpOptions
import br.com.alice.data.layer.models.ChannelFupAnswer
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertFailsWith

class AutomaticFollowUpServiceTest {

    private val channelFupService: ChannelFupService = mockk()
    private val deviceService: DeviceService = mockk()
    private val pushService: PushService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = AutomaticFollowUpService(
        channelFupService,
        deviceService,
        pushService,
        kafkaProducerService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId)
    private val channelFupId = "c3ad7f1a-3329-4e59-8e2f-a6395091ae68"
    private val channelId = "c3ad7f1a-3329-4e59-8e2f-a6395091ae69"
    private val channelPersonId = "c3ad7f1a-3329-4e59-8e2f-a6395091ae60"
    private val personHealthEventId = "person_health_event_id"
    private val channelFup = TestModelFactory.buildChannelFup(channelFupId.toUUID())
    private val defaultFupMessage = "<h4>Como você está se sentindo em relação à sua última queixa?</h4>"
    private val channelResponse = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        personId = personId.toString(),
        name = "channel_name",
        type = ChannelType.ASSISTANCE_CARE,
        status = ChannelStatus.ACTIVE
    )
    private val device = Device(personId, "device_id")

    private val configValue = "@personName, seu Time de Saúde quer saber como você está se sentindo. Conta pra gente?"
    private val pushMessageId = "push_message_id"

    @Test
    fun `#getFupByChannelFupId should get fup by channelFupId`(): Unit = runBlocking {
        val formattedAnswers = getFormattedAnswers(channelFup.answers)
        val expected = ChannelFollowUp(
            id = personHealthEventId,
            title = channelFup.question,
            options = getAnswersIdentified(formattedAnswers, person.contactName)
        )

        coEvery { channelFupService.get(channelFupId) } returns channelFup.success()

        val result = service.getFupByChannelFupId(channelFupId, personHealthEventId, person.contactName)

        assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#getFupByChannelFupId should get fup with default message when person contact name is empty`(): Unit =
        runBlocking {
            val expected = buildDefaultFupAttribute(personHealthEventId, channelFup.question)

            coEvery { channelFupService.get(channelFupId) } returns channelFup.success()

            val result = service.getFupByChannelFupId(channelFupId, personHealthEventId, "")

            assertThat(result).isSuccessWithData(expected)
        }

    @Test
    fun `#getFupByChannelFupId should get fup with default message when not found channel fup by id`(): Unit =
        runBlocking {
            val expected = buildDefaultFupAttribute(personHealthEventId, defaultFupMessage)

            coEvery { channelFupService.get(channelFupId) } returns NotFoundException("not_found").failure()

            val result = service.getFupByChannelFupId(channelFupId, personHealthEventId, person.contactName)

            assertThat(result).isSuccessWithData(expected)
        }

    @Test
    fun `#sendFupPushMessage should send push notification`(): Unit = runBlocking {
        val firebasePushData = mapOf(
            "path_to_navigate" to "channel",
            "parameter" to channelId,
            "properties" to "{\"category\": \"${Origin.CHANNELS.description}\"}"
        )
        val firebasePush = FirebasePush(
            deviceToken = device.deviceId,
            title = channelResponse.name.orEmpty(),
            body = configValue.replace("@personName", person.contactName),
            data = firebasePushData
        )

        coEvery { deviceService.getDeviceByPerson(personId.toString()) } returns device.success()
        coEvery { pushService.send(firebasePush) } returns pushMessageId.success()

        val result = service.sendFupPushMessage(channelResponse, person.contactName, personId.toString())

        assertThat(result).isSuccess()
    }

    @Test
    fun `#sendFupPushMessage should not send push notification when device is not found`(): Unit = runBlocking {
        coEvery { deviceService.getDeviceByPerson(personId.toString()) } returns NotFoundException("device_not_found").failure()

        assertFailsWith<NotFoundException> {
            service.sendFupPushMessage(channelResponse, person.contactName, personId.toString()).get()
        }

        coVerify { pushService wasNot called }
    }

    private fun getFormattedAnswers(answers: List<ChannelFupAnswer>) =
        answers.map { answer ->
            ChannelFollowUpOptions(
                icon = answer.icon.orEmpty(),
                value = answer.key,
                label = answer.label,
                nextChannelFup = answer.nextChannelFup.orEmpty(),
                starred = answer.starred ?: false
            )
        }

    private fun getAnswersIdentified(
        answersDeIdentified: List<ChannelFollowUpOptions>,
        personContactName: String
    ) = answersDeIdentified.map {
        val label = it.label.replace("@nickname", personContactName)
        it.copy(label = label)
    }

    private fun buildDefaultFupAttribute(personHealthEventId: String, questionFupMessage: String): ChannelFollowUp {
        val fupIFeelBetterIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_fell_better_icon",
            defaultValue = ""
        )
        val fupIDidNotSeeImprovementIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_didnt_see_improvement_icon",
            defaultValue = ""
        )
        val fupIFeelWorseIcon = FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "fup_i_feel_worse_icon",
            defaultValue = ""
        )

        return ChannelFollowUp(
            id = personHealthEventId,
            title = questionFupMessage,
            options = listOf(
                ChannelFollowUpOptions(
                    icon = fupIFeelBetterIcon,
                    label = "Me sinto melhor",
                    value = "fup_i_fell_better"
                ),
                ChannelFollowUpOptions(
                    icon = fupIDidNotSeeImprovementIcon,
                    label = "Não pierce melhora",
                    value = "fup_i_didnt_see_improvement"
                ),
                ChannelFollowUpOptions(
                    icon = fupIFeelWorseIcon,
                    label = "Me sinto pior",
                    value = "fup_i_feel_worse"
                )
            )
        )
    }
}
