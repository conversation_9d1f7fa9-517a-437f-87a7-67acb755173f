package br.com.alice.channel.core.services.internal.processors

import br.com.alice.channel.core.services.internal.FirestoreEventService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class MemberMessageProcessorTest {
    private val firestoreEventService: FirestoreEventService = mockk()
    private val memberMessageProcessor = MemberMessageProcessor(firestoreEventService)

    private val channelId = "xpto"
    private val staffId = RangeUUID.generate()
    private val personId = PersonId()
    private val channelPersonId = RangeUUID.generate()
    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId.toString(),
        personId = personId.toString()
    )

    private val messageDocument = MessageDocument(
        id = "abcd",
        aliceId = staffId.toString(),
        userId = channelPersonId.toString(),
        content = "txt",
        type = MessageType.TEXT,
    )

    @AfterTest
    fun confirmVerify() = confirmVerified(
        firestoreEventService
    )

    @Test
    fun `#process returns true and handle new member message`() = runBlocking {
        coEvery {
            firestoreEventService.handleNewMemberMessage(channelDocument.id!!, messageDocument.id!!)
        } returns "".success()

        val result = memberMessageProcessor.process(channelDocument, messageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { firestoreEventService.handleNewMemberMessage(any(), any()) }
    }

    @Test
    fun `#process returns true and do nothing when message not is from membrer`() = runBlocking {
        val messageDocument = messageDocument.copy(userId = RangeUUID.generate().toString())

        val result = memberMessageProcessor.process(channelDocument, messageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerify { firestoreEventService wasNot called }
    }
}
