package br.com.alice.channel.core.services.internal.processors

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelRingType
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.StaffDocument
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class RingProcessorTest {

    private val staffFirestoreService: StaffFirestoreService = mockk()
    private val ringProcessor = RingProcessor(staffFirestoreService)

    private val baseDate = LocalDateTime.now()
    private val timestamp = baseDate.toTimestamp()
    private val timestamp5 = baseDate.plusMinutes(5).toTimestamp()
    private val channelId = "channelId"
    private val staffId = RangeUUID.generate().toString()
    private val channelPersonId = RangeUUID.generate().toString()
    private val staffInfo = ChannelStaffInfo(
        id = staffId,
        name = "",
        firstName = "",
        lastName = "",
        description = "",
        profileImageUrl = "",
        onBackground = true,
        lastSync = timestamp
    )
    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        personId = "",
        lastSync = timestamp,
        staff = mutableMapOf(staffId to staffInfo),
        kind = ChannelKind.CHAT
    )

    private val staffMessageDocument = MessageDocument(
        userId = staffId,
        type = MessageType.TEXT,
        createdAt = timestamp5
    )

    private val memberMessageDocument = MessageDocument(
        userId = channelPersonId,
        type = MessageType.TEXT,
        createdAt = timestamp5
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(staffFirestoreService)

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns true and update shouldRing to false when message is from staff`() = runBlocking {
        coEvery {
            staffFirestoreService.updateFields(
                staffId,
                mapOf(
                    StaffDocument::shouldRing to false,
                    StaffDocument::channelType to ChannelRingType.ONGOING
                )
            )
        } returns staffId.success()

        val result = ringProcessor.process(channelDocument, staffMessageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns true and update shouldRing to true when message is from member`() = runBlocking {
        coEvery {
            staffFirestoreService.updateFields(
                staffId,
                mapOf(
                    StaffDocument::shouldRing to true,
                    StaffDocument::channelType to ChannelRingType.ONGOING
                )
            )
        } returns staffId.success()

        val result = ringProcessor.process(channelDocument, memberMessageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns true and update channelType ONGOING when channel is a chat`() = runBlocking {
        coEvery {
            staffFirestoreService.updateFields(
                staffId,
                mapOf(
                    StaffDocument::shouldRing to true,
                    StaffDocument::channelType to ChannelRingType.ONGOING
                )
            )
        } returns staffId.success()

        val result = ringProcessor.process(channelDocument, memberMessageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns true and update channelType OPEN when channel is a channel`() = runBlocking {
        val channelDocument = channelDocument.copy(kind = ChannelKind.CHANNEL)

        coEvery {
            staffFirestoreService.updateFields(
                staffId,
                mapOf(
                    StaffDocument::shouldRing to true,
                    StaffDocument::channelType to ChannelRingType.OPEN
                )
            )
        } returns staffId.success()

        val result = ringProcessor.process(channelDocument, memberMessageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffFirestoreService.updateFields(any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns true and do nothing when channel is not active`() = runBlocking {
        val channelDocument = channelDocument.copy(status = ChannelStatus.ARCHIVED)

        val result = ringProcessor.process(channelDocument, memberMessageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerify { staffFirestoreService wasNot called }
    }
}
