package br.com.alice.channel.core.metrics

import br.com.alice.common.observability.metrics.Metric
import io.micrometer.core.instrument.Counter
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberFupFeedbackAdministrativeCoreMetricsTest {

    @BeforeTest
    fun setup() {
        Metric.meterRegistry.meters.map { meter -> Metric.meterRegistry.remove(meter.id) }
    }

    @Test
    fun `#incrementMemberFupFeedbackCounter - count correct by members fup feddbacks`() {
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.SKIPPED)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_BETTER)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_BETTER)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_DID_NOT_SEE_IMPROVEMENT)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_DID_NOT_SEE_IMPROVEMENT)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_DID_NOT_SEE_IMPROVEMENT)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_WORSE)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_WORSE)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_WORSE)
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.FUP_I_FELL_WORSE)

        val memberFupFeedbackSkippedOption = Metric.meterRegistry.meters.find {
            it.id.name == "alice.member_fup_feedback"
                    && it.id.getTag("result") == "skipped"
        } as Counter

        val memberFupFeedbackIFeelBetterOption = Metric.meterRegistry.meters.find {
            it.id.name == "alice.member_fup_feedback"
                    && it.id.getTag("result") == "fup_i_fell_better"
        } as Counter

        val memberFupFeedbackIDitNotSeeImprovementOption = Metric.meterRegistry.meters.find {
            it.id.name == "alice.member_fup_feedback"
                    && it.id.getTag("result") == "fup_i_didnt_see_improvement"
        } as Counter

        val memberFupFeedbackIFellWorseOption = Metric.meterRegistry.meters.find {
            it.id.name == "alice.member_fup_feedback"
                    && it.id.getTag("result") == "fup_i_feel_worse"
        } as Counter

        assertThat(memberFupFeedbackSkippedOption.count()).isEqualTo(1.0)
        assertThat(memberFupFeedbackIFeelBetterOption.count()).isEqualTo(2.0)
        assertThat(memberFupFeedbackIDitNotSeeImprovementOption.count()).isEqualTo(3.0)
        assertThat(memberFupFeedbackIFellWorseOption.count()).isEqualTo(4.0)
    }

    @Test
    fun `#incrementMemberFupFeedbackErrorCounter - increment error count`() {
        MemberFupFeedbackMetrics.incrementMemberFupFeedbackCounter(MemberFupFeedbackOptions.ERROR)

        val memberFupFeedbackErrorOption = Metric.meterRegistry.meters.find {
            it.id.name == "alice.member_fup_feedback"
                    && it.id.getTag("result") == "error"
        } as Counter

        assertThat(memberFupFeedbackErrorOption.count()).isEqualTo(1.0)
    }
}
