package br.com.alice.channel.administrative.consumers

import br.com.alice.akinator.client.AsyncAssistantService
import br.com.alice.akinator.client.AsyncRunThreadRequest
import br.com.alice.akinator.events.AsyncAssistantRequestAction.CREATE_THREAD
import br.com.alice.akinator.events.AsyncAssistantRequestAction.RUN_THREAD
import br.com.alice.akinator.events.AsyncAssistantRequestAction.SEND_MESSAGE
import br.com.alice.akinator.events.AsyncAssistantRequestAction.SEND_MESSAGE_AND_RUN
import br.com.alice.akinator.events.AsyncAssistantResultEvent
import br.com.alice.akinator.events.Result.FAILURE
import br.com.alice.akinator.events.Result.SUCCESS
import br.com.alice.akinator.models.AssistantThreadRunRequestToolChoice
import br.com.alice.akinator.models.ToolChoiceType
import br.com.alice.channel.administrative.services.internal.AI_TRIAGE_ADMINISTRATIVE_TOPIC
import br.com.alice.channel.administrative.services.internal.AiAdministrativeTriageService
import br.com.alice.channel.core.consumers.ConsumerTest
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class AsyncAiAdministrativeChatProcessConsumerTest : ConsumerTest() {

    private val asyncAssistantService: AsyncAssistantService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val channelService: ChannelService = mockk()
    private val aiAdministrativeTriageService: AiAdministrativeTriageService = mockk()

    private val asyncAiAdministrativeChatProcessConsumer = AsyncAiAdministrativeChatProcessConsumer(
        asyncAssistantService,
        messageFirestoreService,
        channelService,
        aiAdministrativeTriageService
    )

    private val channelId = "channelId"
    private val messageId = "messageId"
    private val threadId = "threadId"
    private val assistantId = "assistantId"
    private val toolChoice = AssistantThreadRunRequestToolChoice(type = ToolChoiceType.FILE_SEARCH)
    private val userMessageContent = "content"
    private val aiMessageResponse = "ia message response"
    private val aiMessageSummaryResponse = "ia message summary"
    private val channelPersonId = RangeUUID.generate().toString()
    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId,
        personId = "",
        category = ChannelCategory.ADMINISTRATIVE,
        subCategoryClassifier = ChannelSubCategoryClassifier.AI,
        aiExternalId = threadId
    )
    private val messageDocument = MessageDocument(
        id = messageId,
        userId = channelPersonId,
        type = MessageType.TEXT,
        content = userMessageContent
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        asyncAssistantService,
        messageFirestoreService,
        channelService,
        aiAdministrativeTriageService
    )

    @Test
    fun `#process returns success from process SEND_MESSAGE and trigger RUN_THREAD`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = SUCCESS,
            action = SEND_MESSAGE,
            requestId = channelId,
            data = "",
            metadata = mapOf("attempt" to 0)
        )

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            aiAdministrativeTriageService.getCurrentAssistantIdByChannel(channelDocument)
        } returns assistantId
        every { aiAdministrativeTriageService.getFileSearchOption(channelDocument) } returns toolChoice
        coEvery {
            asyncAssistantService.runThread(
                AsyncRunThreadRequest(
                    id = channelId,
                    threadId = threadId,
                    assistantId = assistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf("attempt" to 0),
                    additionalInstructions = channelDocument.aiInstructions,
                    toolChoice = toolChoice
                )
            )
        } returns Unit.success()

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { aiAdministrativeTriageService.getCurrentAssistantIdByChannel(channelDocument) }
        verifyOnce { aiAdministrativeTriageService.getFileSearchOption(any()) }
        coVerifyOnce { asyncAssistantService.runThread(any()) }
    }

    @Test
    fun `#process returns success from process SEND_MESSAGE failure and resend message`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = FAILURE,
            action = SEND_MESSAGE,
            requestId = channelId,
            data = "",
            metadata = mapOf("messageId" to messageId, "attempt" to 0)
        )

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()
        coEvery { messageFirestoreService.getMessage(channelId, messageId) } returns messageDocument.success()
        coEvery {
            aiAdministrativeTriageService.sendMessageToAi(
                channel = channelDocument,
                message = messageDocument,
                attempt = 1
            )
        } returns Unit.success()

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { messageFirestoreService.getMessage(any(), any()) }
        coVerifyOnce { aiAdministrativeTriageService.sendMessageToAi(any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process returns success from process RUN_THREAD and replay member`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = SUCCESS,
            action = RUN_THREAD,
            requestId = channelId,
            data = aiMessageResponse,
            metadata = mapOf("attempt" to 0)
        )

        coEvery {
            aiAdministrativeTriageService.processAssistantResponse(
                channelId,
                aiMessageResponse,
                channelId
            )
        } returns messageId.success()

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { aiAdministrativeTriageService.processAssistantResponse(any(), any(), any()) }
    }

    @Test
    fun `#process returns success from process RUN_THREAD failure and rerun thread`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = FAILURE,
            action = RUN_THREAD,
            requestId = channelId,
            data = "",
            metadata = mapOf("attempt" to 0)
        )

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            aiAdministrativeTriageService.getCurrentAssistantIdByChannel(channelDocument)
        } returns assistantId
        every { aiAdministrativeTriageService.getFileSearchOption(channelDocument) } returns toolChoice
        coEvery {
            asyncAssistantService.runThread(
                AsyncRunThreadRequest(
                    id = channelId,
                    threadId = threadId,
                    assistantId = assistantId,
                    responseTopic = AI_TRIAGE_ADMINISTRATIVE_TOPIC,
                    metadata = mapOf("attempt" to 1),
                    additionalInstructions = channelDocument.aiInstructions,
                    toolChoice = toolChoice
                )
            )
        } returns Unit.success()

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { channelService.getChannel(any()) }
        coVerifyOnce { aiAdministrativeTriageService.getCurrentAssistantIdByChannel(any()) }
        verifyOnce { aiAdministrativeTriageService.getFileSearchOption(any()) }
        coVerifyOnce { asyncAssistantService.runThread(any()) }
    }

    @Test
    fun `#process returns false and ignore event`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = SUCCESS,
            action = CREATE_THREAD,
            requestId = channelId,
            data = ""
        )

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).isSuccessWithData(false)
    }

    @Test
    fun `#process returns error when exceeded retry limit`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = FAILURE,
            action = SEND_MESSAGE,
            requestId = channelId,
            data = "my exception",
            metadata = mapOf("attempt" to 3)
        )

        val result = asyncAiAdministrativeChatProcessConsumer.process(event)
        assertThat(result).fails().withMessage("my exception").ofType(Exception::class)
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processSummary returns success from process SEND_MESSAGE_AND_RUN and send a private message`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = SUCCESS,
            action = SEND_MESSAGE_AND_RUN,
            requestId = channelId,
            data = aiMessageSummaryResponse,
            metadata = mapOf("attempt" to 0)
        )

        coEvery { channelService.getChannel(channelId) } returns channelDocument.success()
        coEvery {
            aiAdministrativeTriageService.sendMessageToChannel(channelDocument, aiMessageSummaryResponse, true)
        } returns messageId.success()

        val result = asyncAiAdministrativeChatProcessConsumer.processSummary(event)
        assertThat(result).isSuccessWithData(messageId)

        coVerifyOnce { channelService.getChannel(channelId) }
        coVerifyOnce { aiAdministrativeTriageService.sendMessageToChannel(any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processSummary returns true from process SEND_MESSAGE_AND_RUN failure and regenerate summary`() =
        runBlocking {
            val event = AsyncAssistantResultEvent(
                result = FAILURE,
                action = SEND_MESSAGE_AND_RUN,
                requestId = channelId,
                data = aiMessageSummaryResponse,
                metadata = mapOf("attempt" to 0)
            )

            coEvery { channelService.getChannel(channelId) } returns channelDocument.success()
            coEvery { aiAdministrativeTriageService.generateChannelSummary(channelDocument, 1) } returns true.success()

            val result = asyncAiAdministrativeChatProcessConsumer.processSummary(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { channelService.getChannel(any()) }
            coVerifyOnce { aiAdministrativeTriageService.generateChannelSummary(any(), any()) }
        }

    @Test
    fun `#processSummary returns false and ignore event`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = SUCCESS,
            action = SEND_MESSAGE,
            requestId = channelId,
            data = ""
        )

        val result = asyncAiAdministrativeChatProcessConsumer.processSummary(event)
        assertThat(result).isSuccessWithData(false)
    }

    @Test
    fun `#processSummary returns error when exceeded retry limit`() = runBlocking {
        val event = AsyncAssistantResultEvent(
            result = FAILURE,
            action = SEND_MESSAGE_AND_RUN,
            requestId = channelId,
            data = "my exception",
            metadata = mapOf("attempt" to 3)
        )

        val result = asyncAiAdministrativeChatProcessConsumer.processSummary(event)
        assertThat(result).fails().withMessage("my exception").ofType(Exception::class)
    }
}
