ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ br.com.alice.channel.ApplicationKt.module ]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    auth {
        opsGenie = ""
    }
    csatLink = "https://www.alice.com.br/?ci=@ci&dt=@dt&nc=@nc&ct=@ct"
    csatLink = ${?CSAT_LINK}
    csatFormUrl = "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/questionnaire?dl%3Dtrue%26type%3DTEST_CSAT_AA%26source_type%3D@source_type%26source_id%3D@source_id&apn=com.alicesaude.staging&isi=1528785841&ibi=staging.br.com.alice.tech.enduser"
    csatAdmFormUrl = "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/questionnaire?dl%3Dtrue%26type%3DCSAT_MEMBERX%26source_type%3D@source_type%26source_id%3D@source_id&apn=com.alicesaude.staging&isi=1528785841&ibi=staging.br.com.alice.tech.enduser"
    videoCallDeeplink = "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/video_call?meeting_id%3D@meetingId"
    videoCallBucket = ${?VIDEO_CALL_BUCKET}
    zendeskTicketsURL = "https://alice709.zendesk.com/agent/tickets"
    generativeDalyaAkinatorSetupId = ${?GENERATIVE_DALYA_AKINATOR_SETUP_ID}
    moneyInUrlDomain = ${?MONEY_IN_URL_DOMAIN}
    featureConfigSingleLoad = "true"
}

test {
    auth {
        opsGenie = ""
    }
    csatLink = "https://www.alice.com.br/?ci=@ci&dt=@dt&nc=@nc&ct=@ct"
    csatLink = ${?CSAT_LINK}
    csatFormUrl = "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/questionnaire?dl%3Dtrue%26type%3DTEST_CSAT_AA%26source_type%3D@source_type%26source_id%3D@source_id&apn=com.alicesaude.staging&isi=1528785841&ibi=staging.br.com.alice.tech.enduser"
    csatAdmFormUrl = "https://alicesaudestaging.page.link/?link=https://www.alice.com.br/questionnaire?dl%3Dtrue%26type%3DCSAT_MEMBERX%26source_type%3D@source_type%26source_id%3D@source_id&apn=com.alicesaude.staging&isi=1528785841&ibi=staging.br.com.alice.tech.enduser"
    videoCallDeeplink = "https://deeplink/video_call?meeting_id%3D@meetingId"
    videoCallBucket = "bucket_video_call"
    zendeskTicketsURL = "https://alice709.zendesk.com/agent/tickets"
    generativeDalyaAkinatorSetupId = ""
    moneyInUrlDomain = "test"
    featureConfigSingleLoad = "true"
}

production {
    auth {
        opsGenie = ${?GENIE_API_KEY}
    }
    csatLink = "https://pt.surveymonkey.com/r/FJHV7LT?ci=@ci&dt=@dt&nc=@nc&ct=@ct"
    csatLink = ${?CSAT_LINK}
    csatFormUrl = ""
    csatFormUrl = ${?CSAT_FORM_URL}
    csatAdmFormUrl = ""
    csatAdmFormUrl = ${?CSAT_ADM_FORM_URL}
    videoCallDeeplink = ""
    videoCallDeeplink = ${?VIDEO_CALL_DEEPLINK}
    videoCallBucket = ""
    videoCallBucket = ${?VIDEO_CALL_BUCKET}
    zendeskTicketsURL = ""
    zendeskTicketsURL = ${?ZENDESK_TICKETS_URL}
    generativeDalyaAkinatorSetupId = ""
    generativeDalyaAkinatorSetupId = ${?GENERATIVE_DALYA_AKINATOR_SETUP_ID}
    moneyInUrlDomain = ""
    moneyInUrlDomain = ${?MONEY_IN_URL_DOMAIN}
    featureConfigSingleLoad = "false"
    featureConfigSingleLoad = ${?FEATURE_CONFIG_SINGLE_LOAD}
}
