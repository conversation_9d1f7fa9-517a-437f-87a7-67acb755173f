package br.com.alice.member.onboarding.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.services.MemberOnboardingCheckpointDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberOnboardingCheckpointServiceImplTest {
    private val memberOnboardingCheckpointDataService: MemberOnboardingCheckpointDataService = mockk()

    private val memberOnboardingCheckpointService = MemberOnboardingCheckpointServiceImpl(
        memberOnboardingCheckpointDataService
    )

    private val person = TestModelFactory.buildPerson()
    private val memberOnboardingStep = TestModelFactory.buildMemberOnboardingStep()
    private val memberOnboardingCheckpoint = TestModelFactory.buildMemberOnboardingCheckpoint(
        personId = person.id,
        currentStepId = memberOnboardingStep.id,
    )

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#create should add new MemberOnboardingCheckpoint`() = runBlocking {
        coEvery {
            memberOnboardingCheckpointDataService.add(memberOnboardingCheckpoint)
        } returns memberOnboardingCheckpoint.success()

        val actual = memberOnboardingCheckpointService.create(memberOnboardingCheckpoint)

        ResultAssert.assertThat(actual).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce {
            memberOnboardingCheckpointDataService.add(any())
        }
    }

    @Test
    fun `#update should update new MemberOnboardingCheckpoint`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.update(memberOnboardingCheckpoint) } returns memberOnboardingCheckpoint.success()

        val actual = memberOnboardingCheckpointService.update(memberOnboardingCheckpoint)

        ResultAssert.assertThat(actual).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(any()) }
    }

    @Test
    fun `#find should return single MemberOnboardingCheckpoint`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.get(memberOnboardingCheckpoint.id) } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.find(memberOnboardingCheckpoint.id)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.get(any()) }
    }

    @Test
    fun `#findByPersonId should return single MemberOnboardingCheckpoint`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.findOne( queryEq {
            where {
                this.personId.eq(memberOnboardingCheckpoint.personId)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.findByPersonId(memberOnboardingCheckpoint.personId)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.findOne( queryEq {
            where {
                this.personId.eq(memberOnboardingCheckpoint.personId)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) }
    }

    @Test
    fun `#dropCheckpoint should update MemberOnboardingCheckpoint by changing status`() = runBlocking {
        val droppedMemberOnboardingCheckpoint = memberOnboardingCheckpoint.copy(status = MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.DROPPED)

        coEvery { memberOnboardingCheckpointDataService.update(
            match { it.id == memberOnboardingCheckpoint.id
                    && it.status == droppedMemberOnboardingCheckpoint.status
            }
        ) } returns droppedMemberOnboardingCheckpoint.success()

        val actual = memberOnboardingCheckpointService.dropCheckpoint(memberOnboardingCheckpoint)

        ResultAssert.assertThat(actual).isSuccessWithData(droppedMemberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(
            match { it.id == memberOnboardingCheckpoint.id
                    && it.status == droppedMemberOnboardingCheckpoint.status
            }
        ) }
    }

    @Test
    fun `#updateCheckpointBasedOnStep should update MemberOnboardingCheckpoint when person has checkpoint`() = runBlocking {
        coEvery {
            memberOnboardingCheckpointDataService.update(memberOnboardingCheckpoint)
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.updateCheckpointBasedOnStep(memberOnboardingCheckpoint, memberOnboardingStep)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(memberOnboardingCheckpoint) }
    }

    @Test
    fun `#upsertCheckpointBasedOnStep should update MemberOnboardingCheckpoint when person has checkpoint`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.findOne( queryEq {
            where { this.personId.eq(person.id)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) } returns memberOnboardingCheckpoint.success()

        coEvery {
            memberOnboardingCheckpointDataService.update(memberOnboardingCheckpoint)
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.upsertCheckpointBasedOnStep(memberOnboardingCheckpoint.personId, memberOnboardingStep)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.findOne( queryEq {
            where {
                this.personId.eq(memberOnboardingCheckpoint.personId)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) }
        coVerifyOnce { memberOnboardingCheckpointDataService.update(memberOnboardingCheckpoint) }
    }

    @Test
    fun `#upsertCheckpointBasedOnStep should create STARTED MemberOnboardingCheckpoint when person has no checkpoint and step is not lastStep`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.findOne( queryEq {
            where { this.personId.eq(person.id)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) } returns NotFoundException().failure()

        coEvery {
            memberOnboardingCheckpointDataService.add(
                match {
                    it.personId == person.id
                            && it.currentStepId == memberOnboardingStep.id
                            && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.STARTED
                }
            )
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.upsertCheckpointBasedOnStep(memberOnboardingCheckpoint.personId, memberOnboardingStep)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.findOne( queryEq {
            where {
                this.personId.eq(memberOnboardingCheckpoint.personId)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) }

        coVerifyOnce { memberOnboardingCheckpointDataService.add(
            match {
                it.personId == person.id
                        && it.currentStepId == memberOnboardingStep.id
                        && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.STARTED
            }
        ) }
    }

    @Test
    fun `#upsertCheckpointBasedOnStep should create ENDED MemberOnboardingCheckpoint when person has no checkpoint and step is lastStep`() = runBlocking {
        coEvery { memberOnboardingCheckpointDataService.findOne( queryEq {
            where { this.personId.eq(person.id)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) } returns NotFoundException().failure()

        coEvery {
            memberOnboardingCheckpointDataService.add(
                match {
                    it.personId == person.id
                            && it.currentStepId == memberOnboardingStep.id
                            && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.ENDED
                })
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.upsertCheckpointBasedOnStep(memberOnboardingCheckpoint.personId, memberOnboardingStep.copy(type=MemberOnboardingStep.MemberOnboardingStepType.END))

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.findOne( queryEq {
            where {
                this.personId.eq(memberOnboardingCheckpoint.personId)
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        } ) }

        coVerifyOnce { memberOnboardingCheckpointDataService.add(
            match {
                it.personId == person.id
                        && it.currentStepId == memberOnboardingStep.id
                        && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.ENDED
            }
        ) }
    }

    @Test
    fun `#createCheckpointBasedOnStep should create STARTED MemberOnboardingCheckpoint when person has no checkpoint and step is not lastStep`() = runBlocking {
        coEvery {
            memberOnboardingCheckpointDataService.add(
                match {
                    it.personId == person.id
                            && it.currentStepId == memberOnboardingStep.id
                            && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.STARTED
                }
            )
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.createCheckpointBasedOnStep(memberOnboardingCheckpoint.personId, memberOnboardingStep)

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.add(
            match {
                it.personId == person.id
                        && it.currentStepId == memberOnboardingStep.id
                        && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.STARTED
            }
        ) }
    }

    @Test
    fun `#createCheckpointBasedOnStep should create ENDED MemberOnboardingCheckpoint when person has no checkpoint and step is lastStep`() = runBlocking {
        coEvery {
            memberOnboardingCheckpointDataService.add(
                match {
                    it.personId == person.id
                            && it.currentStepId == memberOnboardingStep.id
                            && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.ENDED
                })
        } returns memberOnboardingCheckpoint.success()

        val result = memberOnboardingCheckpointService.createCheckpointBasedOnStep(memberOnboardingCheckpoint.personId, memberOnboardingStep.copy(type=MemberOnboardingStep.MemberOnboardingStepType.END))

        ResultAssert.assertThat(result).isSuccessWithData(memberOnboardingCheckpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.add(
            match {
                it.personId == person.id
                        && it.currentStepId == memberOnboardingStep.id
                        && it.status == MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.ENDED
            }
        ) }
    }

    @Test
    fun `#createCheckpointBasedOnStep should update HIGH riskDuringStratification MemberOnboardingCheckpoint when person has no checkpoint and step is HIGH_RISK`() = runBlocking {

        val checkpoint = memberOnboardingCheckpoint.copy(
            riskDuringStratification = MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk.HIGH)

        coEvery {
            memberOnboardingCheckpointDataService.update(checkpoint)
        } returns checkpoint.success()

        val result = memberOnboardingCheckpointService.updateCheckpointBasedOnStep(
            memberOnboardingCheckpoint,
            memberOnboardingStep.copy(
                type = MemberOnboardingStep.MemberOnboardingStepType.HIGH_RISK
            ))

        ResultAssert.assertThat(result).isSuccessWithData(checkpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(checkpoint) }
    }

    @Test
    fun `#createCheckpointBasedOnStep should update LOW riskDuringStratification MemberOnboardingCheckpoint when person has no checkpoint and step is LOW_RISK`() = runBlocking {

        val checkpoint = memberOnboardingCheckpoint.copy(
            riskDuringStratification = MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk.LOW)

        coEvery {
            memberOnboardingCheckpointDataService.update(checkpoint)
        } returns checkpoint.success()

        val result = memberOnboardingCheckpointService.updateCheckpointBasedOnStep(
            memberOnboardingCheckpoint,
            memberOnboardingStep.copy(
                type = MemberOnboardingStep.MemberOnboardingStepType.LOW_RISK
            ))

        ResultAssert.assertThat(result).isSuccessWithData(checkpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(checkpoint) }
    }

    @Test
    fun `#updateCheckpointHighRiskStep should save ReferralTaskId`() = runBlocking {
        val referralTaskId = RangeUUID.generate()
        val checkpoint = memberOnboardingCheckpoint.copy(
            referralTaskId = referralTaskId,
            currentStepId = memberOnboardingStep.id,
            status = MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus.STARTED,
            riskDuringStratification = MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk.HIGH
        )

        coEvery {
            memberOnboardingCheckpointDataService.update(checkpoint)
        } returns checkpoint.success()

        val result = memberOnboardingCheckpointService.updateCheckpointHighRiskStep(
            memberOnboardingCheckpoint,
            referralTaskId,
            memberOnboardingStep.copy(
                type = MemberOnboardingStep.MemberOnboardingStepType.HIGH_RISK
            )
        )

        ResultAssert.assertThat(result).isSuccessWithData(checkpoint)

        coVerifyOnce { memberOnboardingCheckpointDataService.update(checkpoint) }
    }

}
