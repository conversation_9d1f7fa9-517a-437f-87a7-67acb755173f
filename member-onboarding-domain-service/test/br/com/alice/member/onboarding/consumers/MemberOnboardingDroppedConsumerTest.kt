package br.com.alice.member.onboarding.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.onboarding.notifier.MemberOnboardingDroppedEvent
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class MemberOnboardingDroppedConsumerTest: ConsumerTest() {

    private val kafkaProducerService: KafkaProducerService = mockk()
    private val consumer = MemberOnboardingDroppedConsumer(
        kafkaProducerService
    )

    private val person = TestModelFactory.buildPerson()
    @Test
    fun `#triggerMemberOnboardingReadyToTeamAssociationEvent should trigger team association event`(): Unit = runBlocking {
        val event = MemberOnboardingDroppedEvent(
            personId = person.id
        )
        val producerResult = ProducerResult(LocalDateTime.now(), "topic", 100)

        coEvery {
            kafkaProducerService.produce(any(), any())
        } returns producerResult

        val result = consumer.triggerMemberOnboardingReadyToTeamAssociationEvent(event)

        ResultAssert.assertThat(result).isSuccessWithData(producerResult)

        coVerifyOnce {
            kafkaProducerService.produce(any(), any())
        }
    }


}
