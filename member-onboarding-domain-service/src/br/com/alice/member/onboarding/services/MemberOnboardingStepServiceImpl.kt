package br.com.alice.member.onboarding.services

import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.services.MemberOnboardingStepDataService
import br.com.alice.member.onboarding.client.MemberOnboardingStepService
import java.util.UUID

class MemberOnboardingStepServiceImpl(
    private val memberOnboardingStepDataService: MemberOnboardingStepDataService
): MemberOnboardingStepService {
    override suspend fun create(memberOnboardingStep: MemberOnboardingStep) =
        memberOnboardingStepDataService.add(memberOnboardingStep)

    override suspend fun update(memberOnboardingStep: MemberOnboardingStep) =
        memberOnboardingStepDataService.update(memberOnboardingStep)

    override suspend fun find(id: UUID) =
        memberOnboardingStepDataService.get(id)

    override suspend fun findByTemplateId(id: UUID) =
        memberOnboardingStepDataService.find { where { Predicate.eq(MemberOnboardingStepDataService.MemberOnboardingTemplateId(), id) } }

    override suspend fun findFirstStepByTemplateId(id: UUID) =
        memberOnboardingStepDataService.findOne { where { this.memberOnboardingTemplateId.eq(id) and this.type.eq(MemberOnboardingStep.MemberOnboardingStepType.INIT)} }

    override suspend fun findByTemplateIdAndType(id: UUID, type: MemberOnboardingStep.MemberOnboardingStepType) =
        memberOnboardingStepDataService.findOne {
            where { this.memberOnboardingTemplateId.eq(id) and this.type.eq(type) }
        }

}

