package br.com.alice.fhir.converters.dischargeSummary

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.clearWhitespaces
import br.com.alice.common.readFile
import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.data.layer.models.DischargeSummaryItemKind
import br.com.alice.data.layer.models.DischargeSummaryMedicines
import br.com.alice.data.layer.models.DischargeSummaryPractitioner
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.fhir.commons.fhirContentParser
import ca.uhn.fhir.context.FhirVersionEnum
import org.assertj.core.api.Assertions.assertThat
import org.hl7.fhir.r4.model.Bundle
import kotlin.test.Test

class FhirResourceSantaJoanaToDischargeSummaryConverterTest {
    private val requestJson = readFile("testResources/bundle-santa-joana.json")

    @Test
    fun `#convert`() {
        val expectedPractitioner = DischargeSummaryPractitioner(
            name = "LARISSA DE FREITAS FLOSI",
            councilType = "CRM",
            councilValue = "135205",
            qualification = "GINECOLOGIA/OBSTETRICIA"
        )

        val bundle = fhirContentParser(FhirVersionEnum.R4)
            .parseResource(Bundle::class.java, requestJson)
        val result: DischargeSummary = FhirResourceSantaJoanaToDischargeSummaryConverter(bundle)
            .convert(PersonId(), ProviderIntegration.SANTA_JOANA)



        assertThat(result.externalId).isEqualTo("HMSJ-2657828")
        assertThat(result.dischargeItem.kind).isEqualTo(DischargeSummaryItemKind.HOSPITALIZATION)

        assertThat(result.dischargeItem.practitionerSpecialty?.size).isEqualTo(2)
        assertThat(result.dischargeItem.practitionerSpecialty?.get(0)).isEqualTo(expectedPractitioner)
        assertThat(result.dischargeItem.practitionerSpecialty?.joinToString(" | ") { it.name }).isEqualTo("LARISSA DE FREITAS FLOSI | TATIANE VIEIRA DE MEDEIROS")

        assertThat(result.dischargeItem.diagnostic?.size).isEqualTo(1)
        assertThat(result.dischargeItem.diagnostic!!.first().clearWhitespaces().replace("\n", "")).isEqualTo(
            """SUMARIO DE INTERNACAO HOSPITALAR GESTANTE - 15/11/2022 23:23:39
                DIAGNÓSTICO OBSTÉTRICO NORMALIDADE
                NATUREZA DO ATENDIMENTO: ASSISTÊNCIA A GESTAÇÃO / DUM IG: 23 SEMANAS, 6 DIAS / USG IG: 24 SEMANAS, 1 DIAS / GESTACAO: 1
                
                DIAGNÓSTICO OBSTÉTRICO PATOLÓGICO ATUAL
                NÃO
                
                DIAGNÓSTICO CLINICO CIRURGICO
                HIPOTIREOIDISMO, SRM USO DE MEDICAÇÃO
                
                DIAGNÓSTICO PRINCIPAL DE INTERNAÇÃO
                DOR LOMBAR BAIXA
                
                CID
                N23 - CÓLICA NEFRÉTICA NÃO ESPECIFICADA
""".clearWhitespaces()

        )

        assertThat(result.dischargeItem.risk?.size).isEqualTo(2)
        assertThat(result.dischargeItem.risk?.joinToString(" | ") { it.codeValue }).isEqualTo("Aborto: 0 | Classificação: VERDE")
        assertThat(result.dischargeItem.location).isEqualTo("HOSP E MATERNIDADE SANTA JOANA S/A")
        assertThat(result.dischargeItem.procedures?.size).isEqualTo(1)
        assertThat(result.dischargeItem.procedures?.map { it.value?.clearWhitespaces() }).isEqualTo(listOf("Cirurgias realizadas: NÃO Tratamento Clinico: SIM / CONTROLE DE DOR Procedimentos Exames Complementares: SIM / RNM ABDOMEN".clearWhitespaces()))
        assertThat(result.dischargeItem.structuredMedicine?.size).isEqualTo(20)
        assertThat(result.dischargeItem.structuredMedicine?.first()).isEqualTo(
            DischargeSummaryMedicines(
                code = "1781708900042",
                method = "INTRAVENOSO",
                system = "https://consultas.anvisa.gov.br/#/bulario/q/?numeroRegistro=1781708900042",
                value = null,
                orientation = null
            )
        )
        assertThat(result.dischargeItem.vitalSigns?.size).isEqualTo(6)
        assertThat(result.dischargeItem.vitalSigns!!.first().value).isEqualTo("PAS(mmHg): 129")

    }
}
