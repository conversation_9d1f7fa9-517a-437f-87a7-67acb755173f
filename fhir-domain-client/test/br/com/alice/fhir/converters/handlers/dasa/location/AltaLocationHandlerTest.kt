package br.com.alice.fhir.converters.handlers.dasa.location

import br.com.alice.data.layer.models.ProviderName.ALTA
import br.com.alice.fhir.handlers.dasa.location.AltaLocationHandler
import br.com.alice.fhir.handlers.dasa.location.LocationHandler
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals


class AltaLocationHandlerTest {

    private val nextHandler = mockk<LocationHandler>()
    private val handler = AltaLocationHandler(nextHandler)

    @BeforeTest
    fun beforeTest() {
        every { nextHandler.getLocation(any()) } returns mockk()
    }

    @Test
    fun `get location should return Alta location listed with PREMI`() {
        val result = handler.getLocation("PREMI - Alta Parque Ibirapuera")
        assertEquals(ALTA, result)
        verify { nextHandler wasNot called }
    }

    @Test
    fun `get location should return Alta when location listed with Diagnosticos da America`() {
        val result = handler.getLocation("Alta Excelencia Diagnostica")
        assertEquals(ALTA, result)
        verify { nextHandler wasNot called }
    }

    @Test
    fun `get location should return Alta when location listed with Alta Campo Belo`() {
        val result = handler.getLocation("PREMI - Alta Campo Belo")
        assertEquals(ALTA, result)
        verify { nextHandler wasNot called }
    }
    @Test
    fun `get location should return Alta with accent mark when location listed with Alta Vila Olimpia`() {
        val result = handler.getLocation("Alta Excelência Diagnóstica - Vila Olímpia")
        assertEquals(ALTA, result)
        verify { nextHandler wasNot called }
    }

    @Test
    fun `get location should return Alta when location listed with Alta centro medico`() {
        val result = handler.getLocation("alta centro médico Hospital 9 de Julho - Premium")
        assertEquals(ALTA, result)
        verify { nextHandler wasNot called }
    }

    @Test
    fun `get location should call nextHandler when location not listed with Salomao Zoppi`() {
        handler.getLocation("Salomão Zoppi - Angélica (Mega Unidade)")
        verify(exactly = 1) { nextHandler.getLocation(any()) }
    }

}
