package br.com.alice.fhir.handlers.dasa.location

import br.com.alice.data.layer.models.ProviderName

class AltaLocationHandler(
    private val nextHandler: LocationHandler? = null
) : LocationHandler {

    private val possibleLocations = listOf(
        "PREMI -",
        "Alta Excelencia Diagnostica",
        "Alta Excelência Diagnóstica",
        "Alta Centro médico",
        "Alta Centro medico",
    )

    override fun getLocation(location: String?): ProviderName? {
        if (location == null) return null
        return if (possibleLocations.any { location.trim().contains(it, ignoreCase = true) })
            ProviderName.ALTA
        else
            nextHandler?.getLocation(location)
    }
}
