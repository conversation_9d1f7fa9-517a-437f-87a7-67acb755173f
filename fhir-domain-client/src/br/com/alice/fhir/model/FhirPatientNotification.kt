package br.com.alice.fhir.model

import br.com.alice.data.layer.models.ProviderIntegration

data class FhirPatientNotification(
    val patient: PatientToken,
    val encounterIdentifier: String?,
    val reason: ReasonNotification,
    val provider: ProviderIntegration,
)

enum class ReasonNotification {
    ADMITTED, DISCHARGE;
}

data class PatientToken(
    val nationalId: String,
    val publicToken: String,
)
