package br.com.alice.fhir.model.resources

import br.com.alice.fhir.converters.PROCEDURE_RESOURCE_NAME

data class Procedure(
    override val id: String? = null,
    override val meta: Meta?,
    override val implicitRules: String?,
    override val language: String?,
    override val resourceType: String = PROCEDURE_RESOURCE_NAME,
    override val extension: List<Extension>? = emptyList(),
    override val contained: List<Resource>? = emptyList(),
    val identifier: List<Identifier>? = emptyList(),
    val basedOn: List<Reference>? = emptyList(),
    val partOf: List<Reference>? = emptyList(),
    val status: String,
    val subject: Reference,
    val encounter: Reference?,
    val performedPeriod: Period?,
    val recorder: Reference?,
    val asserter: Reference?,
    val note: List<Annotation>? = emptyList()
) : DomainResource
