package br.com.alice.fhir.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.FhirBundle
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
@Deprecated("use fhir_document")
interface BundleService : Service, Getter<FhirBundle> {
    override val namespace: String get() = "fhir"
    override val serviceName: String get() = "bundle"
    suspend fun getByExternalId(externalId: String): Result<FhirBundle, Throwable>
    override suspend fun get(id: UUID): Result<FhirBundle, Throwable>
    suspend fun findByPersonId(personId: PersonId): Result<List<FhirBundle>, Throwable>
}
