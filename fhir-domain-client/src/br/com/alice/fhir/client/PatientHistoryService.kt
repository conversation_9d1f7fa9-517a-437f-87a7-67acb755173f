package br.com.alice.fhir.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.fhir.model.FhirPatientNotification
import com.github.kittinunf.result.Result

@RemoteService
interface PatientHistoryService : Service {
    override val namespace: String get() = "fhir"
    override val serviceName: String get() = "patient_history"

    suspend fun publish(event: FhirPatientNotification, person: Person): Result<Boolean, Throwable>
    suspend fun retrieve(
        mpi: String,
        person: Person,
        encounter: String?,
        page: Int,
        limit: Int,
        providerName: ProviderIntegration
    ): Result<String, Throwable>
}
