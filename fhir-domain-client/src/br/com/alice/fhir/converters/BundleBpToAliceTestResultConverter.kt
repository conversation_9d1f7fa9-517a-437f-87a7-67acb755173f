package br.com.alice.fhir.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toRangeSafeUUID
import br.com.alice.common.core.extensions.toZonedDateTime
import br.com.alice.data.layer.models.AliceResultItem
import br.com.alice.data.layer.models.AliceTestResult
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.AliceTestResultType
import br.com.alice.data.layer.models.FhirBundle
import br.com.alice.data.layer.models.FhirEncounter
import br.com.alice.data.layer.models.FhirObservation
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.ReferenceRange
import java.time.LocalDateTime

const val BP_RESULT_KEYWORD = "RESULTADO"
const val BP_REFERENCE_KEYWORD = "VALORES DE REFERÊNCIA"
const val BP_EXAM_VALUE_CODE = 4
const val DEFAULT_REGEX_PATTERN = "\\n|[ ]{2,}"

object BundleBpToAliceTestResultConverter {
    fun convert(bundle: FhirBundle, provider: ProviderIntegration): AliceTestResultBundle {
        val validExams = getValidExams(bundle.observations ?: emptyList())
        if (validExams.isEmpty()) throw InvalidArgumentException(
            code = "need_exams_to_build",
            message = "Need one or more exams to build"
        )

        return AliceTestResultBundle(
            id = bundle.id,
            personId = bundle.personId,
            externalId = bundle.externalId,
            integrationSource = provider,
            results = getValidExams(bundle.observations ?: emptyList())
                .map { buildTestResult(it, bundle.encounter, bundle.createdAt) }
        )
    }

    private fun buildTestResult(
        observation: FhirObservation,
        encounter: FhirEncounter?,
        releasedAt: LocalDateTime,
    ): AliceTestResult {
        return AliceTestResult(
            id = observation.id?.toRangeSafeUUID() ?: RangeUUID.generate(),
            name = getTestDisplay(observation),
            collectedAt = getCollectAt(encounter, observation),
            releasedAt = releasedAt,
            requestedBy = getRequestedBy(encounter),
            items = listOf(buildItemResult(observation))
        )
    }

    private fun buildItemResult(observation: FhirObservation): AliceResultItem {
        return AliceResultItem(
            name = getTestDisplay(observation),
            description = getTestDisplay(observation),
            result = getTextResult(observation.valueString),
            unit = null,
            reference = listOf(getReferenceResult(observation.valueString)),
            referenceRange = ReferenceRange.NOT_APPLIED,
            type = AliceTestResultType.TEXT
        )
    }

    private fun structuredTestResult(result: String): Map<String, String> {
        val defaultStructure = mutableMapOf(BP_RESULT_KEYWORD to "none", BP_REFERENCE_KEYWORD to "none")
        val parsedResult = result
            .split(Regex(DEFAULT_REGEX_PATTERN))
            .filter { it.isNotBlank() }

        if (parsedResult.size >= 4 &&
            parsedResult.contains(BP_RESULT_KEYWORD) &&
            parsedResult.contains(BP_REFERENCE_KEYWORD)
        ) {
            defaultStructure[BP_RESULT_KEYWORD] = parsedResult[parsedResult.indexOf(BP_RESULT_KEYWORD) + 2]
            defaultStructure[BP_REFERENCE_KEYWORD] = parsedResult[parsedResult.indexOf(BP_REFERENCE_KEYWORD) + 2]
        }

        return defaultStructure
    }

    private fun getTextResult(result: String?): String {
        if (result == null) return "none"

        val textResult = structuredTestResult(result)
        return textResult[BP_RESULT_KEYWORD] ?: "none"
    }

    private fun getReferenceResult(result: String?): String {
        if (result == null) return "none"

        val textReference = structuredTestResult(result)
        return textReference[BP_REFERENCE_KEYWORD] ?: "none"
    }

    private fun getValidExams(observations: List<FhirObservation>) =
        observations.filter { observation ->
            val extension = observation.extension?.firstOrNull()
            return@filter extension?.valueCode == BP_EXAM_VALUE_CODE || extension?.valueString == "Exames Laboratório"
        }

    private fun getTestDisplay(observation: FhirObservation) =
        observation.code?.text ?: observation.code?.coding?.firstOrNull()?.display ?: "none"

    private fun getRequestedBy(encounter: FhirEncounter?): String {
        val practitioner = encounter?.participant?.firstOrNull()?.individual?.display
        if (practitioner != null) return practitioner

        return "Não identificado"
    }

    private fun getCollectAt(encounter: FhirEncounter?, observation: FhirObservation): LocalDateTime {
        val dateEncounter = encounter?.period?.start
        if (dateEncounter != null) return dateEncounter.toZonedDateTime().toLocalDateTime()

        val dateObservation = observation.effectiveDateTime
        if (dateObservation != null) return dateObservation.toZonedDateTime().toLocalDateTime()

        throw IllegalArgumentException("collectedAt is null")
    }
}
