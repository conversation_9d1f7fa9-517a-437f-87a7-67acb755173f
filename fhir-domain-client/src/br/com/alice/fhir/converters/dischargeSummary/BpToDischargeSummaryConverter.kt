package br.com.alice.fhir.converters.dischargeSummary

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toZonedDateTime
import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.data.layer.models.DischargeSummaryImpression
import br.com.alice.data.layer.models.DischargeSummaryItem
import br.com.alice.data.layer.models.DischargeSummaryItemKind
import br.com.alice.data.layer.models.DischargeSummaryPractitioner
import br.com.alice.data.layer.models.DischargeSummaryRisk
import br.com.alice.data.layer.models.DischargeSummaryVitalSigns
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.fhir.exception.EncounterNotFound
import org.hl7.fhir.dstu3.model.Bundle
import org.hl7.fhir.dstu3.model.ClinicalImpression
import org.hl7.fhir.dstu3.model.Condition
import org.hl7.fhir.dstu3.model.DiagnosticReport
import org.hl7.fhir.dstu3.model.Encounter
import org.hl7.fhir.dstu3.model.HumanName
import org.hl7.fhir.dstu3.model.MedicationStatement
import org.hl7.fhir.dstu3.model.Observation
import org.hl7.fhir.dstu3.model.Practitioner
import org.hl7.fhir.dstu3.model.ResourceType
import org.hl7.fhir.dstu3.model.RiskAssessment

class BpToDischargeSummaryConverter(private val bundle: Bundle) {
    private val encounter: Encounter by lazy { findEncounter() }
    private val observations: List<Observation> by lazy { findObservation() }
    private val practitioners: List<Practitioner> by lazy { findPractitioner() }
    private val diagnosticReports: List<DiagnosticReport> by lazy { findDiagnosticReport() }
    private val riskAssessments: List<RiskAssessment> by lazy { findRiskAssessment() }
    private val clinicalImpressions: List<ClinicalImpression> by lazy { findClinicalImpression() }
    private val conditions: List<Condition> by lazy { findCondition() }
    private val medications: List<MedicationStatement> by lazy { findMedication() }

    fun convert(personId: PersonId, provider: ProviderIntegration): DischargeSummary {
        return DischargeSummary(
            personId = personId,
            externalId = encounter.identifier.first().value,
            provider = provider,
            dischargeItem = buildSingleItem()
        )
    }

    private fun buildSingleItem(): DischargeSummaryItem {
        return DischargeSummaryItem(
            kind = encounter.class_.code.let {
                if (it.lowercase() == "emergency") {
                    DischargeSummaryItemKind.EMERGENCY
                } else {
                    DischargeSummaryItemKind.HOSPITALIZATION
                }
            },
            location = if (encounter.hasLocation()) encounter.location.first().location.display else null,
            admittedAt = encounter.period.start.toInstant().toString().toZonedDateTime().toLocalDateTime(),
            dischargedAt = encounter.period.end.toInstant().toString().toZonedDateTime().toLocalDateTime(),
            practitionerSpecialty = practitioners.map {
                DischargeSummaryPractitioner(
                    name = extractHumanName(it.name),
                    councilValue = it.identifier.firstOrNull()?.value,
                    councilType = it.identifier.firstOrNull()?.type?.coding?.firstOrNull()?.code,
                    qualification = it.qualification.firstOrNull()?.code?.text
                )
            },
            diagnostic = diagnosticReports.map { it.code.text },
            risk = riskAssessments.map {
                DischargeSummaryRisk(
                    codeType = it.reasonCodeableConcept.coding.first().code,
                    codeValue = it.reasonCodeableConcept.coding.first().display
                )
            },
            clinicalImpression = clinicalImpressions
                .filter { !it.summary.lowercase().trim().contains("orientação de alta") }
                .map {
                    DischargeSummaryImpression(
                        description = it.description,
                        summary = it.summary
                    )
                },
            carePlan = clinicalImpressions
                .filter { it.summary.lowercase().trim().contains("orientação de alta") }
                .map {
                    DischargeSummaryImpression(
                        description = it.description,
                        summary = it.summary
                    )
                },
            vitalSigns = observations
                .filter { it.extension.first().value.toString().equals("Sinais Vitais", true) }
                .map {
                    DischargeSummaryVitalSigns(
                        code = it.code.coding.firstOrNull()?.code ?: "",
                        display = it.code.text,
                        value = it.value.toString()
                    )
                },
            condition = conditions.mapNotNull { it.note.firstOrNull()?.text },
            medicines = medications.mapNotNull { it.note.firstOrNull()?.text }
        )
    }

    private fun extractHumanName(humanNames: List<HumanName>): String {
        val practitioner = humanNames.first()
        return if (practitioner.hasText()) {
            practitioner.text
        } else {
            practitioner.family
        }
    }

    private fun findEncounter(): Encounter {
        val resourceBased = bundle.entry.firstOrNull { it.resource.resourceType == ResourceType.Encounter }
            ?: throw EncounterNotFound()

        return resourceBased.resource as Encounter
    }

    private fun findDiagnosticReport(): List<DiagnosticReport> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.DiagnosticReport }
            .map { it.resource as DiagnosticReport }
            .filter { it.context.reference == encounter.id }
    }

    private fun findObservation(): List<Observation> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.Observation }
            .map { it.resource as Observation }
            .filter { it.context.reference == encounter.id }
    }

    private fun findPractitioner(): List<Practitioner> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.Practitioner }
            .map { it.resource as Practitioner }
    }

    private fun findClinicalImpression(): List<ClinicalImpression> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.ClinicalImpression }
            .map { it.resource as ClinicalImpression }
            .filter { it.context.reference == encounter.id }
    }

    private fun findCondition(): List<Condition> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.Condition }
            .map { it.resource as Condition }
            .filter { it.context.reference == encounter.id }
    }

    private fun findMedication(): List<MedicationStatement> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.MedicationStatement }
            .map { it.resource as MedicationStatement }
            .filter { it.context.reference == encounter.id }
    }

    private fun findRiskAssessment(): List<RiskAssessment> {
        return bundle.entry
            .filter { it.resource.resourceType == ResourceType.RiskAssessment }
            .map { it.resource as RiskAssessment }
            .filter { it.context.reference == encounter.id }
    }
}
