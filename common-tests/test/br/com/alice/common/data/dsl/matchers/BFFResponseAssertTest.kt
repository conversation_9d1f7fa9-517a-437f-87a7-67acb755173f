package br.com.alice.common.data.dsl.matchers

import io.ktor.http.*
import io.ktor.server.testing.*
import io.mockk.every
import io.mockk.mockk
import kotlin.test.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows

class BFFResponseAssertTest {
    data class Person(val firstName: String, val lastName: String)

    @Test
    fun `isOKWithData passes when response keep camelCase`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK
        every { response.content } returns """{"firstName":"John","lastName": "Doe"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isOKWithData(Person("<PERSON>", "<PERSON><PERSON>"))
        }
    }

    @Test
    fun `isAcceptedWithData passes when response keep camelCase`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.Accepted
        every { response.content } returns """{"firstName":"John","lastName": "Doe"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isAcceptedWithData(Person("John", "Doe"))
        }
    }

    @Test
    fun `isOKWithDataIgnoringGivenFields passes when response keep camelCase`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK
        every { response.content } returns """{"firstName":"John","lastName": "Doe", "motherName" : "Jane"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isOKWithDataIgnoringGivenFields(Person("John", "Doe"), "motherName")
        }
    }

    @Test
    fun `isUnauthorized passes when response status is Unauthorized`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.Unauthorized

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isUnauthorized()
        }
    }

    @Test
    fun `isUnauthorized fails when response status is not Unauthorized`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isUnauthorized()
        }
    }

    @Test
    fun `isBadRequest passes when response status is BadRequest`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.BadRequest

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isBadRequest()
        }
    }

    @Test
    fun `isBadRequest fails when response status is not BadRequest`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isBadRequest()
        }
    }

    @Test
    fun `isBadRequestWithErrorCode passes when status is BadRequest and error code and message match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.BadRequest
        every { response.content } returns """{"code":"ERR001","message":"Invalid request"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isBadRequestWithErrorCode("ERR001", "Invalid request")
        }
    }

    @Test
    fun `isBadRequestWithErrorCode fails when status is not BadRequest`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK
        every { response.content } returns """{"code":"ERR001","message":"Invalid request"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isBadRequestWithErrorCode("ERR001", "Invalid request")
        }
    }

    @Test
    fun `isBadRequestWithErrorCode fails when error code does not match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.BadRequest
        every { response.content } returns """{"code":"ERR002","message":"Invalid request"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isBadRequestWithErrorCode("ERR001", "Invalid request")
        }
    }

    @Test
    fun `isBadRequestWithErrorCode fails when error message does not match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.BadRequest
        every { response.content } returns """{"code":"ERR001","message":"Wrong message"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isBadRequestWithErrorCode("ERR001", "Invalid request")
        }
    }

    @Test
    fun `isNotFound passes when response status is NotFound`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.NotFound

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isNotFound()
        }
    }

    @Test
    fun `isNotFound fails when response status is not NotFound`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isNotFound()
        }
    }

    @Test
    fun `isForbidden passes when response status is Forbidden`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.Forbidden

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isForbidden()
        }
    }

    @Test
    fun `isForbidden fails when response status is not Forbidden`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isForbidden()
        }
    }

    @Test
    fun `isInternalServerError passes when response status is InternalServerError`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.InternalServerError

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isInternalServerError()
        }
    }

    @Test
    fun `isInternalServerError fails when response status is not InternalServerError`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isInternalServerError()
        }
    }

    @Test
    fun `isCreated passes when response status is Created`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.Created

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isCreated()
        }
    }

    @Test
    fun `isCreated fails when response status is not Created`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()
        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isCreated()
        }
    }

    @Test
    fun `isNotFoundWithErrorCode passes when status is NotFound and error code matches`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.NotFound
        every { response.content } returns """{"code":"ERR404","message":"Not Found"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.isNotFoundWithErrorCode("ERR404")
        }
    }

    @Test
    fun `isNotFoundWithErrorCode fails when status is not NotFound`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.OK
        every { response.content } returns """{"code":"ERR404","message":"Not Found"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isNotFoundWithErrorCode("ERR404")
        }
    }

    @Test
    fun `isNotFoundWithErrorCode fails when error code does not match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.status() } returns HttpStatusCode.NotFound
        every { response.content } returns """{"code":"ERR400","message":"Bad Request"}"""

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.isNotFoundWithErrorCode("ERR404")
        }
    }

    @Test
    fun `containsHeaderWithValue passes when header and value match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.headers["X-Custom-Header"] } returns "HeaderValue"

        val assertResponse = BFFResponseAssert(call.response)

        assertDoesNotThrow {
            assertResponse.containsHeaderWithValue("X-Custom-Header", "HeaderValue")
        }
    }

    @Test
    fun `containsHeaderWithValue fails when header is not found`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.headers["X-Custom-Header"] } returns null

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.containsHeaderWithValue("X-Custom-Header", "HeaderValue")
        }
    }

    @Test
    fun `containsHeaderWithValue fails when header value does not match`() {
        val call = mockk<TestApplicationCall>()
        val response = mockk<TestApplicationResponse>()

        every { call.response } returns response
        every { response.headers["X-Custom-Header"] } returns "WrongValue"

        val assertResponse = BFFResponseAssert(call.response)

        assertThrows<AssertionError> {
            assertResponse.containsHeaderWithValue("X-Custom-Header", "HeaderValue")
        }
    }
}
