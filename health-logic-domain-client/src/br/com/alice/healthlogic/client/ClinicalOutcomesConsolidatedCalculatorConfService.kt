package br.com.alice.healthlogic.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ClinicalOutcomesConsolidatedCalculatorConf
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ClinicalOutcomesConsolidatedCalculatorConfService : Service {

    override val namespace get() = "health_logic"
    override val serviceName get() = "clinical_outcomes_consolidated_calculator_conf"

    suspend fun add(model: ClinicalOutcomesConsolidatedCalculatorConf): Result<ClinicalOutcomesConsolidatedCalculatorConf, Throwable>

    suspend fun inactivate(configurationId: UUID): Result<ClinicalOutcomesConsolidatedCalculatorConf, Throwable>

    suspend fun getByOutcomeConfIdUsedForCalculation(outcomeConfId: UUID): Result<List<ClinicalOutcomesConsolidatedCalculatorConf>, Throwable>

    suspend fun getByOutcomeConfId(outcomeConfId: UUID): Result<List<ClinicalOutcomesConsolidatedCalculatorConf>, Throwable>
}
