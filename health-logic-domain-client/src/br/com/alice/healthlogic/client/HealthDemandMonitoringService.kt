package br.com.alice.healthlogic.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HealthDemandMonitoring
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthDemandMonitoringService : Service {

    override val namespace get() = "health_logic"
    override val serviceName get() = "health_demand_monitoring"

    suspend fun find(range: IntRange, outcomeConfId: String?, healthConditionId: String?): Result<List<HealthDemandMonitoring>, Throwable>

    suspend fun get(id: UUID): Result<HealthDemandMonitoring, Throwable>

    suspend fun create(
        outcomeConfId: UUID,
        healthConditionId: UUID,
        schedulingConfig: List<HealthDemandMonitoring.SchedulingConfig>,
        outcomeEvolutionRanges: List<HealthDemandMonitoring.OutcomeEvolutionRange>
    ): Result<HealthDemandMonitoring, Throwable>

    suspend fun update(
        id: UUID,
        outcomeConfId: UUID,
        healthConditionId: UUID,
        schedulingConfig: List<HealthDemandMonitoring.SchedulingConfig>,
        outcomeEvolutionRanges: List<HealthDemandMonitoring.OutcomeEvolutionRange> = emptyList()
    ): Result<HealthDemandMonitoring, Throwable>

    suspend fun findByHealthConditionId(healthConditionId: UUID): Result<HealthDemandMonitoring, Throwable>

    suspend fun findByOutcomeConfId(outcomeConfId: UUID): Result<List<HealthDemandMonitoring>, Throwable>

    suspend fun count(outcomeConfId: String?, healthConditionId: String?): Result<Int, Throwable>

    suspend fun findByOutcomesConfigsIds(outcomeConfigsIds: List<UUID>): Result<List<HealthDemandMonitoring>, Throwable>
}
