package br.com.alice.healthlogic.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Protocol
import br.com.alice.data.layer.models.Protocol.ProtocolCategory
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProtocolService: Service {

    override val namespace get() = "health_logic"
    override val serviceName get() = "protocol"

    suspend fun create(protocol: Protocol): Result<Protocol, Throwable>

    suspend fun update(protocol: Protocol): Result<Protocol, Throwable>

    suspend fun get(id: UUID): Result<Protocol, Throwable>

    suspend fun findProtocol(query: String?, status: Protocol.ProtocolStatus?, range: IntRange): Result<List<Protocol>, Throwable>

    suspend fun count(query: String?, status: Protocol.ProtocolStatus?): Result<Int, Throwable>

    suspend fun findByHealthConditionIds(ids: List<UUID>): Result<List<Protocol>, Throwable>

    suspend fun findByHealthConditionsAndCategories(ids: List<UUID>, categories: List<ProtocolCategory>): Result<List<Protocol>, Throwable>

    suspend fun findByRootNodeId(rootNodeId: UUID): Result<Protocol, Throwable>
}
