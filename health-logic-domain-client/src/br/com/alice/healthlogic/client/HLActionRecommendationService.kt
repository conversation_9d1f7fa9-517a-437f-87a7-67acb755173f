package br.com.alice.healthlogic.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.HLActionRecommendation
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HLActionRecommendationService : Service {
    override val namespace get() = "health_logic"
    override val serviceName get() = "recommendation"

    suspend fun create(actionRecommendation: HLActionRecommendation): Result<HLActionRecommendation, Throwable>

    suspend fun update(actionRecommendation: HLActionRecommendation, caseRecord: CaseRecord): Result<HLActionRecommendation, Throwable>

    suspend fun get(id: UUID): Result<HLActionRecommendation, Throwable>
    suspend fun getByCaseIds(ids: List<UUID>): Result<List<HLActionRecommendation>, Throwable>
}
