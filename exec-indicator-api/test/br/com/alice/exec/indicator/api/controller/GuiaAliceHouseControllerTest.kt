package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.ErrorResponse
import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toRangeSafeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AcommodationType
import br.com.alice.data.layer.models.AttendanceType
import br.com.alice.data.layer.models.ExecutionGroup
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.HospitalizationType
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.exec.indicator.client.CreateGuiaService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.client.ExecutionGroupRequest
import br.com.alice.exec.indicator.client.ExecutionGroupService
import br.com.alice.exec.indicator.client.GuiaAliceHouseService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.ProcedureRequest
import br.com.alice.exec.indicator.exception.GuiaExecutionGroupNotFoundException
import br.com.alice.exec.indicator.exception.GuiaProcedureNotExistsException
import br.com.alice.exec.indicator.models.CreateGuiaItemsRequest
import br.com.alice.exec.indicator.models.CreateGuiaProfissionalRequest
import br.com.alice.exec.indicator.models.CreateGuiaProfissionalSpecialtyRequest
import br.com.alice.exec.indicator.models.CreateGuiaRequest
import br.com.alice.exec.indicator.models.GuiaBaseData
import br.com.alice.exec.indicator.models.GuiaHistoryResponse
import br.com.alice.exec.indicator.models.GuiaHospitalizationData
import br.com.alice.exec.indicator.models.GuiaMemberResponse
import br.com.alice.exec.indicator.models.GuiaProcedureResponse
import br.com.alice.exec.indicator.models.GuiaProfessionalResponse
import br.com.alice.exec.indicator.models.GuiaProfessionalSpecialtyResponse
import br.com.alice.exec.indicator.models.GuiaProviderResponse
import br.com.alice.exec.indicator.models.GuiaPsData
import br.com.alice.exec.indicator.models.GuiaResponse
import br.com.alice.exec.indicator.models.GuiaStaffAuthorizationHistoricResponse
import br.com.alice.exec.indicator.models.Item
import br.com.alice.exec.indicator.models.ItemSearchType
import br.com.alice.exec.indicator.models.RecentExecutionGroupResponse
import br.com.alice.exec.indicator.models.toProfessional
import br.com.alice.exec.indicator.models.GuiaHistoryPaginatorResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class GuiaAliceHouseControllerTest : ControllerTestHelper() {
    private val createGuiaService: CreateGuiaService = mockk()
    private val guiaAliceHouseService: GuiaAliceHouseService = mockk()
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService = mockk()
    private val executionGroupService: ExecutionGroupService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()

    private val guiaController = spyk(
        GuiaAliceHouseController(
            createGuiaService,
            guiaAliceHouseService,
            execIndicatorAuthorizerService,
            mvAuthorizedProcedureService
        )
    )

    private val execAuthorizer = TestModelFactory.buildExecIndicatorAuthorizer()
    private val now = LocalDateTime.of(1981, 6, 3, 10, 0, 0)
    private val nowDate = LocalDate.of(1981, 6, 3)
    private val personId = PersonId()
    private val request = CreateGuiaRequest(
        personId = personId.id,
        items = listOf(
            CreateGuiaItemsRequest(
                itemId = RangeUUID.generate(),
                aliceCode = "aliceCode",
                quantity = 1,
                type = ItemSearchType.PROCEDURE
            ),
            CreateGuiaItemsRequest(
                itemId = RangeUUID.generate(),
                aliceCode = "aliceCode2",
                quantity = 1,
                type = ItemSearchType.PROCEDURE
            )
        ),
        authorizerId = execAuthorizer.id,
        professional = CreateGuiaProfissionalRequest(
            fullName = "fullName",
            councilState = "SP",
            councilNumber = "number",
            councilName = "CRM",
            specialty = CreateGuiaProfissionalSpecialtyRequest(10L, "Cardiologista")
        ),
        extraGuiaInfo = ExtraGuiaInfo(
            clinicIndication = "Indicação clinica",
            newBorn = false,
            dateAttendance = LocalDate.now(),
            procedureType = MvUtil.TISS.EXAM
        )
    )

    private val guiaCode = "000123456789"
    private val guiaExecutionGroupId = RangeUUID.generate()

    private val guiaPS = GuiaResponse(
        number = guiaCode,
        date = nowDate,
        type = "Pronto Socorro",
        status = MvAuthorizedProcedureStatus.PENDING,
        professional = GuiaProfessionalResponse(
            name = "Nome do profissional",
            councilState = "SP",
            councilNumber = "000259",
            council = "CRM",
            specialty = GuiaProfessionalSpecialtyResponse(id = 1L, name = "Médico cardiologista")
        ),
        member = GuiaMemberResponse(
            id = RangeUUID.generate(),
            fullName = "Nome do Membro",
            dateOfBirth = now,
            nationalId = "12345678909",
            product = "Alice AP.***********.11",
            email = "<EMAIL>",
            accommodation = "Enfermaria"
        ),
        provider = GuiaProviderResponse(
            name = "Casa Alice",
            address = "Av. Rebouças, 3506",
            phone = "(xx) xxxxx-xxxx"
        ),
        additionalData = GuiaPsData(
            accidentIndication = "Não acidente",
            newBorn = false
        ),
        procedures = listOf(
            GuiaProcedureResponse(
                id = RangeUUID.generate(),
                description = "Raio X",
                aliceCode = "10",
                tussCode = "100",
                providerCode = "100"
            ),
            GuiaProcedureResponse(
                id = RangeUUID.generate(),
                description = "Tomografia",
                aliceCode = "10",
                tussCode = "100",
                providerCode = "100"
            )
        )
    )

    private val guiaExam = guiaPS.copy(additionalData = GuiaBaseData(newBorn = false))
    private val guiaHospitalization = guiaPS.copy(
        additionalData = GuiaHospitalizationData(
            accidentIndication = "Não acidente",
            newBorn = false,
            hospitalizationType = HospitalizationType.CLINICAL.name,
            attendanceCharacter = "",
            dateAttendance = nowDate,
            dailyQuantity = 1,
            accommodationType = AcommodationType.ROOM.name,
            clinicIndication = "Indicado para cirurgia",
            attendanceType = AttendanceType.HOSPITAL.name
        )
    )

    private val historics = listOf(
        GuiaHistoryResponse(
            id = RangeUUID.generate(),
            number = "203040",
            date = now,
            status = MvAuthorizedProcedureStatus.PENDING,
            type = MvUtil.TISS.PS
        ),
        GuiaHistoryResponse(
            id = RangeUUID.generate(),
            number = "506070",
            date = now,
            status = MvAuthorizedProcedureStatus.AUTHORIZED,
            type = MvUtil.TISS.EXAM
        )
    )

    val procedures = listOf(TestModelFactory.buildMvAuthorizedProcedure())

    private val noHasRecentGuia = RecentExecutionGroupResponse(false, null)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { guiaController }
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#createGuia should create exam guia successfully`() {
        val createdProcedures = listOf(
            MvAuthorizedProcedure.buildForAuthorizationRequest(
                personId = PersonId(request.personId),
                procedureId = request.items[0].aliceCode,
                testRequestId = null,
                professional = request.professional.toProfessional(),
                status = MvAuthorizedProcedureStatus.AUTHORIZED
            ),
            MvAuthorizedProcedure.buildForAuthorizationRequest(
                personId = PersonId(request.personId),
                procedureId = request.items[1].aliceCode,
                testRequestId = null,
                professional = request.professional.toProfessional(),
                status = MvAuthorizedProcedureStatus.PENDING
            )
        )

        coEvery {
            mvAuthorizedProcedureService.findRecentExecutionGroupByProceduresAndProvider(
                person = PersonId(request.personId),
                date = any(),
                aliceCodes = request.items.map {
                    it.aliceCode

                },
                authorizerId = request.authorizerId,
            )
        } returns noHasRecentGuia.success()

        coEvery {
            createGuiaService.createItemsBatch(
                PersonId(request.personId),
                request.items.map { p ->
                    Item(
                        aliceCode = p.aliceCode,
                        quantity = 1,
                        type = p.type
                    )
                },
                request.authorizerId,
                staff.email,
                request.professional.toProfessional(),
                request.extraGuiaInfo
            )
        } returns createdProcedures.success()

        coEvery {
            execIndicatorAuthorizerService.get(execAuthorizer.id)
        } returns execAuthorizer.success()

        val authorizedProcedure = createdProcedures
            .filter {
                it.status == MvAuthorizedProcedureStatus.AUTHORIZED
            }
            .map { ProcedureRequest(it.id) }

        val executionGroup = ExecutionGroup(
            id = authorizedProcedure.joinToString(separator = "").toRangeSafeUUID(),
            userEmail = staff.email,
            providerUnitId = execAuthorizer.providerUnitId
        )
        coEvery {
            executionGroupService.addExecutionGroup(
                ExecutionGroupRequest(
                    authorizerId = request.authorizerId,
                    idempotencyId = executionGroup.id,
                    procedures = authorizedProcedure,
                    userEmail = staff.email,
                    providerUnitId = execAuthorizer.providerUnitId
                ), false
            )
        } returns executionGroup.success()

        authenticatedAs(token, staff) {
            post(to = "/guia/create_exam_guia", body = request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#findByExecutionGroup should find guiaPS from execution group id`() {

        mockkStatic(LocalDateTime::class, LocalDate::class) {
            every { LocalDateTime.now() } returns now
            every { LocalDate.now() } returns nowDate

            coEvery {
                guiaAliceHouseService.findByExecutionGroup(guiaExecutionGroupId)
            } returns guiaPS.success()

            authenticatedAs(token, staff) {
                get("/guia/group/${guiaExecutionGroupId}") { response ->

                    assertThat(response).isSuccessfulJson()
                    assertThat(response).isOK()
                    assertThat(response.bodyAsText()).isEqualTo(toJson(guiaPS))

                }
            }
        }
    }

    @Test
    fun `#findByCode should find guiaExam from execution code`() {

        coEvery {
            guiaAliceHouseService.findByCode(guiaCode)
        } returns guiaExam.copy(date = nowDate).success()

        authenticatedAs(token, staff) {
            get("/guia/code/${guiaCode}") { response ->

                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
                assertThat(response.bodyAsText()).isEqualTo(toJson(guiaExam))

            }
        }

    }

    @Test
    fun `#findByExecutionGroup should find guiaHospitalization from execution group id`() {

        mockkStatic(LocalDateTime::class, LocalDate::class) {
            every { LocalDateTime.now() } returns now
            every { LocalDate.now() } returns nowDate

            coEvery {
                guiaAliceHouseService.findByExecutionGroup(guiaExecutionGroupId)
            } returns guiaHospitalization.success()

            authenticatedAs(token, staff) {
                get("/guia/group/${guiaExecutionGroupId}") { response ->

                    assertThat(response).isSuccessfulJson()
                    assertThat(response).isOK()
                    assertThat(response.bodyAsText()).isEqualTo(toJson(guiaHospitalization))

                }
            }
        }
    }

    @Test
    fun `#findByExecutionGroup throws exception when guia not find by execution group id`() {

        val expectedError = GuiaExecutionGroupNotFoundException()

        coEvery {
            guiaAliceHouseService.findByExecutionGroup(guiaExecutionGroupId)
        } returns expectedError.failure()

        authenticatedAs(token, staff) {
            get("/guia/group/${guiaExecutionGroupId}") { response ->

                assertThat(response).isBadRequest()
                val body = response.bodyAsJson<ErrorResponse>()
                assertThat(body)
                    .isEqualTo(ErrorResponse(code = expectedError.code, message = expectedError.message!!))

            }
        }
    }

    @Test
    fun `#findByCode throws exception when guia not find by execution group code`() {

        val expectedError = GuiaExecutionGroupNotFoundException()

        coEvery {
            guiaAliceHouseService.findByCode(guiaCode)
        } returns expectedError.failure()


        authenticatedAs(token, staff) {
            get("/guia/code/${guiaCode}") { response ->

                assertThat(response).isBadRequest()
                val body = response.bodyAsJson<ErrorResponse>()
                assertThat(body)
                    .isEqualTo(ErrorResponse(code = expectedError.code, message = expectedError.message!!))

            }
        }

    }

    @Test
    fun `#findByExecutionGroup throws exception when guia not has procedures`() {

        val expectedError = GuiaProcedureNotExistsException()

        coEvery {
            guiaAliceHouseService.findByExecutionGroup(guiaExecutionGroupId)
        } returns expectedError.failure()

        authenticatedAs(token, staff) {
            get("/guia/group/${guiaExecutionGroupId}") { response ->

                assertThat(response).isBadRequest()
                val body = response.bodyAsJson<ErrorResponse>()
                assertThat(body)
                    .isEqualTo(ErrorResponse(code = expectedError.code, message = expectedError.message!!))

            }
        }


    }

    @Test
    fun `#findByExecutionGroup throws unexpected exception on search guia`() {

        coEvery {
            guiaAliceHouseService.findByExecutionGroup(guiaExecutionGroupId)
        } returns Result.failure(Exception())

        authenticatedAs(token, staff) {
            get("/guia/group/${guiaExecutionGroupId}") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#findHistoryByPersonId should find guias historic from personId`() {

        coEvery {
            guiaAliceHouseService.findHistoryByPersonId(personId)
        } returns historics.success()

        authenticatedAs(token, staff) {
            get("/guia/member/${request.personId}/history") { response ->
                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
                assertThat(response.bodyAsText()).isEqualTo(toJson(historics))

            }
        }

    }

    @Test
    fun `#findHistoryByStatus should find guid history successful`() {

        val status = "PENDING"
        val page = 1
        val limit = 5
        val sortOrder = "ASC"
        val sortField = "createdAt"

        val executionGroupId = RangeUUID.generate()

        val person = TestModelFactory.buildPerson()

        val pendingProcedures = listOf(
            TestModelFactory.buildMvAuthorizedProcedure(
                status = MvAuthorizedProcedureStatus.PENDING,
                executionGroupId = executionGroupId,
                personId = personId
            ),
            TestModelFactory.buildMvAuthorizedProcedure(
                status = MvAuthorizedProcedureStatus.PENDING,
                executionGroupId = executionGroupId,
                personId = personId
            )
        )

        val authorizers = listOf(TestModelFactory.buildExecIndicatorAuthorizer())

        val providerUnits = listOf(
            TestModelFactory.buildProviderUnit(id = authorizers[0].providerUnitId).copy(
                address = TestModelFactory.buildStructuredAddress(
                    referencedModelId = authorizers[0].providerUnitId,
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
                )
            )
        )

        val executionGroup = TestModelFactory.buildExecutionGroup(
            id = executionGroupId,
            userEmail = staff.email,
            providerUnitId = authorizers[0].providerUnitId
        )

        val personInternalReferences = listOf(TestModelFactory.buildPersonInternalReference(personId = person.id))

        val expectedData = listOf(
            GuiaStaffAuthorizationHistoricResponse(
                executionGroupId = executionGroupId,
                type = pendingProcedures.first().extraGuiaInfo.procedureType,
                number = executionGroup.code,
                requestedAt = executionGroup.createdAt,
                member = GuiaMemberResponse(
                    id = person.id.id,
                    internalCode = personInternalReferences.first().internalCode,
                    socialName = person.fullSocialName,
                    registerName = person.fullRegisterName,
                    dateOfBirth = person.dateOfBirth,
                    nationalId = person.nationalId
                ),
                professional = GuiaProfessionalResponse(
                    name = pendingProcedures.first().requestedByProfessional.fullName ?: "",
                    council = pendingProcedures.first().requestedByProfessional.council.name,
                    councilNumber = pendingProcedures.first().requestedByProfessional.councilNumber,
                    councilState = pendingProcedures.first().requestedByProfessional.councilState.name,
                    specialty = GuiaProfessionalSpecialtyResponse(
                        id = pendingProcedures.first().requestedByProfessional.specialty?.id,
                        name = pendingProcedures.first().requestedByProfessional.specialty?.name
                    )
                ),
                provider = GuiaProviderResponse(
                    name = providerUnits.first().name,
                    address = providerUnits.first().address!!.formattedAddress(),
                    phone = providerUnits.first().phones.firstOrNull()?.phone,
                    cnpj = providerUnits.first().cnpj
                )
            )
        )

        val expectedResponse = GuiaHistoryPaginatorResponse(data = expectedData, total = 1, pages = 1, page = 1)


        coEvery {
            guiaAliceHouseService.findHistoricByStatus(
                status = MvAuthorizedProcedureStatus.valueOf(status),
                page = page, limit = limit, sortOrder = SortOrder.Ascending, sortField = sortField
            )
        } returns Pair(expectedData, expectedData.size).success()



        authenticatedAs(token, staff) {
            get("/guia/historic?status=$status&limit=$limit&page=$page&sortOrder=$sortOrder&sortField=$sortField") { response ->
                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
                assertThat(response.bodyAsText()).isEqualTo(toJson(expectedResponse))
            }
        }

    }

    @Test
    fun `#findHistoryByStatus throws unexpected exception on send invalid params`() {

        val status = "PENDING"
        val page = -1
        val limit = 0
        val sortOrder = "ASC"
        val sortField = "createdAt"

        authenticatedAs(token, staff) {
            get("/guia/historic?status=$status&limit=$limit&page=$page&sortOrder=$sortOrder&sortField=$sortField") { response ->
                assertThat(response).isBadRequest()
            }
        }

    }
}
