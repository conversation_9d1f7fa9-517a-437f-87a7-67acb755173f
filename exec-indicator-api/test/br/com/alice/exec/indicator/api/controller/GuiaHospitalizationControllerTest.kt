package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AnsDischargeMotive
import br.com.alice.data.layer.models.AttendanceCharacter
import br.com.alice.data.layer.models.HospitalizationHealthCondition
import br.com.alice.data.layer.models.HospitalizationType
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.api.converters.RelatedGuiaConverter.toRelatedGuiaResponseSingle
import br.com.alice.exec.indicator.client.GuiaHospitalizationService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.models.GuiaCreationProcedure
import br.com.alice.exec.indicator.models.GuiaCreationRequest
import br.com.alice.exec.indicator.models.GuiaHospitalizationCreationRequest
import br.com.alice.exec.indicator.models.GuiaHospitalizationExtensionCreationRequest
import br.com.alice.exec.indicator.models.HospitalizationInfoRequest
import br.com.alice.exec.indicator.models.HospitalizationInfoUpdateDatesRequest
import br.com.alice.exec.indicator.models.RelatedGuiaResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class GuiaHospitalizationControllerTest : ControllerTestHelper() {

    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val guiaHospitalizationService: GuiaHospitalizationService = mockk()

    private val controller = GuiaHospitalizationController(
        totvsGuiaService,
        guiaHospitalizationService,
    )

    private val person = TestModelFactory.buildPerson()
    private val mvAuthorizedProcedure = TestModelFactory.buildMvAuthorizedProcedure()
    private val referenceTotvsGuia = TestModelFactory.buildTotvsGuia()
    private val totvsGuia = TestModelFactory.buildTotvsGuia(personId = person.id)
    private val professionalIdentification = ProfessionalIdentification(
        councilNumber = "123456",
        councilState = State.CE,
        council = CouncilType.CRM,
        fullName = "Fulano de Tal",
        specialty = ProfessionalSpecialty(
            id = 1L,
            name = "Cardiologia"
        )
    )

    private val searchStatus = listOf(
        TotvsGuiaStatus.AUTHORIZED, TotvsGuiaStatus.PENDING, TotvsGuiaStatus.PARTIALLY_AUTHORIZED
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#create should create hospitalization guia without problems`() {
        val guiaCreationRequest = GuiaCreationRequest(
            type = MvUtil.TISS.EXAM,
            accidentIndication = AnsAccidentIndication.NOT_ACCIDENT,
            requestedAt = LocalDate.now(),
            newBorn = false,
            requester = professionalIdentification,
            procedures = listOf(
                GuiaCreationProcedure(
                    procedureId = "123456",
                    quantity = 1
                )
            ),
            personId = person.id.toUUID(),
            creatorCnpj = "12345678901234",
        )

        val providerUnitId = RangeUUID.generate()
        val hospitalizationInfoRequest = HospitalizationInfoRequest(
            providerUnitId = providerUnitId,
            hospitalizationType = HospitalizationType.CLINICAL,
            attendanceCharacter = AttendanceCharacter.ELECTIVE,
            suggestedDate = LocalDate.now(),
            numberOfDays = 5,
            chemotherapyIndication = false,
            opmeIndication = true,
            clinicalIndication = "dor aguda",
            healthCondition = HospitalizationHealthCondition(
                name = "Estável",
                code = "1"
            )
        )

        val hospitalizationGuiaCreationRequest = GuiaHospitalizationCreationRequest(
            guiaCreationRequest = guiaCreationRequest,
            hospitalizationInfoRequest = hospitalizationInfoRequest
        )

        coEvery {
            guiaHospitalizationService.createGuiaHospitalization(
                hospitalizationGuiaCreationRequest
            )
        } returns listOf(mvAuthorizedProcedure).success()

        authenticatedAs(token, staff) {
            post("v2/guia/hospitalization", hospitalizationGuiaCreationRequest) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#createExtension should create hospitalization extension guia`() {
        val request = GuiaHospitalizationExtensionCreationRequest(
            numberOfDays = 5,
            clinicalIndication = "dor aguda",
            requester = professionalIdentification,
            procedures = listOf(
                GuiaCreationProcedure(
                    procedureId = "123456",
                    quantity = 1
                )
            ),
            medicalRequestFileIds = emptyList(),
            type = MvUtil.TISS.EXTENSION,
            requestedAt = LocalDate.now(),
        )

        val expectedResponse = listOf(mvAuthorizedProcedure)

        coEvery {
            guiaHospitalizationService.createGuiaHospitalizationExtension(referenceTotvsGuia.id, request)
        } returns listOf(mvAuthorizedProcedure).success()

        authenticatedAs(token, staff) {
            post("v2/guia/${referenceTotvsGuia.id}/hospitalization_extension", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#findLastByPersonId should get last hospitalization`() {
        val cnpj = "123456789000123"
        val totvsGuias = listOf(
            totvsGuia.copy(
                type = MvUtil.TISS.HOSPITALIZATION,
                createdAt = LocalDateTime.now().minusDays(10),
                cnpj = cnpj
            ),
            totvsGuia.copy(
                type = MvUtil.TISS.HOSPITALIZATION,
                createdAt = LocalDateTime.now(),
                externalCode = "123456",
                status = TotvsGuiaStatus.AUTHORIZED,
                cnpj = cnpj
            ),
            totvsGuia.copy(
                type = MvUtil.TISS.HOSPITALIZATION,
                createdAt = LocalDateTime.now().minusDays(1),
                cnpj = cnpj
            )
        )
        val lastTotvsGuia = totvsGuias[1]

        coEvery {
            totvsGuiaService.findByPersonIdAndTypeAndCnpj(person.id, MvUtil.TISS.HOSPITALIZATION, cnpj)
        } returns totvsGuias

        val expectedResponse = lastTotvsGuia.success().toRelatedGuiaResponseSingle().get()

        authenticatedAs(token, staff) {
            get("v2/guia/${person.id}/hospitalization?cnpj=$cnpj") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            totvsGuiaService.findByPersonIdAndTypeAndCnpj(person.id, MvUtil.TISS.HOSPITALIZATION, cnpj)
        }
    }

    @Test
    fun `#findHospitalizationByTotvsGuiaId should get hospitalization by totvsGuiaId`() {
        val hospitalizationTotvsGuia = totvsGuia.copy(
            type = MvUtil.TISS.HOSPITALIZATION,
            createdAt = LocalDateTime.now(),
            externalCode = "123456",
            status = TotvsGuiaStatus.AUTHORIZED,
        )


        coEvery {
            totvsGuiaService.get(hospitalizationTotvsGuia.id)
        } returns hospitalizationTotvsGuia

        val expectedResponse = hospitalizationTotvsGuia.success().toRelatedGuiaResponseSingle().get()

        authenticatedAs(token, staff) {
            get("v2/guia/hospitalizations/${hospitalizationTotvsGuia.id}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { totvsGuiaService.get(hospitalizationTotvsGuia.id) }
    }

    @Test
    fun `#findLastByPersonId should return empty list when last totvsGuia doesn't exists`() {
        val cnpj = "123456789000123"

        coEvery {
            totvsGuiaService.findByPersonIdAndTypeAndCnpj(person.id, MvUtil.TISS.HOSPITALIZATION, cnpj)
        } returns NotFoundException().failure()

        authenticatedAs(token, staff) {
            get("v2/guia/${person.id}/hospitalization?cnpj=$cnpj") { response ->
                ResponseAssert.assertThat(response).isOKWithData(emptyList<RelatedGuiaResponse>())
            }
        }

        coVerifyOnce {
            totvsGuiaService.findByPersonIdAndTypeAndCnpj(person.id, MvUtil.TISS.HOSPITALIZATION, cnpj)
        }
    }

    @Test
    fun `#updateHospitalizationInfo should update hospitalization info`() {
        val request = HospitalizationInfoUpdateDatesRequest(
            guiaNumber = "123456",
            startDate = LocalDate.now(),
            dischargeDate = LocalDate.now(),
            dischargeMotive = AnsDischargeMotive.REQUESTED_DISCHARGE
        )

        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(totvsGuiaId = totvsGuia.id)

        coEvery {
            guiaHospitalizationService.updateHospitalizationDateByRequest(totvsGuia.id, request)
        } returns hospitalizationInfo

        authenticatedAs(token, staff) {
            put("v2/guia/hospitalization/${totvsGuia.id}/dates", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(hospitalizationInfo)
            }
        }
    }
}
