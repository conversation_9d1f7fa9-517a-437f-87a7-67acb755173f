package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.eita.nullvs.events.NullvsGuiaFileUploadEvent
import br.com.alice.exec.indicator.api.controller.GuiaFileUploadController.Companion.EITA_FILE_VAULT_STORAGE_DOMAIN
import br.com.alice.exec.indicator.api.controller.GuiaFileUploadController.Companion.EITA_FILE_VAULT_STORAGE_NAMESPACE
import br.com.alice.exec.indicator.api.models.GuiaFileUploadResponse
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.PersonVaultUploadByteArray
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.success
import io.ktor.http.HttpMethod
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class GuiaFileUploadControllerTest : ControllerTestHelper() {
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val controller = GuiaFileUploadController(
        fileVaultActionService,
        totvsGuiaService,
        kafkaProducerService
    )

    private val person = TestModelFactory.buildPerson()
    private val totvsGuia = TestModelFactory.buildTotvsGuia(
        externalCode = "1234"
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#uploadMedicalRequestFile should upload a file without problems`() {
        val personId = person.id

        val personVaultUploadByteArray = PersonVaultUploadByteArray(
            domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
            namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = "test.jpg",
            fileContent = ByteArray(0),
            fileType = FileType.fromExtension("jpg")!!,
            personId = personId,
            fileSize = 0L
        )

        val fileVault = TestModelFactory.buildFileVault()

        val vaultResponse = VaultResponse(
            url = fileVault.url,
            id = fileVault.id,
            type = fileVault.fileType,
            vaultUrl = fileVault.url,
            fileName = fileVault.originalFileName,
            fileSize = fileVault.fileSize
        )

        coEvery {
            fileVaultActionService.uploadFileAndGenerateSecuredLink(match {
                it.domain == personVaultUploadByteArray.domain &&
                        it.namespace == personVaultUploadByteArray.namespace &&
                        it.originalFileName == personVaultUploadByteArray.originalFileName &&
                        it.personId == personVaultUploadByteArray.personId
            })
        } returns vaultResponse.success()

        authenticatedAs(token, staff) {
            multipart(
                HttpMethod.Post,
                "v2/guia/medical_request_file",
                fileName = "test.jpg",
                parameters = mapOf(
                    "personId" to personId.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    GuiaFileUploadResponse(
                        fileName = fileVault.id.toString() + "." + fileVault.fileType,
                        id = fileVault.id,
                        originalFileName = fileVault.originalFileName,
                        url = fileVault.url
                    )
                )
            }
        }
    }

    @Test
    fun `#removeMedicalRequestFile should delete a file without problems`() {
        val personId = person.id
        val fileName = "test.jpg"

        coEvery {
            fileVaultActionService.deleteFile(
                match {
                    it.domain == EITA_FILE_VAULT_STORAGE_DOMAIN &&
                            it.namespace == EITA_FILE_VAULT_STORAGE_NAMESPACE &&
                            it.fileName == fileName &&
                            it.personId == personId
                }
            )
        } returns true.success()

        authenticatedAs(token, staff) {
            delete("v2/guia/medical_request_file/${personId}/${fileName}") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#addMedicalFilesToTotvsGuia should add a file to a guia without problems`() {
        val personId = person.id

        val personVaultUploadByteArray = PersonVaultUploadByteArray(
            domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
            namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = "test.jpg",
            fileContent = ByteArray(0),
            fileType = FileType.fromExtension("jpg")!!,
            personId = personId,
            fileSize = 0L
        )

        val fileVault = TestModelFactory.buildFileVault()

        val vaultResponse = VaultResponse(
            url = fileVault.url,
            id = fileVault.id,
            type = fileVault.fileType,
            vaultUrl = fileVault.url,
            fileName = fileVault.originalFileName,
            fileSize = fileVault.fileSize
        )

        coEvery {
            fileVaultActionService.uploadFileAndGenerateSecuredLink(match {
                it.domain == personVaultUploadByteArray.domain &&
                        it.namespace == personVaultUploadByteArray.namespace &&
                        it.originalFileName == personVaultUploadByteArray.originalFileName &&
                        it.personId == personVaultUploadByteArray.personId
            })
        } returns vaultResponse.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        coEvery {
            totvsGuiaService.update(totvsGuia.copy(medicalRequestFileIds = totvsGuia.medicalRequestFileIds + fileVault.id))
        } returns totvsGuia.success()

        coEvery { kafkaProducerService.produce(
            match { it is NullvsGuiaFileUploadEvent &&
                    it.payload.fileVaultId == fileVault.id &&
                    it.payload.guiaExternalCode == totvsGuia.externalCode
            }
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        authenticatedAs(token, staff) {
            multipart(
                HttpMethod.Post,
                "v2/guia/${totvsGuia.id}/file",
                fileName = "test.jpg",
                parameters = mapOf(
                    "personId" to personId.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    GuiaFileUploadResponse(
                        fileName = fileVault.id.toString() + "." + fileVault.fileType,
                        id = fileVault.id,
                        originalFileName = fileVault.originalFileName,
                        url = fileVault.url
                    )
                )
            }
        }

        coVerifyOnce {
            totvsGuiaService.get(totvsGuia.id)
        }

        coVerifyOnce {
            totvsGuiaService.update(totvsGuia.copy(medicalRequestFileIds = totvsGuia.medicalRequestFileIds + fileVault.id))
        }

        coVerifyOnce {
            fileVaultActionService.uploadFileAndGenerateSecuredLink(match {
                it.domain == personVaultUploadByteArray.domain &&
                        it.namespace == personVaultUploadByteArray.namespace &&
                        it.originalFileName == personVaultUploadByteArray.originalFileName &&
                        it.personId == personVaultUploadByteArray.personId
            })
        }

        coVerifyOnce { kafkaProducerService.produce(
            match { it is NullvsGuiaFileUploadEvent }
        ) }
    }

    @Test
    fun `#addMedicalFilesToTotvsGuia should not produce event if guide is not integrated yet`() {
        val totvsGuia = totvsGuia.copy(externalCode = null)

        val personId = person.id

        val personVaultUploadByteArray = PersonVaultUploadByteArray(
            domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
            namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = "test.jpg",
            fileContent = ByteArray(0),
            fileType = FileType.fromExtension("jpg")!!,
            personId = personId,
            fileSize = 0L
        )

        val fileVault = TestModelFactory.buildFileVault()

        val vaultResponse = VaultResponse(
            url = fileVault.url,
            id = fileVault.id,
            type = fileVault.fileType,
            vaultUrl = fileVault.url,
            fileName = fileVault.originalFileName,
            fileSize = fileVault.fileSize
        )

        coEvery {
            fileVaultActionService.uploadFileAndGenerateSecuredLink(match {
                it.domain == personVaultUploadByteArray.domain &&
                        it.namespace == personVaultUploadByteArray.namespace &&
                        it.originalFileName == personVaultUploadByteArray.originalFileName &&
                        it.personId == personVaultUploadByteArray.personId
            })
        } returns vaultResponse.success()

        coEvery {
            totvsGuiaService.get(totvsGuia.id)
        } returns totvsGuia.success()

        coEvery {
            totvsGuiaService.update(totvsGuia.copy(medicalRequestFileIds = totvsGuia.medicalRequestFileIds + fileVault.id))
        } returns totvsGuia.success()

        authenticatedAs(token, staff) {
            multipart(
                HttpMethod.Post,
                "v2/guia/${totvsGuia.id}/file",
                fileName = "test.jpg",
                parameters = mapOf(
                    "personId" to personId.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    GuiaFileUploadResponse(
                        fileName = fileVault.id.toString() + "." + fileVault.fileType,
                        id = fileVault.id,
                        originalFileName = fileVault.originalFileName,
                        url = fileVault.url
                    )
                )
            }
        }

        coVerifyOnce {
            totvsGuiaService.get(totvsGuia.id)
        }

        coVerifyOnce {
            totvsGuiaService.update(totvsGuia.copy(medicalRequestFileIds = totvsGuia.medicalRequestFileIds + fileVault.id))
        }

        coVerifyOnce {
            fileVaultActionService.uploadFileAndGenerateSecuredLink(match {
                it.domain == personVaultUploadByteArray.domain &&
                        it.namespace == personVaultUploadByteArray.namespace &&
                        it.originalFileName == personVaultUploadByteArray.originalFileName &&
                        it.personId == personVaultUploadByteArray.personId
            })
        }

        coVerifyNone { kafkaProducerService.produce(
            match { it is NullvsGuiaFileUploadEvent }
        ) }
    }

}
