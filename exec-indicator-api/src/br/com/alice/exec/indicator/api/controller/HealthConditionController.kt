package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.HealthConditionCodeType.CID_10
import br.com.alice.healthcondition.client.HealthConditionService
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import java.util.UUID

class HealthConditionController(
    private val healthConditionService: HealthConditionService,
) : Controller() {

    companion object {
        const val MAXIMUM_CID_CODE_SIZE = 4
    }

    suspend fun list(params: Parameters): Response {
        val searchTerm = parseQuery(params) ?: ""
        return healthConditionService.search(searchTerm)
            .map { healthConditions ->
                healthConditions.filter { it.codeType == CID_10 && it.code!!.length <= MAXIMUM_CID_CODE_SIZE }
            }
            .mapEach {
                HealthConditionResponse(it.id, it.name, it.displayName, it.code, it.codeType.name)
            }.foldResponse()
    }

}


data class HealthConditionResponse(
    val id: UUID,
    val name: String,
    val displayName: String,
    val code: String? = null,
    val codeType: String,
)
