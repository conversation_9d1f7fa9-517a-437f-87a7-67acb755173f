package br.com.alice.exec.indicator.api.controller

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.provider.client.Filter
import br.com.alice.provider.client.MedicalSpecialtyService
import io.ktor.http.Parameters
import java.util.UUID

class SpecialtyController(
    private val medicalSpecialtyService: MedicalSpecialtyService,
) : Controller() {

    suspend fun list(params: Parameters): Response {
        val range = parseRange(params)
        val searchTerm = parseQuery(params)
        return medicalSpecialtyService.findBy(
            Filter(
                name = searchTerm,
                types = listOf(MedicalSpecialtyType.SPECIALTY),
                active = true
            ),
            range
        ).mapEach { SpecialtyResponse(it.id, it.name, it.cboCode?.code) }.foldResponse()
    }

}

data class SpecialtyResponse(
    val id: UUID,
    val name: String,
    val cboCode: String? = null
)
