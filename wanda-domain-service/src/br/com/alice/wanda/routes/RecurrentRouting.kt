package br.com.alice.wanda.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.wanda.controllers.FollowUpController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.recurringRoutes() {
    val followUpController by inject<FollowUpController>()

    route("/recurring_subscribers") {
        post("/execute_auto_follow_ups") {
            asyncLayer {
                coHandler(followUpController::executeAutoFollowUps)
            }
        }
        post("/execute_auto_follow_ups_on_weekends") {
            asyncLayer {
                coHandler(followUpController::executeAutoFollowUps)
            }
        }
    }
}
