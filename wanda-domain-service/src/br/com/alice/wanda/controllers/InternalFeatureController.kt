package br.com.alice.wanda.controllers

import br.com.alice.common.Response
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.services.PersonHealthEventDataService
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import com.github.kittinunf.result.map
import io.opentelemetry.api.trace.Span
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class InternalFeatureController(
    private val personHealthEventDataService: PersonHealthEventDataService,
    private val kafkaProducerService: KafkaProducerService
) : WandaController() {

    suspend fun changeActiveTasksOwner(request: ChangeActiveTasksOwnerRequest): Response =
        span("changeActiveTasksOwner") { span ->
            withWandaEnvironment {
                span.setChangeActiveTasksOwnerRequestInfo(request)
                val successCount = AtomicInteger(0)
                val currentOwnerStaffId = request.currentOwnerStaffId
                val validStatuses = listOf(
                    PersonHealthEventStatus.NOT_STARTED,
                    PersonHealthEventStatus.IN_PROGRESS,
                    PersonHealthEventStatus.PENDING_RELEASE,
                    PersonHealthEventStatus.PENDING_DATA
                )

                personHealthEventDataService
                    .find {
                        where {
                            this.staffId.eq(currentOwnerStaffId) and
                                    this.status.inList(validStatuses)
                        }
                            .offset { request.offset }
                            .limit { request.limit }
                    }
                    .map { personHealthEvents ->
                        val newOwnerStaffId = request.newOwnerStaffId
                        personHealthEvents
                            .pmap { personHealthEvent -> personHealthEvent.copy(staffId = newOwnerStaffId) }
                            .let { personHealthEventsToUpdate ->
                                personHealthEventDataService.updateList(personHealthEventsToUpdate)
                                    .then { list ->
                                        list.map { updated ->
                                            val personHealthEventUpdatedEvent = PersonHealthEventUpdatedEvent(updated)
                                            kafkaProducerService.produce(personHealthEventUpdatedEvent)
                                            successCount.incrementAndGet()
                                        }
                                    }
                            }
                    }

                span.setAttribute("success_count", successCount.get())
                successCount.toResponse()
            }
        }

}

data class ChangeActiveTasksOwnerRequest(
    val currentOwnerStaffId: UUID,
    val newOwnerStaffId: UUID,
    val offset: Int = 0,
    val limit: Int = 100
)

private fun Span.setChangeActiveTasksOwnerRequestInfo(request: ChangeActiveTasksOwnerRequest) {
    setAttribute("current_owner_staff_id", request.currentOwnerStaffId)
    setAttribute("new_owner_staff_id", request.newOwnerStaffId)
    setAttribute("offset", request.offset)
    setAttribute("limit", request.limit)
}
