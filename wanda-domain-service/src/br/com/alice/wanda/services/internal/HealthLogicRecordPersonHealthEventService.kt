package br.com.alice.wanda.services.internal

import br.com.alice.data.layer.models.HealthLogicRecord
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory.INTERNAL_TASK
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.HEALTH_LOGIC_RECORD
import br.com.alice.data.layer.models.PersonHealthEventStatus.FINISHED
import br.com.alice.healthlogic.client.HealthLogicsService
import br.com.alice.healthlogic.models.healthLogics.HealthLogic
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgram
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class HealthLogicRecordPersonHealthEventService(
    private val internalPersonHealthEventService: InternalPersonHealthEventService,
    private val healthLogicsService: HealthLogicsService,
) {
    suspend fun upsertPersonHealthEvent(
        healthLogicRecord: HealthLogicRecord
    ): Result<Boolean, Throwable> =
        Result.of {
            val program = healthLogicsService.getProgramDetails(
                healthLogicId = healthLogicRecord.healthLogicNodeId,
                healthLogicProgramId = healthLogicRecord.currentNodeId
            ).get()

            if (program.actions.isEmpty()) return false.success()

            val healthLogic = healthLogicsService.get(healthLogicRecord.healthLogicNodeId).get()

            val personHealthEvent = buildPersonHealthEvent(healthLogicRecord, healthLogic, program)

            internalPersonHealthEventService.upsertPersonHealthEvent(personHealthEvent).map { true }.get()
        }

    private fun buildPersonHealthEvent(
        healthLogicRecord: HealthLogicRecord,
        healthLogic: HealthLogic,
        program: HealthLogicProgram,
    ) = PersonHealthEvent(
            personId = healthLogicRecord.personId,
            category = INTERNAL_TASK,
            title = program.name,
            description = """
                |Programa atualizado: ${program.name} - ${healthLogic.name}
            """.trimIndent(),
            eventDate = healthLogicRecord.addedAt,
            status = FINISHED,
            referencedModelClass = HEALTH_LOGIC_RECORD,
            referencedModelId = healthLogicRecord.id.toString()
        )
}
