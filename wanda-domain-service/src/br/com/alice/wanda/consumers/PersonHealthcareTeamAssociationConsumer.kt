package br.com.alice.wanda.consumers

import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.HealthcareAdditionalTeamType.CARE_COORDINATION_NURSE
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory.INTERNAL_TASK
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.HEALTHCARE_MAP
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.marauders.map.event.HealthcareMapUpdatedEvent
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.success

class PersonHealthcareTeamAssociationConsumer(
    private val personHealthEventService: InternalPersonHealthEventService,
    private val healthcareTeamService: HealthcareTeamService,
    private val healthcareAdditionalTeamService: HealthcareAdditionalTeamService
) : CreatePersonHealthEventConsumer(healthcareTeamService, personHealthEventService) {

    suspend fun updateCaseRecordsHealthPlanTask(event: PersonHealthcareTeamAssociationUpdatedEvent) =
        withSubscribersEnvironment {
            val associationUpdate = event.payload
            healthcareTeamService.get(associationUpdate.newHealthcareTeamId).flatMapPair {
                personHealthEventService.findCaseRecordTasksByPersonId(
                    personId = associationUpdate.personId
                )
            }.flatMap { (personHealthEvents, healthcareTeam) ->
                personHealthEvents.map { personHealthEvent ->
                    personHealthEventService.updatePersonHealthEventById(
                        personHealthEvent.copy(staffId = healthcareTeam.physicianStaffId)
                    )
                }.lift()
            }
        }

    suspend fun notifyCareCoordinationOfNewTarget(event: HealthcareMapUpdatedEvent) =
        event.payload.healthcareMap
            .takeIf { it.isTarget() }
            ?.let {
                val careCoordTeamId = healthcareAdditionalTeamService.getActiveByType(
                    staffType = CARE_COORDINATION_NURSE
                ).getOrNullIfNotFound()
                    ?.first()
                    ?.id
                    ?: return true.success()

                processEvent(
                    personId = event.payload.healthcareMap.personId,
                    upsert = true
                ) {
                    val date = event.payload.healthcareMap.riskAddedAt
                        ?: event.payload.healthcareMap.healthcareTeamAddedAt
                    PersonHealthEvent(
                        personId = event.payload.healthcareMap.personId,
                        healthcareTeamId = it?.id,
                        healthcareAdditionalTeamId = careCoordTeamId,
                        category = INTERNAL_TASK,
                        title = "Novo membro alvo adicionado",
                        description = "Membro alvo alocado em time de saúde. Risco Magenta: ${event.payload.healthcareMap.riskValue}",
                        eventDate = date,
                        dueDate = date.plusDays(2),
                        status = NOT_STARTED,
                        referencedModelId = event.payload.healthcareMap.id.toString(),
                        referencedModelClass = HEALTHCARE_MAP
                    )
                }
            } ?: true.success()
}
