package br.com.alice.wanda.consumers

import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.event.LowerProgramPriorityCalculatedEvent
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class LowerProgramPriorityCalculatedEventConsumer(
    private val healthcareTeamService: HealthcareTeamService,
    private val internalPersonHealthEventService: InternalPersonHealthEventService
) : Consumer() {

    suspend fun handleEvent(event: LowerProgramPriorityCalculatedEvent) = withSubscribersEnvironment {
        val healthLogicRecord = event.payload.currentHealthLogicRecord
        val healthLogic = event.payload.healthLogic
        val program = event.payload.calculatedProgram

        val ignoreLogic = FeatureService.anyInList(
            FeatureNamespace.WANDA,
            "ignore_logics_for_task",
            listOf(healthLogic.id.toString()),
            false
        )

        if (ignoreLogic) return@withSubscribersEnvironment true.success()

        val healthcareTeam = healthcareTeamService.getHealthcareTeamByPerson(healthLogicRecord.personId).getOrNullIfNotFound()
            ?: return@withSubscribersEnvironment false.success()


        val event = PersonHealthEvent(
            personId = healthLogicRecord.personId,
            staffId = healthcareTeam.physicianStaffId,
            healthcareTeamId = healthcareTeam.id,
            category = PersonHealthEventCategory.INTERNAL_TASK,
            title = "Validar programa ${program.name}",
            description = """
                Programa calculado automaticamente ${program.name} para 
                lógica ${healthLogic.name} é menos prioritário que programa 
                atual. Valide manualmente.
            """,
            dueDate = event.eventDate.plusDays(5),
            eventDate = event.eventDate,
            status = PersonHealthEventStatus.NOT_STARTED,
            referencedModelId = healthLogicRecord.id.toString(),
            referencedModelClass = PersonHealthEventReferenceModel.HEALTH_LOGIC_RECORD
        )

        internalPersonHealthEventService.upsertPersonHealthEvent(event).map { true }
    }


}
