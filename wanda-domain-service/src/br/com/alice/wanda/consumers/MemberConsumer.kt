package br.com.alice.wanda.consumers

import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.membership.model.events.PersonTaskUpsertedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic.personTaskToPersonHealthEvent
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic.unifyPersonTaskInOneHealthEvent
import br.com.alice.wanda.services.MembershipService
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import java.time.LocalDateTime

class MemberConsumer(
    private val membershipService: MembershipService,
    private val internalPersonHealthEventService: InternalPersonHealthEventService,
    healthcareTeamService: HealthcareTeamService
) : CreatePersonHealthEventConsumer(healthcareTeamService, internalPersonHealthEventService) {

    suspend fun handleMemberCancelled(event: MemberCancelledEvent) = withSubscribersEnvironment {
        membershipService.cancelPersonHealthEventFromInactiveMember(event.payload.member)
    }

    suspend fun upsertPersonTaskPersonHealthEvent(event: PersonTaskUpsertedEvent) =
        processEvent(
            personId = event.payload.task.personId,
            upsert = true
        ) { healthcareTeam ->
            val task = event.payload.task

            internalPersonHealthEventService.findByCategoryPersonIdAndTime(
                personId = task.personId,
                category = PersonHealthEventCategory.PERSON_TASK_TEST_REQUEST,
                from = LocalDateTime.now().minusHours(12)
            )?.let {
                unifyPersonTaskInOneHealthEvent(task, it)
            } ?: personTaskToPersonHealthEvent(task, healthcareTeam)
        }
}
