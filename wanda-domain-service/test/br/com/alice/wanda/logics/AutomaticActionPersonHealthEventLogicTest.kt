package br.com.alice.wanda.logics

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.AppointmentType.FOLLOW_UP_VISIT
import br.com.alice.data.layer.models.AppointmentType.IMMERSION
import br.com.alice.data.layer.models.AppointmentType.STATEMENT_OF_HEALTH
import br.com.alice.data.layer.models.AutomaticFollowUpContent
import br.com.alice.data.layer.models.AutomaticFollowUpType
import br.com.alice.data.layer.models.PersonHealthEventCategory.AA_HEALTH_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_IMMERSION
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_STATEMENT_OF_HEALTH
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.APPOINTMENT_PERSON_HEALTH_EVENT_SCRIPT_ACTION
import br.com.alice.data.layer.models.PersonHealthEventScriptAction
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.staff.models.HealthcareTeamInfo
import br.com.alice.staff.models.StaffInfo
import br.com.alice.wanda.logics.AutomaticActionPersonHealthEventLogic.appointmentActionToPersonHealthEvent
import io.mockk.clearAllMocks
import io.mockk.mockkObject
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class EtaActionPersonHealthEventLogicTest {

    private val now = LocalDateTime.now()
    private val personId = PersonId()
    private val staffId = RangeUUID.generate()
    private val healthcareTeam =
        TestModelFactory.buildHealthcareTeam(physicianStaffId = staffId)
    private val healthCareTeamInfo = HealthcareTeamInfo(
        id = healthcareTeam.id,
        physicianInfo = StaffInfo(
            id = healthcareTeam.physicianStaffId,
            firstName = "Test",
            profileImageUrl = "url"
        ),
        nurseInfo = StaffInfo(
            id = healthcareTeam.nurseStaffId!!,
            firstName = "Test",
            profileImageUrl = "url"
        ),
        digitalCareNurses = emptyList()
    )
    private val appointment =
        TestModelFactory.buildAppointment(
            personId = personId,
            type = ASSISTANCE_CARE
        )
    private val scriptAction = PersonHealthEventScriptAction(
        name = "event-name",
        responsible = Role.MANAGER_PHYSICIAN,
        action = ScriptAction(type = ScriptActionType.PERSON_HEALTH_EVENT)
    )
    private val expected = TestModelFactory.buildPersonHealthEvent(
        personId = personId,
        staffId = staffId,
        healthcareTeamId = healthcareTeam.id,
        title = scriptAction.name,
        description = appointment.content,
        eventDate = appointment.createdAt,
        referencedModelId = "${appointment.id}_${scriptAction.id}",
        referencedModelClass = APPOINTMENT_PERSON_HEALTH_EVENT_SCRIPT_ACTION,
        dueDate = now.plusDays(scriptAction.dueDate.toLong()).plusHours(scriptAction.dueHour.toLong()),
        referencedLinks = listOf(
            ReferencedLink(
                id = scriptAction.id.toString(),
                model = scriptAction.classSimpleName()
            )
        ),
        category = AA_HEALTH_FOLLOW_UP,
        status = NOT_STARTED
    )
    @BeforeTest
    fun setup() {
        mockkObject(PersonHealthEventLogic)
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#appointmentActionToPersonHealthEvent`() {
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            referencedLinks = listOf(
                ReferencedLink(
                    id = "channelId",
                    model = "Channel"
                ),
                ReferencedLink(
                    id = scriptAction.id.toString(),
                    model = scriptAction.classSimpleName()
                )
            ),
            channelId = "channelId"
        )
        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment.copy(channelId="channelId"),
            scriptAction = scriptAction,
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - without healthcareTeam`() {
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            staffId = FALLBACK_RESPONSIBLE_NURSE_STAFF_ID,
            healthcareTeamId = null,
            referencedLinks = listOf(
                ReferencedLink(
                    id = "channelId",
                    model = "Channel"
                ),
                ReferencedLink(
                    id = scriptAction.id.toString(),
                    model = scriptAction.classSimpleName()
                )
            ),
            channelId = "channelId"
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment.copy(channelId="channelId"),
            scriptAction = scriptAction,
            healthcareTeam = null,
            now = now
        )

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - without assistanceCare`() {
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            referencedLinks =
            listOf(
                ReferencedLink(
                    id = scriptAction.id.toString(),
                    model = scriptAction.classSimpleName()
                )
            )
        )
        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment,
            scriptAction = scriptAction,
            healthcareTeam = healthCareTeamInfo,
            now = now
        )

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - IMMERSION`() {
        val appointment = appointment.copy(type = IMMERSION)
        val expected = expected.copy(
            category = APPOINTMENT_IMMERSION,
            createdByStaffId = appointment.staffId
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment,
            scriptAction = scriptAction,
            healthcareTeam = healthCareTeamInfo,
            now = now
        )

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - FOLLOW_UP_VISIT`() {
        val appointment = appointment.copy(type = FOLLOW_UP_VISIT)
        val expected = expected.copy(
            category = APPOINTMENT_FOLLOW_UP,
            createdByStaffId = appointment.staffId
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment,
            scriptAction = scriptAction,
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - STATEMENT_OF_HEALTH`() {
        val expected = expected.copy(
            category = APPOINTMENT_STATEMENT_OF_HEALTH,
            createdByStaffId = appointment.staffId
        )
        val appointment = appointment.copy(type = STATEMENT_OF_HEALTH)

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment,
            scriptAction = scriptAction,
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - FOLLOW_UP_AUTO`() {
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            automaticFollowUp = true,
            automaticFollowUpMessage = "Como você está se sentindo em relação à sua dor",
            automaticFollowUpContent = AutomaticFollowUpContent(
                message = "Como você está se sentindo em relação à sua dor",
                type = AutomaticFollowUpType.ACUTE_CASE
            ),
            referencedLinks = listOf(
                ReferencedLink(id = "channelId", model = "Channel"),
                ReferencedLink(id = scriptAction.id.toString(), model = "PersonHealthEventScriptAction")
            ),
            channelId = "channelId"
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment.copy(channelId = "channelId"),
            scriptAction = scriptAction.copy(
                autoExec = true,
                additionalFupMessage = "Como você está se sentindo em relação à sua dor"
            ),
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - CHANNEL_FOLLOW_UP`() {
        val channelFupId = RangeUUID.generate()
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            automaticFollowUp = true,
            automaticFollowUpMessage = "Como você está se sentindo em relação à sua dor",
            automaticFollowUpContent = AutomaticFollowUpContent(
                message = "Como você está se sentindo em relação à sua dor",
                type = null,
                channelFupId = channelFupId
            ),
            referencedLinks = listOf(
                ReferencedLink(id = "channelId", model = "Channel"),
                ReferencedLink(id = scriptAction.id.toString(), model = "PersonHealthEventScriptAction")
            ),
            channelId = "channelId"
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment.copy(channelId="channelId"),
            scriptAction = scriptAction.copy(
                autoExec = true,
                additionalFupMessage = "Como você está se sentindo em relação à sua dor",
                channelFupId = channelFupId
            ),
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#appointmentActionToPersonHealthEvent - can not be automatic follow up when channel id is null`() {
        val expected = expected.copy(
            createdByStaffId = appointment.staffId,
            automaticFollowUp = false,
            automaticFollowUpMessage = null,
            automaticFollowUpContent = null,
            referencedLinks = listOf(
                ReferencedLink(id = scriptAction.id.toString(), model = "PersonHealthEventScriptAction")
            )
        )

        val result = appointmentActionToPersonHealthEvent(
            appointment = appointment,
            scriptAction = scriptAction.copy(
                autoExec = true,
                additionalFupMessage = "Como você está se sentindo em relação à sua dor"
            ),
            healthcareTeam = healthCareTeamInfo,
            now = now
        )
        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }
}
