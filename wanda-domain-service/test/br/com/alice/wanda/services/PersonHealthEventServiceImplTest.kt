package br.com.alice.wanda.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.minLocalDateTimeWithSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventAggregator
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventCategory.AA_IMMERSION_HEALTH_FOLLOW_UP
import br.com.alice.data.layer.models.PersonHealthEventCategory.ADM_ATTENDANCE
import br.com.alice.data.layer.models.PersonHealthEventCategory.ALICE_AT_HOME
import br.com.alice.data.layer.models.PersonHealthEventCategory.APPOINTMENT_COMMUNITY
import br.com.alice.data.layer.models.PersonHealthEventCategory.INTERNAL_TASK
import br.com.alice.data.layer.models.PersonHealthEventCategory.SUMMARY_EMERGENCY
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.APPOINTMENT_SCHEDULE
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.PersonHealthEventStatus.IN_PROGRESS
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.data.layer.services.PersonHealthEventDataService
import br.com.alice.wanda.client.PersonHealthEventChannelFup
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import br.com.alice.wanda.logics.PersonHealthEventLogic
import br.com.alice.wanda.logics.PredicateLogic
import br.com.alice.wanda.model.HealthEventSummary
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyAll
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.verifyAll
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonHealthEventServiceImplTest {
    private val dataService: PersonHealthEventDataService = mockk()
    private val internalPersonHealthEventService: InternalPersonHealthEventService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = PersonHealthEventServiceImpl(
        dataService,
        internalPersonHealthEventService,
        kafkaProducerService,
    )

    private val limitMonths = LocalDateTime.parse("2019-05-15T10:00:00").atBeginningOfTheDay()

    @AfterTest
    fun confirmMocks() = confirmVerified(dataService, internalPersonHealthEventService, kafkaProducerService)

    private val model = TestModelFactory.buildPersonHealthEvent()
    private val mockedPersonHealthEventList = listOf(
        TestModelFactory.buildPersonHealthEvent(dueDate = LocalDateTime.now().plusDays(1)),
        TestModelFactory.buildPersonHealthEvent(dueDate = LocalDateTime.now().plusDays(1)),
        TestModelFactory.buildPersonHealthEvent(dueDate = LocalDateTime.now().plusDays(1)),
        TestModelFactory.buildPersonHealthEvent(dueDate = LocalDateTime.now().plusDays(5))
    )
    private val testPerson = TestModelFactory.buildPerson()

    private val staff = TestModelFactory.buildHealthcareTeamPhysician()
    private val allowedCategories = listOf(
        INTERNAL_TASK,
        ADM_ATTENDANCE,
        ALICE_AT_HOME
    )
    private val staffIdToSearch = RangeUUID.generate()
    private val additionalTeamIdToSearch = RangeUUID.generate()
    private val dueDateToSearch = LocalDateTime.now()
    private val categoriesToSearch = listOf(PersonHealthEventCategory.AA_FOLLOW_UP)
    private val testPersonId = PersonId()
    private val dateNow = LocalDateTime.now()

    private val countPredicate = PredicateLogic.buildSummaryCountPredicate(staffIdToSearch, dueDateToSearch, categoriesToSearch)
    private val dueCountPredicate = PredicateLogic.buildSummaryDueCountPredicate(staffIdToSearch, dueDateToSearch, categoriesToSearch)
    private val automaticFollowUpCountPredicate = PredicateLogic.buildSummaryAutomaticFollowUpCountPredicate(staffIdToSearch, dueDateToSearch, categoriesToSearch)


    @Test
    fun `#findActivesByPersonIds calls dataService filtering by personIds and status`() = runBlocking {
        val personIds = listOf(PersonId())

        coEvery { dataService.find(any()) } returns mockedPersonHealthEventList.success()

        val result = service.findActivesByPersonIds(personIds)
        assertThat(result).isSuccessWithData(mockedPersonHealthEventList)

        coVerify(exactly = 1) {
            dataService.find(queryEq {
                where { this.personId.inList(personIds) and this.status.inList(listOf(NOT_STARTED, IN_PROGRESS)) }
            })
        }
    }

    @Test
    fun `#create should call InternalPersonHealthEvent and return success`() = runBlocking {
        coEvery { internalPersonHealthEventService.addPersonHealthEvent(model) } returns model.success()

        val result = service.create(model)

        assertThat(result).isSuccess()
        assertThat(result.get()).isEqualTo(model)
        coVerifyOnce { internalPersonHealthEventService.addPersonHealthEvent(any()) }
    }

    @Test
    fun `#update of AppointmentSchedule should call InternalPersonHealthEvent, produce an event and return success`() =
        runBlocking {
            val updateRequest = model.copy(
                title = "${model.title} updated",
                description = "${model.description} updated",
                referencedModelClass = APPOINTMENT_SCHEDULE,
                version = 1,
            )

            val expectedResponse = PersonHealthEvent(
                personId = updateRequest.personId,
                staffId = updateRequest.staffId,
                title = updateRequest.title,
                description = updateRequest.description,
                category = updateRequest.category,
                status = updateRequest.status,
                dueDate = updateRequest.dueDate,
                referencedModelClass = updateRequest.referencedModelClass,
                version = 1,
            )

            coEvery {
                internalPersonHealthEventService.updatePersonHealthEventById(updateRequest)
            } returns expectedResponse.success()

            coEvery {
                kafkaProducerService.produce(
                    match { it: PersonHealthEventUpdatedEvent ->
                        it.payload.personHealthEvent == expectedResponse
                    }
                )
            } returns mockk()

            val result = service.update(updateRequest)

            assertThat(result).isSuccess()
            assertThat(result.get()).isEqualTo(expectedResponse)
            coVerifyOnce { internalPersonHealthEventService.updatePersonHealthEventById(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#findByFilters should return the correct list of events when given due_date is a period`() =
        runBlocking {
            mockkStatic(LocalDateTime::class) {
                every { LocalDateTime.now().minusMonths(3) } returns limitMonths
                val dueDateFilter = LocalDateTime.parse("2019-06-15T10:00:00")
                mockkObject(PersonHealthEventLogic)
                val filter =
                    "{\"due_date\": {\"first\": \"${dueDateFilter}\", \"second\": \"${dueDateFilter.plusDays(1)}\"}}"
                val sortBy = "due_date"
                val sortOrder = "asc"
                val range = IntRange(0, 49)

                val expectedDueDateSince = dueDateFilter.minLocalDateTimeWithSaoPauloTimeZone()
                val expectedDueDateUntil = expectedDueDateSince.plusDays(2)

                val mockEvents =
                    mockedPersonHealthEventList.filter { it.dueDate!! >= expectedDueDateSince && it.dueDate!! < expectedDueDateUntil }
                val eventAggregator = PersonHealthEventAggregator(
                    events = mockEvents,
                    count = mockEvents.size
                )

                val dueDateGreaterEqPredicate =
                    Predicate.greaterEq(PersonHealthEventDataService.FieldOptions().dueDate, expectedDueDateSince)
                val dueDateLessPredicate =
                    Predicate.less(PersonHealthEventDataService.FieldOptions().dueDate, expectedDueDateUntil)
                val expectedPredicates = dueDateGreaterEqPredicate
                    .and(dueDateLessPredicate)

                every { PersonHealthEventLogic.allowedCategoriesFromStaff(staff) } returns allowedCategories
                every { PersonHealthEventLogic.isHealthManagement(staff) } returns false

                coEvery {
                    dataService.find(queryEq {
                        where { expectedPredicates }
                            .orderBy { PersonHealthEventDataService.OrderingOptions().dueDate }
                            .sortOrder { SortOrder.Ascending }
                            .offset { range.first }
                            .limit { range.count() }
                    })
                } returns mockEvents.success()

                val result =
                    service.findByFilters(filter, sortBy, sortOrder, range, staff)

                assertThat(result).isSuccessWithData(eventAggregator)
                coVerifyOnce { dataService.find(any()) }
            }
        }

    @Test
    fun `#findByFilters should return the correct list of events when given due_date is just since date`() =
        runBlocking {
            mockkStatic(LocalDateTime::class) {
                every { LocalDateTime.now().minusMonths(3) } returns limitMonths
                val dueDateFilter = LocalDateTime.parse("2019-06-15T10:00:00")
                mockkObject(PersonHealthEventLogic)
                val filter = "{\"due_date\": {\"first\": \"${dueDateFilter}\"}}"
                val sortBy = "due_date"
                val sortOrder = "asc"
                val range = IntRange(0, 49)
                val expectedDueDateSince = dueDateFilter.minLocalDateTimeWithSaoPauloTimeZone()

                val mockEvents =
                    mockedPersonHealthEventList.filter { it.dueDate!! >= expectedDueDateSince }
                val eventAggregator = PersonHealthEventAggregator(
                    events = mockEvents,
                    count = mockEvents.size
                )

                val dueDateGreaterEqPredicate =
                    Predicate.greaterEq(PersonHealthEventDataService.FieldOptions().dueDate, expectedDueDateSince)

                every { PersonHealthEventLogic.allowedCategoriesFromStaff(staff) } returns allowedCategories
                every { PersonHealthEventLogic.isHealthManagement(staff) } returns false

                coEvery {
                    dataService.find(queryEq {
                        where { dueDateGreaterEqPredicate }
                            .orderBy { PersonHealthEventDataService.OrderingOptions().dueDate }
                            .sortOrder { SortOrder.Ascending }
                            .offset { range.first }
                            .limit { range.count() }
                    })
                } returns mockEvents.success()

                val result =
                    service.findByFilters(filter, sortBy, sortOrder, range, staff)

                assertThat(result).isSuccessWithData(eventAggregator)
                coVerifyOnce { dataService.find(any()) }
            }
        }

    @Test
    fun `#findByFilters should return the correct list of events when given due_date is just until date`() =
        runBlocking {
            mockkStatic(LocalDateTime::class) {
                every { LocalDateTime.now().minusMonths(3) } returns limitMonths
                val dueDateFilter = LocalDateTime.parse("2019-06-15T10:00:00")
                mockkObject(PersonHealthEventLogic)
                val filter = "{\"due_date\": {\"second\": \"${dueDateFilter}\"}}"
                val sortBy = "due_date"
                val sortOrder = "asc"
                val range = IntRange(0, 49)
                val expectedDueDateUntil = dueDateFilter.minLocalDateTimeWithSaoPauloTimeZone().plusDays(1)

                val mockEvents =
                    mockedPersonHealthEventList.filter { it.dueDate!! < expectedDueDateUntil }
                val eventAggregator = PersonHealthEventAggregator(
                    events = mockEvents,
                    count = mockEvents.size
                )

                val dueDateLessPredicate =
                    Predicate.less(PersonHealthEventDataService.FieldOptions().dueDate, expectedDueDateUntil)
                val expectedPredicates =
                    Predicate.greaterEq(PersonHealthEventDataService.FieldOptions().dueDate, limitMonths)
                        .and(dueDateLessPredicate)

                every { PersonHealthEventLogic.allowedCategoriesFromStaff(staff) } returns allowedCategories
                every { PersonHealthEventLogic.isHealthManagement(staff) } returns false

                coEvery {
                    dataService.find(queryEq {
                        where { expectedPredicates }
                            .orderBy { PersonHealthEventDataService.OrderingOptions().dueDate }
                            .sortOrder { SortOrder.Ascending }
                            .offset { range.first }
                            .limit { range.count() }
                    })
                } returns mockEvents.success()

                val result =
                    service.findByFilters(filter, sortBy, sortOrder, range, staff)

                assertThat(result).isSuccessWithData(eventAggregator)
                coVerifyOnce { dataService.find(any()) }
            }
        }

    @Test
    fun `#findByPersonIdWithFilters should return the correct list of events`() =
        runBlocking {
            mockkStatic(LocalDateTime::class) {
                every { LocalDateTime.now().minusMonths(3) } returns limitMonths
                mockkObject(PersonHealthEventLogic)
                val dueDateFilter = LocalDateTime.parse("2019-06-15T10:00:00")
                val filter = "{\"due_date\": {\"second\": \"${dueDateFilter}\"}}"
                val sortBy = "due_date"
                val sortOrder = "asc"
                val range = IntRange(1, 2)
                val expectedDueDateUntil = dueDateFilter.minLocalDateTimeWithSaoPauloTimeZone().plusDays(1)

                val mockEvents = mockedPersonHealthEventList
                val eventAggregator = PersonHealthEventAggregator(
                    events = mockEvents,
                    count = mockEvents.size
                )

                val dueDateLessPredicate =
                    Predicate.less(PersonHealthEventDataService.FieldOptions().dueDate, expectedDueDateUntil)

                val expectedPredicates =
                    Predicate.greaterEq(PersonHealthEventDataService.FieldOptions().dueDate, limitMonths) and
                            dueDateLessPredicate

                coEvery {
                    dataService.find(queryEq {
                        where { this.personId.eq(testPerson.id) and expectedPredicates }
                            .orderBy { PersonHealthEventDataService.OrderingOptions().dueDate }
                            .sortOrder { SortOrder.Ascending }
                            .offset { range.first }
                            .limit { range.count() }
                    })
                } returns mockEvents.success()

                coEvery {
                    dataService.count(queryEq {
                        where { this.personId.eq(testPerson.id) and expectedPredicates }
                    })
                } returns mockEvents.size.success()

                val result =
                    service.findByPersonIdWithFilters(
                        testPerson.id,
                        filter,
                        sortBy,
                        sortOrder,
                        range,
                    )

                assertThat(result).isSuccessWithData(eventAggregator)
                coVerifyOnce { dataService.find(any()) }
            }
        }

    @Test
    fun `#findNextChannelFollowUpFromDate should return success with one result`() = runBlocking {
        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.channelId.eq("xpto")
                        .and(dueDate.greater(LocalDateTime.of(2020, 12, 12, 12, 0)))
                        .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                }
                    .orderBy { dueDate }
                    .sortOrder { asc }
            })
        } returns mockedPersonHealthEventList.first().success()

        val result = service.findNextChannelFollowUpFromDate(
            LocalDateTime.of(2020, 12, 12, 12, 0),
            "xpto"
        )

        assertThat(result).isSuccessWithData(mockedPersonHealthEventList.first())
        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findNextChannelFollowUpFromDateByList should return empty map if phe without channel`() = runBlocking {
        val channelIds = listOf("xpto", "oxtp")

        coEvery {
            dataService.find(queryEq {
                where {
                    this.channelId.inList(channelIds)
                        .and(dueDate.greater(LocalDateTime.of(2020, 12, 12, 12, 0)))
                        .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                }
                    .orderBy { dueDate }
                    .sortOrder { asc }
            })
        } returns emptyList<PersonHealthEvent>().success()

        val result = service.findNextChannelFollowUpFromDateByList(
            LocalDateTime.of(2020, 12, 12, 12, 0),
            channelIds
        )

        assertThat(result).isSuccessWithData(emptyList())
        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#findNextChannelFollowUpFromDateByList should return success with 2 result`() = runBlocking {
        val healthEvent1 = mockedPersonHealthEventList[0].copy(
            referencedLinks = listOf(
                ReferencedLink(
                    id = "xpto",
                    model = "Channel"
                )
            ),
            channelId = "xpto"
        )
        val healthEvent2 = mockedPersonHealthEventList[1].copy(
            referencedLinks = listOf(
                ReferencedLink(
                    id = "oxtp",
                    model = "Channel"
                )
            ),
            channelId = "oxtp"
        )
        val healthEvent3 = mockedPersonHealthEventList[1].copy(
            referencedLinks = listOf(
                ReferencedLink(
                    id = "oxtp",
                    model = "Channel"
                )
            ),
            channelId = "oxtp",
            dueDate = LocalDate.now().atBeginningOfTheDay()
        )
        val expected = listOf(
            PersonHealthEventChannelFup(
                id = healthEvent1.id,
                title = healthEvent1.title,
                dueDate = healthEvent1.dueDate,
                channelId = healthEvent1.channelId!!
            ),
            PersonHealthEventChannelFup(
                id = healthEvent2.id,
                title = healthEvent2.title,
                dueDate = healthEvent2.dueDate,
                channelId = healthEvent2.channelId!!
            ),
            PersonHealthEventChannelFup(
                id = healthEvent3.id,
                title = healthEvent3.title,
                dueDate = healthEvent3.dueDate,
                channelId = healthEvent3.channelId!!
            )
        )

        coEvery {
            dataService.find(queryEq {
                where {
                    this.channelId.inList(listOf("xpto", "oxtp"))
                        .and(dueDate.greater(LocalDateTime.of(2020, 12, 12, 12, 0)))
                        .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                }
                    .orderBy { dueDate }
                    .sortOrder { asc }
            })
        } returns listOf(healthEvent1, healthEvent2, healthEvent3).success()

        val result = service.findNextChannelFollowUpFromDateByList(
            LocalDateTime.of(2020, 12, 12, 12, 0),
            listOf("xpto", "oxtp")
        )

        assertThat(result).isSuccessWithData(expected)
        coVerifyOnce { dataService.find(any()) }
    }

    @Test
    fun `#findByChannelIdAndFupIds should return PHE`() = runBlocking {
        val channelId = "xpto"
        val fupIds = listOf(RangeUUID.generate())
        val phe = TestModelFactory.buildPersonHealthEvent()

        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.channelId.eq(channelId)
                        .and(automaticFollowUpContent.inList(fupIds))
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
            })
        } returns phe.success()

        val result = service.findByChannelIdAndFupIds(channelId, fupIds)

        assertThat(result).isSuccessWithData(phe)
        coVerifyOnce { dataService.findOne(any()) }
    }


    @Test
    fun `#getSummaryByCategories - must return correct values`() = runBlocking {
        coEvery { dataService.count(queryEq { where { countPredicate } }) } returns 10.success()
        coEvery { dataService.count(queryEq { where { dueCountPredicate } }) } returns 2.success()
        coEvery { dataService.count(queryEq { where { automaticFollowUpCountPredicate } }) } returns 1.success()

        val result = service.getSummaryByCategories(staffIdToSearch, dueDateToSearch, categoriesToSearch)

        assertThat(result).isSuccessWithData(HealthEventSummary(10, 2, 1))

        coVerify(exactly = 3) { dataService.count(any()) }
    }

    @Test
    fun `#getSummaryByCategories - when a call failures`() = runBlocking {
        coEvery { dataService.count(any()) } returns IllegalAccessException("").failure()

        val result = service.getSummaryByCategories(staffIdToSearch, dueDateToSearch, categoriesToSearch)

        assertThat(result).isFailureOfType(IllegalAccessException::class)

        coVerify(exactly = 3) { dataService.count(any()) }
    }

    @Test
    fun `#getSummaryByPool - must return correct values`() = runBlocking {
        val date = LocalDateTime.now()

        val countPredicate = PredicateLogic.buildPoolSummaryCountPredicate(listOf(additionalTeamIdToSearch), date)
        val dueCountPredicate = PredicateLogic.buildPoolSummaryDueCountPredicate(listOf(additionalTeamIdToSearch), date)

        coEvery { dataService.count(queryEq { where { countPredicate } }) } returns 10.success()
        coEvery { dataService.count(queryEq { where { dueCountPredicate } }) } returns 2.success()

        val result = service.getSummaryByPool(date, listOf(additionalTeamIdToSearch))
        assertThat(result).isSuccessWithData(HealthEventSummary(10, 2, 0))

        coVerify(exactly = 2) { dataService.count(any()) }
    }

    @Test
    fun `#getNextThreeTasksByPersonId - when a error on search`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                        .limit { 3 }
                        .orderBy { dueDate }
                })
            } returns IllegalAccessException("").failure()

            val result = service.getNextThreeTasksByPersonId(testPerson.id)

            assertThat(result).isFailureOfType(IllegalAccessException::class)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#getNextThreeTasksByPersonId - when a there's no next tasks`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                        .limit { 3 }
                        .orderBy { dueDate }
                })
            } returns emptyList<PersonHealthEvent>().success()

            val result = service.getNextThreeTasksByPersonId(testPerson.id)

            assertThat(result).isSuccessWithData(emptyList())
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#getNextThreeTasksByPersonId - when a there's tasks`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                        .limit { 3 }
                        .orderBy { dueDate }
                })
            } returns mockedPersonHealthEventList.success()

            val result = service.getNextThreeTasksByPersonId(testPerson.id)

            assertThat(result).isSuccessWithData(mockedPersonHealthEventList)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#countNextTasksByPersonId - when a error on search`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.count(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                })
            } returns IllegalAccessException("").failure()

            val result = service.countNextTasksByPersonId(testPerson.id)

            assertThat(result).isFailureOfType(IllegalAccessException::class)
            coVerifyOnce { dataService.count(any()) }
        }
    }

    @Test
    fun `#countNextTasksByPersonId - when a there's no next tasks`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.count(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                })
            } returns 0.success()

            val result = service.countNextTasksByPersonId(testPerson.id)

            assertThat(result).isSuccessWithData(0)
            coVerifyOnce { dataService.count(any()) }
        }
    }

    @Test
    fun `#countNextTasksByPersonId - when a there's tasks`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.count(queryEq {
                    where {
                        personId.eq(testPerson.id)
                            .and(dueDate.greater(date))
                            .and(status.inList(listOf(NOT_STARTED, IN_PROGRESS)))
                    }
                })
            } returns 3.success()

            val result = service.countNextTasksByPersonId(testPerson.id)

            assertThat(result).isSuccessWithData(3)
            coVerifyOnce { dataService.count(any()) }
        }
    }

    @Test
    fun `#findSuggestedResponsibleForStaff returns 3 used staffs`() = runBlocking {
        val staff2 = TestModelFactory.buildStaff()
        val staff3 = TestModelFactory.buildStaff()
        val staff4 = TestModelFactory.buildStaff()
        val healthEventList = listOf(
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff2.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff2.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff3.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff3.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff4.id, createdByStaffId = staff.id),
        )
        val expected = listOf(staff.id, staff2.id, staff3.id)
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns healthEventList.success()

            val result = service.findSuggestedResponsibleForStaff(staff.id)

            assertThat(result).isSuccessWithData(expected)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedResponsibleForStaff returns 2 used staffs if does not have 3`() = runBlocking {
        val staff2 = TestModelFactory.buildStaff()
        val healthEventList = listOf(
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff2.id, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(staffId = staff2.id, createdByStaffId = staff.id)
        )
        val expected = listOf(staff.id, staff2.id)
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns healthEventList.success()

            val result = service.findSuggestedResponsibleForStaff(staff.id)

            assertThat(result).isSuccessWithData(expected)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedResponsibleForStaff returns empty list if nothing was found`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns emptyList<PersonHealthEvent>().success()

            val result = service.findSuggestedResponsibleForStaff(staff.id)

            assertThat(result).isSuccessWithData(emptyList())
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedResponsibleForStaff returns error if fails to find`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns NotFoundException("resource_not_found").failure()

            val result = service.findSuggestedResponsibleForStaff(staff.id)

            assertThat(result).isFailure()
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedCategoriesForStaff returns 3 used categories`() = runBlocking {
        val healthEventList = listOf(
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(category = SUMMARY_EMERGENCY, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(category = SUMMARY_EMERGENCY, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(category = INTERNAL_TASK, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(category = INTERNAL_TASK, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(category = APPOINTMENT_COMMUNITY, createdByStaffId = staff.id),
        )
        val expected = listOf(AA_IMMERSION_HEALTH_FOLLOW_UP, SUMMARY_EMERGENCY, INTERNAL_TASK)
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns healthEventList.success()

            val result = service.findSuggestedCategoriesForStaff(staff.id)

            assertThat(result).isSuccessWithData(expected)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedCategoriesForStaff returns 2 used categories if cannot find 3`() = runBlocking {
        val healthEventList = listOf(
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(
                category = AA_IMMERSION_HEALTH_FOLLOW_UP,
                createdByStaffId = staff.id
            ),
            TestModelFactory.buildPersonHealthEvent(category = SUMMARY_EMERGENCY, createdByStaffId = staff.id),
            TestModelFactory.buildPersonHealthEvent(category = SUMMARY_EMERGENCY, createdByStaffId = staff.id)
        )
        val expected = listOf(AA_IMMERSION_HEALTH_FOLLOW_UP, SUMMARY_EMERGENCY)
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns healthEventList.success()

            val result = service.findSuggestedCategoriesForStaff(staff.id)

            assertThat(result).isSuccessWithData(expected)
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedCategoriesForStaff returns empty list if nothing was found`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns emptyList<PersonHealthEvent>().success()

            val result = service.findSuggestedCategoriesForStaff(staff.id)

            assertThat(result).isSuccessWithData(emptyList())
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findSuggestedCategoriesForStaff returns error if fails to find`() = runBlocking {
        mockkStatic(LocalDateTime::class) {
            val date = LocalDateTime.of(2020, 2, 17, 17, 30, 0)
            every { LocalDateTime.now() } returns date

            coEvery {
                dataService.find(queryEq {
                    where {
                        this.eventDate.greater(date.minusDays(10))
                            .and(createdByStaffId.eq(staff.id))
                    }.limit { 10 }
                })
            } returns NotFoundException("resource_not_found").failure()

            val result = service.findSuggestedCategoriesForStaff(staff.id)

            assertThat(result).isFailure()
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#getLastEvent - should return last event`() = mockLocalDateTime(dateNow) {
        val categories = listOf(PersonHealthEventCategory.APPOINTMENT_FOLLOW_UP)
        val status = listOf(PersonHealthEventStatus.FINISHED, PersonHealthEventStatus.FINISHED_BY_INACTIVITY)

        coEvery {
            dataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(testPersonId) and
                                this.category.inList(categories) and
                                this.status.inList(status) and
                                this.dueDate.less(dateNow) and
                                this.dueDate.greater(dateNow.minusMonths(3).atBeginningOfTheDay())
                    }.orderBy { this.dueDate }.sortOrder { desc }
                }
            )
        } returns model.success()

        val result = service.getLastEvent(testPersonId, categories, status)
        assertThat(result).isSuccessWithData(model)

        coVerifyOnce { dataService.findOne(any()) }
    }

}
