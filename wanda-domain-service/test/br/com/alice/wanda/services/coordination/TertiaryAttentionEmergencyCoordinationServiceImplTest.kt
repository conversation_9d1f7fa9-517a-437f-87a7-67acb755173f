package br.com.alice.wanda.services.coordination

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentCoordination.CoordinatedType.COORDINATED
import br.com.alice.data.layer.models.AppointmentCoordination.CoordinatedType.UNCOORDINATED
import br.com.alice.data.layer.models.AppointmentCoordination.Type.TERTIARY_INTENTION_EMERGENCY
import br.com.alice.data.layer.models.COORDINATED_TYPES
import br.com.alice.data.layer.models.TertiaryIntentionCoordinated
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.services.AppointmentCoordinationDataService
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEvent
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEventPayload
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TertiaryAttentionEmergencyCoordinationServiceImplTest {
    private val appointmentCoordinationDataService: AppointmentCoordinationDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = TertiaryAttentionEmergencyCoordinationServiceImpl(
        appointmentCoordinationDataService,
        kafkaProducerService,
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
        confirmVerified(
            appointmentCoordinationDataService,
            kafkaProducerService,
        )
    }

    private val person = TestModelFactory.buildPerson()

    private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination(
        personId = person.id
    )
    private val emergencyIntention = TestModelFactory.buildTertiaryIntentionEmergency(
        personId = person.id
    )

    @Test
    fun `#create - don't do anything if coordination does not change`() = runBlocking {
        val appointmentCoordination = appointmentCoordination.copy(
            coordinated = COORDINATED
        )

        coEvery {
            appointmentCoordinationDataService.findOneOrNull(
                queryEq {
                    where {
                        appointmentId.eq(emergencyIntention.id) and
                                appointmentType.eq(TERTIARY_INTENTION_EMERGENCY.name)
                    }
                }
            )
        } returns appointmentCoordination

        val result = service.checkTertiaryIntention(emergencyIntention)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
        coVerifyNone { appointmentCoordinationDataService.update(any()) }
        coVerifyNone { appointmentCoordinationDataService.add(any()) }
    }

    @Test
    fun `#create - should update when coordination change`() = runBlocking {
        val appointmentCoordination = appointmentCoordination.copy(
            coordinated = UNCOORDINATED
        )

        coEvery {
            appointmentCoordinationDataService.findOneOrNull(
                queryEq {
                    where {
                        appointmentId.eq(emergencyIntention.id) and
                                appointmentType.eq(TERTIARY_INTENTION_EMERGENCY.name)
                    }
                }
            )
        } returns appointmentCoordination

        coEvery {
            appointmentCoordinationDataService.update(
                match {
                    it.coordinated == COORDINATED
                }
            )
        } returns appointmentCoordination.success()

        val result = service.checkTertiaryIntention(emergencyIntention)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
        coVerifyOnce { appointmentCoordinationDataService.update(any()) }
        coVerifyNone { appointmentCoordinationDataService.add(any()) }
    }

    @ParameterizedTest
    @MethodSource("coordinatedTertiaryTouchPoints")
    fun `#create - should insert when tertiary intention does not exists`(emergencyIntention: TertiaryIntentionTouchPoint) =
        runBlocking {
            val emergencyAppointmentCoordination = appointmentCoordination.copy(
                appointmentId = emergencyIntention.id,
                appointmentType = TERTIARY_INTENTION_EMERGENCY,
                coordinated = COORDINATED,
                healthPlanTaskId = null,
                appointmentScheduleId = null
            )

            coEvery {
                appointmentCoordinationDataService.findOneOrNull(
                    queryEq {
                        where {
                            appointmentId.eq(emergencyIntention.id) and
                                    appointmentType.eq(TERTIARY_INTENTION_EMERGENCY.name)
                        }
                    }
                )
            } returns null

            coEvery {
                appointmentCoordinationDataService.add(
                    match {
                        it == emergencyAppointmentCoordination.copy(id = it.id, createdAt = it.createdAt)
                    }
                )
            } returns emergencyAppointmentCoordination.success()

            coEvery {
                kafkaProducerService.produce(
                    match { event: AppointmentCoordinationCreatedEvent ->
                        event.payload == AppointmentCoordinationCreatedEventPayload(emergencyAppointmentCoordination)
                    }
                )
            } returns mockk()

            val result = service.checkTertiaryIntention(emergencyIntention)

            assertThat(result).isSuccessWithData(emergencyAppointmentCoordination)

            coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
            coVerifyNone { appointmentCoordinationDataService.update(any()) }
            coVerifyOnce { appointmentCoordinationDataService.add(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    private fun coordinatedTertiaryTouchPoints() = COORDINATED_TYPES.map { coordinated ->
        TestModelFactory.buildTertiaryIntentionEmergency(
            coordinated = coordinated,
            personId = person.id
        )
    }

    @Test
    fun `#create - should insert when tertiary intention does not exists as UNCOORDINATED`() =
        runBlocking {
            val emergencyAppointmentCoordination = appointmentCoordination.copy(
                appointmentId = emergencyIntention.id,
                appointmentType = TERTIARY_INTENTION_EMERGENCY,
                coordinated = UNCOORDINATED,
                healthPlanTaskId = null,
                appointmentScheduleId = null
            )

            coEvery {
                appointmentCoordinationDataService.findOneOrNull(
                    queryEq {
                        where {
                            appointmentId.eq(emergencyIntention.id) and
                                    appointmentType.eq(TERTIARY_INTENTION_EMERGENCY.name)
                        }
                    }
                )
            } returns null

            coEvery {
                appointmentCoordinationDataService.add(
                    match {
                        it == emergencyAppointmentCoordination.copy(id = it.id, createdAt = it.createdAt)
                    }
                )
            } returns emergencyAppointmentCoordination.success()

            coEvery {
                kafkaProducerService.produce(
                    match { event: AppointmentCoordinationCreatedEvent ->
                        event.payload == AppointmentCoordinationCreatedEventPayload(emergencyAppointmentCoordination)
                    }
                )
            } returns mockk()

            val result = service.checkTertiaryIntention(
                emergencyIntention.copy(
                    coordinated = TertiaryIntentionCoordinated.TIR_NOT_AND_NON_RELEVANT
                )
            )

            assertThat(result).isSuccessWithData(emergencyAppointmentCoordination)

            coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
            coVerifyNone { appointmentCoordinationDataService.update(any()) }
            coVerifyOnce { appointmentCoordinationDataService.add(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

}
