# Alice's Wanda Domain
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Awanda-domain-service&metric=coverage&token=d76ae29e1f4811ea444bb9085710bbfd3cbeb397)](https://sonarcloud.io/summary/new_code?id=mono%3Awanda-domain-service)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Awanda-domain-service&metric=code_smells&token=d76ae29e1f4811ea444bb9085710bbfd3cbeb397)](https://sonarcloud.io/summary/new_code?id=mono%3Awanda-domain-service)
[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Awanda-domain-service&metric=duplicated_lines_density&token=d76ae29e1f4811ea444bb9085710bbfd3cbeb397)](https://sonarcloud.io/summary/new_code?id=mono%3Awanda-domain-service)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Awanda-domain-service&metric=bugs&token=d76ae29e1f4811ea444bb9085710bbfd3cbeb397)](https://sonarcloud.io/summary/new_code?id=mono%3Awanda-domain-service)

This is our Wanda domain, responsible for handling all Person Health Events, its practical use appears on ``Cockpit, Wanda Tasks on Pillars and Member's timeline``. 

### Responsible Team
Gestão de Saúde, find us on ``#eng-gestao-de-saude`` on Slack ;)

### Local development

Requirements
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``db_reset db_seed db_run`` - to start our database and data layer
* ``run service=wanda-domain-service`` - to run it
