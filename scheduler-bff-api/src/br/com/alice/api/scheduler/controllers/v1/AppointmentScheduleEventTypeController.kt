package br.com.alice.api.scheduler.controllers.v1

import br.com.alice.api.scheduler.controllers.StaffController
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeDateExceptionResponseItem
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeResponse
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeStaffAssociationsRequest
import br.com.alice.api.scheduler.controllers.model.AppointmentScheduleEventTypeStaffAssociationsResponse
import br.com.alice.api.scheduler.controllers.model.CategoryResponse
import br.com.alice.api.scheduler.controllers.model.MedicalSpecialtyResponse
import br.com.alice.api.scheduler.controllers.model.MedicalSpecialtyWithType
import br.com.alice.api.scheduler.controllers.model.StaffAppointmentScheduleEventTypeAssociationsRequest
import br.com.alice.api.scheduler.converters.AppointmentScheduleEventTypeRequestConverter
import br.com.alice.api.scheduler.converters.AppointmentScheduleEventTypeResponseConverter
import br.com.alice.api.scheduler.converters.AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter
import br.com.alice.api.scheduler.converters.SimpleStaffResponseConverter
import br.com.alice.common.Response
import br.com.alice.common.convertTo
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.beginningOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.endOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeDateException
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation.ON_SITE
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation.REMOTE
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.Weekday
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeDateExceptionService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.exceptions.ExistingGenericEventTypeWithSameSubSpecialtyException
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffAvailabilityResponse
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.gson.Gson
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.Span
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class AppointmentScheduleEventTypeController(
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val staffAvailabilityService: StaffAvailabilityService,
    private val appointmentScheduleEventTypeDateExceptionService: AppointmentScheduleEventTypeDateExceptionService,
    private val eventTypeProviderUnitService: EventTypeProviderUnitService,
    private val staffService: StaffService
) : StaffController(staffService) {

    suspend fun createWithProviderUnits(
        request: AppointmentScheduleEventTypeRequest
    ): Response = span("createWithProviderUnits") { span ->
        span.setEventType(request)
        val validationResult = validateRequest(request)
        if (validationResult != EventTypeRequestValidation.OK) {
            return@span Response(status = HttpStatusCode.BadRequest, message = validationResult.toString())
        }

        val eventTypeProviderUnits = buildEventTypeProviderUnits(request)
        AppointmentScheduleEventTypeRequestConverter.convert(request).let {
            appointmentScheduleEventTypeService.createWithProviderUnits(
                appointmentScheduleEventType = it.copy(lastUpdatedBy = currentStaffId()),
                eventTypeProviderUnits = eventTypeProviderUnits
            )
        }
            .recordResult(span)
            .fold(
                { AppointmentScheduleEventTypeResponseConverter.convert(it).toResponse() },
                { mapErrorToResponse(it) }
            )
    }

    suspend fun updateWithProviderUnits(
        appointmentScheduleEventTypeId: UUID,
        request: AppointmentScheduleEventTypeRequest
    ): Response = span("updateWithProviderUnits") { span ->
        span.setEventType(request, appointmentScheduleEventTypeId)
        val validationResult = validateRequest(request)
        if (validationResult != EventTypeRequestValidation.OK) {
            return@span Response(status = HttpStatusCode.BadRequest, message = validationResult.toString())
        }

        val eventTypeProviderUnits = buildEventTypeProviderUnits(request)
        AppointmentScheduleEventTypeRequestConverter.convert(request).let {
            appointmentScheduleEventTypeService.updateWithProviderUnits(
                appointmentScheduleEventType = it.copy(
                    id = appointmentScheduleEventTypeId,
                    lastUpdatedBy = currentStaffId(),
                ),
                eventTypeProviderUnits = eventTypeProviderUnits
            )
        }
            .recordResult(span)
            .fold(
                { AppointmentScheduleEventTypeResponseConverter.convert(it).toResponse() },
                { mapErrorToResponse(it) }
            )
    }

    suspend fun list(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val query = parseQuery(queryParams)
        val type = queryParams["type"]?.let { AppointmentScheduleType.valueOf(it) }
        val status = parseStatusList(queryParams)

        return appointmentScheduleEventTypeService.query(
            searchQuery = query,
            range = range,
            appointmentScheduleType = type,
            status = status,
        )
            .map { eventTypes ->
                val specialtiesIds = eventTypes.pmap {
                    val specialtyId = it.specialtyId
                    val subSpecialtyIds = (it.subSpecialtyIds ?: emptyList())
                    listOfNotNull(subSpecialtyIds.plus(specialtyId))
                }
                    .flatten()
                    .flatten()
                    .filterNotNull()

                val specialties = if (specialtiesIds.isNotEmpty()) getSpecialties(specialtiesIds) else listOf()
                eventTypes.pmap {
                    val filteredSpecialties = specialties
                        .filter { specialty ->
                            specialty.id == it.specialtyId
                                    || it.subSpecialtyIds?.contains(specialty.id) == true
                        }
                    AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
                        source = it,
                        specialties = filteredSpecialties
                    )
                }
            }
            .flatMapPair {
                appointmentScheduleEventTypeService.count(
                    searchQuery = query,
                    appointmentScheduleType = type,
                    status = status,
                )
            }
            .map {
                AppointmentScheduleEventTypeResponse(total = it.first, items = it.second)
            }
            .foldResponse()
    }

    suspend fun getWithSpecialties(id: UUID): Response {
        val appointmentScheduleEventType = appointmentScheduleEventTypeService.getWithProviderUnits(id).get()
        val lastUpdatedByStaff = appointmentScheduleEventType.lastUpdatedBy?.let {
            staffService.get(it).get()
        }

        val subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds ?: emptyList()
        val specialtyId = appointmentScheduleEventType.specialtyId
        val specialtyIds = subSpecialtyIds.let { specialtyIds ->
            specialtyId?.let { specialtyId -> specialtyIds.plus(specialtyId) } ?: specialtyIds
        }
        val specialties = getSpecialties(specialtyIds)

        return AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
            source = appointmentScheduleEventType,
            specialties = specialties,
            lastUpdatedByStaff = lastUpdatedByStaff,
        ).success().foldResponse()
    }

    suspend fun getMedicalSpecialties(queryParams: Parameters) =
        parseQuery(queryParams)?.let { query ->
            medicalSpecialtyService.getByName(query, MedicalSpecialtyType.SPECIALTY)
                .map { specialties -> specialties.filter { it.active } }
                .map { medicalSpecialties -> medicalSpecialties.map { it.convertTo(MedicalSpecialtyResponse::class) } }
                .foldResponse()
        } ?: run {
            Response(HttpStatusCode.OK, emptyList<MedicalSpecialtyResponse>())
        }

    suspend fun getMedicalSubSpecialties(parentSpecialtyId: UUID) =
        medicalSpecialtyService.getByParentId(parentSpecialtyId)
            .map { specialties -> specialties.filter { it.active } }
            .map { medicalSpecialties -> medicalSpecialties.map { it.convertTo(MedicalSpecialtyResponse::class) } }
            .foldResponse()

    fun getAppointmentScheduleCategories() =
        AppointmentScheduleType.values().sortedBy { it.description }.map {
            CategoryResponse(
                description = it.description,
                value = it.name
            )
        }.let {
            Response(HttpStatusCode.OK, it)
        }

    suspend fun getStaffsAssociatedToEventType(appointmentScheduleEventTypeId: UUID) =
        appointmentScheduleOptionService.getStaffsAssociatedToEvents(appointmentScheduleEventTypeId, null)
            .map { staffs ->
                staffs.map { SimpleStaffResponseConverter.convert(it) }
            }
            .foldResponse()

    suspend fun getEventTypesAssociatedToStaff(staffId: UUID) =
        appointmentScheduleOptionService.getEventsWithProviderUnits(staffId)
            .map { appointmentScheduleEventTypes ->
                appointmentScheduleEventTypes.map {
                    AppointmentScheduleEventTypeWithProviderUnitsAndSpecialtiesResponseConverter.convert(
                        it
                    )
                }
            }
            .foldResponse()

    suspend fun associateStaffs(
        appointmentScheduleEventTypeId: UUID,
        associateStaffsRequest: AppointmentScheduleEventTypeStaffAssociationsRequest
    ): Response {
        logger.info(
            "-OpsLog- AppointmentScheduleEventTypeController::associateStaffs",
            "requester" to currentStaffId(),
            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
            "staff_ids" to associateStaffsRequest.staffIds.toString()
        )
        return associateStaffsRequest.staffIds
            .pmap { staffId ->
                appointmentScheduleOptionService.associateStaff(
                    staffId,
                    appointmentScheduleEventTypeId,
                )
                    .then {
                        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
                            .flatMap { currentAppointmentScheduleEventType ->
                                appointmentScheduleEventTypeService.update(
                                    currentAppointmentScheduleEventType.copy(lastUpdatedBy = currentStaffId())
                                )
                            }
                    }
            }
            .let { results ->
                buildAssociationResponse(results)
            }
    }

    suspend fun disassociateStaffs(
        appointmentScheduleEventTypeId: UUID,
        associateStaffsRequest: AppointmentScheduleEventTypeStaffAssociationsRequest
    ): Response {
        logger.info(
            "-OpsLog- AppointmentScheduleEventTypeController::disassociateStaffs",
            "requester" to currentStaffId(),
            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
            "staff_ids" to associateStaffsRequest.staffIds.toString()
        )
        return associateStaffsRequest.staffIds.pmap { staffId ->
            appointmentScheduleOptionService.disassociateStaff(
                staffId,
                appointmentScheduleEventTypeId,
            ).then {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId).flatMap {
                    appointmentScheduleEventTypeService.update(it.copy(lastUpdatedBy = currentStaffId()))
                }
            }
        }.let { results ->
            buildAssociationResponse(results)
        }
    }

    suspend fun associateStaffToEventTypes(
        staffId: UUID,
        request: StaffAppointmentScheduleEventTypeAssociationsRequest
    ): Response = span("associateStaffToEventTypes") { span ->
        val requesterStaffId = currentStaffId()
        span.setAttribute("requester_staff_id", requesterStaffId)
        span.setAttribute("staff_id", staffId)
        val appointmentScheduleEventTypeIds = request.appointmentScheduleEventTypeIds
        span.setAttribute("event_types", appointmentScheduleEventTypeIds.toString())

        appointmentScheduleEventTypeIds
            .pmap { appointmentScheduleEventTypeId ->
                appointmentScheduleOptionService.associateStaff(
                    staffId,
                    appointmentScheduleEventTypeId
                )
                    .then { update(appointmentScheduleEventTypeId, requesterStaffId) }
            }
            .let { results -> buildAssociationResponse(results) }
    }

    suspend fun disassociateStaffToEventTypes(
        staffId: UUID,
        request: StaffAppointmentScheduleEventTypeAssociationsRequest
    ): Response = span("disassociateStaffToEventTypes") { span ->
        val requesterStaffId = currentStaffId()
        span.setAttribute("requester_staff_id", requesterStaffId)
        span.setAttribute("staff_id", staffId)
        val appointmentScheduleEventTypeIds = request.appointmentScheduleEventTypeIds
        span.setAttribute("event_types", appointmentScheduleEventTypeIds.toString())

        appointmentScheduleEventTypeIds
            .pmap { appointmentScheduleEventTypeId ->
                appointmentScheduleOptionService.disassociateStaff(
                    staffId,
                    appointmentScheduleEventTypeId
                )
                    .then { update(appointmentScheduleEventTypeId, requesterStaffId) }
            }
            .let { results -> buildAssociationResponse(results) }
    }

    suspend fun getAvailabilityForPeriod(
        appointmentScheduleEventTypeId: UUID, queryParams: Parameters
    ): Response = span("getAvailabilityForPeriod") { span ->
        val localDateTimeNow = LocalDateTime.now()
        val fromDate = queryParams["fromDate"]?.toLocalDate()
            ?: beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val toDate = queryParams["toDate"]?.toLocalDate()
            ?: endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val providerUnitId = queryParams["providerUnitId"]
        val staffId = queryParams["staffId"]
        val byPassSchedulingConstraints = queryParams["byPassSchedulingConstraints"]?.toBoolean() ?: false
        span.setScheduleInfo(
            fromDate,
            toDate,
            appointmentScheduleEventTypeId,
            providerUnitId,
            staffId,
            byPassSchedulingConstraints
        )

        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            .flatMap { appointmentScheduleEventType ->
                if (appointmentScheduleEventType.status == Status.INACTIVE)
                    return@flatMap emptyList<SlotForSpecificDuration>().success()

                val providerUnitIds = buildProviderUnitIds(providerUnitId, appointmentScheduleEventTypeId)

                getStaffAvailability(
                    staffId,
                    fromDate,
                    toDate,
                    appointmentScheduleEventTypeId,
                    appointmentScheduleEventType,
                    providerUnitIds,
                    byPassSchedulingConstraints
                )
            }
            .map { StaffAvailabilityResponse(it) }
            .foldResponse()
    }

    suspend fun getDateExceptions(
        appointmentScheduleEventTypeId: UUID,
    ) = appointmentScheduleEventTypeDateExceptionService.getForEventType(
        appointmentScheduleEventTypeId
    ).map { dateExceptions ->
        val items = dateExceptions.map { it.convertTo(AppointmentScheduleEventTypeDateExceptionResponseItem::class) }
        AppointmentScheduleEventTypeDateExceptionResponse(items)
    }.foldResponse()

    suspend fun createDateException(
        appointmentScheduleEventTypeId: UUID,
        request: AppointmentScheduleEventTypeDateExceptionRequest
    ): Response {
        logger.info(
            "-OpsLog- AppointmentScheduleEventTypeController::createDateException",
            "requester" to currentStaffId(),
            "exception_date" to request.exceptionDate,
            "event_type_id" to appointmentScheduleEventTypeId,
        )
        return appointmentScheduleEventTypeDateExceptionService.add(
            AppointmentScheduleEventTypeDateException(
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                exceptionDate = request.exceptionDate,
            )
        ).foldResponse()
    }

    suspend fun deleteDateException(
        appointmentScheduleEventTypeDateExceptionId: UUID,
    ): Response {
        logger.info(
            "-OpsLog- AppointmentScheduleEventTypeController::deleteDateException",
            "requester" to currentStaffId(),
            "date_exception_id" to appointmentScheduleEventTypeDateExceptionId
        )
        return appointmentScheduleEventTypeDateExceptionService.delete(
            appointmentScheduleEventTypeDateExceptionId,
        ).foldResponse()
    }

    enum class EventTypeRequestValidation {
        OK,
        BLANK_TITLE,
        INVALID_DURATION,
        INVALID_MINIMUM_TIME_TO_SCHEDULE_BEFORE_APPOINTMENT_TIME,
        INVALID_MINIMUM_NUMBER_OF_DAYS_FROM_NOW_TO_ALLOW_SCHEDULING,
        INVALID_SPECIALTIES_BY_APPOINTMENT_SCHEDULE_TYPE,
        INVALID_EMPTY_LOCATIONS,
        INVALID_AVAILABLE_WEEK_DAYS,
        INVALID_NUMBER_OF_DAYS_FROM_NOW_TO_ALLOW_SCHEDULING
    }

    private fun mapErrorToResponse(throwable: Throwable): Response {
        val message = throwable.message ?: ""
        return when (throwable) {
            is ExistingGenericEventTypeWithSameSubSpecialtyException -> Response(HttpStatusCode.BadRequest, message)
            is NotFoundException -> Response(HttpStatusCode.NotFound, message)
            else -> Response(HttpStatusCode.InternalServerError, message)
        }
    }

    private fun buildAssociationResponse(results: List<Result<AppointmentScheduleOption, Throwable>>): Response {
        val successResponses = results.filter { it.isSuccess() }.map { it.get() }
        val failureResponses = results.filter { it.isFailure() }.map { it.get() }
        val response = AppointmentScheduleEventTypeStaffAssociationsResponse(
            associatedStaffIds = successResponses.mapNotNull { it.staffId },
            notAssociatedStaffIds = failureResponses.mapNotNull { it.staffId },
        )
        return response.toResponse()
    }

    private suspend fun getSpecialties(specialtyIds: List<UUID>): List<MedicalSpecialtyWithType> =
        if (specialtyIds.isNotEmpty()) {
            medicalSpecialtyService.getByIds(specialtyIds).map { medicalSpecialties ->
                medicalSpecialties.map { it.convertTo(MedicalSpecialtyWithType::class) }
            }.get()
        } else listOf()

    private fun validateRequest(request: AppointmentScheduleEventTypeRequest): EventTypeRequestValidation {
        val titleIsBlank = request.title.trim().isBlank()
        val invalidDuration = isInvalidDuration(request)
        val invalidMinimumTimeToScheduleBeforeAppointmentTime =
            isInvalidMinimumTimeToScheduleBeforeAppointmentTime(request)
        val invalidMinimumNumberOfDaysFromNowToAllowScheduling =
            isInvalidMinimumNumberOfDaysFromNowToAllowScheduling(request)

        val subSpecialtyIds = listOfNotNull(request.subSpecialtyIds).flatten()
        val subSpecialtyId = request.subSpecialtyId
        val requestSubSpecialties = subSpecialtyId?.let { subSpecialtyIds.plus(it) } ?: subSpecialtyIds
        val invalidSpecialties = validateSpecialties(request.category, request.specialtyId, requestSubSpecialties)
        val invalidLocations = request.locations?.isEmpty() == true
        val isInvalidAvailableWeekDays = isInvalidAvailableWeekDays(request)
        val isInvalidNumberOfDaysFromNowToAllowScheduling = isInvalidNumberOfDaysFromNowToAllowScheduling(request)

        return when {
            titleIsBlank ->
                EventTypeRequestValidation.BLANK_TITLE

            invalidDuration ->
                EventTypeRequestValidation.INVALID_DURATION

            invalidMinimumTimeToScheduleBeforeAppointmentTime ->
                EventTypeRequestValidation.INVALID_MINIMUM_TIME_TO_SCHEDULE_BEFORE_APPOINTMENT_TIME

            invalidMinimumNumberOfDaysFromNowToAllowScheduling ->
                EventTypeRequestValidation.INVALID_MINIMUM_NUMBER_OF_DAYS_FROM_NOW_TO_ALLOW_SCHEDULING

            invalidSpecialties ->
                EventTypeRequestValidation.INVALID_SPECIALTIES_BY_APPOINTMENT_SCHEDULE_TYPE

            invalidLocations ->
                EventTypeRequestValidation.INVALID_EMPTY_LOCATIONS

            isInvalidAvailableWeekDays ->
                EventTypeRequestValidation.INVALID_AVAILABLE_WEEK_DAYS

            isInvalidNumberOfDaysFromNowToAllowScheduling ->
                EventTypeRequestValidation.INVALID_NUMBER_OF_DAYS_FROM_NOW_TO_ALLOW_SCHEDULING

            else -> EventTypeRequestValidation.OK
        }
    }

    private fun isInvalidDuration(request: AppointmentScheduleEventTypeRequest): Boolean {
        val minimumAppointmentScheduleDurationInMinutesAllowed = FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "minimum_appointment_schedule_duration_in_minutes_allowed",
            defaultValue = 15
        )
        return request.locations?.any { it.duration < minimumAppointmentScheduleDurationInMinutesAllowed }
            ?: request.duration?.let { it < minimumAppointmentScheduleDurationInMinutesAllowed }
            ?: true
    }

    private fun isInvalidMinimumTimeToScheduleBeforeAppointmentTime(request: AppointmentScheduleEventTypeRequest) =
        request.locations?.any { it.minimumTimeToScheduleBeforeAppointmentTime < 1 }
            ?: request.minimumTimeToScheduleBeforeAppointmentTime?.let { it < 1 }
            ?: true

    private fun isInvalidMinimumNumberOfDaysFromNowToAllowScheduling(request: AppointmentScheduleEventTypeRequest): Boolean {
        val minValueOfNumberOfDaysFromNowToAllowScheduling = FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "min_value_of_number_of_days_from_now_to_allow_scheduling_allowed",
            defaultValue = 1
        )
        return request.locations?.any { it.numberOfDaysFromNowToAllowScheduling < minValueOfNumberOfDaysFromNowToAllowScheduling }
            ?: request.numberOfDaysFromNowToAllowScheduling?.let { it < minValueOfNumberOfDaysFromNowToAllowScheduling }
            ?: true
    }

    private fun isInvalidAvailableWeekDays(request: AppointmentScheduleEventTypeRequest) =
        request.locations?.any { it.availableWeekDays.isEmpty() }
            ?: request.availableWeekDays?.isEmpty()
            ?: true

    private fun isInvalidNumberOfDaysFromNowToAllowScheduling(request: AppointmentScheduleEventTypeRequest) =
        request.locations?.any { it.numberOfDaysFromNowToAllowScheduling > 90 }
            ?: request.numberOfDaysFromNowToAllowScheduling?.let { it > 90 }
            ?: true

    private fun validateSpecialties(
        category: AppointmentScheduleType,
        specialtyId: UUID?,
        subSpecialtyIds: List<UUID>
    ): Boolean {
        val validCategoriesToAllowEmptySpecialties =
            AppointmentSchedule.testTypes().plus(AppointmentScheduleType.HEALTH_DECLARATION)
        val validCategoryToByPassSpecialtiesValidation = validCategoriesToAllowEmptySpecialties.contains(category)
        val validSpecialties = specialtyId != null && subSpecialtyIds.isNotEmpty()

        return when {
            validCategoryToByPassSpecialtiesValidation || validSpecialties -> false
            else -> true
        }
    }

    private fun parseStatusList(queryParams: Parameters): List<Status>? {
        val gson = Gson()
        return queryParams["status"]?.let<String, ArrayList<Status>?> { gson.fromJson(it) }
    }

    private fun buildEventTypeProviderUnits(
        request: AppointmentScheduleEventTypeRequest
    ): List<AppointmentScheduleEventTypeLocationAvailability> = when {
        request.locations != null -> {
            request.locations.map {
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = it.providerUnitId,
                    type = if (it.providerUnitId != null) ON_SITE else REMOTE,
                    duration = it.duration,
                    minimumTimeToScheduleBeforeAppointmentTime = it.minimumTimeToScheduleBeforeAppointmentTime,
                    numberOfDaysFromNowToAllowScheduling = it.numberOfDaysFromNowToAllowScheduling,
                    availableWeekDays = it.availableWeekDays,
                    availabilityStartTime = it.availabilityStartTime,
                    availabilityEndTime = it.availabilityEndTime
                )
            }
        }

        request.providerUnitIds != null -> {
            request.providerUnitIds.distinct().map {
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = it,
                    type = ON_SITE,
                    duration = request.duration ?: 30,
                    minimumTimeToScheduleBeforeAppointmentTime = request.minimumTimeToScheduleBeforeAppointmentTime
                        ?: 1,
                    numberOfDaysFromNowToAllowScheduling = request.numberOfDaysFromNowToAllowScheduling ?: 90,
                    availableWeekDays = request.availableWeekDays ?: Weekday.values().toList(),
                    availabilityStartTime = null,
                    availabilityEndTime = null
                )
            }
        }

        else -> {
            listOf(
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = null,
                    type = REMOTE,
                    duration = request.duration ?: 30,
                    minimumTimeToScheduleBeforeAppointmentTime = request.minimumTimeToScheduleBeforeAppointmentTime
                        ?: 1,
                    numberOfDaysFromNowToAllowScheduling = request.numberOfDaysFromNowToAllowScheduling ?: 90,
                    availableWeekDays = request.availableWeekDays ?: Weekday.values().toList(),
                    availabilityStartTime = null,
                    availabilityEndTime = null
                )
            )
        }
    }

    private suspend fun buildProviderUnitIds(providerUnitId: String?, appointmentScheduleEventTypeId: UUID) =
        when {
            providerUnitId != null -> {
                if (providerUnitId.contains("digital", ignoreCase = true)) null
                else listOfNotNull(providerUnitId.toSafeUUID())
            }

            else -> {
                val eventTypeProviderUnits =
                    eventTypeProviderUnitService.getForEventType(appointmentScheduleEventTypeId).get()
                if (eventTypeProviderUnits.isNotEmpty()) eventTypeProviderUnits.mapNotNull { it.providerUnitId }
                else null
            }
        }.let { providerUnitIds -> providerUnitIds?.let { it.ifEmpty { null } } }

    private suspend fun update(
        appointmentScheduleEventTypeId: UUID,
        requesterStaffId: UUID?
    ) = appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
        .flatMap { appointmentScheduleEventTypeService.update(it.copy(lastUpdatedBy = requesterStaffId)) }

    private suspend fun getStaffAvailability(
        staffId: String?,
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType,
        providerUnitIds: List<UUID>?,
        byPassSchedulingConstraints: Boolean
    ) = if (staffId.isNullOrBlank()) {
        staffAvailabilityService.getForEventTypeAndPeriod(
            startDate = fromDate,
            endDate = toDate,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            appointmentScheduleEventType = appointmentScheduleEventType,
            providerUnitIds = providerUnitIds
        )
    } else {
        staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
            startDateTime = fromDate.atStartOfDay(),
            endDateTime = toDate.atEndOfTheDay(),
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            appointmentScheduleEventType = appointmentScheduleEventType,
            providerUnitId = providerUnitIds?.firstOrNull(),
            staffId = staffId.toSafeUUID(),
            byPassSchedulingConstraints = byPassSchedulingConstraints
        )
    }

    private suspend fun Span.setEventType(request: AppointmentScheduleEventTypeRequest, eventTypeId: UUID? = null) {
        setAttribute("requester_staff_id", currentStaffId())
        setAttribute("event_type_id", eventTypeId)
        setAttribute("request", request)
    }

    private fun Span.setScheduleInfo(
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        providerUnitId: String?,
        staffId: String?,
        byPassSchedulingConstraints: Boolean
    ) {
        setAttribute("from_date", fromDate.toString())
        setAttribute("to_date", toDate.toString())
        setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)
        setAttribute("provider_unit_id", providerUnitId.toString())
        setAttribute("staff_id", staffId.toString())
        setAttribute("by_pass_scheduling_constraints", byPassSchedulingConstraints)
    }

}
