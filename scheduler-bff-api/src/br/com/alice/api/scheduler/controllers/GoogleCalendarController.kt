package br.com.alice.api.scheduler.controllers

import br.com.alice.common.Response
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.ExternalCalendarEventService
import br.com.alice.schedule.client.ExternalCalendarRecurrentEventService
import br.com.alice.schedule.client.GoogleCalendarEventService
import br.com.alice.schedule.model.events.InternalAppointmentScheduleCreatedEvent
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import java.time.LocalDateTime

class GoogleCalendarController(
    private val googleCalendarEventService: GoogleCalendarEventService,
    private val externalCalendarRecurrentEventService: ExternalCalendarRecurrentEventService,
    private val kafkaProducerService: KafkaProducerService,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val externalCalendarEventService: ExternalCalendarEventService,
    staffService: StaffService
) : StaffController(staffService) {

    suspend fun syncUserGoogleCalendar(staffId: String, queryParams: Parameters): Response =
        span("syncUserGoogleCalendar") { span ->
            span.setAttribute("requester_staff_id", this.currentStaffId().toString())
            span.setAttribute("staff_id", staffId)
            val beginningOfCurrentDayDateTime = LocalDateTime.now().atBeginningOfTheDay()
            val startDate = queryParams["start_date"]?.let {
                try {
                    LocalDateTime.parse(it)
                } catch (ex: Exception) {
                    beginningOfCurrentDayDateTime
                }
            } ?: LocalDateTime.now().atBeginningOfTheDay()
            span.setAttribute("start_date", startDate.toString())

            googleCalendarEventService.getAndStoreUserGoogleCalendarEvents(staffId.toUUID(), startDate)
                .foldResponse()
        }

    suspend fun fetchSingleEventsFromRecurrence(recurrentEventId: String): Response =
        span("fetchSingleEventsFromRecurrence") { span ->
            span.setAttribute("requester_staff_id", this.currentStaffId().toString())
            span.setAttribute("recurrentEventId", recurrentEventId)

            externalCalendarRecurrentEventService.get(recurrentEventId.toSafeUUID())
                .flatMap { externalCalendarRecurrentEvent ->
                    googleCalendarEventService.reFetchRecurrentEvent(externalCalendarRecurrentEvent)
                }
                .flatMap { externalCalendarRecurrentEvent ->
                    googleCalendarEventService.reFetchFutureGoogleCalendarEventsForRecurrence(
                        externalCalendarRecurrentEvent
                    )
                        .flatMap {
                            googleCalendarEventService.getFutureGoogleCalendarEventsForRecurrence(
                                externalCalendarRecurrentEvent
                            )
                        }
                }
                .get()

            Response.OK
        }

    suspend fun refetchGoogleCalendarEvent(externalCalendarEventId: String): Response {
        logger.info(
            "GoogleCalendarController::refetchGoogleCalendarEvent",
            "requester_staff_id" to this.currentStaffId(),
            "event_id" to externalCalendarEventId,
        )

        externalCalendarEventService.get(externalCalendarEventId.toSafeUUID()).flatMap { externalCalendarEvent ->
            googleCalendarEventService.reFetchEvent(externalCalendarEvent)
        }.get()

        return Response.OK
    }

    suspend fun createEventFromAppointmentSchedule(appointmentScheduleId: String): Response {
        logger.info(
            "GoogleCalendarController::createEventFromAppointmentSchedule",
            "requester_staff_id" to this.currentStaffId(),
            "appointment_schedule_id" to appointmentScheduleId,
        )

        appointmentScheduleService.get(appointmentScheduleId.toSafeUUID()).flatMap {
            kafkaProducerService.produce(InternalAppointmentScheduleCreatedEvent(it))
            true.success()
        }

        return Response.OK
    }
}

