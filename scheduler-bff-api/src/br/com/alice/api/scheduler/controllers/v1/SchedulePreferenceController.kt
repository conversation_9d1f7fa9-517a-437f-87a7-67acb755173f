package br.com.alice.api.scheduler.controllers.v1

import br.com.alice.api.scheduler.controllers.StaffController
import br.com.alice.api.scheduler.controllers.model.SchedulePreferenceRequest
import br.com.alice.api.scheduler.controllers.model.SchedulePreferenceWithStaffInfoResponse
import br.com.alice.api.scheduler.converters.SchedulePreferenceResponseConverter
import br.com.alice.api.scheduler.converters.SchedulePreferenceWithStaffNameResponseConverter
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmapNotNull
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.schedule.client.SchedulePreferenceService
import br.com.alice.schedule.client.StaffSchedulePreferenceService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class SchedulePreferenceController(
    private val schedulePreferenceService: SchedulePreferenceService,
    private val staffSchedulePreferenceService: StaffSchedulePreferenceService,
    staffService: StaffService,
) : StaffController(staffService) {

    suspend fun get(staffId: String): Response {
        val currentStaffId = currentStaffId()
        logger.info(
            "SchedulePreferenceController::get",
            "staff_id" to staffId,
            "requester_id" to currentStaffId
        )
        return schedulePreferenceService.getByStaffId(staffId = staffId.toUUID())
            .map { SchedulePreferenceResponseConverter.convert(it) }
            .foldResponse()
    }

    suspend fun getOrCreate(staffId: String): Response {
        val currentStaffId = currentStaffId()
        logger.info(
            "SchedulePreferenceController::getOrCreate",
            "staff_id" to staffId,
            "requester_id" to currentStaffId
        )
        return schedulePreferenceService.getOrCreate(staffId.toSafeUUID())
            .map { SchedulePreferenceResponseConverter.convert(it) }
            .mapError { throwable ->
                logger.error(
                    "SchedulePreferenceController::getOrCreate",
                    throwable
                )
                throwable
            }
            .foldResponse()
    }

    suspend fun update(staffId: String, request: SchedulePreferenceRequest): Response {
        val currentStaffId = currentStaffId()
        logger.info(
            "SchedulePreferenceController::update",
            "staff_id" to staffId,
            "request" to request,
            "requester_id" to currentStaffId
        )
        return schedulePreferenceService.getByStaffId(staffId.toUUID())
            .flatMap { schedulePreference ->
                schedulePreferenceService.update(
                    schedulePreference.copy(
                        intervalBetweenEvents = request.intervalBetweenEvents,
                        weeklyHours = request.weeklyHours,
                        zoomLink = request.zoomLink
                    )
                )
            }
            .map { SchedulePreferenceResponseConverter.convert(it) }
            .mapError { throwable ->
                logger.error(
                    "SchedulePreferenceController::update",
                    throwable
                )
                throwable
            }
            .foldResponse()
    }

    suspend fun search(queryParams: Parameters): Response = coroutineScope {
        val range = parseRange(queryParams)
        val searchToken = queryParams["q"]
        val hasStaffSchedules = queryParams["has_staff_schedules"]?.toBoolean()
        val role = queryParams["role"]

        logger.info("SchedulePreferenceController::index", "query_params" to queryParams.entries())

        val staffSchedulePreferenceCountDeferred = async {
            staffSchedulePreferenceService.countByFilters(
                searchToken,
                staffRole = role,
                hasStaffSchedules = hasStaffSchedules
            ).get()
        }

        return@coroutineScope staffSchedulePreferenceService.search(
            searchToken,
            staffRole = role,
            range = range,
            hasStaffSchedules = hasStaffSchedules,
        )
            .flatMapPair { staffSchedulePreferences ->
                val schedulePreferenceIds = staffSchedulePreferences.map { it.schedulePreferenceId }.distinct()
                if (hasStaffSchedules != null) {
                    schedulePreferenceService.getOnlyWithCalendar(schedulePreferenceIds)
                } else {
                    schedulePreferenceService.getByIds(schedulePreferenceIds)
                }
            }
            .map { schedulePreferencesAndStaffSchedulePreferences ->
                val schedulePreferences = schedulePreferencesAndStaffSchedulePreferences.first.associateBy { it.id }
                val staffSchedulePreferences = schedulePreferencesAndStaffSchedulePreferences.second
                staffSchedulePreferences.pmapNotNull { staffSchedulePreference ->
                    schedulePreferences[staffSchedulePreference.schedulePreferenceId]?.let {
                        SchedulePreferenceWithStaffNameResponseConverter.convert(
                            source = it,
                            staffSchedulePreference = staffSchedulePreference
                        )
                    }
                }
            }
            .map { schedulePreferences ->
                SchedulePreferenceWithStaffInfoResponse(
                    total = staffSchedulePreferenceCountDeferred.await(),
                    items = schedulePreferences
                )
            }
            .foldResponse()
    }
}
