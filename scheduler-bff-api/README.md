# Scheduler BFF
This is our Scheduler BFF API, responsible for handle all HTTP calls from schedule frontend.

### Responsible Team
Agendamentos, find us on ``#eng-agendamento`` on Slack ;)
- [engineering notion](https://www.notion.so/alicehealth/Agendamento-c3d9f9eb3f934a2c895f953b577c4020)
- [product notion](https://www.notion.so/alicehealth/Agendamentos-957eef7ec0cb46ac803feccec7357d97)

### Local development

Requirements
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``clean`` - delete build files
* ``tests`` - run all tests
* ``run`` - run project on 8110 port

### Run locally
``make run`` and then you can access http://localhost:8110
