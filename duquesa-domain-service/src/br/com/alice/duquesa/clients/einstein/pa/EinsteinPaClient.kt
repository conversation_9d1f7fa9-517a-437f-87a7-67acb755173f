package br.com.alice.duquesa.clients.einstein.pa

import ServiceConfig
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.serialization.gsonIdentity
import br.com.alice.duquesa.clients.einstein.pa.model.EinsteinAuthResponse
import br.com.alice.duquesa.clients.einstein.pa.model.EinsteinGeneratePaRequest
import br.com.alice.duquesa.clients.einstein.pa.model.EinsteinGeneratePaResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import io.ktor.client.HttpClient
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.content.TextContent

class EinsteinPaClient(
    private val client: HttpClient,
) {
    private val credentials = ServiceConfig.einstein
    private val gson = gsonIdentity

    suspend fun generateTicket(
        body: EinsteinGeneratePaRequest,
        auth: EinsteinAuthResponse
    ): Result<EinsteinGeneratePaResponse, Throwable> =
        Result.of<String, Throwable> {
            logger.debug("EINSTEIN PA", "payload" to gson.toJson(body))
            client.post("${credentials.apiUrl}/servico/adm/acionamentos/create") {
                header("x-access-token", auth.token)
                setBody(TextContent(gson.toJson(body), ContentType.Application.Json))
            }.bodyAsText()
        }.map {
            gson.fromJson(it, EinsteinGeneratePaResponse::class.java)
        }.then {
            logger.info("success to get ticket PA Einstein", "response" to it)
        }.thenError {
            logger.error("error for get ticket PA Einstein", "document" to body.document, it)
        }
}
