package br.com.alice.duquesa.clients.einstein.scheduling.model

import com.google.gson.annotations.SerializedName

data class EinsteinUnit(
    val id: String,
    val name: String,
    @SerializedName("unit_external_id")
    val unitExternalId: String? = null,
    val company: String,
    @SerializedName("business_unit_work_hour_id")
    val businessUnitWorkHourId: String,
    @SerializedName("business_unit_type_id")
    val businessUnitTypeId: String,
    val address: String,
    @SerializedName("address_number")
    val addressNumber: String,
    val active: Boolean,
    val cnpj: String,
    val cep: String,
    @SerializedName("block_building")
    val blockBuilding: String,
    @SerializedName("address_complement")
    val addressComplement: String? = "",
    val city: String,
    val state: String,
    val neighborhood: String,
    @SerializedName("id_sgh")
    val idSGH: String,
    val cnes: String,
    val latitude: String,
    val longitude: String,
    @SerializedName("business_unit_work_hour")
    val businessUnitWorkHour: EinsteinUnitWorkHours,
    val locations: List<EinsteinUnitLocation>
) {
    fun getAliceAddress(): String =
        StringBuilder(address)
            .append(" $addressNumber")
            .append(" - $city")
            .trim()
            .toString()

    fun toScheduleRequest() = ScheduleAppointmentUnit(
        unitId = idSGH,
        catalogUnitId = id,
        name = name,
        address = address,
        neighborhood = neighborhood,
        number = addressNumber,
        additionalInfo = addressComplement,
        city = city,
        state = state
    )
}

data class EinsteinUnitLocation(
    val id: String,
    val name: String,
    val description: String,
    val businessUnitId: String,
    val observations: String,
)

data class EinsteinUnitWorkHours(
    val id: String,
    @SerializedName("work_hour_start")
    val workHourStart: String? = null,
    @SerializedName("work_hour_end")
    val workHourEnd: String? = null,
    @SerializedName("is_24h_weekdays")
    val is24hWeekdays: Boolean,
    @SerializedName("is_24h_weekends")
    val is24hWeekends: Boolean,
    @SerializedName("is_24h_holiday")
    val is24hHoliday: Boolean,

    @SerializedName("different_work_hour_start")
    val differentWorkHourStart: String? = null,
    @SerializedName("different_work_hour_end")
    val differentWorkHourEnd: String? = null,
    @SerializedName("holiday_work_hour_start")
    val holidayWorkHourStart: String? = null,
    @SerializedName("holiday_work_hour_end")
    val holidayWorkHourEnd: String? = null,

    val holiday: Boolean,
    val monday: Boolean,
    val tuesday: Boolean,
    val wednesday: Boolean,
    val thursday: Boolean,
    val friday: Boolean,
    val saturday: Boolean,
    val sunday: Boolean,
)
