package br.com.alice.duquesa.clients.einstein.elegible_life.model

import com.google.gson.annotations.SerializedName

data class EinsteinLifeRegisterRequest(
    val type: String = "ext",
    val postback: String?,
    @SerializedName("operacao")
    val operation: String,
    @SerializedName("contratos")
    val contracts: List<String>?,
    @SerializedName("dados")
    val data: List<EinsteinLifeData>
)

enum class EinsteinLifeRegisterOperation(val description: String) {
    I("Incluir Vida"),
    A("Atualizar Vida"),
    E("Excluir Vida");
}

data class EinsteinLifeData(
    @SerializedName("ID_EMPRESA")
    val personId: String? = null,
    @SerializedName("NOME")
    val name: String,
    @SerializedName("NOME_SOCIAL")
    val socialName: String? = null,
    @SerializedName("DNAS")
    val birthDate: String,
    @SerializedName("SEXO")
    val sex: String,
    @SerializedName("NCPF")
    val nationalId: String? = null,
    @SerializedName("NRG")
    val document: String? = null,
    @SerializedName("NPAS_RNE")
    val passport: String? = null,
    @SerializedName("NCPFTITULAR")
    val holderNationalId: String? = null,
    @SerializedName("NACITITULAR")
    val holderDocumentType: String? = null,
    @SerializedName("NACI")
    val documentType: String? = null,
    @SerializedName("NCAR")
    val cardNumber: String? = null,
    @SerializedName("TITU")
    val holder: String,
    @SerializedName("PAREN")
    val kinship: String,
    @SerializedName("ENDER")
    val address: String? = null,
    @SerializedName("E_NUM")
    val number: String? = null,
    @SerializedName("E_COMP")
    val complement: String? = null,
    @SerializedName("E_CEP")
    val zipcode: String? = null,
    @SerializedName("E_BAIR")
    val neighborhood: String? = null,
    @SerializedName("E_ESTA")
    val state: String? = null,
    @SerializedName("FONE")
    val phoneNumber: String? = null,
    @SerializedName("EMAIL")
    val email: String? = null,
    @SerializedName("SUBGRUPO")
    val subgroup: String,
    @SerializedName("VAL_DE")
    val contractStartDate: String,
    @SerializedName("VAL_ATE")
    val contractEndDate: String,

    )

enum class EinsteinLifeRegisterSex(val code: String) {
    FEMALE( "F"),
    MALE( "M"),
}

enum class EinsteinLifeRegisterHolder(val description: String) {
    TITU("Titular"),
    DEPE("Dependente"),
}

enum class EinsteinLifeRegisterDocumentType(val code: String, val description: String) {
    PASSPORT("141", "Passaporte/RNE"),
    CPF("2", "CPF"),
}

enum class EinsteinLifeRegisterKinship(val code: String, val description: String) {
    TITULAR("15", "Titular"),
    OUTROS("16", "Outros"),
}
