package br.com.alice.duquesa.clients.fleury.aps

import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.duquesa.clients.fleury.aps.model.FleuryApsAppointment
import br.com.alice.duquesa.clients.fleury.aps.model.FleuryApsScheduleRequest
import br.com.alice.duquesa.clients.fleury.aps.model.FleuryApsToken
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import io.ktor.client.HttpClient

class FleuryApsScheduleClient(
    override val client: HttpClient
): FleuryApsBaseClient(client) {

    suspend fun schedule(body: FleuryApsScheduleRequest, token: FleuryApsToken): Result<FleuryApsAppointment, Throwable> =
        post<FleuryApsScheduleRequest, FleuryApsAppointment>(method = "schedule", path = "integration/cuidado-digital/v1/consultas", token = token, body = body)
            .map { it.copy(date = it.date.toSaoPauloTimeZone()) }


    suspend fun cancel(id: String, token: FleuryApsToken): Result<Boolean, Throwable> =
        patch(method = "cancel", path = "integration/cuidado-digital/v1/consultas/$id/cancel", token = token)
            .map { true }
}
