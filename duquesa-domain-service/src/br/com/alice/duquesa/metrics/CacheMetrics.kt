package br.com.alice.duquesa.metrics

import br.com.alice.common.observability.metrics.Metric
import br.com.alice.common.redis.Result

object CacheMetrics {
    const val EINSTEIN_SCH_LIST_PRODUCTS = "cache_einstein_list_products"
    const val EINSTEIN_SCH_TOKEN = "cache_einstein_token"
    const val EINSTEIN_SCH_COVERAGE = "cache_einstein_coverage"
    const val EINSTEIN_SCH_DATE = "cache_einstein_date"

    const val PROXXIA_CIA_TOKEN = "proxxia_cia_token"
    const val PROXXIA_CIA_ESTABLISHMENTS = "proxxia_cia_establishments"

    fun registerMetrics() {
        listOf(
            EINSTEIN_SCH_LIST_PRODUCTS,
            EINSTEIN_SCH_TOKEN,
            EINSTEIN_SCH_COVERAGE,
            EINSTEIN_SCH_DATE,
            PROXXIA_CIA_TOKEN
        ).forEach { metric ->
            Metric.registerCounter(metric, "result" to Result.SUCCESS.toLabel())
            Metric.registerCounter(metric, "result" to Result.FAILURE.toLabel())
        }
    }

    fun incrementEinsteinListProducts(result: Result) =
        Metric.increment(EINSTEIN_SCH_LIST_PRODUCTS, "result" to result.toLabel())

    fun incrementEinsteinToken(result: Result) =
        Metric.increment(EINSTEIN_SCH_TOKEN, "result" to result.toLabel())

    fun incrementEinsteinCoverage(result: Result) =
        Metric.increment(EINSTEIN_SCH_TOKEN, "result" to result.toLabel())

    fun incrementEinsteinDate(result: Result) =
        Metric.increment(EINSTEIN_SCH_DATE, "result" to result.toLabel())

    fun incrementCiaToken(result: Result) =
        Metric.increment(PROXXIA_CIA_TOKEN, "result" to result.toLabel())

    fun incrementCiaEstablishments(result: Result) =
        Metric.increment(PROXXIA_CIA_ESTABLISHMENTS, "result" to result.toLabel())
}
