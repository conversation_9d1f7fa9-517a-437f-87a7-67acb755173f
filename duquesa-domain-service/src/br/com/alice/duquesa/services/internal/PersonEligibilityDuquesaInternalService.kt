package br.com.alice.duquesa.services.internal

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.PersonEligibilityBrand
import br.com.alice.data.layer.models.PersonEligibilityDuquesa
import br.com.alice.data.layer.services.PersonEligibilityDuquesaDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap

class PersonEligibilityDuquesaInternalService(
    private val dataService: PersonEligibilityDuquesaDataService
) {
    suspend fun findByPersonIdAndProvider(
        personId: PersonId, duquesaCia: PersonEligibilityBrand
    ): Result<PersonEligibilityDuquesa, Throwable> = dataService.findOne {
        where { this.personId.eq(personId) and this.brand.eq(duquesaCia) }
    }

    suspend fun upsert(model: PersonEligibilityDuquesa): Result<PersonEligibilityDuquesa, Throwable> =
        findByPersonIdAndProvider(model.personId, model.brand).flatMap {
            dataService.update(model.copy(id = it.id, version = it.version))
        }.coFoldNotFound {
            dataService.add(model)
        }.then {
            logger.info(
                "create or update person eligibility",
                "brand" to it.brand,
                "person_id" to it.personId,
                "version" to it.version,
                "external_person_id" to it.externalPersonId
            )
        }.thenError {
            logger.error(
                "error for create or update person eligibility",
                "brand" to model.brand,
                "person_id" to model.personId,
                "external_person_id" to model.externalPersonId,
                it
            )
        }
}
