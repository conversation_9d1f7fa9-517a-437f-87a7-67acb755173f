package br.com.alice.duquesa.services.internal

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.GenericCache
import br.com.alice.duquesa.client.SlotsRequest
import br.com.alice.duquesa.clients.fleury.aps.FleuryApsAuthClient
import br.com.alice.duquesa.clients.fleury.aps.FleuryApsEstablishmentsClient
import br.com.alice.duquesa.clients.fleury.aps.FleuryApsScheduleClient
import br.com.alice.duquesa.clients.fleury.aps.FleuryApsSlotsClient
import br.com.alice.duquesa.clients.fleury.aps.converters.toFleury
import br.com.alice.duquesa.clients.fleury.aps.converters.toFleuryAuth
import br.com.alice.duquesa.clients.fleury.aps.converters.toFleuryPatient
import br.com.alice.duquesa.clients.fleury.aps.converters.toModelClient
import br.com.alice.duquesa.clients.fleury.aps.model.FleuryApsScheduleRequest
import br.com.alice.duquesa.clients.fleury.aps.model.FleuryApsToken
import br.com.alice.duquesa.event.FleuryScheduleCancelEvent
import br.com.alice.duquesa.event.FleuryScheduleRegisterEvent
import br.com.alice.duquesa.models.Establishment
import br.com.alice.duquesa.models.Slot
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class FleuryApsService(
    private val authClient: FleuryApsAuthClient,
    private val establishmentsClient: FleuryApsEstablishmentsClient,
    private val slotsClient: FleuryApsSlotsClient,
    private val scheduleClient: FleuryApsScheduleClient,
    private val personService: PersonService,
    private val cache: GenericCache,
    private val producer: KafkaProducerService,
) {
    suspend fun getEstablishments(personId: PersonId): Result<List<Establishment>, Throwable> =
        getTokenForPerson(personId)
            .flatMap { token -> establishmentsClient.getEstablishments(token = token) }
            .map { it.map { unit -> unit.toModelClient() } }

    suspend fun getSlotsAvailable(personId: PersonId, filters: SlotsRequest): Result<List<Slot>, Throwable> =
        getTokenForPerson(personId)
            .flatMap { token -> slotsClient.getSlots(filters.toFleury(), token) }
            .map { it.map { slot -> slot.toSlot() } }

    suspend fun getSlotsAvailableByProfessional(personId: PersonId, professionalId: String, filters: SlotsRequest): Result<List<Slot>, Throwable> =
        getTokenForPerson(personId)
            .flatMap { token -> slotsClient.getSlots(
                filters.toFleury(professionalId),
                token
            ) }
            .map { it.map { slot -> slot.toSlot() } }

    suspend fun schedule(personId: PersonId, slotId: String) =
        personService.get(personId)
            .flatMapPair { getTokenForPerson(personId)  }
            .flatMap { (token, person) -> scheduleClient.schedule(
                FleuryApsScheduleRequest(slotId, person.toFleuryPatient()),
                token
            ) }
            .then { appointment ->
                logger.info("Fleury APS scheduled", "person_id" to personId, "id" to appointment.id)
                producer.produce(FleuryScheduleRegisterEvent(
                    personId = personId,
                    id = appointment.id,
                    date = appointment.date,
                    duration = appointment.duration,
                    professionalId = appointment.professional.id,
                    professionalName = appointment.professional.name.orEmpty(),
                    unitId = appointment.unit.unitId.orEmpty(),
                    unitAddress = appointment.unit.address.orEmpty(),
                    unitName = appointment.unit.name.orEmpty(),
                    attendanceLink = appointment.attendanceLink,
                    type = appointment.type.toAliceType(),
                    appointmentType = appointment.appointmentType.toAliceType()
                ))
            }

    suspend fun cancel(personId: PersonId, id: String) =
        getTokenForPerson(personId)
            .flatMap { token -> scheduleClient.cancel(id, token) }
            .then {
                logger.info("Fleury APS schedule canceled", "person_id" to personId, "id" to id)
                producer.produce(FleuryScheduleCancelEvent(
                    id = id,
                    personId = personId
                ))
            }

    private suspend fun getTokenForPerson(personId: PersonId): Result<FleuryApsToken, Throwable> = coResultOf {
        cache.get(
            key = "fleury-aps:auth:$personId",
            type = FleuryApsToken::class,
            expirationTime = 5 * 60L // 5 minutes
        ) {
            personService.get(personId)
                .flatMap { authClient.auth(it.toFleuryAuth()) }
                .get()
        }
    }
}
