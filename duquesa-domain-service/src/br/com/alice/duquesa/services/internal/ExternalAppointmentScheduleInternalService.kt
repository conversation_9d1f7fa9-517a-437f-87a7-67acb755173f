package br.com.alice.duquesa.services.internal

import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ExternalAppointmentSchedule
import br.com.alice.data.layer.models.ExternalAppointmentScheduleModel
import br.com.alice.data.layer.models.ScheduleProvider
import br.com.alice.data.layer.services.ExternalAppointmentScheduleModelDataService
import br.com.alice.duquesa.event.ExternalAppointmentScheduleUpsertEvent
import br.com.alice.duquesa.metrics.Metrics
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class ExternalAppointmentScheduleInternalService(
    private val dataService: ExternalAppointmentScheduleModelDataService,
    private val kafkaProducerService: KafkaProducerService
) {
    suspend fun findBySlotId(
        personId: PersonId,
        slotId: String,
        provider: ScheduleProvider
    ): Result<ExternalAppointmentSchedule, Throwable> = dataService.findOne {
        where { this.personId.eq(personId) and this.slotId.eq(slotId) and this.provider.eq(provider) }
    }.map { it.toTransport() }

    suspend fun upsert(model: ExternalAppointmentSchedule): Result<ExternalAppointmentSchedule, Throwable> =
        findBySlotId(slotId = model.slotId, provider = model.provider, personId = model.personId).flatMap {
            update(model.copy(id = it.id, version = it.version))
        }.coFoldNotFound {
            add(model.toModel())
        }.thenError {
            logger.error(
                "error to upsert external appointment schedule",
                "provider" to model.provider,
                "person_id" to model.personId,
                "version" to model.version,
                it
            )
        }

    suspend fun update(model: ExternalAppointmentSchedule) = dataService.update(model.toModel())
        .map { it.toTransport() }
        .then {
            kafkaProducerService.produce(ExternalAppointmentScheduleUpsertEvent(it))
            logger.info(
                "update external appointment schedule",
                "provider" to it.provider,
                "status" to it.status,
                "person_id" to it.personId,
                "version" to it.version,
                "slot_id" to it.slotId
            )
        }.thenError {
            logger.error(
                "update external appointment schedule",
                "provider" to model.provider,
                "person_id" to model.personId,
                "version" to model.version,
                it
            )
        }

    private suspend fun add(model: ExternalAppointmentScheduleModel) = dataService.add(model)
        .map { it.toTransport() }
        .then {
            Metrics.incrementAppointment(it)
            kafkaProducerService.produce(ExternalAppointmentScheduleUpsertEvent(it))
            logger.info(
                "create appointment schedule",
                "provider" to it.provider,
                "person_id" to it.personId,
                "version" to it.version,
                "slot_id" to it.slotId
            )
        }.thenError {
            logger.error(
                "create or update external appointment schedule",
                "provider" to model.provider,
                "person_id" to model.personId,
                "version" to model.version,
                it
            )
        }
}

fun ExternalAppointmentScheduleModel.toTransport() = this.convertTo(ExternalAppointmentSchedule::class)
fun ExternalAppointmentSchedule.toModel() = this.convertTo(ExternalAppointmentScheduleModel::class)
