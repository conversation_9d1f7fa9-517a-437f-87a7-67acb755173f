package br.com.alice.duquesa.services.internal

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.models.ExternalPaOrigin
import br.com.alice.duquesa.clients.fleury.pa.FleuryAuthClient
import br.com.alice.duquesa.clients.fleury.pa.FleuryBaseTest
import br.com.alice.duquesa.clients.fleury.pa.FleuryPaClient
import br.com.alice.duquesa.clients.fleury.pa.model.GenerateTicketPaResponse
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test

class FleuryPaServiceTest : FleuryBaseTest() {
    private val authClient: FleuryAuthClient = mockk()
    private val paClient: FleuryPaClient = mockk()

    val service = FleuryPaService(authClient, paClient)
    private val paResponse = GeneratedAttendanceResponse(
        link = "url_short",
        id = "7e6d975a-b13e-439f-9fbe-91161c93dc00",
        origin = ExternalPaOrigin.FLEURY
    )


    @Test
    fun `#generateAttendance should generated attendance PA`() = mockRangeUUID("7e6d975a-b13e-439f-9fbe-91161c93dc00".toUUID()) {
        val paFleuryResponse = GenerateTicketPaResponse("url_long", "url_short")

        coEvery { authClient.auth() } returns token.success()
        coEvery { paClient.generateTicket(requestPerson, token) } returns paFleuryResponse.success()

        val response = service.generateAttendance(person)

        assertThat(response).isSuccessWithData(paResponse)


    }
}
