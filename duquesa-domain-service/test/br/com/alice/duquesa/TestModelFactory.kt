package br.com.alice.duquesa

import br.com.alice.duquesa.clients.cia_consulta.DATE_FORMAT
import br.com.alice.duquesa.clients.cia_consulta.models.CIA_DOCTOR_FAMILY
import br.com.alice.duquesa.clients.cia_consulta.models.PersonEligibility
import br.com.alice.duquesa.clients.cia_consulta.models.Professional
import br.com.alice.duquesa.clients.cia_consulta.models.ProfessionalRegistration
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleAddress
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleProduct
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleProductGroup
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleProductGroupArea
import br.com.alice.duquesa.clients.cia_consulta.models.Slot
import br.com.alice.duquesa.clients.cia_consulta.models.SlotsAvailableResponse
import br.com.alice.duquesa.clients.cia_consulta.models.SlotsData
import br.com.alice.duquesa.models.Establishment
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

object TestModelFactory {
    fun buildEstablishmentClient() = Establishment(
        id = "10",
        name = "Shop Metrô Tucuruvi",
        address = "Avenida Doutor Antônio Maria Laet 566 Shopping Metrô Tucuruvi - L1 - SL40A - São Paulo",
        latitude = -23.480860999999997,
        longitude = -46.603857999999995,
        addressLink = "https://www.google.com/maps/place/Cia.+da+Consulta/@-23.4808616,-46.606052,17z/data=!3m1!4b1!4m5!3m4!1s0x94cef7e411487529:0x493af52950848560!8m2!3d-23.4808616!4d-46.6038579"
    )

    fun buildEstablishmentCia(id: Long = 10) = br.com.alice.duquesa.clients.cia_consulta.models.Establishment(
        id = id,
        name = "Shop Metrô Tucuruvi",
        address = ScheduleAddress(
            address = "Avenida Doutor Antônio Maria Laet",
            addressNumber = "566",
            additionalAddress = "Shopping Metrô Tucuruvi - L1 - SL40A",
            neighborhood = "Parada Inglesa",
            city = "São Paulo",
            state = "SP",
            country = "Brasil",
            zipcode = "02240-000",
            latitude = -23.480860999999997,
            longitude = -46.603857999999995,
            ibgeCode = "355030"
        ),
        openingHours = "Segunda à sexta das 8h às 19h.\r\nSábado das 8h às 14h.",
        collectionHours = "Segunda à sexta das 8h às 14h.<br/>Sábado das 8h às 9:30h.",
        googleMapsLink = "https://www.google.com/maps/place/Cia.+da+Consulta/@-23.4808616,-46.606052,17z/data=!3m1!4b1!4m5!3m4!1s0x94cef7e411487529:0x493af52950848560!8m2!3d-23.4808616!4d-46.6038579",
        contact = "Para entrar em contato com a Cia. da Consulta, ligue (011) 2828-2008 ou envie um e-<NAME_EMAIL>. Nosso horário de atendimento telefônico é de segunda a sexta-feira, das 7h às 20h e, de sábado, das 7h às 13h.",
        howToArrive = "Se escolher vir de metrô, utilize a linha azul e desça na estação Tucuruvi. Ao sair do metrô pelo lado direito, siga na direção do Shopping Metrô Tucuruvi. Ao passar as catracas, desça as escadas e siga pela direita até chegar a unidade da Cia. da Consulta, que estará a sua esquerda.<br/><br/>Se preferir vir de carro também é fácil, nossa unidade fica dentro do Shopping Metrô Tucuruvi. Vindo pela Avenida Dr. Antônio Maria Laet, siga em frente na direção da Rua Carapari. Vire à direita na Rua DRua Moacyr Vaz de Andrade, mantenha a direita na Rua Paulo de Faria. A entrada do Shopping Metrô Tucuruvi estará à direita.<br/>Nós não temos estacionamento próprio, porém, você pode utilizar o estacionamento do shopping.",
        cnes = "9797157"
    )

    fun buildSlotCia(id: Long = 10020308) = Slot(
        id = id,
        status = "available",
        scheduleDate = LocalDateTime.parse(
            "2023-04-12 16:40:00",
            DateTimeFormatter.ofPattern(DATE_FORMAT)
        ),
        duration = 20,
        professionalId = 21263,
        establishmentId = 10,
        availableProductIds = listOf(CIA_DOCTOR_FAMILY),
        slotType = "consult",
        minAge = 16,
        maxAge = 150,
        productId = CIA_DOCTOR_FAMILY,
        medicalInsuranceId = 1,
        medicalInsuranceCategoryId = 1,
        personId = null,
        roomId = null
    )

    fun buildProductCia() = ScheduleProduct(
        id = CIA_DOCTOR_FAMILY,
        name = "Consulta de medicina de familia",
        description = null ,
        preparation = null,
        group = ScheduleProductGroup(
            id = 910001,
            name = "Consulta de medicina de familia",
            area = ScheduleProductGroupArea(
                id = 910,
                name = "CIA - Consultas"
            )
        ),
        cboCode = "225110",
        price = 0L
    )

    fun buildProfessionalCia(
        id: Long = 1
    ) = Professional(
        id = id,
        name = "Rafael de Campos",
        gender = "I",
        hasImage = true,
        registration = ProfessionalRegistration(
            registrationType = "CRM",
            registrationNumber = 140503,
            registrationState = "SP"
        ),
        productsIds = listOf(
            CIA_DOCTOR_FAMILY,
            9100110001
        ),
        establishmentIds = listOf(
            11
        )
    )

    fun slotsResponseCia(
        slots: List<Slot> = listOf(buildSlotCia()),
        professionals: List<Professional> = listOf(buildProfessionalCia()),
        establishments: List<br.com.alice.duquesa.clients.cia_consulta.models.Establishment> = listOf(
            buildEstablishmentCia()
        ),
    ) = SlotsAvailableResponse(
        data = SlotsData(
            slots = slots,
            products = listOf(
                buildProductCia()
            ),
            professionals = professionals,
            establishments = establishments,
        )
    )

    fun buildPersonEligibility() = PersonEligibility(
        updatedAt = "2023-04-13T17:43:50.000Z",
        id = 171674,
        insuranceId = 15,
        insuranceCategoryId = "1",
        personId = "534470",
        establishmentId = 3,
        allowed = "L",
        status = "I",
        validSince = "2023-04-13T00:00:00.000Z",
        validUntil = "2025-04-13T00:00:00.000Z",
        user = "proxxia_api"
    )
}
