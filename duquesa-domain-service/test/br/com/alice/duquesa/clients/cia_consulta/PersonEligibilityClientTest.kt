package br.com.alice.duquesa.clients.cia_consulta

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.duquesa.clients.cia_consulta.models.PersonEligibility
import br.com.alice.duquesa.clients.cia_consulta.models.PersonEligibilityRequest
import br.com.alice.duquesa.clients.cia_consulta.models.PersonEligibilityResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.content.TextContent
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import java.net.URLDecoder
import kotlin.test.Ignore
import kotlin.test.Test

class PersonEligibilityClientTest : ProxxiaCiaBaseTest() {
    private val requestPersonEligibility = PersonEligibilityRequest(
        personId = idPersonCia,
    )

    private val personEligibility = PersonEligibility(
        updatedAt = "2023-04-13T17:43:50.000Z",
        id = 171674,
        insuranceId = 15,
        insuranceCategoryId = "1",
        personId = "534470",
        establishmentId = 3,
        allowed = "L",
        status = "I",
        validSince = "2023-04-13T00:00:00.000Z",
        validUntil = "2025-04-13T00:00:00.000Z",
        user = "proxxia_api"
    )

    @Test
    @Ignore("Not run by default because call Cia test server")
    fun `#upsertPersonEligibility should add person eligibility in cia`() = runBlocking {
        val authClient = CiaAuthClient(DefaultHttpClient(timeoutInMillis = 10_000), cache)
        val personEligibilityClient = PersonEligibilityClient(DefaultHttpClient(timeoutInMillis = 50_000))
        val token = authClient.auth().get()

        val personCiaResponse = personEligibilityClient.upsertPersonEligibility(requestPersonEligibility, token)

        ResultAssert.assertThat(personCiaResponse).isSuccessOfType(PersonEligibilityResponse::class)
        Assertions.assertEquals(idPersonCia.toString(), personCiaResponse.get().personEligibility[0].personId)
    }

    @Test
    @Ignore("Not run by default because call Cia test server")
    fun `#getPersonEligibilityById should add person eligibility in cia`() = runBlocking {
        val authClient = CiaAuthClient(DefaultHttpClient(timeoutInMillis = 10_000), cache)
        val personEligibilityClient = PersonEligibilityClient(DefaultHttpClient(timeoutInMillis = 50_000))
        val token = authClient.auth().get()

        val personCiaResponse = personEligibilityClient.getPersonEligibilityById(idPersonCia, token)

        ResultAssert.assertThat(personCiaResponse).isSuccessOfType(PersonEligibilityResponse::class)
        Assertions.assertEquals(idPersonCia.toString(), personCiaResponse.get().personEligibility[0].personId)
    }

    @Test
    fun `#upsertPersonEligibility should add person eligibility in cia mock`() = runBlocking {
        val expected = PersonEligibilityResponse(
            personEligibility = listOf(
                personEligibility
            ),
            success = true
        )
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/api/personElegibility")
                    assertThat(request.method).isEqualTo(HttpMethod.Post)
                    assertThat(request.headers["Authorization"]).isEqualTo(token.token)

                    val textContent = (request.body as TextContent)
                    assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    assertThat(textContent.text)
                        .isEqualTo(gson.toJson(requestPersonEligibility))

                    respond(expectedJson)
                }
            }
        }

        val clientMock = PersonEligibilityClient(httpClientMock)

        val response = clientMock.upsertPersonEligibility(requestPersonEligibility, token)
        ResultAssert.assertThat(response).isSuccessWithData(expected)
    }

    @Test
    fun `#getPersonEligibilityById should add person eligibility in cia mock`() = runBlocking {
        val expected = PersonEligibilityResponse(
            personEligibility = listOf(
                personEligibility
            ),
            success = true
        )
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            expectSuccess = true
            engine {
                addHandler { request ->
                    assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/api/personElegibility/$idPersonCia")
                    assertThat(request.method).isEqualTo(HttpMethod.Get)
                    assertThat(request.headers["Authorization"]).isEqualTo(token.token)

                    respond(expectedJson)
                }
            }
        }

        val clientMock = PersonEligibilityClient(httpClientMock)

        val response = clientMock.getPersonEligibilityById(idPersonCia, token)
        ResultAssert.assertThat(response).isSuccessWithData(expected)
    }
}
