package br.com.alice.duquesa.clients.fleury.pa

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.duquesa.clients.fleury.pa.model.GenerateTicketPaResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.content.TextContent
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.net.URLDecoder
import kotlin.test.Ignore
import kotlin.test.Test


class FleuryPaClientTest : FleuryBaseTest() {
    @Test
    @Ignore
    fun `#generateTicket should return link PA`(): Unit = runBlocking {
        val client = DefaultHttpClient(timeoutInMillis = 20_000)
        val authService = FleuryAuthClient(client)
        val paService = FleuryPaClient(client)

        val token = authService.auth()
        val response = paService.generateTicket(requestPerson, token.get())
        assertThat(response).isSuccess()
    }

    @Test
    fun `#generateTicket should return link PA mock`(): Unit = runBlocking {
        val paResponse = GenerateTicketPaResponse("url_long", "url_short")
        val expectedCodeResponseJson = gson.toJson(paResponse)

        val httpClientMock = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/pa-digital/v1/api/appointments/drtis/white-label/ondemand-link")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)

                    Assertions.assertThat(request.headers["client_id"]).isEqualTo(credentials.clientId)
                    Assertions.assertThat(request.headers["access_token"]).isEqualTo(token.accessToken)
                    Assertions.assertThat(request.headers["company"]).isEqualTo("ALICE")

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)

                    Assertions.assertThat(textContent.text).isEqualTo(gson.toJson(requestPerson))

                    respond(expectedCodeResponseJson)
                }
            }
        }
        val paService = FleuryPaClient(httpClientMock)
        val response = paService.generateTicket(requestPerson, token)
        assertThat(response).isSuccessWithData(paResponse)
    }
}
