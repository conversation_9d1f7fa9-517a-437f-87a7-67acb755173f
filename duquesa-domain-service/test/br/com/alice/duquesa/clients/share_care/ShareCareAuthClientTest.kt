package br.com.alice.duquesa.clients.share_care

import ServiceConfig
import br.com.alice.common.RangeUUID
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.service.serialization.gsonIdentity
import br.com.alice.duquesa.clients.share_care.model.ShareCareAuthRequest
import br.com.alice.duquesa.clients.share_care.model.ShareCareResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.content.TextContent
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.net.URLDecoder
import kotlin.test.Ignore
import kotlin.test.Test

class ShareCareAuthClientTest {
    private val credentials = ServiceConfig.shareCare
    private val gson = gsonIdentity

    @Test
    @Ignore("Not run by default because call Share Care test server")
    fun `#should returns token`() = runBlocking {
        val shareCareAuthClient = ShareCareAuthClient(DefaultHttpClient(timeoutInMillis = 10_000))

        val token = shareCareAuthClient.auth()
        assertThat(token).isSuccessOfType(ShareCareResponse::class)
    }

    @Test
    fun `#auth should return token`() = runBlocking {
        val requestBody = ShareCareAuthRequest(credentials.clientId, credentials.clientSecret)
        val expected = ShareCareResponse(RangeUUID.generate().toString())
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/auth/login")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    Assertions.assertThat(textContent.text).isEqualTo(gson.toJson(requestBody))

                    respond(expectedJson)
                }
            }
        }

        val clientMock = ShareCareAuthClient(httpClientMock)

        val response = clientMock.auth()
        assertThat(response).isSuccessWithData(expected)
    }
}
