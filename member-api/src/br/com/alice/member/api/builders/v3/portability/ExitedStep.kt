package br.com.alice.member.api.builders.v3.portability

import br.com.alice.data.layer.models.InsurancePortabilityRequest
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.v3.PortabilityStepInput
import br.com.alice.member.api.models.onboarding.v3.PortabilityStepInputAction
import br.com.alice.member.api.models.onboarding.v3.PortabilityStepInputType
import br.com.alice.member.api.models.onboarding.v3.PortabilityStepResponse

object ExitedStep: PortabilityStep {
    override fun build(
        insurancePortabilityRequest: InsurancePortabilityRequest,
        shouldGoToLegalGuardianFlow: Boolean?,
        email: String?,
    ): PortabilityStepResponse =
        PortabilityStepResponse(
            current = insurancePortabilityRequest.step!!,
            title = "",
            input = PortabilityStepInput(
                type = PortabilityStepInputType.ACKNOWLEDGE,
                action = PortabilityStepInputAction(
                    next = null,
                    previous = null,
                ),
            ),
            navigation = getNavigation(
                insurancePortabilityRequest,
                shouldGoToLegalGuardianFlow,
                email,
            )
        )

    private fun getNavigation(
        insurancePortabilityRequest: InsurancePortabilityRequest,
        shouldGoToLegalGuardianFlow: Boolean?,
        email: String?,
    ): NavigationResponse =
        if (insurancePortabilityRequest.status === InsurancePortabilityRequestStatus.APPROVED) {
            if (shouldGoToLegalGuardianFlow != null && shouldGoToLegalGuardianFlow) {
                NavigationResponse(
                    mobileRoute = MobileRouting.LEGAL_GUARDIAN_RESPONSIBILITY_TERM,
                    properties = mapOf("email" to email!!)
                )
            } else {
                NavigationResponse(mobileRoute = MobileRouting.CONTRACT_SIGNING, link = Links.CONTRACT)
            }
        } else
            NavigationResponse(
                mobileRoute = MobileRouting.REGISTRATION,
                link = Links.REGISTRATION
            )

    override fun next(insurancePortabilityRequest: InsurancePortabilityRequest, value: String): InsurancePortabilityRequest = throw NotImplementedError("Not implemented next")

    override fun previous(insurancePortabilityRequest: InsurancePortabilityRequest): InsurancePortabilityRequest = throw NotImplementedError("Not implemented previous")
}

