package br.com.alice.member.api.builders

import br.com.alice.common.convertTo
import br.com.alice.data.layer.models.FaqContent
import br.com.alice.data.layer.models.FaqGroup
import br.com.alice.member.api.models.FaqContentResponse
import br.com.alice.member.api.models.FaqGroupResponse
import br.com.alice.member.api.models.ListFaqGroupResponse
import java.util.UUID

object FaqGroupsResponseBuilder {

    fun buildListFaqGroups(faqGroups: List<FaqGroup>, faqContents: List<FaqContent>): ListFaqGroupResponse {
        val groupResponse = faqGroups
            .map { faqGroup ->
                FaqGroupResponse(
                    title = faqGroup.title,
                    description = faqGroup.description,
                    featured = faqGroup.featured,
                    contents = buildContents(faqGroup.id, faqContents)
                )
            }.filter {
                it.contents.isNotEmpty()
            }

        return ListFaqGroupResponse(
            groupResponse = groupResponse
        )
    }
    private fun buildContents(groupId: UUID, faqContents: List<FaqContent>): List<FaqContentResponse> =
        faqContents
            .filter { groupId in it.groupIds }
            .map { it.convertTo(FaqContentResponse::class) }
}
