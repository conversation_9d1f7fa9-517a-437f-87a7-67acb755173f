package br.com.alice.member.api.controllers.duquesa.models

import br.com.alice.member.api.models.appContent.Navigation

data class EstablishmentsResponse(
    val groups: List<ContentGroup>
)

data class ContentGroup(
    val name: String,
    val establishments: List<EstablishmentResponse> = emptyList()
)

data class EstablishmentResponse(
    val id: String,
    val name: String,
    val address: String,
    val navigation: Navigation
)
