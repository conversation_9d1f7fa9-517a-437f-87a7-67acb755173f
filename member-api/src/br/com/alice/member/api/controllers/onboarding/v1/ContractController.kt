package br.com.alice.member.api.controllers.onboarding.v1

import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.idToken
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.ContractPart
import br.com.alice.member.api.builders.ContractDetailsResponseBuilder
import br.com.alice.member.api.converters.ContractPersonalInfoResponseConverter
import br.com.alice.member.api.models.onboarding.ContractPartResponse
import br.com.alice.member.api.models.onboarding.ContractResponse
import br.com.alice.member.api.models.onboarding.ContractSignatureResponse
import br.com.alice.member.api.models.onboarding.ContractSignatureSentResponse
import br.com.alice.member.api.models.onboarding.UpdatePersonInfoRequest
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.MemberDocuments
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.onboarding.GracePeriodService
import br.com.alice.membership.model.Signature
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.time.Duration
import java.time.LocalDateTime

class ContractController(
    private val personService: PersonService,
    private val memberDocuments: MemberDocuments,
    private val contractRegistry: ContractRegistry,
    private val gracePeriodService: GracePeriodService,
) : Controller() {

    suspend fun getOnboardingContract(): Response {
        val personId = PersonId.fromString(currentUid())
        return contractRegistry.findContract(personId).map { contract ->
            val documentUrl = memberDocuments.getExternalUrl(
                documentUrl = contract.documentUrl,
                expireDuration = Duration.ofMinutes(30L)
            )

            val parts = ContractPart.values().map { part ->
                ContractPartResponse(
                    part = part,
                    startPage = part.startPage,
                    endPage = part.endPage,
                    signed = contract.isPartSigned(part)
                )
            }

            ContractResponse(documentUrl, parts)
        }.foldResponse()
    }

    suspend fun sign(part: String): Response {
        val contractPart = ContractPart.fromString(part)
        val personId = PersonId.fromString(currentUid())

        val result = contractRegistry.findContract(personId).flatMap { contract ->
            personService.get(personId).flatMap { person ->
                val signature = Signature(
                    idToken = idToken()!!,
                    userAgent = userAgent(),
                    ipAddress = clientIpAddress(),
                    contractPart = contractPart,
                    person = person
                )

                contractRegistry.sign(contract, signature)
            }
        }

        return result.coFoldResponse({
            ContractSignatureResponse(
                part = contractPart,
                signedAt = LocalDateTime.now(),
            )
        })
    }

    suspend fun sendSignature(): Response {
        val personId = PersonId.fromString(currentUid())

        return contractRegistry.findContract(personId).flatMap { contract ->
            contractRegistry.finish(contract)
        }.coFoldResponse({ ContractSignatureSentResponse(it.signatureSentAt) })
    }

    suspend fun getOnboardingContractDetails(): Response =
        gracePeriodService.getGracePeriod(currentUid().toPersonId())
            .map(ContractDetailsResponseBuilder::buildContractDetailsResponse)
            .foldResponse()

    suspend fun getPersonalInfo(): Response =
        personService.get(currentUid().toPersonId())
            .map(ContractPersonalInfoResponseConverter::convert)
            .foldResponse()

    suspend fun updatePersonalInfo(request: UpdatePersonInfoRequest): Response =
        personService.get(currentUid().toPersonId())
            .then { AppStateNotifier.invalidateAppState(it.id, AppState.GAS_CONTRACT) }
            .map { person ->
                person.copy(
                    addresses = request.address?.let { listOf(it.convertTo(Address::class)) } ?: person.addresses,
                    identityDocument = request.identity?.documentNumber,
                    identityDocumentIssuingBody = request.identity?.issuingBody,
                    socialFirstName = request.socialFirstName,
                    socialLastName = request.socialLastName,
                    gender = request.gender ?: person.gender,
                    sex = request.biologicalSex ?: person.sex,
                )
            }
            .flatMap { personService.update(it) }
            .map(ContractPersonalInfoResponseConverter::convert)
            .foldResponse()
}
