package br.com.alice.member.api.controllers.onboarding.v2

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.builders.v2.CartResponseBuilder
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.member.api.models.onboarding.v2.UpdateShoppingRequest
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.ShoppingService
import br.com.alice.membership.model.onboarding.ProductOption
import br.com.alice.membership.model.onboarding.UpdateCartBundlesRequest
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class ShoppingController(
    private val personService: PersonService,
    private val shoppingService: ShoppingService,
    private val onboardingService: OnboardingService
) : Controller() {

    suspend fun startShopping() = personService.get(currentUid().toPersonId())
        .flatMap { shoppingService.startShopping(it) }.foldResponse()

    suspend fun getShopping(): Response = runShoppingOperation(::getShopping)

    private suspend fun getShopping(person: Person): Result<ProductOption, Throwable> =
        shoppingService.getShoppingProduct(person)

    suspend fun updateShopping(request: UpdateShoppingRequest): Response =
        runShoppingOperation { updateShopping(request, it) }

    private suspend fun updateShopping(
        request: UpdateShoppingRequest,
        person: Person
    ): Result<ProductOption, Throwable> {
        val shoppingCartRequest = UpdateCartBundlesRequest(
            type = request.type,
            bundleIds = request.ids,
            accommodation = request.accommodation.type,
        )
        return shoppingService.updateShoppingProductWithBundles(person, shoppingCartRequest)
    }

    private suspend fun runShoppingOperation(shoppingBlock: suspend (person: Person) -> Result<ProductOption, Throwable>): Response =
        personService.get(currentUid().toPersonId())
            .flatMap { shoppingBlock(it) }
            .map { CartResponseBuilder.build(it) }
            .foldResponse()

    suspend fun placeOrder(): Response {
        return personService.get(currentUid().toPersonId())
            .map { person ->
                val productOrder = shoppingService.placeOrder(person, currentMemberAppVersion().version).get()
                productOrder to person
            }
            .map { (productOrder, person) ->
                CartResponseBuilder.buildResponse(
                    person,
                    productOrder,
                    onboardingService.getCurrentPhase(person.id).get()
                )
            }
            .foldResponse()
    }
}
