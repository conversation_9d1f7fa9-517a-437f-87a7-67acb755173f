package br.com.alice.member.api.controllers.onboarding.v1

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswer
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.builders.PortabilityQuestionResponseBuilder
import br.com.alice.member.api.builders.PortabilityStatusResponseBuilder
import br.com.alice.member.api.models.BasicNavigationResponse
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.AnswerQuestionRequest
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.ProductOrderService
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class PortabilityController(
    private val personService: PersonService,
    private val productOrderService: ProductOrderService,
    private val portabilityService: InsurancePortabilityService,
    private val onboardingService: OnboardingService,
) : Controller() {

    companion object {
        const val ELIGIBILITY_QUESTION = "eligibility"
    }

    suspend fun listQuestions(): Response = coroutineScope {
        val personId = currentUid().toPersonId()

        val personDeferred = async { personService.get(personId).get() }
        val productOrderDeferred = async { productOrderService.findOrderWithProductAndProvidersByPersonId(personId).get() }

        val person = personDeferred.await()
        val productOrder = productOrderDeferred.await()

        PortabilityQuestionResponseBuilder
            .buildPortabilityOptinQuestionResponse(person, productOrder)
            .toResponse()
    }

    suspend fun answerQuestion(questionType: String, answerRequest: AnswerQuestionRequest): Response = coroutineScope {
        val personId = currentUid().toPersonId()

        val personDeferred = async { personService.get(personId).get() }
        val productOrderDeferred = async { productOrderService.findOrderWithProductAndProvidersByPersonId(personId).get() }

        val person = personDeferred.await()
        val productOrder = productOrderDeferred.await()

        if (questionType == ELIGIBILITY_QUESTION) {
            answerEligibilityQuestion(person, productOrder, answerRequest)
        }
        else {
            val currentQuestion = InsurancePortabilityRequestQuestionType.valueOf(questionType.uppercase())
            val answer = InsurancePortabilityRequestAnswer(answerRequest.value, currentQuestion)

            portabilityService.answer(personId, answer)
                .map { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, productOrder, it) }
                .foldResponse()
        }
    }

    private suspend fun answerEligibilityQuestion(person: Person, productOrder: OrderWithProductWithProviders, answerQuestionRequest: AnswerQuestionRequest): Response {
        val answer = answerQuestionRequest.value.trim().lowercase()

        val result: Any = when (answer) {
            "skip" -> {
                portabilityService.skip(person.id).map {
                    BasicNavigationResponse(
                        navigation = NavigationResponse(
                            mobileRoute = MobileRouting.REGISTRATION,
                            link = Links.REGISTRATION
                        )
                    )
                }.get()
            }
            "confirm" -> {
                val personId = currentUid().toPersonId()
                val portabilityRequest = portabilityService.request(personId).get()
                PortabilityQuestionResponseBuilder.buildQuestionResponse(person, productOrder, portabilityRequest)
            }
            else -> throw UnexpectedAnswerException(answer)
        }

        return result.toResponse()
    }

    suspend fun getStatus(): Response = coroutineScope {
        val personId = currentUid().toPersonId()
        
        val personDeferred = async { personService.get(personId).get() }
        val productOrderDeferred = async { productOrderService.findOrderWithProductAndProvidersByPersonId(personId).get() }
        val portabilityDeferred = async { portabilityService.findByPerson(personId).get() }
        val personOnboardingDeferred = async { onboardingService.findByPerson(personId).get() }

        val person = personDeferred.await()
        val productOrder = productOrderDeferred.await()
        val portability = portabilityDeferred.await()
        val personOnboarding = personOnboardingDeferred.await()

        PortabilityStatusResponseBuilder
            .buildStatusResponse(person, productOrder, portability, personOnboarding)
            .toResponse()
    }
}

class UnexpectedAnswerException(
    message: String,
    code: String = "unexpected_answer",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(answer: String) : this(
        message = "Unexpected answer '$answer'"
    )
}
