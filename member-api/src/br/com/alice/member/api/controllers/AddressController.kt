package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.member.api.services.AddressInternalService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.firebase.database.core.view.QueryParams
import io.ktor.http.Parameters

class AddressController(
    private val addressService: AddressInternalService,
    private val personService: PersonService
) : Controller() {

    suspend fun getAddress(queryParams: Parameters): Response = span("getAddress") { span ->
        val personId = currentUid().toPersonId()
        span.setAttribute("person_id", personId)
        span.setAttribute("params", queryParams.entries().toString())
        val withComplement = queryParams["withComplement"]?.toBoolean() ?: false
        personService.get(personId)
            .flatMap { person -> addressService.getAddress(person, withComplement) }
            .map { address -> AddressListResponse(addresses = listOf(address)) }
            .coFoldNotFound { AddressListResponse().success() }
            .foldResponse()
    }

    suspend fun updateAddress(request: UserAddressWrapperRequest): Response = span("updateAddress") { span ->
        val personId = currentUid().toPersonId()
        span.setAttribute("person_id", personId)

        addressService.updateAddress(request.address, personId)
            .map { address ->
                AddressListResponse(
                    addresses = listOf(address)
                )
            }
            .recordResult(span)
            .foldResponse()
    }

    data class AddressListResponse(
        val helperText: String = "Buscar por nome ou endereço",
        val addresses: List<AddressMapsTransport> = emptyList()
    )
}

data class UserAddressWrapperRequest(
    val address: UserAddressResponse
)
