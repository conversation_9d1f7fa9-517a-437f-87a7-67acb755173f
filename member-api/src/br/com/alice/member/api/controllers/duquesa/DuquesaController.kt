package br.com.alice.member.api.controllers.duquesa

import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.RfcException
import br.com.alice.common.toResponse
import com.github.kittinunf.result.Result

open class DuquesaController : Controller() {

    fun <V : Any, E : Throwable> Result<V, E>.foldDuquesaResponse() =
        fold(
            { it.toResponse() },
            { exception ->
                when (exception) {
                    is RfcException -> Response(
                        status = exception.statusCode,
                        message = ErrorResponse(
                            code = exception.code,
                            message = exception.message ?: "Ocorreu um erro inesperado. Código: ${exception.code}"
                        )
                    )
                    else -> throw exception
                }
            }
        )
}
