package br.com.alice.member.api.converters.appContent

import br.com.alice.app.content.model.BottomTabsTransport
import br.com.alice.common.Converter
import br.com.alice.member.api.ServiceConfig

object BottomTabsResponseConverter : Converter<BottomTabsTransport, BottomTabsTransport>(
    BottomTabsTransport::class, BottomTabsTransport::class
) {

    fun convert(bottomTabs: List<BottomTabsTransport>): List<BottomTabsTransport> {
        return bottomTabs.map {
            it.action.data.endpoint = ServiceConfig.url("/app_content/screen/${it.action.data.endpoint}")
            it
        }
    }

}
