package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.app.content.model.CardHeader
import br.com.alice.app.content.model.CardType
import br.com.alice.app.content.model.SectionType
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.member.api.converters.ActionPlanTaskScheduleTimelineUtils
import br.com.alice.member.api.models.appContent.Section
import br.com.alice.member.api.models.appContent.SectionData
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import java.time.LocalDateTime

/**
 * This is an example of how to convert a referral task into a CardBody
 * This class will incorporate specific behavior of a referral task, such as, recurrent tasks
 */
class ReferralCardListConverter(
    private val appointmentScheduleService: AppointmentScheduleService
): BaseCardListConverter<ReferralNew>() {

    suspend fun convert(source: ReferralNew, cardType: CardType = CardType.CTA): Section {
        val cardData = when (source.status) {
            ActionPlanTaskStatus.SCHEDULED,
            ActionPlanTaskStatus.ACTIVE_ON_GOING -> getScheduledCardData(source, cardType)
            ActionPlanTaskStatus.ACTIVE,
            ActionPlanTaskStatus.OVERDUE -> getActiveCardData(source, cardType)
            else -> getDefaultCard(source)
        }

        return Section(
            id = getSectionId(source),
            type = SectionType.CARD_SECTION,
            data = cardData,
            minAppVersion = "0.0.0"
        )
    }

    private suspend fun getScheduledCardData(
        task: ReferralNew,
        cardType: CardType
    ) = if (hasManySessionsAvailable(task)) {
        buildCardDataForMultipleSessions(task, cardType)
    } else {
        buildCardDataForUniqueSessions(task, cardType)
    }

    private fun getActiveCardData(
        task: ReferralNew,
        cardType: CardType
    ) = SectionData(
            type = cardType,
            header = CardHeader(
                icon = getIcon(task),
                title = getTitle(task),
                description = getActiveCardDescription(task)
            ),
            description = getTaskTitle(task),
            onCardClick = getDefaultCardClick(task),
            buttonPrimary = buildScheduleButton("Agendar", cardType, task),
            status = getStatus(task),
            mainAction = getDefaultMainAction(task),
            accessoryImage = getAccessoryImage(task),
            alignment = getImageAlignment(),
            backgroundColor = getBackgroundColor(task),
        )
    private fun getActiveCardDescription(
        task: ReferralNew
    ) = if (hasManySessionsAvailable(task)) {
        "Consulta 1 - Agendar até **${ActionPlanTaskScheduleTimelineUtils.formatDeadline(task.deadline?.date)}**"
    } else {
        getDescription(task)
    }

    private fun getDefaultCard(task: ReferralNew) = SectionData(
        header = CardHeader(
            icon = getIcon(task),
            title = getTitle(task),
            description = getDescription(task),
        ),
        description = getTaskTitle(task),
        onCardClick = getDefaultCardClick(task),
        status = getStatus(task),
        mainAction = getDefaultMainAction(task),
        accessoryImage = getAccessoryImage(task),
        alignment = getImageAlignment(),
        backgroundColor = getBackgroundColor(task),
    )

    private fun hasManySessionsAvailable(task: ReferralNew) =
        task.sessionsQuantity != null && task.sessionsQuantity!! > 1

    private suspend fun buildCardDataForMultipleSessions(task: ReferralNew, cardType: CardType): SectionData {

        val appointmentsLinkedToTask = appointmentScheduleService.findBy(
            AppointmentScheduleFilter(
                healthPlanTaskId = task.id,
                status = listOf(AppointmentScheduleStatus.SCHEDULED),
                sortOrder = SortOrder.Ascending
            )
        ).getOrNullIfNotFound()

        val multipleSessionsInfo = getMultipleSessionsInfo(task, appointmentsLinkedToTask)
        val buttonText = if (multipleSessionsInfo.hasSomeScheduleDate()) "Agendar próxima consulta" else "Agendar"

        return SectionData(
            type = if (multipleSessionsInfo.scheduledDate == null) cardType else checkScheduleCardType(cardType),
            header = CardHeader(
                icon = getIcon(task),
                title = getTitle(task),
                description = multipleSessionsInfo.description ?: getDescription(task),
                scheduledDate = multipleSessionsInfo.scheduledDate
            ),
            description = getTaskTitle(task),
            onCardClick = getDefaultCardClick(task),
            buttonPrimary = if (multipleSessionsInfo.allScheduledOrCompleted)
                getDefaultPrimaryButton("Visualizar", cardType, listOf(task))
            else buildScheduleButton(buttonText, cardType, task),
            status = getStatus(task),
            mainAction = getDefaultMainAction(task),
            accessoryImage = getAccessoryImage(task),
            alignment = getImageAlignment(),
            backgroundColor = getBackgroundColor(task),
        )
    }

    private suspend fun getMultipleSessionsInfo(
        task: ReferralNew,
        appointmentsLinkedToTask: List<AppointmentSchedule>?
    ): MultipleSessionInfo {

        return if (appointmentsLinkedToTask.isNullOrEmpty()) {
            val dateFormatted = ActionPlanTaskScheduleTimelineUtils.formatDeadline(task.deadline?.date)
            MultipleSessionInfo("Consulta 1 - Agendar até **${dateFormatted}**")
        } else {
            val appointmentsCompleted = appointmentsLinkedToTask.filter { appointment -> appointment.isCompleted }

            if (appointmentsCompleted.size >= task.sessionsQuantity!!) {
                MultipleSessionInfo(
                    description = "Todas as consultas foram realizadas",
                    allScheduledOrCompleted = appointmentsLinkedToTask.size >= task.sessionsQuantity!!
                )
            } else {
                getCloserSessionScheduled(task, appointmentsLinkedToTask, appointmentsCompleted)
            }
        }
    }

    private suspend fun getCloserSessionScheduled(
        task: ReferralNew,
        appointmentsLinkedToTask: List<AppointmentSchedule>,
        appointmentsCompleted: List<AppointmentSchedule>
    ): MultipleSessionInfo {
        return appointmentScheduleService.getCloserScheduleByHealthPlanTask(task.id).fold(
            {
                val formattedDate = ActionPlanTaskScheduleTimelineUtils.getScheduledFormattedDateTitle(it)

                val currentIndex = appointmentsCompleted.size + 1
                MultipleSessionInfo(
                    description = "Consulta ${currentIndex}\n**$formattedDate**",
                    scheduledDate = it.startTime.toSaoPauloTimeZone(),
                    allScheduledOrCompleted = appointmentsLinkedToTask.size >= task.sessionsQuantity!!)
            },
            {
                logger.error(
                    "ReferralCardListConverter getting schedule info for multiple sessions but having generic error, returning null",
                    "task_id" to task.id,
                    it
                )
                MultipleSessionInfo(getDescription(task))
            }
        )
    }

    private fun buildCardDataForUniqueSessions(task: ReferralNew, cardType: CardType): SectionData {
        val formattedDescription = ActionPlanTaskScheduleTimelineUtils.getScheduledFormattedDateTitle(task.scheduledAt) ?: getDescription(task)
        return SectionData(
            type = checkScheduleCardType(cardType),
            header = CardHeader(
                icon = getIcon(task),
                title = formattedDescription,
                scheduledDate = task.scheduledAt?.toSaoPauloTimeZone()
            ),
            description = getTaskTitle(task),
            onCardClick = getDefaultCardClick(task),
            status = getStatus(task),
            mainAction = getDefaultMainAction(task),
            accessoryImage = getAccessoryImage(task),
            alignment = getImageAlignment(),
            backgroundColor = getBackgroundColor(task),
            buttonPrimary = getDefaultPrimaryButton("Visualizar", cardType, listOf(task))
        )
    }

    private fun getTaskTitle(task: ReferralNew) =
        "Encaminhamento para ${task.title} ${task.suggestedSpecialist?.name ?: ""}"
}

data class MultipleSessionInfo(
    val description: String? = null,
    val scheduledDate: LocalDateTime? = null,
    val allScheduledOrCompleted: Boolean = false,
) {
    fun hasSomeScheduleDate() = this.scheduledDate != null
}
