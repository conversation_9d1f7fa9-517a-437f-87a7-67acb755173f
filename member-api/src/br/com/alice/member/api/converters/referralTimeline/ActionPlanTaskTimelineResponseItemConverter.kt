package br.com.alice.member.api.converters.referralTimeline

import br.com.alice.action.plan.model.timeline.ActionPlanTaskTimelineItem
import br.com.alice.action.plan.model.timeline.ActionPlanTaskTimelineStatus
import br.com.alice.data.layer.models.AppointmentCoordination
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.member.api.converters.ActionPlanTaskScheduleTimelineUtils

object ActionPlanTaskTimelineResponseItemConverter {

    fun convertCompletedItem(completedAppointment: AppointmentCoordination, index: Int): ActionPlanTaskTimelineItem =
        ActionPlanTaskTimelineItem(
            title = "Consulta ${index + 1}",
            description = "**${ActionPlanTaskScheduleTimelineUtils.getScheduledFormattedDateTitle(completedAppointment.createdAt)}**",
            date = completedAppointment.createdAt,
            status = ActionPlanTaskTimelineStatus.DONE,
            icon = ActionPlanTaskTimelineStatus.DONE.iconUrl
        )
    fun convertScheduleItem(scheduledAppointment: AppointmentSchedule, index: Int) : ActionPlanTaskTimelineItem =
        ActionPlanTaskTimelineItem(
            title = ActionPlanTaskScheduleTimelineUtils.getScheduledFormattedDateTitle(scheduledAppointment),
            description = "Consulta ${index + 1} - ${ActionPlanTaskTimelineStatus.SCHEDULED.description}",
            date = scheduledAppointment.startTime,
            status = ActionPlanTaskTimelineStatus.SCHEDULED,
            icon = ActionPlanTaskTimelineStatus.SCHEDULED.iconUrl
        )

    fun convertBlockedItem(index: Int, sessionsScheduledOrCompleted: Int) =
        ActionPlanTaskTimelineItem(
            title = "Consulta ${index + sessionsScheduledOrCompleted + 1}",
            description = "Agendamento bloqueado",
            status = ActionPlanTaskTimelineStatus.UNAVAILABLE,
            icon = ActionPlanTaskTimelineStatus.UNAVAILABLE.iconUrl
        )

    fun convertAvailableItem(sessionsScheduledOrCompleted: Int) =
        ActionPlanTaskTimelineItem(
            title = "Consulta ${sessionsScheduledOrCompleted + 1}",
            description = "Agendamento disponível",
            status = ActionPlanTaskTimelineStatus.AVAILABLE,
            icon = ActionPlanTaskTimelineStatus.AVAILABLE.iconUrl
        )
}
