package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HealthCommunitySpecialist
import br.com.alice.member.api.models.SpecialistReferralResponse

// TODO: delete when all active scheduling tasks have sub specialty
object SpecialistReferralResponseConverter
    : Converter<HealthCommunitySpecialist, SpecialistReferralResponse>(
    HealthCommunitySpecialist::class,
    SpecialistReferralResponse::class
) {
    fun convert(
        source: HealthCommunitySpecialist,
        specialty: String,
        subSpecialities: List<String>,
        appointmentScheduleOptionUrl: String? = null,
    ): SpecialistReferralResponse {
        return SpecialistReferralResponse(
            title = "Para agendar, basta entrar em contato direto nos telefones",
            specialist = HealthCommunitySpecialistConverter.convert(source, specialty, subSpecialities),
            schedulingUrl = appointmentScheduleOptionUrl,
        )
    }
}
