package br.com.alice.member.api.converters.address

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Address
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.common.googlemaps.services.Address as CoverageAddress

object MapsAddressConverter : Converter<CoverageAddress, Address>(
    CoverageAddress::class, Address::class
) {

    fun convert(
        source: CoverageAddress,
        requestAddress: UserAddressResponse
    ): Address = super.convert(
        source,
        map(Address::number) from source.number.ifEmpty { requestAddress.number },
        map(Address::postalCode) from source.postalCode.ifEmpty { requestAddress.postalCode },
        map(Address::complement) from requestAddress.complement
    )
}
