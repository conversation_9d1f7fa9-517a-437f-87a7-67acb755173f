package br.com.alice.member.api.clients

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.member.api.ServiceConfig
import com.github.kittinunf.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.call.body
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.forms.submitForm
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.http.HttpStatusCode
import io.ktor.http.parameters
import java.util.Base64

val recommendedClientConfig: HttpClientConfig<*>.() -> Unit = {
    install(ContentNegotiation) { gsonSnakeCase() }
    install(HttpRequestRetry) {
        exponentialDelay(maxDelayMs = ServiceConfig.Labi.Retry.maxDelayMsOnRetry)
        retryOnExceptionOrServerErrors(ServiceConfig.Labi.Retry.maxRetries)
        retryOnException(ServiceConfig.Labi.Retry.maxRetries)
    }
}

data class LabiAuthResponse(
    val accessToken: String,
    val expiresIn: Int,
    val tokenType: String
)

class LabiClient(private val client: HttpClient, private val cache: GenericCache) {

    internal var token: String? = null
    internal var tokenExpiresAt: Long? = null

    suspend fun checkLocationAvailability(zipcode: String): Result<Boolean, Throwable> =
        coResultOf {
            cache.get(
                key = "${CACHE_KEY}-$zipcode",
                type = Boolean::class,
                expirationTime = ServiceConfig.Labi.cacheExpirationTimeInMinutes * 60L
            ) {
                try {
                    val endpoint = ServiceConfig.Labi.baseUrl + ServiceConfig.Labi.Endpoints.locationAvailability
                    client.get(urlString = endpoint) {
                        parameter("zipcode", zipcode)
                        withAuth()
                    }
                    true
                } catch (e: ResponseException) {
                    if (e.response.status == HttpStatusCode.NotFound) {
                        false
                    } else throw e
                }
            }
        }

    private suspend fun authenticate() {
        val endpoint = ServiceConfig.Labi.authUrl + ServiceConfig.Labi.Endpoints.getAuthToken
        val username = ServiceConfig.Labi.username
        val password = ServiceConfig.Labi.password
        val encodedBasicAuth = Base64.getEncoder().encodeToString("$username:$password".toByteArray())

        val parameters = parameters {
            append("grant_type", "client_credentials")
            append("scope", "home-collection/zipcode")
        }

        val labiAuthResponse = client.submitForm(url = endpoint, formParameters = parameters) {
            headers.append("Authorization", "Basic $encodedBasicAuth")
        }.body<LabiAuthResponse>()

        setTokenInformation(labiAuthResponse.accessToken, labiAuthResponse.expiresIn)
    }

    private fun setTokenInformation(token: String, expiresIn: Int) {
        this.token = token
        this.tokenExpiresAt =
            System.currentTimeMillis() + (expiresIn * MS_MULTIPLIER) - ServiceConfig.Labi.tokenRefreshBuffer
    }

    private suspend fun HttpRequestBuilder.withAuth() {
        val needAuthenticate = token.isNullOrEmpty() || tokenExpiresAt?.let { it < System.currentTimeMillis() } == true

        takeIf { needAuthenticate }
            ?.let { authenticate() }

        headers.append("Authorization", "Bearer $token")
    }

    private companion object {
        const val MS_MULTIPLIER = 1000
        const val CACHE_KEY = "labi-check-availability"
    }
}
