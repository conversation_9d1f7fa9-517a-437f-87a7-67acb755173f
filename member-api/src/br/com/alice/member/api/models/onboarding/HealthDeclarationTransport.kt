package br.com.alice.member.api.models.onboarding

import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.models.HealthDeclarationQuestionType
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.Link
import br.com.alice.membership.model.onboarding.GroupedAnswerOption
import br.com.alice.membership.model.onboarding.HealthDeclarationAnswerOption
import br.com.alice.membership.model.onboarding.HealthDeclarationGroupedQuestion
import br.com.alice.membership.model.onboarding.HealthDeclarationQuestion

data class ImcAnswerRequest(
    val height: Double,
    val weight: Double
)

data class ConditionPayload(
    val answers: List<ConditionAnswerRequest>
)

data class ConditionAnswerRequest(
    val value: String
)

data class SurgeryPayload(
    val answers: List<SurgeryAnswerRequest>
)

data class SurgeryAnswerRequest(
    val condition: String,
    val value: String
)

enum class FormInputType {
    IMC, MULTIPLE_OPTIONS, MATRIX, FREE_TEXT
}

open class FormInput(
    val type: FormInputType,
    val action: Link
)

data class AnswerOption(
    val label: String,
    val value: String = label,
    val tooltip: String? = null,
    val exclusive: Boolean = false,
    val subQuestion: HealthDeclarationFormQuestion? = null
)

class MultipleOptionsInput(
    val options: List<AnswerOption>,
    val allowOther: Boolean = true,
    val otherPlaceholder: String? = null,
    action: Link
) : FormInput(
    type = FormInputType.MULTIPLE_OPTIONS,
    action = action
)

class MatrixInput(
    val groups: List<GroupedAnswerOption>,
    action: Link
) : FormInput(
    type = FormInputType.MATRIX,
    action = action
)

class FreeTextInput(
    val config: FreeTextConfiguration,
    action: Link
) : FormInput(
    type = FormInputType.FREE_TEXT,
    action = action
)

open class HealthDeclarationFormQuestion(
    val id: HealthDeclarationQuestionType,
    val required: Boolean = true,
    val question: String,
    val progress: Int = 0,
    val input: FormInput,
    val imageUrl: String = MobileApp.Assets.HealthDeclaration.get(id)
)

class ImcFormQuestion(imcQuestion: HealthDeclarationQuestion, personId: String) : HealthDeclarationFormQuestion(
    id = imcQuestion.type,
    question = imcQuestion.question,
    progress = imcQuestion.progress,
    input = FormInput(
        type = FormInputType.IMC,
        action = Link(href = ServiceConfig.url("/onboarding/$personId/health_declaration/answers/imc"))
    )
)

class ConditionFormQuestion(question: HealthDeclarationQuestion, personId: String) : HealthDeclarationFormQuestion(
    id = question.type,
    question = question.question,
    progress = question.progress,
    input = MultipleOptionsInput(
        options = question.answerOptions.map { it.toAnswerOption(personId) }.shuffled(),
        allowOther = question.allowOther,
        otherPlaceholder = question.otherPlaceholder,
        action = Link(
            href = ServiceConfig.url("/onboarding/$personId/health_declaration/answers/${question.type}"),
            rel = question.type.toString()
        )
    )
)

fun HealthDeclarationAnswerOption.toAnswerOption(personId: String) =
    AnswerOption(label, value, tooltip, exclusive, subQuestion?.let { ConditionFormQuestion(it, personId) })

class SurgeryFormQuestionLegacy(question: HealthDeclarationGroupedQuestion, personId: String) :
    HealthDeclarationFormQuestion(
        id = question.type,
        question = question.question,
        progress = question.progress,
        input = MatrixInput(
            groups = question.groupedAnswerOptions,
            action = Link(href = ServiceConfig.url("/onboarding/$personId/health_declaration/answers/surgeries"))
        )
    )

class SurgeryFormQuestion(question: HealthDeclarationQuestion, personId: String) : HealthDeclarationFormQuestion(
    id = question.type,
    question = question.question,
    progress = question.progress,
    input = MultipleOptionsInput(
        options = question.answerOptions.map { option ->
            AnswerOption(
                option.label,
                option.value,
                option.tooltip,
                option.exclusive,
                option.subQuestion?.let {
                    HealthDeclarationFormQuestion(
                        id = it.type,
                        question = it.question,
                        required = false,
                        progress = 100,
                        input = FreeTextInput(
                            config = FreeTextConfiguration(keyboardType = FreeTextKeyboardType.ALPHANUMERIC),
                            action = Link(href = ServiceConfig.url("/onboarding/$personId/health_declaration/answers/${question.type}"))
                        )
                    )
                })
        },
        allowOther = question.allowOther,
        action = Link(
            href = ServiceConfig.url("/onboarding/$personId/health_declaration/answers/${question.type}"),
            rel = question.type.toString()
        )
    )
)

class ConfirmationFormQuestion(question: HealthDeclarationQuestion, personId: String) : HealthDeclarationFormQuestion(
    id = question.type,
    question = question.question,
    required = false,
    progress = 100,
    input = FreeTextInput(
        config = FreeTextConfiguration(keyboardType = FreeTextKeyboardType.ALPHANUMERIC),
        action = Link(href = ServiceConfig.url("/onboarding/$personId/health_declaration/confirm"))
    )
)

data class AnswerResponse(
    val type: HealthDeclarationQuestionType?,
    val question: String,
    val answer: String
)

data class ConditionResponse(
    val condition: String
)

data class SurgeryResponse(
    val condition: String
)

class AnswersSummary(healthDeclaration: HealthDeclaration) {
    val answers = healthDeclaration.answers.map { AnswerResponse(it.questionType, it.question, it.answer) }
    val conditions = healthDeclaration.cpts.map { ConditionResponse(it.condition) }
    val surgeries = healthDeclaration.surgeries.map { SurgeryResponse(it.condition) }
}
