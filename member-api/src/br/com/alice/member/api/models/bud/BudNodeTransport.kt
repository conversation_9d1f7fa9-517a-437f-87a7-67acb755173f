package br.com.alice.member.api.models.bud

import java.util.UUID

data class FinishTriageRequest(
    val personId: UUID? = null,
    val content: String? = null,
    val kind: String? = null,
    val category: String? = null,
    val subCategory: String? = null,
    val tags: String? = null
)

data class NavigateTriageRequest(
    val selectedPills: String? = null,
    val clickedOption: String? = null,
    val selectedDate: String? = null,
    val inputText: String? = null,
    val closeButtonPressed: Boolean? = null,
    val chatButtonPressed: Boolean? = null,
    val backButtonPressed: Boolean? = null,
    val screenId: UUID? = null,
    val screeningNavigationId: UUID? = null
)
