package br.com.alice.member.api.models.accreditedNetwork

import br.com.alice.app.content.model.Tag
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.appContent.Navigation

data class FavoriteInfoTransport(
    val isFavorite: Boolean,
    val action: Navigation,
)

data class ServiceLocation(
    val title: String,
    val details: String,
    val tag: Tag? = null,
    val address: String? = null,
    val serviceDays: List<String> = emptyList(),
    val serviceDaysOptions: List<String> = emptyList(),
    val nextDateAvailable: String? = null,
    val scheduleNavigation: ScheduleNavigation? = null,
    val contacts: List<ServiceContact> = emptyList(),
) {
    data class ScheduleNavigation(
        val mobileRoute: MobileRouting,
        val properties: Map<String, Any>,
    )

    data class ServiceContact(
        val type: ServiceContactType,
        val text: String,
        val url: String?,
    )

    enum class ServiceContactType {
        PHONE, MOBILE, WHATSAPP, WEBSITE, SHARE_BUTTON,
    }
}

data class AccreditedNetworkDetailsSection(
    val title: String,
    val content: List<Content>,
    val icon: String? = null,
    val isExpandable: Boolean = true,
) {
    data class Content(
        val subTitle: String? = null,
        val text: String,
        val icon: String? = null,
    )
}

data class AccreditedNetworkAction(
    val type: AccreditedNetworkActionType,
    val label: String,
    val icon: String? = null,
    val navigation: NavigationResponse? = null,
)

enum class AccreditedNetworkActionType {
    SHARE_BUTTON,
}
