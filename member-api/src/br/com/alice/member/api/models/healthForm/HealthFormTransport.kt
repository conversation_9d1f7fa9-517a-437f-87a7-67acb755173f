package br.com.alice.member.api.models.healthForm

import br.com.alice.data.layer.models.BackCover
import br.com.alice.data.layer.models.HealthFormQuestionOption
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.member.api.models.Link
import java.util.UUID

data class HealthFormQuestionResponse(
    val id: UUID,
    val question: String,
    val details: String?,
    val progress: Int,
    val progressIndex: HealthFormQuestionProgressIndexesResponse? = null,
    val input: HealthFormQuestionInputResponse,
    val section: HealthFormSectionResponse? = null,
)


data class HealthFormQuestionProgressIndexesResponse(
    val current: Int,
    val total: Int,
)

data class HealthFormQuestionInputResponse(
    val action: Link,
    val displayAttributes: Map<String, Any>?,
    val imageUrl: String? = null,
    val options: List<HealthFormQuestionOption>,
    val type: HealthFormQuestionType,
    val validations: HealthFormQuestionValidationResponse? = null,
)

data class AnswerRequest(
    val next: Int?,
    val value: String
)

data class HealthFormSectionResponse(
    val title: String,
    val details: String? = null,
    val imageUrl: String? = null,
    val nextButtonText: String? = null,
    val backCover: BackCover? = null
)

data class HealthFormQuestionValidationResponse(
    val range: HealthFormQuestionValidationRangeResponse? = null,
    val stepSize: Double? = null,
)

data class HealthFormQuestionValidationRangeResponse(
    val begin: Double? = null,
    val end: Double? = null,
)
