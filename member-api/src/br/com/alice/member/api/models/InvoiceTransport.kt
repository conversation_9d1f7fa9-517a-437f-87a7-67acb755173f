package br.com.alice.member.api.models

import br.com.alice.common.core.extensions.isPositive
import br.com.alice.common.core.extensions.orZero
import java.math.BigDecimal
import java.util.UUID

data class InvoiceResponse(
    val id: UUID,
    val boletoUrl: String? = null,
    val barcode: String? = null,
    val status: InvoiceStatusResponse,
    val referenceDate: String,
    val dueDate: String,
    val totalAmount: BigDecimal,
    val breakdown: List<InvoiceBreakdownItemResponse>? = null,
    val paymentInfo: PaymentInfoResponse? = null,
    val paymentOptions: List<PaymentOptionResponse>? = null,
    val canGenerateSecondCopy: Boolean = false,
)

data class InvoiceBreakdownItemResponse(
    val title: String,
    val amount: BigDecimal,
    val info: NavigationResponse? = null
) {
    val type = if (amount.orZero().isPositive()) BreakdownItemType.ADDITION else BreakdownItemType.DISCOUNT
}

data class CTA(
    val label: String,
    val icon: String,
)

data class PaymentOptionResponse(
    val id: String,
    val header: String,
    val actionCta: CTA? = null,
    val copyCta: CTA? = null,
    val code: String? = null,
    val documentUrl: String? = null,
)

data class PaymentInfoResponse(
    val id: String,
    val title: String,
    val code: String? = null,
    val paymentUrl: String? = null,
    val documentUrl: String? = null,
)

enum class BreakdownItemType {
    ADDITION, DISCOUNT
}

enum class InvoiceStatusResponse {
    OPEN, PAID, OVERDUE, CANCELED, FAILED, CANCELED_BY_LIQUIDATION
}
