package br.com.alice.member.api.models

import java.util.UUID

data class ListFaqGroupResponse(val groupResponse: List<FaqGroupResponse>?)

data class FaqGroupResponse(
    val title: String,
    val description: String?,
    val featured: Boolean,
    val contents: List<FaqContentResponse>,
)

data class FaqContentResponse(
    val id: UUID,
    val title: String,
    val imageUrl: String?,
    val description: String?
)

data class FaqFeedbackRequest(
    val faqContentId: UUID,
    val useful: Boolean,
    val anonymous: Boolean
)

data class FaqFeedbackResponse(
    val id: UUID,
    val faqContentId: UUID,
    val useful: Boolean,
    val feedback: String? = null,
    val showUserInput: Boolean,
    val feedbackMessage: String?,
)

data class FaqFeedbackTextRequest(
    val feedback: String
)
