package br.com.alice.member.api.models.onboarding

open class RegistrationChatInputConfiguration

enum class FreeTextKeyboardType {
    NUMERIC,
    ALPHANUMERIC,
    PHONE,
    EMAIL
}

enum class CapitalizationType {
    NONE,
    WORDS,
    SENTENCES,
    ALL_CHARACTERS
}

data class FreeTextConfiguration(
    val keyboardType: FreeTextKeyboardType,
    val mask: String? = null,
    val capitalizationType: CapitalizationType? = null,
    val placeholderText: String? = null,
    val hint: String? = null,
    val freeTextLabel: String? = null,
) :
    RegistrationChatInputConfiguration()

data class OptionButton(val label: String, val value: String, val primary: Boolean)
data class OptionButtonsConfiguration(val options: List<OptionButton>) :
    RegistrationChatInputConfiguration()

class WebviewConfiguration(val url: String, val options: List<OptionButton>) : RegistrationChatInputConfiguration()
