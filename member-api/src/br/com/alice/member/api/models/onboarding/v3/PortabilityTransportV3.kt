package br.com.alice.member.api.models.onboarding.v3

import br.com.alice.data.layer.models.InsurancePortabilityMissingDocumentFile
import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.data.layer.models.InsurancePortabilityRequestStep
import br.com.alice.data.layer.models.InsurancePortabilityRequestType
import br.com.alice.member.api.models.NavigationResponse
import java.util.UUID

data class PortabilityResponse(
    val id: UUID,
    val status: InsurancePortabilityRequestStatus,
    val type: InsurancePortabilityRequestType,
    val declinedReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val missingDocuments: List<InsurancePortabilityMissingDocumentFile>? = emptyList(),
    val notes: String? = null,
)

data class PortabilityStepRequest(
    val value: String? = null,
)

data class PortabilityFinishRequest(
    val value: String? = null,
)

enum class PortabilityFinishOptions {
    NEXT,
    UPLOAD,
}

data class PortabilityStepResponse(
    val current: InsurancePortabilityRequestStep,
    val title: String,
    val detail: String? = null,
    val more: PortabilityStepMoreInfoResponse? = null,
    val input: PortabilityStepInput,
    val navigation: NavigationResponse? = null
)

data class PortabilityStepMoreInfoResponse(
    val hint: String,
    val title: String,
    val description: String,
)

data class PortabilityStepInput(
    val type: PortabilityStepInputType,
    val action: PortabilityStepInputAction,
    val options: List<PortabilityStepInputOption> = emptyList()
)

data class PortabilityStepInputOption(
    val title: String,
    val detail: String? = null,
    val value: String,
    val exclusive: Boolean = false,
)

data class PortabilityStepInputAction(
    val previous: PortabilityStepInputActionHref? = null,
    val next: PortabilityStepInputActionHref? = null,
    val navigationResponse: NavigationResponse? = null,
)

data class PortabilityStepInputActionHref(
    val href: String
)

enum class PortabilityStepInputType {
    OPTION_BUTTONS,
    ACKNOWLEDGE,
}
