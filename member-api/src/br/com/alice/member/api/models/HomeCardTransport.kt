package br.com.alice.member.api.models

import br.com.alice.common.featureaccess.features.ServiceCard

data class HomeCard(
    val welcome: WelcomeCardContent? = null,
    val healthPlan: HealthPlanResponse? = null,
    val scheduledAppointment: ScheduledAppointmentCardContent? = null
)

data class WelcomeCardContent(
    val title: String,
    val text: String,
    val navigation: NavigationResponse
)

data class ScheduledAppointmentCardContent(
    val title: String,
    val scheduledAppointment: ScheduledAppointmentResponse,
    val healthcareTeam: HealthcareTeamResponse,
    val navigation: NavigationResponse
)

data class ServiceResponse(
    val navigation: NavigationResponse,
    val card: ServiceCard,
)

data class ServicesResponse(
    val title: String? = null,
    val services: List<ServiceResponse>? = null,
)
