package br.com.alice.member.api.routes

import br.com.alice.common.coHandlerLimited
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.ReminderController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get

fun Route.reminderRoutes() {
    authenticate {
        val reminderController by inject<ReminderController>()
        get("/reminders") { coHandlerLimited(reminderController::listReminders) }
    }
}
