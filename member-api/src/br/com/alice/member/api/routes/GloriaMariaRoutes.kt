package br.com.alice.member.api.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.GloriaMariaController
import br.com.alice.member.api.controllers.LeagueMigrationController
import br.com.alice.member.api.controllers.LeanMigrationController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.route

fun Route.gloriaMariaRoutes() {
    authenticate {
        val leanMigration by inject<LeanMigrationController>()
        val leagueMigration by inject<LeagueMigrationController>()
        val gloriaMaria by inject<GloriaMariaController>()

        route("/gloria-maria/league-migration/") {
            get("v1/playlist") { coHandler(leanMigration::playlist) }
        }

        route("/gloria-maria/lean-migration-october-2023") {
            get("/v1/playlist") { coHandler(leanMigration::playlist) }
        }

        route("/gloria-maria/league-migration-september-2022") {
            get("/v1/playlist") { coHandler(leagueMigration::playlist) }
        }

        route("/gloria-maria/v2") {
            get("playlist/{id}") { coHandler("id", gloriaMaria::getPlaylistById) }
        }
    }
}
