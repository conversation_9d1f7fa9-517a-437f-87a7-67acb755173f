package br.com.alice.member.api.routes

import br.com.alice.common.coHandler
import br.com.alice.common.coHandlerLimited
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.AppointmentScheduleContentController
import br.com.alice.member.api.controllers.AppointmentScheduleController
import br.com.alice.member.api.controllers.AppointmentScheduleEventTypeController
import br.com.alice.member.api.controllers.AppointmentScheduleOptionController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.appointmentScheduleRoutes() {
    val appointmentScheduleController by inject<AppointmentScheduleController>()
    val appointmentScheduleContentController by inject<AppointmentScheduleContentController>()
    val appointmentScheduleEventTypeController by inject<AppointmentScheduleEventTypeController>()

    authenticate {
        route("/appointment_schedules") {
            get("/schedule/navigation") { coHandler(appointmentScheduleContentController::getScheduleNavigation) }
            get("/on_site/{appointmentScheduleEventTypeId}/providers") {
                coHandler(
                    "appointmentScheduleEventTypeId", appointmentScheduleContentController::getScheduleOnSiteProviders
                )
            }

            get { coHandlerLimited(appointmentScheduleController::getNextAppointmentSchedules) }
            post { coHandlerLimited(appointmentScheduleController::createAppointmentSchedule) }

            get("/{appointmentScheduleId}") {
                coHandlerLimited(
                    "appointmentScheduleId", appointmentScheduleController::getById
                )
            }

            get("/{appointmentScheduleId}/details") {
                coHandlerLimited(
                    "appointmentScheduleId", appointmentScheduleController::getDetails
                )
            }

            post("/{appointmentScheduleId}/cancel") {
                coHandlerLimited(
                    "appointmentScheduleId", appointmentScheduleController::cancelById
                )
            }

            post("/{appointmentScheduleId}/reschedule") {
                coHandlerLimited(
                    "appointmentScheduleId", appointmentScheduleController::rescheduleAppointmentSchedule
                )
            }

            post("/{appointmentScheduleId}/check_in") {
                coHandlerLimited(
                    "appointmentScheduleId", appointmentScheduleController::checkInAppointmentSchedule
                )
            }

            get("/list_scheduled_by_person_and_type/") {
                coHandler(appointmentScheduleController::listScheduledByPersonAndType)
            }
            get("/has_colliding_schedules/") {
                coHandler(appointmentScheduleController::hasCollidingSchedules)
            }

            get("/check_first/{appointmentScheduleEventTypeId}") {
                coHandler(
                    "appointmentScheduleEventTypeId",
                    appointmentScheduleController::getFirstAppointmentScheduleEventIfAvailable
                )
            }
        }

        get("/appointment_schedule_event_type_availability/{appointmentScheduleEventTypeId}") {
            coHandler(
                "appointmentScheduleEventTypeId",
                appointmentScheduleController::getAvailabilityForAppointmentScheduleEventTypeForPeriod
            )
        }

        route("/appointment_schedule_event_type") {
            get("/{appointmentScheduleEventTypeId}") {
                coHandler(
                    "appointmentScheduleEventTypeId",
                    appointmentScheduleEventTypeController::getByIdAndProviderUnit
                )
            }

            get("/{appointmentScheduleEventTypeId}/staffs_related_to_conditions") {
                coHandler(
                    "appointmentScheduleEventTypeId",
                    appointmentScheduleEventTypeController::getStaffsRelatedToActiveConditions
                )
            }

            get("/setup") { coHandler(appointmentScheduleEventTypeController::webviewSetup) }
            get("/provider_units") { coHandler(appointmentScheduleEventTypeController::getEventProviderUnits) }
        }


        route("/appointment_schedule_options") {
            val appointmentScheduleOptionController by inject<AppointmentScheduleOptionController>()

            get { coHandler(appointmentScheduleOptionController::getAppointmentScheduleOptions) }
            get("/type/{type}") {
                coHandler(
                    "type",
                    appointmentScheduleOptionController::getAppointmentScheduleOptionsByType
                )
            }
        }

        route("/staff_availability/{staffId}") {
            get {
                coHandler(
                    "staffId",
                    appointmentScheduleController::getStaffAvailabilityForStaffForPeriod
                )
            }
            get("/advanced_access") {
                coHandler(
                    "staffId",
                    appointmentScheduleController::getAdvancedAccessAvailability
                )
            }
        }
    }
}
