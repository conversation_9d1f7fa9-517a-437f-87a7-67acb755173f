package br.com.alice.member.api.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.member.api.controllers.AppStateController
import br.com.alice.member.api.controllers.device.DeviceController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.deviceRoutes() {
    val deviceController by inject<DeviceController>()
    val appStateController by inject<AppStateController>()

    route("/clear_app_state_by_person") {
        post("") { coHandler(appStateController::clearAppStateByPersonId) }
    }

    authenticate {
        route("/device") {
            get("/") { coHandler(deviceController::getDeviceAppVersion) }
        }
    }
}
