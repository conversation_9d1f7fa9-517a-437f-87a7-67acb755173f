package br.com.alice.member.api.builders.provider.details

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CalloutType
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.common.Brand
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.data.layer.models.Referral
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType
import br.com.alice.member.api.models.accreditedNetwork.ProviderDetailsTransport
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class AliceClinicalProviderUnitDetailsBuilderTest {

    private val appVersion = SemanticVersion("4.0.0")
    private val product = TestModelFactory.buildProduct()
    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember(personId = person.id, product = product)
    private val address = TestModelFactory.buildStructuredAddress()
    private val providerUnit = TestModelFactory.buildProviderUnit()

    @Test
    fun `Should return correct ProviderDetailsTransport`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(isTherapy = true)
        val referrals = emptyList<Referral>()

        val expected = ProviderDetailsTransport(
            id = providerUnit.id,
            type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
            title = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY.title,
            name = providerUnit.name,
            header = ProviderDetailsTransport.Header(
                name = providerUnit.name,
                providerImageUrl = providerUnit.imageUrl,
            ),
            disclaimer = ContactCallOutResponse(
                title = "É necessário encaminhamento",
                body = "Para agendar um atendimento você precisa de indicação médica.",
                variant = ContactCallOutResponse.Variant.INFORMATION,
                action = ContactCallOutResponse.Action(
                    type = CalloutType.NAVIGATION,
                    label = "Falar com Alice Agora",
                    onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
                )
            ),
            providerImageUrl = providerUnit.imageUrl,
            addresses = listOf(
                ProviderDetailsTransport.Address(
                    label = "Endereço",
                    address = address.formattedAddress(),
                    distance = null
                ),
            ),
            phoneNumbers = listOf(
                ProviderDetailsTransport.PhoneNumber(
                    label = "Telefone 1",
                    phoneNumber = "(011) 99999-9999",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                ),
                ProviderDetailsTransport.PhoneNumber(
                    label = "Secundário",
                    phoneNumber = "(011) 98888-8888",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                )
            ),
            specialty = specialty.name,
            cardDescription = "Para atendimento, use a carteirinha Alice:" ,
            membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            ),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = providerUnit.id,
                specialtyIds = listOf(specialty.id),
                referenceType = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                isFavorite = false,
                appVersion = appVersion,
            )
        )

        val result = AliceClinicalProviderUnitDetailsBuilder.build(
            AliceClinicalProviderUnitDetailsBuilder.Params(
                addresses = listOf(address),
                providerUnit = providerUnit,
                specialty = specialty,
                person = person,
                member = member,
                product = product,
                referrals = referrals,
                favorite = TestModelFactory.buildAccreditedNetworkFavorite(),
                appVersion = appVersion,
                showCalloutToDuquesaMember = false
            )
        )

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `Should return correct ProviderDetailsTransport when member product is Duquesa`() {
        val product = TestModelFactory.buildProduct(brand = Brand.DUQUESA)
        val specialty = TestModelFactory.buildMedicalSpecialty(isTherapy = true)
        val referrals = emptyList<Referral>()

        val expected = ProviderDetailsTransport(
            id = providerUnit.id,
            type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
            title = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY.title,
            name = providerUnit.name,
            header = ProviderDetailsTransport.Header(
                name = providerUnit.name,
                providerImageUrl = providerUnit.imageUrl,
            ),
            disclaimer = ContactCallOutResponse(
                title = "É necessário encaminhamento",
                body = "Para agendar um atendimento você precisa de indicação médica.",
                variant = ContactCallOutResponse.Variant.INFORMATION,
                action = ContactCallOutResponse.Action(
                    type = CalloutType.NAVIGATION,
                    label = "Falar com Alice Agora",
                    onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
                )
            ),
            providerImageUrl = providerUnit.imageUrl,
            addresses = listOf(
                ProviderDetailsTransport.Address(
                    label = "Endereço",
                    address = address.formattedAddress(),
                    distance = null
                ),
            ),
            phoneNumbers = listOf(
                ProviderDetailsTransport.PhoneNumber(
                    label = "Telefone 1",
                    phoneNumber = "(011) 99999-9999",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                ),
                ProviderDetailsTransport.PhoneNumber(
                    label = "Secundário",
                    phoneNumber = "(011) 98888-8888",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                )
            ),
            specialty = specialty.name,
            cardDescription = "Para atendimento, use a carteirinha Alice:" ,
            membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            ),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = providerUnit.id,
                specialtyIds = providerUnit.medicalSpecialtyIds!!,
                referenceType = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                isFavorite = false,
                appVersion = appVersion,
            )
        )

        val result = AliceClinicalProviderUnitDetailsBuilder.build(
            AliceClinicalProviderUnitDetailsBuilder.Params(
                addresses = listOf(address),
                providerUnit = providerUnit,
                specialty = specialty,
                person = person,
                member = member,
                product = product,
                referrals = referrals,
                favorite = null,
                appVersion = appVersion,
                showCalloutToDuquesaMember = true
            )
        )

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `Should return correct ProviderDetailsTransport when specialty is null`() {
        val specialty = null
        val referrals = emptyList<Referral>()

        val expected = ProviderDetailsTransport(
            id = providerUnit.id,
            type = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
            title = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY.title,
            name = providerUnit.name,
            header = ProviderDetailsTransport.Header(
                name = providerUnit.name,
                providerImageUrl = providerUnit.imageUrl,
            ),
            disclaimer = null,
            providerImageUrl = providerUnit.imageUrl,
            addresses = listOf(
                ProviderDetailsTransport.Address(
                    label = "Endereço",
                    address = address.formattedAddress(),
                    distance = null
                )
            ),
            phoneNumbers = listOf(
                ProviderDetailsTransport.PhoneNumber(
                    label = "Telefone 1",
                    phoneNumber = "(011) 99999-9999",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                ),
                ProviderDetailsTransport.PhoneNumber(
                    label = "Secundário",
                    phoneNumber = "(011) 98888-8888",
                    type = ProviderDetailsTransport.PhoneNumber.Type.PHONE,
                    phoneUrl = "tel:************"
                )
            ),
            specialty = "",
            cardDescription = "Para atendimento, use a carteirinha Alice:" ,
            membershipCard = MemberCardResponse(
                number = person.nationalId,
                type = MemberCardType.ALICE,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                ansNumber = "-",
            ),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = providerUnit.id,
                specialtyIds = providerUnit.medicalSpecialtyIds!!,
                referenceType = ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY,
                isFavorite = true,
                appVersion = appVersion,
            ),
        )

        val result = AliceClinicalProviderUnitDetailsBuilder.build(
            AliceClinicalProviderUnitDetailsBuilder.Params(
                addresses = listOf(address),
                providerUnit = providerUnit,
                specialty = specialty,
                person = person,
                member = member,
                product = product,
                referrals = referrals,
                favorite = TestModelFactory.buildAccreditedNetworkFavorite(),
                appVersion = appVersion,
                showCalloutToDuquesaMember = false
            )
        )

        assertThat(result).isEqualTo(expected)
    }


}
