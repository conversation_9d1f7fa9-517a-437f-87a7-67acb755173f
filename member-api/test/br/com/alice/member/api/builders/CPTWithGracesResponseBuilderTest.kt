package br.com.alice.member.api.builders

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.GracePeriodType
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.builders.CPTWithGracesResponseBuilder
import br.com.alice.member.api.models.CPTTransport
import br.com.alice.member.api.models.CPTWithGracesResponse
import br.com.alice.member.api.models.Grace
import br.com.alice.member.api.models.GraceTransport
import br.com.alice.member.api.models.ProcedureTransport
import br.com.alice.member.api.models.RemainingTime
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import kotlin.test.Test

class CPTWithGracesResponseBuilderTest {

    private val person = TestModelFactory.buildPerson()
    private val localDateTime = LocalDateTime.of(2023, Month.AUGUST, 30, 0, 0, 0)
    private val member = TestModelFactory.buildMember(person.id).copy(activationDate = localDateTime.minusDays(32L))
    private val activationDate = member.activationDate!!
    private val memberCpt = MemberCpt()

    @Test
    fun `#buildCPTWithGracesResponse - when member has no cpts and no graces, should return expected CPTWithGracesResponse`() =
        mockLocalDateTime(localDateTime) {
            val expected = CPTWithGracesResponse(
                cpt = null,
                grace = GraceTransport(activationDate.toBrazilianDateFormat(), emptyList())
            )

            val response = CPTWithGracesResponseBuilder.buildCPTWithGracesResponse(member, memberCpt)
            assertThat(response).isEqualTo(expected)
        }

    @Test
    fun `#buildCPTWithGracesResponse - when member has only graces, should return expected CPTWithGracesResponse`() =
        mockLocalDateTime(localDateTime) {
            val expected = CPTWithGracesResponse(
                cpt = null,
                grace = GraceTransport(
                    activationDate.toBrazilianDateFormat(), listOf(
                        Grace("grace 1", RemainingTime(730, 32, "DAYS")),
                        Grace("grace 2", RemainingTime(731, 334, "DAYS")),
                        Grace("grace 3", RemainingTime(730, 1064, "DAYS"))
                    )
                )
            )

            val response = CPTWithGracesResponseBuilder.buildCPTWithGracesResponse(
                member, 
                memberCpt.copy(
                    gracePeriod = listOf(
                        CptGracePeriod(
                            condition = "grace 1",
                            validUntil = member.activationDate!!.plusDays(64L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            type = GracePeriodType.ELECTIVE_SURGERY,
                            periodInDays = 0
                        ),
                        CptGracePeriod(
                            condition = "grace 2",
                            validUntil = member.activationDate!!.plusMonths(12L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            type = GracePeriodType.THERAPY,
                            periodInDays = 0
                        ),
                        CptGracePeriod(
                            condition = "grace 3",
                            validUntil = member.activationDate!!.plusMonths(36L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            type = GracePeriodType.PAC,
                            periodInDays = 0
                        )
                    )
                )
            )
            assertThat(response).isEqualTo(expected)
        }

    @Test
    fun `#buildCPTWithGracesResponse - when member has a overwritten default grace, should return expected CPTWithGracesResponse`() =
        mockLocalDateTime(localDateTime) {
            val expected = CPTWithGracesResponse(
                cpt = null,
                grace = GraceTransport(
                    activationDate.toBrazilianDateFormat(), 
                    listOf(
                        Grace("grace 1", RemainingTime(730, 32, "DAYS")),
                        Grace("Urgência e Emergência", RemainingTime(730, 0, "DAYS")),
                    )
                )
            )

            val response = CPTWithGracesResponseBuilder.buildCPTWithGracesResponse(
                member, 
                memberCpt.copy(
                    gracePeriod = listOf(
                        CptGracePeriod(
                            condition = "grace 1",
                            validUntil = activationDate.plusDays(64L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            type = GracePeriodType.PAC,
                            periodInDays = 0
                        ),
                        CptGracePeriod(
                            condition = "Urgência e Emergência",
                            validUntil = activationDate.plusHours(24L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            type = GracePeriodType.PAC,
                            periodInDays =0
                        ),
                    )
                )
            )
            assertThat(response).isEqualTo(expected)
        }

    @Test
    fun `#buildCPTWithGracesResponse - when member has CPTs, should return expected CPTWithGracesResponse`() =
        mockLocalDateTime(localDateTime) {
            val remainingTime = RemainingTime(731, 670, "DAYS")
            val expected = CPTWithGracesResponse(
                cpt = CPTTransport(
                    remainingTime,
                    listOf(
                        "cpt 1",
                        "cpt 2"
                    ),
                    listOf(
                        ProcedureTransport(
                            "Exames de alta complexidade",
                            "Ex: Ressonância magnética, tomografia computadorizada, entre outros.",
                            remainingTime
                        ),
                        ProcedureTransport(
                            "Procedimentos cirúrgicos",
                            "Seja em casos pré-agendados, de urgência ou emergência.",
                            remainingTime
                        ),
                        ProcedureTransport(
                            "Internações em leitos de CTI e/ou UTI",
                            "Seja em casos pré-agendados, de urgência ou emergência.",
                            remainingTime
                        ),
                        ProcedureTransport(
                            "Outros procedimentos de alta complexidade",
                            "Ex: Sessões de quimioterapia, radioterapia, hemodiálise crônica, entre outros.",
                            remainingTime
                        )
                    ),
                    MobileApp.Links.ALL_PROCEDURES_URL
                ),
                grace = GraceTransport(activationDate.toBrazilianDateFormat(), emptyList())
            )

            val response = CPTWithGracesResponseBuilder.buildCPTWithGracesResponse(
                member, memberCpt.copy(
                    conditions = listOf(
                        CptCondition(
                            name = "cpt 1",
                            cid = "cid1",
                            validUntil = LocalDateTime.now().plusMonths(22).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            periodInDays = 0
                        ),
                        CptCondition(
                            name = "cpt 2",
                            cid = "cid2",
                            validUntil = LocalDateTime.now().plusMonths(22).toBrazilianDateFormat(),
                            baseDate = LocalDate.now(),
                            periodInDays = 0
                        )
                    )
                )
            )
            assertThat(response).isEqualTo(expected)
        }
}
