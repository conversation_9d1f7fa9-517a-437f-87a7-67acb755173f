package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.AppointmentScheduleEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCollisionDetectedEvent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class AppointmentScheduleCollisionDetectedConsumerTest: ConsumerTest() {

    private val consumer = AppointmentScheduleCollisionDetectedConsumer()

    private val person = TestModelFactory.buildPerson()
    private val staff = TestModelFactory.buildStaff()

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly`() = runBlocking {
        mockkObject(AppStateNotifier) {
            coEvery { AppStateNotifier.updateAppState(person.id, AppState.HOME) } returns Unit
            coEvery {
                AppStateNotifier.updateAppState(person.id, AppState.APPOINTMENT_SCHEDULE_COLLISION_DETECTED)
            } returns Unit

            val event = AppointmentScheduleCollisionDetectedEvent(
                buildAppointmentScheduleRequestEvent(),
                person.id
            )

            val result = consumer.updateAppState(event)
            assertThat(result).isSuccessWithData(Unit)

            coVerify(exactly = 1) { AppStateNotifier.updateAppState(person.id, AppState.HOME) }
            coVerify(exactly = 1) { AppStateNotifier.updateAppState(person.id, AppState.APPOINTMENT_SCHEDULE_COLLISION_DETECTED) }
        }
    }

    private fun buildAppointmentScheduleRequestEvent(
        location: String? = null,
        healthPlanTaskId: String? = null,
        personTaskId: String? = null,
        eventName: String = "Primeira consulta com seu time de saúde"
    ) =
        AppointmentScheduleEvent(
            personEmail = person.email,
            staffEmail = staff.email,
            eventId = "C4L3NDLY3V3NT",
            eventName = eventName,
            location = location,
            startTime = LocalDateTime.of(2020, 1, 1, 10, 10),
            endTime = LocalDateTime.of(2020, 1, 1, 10, 10).plusHours(1),
            healthPlanTaskId = healthPlanTaskId,
            personTaskId = personTaskId,
            currentUserType = person.userType,

            )

}
