package br.com.alice.member.api.onboarding.v2

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ShoppingCart
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.builders.v2.CartResponseBuilder
import br.com.alice.member.api.controllers.onboarding.v2.ShoppingController
import br.com.alice.member.api.models.HttpMethod
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PlaceProductOrderResponse
import br.com.alice.member.api.models.ProductOrderResponse
import br.com.alice.member.api.models.onboarding.v2.CartProductOrderResponse
import br.com.alice.member.api.models.onboarding.v2.CartResponse
import br.com.alice.member.api.models.onboarding.v2.ProductAccommodationRequest
import br.com.alice.member.api.models.onboarding.v2.UpdateShoppingRequest
import br.com.alice.membership.buildProductOption
import br.com.alice.membership.client.onboarding.OnboardingAlreadyStartedException
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.ProductOptionInfo
import br.com.alice.membership.client.onboarding.ShoppingCartService
import br.com.alice.membership.client.onboarding.ShoppingService
import br.com.alice.membership.model.onboarding.ShoppingCartWithProduct
import br.com.alice.membership.model.onboarding.UpdateCartBundlesRequest
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import java.math.BigDecimal
import kotlin.test.BeforeTest
import kotlin.test.Test

class ShoppingRoutesTest : RoutesTestHelper() {

    private val personService: PersonService = mockk()
    private val shoppingCartService: ShoppingCartService = mockk()
    private val shoppingService: ShoppingService = mockk()
    private val onboardingService: OnboardingService = mockk()

    private val lead = TestModelFactory.buildLead()
    private val person = TestModelFactory.buildPerson().copy(leadId = lead.id)

    private val token = RangeUUID.generate().toString()

    private val bundleId = RangeUUID.generate().toString()

    private val updateRequest = UpdateShoppingRequest(
        type = ProductBundleType.HOSPITAL,
        ids = listOf(bundleId),
        accommodation = ProductAccommodationRequest(AccommodationType.ROOM)
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            ShoppingController(
                personService,
                shoppingService,
                onboardingService,
            )
        }
    }

    @Test
    fun `#updateCart should return 404 Not Found when person was not found`() {
        coEvery { personService.get(person.id) } returns NotFoundException("person_not_found").failure()

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/shopping/v2/product", updateRequest) { assertThat(it).isNotFound() }
        }
    }

    @Test
    fun `#updateCart should return 404 Not Found when a person doesnt have a leadId`() {
        val personWithoutLead = person.copy(leadId = null)
        coEvery { personService.get(person.id) } returns personWithoutLead.success()
        coEvery {
            shoppingService.updateShoppingProductWithBundles(
                personWithoutLead,
                any()
            )
        } returns NotFoundException("lead_id not found").failure()

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/shopping/v2/product", updateRequest) { assertThat(it).isNotFound() }
        }
    }

    @Test
    fun `#updateCart should return 200 OK with expected response`() {
        mockkObject(CartResponseBuilder) {
            val cartRequest = UpdateCartBundlesRequest(
                type = updateRequest.type,
                bundleIds = updateRequest.ids,
                accommodation = updateRequest.accommodation.type
            )

            coEvery { personService.get(person.id) } returns person.success()

            val productOption = TestModelFactory.buildProductOption()
            coEvery {
                shoppingService.updateShoppingProductWithBundles(
                    person,
                    cartRequest
                )
            } returns productOption.success()

            val cartResponse = CartResponse(
                sections = emptyList(),
                info = emptyList(),
                productOrder = CartProductOrderResponse(BigDecimal(100)),
                orderAction = Link(
                    href = "cart",
                    rel = "cart",
                    type = HttpMethod.POST
                )
            )

            coEvery { CartResponseBuilder.build(productOption) } returns cartResponse

            authenticatedAs(token, toTestPerson(person)) {
                put("/onboarding/shopping/v2/product", updateRequest) { response ->
                    assertThat(response).isOKWithData(cartResponse)
                }
            }
        }
    }

    @Test
    fun `#getCart should return 200 OK with expected response, when cart is found`() {
        mockkObject(CartResponseBuilder) {
            val product = TestModelFactory.buildProduct()

            val shoppingCart = ShoppingCart(
                leadId = lead.id,
                productId = product.id,
                providerIds = listOf("HAOC Vergueiro")
            )

            coEvery { personService.get(person.id) } returns person.success()

            val cartWithProduct = ShoppingCartWithProduct(shoppingCart, product)
            coEvery { shoppingCartService.findCartWithActiveProductByLead(lead.id) } returns cartWithProduct.success()

            val productOption = TestModelFactory.buildProductOption()
            coEvery { shoppingService.getShoppingProduct(person) } returns productOption.success()

            val cartResponse = CartResponse(
                sections = emptyList(),
                info = emptyList(),
                productOrder = CartProductOrderResponse(BigDecimal(100)),
                orderAction = Link(
                    href = "cart",
                    rel = "cart",
                    type = HttpMethod.POST
                )
            )

            coEvery { CartResponseBuilder.build(productOption) } returns cartResponse

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/shopping/v2/cart") { response ->
                    assertThat(response).isOKWithData(cartResponse)
                }
            }
        }
    }

    @Test
    fun `#getCart should return 200 OK with expected response, when cart is not found`() {
        mockkObject(CartResponseBuilder) {
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { shoppingCartService.findCartWithActiveProductByLead(lead.id) } returns NotFoundException("not_found").failure()

            val referenceProduct = TestModelFactory.buildProduct(reference = true)

            val productOption = TestModelFactory.buildProductOption()
            coEvery {
                shoppingService.getProductOption(ProductOptionInfo(referenceProduct.id, listOf(person.age)))
            } returns productOption.success()
            coEvery { shoppingService.getShoppingProduct(person) } returns productOption.success()

            val cartResponse = CartResponse(
                sections = emptyList(),
                info = emptyList(),
                productOrder = CartProductOrderResponse(BigDecimal(100)),
                orderAction = Link(
                    href = "cart",
                    rel = "cart",
                    type = HttpMethod.POST
                )
            )

            coEvery { CartResponseBuilder.build(productOption) } returns cartResponse

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/shopping/v2/cart") { response ->
                    assertThat(response).isOKWithData(cartResponse)
                }
            }
        }
    }

    @Test
    fun `#checkout should return 404 Not Found when a person doesnt exist`() {
        coEvery { personService.get(person.id) } returns NotFoundException("not_found").failure()

        authenticatedAs(token, toTestPerson(person)) {
            post("/onboarding/shopping/v2/order") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#checkout should return 200 OK when an order was created`() {
        val shoppingCart = TestModelFactory.buildShoppingCart(lead.id)
        val productOrder = TestModelFactory.buildProductOrder(person.id)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { shoppingCartService.findByLeadId(lead.id) } returns shoppingCart.success()
        coEvery { shoppingService.placeOrder(person, any()) } returns productOrder.success()
        coEvery { onboardingService.getCurrentPhase(any()) } returns OnboardingPhase.PORTABILITY.success()

        val expectedResponse = PlaceProductOrderResponse(
            order = ProductOrderResponse(productOrder.id),
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_QUESTIONS,
                link = Links.PORTABILITY_QUESTIONS_V2
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            post("/onboarding/shopping/v2/order") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#start should return 200 OK with expected response`() {
        mockkObject(CartResponseBuilder) {
            coEvery { personService.get(person.id) } returns person.success()

            val personOnboarding = TestModelFactory.buildPersonOnboarding(person.id)
            coEvery { shoppingService.startShopping(person) } returns personOnboarding.success()


            authenticatedAs(token, toTestPerson(person)) {
                post("/onboarding/shopping/v2/start", updateRequest) { response ->
                    assertThat(response).isOKWithData(personOnboarding)
                }
            }
        }
    }

    @Test
    fun `#start should return 400 OK with expected error code`() {
        mockkObject(CartResponseBuilder) {
            coEvery { personService.get(person.id) } returns person.success()

            coEvery { shoppingService.startShopping(person) } returns OnboardingAlreadyStartedException(person.id).failure()

            authenticatedAs(token, toTestPerson(person)) {
                post("/onboarding/shopping/v2/start", updateRequest) { response ->
                    assertThat(response).isBadRequestWithErrorCode(OnboardingAlreadyStartedException(person.id).code)
                }
            }
        }
    }
}
