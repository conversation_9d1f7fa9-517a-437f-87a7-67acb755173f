package br.com.alice.member.api.models.onboarding

import br.com.alice.member.api.models.onboarding.ChatInputType
import br.com.alice.member.api.models.onboarding.FreeTextConfiguration
import br.com.alice.member.api.models.onboarding.FreeTextKeyboardType
import br.com.alice.member.api.models.onboarding.NickNameChangeChat
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class NickNameChangeChatTest : br.com.alice.member.api.models.onboarding.ChatTest() {

    private val nickNameChangeChat = NickNameChangeChat()

    @Test
    fun `#messages should return 2 chat messages`() {
        assertThat(nickNameChangeChat.messages).hasSize(1)

        val textMessages = nickNameChangeChat.messages.map { it.text }
        assertThat(textMessages).hasSameElementsAs(listOf(
            "Sem problemas. Como você prefere ser chamado?"
        ))
    }

    @Test
    fun `#messages should have just one validation messages`() {
        assertThat(nickNameChangeChat.validationMessages).hasSize(1)
        val chatMessage = nickNameChangeChat.validationMessages.first()
        assertThat(chatMessage.text).isEqualTo("Ops. Nome parece inválido, pode digitar novamente?")
    }

    @Test
    fun `#input should return be an alphanumeric freetext`() {
        val input = nickNameChangeChat.input("someLink")

        val config = (input.config as FreeTextConfiguration)
        assertThat(input.type).isEqualTo(ChatInputType.FREE_TEXT)
        assertThat(config.keyboardType).isEqualTo(FreeTextKeyboardType.ALPHANUMERIC)
    }

}
