package br.com.alice.member.api.services.beneficiary

import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.GridItemSection
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.InfiniteScrollSection
import br.com.alice.app.content.model.section.MenuSection
import br.com.alice.app.content.model.section.TabBarSection
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.Cpt
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class BeneficiaryCPTsScreenServiceTest {

    private val service = BeneficiaryCPTsScreenService()
    private val personId = PersonId().toString()

    @Test
    fun `#getCptsExplanationScreen should return a valid cheshire screen structure`(): Unit = runBlocking {
        val result = service.getCptsExplanationScreen(personId)

        val expectedInfiniteScrollSection = result.layout.body.first();
        val expectedGridSection = (expectedInfiniteScrollSection.data as InfiniteScrollSection).body.first()
        val gridSection = expectedGridSection.data as GridSection
        val expectedTitleText =  (gridSection.children[0].data as GridItemSection).child
        val expectedSubtitleText =  (gridSection.children[1].data as GridItemSection).child
        val expectedExplanation1 =  (gridSection.children[2].data as GridItemSection).child
        val expectedExplanation2 =  (gridSection.children[3].data as GridItemSection).child

        assertEquals(expectedInfiniteScrollSection.type, SectionType.INFINITE_SCROLL_SECTION)
        assertEquals(expectedGridSection.type, SectionType.GRID_SECTION)
        assertEquals(expectedTitleText.type, SectionType.TEXT_SECTION)
        assertEquals(expectedSubtitleText.type, SectionType.TEXT_SECTION)
        assertEquals(expectedExplanation1.type, SectionType.TEXT_SECTION)
        assertEquals(expectedExplanation2.type, SectionType.TEXT_SECTION)
    }
    @Test
    fun `#getCptsScreen should return a valid cheshire screen structure`(): Unit = runBlocking {
        val result = service.getCptsScreen(personId)

        val expectedInfiniteScrollSection = result.layout.body.first();
        val expectedGridSection = (expectedInfiniteScrollSection.data as InfiniteScrollSection).body.first()
        val gridSection = expectedGridSection.data as GridSection
        val expectedTitleText =  (gridSection.children[0].data as GridItemSection).child
        val expectedSubtitleText =  (gridSection.children[1].data as GridItemSection).child
        val expectedConditionsList =  (gridSection.children[2].data as GridItemSection).child

        assertEquals(expectedInfiniteScrollSection.type, SectionType.INFINITE_SCROLL_SECTION)
        assertEquals(expectedGridSection.type, SectionType.GRID_SECTION)
        assertEquals(expectedTitleText.type, SectionType.TEXT_SECTION)
        assertEquals(expectedSubtitleText.type, SectionType.TEXT_SECTION)
        assertEquals(expectedConditionsList.type, SectionType.LIST_MENU_SECTION)
    }

    @Test
    fun `#getCptsListCards should return a list of cpt cards`(): Unit = runBlocking {
        val cpts = listOf(
            Cpt("Síndrome do ovário policístico", listOf("C01", "C011", "C0111")),
            Cpt("Pedra nos rins", listOf("C02")))

        val result = service.getCptsListCards(personId, cpts)

        val expectedFirstMenuItem = result.content[0].data as MenuSection
        val expectedSecondMenuItem = result.content[1].data as MenuSection

        val expectedFirstRemoteAction = expectedFirstMenuItem.onTapAction?.params?.get("action") as RemoteAction
        val expectedSecondRemoteAction = expectedSecondMenuItem.onTapAction?.params?.get("action") as RemoteAction

        assertEquals(result.content.count(), 2)
        assertEquals(expectedFirstMenuItem.title, "Síndrome do ovário policístico")
        assertEquals(expectedFirstMenuItem.label, "CID C01-C011-C0111")
        assertEquals(expectedFirstRemoteAction.endpoint, "/beneficiaries/$personId/screen/cpt_details?condition=S%C3%ADndrome+do+ov%C3%A1rio+polic%C3%ADstico&cids=C01-C011-C0111")
        assertEquals(expectedSecondMenuItem.title, "Pedra nos rins")
        assertEquals(expectedSecondMenuItem.label, "CID C02")
        assertEquals(expectedSecondRemoteAction.endpoint, "/beneficiaries/$personId/screen/cpt_details?condition=Pedra+nos+rins&cids=C02")
    }

    @Test
    fun `#getConditionsScreen should return a valid cheshire screen structure`(): Unit = runBlocking {
        val result = service.getCptDetailsScreen(personId, "Síndrome do ovário policístico", "C01")

        val expectedTabBarSection = result.layout.body.first()
        val tabBarData = expectedTabBarSection.data as TabBarSection

        assertEquals(expectedTabBarSection.type, SectionType.TAB_BAR_SECTION)
        assertEquals(tabBarData.headers?.count(), 2)
        assertEquals(tabBarData.tabBarItems.count(), 2)
        assertEquals(tabBarData.tabBarItems[0].content, "Coberto")
        assertEquals(tabBarData.tabBarItems[0].action.endpoint, "/beneficiaries/$personId/screen/covered_procedures")
        assertEquals(tabBarData.tabBarItems[1].content, "Não coberto")
        assertEquals(tabBarData.tabBarItems[1].action.endpoint, "/beneficiaries/$personId/screen/uncovered_procedures")
    }

    @Test
    fun `#getCoveredProceduresScreen should return a valid cheshire screen structure`(): Unit = runBlocking {
        val result = service.getCoveredProceduresScreen()

        val expectedInfiniteScrollSection = result.layout.body.first();
        val expectedGridSection = (expectedInfiniteScrollSection.data as InfiniteScrollSection).body.first()
        val gridSection = expectedGridSection.data as GridSection

        assertEquals(gridSection.children.count(), 7)
        assertEquals(getProcedureTitleSection(gridSection, 1).content.title, "<icon>check_with_border</icon>  Alice Agora")
        assertEquals(getProcedureDescriptionSection(gridSection, 1).content.title, "Atendimento 24h pelo app, a qualquer hora e de onde você estiver")
        assertEquals(getProcedureTitleSection(gridSection, 2).content.title, "<icon>check_with_border</icon>  Consultas com especialistas")
        assertEquals(getProcedureTitleSection(gridSection, 3).content.title, "<icon>check_with_border</icon>  Médicos de família da Alice")
        assertEquals(getProcedureTitleSection(gridSection, 4).content.title, "<icon>check_with_border</icon>  Atendimento em pronto-socorro")
        assertEquals(getProcedureTitleSection(gridSection, 5).content.title, "<icon>check_with_border</icon>  Exames simples")
        assertEquals(getProcedureDescriptionSection(gridSection, 5).content.title, "Hemograma, ultrassom, raio X e [https://www.gov.br/ans/pt-br/acesso-a-informacao/participacao-da-sociedade/atualizacao-do-rol-de-procedimentos/copy_of_Anexo_I_Rol_2021RN_465.2021_RN610.pdf](outros)")
        assertEquals(getProcedureTitleSection(gridSection, 6).content.title, "<icon>check_with_border</icon>  Realização de terapias simples")
        assertEquals(getProcedureDescriptionSection(gridSection, 6).content.title, "Fisioterapia, psicoterapia e [https://www.gov.br/ans/pt-br/acesso-a-informacao/participacao-da-sociedade/atualizacao-do-rol-de-procedimentos/copy_of_Anexo_I_Rol_2021RN_465.2021_RN610.pdf](outros)")
    }

    @Test
    fun `#getUncoveredProceduresScreen should return a valid cheshire screen structure`(): Unit = runBlocking {
        val result = service.getUncoveredProceduresScreen()

        val expectedInfiniteScrollSection = result.layout.body.first();
        val expectedGridSection = (expectedInfiniteScrollSection.data as InfiniteScrollSection).body.first()
        val gridSection = expectedGridSection.data as GridSection

        assertEquals(gridSection.children.count(), 4)
        assertEquals(getProcedureTitleSection(gridSection, 1).content.title, "<icon>nop</icon>  Procedimentos de alta complexidade (PAC)")
        assertEquals(getProcedureDescriptionSection(gridSection, 1).content.title, "Ressonância, tomografia, medicação de alto custo e [https://www.gov.br/ans/pt-br/acesso-a-informacao/participacao-da-sociedade/atualizacao-do-rol-de-procedimentos/copy_of_Anexo_I_Rol_2021RN_465.2021_RN610.pdf](outros)")
        assertEquals(getProcedureTitleSection(gridSection, 2).content.title, "<icon>nop</icon>  Cirurgias relacionadas")
        assertEquals(getProcedureTitleSection(gridSection, 3).content.title, "<icon>nop</icon>  Internações em UTI/CTI")
    }

    @Test
    fun `#getConfirmCPTsAction should return a close remote action`(): Unit = runBlocking {
        val result = service.getConfirmCPTsAction()

        assertEquals(result.params?.get("root"), true)
        assertEquals(result.params?.get("pop_twice"), true)
    }

    private fun getProcedureTitleSection(mainGridSection: GridSection, index: Int): TextSection.Content {
        val internalGrid = (mainGridSection.children[index].data as GridItemSection).child.data as GridSection
        val gridItemSection = internalGrid.children[0].data as GridItemSection
        return gridItemSection.child.data as TextSection.Content
    }

    private fun getProcedureDescriptionSection(mainGridSection: GridSection, index: Int): TextSection.Content {
        val internalGrid = (mainGridSection.children[index].data as GridItemSection).child.data as GridSection
        val gridItemSection = internalGrid.children[1].data as GridItemSection
        return gridItemSection.child.data as TextSection.Content
    }
}
