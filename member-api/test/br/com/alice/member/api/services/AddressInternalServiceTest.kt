package br.com.alice.member.api.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.googlemaps.services.GoogleMapsService
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory.buildPerson
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals
import br.com.alice.data.layer.models.Address as PersonAddress

class AddressInternalServiceTest {

    private val googleMapsService: GoogleMapsService = mockk()
    private val personService: PersonService = mockk()
    val service = AddressInternalService(googleMapsService, personService)
    private val person = buildPerson()
    private val personAddress = person.mainAddress!!
    private val lat = -23.5718116
    private val lng = -46.69273700000001
    private val complement = "Some complement"

    private val mapsAddress = Address(
        id = "ChIJ_UPfFNBXzpQRWTmRXq3FZPI",
        street = "Avenida Rebouças",
        number = "3506",
        complement = personAddress.complement,
        neighbourhood = "Pinheiros",
        postalCode = "05402-600",
        city = "São Paulo",
        state = "SP",
        country = "Brasil",
        lat = lat,
        lng = lng
    )
    private val transportAddress = AddressMapsTransport(
        nickName = "Principal",
        street = mapsAddress.street,
        country = mapsAddress.country,
        placeId = mapsAddress.id,
        state = mapsAddress.state,
        city = mapsAddress.city,
        number = mapsAddress.number,
        neighbourhood = mapsAddress.neighbourhood,
        postalCode = mapsAddress.postalCode,
        lat = mapsAddress.lat,
        lng = mapsAddress.lng,
        complement = personAddress.complement
    )

    private val requestAddress = UserAddressResponse(
        street = mapsAddress.street,
        state = State.SP,
        city = mapsAddress.city,
        number = mapsAddress.number,
        neighbourhood = mapsAddress.neighbourhood,
        postalCode = mapsAddress.postalCode,
        complement = complement
    )

    private val address = PersonAddress(
        street = mapsAddress.street,
        state = State.SP,
        city = mapsAddress.city,
        number = mapsAddress.number,
        neighbourhood = mapsAddress.neighbourhood,
        postalCode = mapsAddress.postalCode,
        complement = complement,
        lat = lat,
        lng = lng
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        googleMapsService,
        personService
    )

    @Test
    fun `#getAddress should return address with person address`() = runBlocking {
        coEvery { googleMapsService.getAddressByQuery(personAddress.toString()) } returns mapsAddress.success()

        val result = service.getAddress(person, withComplement = true)
        assertThat(result).isSuccessWithData(transportAddress.copy(nickName = "Principal"))

        coVerifyOnce { googleMapsService.getAddressByQuery(any()) }
    }

    @Test
    fun `#getAddress should return null with person address when address is invalid`() = runBlocking {
        val person = person.copy(addresses = listOf())
        val result = service.getAddress(person)
        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getAddress should return address with lat and lng`() = runBlocking {
        coEvery { googleMapsService.getAddressByLatLng(lat, lng) } returns mapsAddress.success()

        val result = service.getAddress(lat, lng)
        assertThat(result).isSuccessWithData(
            transportAddress.copy(
                nickName = "Maps Service"
            )
        )

        coVerifyOnce { googleMapsService.getAddressByLatLng(any(), any()) }
    }

    @Test
    fun `#getByPlaceId should return address with place id`() = runBlocking {
        coEvery { googleMapsService.getAddressById(mapsAddress.id) } returns mapsAddress.success()

        val result = service.getByPlaceId(mapsAddress.id)
        assertThat(result).isSuccessWithData(
            transportAddress.copy(
                nickName = "Maps Service"
            )
        )
        assertEquals(
            expected = "Avenida Rebouças, 3506, ${mapsAddress.complement} - Pinheiros, São Paulo - SP, 05402-600, Brasil",
            actual = result.get().fullAddress
        )
        assertEquals(
            expected = "Avenida Rebouças, 3506",
            actual = result.get().shortAddress
        )
        coVerifyOnce { googleMapsService.getAddressById(any()) }
    }

    @Test
    fun `#autocompletedAddress should return autocomplete response`() = runBlocking {
        val response = AutocompleteTransport(
            placeId = "place_id",
            description = "Av. Rebouças, 3506 - Pinheiros, São Paulo - SP, 05402-600, Brasil",
            mainText = "Av. Rebouças, 3506",
            secondaryText = "Pinheiros, São Paulo - SP, 05402-600, Brasil"
        )
        val session = RangeUUID.generate()
        val query = "Av briga"
        coEvery { googleMapsService.autocompleteByText(query, session) } returns listOf(response).success()

        val result = service.autocompletedAddress(query, session)
        assertThat(result).isSuccessWithData(listOf(response))

        coVerifyOnce { googleMapsService.autocompleteByText(any(), any()) }
    }

    @Test
    fun `#updateAddress should update address with lat and lng`() = runBlocking {
        coEvery {
            personService.get(person.id)
        } returns person.success()

        coEvery {
            googleMapsService.getAddressByQuery(address.toStringWithoutComplement())
        } returns mapsAddress.success()

        val personToUpdate = person.copy(
            addresses = listOf(address)
        )

        coEvery {
            personService.update(personToUpdate)
        } returns personToUpdate.success()

        val result = service.updateAddress(requestAddress, person.id)
        assertThat(result).isSuccessWithData(transportAddress.copy(complement = mapsAddress.complement))

        coVerifyOnce {
            personService.get(any())
            googleMapsService.getAddressByQuery(any())
            personService.update(any())
        }
    }

    @Test
    fun `#updateAddress should fail when update fail`() = runBlocking {
        coEvery {
            personService.get(person.id)
        } returns person.success()

        coEvery {
            googleMapsService.getAddressByQuery(address.toStringWithoutComplement())
        } returns mapsAddress.success()

        val personToUpdate = person.copy(
            addresses = listOf(address)
        )

        coEvery { personService.update(personToUpdate) } returns Exception("ex").failure()

        val result = service.updateAddress(requestAddress, person.id)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce {
            personService.get(any())
            googleMapsService.getAddressByQuery(any())
            personService.update(any())
        }
    }

    @Test
    fun `#updateAddress should update address with lat and lng and request values`() = runBlocking {
        val requestPostalCode = "05402-601"
        val requestNumber = "900"

        val address = address.copy(
            postalCode = requestPostalCode,
            number = requestNumber
        )

        coEvery {
            personService.get(person.id)
        } returns person.success()

        coEvery {
            googleMapsService.getAddressByQuery(address.toStringWithoutComplement())
        } returns mapsAddress.copy(
            postalCode = "",
            number = ""
        ).success()

        val personToUpdate = person.copy(
            addresses = listOf(address)
        )

        coEvery {
            personService.update(personToUpdate)
        } returns personToUpdate.success()

        val result = service.updateAddress(
            requestAddress.copy(
                postalCode = requestPostalCode,
                number = requestNumber
            ),
            person.id
        )

        assertThat(result).isSuccessWithData(
            transportAddress.copy(
                complement = mapsAddress.complement,
                postalCode = requestPostalCode,
                number = requestNumber
            )
        )

        coVerifyOnce {
            personService.get(any())
            googleMapsService.getAddressByQuery(any())
            personService.update(any())
        }
    }

    @Test
    fun `#getAddress should return address with lat and lng and empty postal code and number`() = runBlocking {
        coEvery { googleMapsService.getAddressByLatLng(lat, lng) } returns mapsAddress.copy(
            postalCode = "",
            number = ""
        ).success()

        val result = service.getAddress(lat, lng)
        assertThat(result).isSuccessWithData(
            transportAddress.copy(
                nickName = "Maps Service",
                postalCode = "",
                number = ""
            )
        )

        coVerifyOnce { googleMapsService.getAddressByLatLng(any(), any()) }
    }
}
