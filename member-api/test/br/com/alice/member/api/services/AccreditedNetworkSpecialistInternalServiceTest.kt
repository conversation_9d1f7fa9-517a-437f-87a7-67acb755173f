package br.com.alice.member.api.services

import br.com.alice.app.content.client.AppointmentScheduleEventService
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CalloutAction
import br.com.alice.app.content.model.CalloutType
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedRatingService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildActionPlanTaskReferral
import br.com.alice.data.layer.helpers.TestModelFactory.buildMedicalSpecialty
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.ReferralSpecialty
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.withContacts
import br.com.alice.member.api.builders.AccreditedNetworkSpecialistResponseBuilder
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.SpecialistSchedulingInformationTransport
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.MemberWithProduct
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.SchedulePreTriageFilters
import br.com.alice.schedule.client.SchedulePreTriageResponse
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedNetworkSpecialistInternalServiceTest {
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val healthCareTeamService: HealthcareTeamService = mockk()
    private val specialistSchedulingInternalService: SpecialistSchedulingInternalService = mockk()
    private val consolidatedRatingService: ConsolidatedRatingService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val appointmentSchedulePreTriageService: AppointmentSchedulePreTriageService = mockk()
    private val appointmentScheduleEventService: AppointmentScheduleEventService = mockk()
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService = mockk()

    private val service = AccreditedNetworkSpecialistInternalService(
        personService,
        memberService,
        healthProfessionalService,
        healthCareTeamService,
        specialistSchedulingInternalService,
        consolidatedRatingService,
        medicalSpecialtyService,
        appointmentSchedulePreTriageService,
        appointmentScheduleEventService,
        accreditedNetworkFavoriteService,
    )

    companion object {
        private val ROOT_NODE_ID = RangeUUID.generate()
    }

    private val referralNew: ReferralNew = buildActionPlanTaskReferral().specialize()
    private val appVersion = SemanticVersion("1.0.0")
    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember()
    private val product = TestModelFactory.buildProduct()
    private val specialistId = RangeUUID.generate()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(id = specialistId)
        .withContacts(listOf(TestModelFactory.buildContact()))


    @Test
    fun `getSpecialistDetails should return a SpecialistDetailsTransport`() = runBlocking {
        val healthCommunitySpecialist = TestModelFactory.buildHealthCommunitySpecialist(id = specialistId)
        val consolidatedRating = TestModelFactory.buildConsolidatedRating()
        val medicalSpecialties =
            healthProfessional.subSpecialtyIds.map { buildMedicalSpecialty(id = it) }
        val lat = 0.0
        val lng = 0.0
        val schedulingInfo = SpecialistSchedulingInformationTransport(
            appointmentType = SpecialistAppointmentType.REMOTE,
            schedulingUrl = "url",
            nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30),
        )
        val serviceDays = listOf("seg", "ter", "qua")

        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            memberService.findActiveMembershipWithProduct(
                person.id,
                MemberService.FindOptions(withCassiMember = true)
            )
        } returns MemberWithProduct(member, product).success()
        coEvery {
            healthProfessionalService.get(
                specialistId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        } returns healthProfessional.success()
        coEvery { consolidatedRatingService.getByHealthProfessional(specialistId) } returns consolidatedRating.success()
        coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!
        ).success()
        coEvery { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) } returns medicalSpecialties.success()
        coEvery {
            specialistSchedulingInternalService.getUniquePlanTask(
                person.id,
                null,
                healthProfessional.specialtyId!!
            )
        } returns referralNew.success()
        coEvery {
            specialistSchedulingInternalService.getScheduleInformation(
                person,
                healthProfessional,
                referralNew
            )
        } returns schedulingInfo.success()
        coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
        coEvery {
            accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                person.id,
                specialistId
            )
        } returns TestModelFactory.buildAccreditedNetworkFavorite().success()

        val result = service.getSpecialistDetails(person.id, specialistId, appVersion, lat, lng, null)

        assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce {
            memberService.findActiveMembershipWithProduct(
                person.id,
                MemberService.FindOptions(withCassiMember = true)
            )
        }
        coVerifyOnce { consolidatedRatingService.getByHealthProfessional(specialistId) }
        coVerifyOnce {
            healthProfessionalService.get(
                specialistId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        }
        coVerifyOnce { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) }
        coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
        coVerifyNone { healthProfessionalService.findByStaffId(specialistId) }
    }

    @Test
    fun `getSpecialistDetails should return a SpecialistDetailsTransport with alice card type for duquesa member`() =
        runBlocking {
            val member = member.copy(brand = Brand.DUQUESA)
            val healthCommunitySpecialist = TestModelFactory.buildHealthCommunitySpecialist(id = specialistId)
            val consolidatedRating = TestModelFactory.buildConsolidatedRating()
            val medicalSpecialties = healthProfessional.subSpecialtyIds.map { buildMedicalSpecialty(id = it) }
            val lat = 0.0
            val lng = 0.0
            val schedulingInfo = SpecialistSchedulingInformationTransport(
                appointmentType = SpecialistAppointmentType.REMOTE,
                schedulingUrl = "url",
                nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30),
            )
            val serviceDays = listOf("seg", "ter", "qua")
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val medicalSpecialty = buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            } returns MemberWithProduct(member, product).success()
            coEvery {
                healthProfessionalService.get(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns healthProfessional.success()
            coEvery { consolidatedRatingService.getByHealthProfessional(specialistId) } returns consolidatedRating.success()
            coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns medicalSpecialty.success()
            coEvery { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) } returns medicalSpecialties.success()
            coEvery {
                specialistSchedulingInternalService.getUniquePlanTask(
                    person.id,
                    null,
                    healthProfessional.specialtyId!!
                )
            } returns referralNew.success()
            coEvery {
                specialistSchedulingInternalService.getScheduleInformation(
                    person,
                    healthProfessional,
                    referralNew
                )
            } returns schedulingInfo.success()
            coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
            coEvery {
                accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                    person.id,
                    specialistId
                )
            } returns favorite.success()

            val result = service.getSpecialistDetails(person.id, specialistId, appVersion, lat, lng, null)

            val expected = AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
                person = person,
                member = member,
                product = product,
                specialist = healthProfessional,
                subSpecialties = medicalSpecialties,
                favorite = favorite,
                consolidatedRating = consolidatedRating,
                specialty = medicalSpecialty,
                memberLat = lat,
                memberLng = lng,
                schedulingInfo = schedulingInfo,
                serviceDays = serviceDays,
                appVersion = appVersion
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            }
            coVerifyOnce { consolidatedRatingService.getByHealthProfessional(specialistId) }
            coVerifyOnce {
                healthProfessionalService.get(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) }
            coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
            coVerifyNone { healthProfessionalService.findByStaffId(specialistId) }
        }

    @Test
    fun `getSpecialistDetails should return a SpecialistDetailsTransport if specialistId is not from HP`() =
        runBlocking {
            val healthProfessional =
                TestModelFactory.buildHealthProfessional().withContacts(listOf(TestModelFactory.buildContact()))
            val consolidatedRating = TestModelFactory.buildConsolidatedRating()
            val medicalSpecialties =
                healthProfessional.subSpecialtyIds.map { buildMedicalSpecialty(id = it) }
            val lat = 0.0
            val lng = 0.0

            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            } returns MemberWithProduct(member, product).success()
            coEvery {
                healthProfessionalService.get(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns NotFoundException().failure()
            coEvery {
                healthProfessionalService.findByStaffId(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns healthProfessional.success()
            coEvery { consolidatedRatingService.getByHealthProfessional(healthProfessional.id) } returns consolidatedRating.success()
            coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!
            ).success()
            coEvery { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) } returns medicalSpecialties.success()
            coEvery {
                specialistSchedulingInternalService.getUniquePlanTask(
                    person.id,
                    null,
                    healthProfessional.specialtyId!!
                )
            } returns ManyActionPlanTaskException().failure()
            coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns emptyList<String>().success()
            coEvery {
                accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                    person.id,
                    healthProfessional.id,
                )
            } returns TestModelFactory.buildAccreditedNetworkFavorite().success()

            val result = service.getSpecialistDetails(person.id, healthProfessional.id, appVersion, lat, lng, null)

            assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            }
            coVerifyOnce { consolidatedRatingService.getByHealthProfessional(healthProfessional.id) }
            coVerifyOnce {
                healthProfessionalService.get(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                healthProfessionalService.findByStaffId(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce { consolidatedRatingService.getByHealthProfessional(healthProfessional.id) }
            coVerifyOnce { medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds) }
            coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
        }

    @Test
    fun `getSpecialistDetails should return not found if specialist is not found in HP`() =
        runBlocking {
            val lat = 0.0
            val lng = 0.0

            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            } returns MemberWithProduct(member, product).success()
            coEvery {
                healthProfessionalService.get(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns NotFoundException().failure()
            coEvery {
                healthProfessionalService.findByStaffId(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns NotFoundException().failure()
            coEvery {
                consolidatedRatingService.getByHealthProfessional(specialistId)
            } returns NotFoundException().failure()

            val result = service.getSpecialistDetails(person.id, specialistId, appVersion, lat, lng, null)

            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce {
                memberService.findActiveMembershipWithProduct(
                    person.id,
                    MemberService.FindOptions(withCassiMember = true)
                )
            }
            coVerifyOnce {
                healthProfessionalService.get(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                healthProfessionalService.findByStaffId(
                    specialistId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
        }

    @Test
    fun `getMyHealthCareTeamSpecialist should return a SpecialistDetailsTransport`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val healthcareTeam = TestModelFactory.buildHealthcareTeam(physicianStaffId = healthProfessional.staffId)
        val lat = 0.0
        val lng = 0.0
        val serviceDays = listOf("seg", "ter", "qua")
        val schedulingInfo = SpecialistSchedulingInformationTransport(
            appointmentType = SpecialistAppointmentType.REMOTE,
            schedulingUrl = "url",
            nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30),
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthcareTeam.success()
        coEvery {
            healthProfessionalService.findByStaffId(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        } returns healthProfessional.success()
        coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
        coEvery {
            specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                healthProfessional.staffId,
                healthcareTeam.segment
            )
        } returns schedulingInfo.success()
        coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!
        ).success()

        coEvery {
            appointmentSchedulePreTriageService.getProtocolByFilters(
                SchedulePreTriageFilters(person.id)
            )
        } returns SchedulePreTriageResponse(ROOT_NODE_ID).success()

        val result = service.getMyHealthCareTeamSpecialist(person.id, lat, lng, appVersion)

        assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(person.id) }
        coVerifyOnce {
            healthProfessionalService.findByStaffId(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        }
        coVerifyOnce {
            specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                healthProfessional.staffId,
                healthcareTeam.segment
            )
        }
        coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
        coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
        coVerifyOnce { appointmentSchedulePreTriageService.getProtocolByFilters(any()) }
    }

    @Test
    fun `getMyHealthCareTeamSpecialist should return a UnsupportedOperationException if not found healthcareTeam`() = runBlocking {
        val lat = 0.0
        val lng = 0.0

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns NotFoundException().failure()

        val result = service.getMyHealthCareTeamSpecialist(person.id, lat, lng, appVersion)

        assertThat(result).isFailureOfType(UnsupportedOperationException::class)

        coVerifyOnce {
            personService.get(any())
            healthCareTeamService.getHealthcareTeamByPerson(any())
        }

        coVerifyNone {
            healthProfessionalService.findByStaffId(any(), any())
            specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(any(), any())
            medicalSpecialtyService.getById(any())
            appointmentSchedulePreTriageService.getProtocolByFilters(any())
        }
    }


    @Test
    fun `getHealthCareTeamSpecialist should return a SpecialistDetailsTransport not from member healthcare team`() =
        runBlocking {
            val contact = TestModelFactory.buildContact()
            val healthProfessional = TestModelFactory.buildHealthProfessional(contactIds = listOf(contact.id))
            val healthcareTeam =
                TestModelFactory.buildHealthcareTeam()
            val serviceDays = listOf("seg", "ter", "qua")
            val lat = 0.0
            val lng = 0.0

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthcareTeam.success()
            coEvery {
                healthProfessionalService.get(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns healthProfessional.withContacts(listOf(contact)).success()
            coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
            coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!
            ).success()

            val result = service.getHealthCareTeamSpecialist(person.id, healthProfessional.id, lat, lng, appVersion)

            assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(person.id) }
            coVerifyOnce {
                healthProfessionalService.get(
                    healthProfessional.id,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) }
            coVerifyNone {
                healthProfessionalService.findByStaffId(
                    healthProfessional.id
                )
            }
            coVerifyNone {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthProfessional.staffId,
                    healthcareTeam.segment
                )
            }
            coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
        }

    @Test
    fun `getHealthCareTeamSpecialist should return a SpecialistDetailsTransport using HP staffId`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val healthCareTeam = TestModelFactory.buildHealthcareTeam()
        val lat = 0.0
        val lng = 0.0
        val serviceDays = listOf("seg", "ter", "qua")

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
        coEvery {
            healthProfessionalService.get(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        } returns NotFoundException().failure()
        coEvery {
            healthProfessionalService.findByStaffId(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        } returns healthProfessional.success()
        coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
        coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!
        ).success()

        val result = service.getHealthCareTeamSpecialist(person.id, healthProfessional.staffId, lat, lng, appVersion)

        assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

        coVerifyOnce { personService.get(person.id) }
        coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(person.id) }
        coVerifyOnce {
            healthProfessionalService.get(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        }
        coVerifyOnce {
            healthProfessionalService.findByStaffId(
                healthProfessional.staffId,
                HealthProfessionalService.FindOptions(withContact = true)
            )
        }
        coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
    }

    @Test
    fun `buildCalloutSectionWithException should return callout many action exception`() {
        val result = service.buildCalloutSectionWithException(ManyActionPlanTaskException())
        val expected = CalloutSection(
            title = "Mais de um encaminhamento",
            calloutBody = "Para agendar com esse profissional é necessário selecionar um dos encaminhamentos disponíveis",
            calloutVariant = CalloutVariant.TUTORIAL,
            calloutAction = AccreditedNetworkSpecialistInternalService.PDA_CALLOUT,
        )
        assertEquals(expected, result)
    }

    @Test
    fun `buildCalloutSectionWithException should return callout suggestion specialist if different`() {
        val result = service.buildCalloutSectionWithException(SuggestionSpecialistIsDifferentException())
        val expected = CalloutSection(
            title = "Verifique o profissional",
            calloutBody = "Esse especialista é diferente do profissional indicado no seu encaminhamento",
            calloutVariant = CalloutVariant.WARNING,
            calloutAction = AccreditedNetworkSpecialistInternalService.PDA_CALLOUT,
        )
        assertEquals(expected, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout when referral is null, isAliceMember is true and specialty is therapy`() {
        val healthProfessional =
            TestModelFactory.buildHealthProfessional().withContacts(listOf(TestModelFactory.buildContact()))
        val medicalSpecialty = buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!,
            name = "Psicologia",
            isTherapy = true
        )
        val result = service.buildCalloutByReferral(null, healthProfessional, medicalSpecialty, true)
        val expected = CalloutSection(
            title = "É necessário encaminhamento",
            calloutBody = "Para agendar um atendimento você precisa de indicação médica.",
            calloutVariant = CalloutVariant.INFORMATION,
            calloutAction = CalloutAction(
                type = CalloutType.NAVIGATION,
                label = "Falar com Alice Agora",
                onClickAction = RemoteAction(
                    mobileRoute = ActionRouting.ALICE_AGORA
                )
            )
        )
        assertEquals(expected, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout when referral is null and isDuquesa member`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBERSHIP,
            "has_duquesa_member_migrate_to_alice_app",
            true
        ) {
            val healthProfessional =
                TestModelFactory.buildHealthProfessional().withContacts(listOf(TestModelFactory.buildContact()))
            val medicalSpecialty = buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!,
                name = "Psicologia"
            )
            val expected = CalloutSection(
                title = "É necessário encaminhamento",
                calloutBody = "Para agendar um atendimento você precisa de indicação médica.",
                calloutVariant = CalloutVariant.INFORMATION,
                calloutAction = CalloutAction(
                    type = CalloutType.NAVIGATION,
                    label = "Falar com Alice Agora",
                    onClickAction = RemoteAction(
                        mobileRoute = ActionRouting.ALICE_AGORA
                    )
                )
            )

            val result = service.buildCalloutByReferral(null, healthProfessional, medicalSpecialty, false)

            assertEquals(expected, result)
        }
    }

    @Test
    fun `buildCalloutByReferral should return callout when specialty to HP is different by apt`() {
        val healthProfessional =
            TestModelFactory.buildHealthProfessional().withContacts(listOf(TestModelFactory.buildContact()))
        val referralNew = buildActionPlanTaskReferral(
            subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "sub specialty"
            )
        ).specialize<ReferralNew>()

        val medicalSpecialty = buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!,
            name = "Psicologia",
            isTherapy = true
        )
        val result = service.buildCalloutByReferral(referralNew, healthProfessional, medicalSpecialty, true)
        val expected = CalloutSection(
            title = "Subespecialidade incorreta",
            calloutBody = "Não perca viagem! Esse profissional pode não atender a demanda específica do seu encaminhamento.",
            calloutVariant = CalloutVariant.TUTORIAL,
            calloutAction = AccreditedNetworkSpecialistInternalService.PDA_CALLOUT,
        )
        assertEquals(expected, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout null when referral is null, isAliceMember and specialty is not therapy`() {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val medicalSpecialty = buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!,
            name = "Cardiologia",
            isTherapy = false
        )
        val result = service.buildCalloutByReferral(null, healthProfessional, medicalSpecialty, true)
        assertEquals(null, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout null when referral is not null, isAliceMember and specialty is therapy`() {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val medicalSpecialty = buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!,
            name = "Cardiologia",
            isTherapy = true
        )
        val result = service.buildCalloutByReferral(referralNew, healthProfessional, medicalSpecialty, true)
        assertEquals(null, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout null when referral is not null and isDuquesa member`() {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val medicalSpecialty = buildMedicalSpecialty(
            id = healthProfessional.specialtyId!!,
            name = "Cardiologia"
        )
        val result = service.buildCalloutByReferral(referralNew, healthProfessional, medicalSpecialty, false)
        assertEquals(null, result)
    }

    @Test
    fun `buildCalloutByReferral should return callout null when isDuquesa member and flag is off`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.MEMBERSHIP,
            "has_duquesa_member_migrate_to_alice_app",
            false
        ) {
            val healthProfessional =
                TestModelFactory.buildHealthProfessional().withContacts(listOf(TestModelFactory.buildContact()))
            val medicalSpecialty = buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!,
                name = "Psicologia"
            )

            val result = service.buildCalloutByReferral(null, healthProfessional, medicalSpecialty, false)

            assertEquals(null, result)
        }
    }

    @Test
    fun `buildCalloutByReferral should return callout null when referral is null, isDuquesa member and specialty is not gatekeeper`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.MEMBERSHIP,
                mapOf(
                    "has_duquesa_member_migrate_to_alice_app" to true,
                    "specialties_without_duquesa_gatekeeper" to listOf(healthProfessional.specialtyId.toString())
                )
            ) {
                val medicalSpecialty = buildMedicalSpecialty(
                    id = healthProfessional.specialtyId!!,
                    name = "Psicologia"
                )

                val result = service.buildCalloutByReferral(null, healthProfessional, medicalSpecialty, false)

                assertEquals(null, result)
            }
        }

    @Test
    fun `getHealthCareTeamSpecialist should build a SpecialistDetailsTransport with rootNodeId if staff is the physician of healthcare team and appointmentHub is disabled`() =
        runBlocking {
            val healthProfessional = TestModelFactory.buildHealthProfessional()
            val healthCareTeam = TestModelFactory.buildHealthcareTeam(physicianStaffId = healthProfessional.staffId)
            val lat = 0.0
            val lng = 0.0
            val serviceDays = listOf("seg", "ter", "qua")
            val schedulingInfo = SpecialistSchedulingInformationTransport(
                appointmentType = SpecialistAppointmentType.REMOTE,
                schedulingUrl = "url",
                nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery {
                healthProfessionalService.get(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns NotFoundException().failure()
            coEvery {
                healthProfessionalService.findByStaffId(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns healthProfessional.success()
            coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
            coEvery {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthProfessional.staffId,
                    healthCareTeam.segment
                )
            } returns schedulingInfo.success()
            coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!
            ).success()

            coEvery {
                appointmentSchedulePreTriageService.getProtocolByFilters(
                    SchedulePreTriageFilters(person.id)
                )
            } returns SchedulePreTriageResponse(ROOT_NODE_ID).success()

            val result =
                service.getHealthCareTeamSpecialist(person.id, healthProfessional.staffId, lat, lng, appVersion)

            assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(person.id) }
            coVerifyOnce {
                healthProfessionalService.get(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                healthProfessionalService.findByStaffId(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthProfessional.staffId,
                    healthCareTeam.segment
                )
            }
            coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
            coVerifyOnce { appointmentSchedulePreTriageService.getProtocolByFilters(any()) }
        }

    @Test
    fun `getHealthCareTeamSpecialist should build a SpecialistDetailsTransport with scheduleUrl if staff is the physician of healthcare team and appointmentHub is enabled`() =
        runBlocking {
            val healthProfessional = TestModelFactory.buildHealthProfessional()
            val healthCareTeam = TestModelFactory.buildHealthcareTeam(physicianStaffId = healthProfessional.staffId)
            val lat = 0.0
            val lng = 0.0
            val serviceDays = listOf("seg", "ter", "qua")
            val schedulingInfo = SpecialistSchedulingInformationTransport(
                appointmentType = SpecialistAppointmentType.REMOTE,
                schedulingUrl = "url",
                nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthCareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery {
                healthProfessionalService.get(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns NotFoundException().failure()
            coEvery {
                healthProfessionalService.findByStaffId(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            } returns healthProfessional.success()
            coEvery { specialistSchedulingInternalService.getSpecialistServiceDays(healthProfessional.staffId) } returns serviceDays.success()
            coEvery {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthProfessional.staffId,
                    healthCareTeam.segment
                )
            } returns schedulingInfo.success()
            coEvery { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) } returns buildMedicalSpecialty(
                id = healthProfessional.specialtyId!!
            ).success()

            coEvery {
                appointmentScheduleEventService.buildSaraScheduleUrlVersion(
                    any()
                )
            } returns "http://url".success()

            val result =
                service.getHealthCareTeamSpecialist(
                    person.id,
                    healthProfessional.staffId,
                    lat,
                    lng,
                    SemanticVersion("10.0.0")
                )

            assertThat(result).isSuccessOfType(SpecialistDetailsTransport::class)

            coVerifyOnce { personService.get(person.id) }
            coVerifyOnce { healthCareTeamService.getHealthcareTeamByPerson(person.id) }
            coVerifyOnce {
                healthProfessionalService.get(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                healthProfessionalService.findByStaffId(
                    healthProfessional.staffId,
                    HealthProfessionalService.FindOptions(withContact = true)
                )
            }
            coVerifyOnce {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthProfessional.staffId,
                    healthCareTeam.segment
                )
            }
            coVerifyOnce { medicalSpecialtyService.getById(healthProfessional.specialtyId!!) }
            coVerifyOnce { appointmentScheduleEventService.buildSaraScheduleUrlVersion(any()) }
        }
}
