package br.com.alice.member.api.services.beneficiary

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.section.ActionCardSection
import br.com.alice.app.content.model.section.ActionCardSectionType
import br.com.alice.app.content.model.section.ActivationVideoCallSection
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.app.content.model.section.ListMenuSection
import br.com.alice.app.content.model.section.MenuSection
import br.com.alice.app.content.model.section.TabBarSection
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.member.api.models.activation.ActivationItemTransport
import br.com.alice.member.api.models.activation.ActivationScreenTransport
import br.com.alice.member.api.models.activation.VideoCallTransport
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentSchedule
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentStaff
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import kotlin.test.Test
import kotlin.test.assertEquals

class BeneficiaryActivationHomeScreenServiceTest {
    private val service = BeneficiaryActivationHomeScreenService()

    val pendingDependentsTransport = ActivationScreenTransport(
        listOf(
            ActivationItemTransport(
                personId = "1",
                personFirstName = "Dependente 1",
                isMinor = true,
                personIsBeneficiary = false,
                isPending = true,
                beneficiaryNationalId = "1",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
            ActivationItemTransport(
                personId = "2",
                personFirstName = "Dependente 2",
                isMinor = false,
                personIsBeneficiary = false,
                isPending = true,
                beneficiaryNationalId = "2",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
            ActivationItemTransport(
                personId = "3",
                personFirstName = "Dependente 3",
                isMinor = false,
                personIsBeneficiary = true,
                isPending = true,
                beneficiaryNationalId = "3",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
        )
    )

    val completedDependentsTransport = ActivationScreenTransport(
        listOf(
            ActivationItemTransport(
                personId = "1",
                personFirstName = "Dependente 1",
                isMinor = true,
                personIsBeneficiary = false,
                isPending = false,
                beneficiaryNationalId = "1",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
            ActivationItemTransport(
                personId = "2",
                personFirstName = "Dependente 2",
                isMinor = false,
                personIsBeneficiary = false,
                isPending = false,
                beneficiaryNationalId = "2",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
            ActivationItemTransport(
                personId = "3",
                personFirstName = "Dependente 3",
                isMinor = false,
                personIsBeneficiary = true,
                isPending = false,
                beneficiaryNationalId = "3",
                currentPhaseIndex = 0,
                cptsCount = 0,
            ),
        )
    )

    fun mockTransportItem(
        firstName: String = "Person",
        isMinor: Boolean = false,
        isBeneficiary: Boolean = true,
        isPending: Boolean = true,
        beneficiaryNationalId: String = "01234567890",
        currentPhase: BeneficiaryOnboardingPhaseType,
        videoCall: VideoCallTransport? = null,
        cptsCount: Int? = null,
        isDelayedActivation: Boolean = false
    ) =
        ActivationItemTransport(
            personId = PersonId().toString(),
            personFirstName = firstName,
            isMinor = isMinor,
            personIsBeneficiary = isBeneficiary,
            isPending = isPending,
            beneficiaryNationalId = beneficiaryNationalId,
            currentPhaseIndex = currentPhase.order,
            videoCall = videoCall,
            cptsCount = cptsCount ?: 0,
            isDelayedActivation = isDelayedActivation
        )
    @Nested
    inner class HealthDeclarationPhase {
        @Test
        fun `#buildActivationHomeCards should return a Health Declaration card active with no dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videocallActionCard = screen.layout.body[2].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(healthDeclarationActionCard.title, "Declaração de Saúde")
            assertEquals(
                healthDeclarationActionCard.description,
                "Para começar, precisamos entender o panorama geral da sua saúde."
            )
            assertEquals(healthDeclarationActionCard.actionCardBadge?.label, "Pendente")

            // Next phase - Must contain description
            assertEquals(videocallActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(videocallActionCard.title, "Videochamada com enfermagem")
            assertEquals(
                videocallActionCard.description,
                "Nosso time de enfermagem revisará a declaração de saúde com você."
            )
            assertEquals(videocallActionCard.orientationsButton, null)
        }

        @Test
        fun `#buildActivationHomeCards should return a delayed added dependent callout section`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION, isDelayedActivation = true)
                    )
                )
            )

            val dependentCallout = screen.layout.body[1].data as CalloutSection
            val healthDeclarationActionCard = screen.layout.body[2].data as ActionCardSection

            assertEquals(dependentCallout.title, "Novo dependente adicionado ao plano")
            assertEquals(dependentCallout.calloutBody, "Person precisa acessar o aplicativo da Alice com seu próprio CPF para concluir suas tarefas e ativar o plano.")

            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.ACTIVE)
        }

        @Test
        fun `#buildActivationHomeCards should return a delayed added minor dependent callout section`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                            isMinor = true,
                            isDelayedActivation = true
                        )
                    )
                )
            )

            val dependentCallout = screen.layout.body[1].data as CalloutSection

            assertEquals(dependentCallout.title, "Novo dependente adicionado ao plano")
            assertEquals(dependentCallout.calloutBody, "Para concluir a ativação de Person, acesse o aplicativo da Alice com o CPF do dependente.")
        }

        @Test
        fun `#buildActivationHomeCards should return a delayed added dependents callout section`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                            isMinor = true,
                            isDelayedActivation = true
                        ),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                            isDelayedActivation = true
                        )
                    )
                )
            )

            val dependentCallout = screen.layout.body[1].data as CalloutSection

            assertEquals(dependentCallout.title, "Novos dependentes adicionados ao plano")
            assertEquals(dependentCallout.calloutBody, "Para concluir a ativação dos novos dependentes, acesse o aplicativo da Alice com o CPF do dependente.")
        }

        @Test
        fun `#buildActivationHomeCards should return a Health Declaration card active with dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT, isBeneficiary = false),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videocallActionCard = screen.layout.body[2].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(healthDeclarationActionCard.title, "Declaração de Saúde")
            assertEquals(
                healthDeclarationActionCard.description,
                "Para começar, precisamos entender o panorama geral da sua saúde e de seus dependentes."
            )
            assertEquals(healthDeclarationActionCard.actionCardBadge?.label, "1 de 4 concluídos")

            // Next phase - Must contain description
            assertEquals(videocallActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(videocallActionCard.title, "Videochamada com enfermagem")
            assertEquals(
                videocallActionCard.description,
                "Nosso time de enfermagem revisará as declarações de saúde com você e seus dependentes."
            )
        }

        @Test
        fun `#buildActivationHomeCards should return a Health Declaration card active with beneficiary in appointment`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                            isBeneficiary = false,
                            isMinor = true
                        ),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                            isBeneficiary = false,
                            isMinor = false,
                        ),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videoCallActionCard = screen.layout.body[2].data as ActionCardSection
            val dataAnalysisCard = screen.layout.body[3].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(healthDeclarationActionCard.title, "Declaração de Saúde")
            assertEquals(
                healthDeclarationActionCard.description,
                "Para começar, precisamos entender o panorama geral da sua saúde e de seus dependentes."
            )
            assertEquals(healthDeclarationActionCard.actionCardBadge?.label, "1 de 3 concluídos")

            assertEquals(videoCallActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(videoCallActionCard.description, "Nosso time de enfermagem revisará as declarações de saúde com você e seus dependentes.")

            assertEquals(dataAnalysisCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(dataAnalysisCard.description, null)
        }

        @Test
        fun `#buildActivationHomeCards should return a Health Declaration card incomplete`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT, isBeneficiary = false),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videoCallActionCard = screen.layout.body[2].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.INCOMPLETE)
            assertEquals(healthDeclarationActionCard.title, "Declaração de Saúde")
            assertEquals(
                healthDeclarationActionCard.description,
                "Aguardando Person e Person concluírem a tarefa."
            )
            assertEquals(healthDeclarationActionCard.actionCardBadge?.label, "2 de 4 concluídos")

            assertEquals(videoCallActionCard.actionCardType, ActionCardSectionType.ACTIVE)
        }
    }

    @Nested
    inner class VideoCallPhase {
        @Test
        fun `#buildActivationHomeCards should return a VideoCall card active without appointment`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                            videoCall = VideoCallTransport("https://alice.com.br")
                        ),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videoCallActionCard = screen.layout.body[2].data as ActionCardSection
            val waitingCPTsActionCard = screen.layout.body[3].data as ActionCardSection

            //Previous phases
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.COMPLETED)

            // Current phase
            assertEquals(videoCallActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(videoCallActionCard.title, "Videochamada com enfermagem")
            assertEquals(
                videoCallActionCard.description,
                "Nossa enfermagem revisará a declaração de saúde com você. Duração prevista: 30 min."
            )
            assertEquals(videoCallActionCard.actionCardBadge?.label, "Pendente")
            assertEquals(videoCallActionCard.button?.onTapAction?.mobileRoute, ActionRouting.WEBVIEW)
            assertEquals(videoCallActionCard.orientationsButton?.label?.text, "Confira as orientações")

            // Next phase - Must contain description
            assertEquals(waitingCPTsActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(waitingCPTsActionCard.title, "Análise de dados")
            assertEquals(waitingCPTsActionCard.description, "Nesta etapa, analisaremos todas as informações enviadas.")
        }

        @Test
        fun `#buildActivationHomeCards should return a VideoCall card active without appointment, with dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                            videoCall = VideoCallTransport("https://alice.com.br")
                        ),
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                            isMinor = true,
                        ),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videoCallActionCard = screen.layout.body[2].data as ActionCardSection
            val waitingCPTsActionCard = screen.layout.body[3].data as ActionCardSection

            //Previous phases
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.COMPLETED)

            // Current phase
            assertEquals(videoCallActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(videoCallActionCard.title, "Videochamada com enfermagem")
            assertEquals(
                videoCallActionCard.description,
                "Nosso time de enfermagem revisará as declarações de saúde com você e seus dependentes. Duração prevista: 60 min."
            )
            assertEquals(videoCallActionCard.actionCardBadge?.label, "Pendente")
            assertEquals(videoCallActionCard.button?.onTapAction?.mobileRoute, ActionRouting.WEBVIEW)
            assertEquals(videoCallActionCard.orientationsButton?.label?.text, "Confira as orientações")

            // Next phase - Must contain description
            assertEquals(waitingCPTsActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(waitingCPTsActionCard.title, "Análise de dados")
            assertEquals(waitingCPTsActionCard.description, "Nesta etapa, analisaremos todas as informações enviadas.")
        }

        @Test
        fun `#buildActivationHomeCards should return a VideoCall card active with appointment`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(
                            currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED,
                            videoCall = VideoCallTransport(
                                appointment = HealthDeclarationAppointmentSchedule(
                                    HealthDeclarationAppointmentStaff(
                                        id = RangeUUID.generate(),
                                        description = "Enfermeira",
                                        firstName = "Alice",
                                        profileImageUrl = "https://alice.com.br"
                                    ),
                                    cancelUrl = "cancelUrl",
                                    rescheduleUrl = "rescheduleUrl",
                                    startTime = "2025-02-07T18:55:00",
                                    location = "pitaya land"
                                ))
                            ),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED, isBeneficiary = false),
                    )
                )
            )

            val healthDeclarationActionCard = screen.layout.body[1].data as ActionCardSection
            val videoCallActionCard = screen.layout.body[2].data as ActionCardSection
            val waitingCPTsActionCard = screen.layout.body[3].data as ActionCardSection

            val activationVideoCallSection = videoCallActionCard.customContent?.data as ActivationVideoCallSection

            //Previous phases
            assertEquals(healthDeclarationActionCard.actionCardType, ActionCardSectionType.COMPLETED)

            // Current phase
            assertEquals(videoCallActionCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(videoCallActionCard.title, "Videochamada com enfermagem")
            assertEquals(
                videoCallActionCard.description,
                "Antes da videochamada, baixe o aplicativo Zoom na loja de aplicativo do seu celular e finalize o cadastro inserindo o ano de seu nascimento e e-mail."
            )
            assertEquals(videoCallActionCard.orientationsButton?.label?.text, "Confira as orientações")
            assertEquals(videoCallActionCard.actionCardBadge?.label, "Pendente")
            assertEquals(videoCallActionCard.button?.onTapAction, null)
            assertEquals(activationVideoCallSection.cancelUrl, "cancelUrl")
            assertEquals(activationVideoCallSection.rescheduleUrl, "rescheduleUrl")
            assertEquals(activationVideoCallSection.startTime, "2025-02-07T18:55:00")
            assertEquals(activationVideoCallSection.formattedStartTime, "07/02/2025 às 15h55")
            assertEquals(activationVideoCallSection.instructions, "O link da será disponibilizado 30 minutos antes da videochamada.")
            assertEquals(activationVideoCallSection.minimumMinutesToShowLocation, 30)
            assertEquals(activationVideoCallSection.location, "pitaya land")
            assertEquals(activationVideoCallSection.staff?.description, "Enfermeira")
            assertEquals(activationVideoCallSection.staff?.firstName, "Alice")
            assertEquals(activationVideoCallSection.staff?.profileImageUrl, "https://alice.com.br")

            // Next phase - Must contain description
            assertEquals(waitingCPTsActionCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(waitingCPTsActionCard.title, "Análise de dados")
            assertEquals(waitingCPTsActionCard.description, "Nesta etapa, analisaremos todas as informações enviadas.")
        }
    }

    @Nested
    inner class CPTsPhase {
        @Test
        fun `#buildActivationHomeCards should return a Waiting CPTs application card active`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION)
                    )
                )
            )

            val waitingCPTsApplicationCard = screen.layout.body[3].data as ActionCardSection
            val cptsConfirmationCard = screen.layout.body[4].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(waitingCPTsApplicationCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(waitingCPTsApplicationCard.title, "Análise de dados")
            assertEquals(
                waitingCPTsApplicationCard.description,
                "Estamos analisando os dados enviados. Por favor, aguarde a conclusão para prosseguir."
            )
            assertEquals(waitingCPTsApplicationCard.actionCardBadge?.label, "Em andamento")

            // Next phase - Must contain description
            assertEquals(cptsConfirmationCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(cptsConfirmationCard.title, "Cobertura Parcial Temporária")
            assertEquals(
                cptsConfirmationCard.description,
                "Caso uma CPT seja aplicada, você receberá o termo para assinatura."
            )
        }

        @Test
        fun `#buildActivationHomeCards should return a CPTs confirmation card active without dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION)
                    )
                )
            )

            val cptsConfirmationCard = screen.layout.body[4].data as ActionCardSection
            val reviewTermsCard = screen.layout.body[5].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(cptsConfirmationCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(cptsConfirmationCard.title, "Cobertura Parcial Temporária")
            assertEquals(
                cptsConfirmationCard.description,
                "Revise as restrições temporárias aplicadas com base nas respostas da sua declaração de saúde."
            )
            assertEquals(cptsConfirmationCard.actionCardBadge?.label, "Pendente")

            // Next phase - Must contain description
            assertEquals(reviewTermsCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(reviewTermsCard.title, "Revisar e assinar os termos de saúde")
            assertEquals(
                reviewTermsCard.description,
                "Chegou o momento de formalizar as informações das declarações de saúde."
            )
        }

        @Test
        fun `#buildActivationHomeCards should return a CPTs confirmation card active with dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION, cptsCount = 1),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, isBeneficiary = false, cptsCount = 1)
                    )
                )
            )

            val cptsConfirmationCard = screen.layout.body[4].data as ActionCardSection
            val reviewTermsCard = screen.layout.body[5].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(cptsConfirmationCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(cptsConfirmationCard.title, "Cobertura Parcial Temporária")
            assertEquals(
                cptsConfirmationCard.description,
                "Revise as restrições temporárias aplicadas com base nas declarações de saúde."
            )
            assertEquals(cptsConfirmationCard.actionCardBadge?.label, "1 de 2 concluídos")

            // Next phase - Must contain description
            assertEquals(reviewTermsCard.actionCardType, ActionCardSectionType.DISABLED)
            assertEquals(reviewTermsCard.title, "Revisar e assinar os termos de saúde")
            assertEquals(
                reviewTermsCard.description,
                "Chegou o momento de formalizar as informações das declarações de saúde."
            )
        }
    }

    @Nested
    inner class ReviewTermsPhase {
        @Test
        fun `#buildActivationHomeCards should return a Review terms card active without dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE)
                    )
                )
            )

            val reviewTermsCard = screen.layout.body[5].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(reviewTermsCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(reviewTermsCard.title, "Revisar e assinar os termos de saúde")
            assertEquals(
                reviewTermsCard.description,
                "Chegou o momento de você entender um pouco mais sobre a Alice e formalizar sua declaração de saúde."
            )
            assertEquals(reviewTermsCard.actionCardBadge?.label, "Pendente")
        }

        @Test
        fun `#buildActivationHomeCards should return a CPTs confirmation card active with dependents`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.FINISHED, isBeneficiary = false),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE, isBeneficiary = false),
                    )
                )
            )

            val reviewTermsCard = screen.layout.body[5].data as ActionCardSection

            // Current phase
            assertEquals(screen.id, "activation_home")
            assertEquals(screen.layout.body.count(), 6)
            assertEquals(reviewTermsCard.actionCardType, ActionCardSectionType.ACTIVE)
            assertEquals(reviewTermsCard.title, "Revisar e assinar os termos de saúde")
            assertEquals(
                reviewTermsCard.description,
                "Chegou o momento de você entender um pouco mais sobre a Alice e formalizar as declarações de saúde."
            )
            assertEquals(reviewTermsCard.actionCardBadge?.label, "1 de 4 concluídos")
        }
    }

    @Nested
    inner class HealthDeclarationList {
        @Test
        fun `#buildHealthDeclarationListScreen should return a list of pending Health declarations`(): Unit = runBlocking {
            val screen = service.buildHealthDeclarationListScreen(pendingDependentsTransport)

            val listMenuSection = screen.layout.body.first().data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Pendente")
            assertEquals(minorDependentSection.label, "DS de Dependente 1")
            assertEquals(minorDependentSection.icon, "paper")
            assertEquals(minorDependentSection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(adultDependentSection.title, "O dependente maior de idade deve preencher a DS em sua própria conta.")
            assertEquals(adultDependentSection.label, "DS de Dependente 2")
            assertEquals(adultDependentSection.icon, "paper")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(beneficiarySection.title, "Pendente")
            assertEquals(beneficiarySection.label, "Sua Declaração de Saúde (DS)")
            assertEquals(beneficiarySection.icon, "paper")
            assertEquals(beneficiarySection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)
        }

        @Test
        fun `#buildHealthDeclarationListScreen should return a list of finished Health declarations`(): Unit = runBlocking {
            val screen = service.buildHealthDeclarationListScreen(completedDependentsTransport)

            val listMenuSection = screen.layout.body.first().data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Concluída por seu dependente")
            assertEquals(minorDependentSection.label, "DS de Dependente 1")
            assertEquals(minorDependentSection.icon, "check_outlined")
            assertEquals(minorDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(adultDependentSection.title, "Concluída por seu dependente")
            assertEquals(adultDependentSection.label, "DS de Dependente 2")
            assertEquals(adultDependentSection.icon, "check_outlined")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(beneficiarySection.title, "Concluída")
            assertEquals(beneficiarySection.label, "Sua Declaração de Saúde (DS)")
            assertEquals(beneficiarySection.icon, "check_outlined")
            assertEquals(beneficiarySection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)
        }
    }

    @Nested
    inner class CPTList {
        @Test
        fun `#buildCPTsListScreen should return a list of pending CPTs`(): Unit = runBlocking {
            val screen = service.buildCPTsListScreen(pendingDependentsTransport)

            val listMenuSection = screen.layout.body[1].data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Pendente")
            assertEquals(minorDependentSection.label, "CPT de Dependente 1")
            assertEquals(minorDependentSection.icon, "paper")
            assertEquals(minorDependentSection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(adultDependentSection.title, "O dependente maior de idade deve confirmar as CPTs em sua própria conta.")
            assertEquals(adultDependentSection.label, "CPT de Dependente 2")
            assertEquals(adultDependentSection.icon, "paper")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(beneficiarySection.title, "Pendente")
            assertEquals(beneficiarySection.label, "Sua Cobertura Parcial Temporária")
            assertEquals(beneficiarySection.icon, "paper")
            assertEquals(beneficiarySection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)
        }

        @Test
        fun `#buildHealthDeclarationListScreen should return a list of finished Health declarations`(): Unit = runBlocking {
            val screen = service.buildCPTsListScreen(completedDependentsTransport)

            val listMenuSection = screen.layout.body[1].data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Concluída por seu dependente")
            assertEquals(minorDependentSection.label, "CPT de Dependente 1")
            assertEquals(minorDependentSection.icon, "check_outlined")
            assertEquals(minorDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(adultDependentSection.title, "Concluída por seu dependente")
            assertEquals(adultDependentSection.label, "CPT de Dependente 2")
            assertEquals(adultDependentSection.icon, "check_outlined")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(beneficiarySection.title, "Concluída")
            assertEquals(beneficiarySection.label, "Sua Cobertura Parcial Temporária")
            assertEquals(beneficiarySection.icon, "check_outlined")
            assertEquals(beneficiarySection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)
        }
    }

    @Nested
    inner class ReviewTermsList {
        @Test
        fun `#buildReviewTermsListScreen should return a list of pending terms`(): Unit = runBlocking {
            val screen = service.buildReviewTermsListScreen(pendingDependentsTransport)

            val listMenuSection = screen.layout.body.first().data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Pendente")
            assertEquals(minorDependentSection.label, "Termos de Dependente 1")
            assertEquals(minorDependentSection.icon, "paper")
            assertEquals(minorDependentSection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(adultDependentSection.title, "O dependente maior de idade deve revisar seus termos em sua própria conta.")
            assertEquals(adultDependentSection.label, "Termos de Dependente 2")
            assertEquals(adultDependentSection.icon, "paper")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, true)

            assertEquals(beneficiarySection.title, "Pendente")
            assertEquals(beneficiarySection.label, "Seus termos de saúde")
            assertEquals(beneficiarySection.icon, "paper")
            assertEquals(beneficiarySection.enabled, true)
            assertEquals(minorDependentSection.clickAffordance, true)
        }

        @Test
        fun `#buildReviewTermsListScreen should return a list of finished terms`(): Unit = runBlocking {
            val screen = service.buildReviewTermsListScreen(completedDependentsTransport)

            val listMenuSection = screen.layout.body.first().data as ListMenuSection
            val menuSections = listMenuSection.body!!
            val minorDependentSection = menuSections[0].data as MenuSection
            val adultDependentSection = menuSections[1].data as MenuSection
            val beneficiarySection = menuSections[2].data as MenuSection

            assertEquals(menuSections.count(), 3)

            assertEquals(minorDependentSection.title, "Concluída por seu dependente")
            assertEquals(minorDependentSection.label, "Termos de Dependente 1")
            assertEquals(minorDependentSection.icon, "check_outlined")
            assertEquals(minorDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(adultDependentSection.title, "Concluída por seu dependente")
            assertEquals(adultDependentSection.label, "Termos de Dependente 2")
            assertEquals(adultDependentSection.icon, "check_outlined")
            assertEquals(adultDependentSection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)

            assertEquals(beneficiarySection.title, "Concluída")
            assertEquals(beneficiarySection.label, "Seus termos de saúde")
            assertEquals(beneficiarySection.icon, "check_outlined")
            assertEquals(beneficiarySection.enabled, false)
            assertEquals(minorDependentSection.clickAffordance, false)
        }
    }

    @Nested
    inner class ProceedFooter {
        @Test
        fun `#buildActivationHomeCards should return a screen with proceed footer`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.FINISHED),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.FINISHED)
                    )
                )
            )
            val buttonSection = screen.layout.footer?.data as ButtonSection

            assertEquals(screen.layout.footer?.type, SectionType.BUTTON_SECTION)
            assertEquals(buttonSection.button.label?.text, "Avançar")
            assertEquals(buttonSection.button.onTapAction?.mobileRoute, ActionRouting.B2B_ACTIVATION)
        }

        @Test
        fun `#buildActivationHomeCards should return a screen without proceed footer`(): Unit = runBlocking {
            val screen = service.buildActivationHomeCards(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.FINISHED)
                    )
                )
            )

            assertEquals(screen.layout.footer, null)
        }
    }

    @Nested
    inner class VideocallInstructions {
        @Test
        fun `#buildVideoCallInstructionsScreen should return a screen with benecifiary instructions`(): Unit = runBlocking {
            val screen = service.buildVideoCallInstructionsScreen(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT),
                    )
                )
            )

            val aboutVideoCallText = screen.layout.body.first().data as TextSection.Content
            assertEquals(aboutVideoCallText.content.title, "Sobre a videochamada")
        }

        @Test
        fun `#buildVideoCallInstructionsScreen should return a screen with beneficiary and dependent tabs`(): Unit = runBlocking {
            val screen = service.buildVideoCallInstructionsScreen(
                ActivationScreenTransport(
                    listOf(
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT),
                        mockTransportItem(currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT)
                    )
                )
            )

            val tabBarSection = screen.layout.body.first().data as TabBarSection

            assertEquals(tabBarSection.tabBarItems.count(), 2)
            assertEquals(tabBarSection.tabBarItems[0].content, "Geral")
            assertEquals(tabBarSection.tabBarItems[1].content, "Dependentes")
        }
    }
}
