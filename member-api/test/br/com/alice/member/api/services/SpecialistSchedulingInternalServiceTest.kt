package br.com.alice.member.api.services

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.beginningOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.endOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildAppointmentScheduleOption
import br.com.alice.data.layer.helpers.TestModelFactory.buildPersonCalendly
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.ReferralSpecialty
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.SuggestedSpecialist
import br.com.alice.data.layer.models.copy
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.accreditedNetwork.SpecialistSchedulingInformationTransport
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleOptionFilters
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.wanda.client.AppointmentCoordinationService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.getFailureOrNull
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class SpecialistSchedulingInternalServiceTest {

    private val staffAvailabilityService: StaffAvailabilityService = mockk()
    private val staffScheduleService: StaffScheduleService = mockk()
    private val schedulingUrlBuilder: SchedulingUrlBuilder = mockk()
    private val actionPlanTaskService: ActionPlanTaskService = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val appointmentCoordinationService: AppointmentCoordinationService = mockk()
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()

    private val service = SpecialistSchedulingInternalService(
        staffAvailabilityService,
        staffScheduleService,
        schedulingUrlBuilder,
        actionPlanTaskService,
        appointmentScheduleService,
        appointmentCoordinationService,
        appointmentScheduleOptionService,
        personCalendlyService,
        appointmentScheduleEventTypeService
    )

    private val mockedLocalDateTime = LocalDateTime.now()

    private val appointmentScheduleEventTypeId = RangeUUID.generate()
    private val appointmentScheduleEventType =
        TestModelFactory.buildAppointmentScheduleEventType(id = appointmentScheduleEventTypeId)
    private val staff = TestModelFactory.buildStaff()

    private val person = TestModelFactory.buildPerson()
    private val specialtyId = RangeUUID.generate()
    val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
        personId = person.id,
        specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
    )

    @Test
    fun `#getSpecialistRemoteScheduleInformation should return correct SpecialistScheduleInformationTransport`() =
        runBlocking {
            withFeatureFlags(
                namespace = FeatureNamespace.MEMBERSHIP,
                keyValueMap = mapOf(
                    "pediatric_remote_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "pediatric_presential_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "default_remote_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "default_presential_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString()
                )
            ) {
                val expectedUrl = "http://alice.com.br"
                val listOfAvailableSlots = listOf(
                    SlotForSpecificDuration(
                        startTime = mockedLocalDateTime,
                        endTime = mockedLocalDateTime.plusMinutes(20),
                        durationInMinutes = 20,
                    ),
                    SlotForSpecificDuration(
                        startTime = mockedLocalDateTime.minusDays(1),
                        endTime = mockedLocalDateTime.minusDays(1).plusMinutes(20),
                        durationInMinutes = 20,
                    ),
                    SlotForSpecificDuration(
                        startTime = mockedLocalDateTime.minusHours(1),
                        endTime = mockedLocalDateTime.minusHours(1).plusMinutes(20),
                        durationInMinutes = 20,
                    )
                )

                coEvery {
                    schedulingUrlBuilder.buildScheduleUrlLight(
                        calendarUrl = null,
                        extras = mapOf(
                            "staff_id" to staff.id.toString(),
                            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString()
                        ),
                        shouldUseInternalScheduler = true
                    )
                } returns expectedUrl

                coEvery {
                    appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
                } returns appointmentScheduleEventType.success()

                coEvery {
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                        staffId = staff.id,
                        startDateTime = any(),
                        endDateTime = any(),
                        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                        appointmentScheduleEventType = appointmentScheduleEventType
                    )
                } returns listOfAvailableSlots.success()

                val result =
                    service.getSpecialistRemoteScheduleInformation(
                        staffId = staff.id,
                        segment = HealthcareTeam.Segment.DEFAULT
                    )

                val expected = SpecialistSchedulingInformationTransport(
                    appointmentType = SpecialistAppointmentType.REMOTE,
                    schedulingUrl = expectedUrl,
                    nextAvailableDate = listOfAvailableSlots[1].startTime
                )

                assertThat(result).isSuccess()
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { schedulingUrlBuilder.buildScheduleUrlLight(any(), any(), any()) }
                coVerifyOnce {
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }

    @Test
    fun `#getSpecialistRemoteScheduleInformation should return null with no appointmentScheduleEventTypeId`() =
        runBlocking {
            val result = service.getSpecialistRemoteScheduleInformation(
                staff.id,
                HealthcareTeam.Segment.PEDIATRIC
            )

            assertThat(result).isFailure()

            coVerifyNone { schedulingUrlBuilder.buildScheduleUrlLight(any(), any(), any()) }
            coVerifyNone {
                staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#getSpecialistRemoteScheduleInformation handles no data gracefully`() =
        runBlocking {
            withFeatureFlags(
                namespace = FeatureNamespace.MEMBERSHIP,
                keyValueMap = mapOf(
                    "pediatric_remote_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "pediatric_presential_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "default_remote_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString(),
                    "default_presential_appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString()
                )
            ) {
                val expectedUrl = "http://alice.com.br"

                coEvery {
                    schedulingUrlBuilder.buildScheduleUrlLight(
                        calendarUrl = null,
                        extras = mapOf(
                            "staff_id" to staff.id.toString(),
                            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId.toString()
                        ),
                        shouldUseInternalScheduler = true
                    )
                } returns expectedUrl

                coEvery {
                    appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
                } returns appointmentScheduleEventType.success()

                coEvery {
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                        staffId = staff.id,
                        startDateTime = any(),
                        endDateTime = any(),
                        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                        appointmentScheduleEventType = appointmentScheduleEventType
                    )
                } returns NotFoundException().failure()

                val result =
                    service.getSpecialistRemoteScheduleInformation(
                        staffId = staff.id,
                        segment = HealthcareTeam.Segment.DEFAULT
                    )

                val expected = SpecialistSchedulingInformationTransport(
                    appointmentType = SpecialistAppointmentType.REMOTE,
                    schedulingUrl = expectedUrl,
                    nextAvailableDate = null
                )

                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { schedulingUrlBuilder.buildScheduleUrlLight(any(), any(), any()) }
                coVerifyOnce {
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }

    @Test
    fun `#getSpecialistServiceDays should return correct list of days`() = runBlocking {
        coEvery { staffScheduleService.getStaffSchedules(staff.id) } returns listOf(
            TestModelFactory.buildStaffSchedule(weekDay = WeekDay.MONDAY),
            TestModelFactory.buildStaffSchedule(weekDay = WeekDay.TUESDAY),
        ).success()

        val result = service.getSpecialistServiceDays(staff.id)
        val expectedServiceDays = listOf("seg", "ter")

        assertThat(result).isSuccessWithData(expectedServiceDays)

        coVerifyOnce { staffScheduleService.getStaffSchedules(staff.id) }
    }

    @Test
    fun `#getSpecialistServiceDays should return empty list when no data is found`() = runBlocking {
        coEvery { staffScheduleService.getStaffSchedules(staff.id) } returns emptyList<StaffSchedule>().success()

        val result = service.getSpecialistServiceDays(staff.id)

        assertThat(result).isSuccessWithData(emptyList())

        coVerifyOnce { staffScheduleService.getStaffSchedules(staff.id) }
    }

    @Test
    fun `#getSpecialistServiceDays should return empty list when NotFoundException is thrown`() = runBlocking {
        coEvery { staffScheduleService.getStaffSchedules(staff.id) } returns NotFoundException("Not found").failure()

        val result = service.getSpecialistServiceDays(staff.id)

        assertThat(result).isFailure()

        coVerifyOnce { staffScheduleService.getStaffSchedules(staff.id) }
    }

    @Test
    fun `getUniquePlanTask should by apt id`() = runBlocking {
        coEvery {
            actionPlanTaskService.getById(actionPlanTask.id)
        } returns actionPlanTask.success()

        val result = service.getUniquePlanTask(person.id, actionPlanTask.id, specialtyId)

        assertThat(result).isSuccessWithData(actionPlanTask.specialize())

        coVerifyOnce { actionPlanTaskService.getById(any()) }
    }

    @Test
    fun `getUniquePlanTask should return unique apt when apt is null`() = runBlocking {
        coEvery {
            actionPlanTaskService.getTasksByFilters(
                ActionPlanTaskFilters(
                    personId = person.id,
                    types = listOf(ActionPlanTaskType.REFERRAL),
                ).withTodoStatuses()
            )
        } returns listOf(actionPlanTask).success()

        val result = service.getUniquePlanTask(person.id, null, specialtyId)

        assertThat(result).isSuccessWithData(actionPlanTask.specialize())

        coVerifyOnce { actionPlanTaskService.getTasksByFilters(any()) }
        coVerifyNone { actionPlanTaskService.getById(any()) }
    }

    @Test
    fun `getUniquePlanTask should return error when not found APT`() = runBlocking {
        coEvery {
            actionPlanTaskService.getTasksByFilters(
                ActionPlanTaskFilters(
                    personId = person.id,
                    types = listOf(ActionPlanTaskType.REFERRAL),
                ).withTodoStatuses()
            )
        } returns emptyList<ActionPlanTask>().success()

        val result = service.getUniquePlanTask(person.id, null, specialtyId)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { actionPlanTaskService.getTasksByFilters(any()) }
        coVerifyNone { actionPlanTaskService.getByHealthPlanTask(any()) }
    }

    @Test
    fun `getUniquePlanTask should return error when has many APT`() = runBlocking {
        val action2 = actionPlanTask.copy(id = RangeUUID.generate())
        coEvery {
            actionPlanTaskService.getTasksByFilters(
                ActionPlanTaskFilters(
                    personId = person.id,
                    types = listOf(ActionPlanTaskType.REFERRAL),
                ).withTodoStatuses()
            )
        } returns listOf(actionPlanTask, action2).success()

        val result = service.getUniquePlanTask(person.id, null, specialtyId)

        assertThat(result).isFailureOfType(ManyActionPlanTaskException::class)

        coVerifyOnce { actionPlanTaskService.getTasksByFilters(any()) }
    }

    @Test
    fun `getUniquePlanTask should return no error when there are multiple APTs but only one is ACTIVE, ACTIVE_ON_GOING or OVERDUE`() =
        runBlocking {
            val actionPlanTaskActive = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 1,
                status = ActionPlanTaskStatus.ACTIVE
            )
            val actionPlanTaskScheduleScheduled = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 1,
                status = ActionPlanTaskStatus.SCHEDULED
            )

            coEvery {
                actionPlanTaskService.getTasksByFilters(
                    ActionPlanTaskFilters(
                        personId = person.id,
                        types = listOf(ActionPlanTaskType.REFERRAL),
                    ).withTodoStatuses()
                )
            } returns listOf(actionPlanTaskScheduleScheduled, actionPlanTaskActive).success()

            val result = service.getUniquePlanTask(person.id, null, specialtyId)

            assertThat(result).isSuccessWithData(actionPlanTaskActive.specialize())

            coVerifyOnce { actionPlanTaskService.getTasksByFilters(any()) }
        }

    @Nested
    inner class GetScheduleInformation {
        private val healthProfessional =
            TestModelFactory.buildHealthProfessional(id = staff.id, staffId = staff.id, specialtyId = specialtyId)

        private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
            status = AppointmentScheduleStatus.SCHEDULED
        ).copy(healthPlanTaskId = actionPlanTask.id)

        private val appointmentScheduleOption = buildAppointmentScheduleOption(
            staffId = healthProfessional.staffId,
            specialtyId = specialtyId,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId
        )

        private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination(
            healthPlanTaskId = actionPlanTask.id
        )
        private val personCalendly = buildPersonCalendly(person.id)
        private val now = LocalDateTime.now()


        @Test
        fun `should return error when suggestion specialist is different`() = runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                suggestedSpecialist = SuggestedSpecialist(
                    id = RangeUUID.generate(),
                    name = "Other Specialist",
                    type = SpecialistType.STAFF
                )
            )

            val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
            assertThat(result).isFailureOfType(SuggestionSpecialistIsDifferentException::class)
        }

        @Test
        fun `should return error when specialty is not allowed to schedule`() = runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.MEMBERSHIP,
                key = "list_of_specialties_without_scheduling_option",
                value = listOf(healthProfessional.specialtyId),
            ) {
                val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                    personId = person.id,
                    specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                    suggestedSpecialist = SuggestedSpecialist(
                        id = RangeUUID.generate(),
                        name = "Other Specialist",
                        type = SpecialistType.STAFF
                    )
                )

                val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
                assertThat(result)
                    .isFailureOfType(IllegalStateException::class)
            }
        }

        @Test
        fun `should return error when APT has not many session and APT is schedule`() = runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 1,
                status = ActionPlanTaskStatus.SCHEDULED
            )

            val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
            assertThat(result).isFailureOfType(IllegalStateException::class)
            assertEquals("Referral should not schedule", result.getFailureOrNull()!!.message)
        }

        @Test
        fun `should return error when schedule option by staff is not exists`() = runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 3,
                status = ActionPlanTaskStatus.SCHEDULED
            )
            coEvery {
                actionPlanTaskService.getById(actionPlanTask.id)
            } returns actionPlanTask.success()
            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = actionPlanTask.id,
                        status = listOf(
                            AppointmentScheduleStatus.SCHEDULED
                        ),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(appointmentSchedule).success()
            coEvery {
                appointmentCoordinationService.getByTaskId(actionPlanTask.personId, actionPlanTask.id)
            } returns listOf(appointmentCoordination).success()

            coEvery {
                appointmentScheduleOptionService.findBy(
                    AppointmentScheduleOptionFilters(
                        staffIds = listOf(healthProfessional.staffId),
                        specialtyId = specialtyId,
                        active = true
                    )
                )
            } returns emptyList<AppointmentScheduleOption>().success()

            val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
            assertThat(result).isFailureOfType(NotFoundException::class)
            assertEquals("Option not found", result.getFailureOrNull()!!.message)

            coVerifyOnce { appointmentScheduleOptionService.findBy(any()) }
        }

        @Test
        fun `should return error when APT has many session and all session is completed`() = runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 2
            )

            coEvery {
                actionPlanTaskService.getById(actionPlanTask.id)
            } returns actionPlanTask.success()
            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = actionPlanTask.id,
                        status = listOf(
                            AppointmentScheduleStatus.SCHEDULED
                        ),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(appointmentSchedule).success()
            coEvery {
                appointmentCoordinationService.getByTaskId(actionPlanTask.personId, actionPlanTask.id)
            } returns listOf(appointmentCoordination).success()

            val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
            assertThat(result).isFailureOfType(IllegalStateException::class)
            assertEquals("All sessions are completed", result.getFailureOrNull()!!.message)

            coVerifyOnce { appointmentScheduleService.findBy(any()) }
            coVerifyOnce { appointmentCoordinationService.getByTaskId(any(), any()) }
        }

        @Test
        fun `should return schedule information`() = runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTaskReferral(
                personId = person.id,
                specialty = ReferralSpecialty(id = specialtyId, name = "Specialty"),
                sessionsQuantity = 3
            )
            val slots = listOf(
                SlotForSpecificDuration(
                    startTime = now,
                    endTime = now.plusMinutes(30),
                    durationInMinutes = 30,
                )
            )

            coEvery {
                appointmentScheduleOptionService.findBy(
                    AppointmentScheduleOptionFilters(
                        staffIds = listOf(healthProfessional.staffId),
                        specialtyId = specialtyId,
                        active = true
                    )
                )
            } returns listOf(appointmentScheduleOption).success()

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = actionPlanTask.id,
                        status = listOf(
                            AppointmentScheduleStatus.SCHEDULED
                        ),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(appointmentSchedule).success()
            coEvery {
                appointmentCoordinationService.getByTaskId(actionPlanTask.personId, actionPlanTask.id)
            } returns listOf(appointmentCoordination).success()

            coEvery { personCalendlyService.getOrCreate(personId = person.id) } returns personCalendly.success()
            coEvery {
                schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                    person = person,
                    appointmentScheduleOption = appointmentScheduleOption,
                    personCalendly = personCalendly,
                    extras = mapOf(
                        "health_plan_task_id" to actionPlanTask.id.toString().toSha256()
                    ),
                )
            } returns "http://alice.com.br"

            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            } returns appointmentScheduleEventType.success()

            coEvery {
                staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                    staffId = healthProfessional.staffId,
                    startDateTime = any(),
                    endDateTime = any(),
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType
                )
            } returns slots.success()

            val result = service.getScheduleInformation(person, healthProfessional, actionPlanTask.specialize())
            assertThat(result).isSuccessWithData(
                SpecialistSchedulingInformationTransport(
                    schedulingUrl = "http://alice.com.br",
                    appointmentType = SpecialistAppointmentType.REMOTE,
                    nextAvailableDate = now,
                )
            )

            coVerifyOnce { appointmentScheduleOptionService.findBy(any()) }
            coVerifyOnce { appointmentScheduleService.findBy(any()) }
            coVerifyOnce { appointmentCoordinationService.getByTaskId(any(), any()) }
        }
    }

    @Test
    fun `#getNextNAvailableDatetimeForSpecialistByEventType should return correct list of next available dates`() =
        runBlocking {
            mockLocalDateTime { now ->
                withFeatureFlag(
                    namespace = FeatureNamespace.MEMBERSHIP,
                    key = "months_threshold_to_schedule",
                    value = 3
                ) {
                    val staffId = RangeUUID.generate()
                    val appointmentScheduleEventTypeId = RangeUUID.generate()
                    val slots = listOf(
                        SlotForSpecificDuration(
                            startTime = now,
                            endTime = now.plusMinutes(30),
                            durationInMinutes = 30,
                        ),
                        SlotForSpecificDuration(
                            startTime = now.plusDays(1),
                            endTime = now.plusDays(1).plusMinutes(30),
                            durationInMinutes = 30,
                        ),
                        SlotForSpecificDuration(
                            startTime = now.plusDays(2),
                            endTime = now.plusDays(2).plusMinutes(30),
                            durationInMinutes = 30,
                        )
                    )

                    val appointmentScheduleEventType =
                        TestModelFactory.buildAppointmentScheduleEventType(id = appointmentScheduleEventTypeId)

                    coEvery {
                        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
                    } returns appointmentScheduleEventType
                        .success()

                    val startDateTime = beginningOfTheMonthWithSaoPauloTimezone(now).atStartOfDay()
                    val endDateTime = endOfTheMonthWithSaoPauloTimezone(now.plusMonths(3)).atEndOfTheDay()

                    coEvery {
                        staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                            staffId = staffId,
                            startDateTime = startDateTime,
                            endDateTime = endDateTime,
                            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                            appointmentScheduleEventType = appointmentScheduleEventType,
                        )
                    } returns slots.success()

                    val result =
                        service.getNextNAvailableDatetimeForSpecialistByEventType(
                            staffId,
                            appointmentScheduleEventTypeId,
                            2
                        )

                    val expectedDates = listOf(slots[0].startTime, slots[1].startTime)

                    assertThat(result).isSuccess()
                    assertThat(result).isSuccessWithData(expectedDates)

                    coVerifyOnce {
                        staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                            staffId = staffId,
                            startDateTime = startDateTime,
                            endDateTime = endDateTime,
                            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                            appointmentScheduleEventType = appointmentScheduleEventType,
                        )
                    }
                    coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
                }
            }
        }
}
