package br.com.alice.member.api.utils

import br.com.alice.common.models.Gender
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeam
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class TitleGeneratorTest {
    private val femalePerson =
        TestModelFactory.buildPerson(nickName = null, firstName = "Maria", gender = Gender.FEMALE)
    private val femalePersonWithNickName = femalePerson.copy(nickName = "Mariazinha")

    private val malePerson = TestModelFactory.buildPerson(nickName = null, firstName = "João", gender = Gender.MALE)
    private val malePersonWithNickName = malePerson.copy(nickName = "Joãozinho")

    private val neutralPerson =
        TestModelFactory.buildPerson(nickName = null, firstName = "Neutro", gender = Gender.NO_ANSWER)
    private val neutralPersonWithNickName = neutralPerson.copy(nickName = "Neutrinho")

    @Test
    fun `#accredited network physician description by segment with female person without nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, femalePerson
        )
        assertEquals("Pediatra da Maria", result)
    }

    @Test
    fun `#accredited network physician description pediatric with female person with nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.MALE, HealthcareTeam.Segment.PEDIATRIC, femalePersonWithNickName
        )
        assertEquals("Pediatra da Mariazinha", result)
    }

    @Test
    fun `#accredited network physician description pediatric with male person without nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, malePerson
        )
        assertEquals("Pediatra do João", result)
    }

    @Test
    fun `#accredited network physician description pediatric with male person with nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.MALE, HealthcareTeam.Segment.PEDIATRIC, malePersonWithNickName
        )
        assertEquals("Pediatra do Joãozinho", result)
    }

    @Test
    fun `#accredited network physician description pediatric with neutral person without nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, neutralPerson
        )
        assertEquals("Pediatra de Neutro", result)
    }

    @Test
    fun `#accredited network physician description pediatric with neutral person with nickname`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.MALE, HealthcareTeam.Segment.PEDIATRIC, neutralPersonWithNickName
        )
        assertEquals("Pediatra de Neutrinho", result)
    }

    @Test
    fun `#accredited network physician description default segment with female professional`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("Sua Médica de Família", result)
    }

    @Test
    fun `#accredited network physician description default segment with male professional`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.MALE, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("Seu Médico de Família", result)
    }

    @Test
    fun `#accredited network physician description default segment with neutral professional`() {
        val result = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            Gender.NON_BINARY, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("Seu Médico de Família", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with female person without nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, femalePerson
        )
        assertEquals("pediatra da Maria", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with female person with nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, femalePersonWithNickName
        )
        assertEquals("pediatra da Mariazinha", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with male person without nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, malePerson
        )
        assertEquals("pediatra do João", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with male person with nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, malePersonWithNickName
        )
        assertEquals("pediatra do Joãozinho", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with neutral person without nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, neutralPerson
        )
        assertEquals("pediatra de Neutro", result)
    }

    @Test
    fun `#appointment card physician description pediatric segment with neutral person with nickname`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.PEDIATRIC, neutralPersonWithNickName
        )
        assertEquals("pediatra de Neutrinho", result)
    }

    @Test
    fun `#appointment card physician description default segment with female professional`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.FEMALE, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("minha médica de família", result)
    }

    @Test
    fun `#appointment card physician description default segment with male professional`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.MALE, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("meu médico de família", result)
    }

    @Test
    fun `#appointment card physician description default segment with neutral professional`() {
        val result = TitleGenerator.getAppointmentCardPhysicianDescriptionBySegment(
            Gender.NON_BINARY, HealthcareTeam.Segment.DEFAULT, femalePerson
        )
        assertEquals("meu médico de família", result)
    }


}
