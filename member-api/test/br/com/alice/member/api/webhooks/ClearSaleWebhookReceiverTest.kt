package br.com.alice.member.api.webhooks

import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonIdentityValidationStatus
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.webhooks.clearsale.BiometricScoreRequest
import br.com.alice.member.api.models.webhooks.clearsale.FacematchRequest
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.person.client.PersonIdentityValidationService
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test


class ClearSaleWebhookReceiverTest : RoutesTestHelper() {
    private val authorizationHeader = mapOf("Authorization" to "Bearer alice")

    private val personIdentityValidationService: PersonIdentityValidationService = mockk()
    private val clearSaleWebhookReceiver = ClearSaleWebhookReceiver(personIdentityValidationService)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { clearSaleWebhookReceiver }
    }

    companion object {

        @BeforeAll
        @JvmStatic
        fun classSetup() {
            mockkObject(AppStateNotifier)
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            unmockkObject(AppStateNotifier)
        }
    }


    @Nested
    inner class BiometricScoreReceiver {

        @Test
        fun `should receive the biometric score from clear sale and valid status from service`() = mockLocalDateTime { now ->
            val transactionId = "transactionId"
            val body = BiometricScoreRequest(transactionId)
            val personIdentityValidation =
                TestModelFactory.buildPersonIdentityValidation(status = PersonIdentityValidationStatus.VALID)

            coEvery {
                personIdentityValidationService
                    .handleBiometricScoreNotification(transactionId, now)
            } returns personIdentityValidation


            every {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            } returns Unit

            internalAuthentication {
                post("/webhooks/clear_sale/notify_biometric_score", body = body, headers = authorizationHeader) {
                    ResponseAssert.assertThat(it).isOK()
                }
            }

            coVerifyOnce {
                personIdentityValidationService
                    .handleBiometricScoreNotification(transactionId, now)
            }

            coVerifyOnce {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            }
        }

        @Test
        fun `should receive the biometric score from clear sale and invalid status from service`() = mockLocalDateTime { now ->
            val transactionId = "transactionId"
            val body = BiometricScoreRequest(transactionId)
            val personIdentityValidation =
                TestModelFactory.buildPersonIdentityValidation(status = PersonIdentityValidationStatus.INVALID)

            coEvery {
                personIdentityValidationService
                    .handleBiometricScoreNotification(transactionId, now)
            } returns personIdentityValidation

            coEvery {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            } returns Unit


            internalAuthentication {
                post("/webhooks/clear_sale/notify_biometric_score", body = body, headers = authorizationHeader) {
                    ResponseAssert.assertThat(it).isOK()
                }
            }

            coVerifyOnce {
                personIdentityValidationService
                    .handleBiometricScoreNotification(transactionId, now)

                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            }
        }

        @Test
        fun `#should return unauthorized error when authorization header is incorrect`() = runBlocking {
            val now = LocalDateTime.now()
            val transactionId = "transactionId"
            val body = BiometricScoreRequest(transactionId)

            internalAuthentication {
                post(
                    "/webhooks/clear_sale/notify_biometric_score",
                    body = body,
                    headers = mapOf("Authorization" to "Basic emVuZGVza19hdXRoX3VzZXI6YytvcjopYlRkcCYqeg==")
                ) { response ->
                    ResponseAssert.assertThat(response).isUnauthorized()
                }
            }
        }
    }

    @Nested
    inner class FacematchReceiver {

        @Test
        fun `should receive the facematch from clear sale and valid status from service`() {
            val transactionId = "transactionId"
            val body = FacematchRequest(transactionId, 2, "Success", true)
            val personIdentityValidation =
                TestModelFactory.buildPersonIdentityValidation(status = PersonIdentityValidationStatus.VALID)

            coEvery {
                personIdentityValidationService
                    .handleFacematchNotification(transactionId, 2, true)
            } returns personIdentityValidation


            every {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            } returns Unit

            internalAuthentication {
                post("/webhooks/clear_sale/notify_facematch", body = body, headers = authorizationHeader) {
                    ResponseAssert.assertThat(it).isOK()
                }
            }

            coVerifyOnce {
                personIdentityValidationService
                    .handleFacematchNotification(transactionId, 2, true)
            }

            coVerifyOnce {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            }
        }

        @Test
        fun `should receive the facematch result from clear sale and invalid status from service`() {
            val transactionId = "transactionId"
            val body = FacematchRequest(transactionId, 2, "Error", true)
            val personIdentityValidation =
                TestModelFactory.buildPersonIdentityValidation(status = PersonIdentityValidationStatus.INVALID)

            coEvery {
                personIdentityValidationService
                    .handleFacematchNotification(transactionId, 2, false)
            } returns personIdentityValidation

            coEvery {
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            } returns Unit


            internalAuthentication {
                post("/webhooks/clear_sale/notify_facematch", body = body, headers = authorizationHeader) {
                    ResponseAssert.assertThat(it).isOK()
                }
            }

            coVerifyOnce {
                personIdentityValidationService
                    .handleFacematchNotification(transactionId, 2, false)
                AppStateNotifier.updateAppState(
                    personIdentityValidation.personId,
                    AppState.IDENTITY_VALIDATION_UPDATED
                )
            }
        }

        @Test
        fun `should receive the facematch result from clear sale and dont call the service when status is processing`() {
            val transactionId = "transactionId"
            val body = FacematchRequest(transactionId, 0, "Processing", false)

            internalAuthentication {
                post("/webhooks/clear_sale/notify_facematch", body = body, headers = authorizationHeader) {
                    ResponseAssert.assertThat(it).isOK()
                }
            }

            coVerifyNone {
                personIdentityValidationService
                    .handleFacematchNotification(any(), any(), any())
                AppStateNotifier.updateAppState(
                    any(),
                    any(),
                )
            }
        }

        @Test
        fun `#should return unauthorized error when authorization header is incorrect`() = runBlocking {
            val transactionId = "transactionId"
            val body = BiometricScoreRequest(transactionId)

            internalAuthentication {
                post(
                    "/webhooks/clear_sale/notify_biometric_score",
                    body = body,
                    headers = mapOf("Authorization" to "Basic emVuZGVza19hdXRoX3VzZXI6YytvcjopYlRkcCYqeg==")
                ) { response ->
                    ResponseAssert.assertThat(response).isUnauthorized()
                }
            }
        }
    }
}
