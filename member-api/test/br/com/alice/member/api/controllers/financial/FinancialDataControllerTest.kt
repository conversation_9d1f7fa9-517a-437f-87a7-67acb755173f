package br.com.alice.member.api.controllers.financial

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.moneyin.client.FinancialDataService
import br.com.alice.moneyin.models.FinancialDataRequest
import br.com.alice.moneyin.models.FinancialDataResponse
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class FinancialDataControllerTest : RoutesTestHelper() {

    private val financialDataService: FinancialDataService = mockk()
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    private val financialDataId = RangeUUID.generate()
    private val financialDataResponse = FinancialDataResponse(financialDataId)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            FinancialDataController(
                financialDataService
            )
        }
    }

    @Test
    fun `#create should return 200 OK with financial data info`() {
        val request = FinancialDataRequest(
            accountHolderId = person.id.toUUID(),
            bankCode = "000",
            bankName = "Nubank S/A",
            branch = "0000",
            accountNumber = "000000",
            accountNickname = "Teste",
            accountHolderNationalId = "***********"
        )

        coEvery {
            financialDataService.create(
                request
            )
        } returns financialDataResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/financial_data", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(financialDataResponse)
            }
        }

        coVerifyOnce { financialDataService.create(any()) }
    }

    @Test
    fun `#delete should return 200 OK with financial data info`() {
        coEvery {
            financialDataService.delete(financialDataId)
        } returns financialDataResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            delete("/financial_data/${financialDataId}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(financialDataResponse)
            }
        }

        coVerifyOnce { financialDataService.delete(any()) }
    }

}
