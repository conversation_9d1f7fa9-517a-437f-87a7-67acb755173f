package br.com.alice.member.api.converters

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.converters.HealthCommunitySpecialistConverter
import br.com.alice.member.api.converters.SpecialtyReferralResponseConverterV2
import br.com.alice.member.api.models.SpecialtyReferralResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SpecialtyReferralResponseConverterV2Test {

    @Test
    fun `#convert returns the correct response`() {
        val specialty = "Ortopedia"
        val specialists = listOf(
            TestModelFactory.buildHealthCommunitySpecialist(name = "<PERSON>"),
            TestModelFactory.buildHealthCommunitySpecialist(name = "<PERSON>")
        )

        val expected = SpecialtyReferralResponse(
            title = "Escolha o profissional de sua preferência",
            providers = specialists.map {
                HealthCommunitySpecialistConverter.convert(it, specialty, emptyList())
            }
        )

        val actual = SpecialtyReferralResponseConverterV2.convert(
            specialists, specialty, emptyMap()
        )

        assertThat(actual).isEqualTo(expected)
    }
}
