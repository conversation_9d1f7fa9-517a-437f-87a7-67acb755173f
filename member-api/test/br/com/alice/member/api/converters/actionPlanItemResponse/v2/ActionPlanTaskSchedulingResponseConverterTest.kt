package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.EmergencyNew
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.data.layer.models.StartType
import br.com.alice.data.layer.models.SuggestedSpecialist
import br.com.alice.data.layer.models.TestRequestNew
import br.com.alice.data.layer.models.copy
import br.com.alice.member.api.models.HealthPlanItemCalendar
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.Navigation
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ActionPlanTaskSchedulingResponseConverterTest {
    private val person = TestModelFactory.buildPerson()

    private val actionPlanTaskCalendarBuilder: ActionPlanTaskCalendarBuilder = mockk()
    private val actionPlanTaskSchedulingResponseConverter = ActionPlanTaskSchedulingResponseConverter(
        actionPlanTaskCalendarBuilder
    )

    private val actionPlanTask = TestModelFactory.buildActionPlanTask(
        personId = person.id,
        type = ActionPlanTaskType.TEST_REQUEST,
        description = "Exames Laboratóriais",
    )

    val calendar = HealthPlanItemCalendar()

    private val testRequestTask =
        TestRequestNew(
            task = actionPlanTask,
            code = "1234",
        )

    private val referralActionPlanTask = TestModelFactory.buildActionPlanTask(
        personId = person.id,
        id = RangeUUID.generate(),
        title = "Consulta com o Nutricionista",
        description = "Para melhorar sua alimentação",
        dueDate = null,
        status = ActionPlanTaskStatus.ACTIVE,
        deadline = Deadline(PeriodUnit.WEEK, 1),
        start = StartType.IMMEDIATE,
        type = ActionPlanTaskType.REFERRAL,
        favorite = false,
    )
    private val referralTask =
        ReferralNew(
            task = referralActionPlanTask,
            diagnosticHypothesis = "Está com colesterol em nível perigoso",
            suggestedSpecialist = SuggestedSpecialist(
                name = "Mariana",
                type = SpecialistType.STAFF,
                id = RangeUUID.generate()
            ),
        )

    private val advancedAccessTask = ReferralNew(
        task = referralActionPlanTask.copy(
            content = mapOf("isAdvancedAccess" to true).plus(referralActionPlanTask.content ?: emptyMap()),
        ),
        diagnosticHypothesis = "Está com colesterol em nível perigoso - Acesso Avançado",
        suggestedSpecialist = SuggestedSpecialist(
            name = "Mariana",
            type = SpecialistType.STAFF,
            id = RangeUUID.generate()
        ),
        isAdvancedAccess = true,
    )

    @Test
    fun `#convert converts test tasks `() = runBlocking {
        coEvery { actionPlanTaskCalendarBuilder.buildCalendar(person.id, any<TestRequestNew>()) } returns calendar

        val expectedAccreditedNetworkRedirect = Navigation(
            mobileRoute = MobileRouting.ACCREDITED_NETWORK,
            method = RemoteActionMethod.GET,
            endpoint = "/accredited_network/providers?health_plan_task_id=${testRequestTask.id}",
            params = mapOf(
                "has_app_state" to true,
                "service_filter_enabled" to true,
                "subspecialty_filter_enabled" to true,
                "favorite_filter_enabled" to true,
                "availability_filter_enabled" to false,
            )
        )

        val result = actionPlanTaskSchedulingResponseConverter.convert(task = testRequestTask)
        assertThat(result).isNotNull
        assertThat(result!!.calendar).isEqualTo(calendar)
        assertThat(result.navigation).isEqualTo(expectedAccreditedNetworkRedirect)
        coVerifyOnce { actionPlanTaskCalendarBuilder.buildCalendar(any(), any<TestRequestNew>()) }
    }

    @Test
    fun `#convert converts referral task`() = runBlocking {
        coEvery { actionPlanTaskCalendarBuilder.buildCalendar(person.id, any<ReferralNew>()) } returns calendar

        val expectedAccreditedNetworkRedirect = Navigation(
            mobileRoute = MobileRouting.ACCREDITED_NETWORK,
            method = RemoteActionMethod.GET,
            endpoint = "/accredited_network/providers?health_plan_task_id=${referralTask.id}",
            params = mapOf(
                "has_app_state" to true,
                "service_filter_enabled" to true,
                "subspecialty_filter_enabled" to true,
                "favorite_filter_enabled" to true,
                "availability_filter_enabled" to false,
            )
        )

        val result = actionPlanTaskSchedulingResponseConverter.convert(task = referralTask)
        assertThat(result).isNotNull
        assertThat(result!!.calendar).isEqualTo(calendar)
        assertThat(result.navigation).isEqualTo(expectedAccreditedNetworkRedirect)
        coVerifyOnce { actionPlanTaskCalendarBuilder.buildCalendar(any(), any<ReferralNew>()) }
    }

    @Test
    fun `#convert converts advancedAccess referral task`() = runBlocking {
        coEvery { actionPlanTaskCalendarBuilder.buildCalendar(person.id, any<ReferralNew>()) } returns calendar

        val expectedAccreditedNetworkRedirect = Navigation(
            mobileRoute = MobileRouting.ACCREDITED_NETWORK,
            method = RemoteActionMethod.GET,
            endpoint = "/accredited_network/providers?health_plan_task_id=${advancedAccessTask.id}",
            params = mapOf(
                "has_app_state" to true,
                "service_filter_enabled" to false,
                "subspecialty_filter_enabled" to false,
                "favorite_filter_enabled" to false,
                "availability_filter_enabled" to true,
            )
        )

        val result = actionPlanTaskSchedulingResponseConverter.convert(task = advancedAccessTask)
        assertThat(result).isNotNull
        assertThat(result!!.calendar).isEqualTo(calendar)
        assertThat(result.navigation).isEqualTo(expectedAccreditedNetworkRedirect)
        coVerifyOnce { actionPlanTaskCalendarBuilder.buildCalendar(any(), any<ReferralNew>()) }
    }

    @Test
    fun `#convert converts emergency task`() = runBlocking {
        coEvery { actionPlanTaskCalendarBuilder.buildCalendar(person.id, any<ReferralNew>()) } returns calendar

        val emergencyTask = EmergencyNew(
            task = referralActionPlanTask,
            diagnosticHypothesis = "Está com colesterol em nível perigoso",
            cityId = "112233",
        )

        val expectedAccreditedNetworkRedirect = Navigation(
            mobileRoute = MobileRouting.ACCREDITED_NETWORK,
            method = RemoteActionMethod.GET,
            endpoint = "/accredited_network/providers?health_plan_task_id=${emergencyTask.id}",
            params = mapOf(
                "has_app_state" to true,
                "service_filter_enabled" to true,
                "subspecialty_filter_enabled" to true,
                "favorite_filter_enabled" to true,
                "availability_filter_enabled" to false,
            )
        )

        val result = actionPlanTaskSchedulingResponseConverter.convert(task = emergencyTask)
        assertThat(result!!.calendar).isEqualTo(calendar)
        assertThat(result.navigation).isEqualTo(expectedAccreditedNetworkRedirect)
        coVerifyOnce { actionPlanTaskCalendarBuilder.buildCalendar(any(), any<ReferralNew>()) }
    }

    @Test
    fun `#convert converts questionnaire task`() = runBlocking {
        val questionnaireTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = RangeUUID.generate(),
            title = "Questionário de Saúde",
            description = "Para melhorar sua alimentação",
            dueDate = null,
            status = ActionPlanTaskStatus.ACTIVE,
            deadline = Deadline(PeriodUnit.WEEK, 1),
            start = StartType.IMMEDIATE,
            type = ActionPlanTaskType.QUESTIONNAIRE,
            favorite = false,
        )
        val result = actionPlanTaskSchedulingResponseConverter.convert(task = questionnaireTask)
        assertThat(result).isNull()
    }
}
