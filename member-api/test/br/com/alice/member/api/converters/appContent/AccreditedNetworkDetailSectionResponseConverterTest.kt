package br.com.alice.member.api.converters.appContent

import br.com.alice.app.content.model.*
import br.com.alice.app.content.model.section.*
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.asMap
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.SectionData
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedNetworkDetailSectionResponseConverterTest {
    private fun section(type: SectionType, data: SectionDataBase) = Section(
        id = UUID.randomUUID().toString(),
        type = type,
        data = data,
        minAppVersion = "0.0.0",
    )

    private fun buttonSectionWith(actionRouting: ActionRouting) = Section(
        id = UUID.randomUUID().toString(),
        type = SectionType.BUTTON_SECTION,
        data = ButtonSection(button = Button(onTapAction = RemoteAction(mobileRoute = actionRouting))),
        minAppVersion = "0.0.0"
    )

    private fun buttonSectionWith(onTapAction: RemoteAction) = Section(
        id = UUID.randomUUID().toString(),
        type = SectionType.BUTTON_SECTION,
        data = ButtonSection(button = Button(onTapAction = onTapAction)),
        minAppVersion = "0.0.0"
    )

    @Test
    fun `SectionResponseConverter#convert should convert TextSection Content`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.TEXT_SECTION,
                data = TextSection.Content(
                    content = TextSection.Content.Value(
                        title = "Title",
                        alignment = Alignment.LEFT,
                        layout = SectionTextLayout.H1
                    )
                ),
            )
        )

        val expected = MemberApiSectionData(
            content = SectionData(
                title = "Title",
                alignment = Alignment.LEFT,
                layout = SectionTextLayout.H1
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert TextSection ContentAction`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.TEXT_SECTION,
                data = TextSection.ContentAction(
                    contentAction = RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = "/endpoint"
                    )
                ),
            )
        )

        val expected = MemberApiSectionData(
            contentAction = br.com.alice.member.api.models.appContent.RemoteAction(
                method = RemoteActionMethod.GET,
                endpoint = "http://localhost/endpoint"
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ImageSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.IMAGE_SECTION,
                data = ImageSection(
                    content = ImageSection.Content(
                        image = Image(url = "url"),
                        alignment = Alignment.LEFT,
                        verticalPadding = SectionPadding.P1,
                        horizontalPadding = SectionPadding.P3
                    )
                ),
            )
        )

        val expected = MemberApiSectionData(
            content = SectionData(
                image = Image(url = "url"),
                alignment = Alignment.LEFT,
                verticalPadding = SectionPadding.P1,
                horizontalPadding = SectionPadding.P3
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert BridgeSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.BRIDGE_SECTION,
                data = BridgeSection(
                    sections = listOf(),
                    rules = listOf(
                        BridgeRule(
                            onAction = BridgeRuleOn("button_1", BridgeRuleOnAction.PRESSED),
                            then = BridgeRuleThen("button_2", BridgeRuleThenProperty.ENABLED, true),
                        )
                    )
                ),
            )
        )

        val expected = MemberApiSectionData(
            sections = listOf(),
            rules = listOf(
                BridgeRule(
                    onAction = BridgeRuleOn("button_1", BridgeRuleOnAction.PRESSED),
                    then = BridgeRuleThen("button_2", BridgeRuleThenProperty.ENABLED, true),
                )
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ButtonSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.BUTTON_SECTION,
                data = ButtonSection(
                    button = Button(id = "123", label = Label(text = "Button")),
                    alignment = Alignment.RIGHT,
                ),
            )
        )

        val expected = MemberApiSectionData(
            button = br.com.alice.member.api.models.appContent.Button(id = "123", label = Label(text = "Button")),
            alignment = Alignment.RIGHT,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert CalendarSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CALENDAR_SECTION,
                data = CalendarSection(
                    variant = Variant.PRIMARY,
                    initialDate = LocalDate.MIN,
                    firstDate = LocalDate.MIN,
                    lastDate = LocalDate.MAX,
                    currentDate = LocalDate.MAX,
                    availableDays = CalendarAvailableDays.WEEKDAYS,
                    initialCalendarMode = CalendarMode.YEAR,
                    confirmButton = Button(),
                )
            )
        )

        val expected = MemberApiSectionData(
            variant = Variant.PRIMARY,
            initialDate = LocalDate.MIN,
            firstDate = LocalDate.MIN,
            lastDate = LocalDate.MAX,
            currentDate = LocalDate.MAX,
            availableDays = CalendarAvailableDays.WEEKDAYS,
            initialCalendarMode = CalendarMode.YEAR,
            confirmButton = Button(),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert CalloutSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CALLOUT_SECTION,
                data = CalloutSection(
                    title = "title",
                    calloutBody = "body",
                    calloutVariant = CalloutVariant.HIGHLIGHT,
                    calloutAction = CalloutAction(),
                    avatarUrl = "url",
                )
            )
        )

        val expected = MemberApiSectionData(
            title = "title",
            calloutBody = "body",
            calloutVariant = CalloutVariant.HIGHLIGHT,
            calloutAction = br.com.alice.member.api.models.appContent.CalloutAction(),
            avatarUrl = "url",
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert CardSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CARD_SECTION,
                data = CardSection(
                    type = CardType.INFO,
                    state = CardState.EXPIRED,
                    header = CardHeader(),
                    title = "some text",
                    description = "some text",
                    backgroundImage = "some text",
                    accessoryImage = "some text",
                    alignment = Alignment.RIGHT_BOTTOM,
                    backgroundColor = BackgroundColor.LIME,
                    contentAction = RemoteAction(endpoint = "/content action"),
                    onCardClick = RemoteAction(endpoint = "/on card click"),
                    mainAction = Button(onTapAction = RemoteAction(endpoint = "/mainAction")),
                    buttonPrimary = Button(onTapAction = RemoteAction(endpoint = "/buttonPrimary")),
                    buttonSecondary = Button(onTapAction = RemoteAction(endpoint = "/buttonSecondary")),
                )
            )
        )

        val expected = MemberApiSectionData(
            type = CardType.INFO,
            state = CardState.EXPIRED,
            header = CardHeader(),
            title = "some text",
            description = "some text",
            backgroundImage = "some text",
            accessoryImage = "some text",
            alignment = Alignment.RIGHT_BOTTOM,
            backgroundColor = BackgroundColor.LIME,
            contentAction = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/content action"),
            onCardClick = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/on card click"),
            mainAction = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/mainAction"
                )
            ),
            buttonPrimary = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/buttonPrimary"
                )
            ),
            buttonSecondary = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/buttonSecondary"
                )
            ),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ChatInputSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CHAT_INPUT_SECTION,
                data = ChatInputSection(
                    placeholder = "some text",
                    text = "some text",
                    onSendAction = RemoteAction(endpoint = "/onSendAction")
                )
            )
        )

        val expected = MemberApiSectionData(
            placeholder = "some text",
            text = "some text",
            onSendAction = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/onSendAction")
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert CheckboxSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CHECKBOX_SECTION,
                data = CheckboxSection(
                    items = listOf("option 1", "option 2"),
                    onStatusChange = OnStatusChange(type = ActionType.CHEVRON),
                )
            )
        )

        val expected = MemberApiSectionData(
            items = listOf("option 1", "option 2"),
            onStatusChange = OnStatusChange(type = ActionType.CHEVRON),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert GroupedColumnSection`() {
        val bodySection = section(
            type = SectionType.CHECKBOX_SECTION,
            data = CheckboxSection(items = listOf("option 1", "option 2"))
        )

        val expectedConvertedBodySection = br.com.alice.member.api.models.appContent.Section(
            id = bodySection.id,
            type = SectionType.CHECKBOX_SECTION,
            data = MemberApiSectionData(items = listOf("option 1", "option 2")),
            minAppVersion = bodySection.minAppVersion,
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.GROUPED_COLUMN_SECTION,
                data = GroupedColumnSection(
                    title = "title",
                    detailsButton = Button(),
                    body = listOf(bodySection)
                )
            )
        )

        val expected = MemberApiSectionData(
            title = "title",
            detailsButton = br.com.alice.member.api.models.appContent.Button(),
            body = listOf(expectedConvertedBodySection)
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert InfiniteScrollSection`() {
        val bodySection = section(
            type = SectionType.CHECKBOX_SECTION,
            data = CheckboxSection(items = listOf("option 1", "option 2"))
        )

        val expectedConvertedBodySection = br.com.alice.member.api.models.appContent.Section(
            id = bodySection.id,
            type = SectionType.CHECKBOX_SECTION,
            data = MemberApiSectionData(items = listOf("option 1", "option 2")),
            minAppVersion = bodySection.minAppVersion,
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.INFINITE_SCROLL_SECTION,
                data = InfiniteScrollSection(
                    title = "title",
                    detailsButton = Button(),
                    body = listOf(bodySection),
                    trackUserBehavior = true,
                )
            )
        )

        val expected = MemberApiSectionData(
            title = "title",
            detailsButton = br.com.alice.member.api.models.appContent.Button(),
            body = listOf(expectedConvertedBodySection),
            trackUserBehavior = true,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert GridItemSection`() {
        val childSection = section(
            type = SectionType.CHECKBOX_SECTION,
            data = CheckboxSection(items = listOf("option 1", "option 2"))
        )

        val expectedConvertedChildSection = br.com.alice.member.api.models.appContent.Section(
            id = childSection.id,
            type = SectionType.CHECKBOX_SECTION,
            data = MemberApiSectionData(items = listOf("option 1", "option 2")),
            minAppVersion = childSection.minAppVersion,
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.GRID_ITEM_SECTION,
                data = GridItemSection(
                    child = childSection,
                    span = 3,
                )
            )
        )

        val expected = MemberApiSectionData(
            child = expectedConvertedChildSection,
            span = 3
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert GridSection`() {
        val childSection = section(
            type = SectionType.CHECKBOX_SECTION,
            data = CheckboxSection(items = listOf("option 1", "option 2"))
        )

        val expectedConvertedChildSection =
            br.com.alice.member.api.models.appContent.Section(
                id = childSection.id,
                type = SectionType.CHECKBOX_SECTION,
                data = MemberApiSectionData(items = listOf("option 1", "option 2")),
                minAppVersion = childSection.minAppVersion,
            )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.GRID_SECTION,
                data = GridSection(
                    children = listOf(childSection),
                    gutter = GridGutter.GUTTER_10,
                    gridVariant = GridVariant.FULL_WIDTH,
                )
            )
        )

        val expected = MemberApiSectionData(
            children = listOf(expectedConvertedChildSection),
            gutter = GridGutter.GUTTER_10,
            gridVariant = GridVariant.FULL_WIDTH,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert StackedCarousel`() {
        val childSection = section(
            type = SectionType.SHORTCUT_SECTION,
            data = ShortcutSection(
                id = "123",
                title = "shortcut-title",
                icon = "shortcut-icon",
                action = RemoteAction(endpoint = "/action"),
                badge = null,
            )
        )

        val expectedConvertedChildSection =
            br.com.alice.member.api.models.appContent.Section(
                id = childSection.id,
                type = SectionType.SHORTCUT_SECTION,
                data = MemberApiSectionData(
                    id = "123",
                    title = "shortcut-title",
                    icon = "shortcut-icon",
                    action = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/action"),
                    badge = null,
                ),
                minAppVersion = childSection.minAppVersion,
            )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.STACKED_CAROUSEL_SECTION,
                data = StackedCarouselSection(
                    id = "stacked_id",
                    children = listOf(childSection),
                )
            )
        )

        val expected = MemberApiSectionData(
            id = "stacked_id",
            children = listOf(expectedConvertedChildSection)
        )

        assertEquals(converted.data, expected)
    }


    @Test
    fun `SectionResponseConverter#convert should convert LinkSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.LINK_SECTION,
                data = LinkSection(
                    link = Link(label = LinkLabel(text = "link")),
                    alignment = Alignment.CENTER,
                    verticalPadding = SectionPadding.P7,
                    horizontalPadding = SectionPadding.P6,
                )
            )
        )

        val expected = MemberApiSectionData(
            link = br.com.alice.member.api.models.appContent.Link(label = LinkLabel(text = "link")),
            alignment = Alignment.CENTER,
            verticalPadding = SectionPadding.P7,
            horizontalPadding = SectionPadding.P6,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ListCardSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.LIST_CARD_SECTION,
                data = ListCardSection(
                    orientation = SectionOrientation.HORIZONTAL,
                    title = "some text",
                    description = "some text",
                    icon = "some text",
                    detailsButton = Button(onTapAction = RemoteAction(endpoint = "/detailsButton")),
                    contentAction = RemoteAction(endpoint = "/contentAction"),
                )
            )
        )

        val expected = MemberApiSectionData(
            orientation = SectionOrientation.HORIZONTAL,
            title = "some text",
            description = "some text",
            icon = "some text",
            detailsButton = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/detailsButton"
                )
            ),
            contentAction = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/contentAction"),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ListMenuSection`() {
        val bodySection = section(
            type = SectionType.CHECKBOX_SECTION,
            data = CheckboxSection(items = listOf("option 1", "option 2"))
        )

        val expectedConvertedBodySection = br.com.alice.member.api.models.appContent.Section(
            id = bodySection.id,
            type = SectionType.CHECKBOX_SECTION,
            data = MemberApiSectionData(items = listOf("option 1", "option 2")),
            minAppVersion = bodySection.minAppVersion,
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.LIST_MENU_SECTION,
                data = ListMenuSection(
                    contentAction = RemoteAction(endpoint = "/contentAction"),
                    body = listOf(bodySection),
                    hasDivider = true,
                )
            )
        )

        val expected = MemberApiSectionData(
            contentAction = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/contentAction"),
            body = listOf(expectedConvertedBodySection),
            hasDivider = true,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert MenuSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.MENU_SECTION,
                data = MenuSection(
                    menuVariant = MenuVariant.INTERMEDIATE,
                    icon = "some text",
                    avatarUrl = "some text",
                    title = "some text",
                    label = "some text",
                    caption = "some text",
                    tag = Tag(),
                    clickAffordance = true,
                    onTapAction = RemoteAction(endpoint = "/onTapAction"),
                )
            )
        )

        val expected = MemberApiSectionData(
            menuVariant = MenuVariant.INTERMEDIATE,
            icon = "some text",
            avatarUrl = "some text",
            title = "some text",
            label = "some text",
            caption = "some text",
            tag = Tag(),
            clickAffordance = true,
            onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/onTapAction"),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ModuleSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.MODULE_SECTION,
                data = ModuleSection(
                    moduleName = "module"
                )
            )
        )

        val expected = MemberApiSectionData(
            moduleName = "module"
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert PillResponseSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.PILL_RESPONSE_SECTION,
                data = PillResponseSection(
                    options = listOf(PillOption(id = "1", title = "option 1")),
                    stack = SectionOrientation.VERTICAL,
                    selectionAlgorithm = PillResponseSelectionAlgorithm.MULTIPLE,
                    confirmButton = Button(onTapAction = RemoteAction(endpoint = "/confirmButton")),
                    noSelectionButton = Button(onTapAction = RemoteAction(endpoint = "/noSelectionButton")),
                )
            )
        )

        val expected = MemberApiSectionData(
            options = listOf(PillOption(id = "1", title = "option 1")),
            stack = SectionOrientation.VERTICAL,
            selectionAlgorithm = PillResponseSelectionAlgorithm.MULTIPLE,
            confirmButton = Button(onTapAction = RemoteAction(endpoint = "http://localhost/confirmButton")),
            noSelectionButton = Button(onTapAction = RemoteAction(endpoint = "http://localhost/noSelectionButton")),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert QuestionnaireSection`() {
        val question = QuestionnaireQuestionResponse(
            id = UUID.randomUUID(),
            questionnaireId = UUID.randomUUID(),
            question = "question?",
            input = QuestionnaireQuestionInputResponse(
                action = "1",
                options = listOf(),
                type = HealthFormQuestionType.MULTIPLE_OPTIONS,
                displayAttributes = null,
            ),
            details = null,
            progress = 1,
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.QUESTIONNAIRE_SECTION,
                data = QuestionnaireSection(
                    question = question
                )
            )
        )

        val expected = MemberApiSectionData(
            question = question
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert SheetSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.SHEET_SECTION,
                data = SheetSection(
                    illustrationUrl = "some text",
                    title = "some text",
                    content = SheetSection.Content(text = "some text"),
                    confirmationButton = Button(onTapAction = RemoteAction(endpoint = "/confirmationButton")),
                    cancelButton = Button(onTapAction = RemoteAction(endpoint = "/cancelButton")),
                )
            )
        )

        val expected = MemberApiSectionData(
            illustrationUrl = "some text",
            title = "some text",
            content = SectionData(text = "some text"),
            confirmationButton = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/confirmationButton"
                )
            ),
            cancelButton = br.com.alice.member.api.models.appContent.Button(
                onTapAction = br.com.alice.member.api.models.appContent.RemoteAction(
                    endpoint = "http://localhost/cancelButton"
                )
            ),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert TabBarSection`() {
        val headerSection = Section(
            id = UUID.randomUUID().toString(),
            type = SectionType.MODULE_SECTION,
            data = ModuleSection(moduleName = "Sample"),
            minAppVersion = "0.0.0",
        )

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.TAB_BAR_SECTION,
                data = TabBarSection(
                    headers = listOf(headerSection),
                    tabBarVariant = TabBarVariant.ICONS,
                    tabBarItems = listOf(
                        TabBarItem(content = "Tab 1", action = RemoteAction(endpoint = "/tab 1"))
                    ),
                    trackUserBehavior = true,
                )
            )
        )

        val expected = MemberApiSectionData(
            headers = listOf(
                br.com.alice.member.api.models.appContent.Section(
                    id = headerSection.id,
                    type = SectionType.MODULE_SECTION,
                    data = MemberApiSectionData(moduleName = "Sample"),
                    minAppVersion = headerSection.minAppVersion,
                )
            ),
            tabBarVariant = TabBarVariant.ICONS,
            tabBarItems = listOf(
                TabBarItem(content = "Tab 1", action = RemoteAction(endpoint = "http://localhost/tab 1"))
            ),
            trackUserBehavior = true,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ActionRouting to MobileRouting`() {
        assertEquals(
            MobileRouting.MEMBER_ONBOARDING,
            SectionResponseConverter.convert(
                buttonSectionWith(actionRouting = ActionRouting.ONBOARDING)
            ).data.button?.onTapAction?.mobileRoute,
        )

        ActionRouting.values()
            .filterNot { it.name.startsWith("ACTION_PLAN_") }
            .filterNot { it == ActionRouting.ONBOARDING }
            .forEach {
                assertEquals(
                    MobileRouting.valueOf(it.name),
                    SectionResponseConverter.convert(buttonSectionWith(actionRouting = it))
                        .data.button?.onTapAction?.mobileRoute,
                )
            }
    }

    @Test
    fun `SectionResponseConverter#convert should add base path to RemoteAction if not set`() {
        assertEquals(
            "http://localhost/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(onTapAction = RemoteAction(endpoint = "/health-plan"))
            ).data.button?.onTapAction?.endpoint,
        )
    }

    @Test
    fun `SectionResponseConverter#convert should not add base path to RemoteAction if set with http or https`() {
        assertEquals(
            "https://alice.com.br/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(onTapAction = RemoteAction(endpoint = "https://alice.com.br/health-plan"))
            ).data.button?.onTapAction?.endpoint,
        )

        assertEquals(
            "http://alice.com.br/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(onTapAction = RemoteAction(endpoint = "http://alice.com.br/health-plan"))
            ).data.button?.onTapAction?.endpoint,
        )
    }

    @Test
    fun `SectionResponseConverter#convert should add base path to RemoteAction params action endpoint if not set`() {
        assertEquals(
            "http://localhost/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = mapOf("action" to mapOf("method" to "GET", "endpoint" to "/health-plan"))
                    )
                )
            ).data.button?.onTapAction?.params?.get("action")?.asMap()?.get("endpoint")
        )
    }

    @Test
    fun `SectionResponseConverter#convert should mantain endpoint as is if base path is set`() {
        val params = mapOf(
            "action" to mapOf("method" to "GET", "endpoint" to "http://alice.com.br/health-plan")
        )

        assertEquals(
            "http://alice.com.br/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = params
                    )
                )
            ).data.button?.onTapAction?.params?.get("action")?.asMap()?.get("endpoint")
        )
    }

    @Test
    fun `SectionResponseConverter#convert should mantain params as is if not contains endpoint`() {
        val params = mapOf(
            "screen_id" to "some_id",
            "another_key" to "another_value",
            "person_id" to "some_person_id"
        )

        assertEquals(
            params,
            SectionResponseConverter.convert(
                buttonSectionWith(
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.APPOINTMENT_SCHEDULE,
                        params = params
                    )
                )
            ).data.button?.onTapAction?.params
        )
    }

    @Test
    fun `SectionResponseConverter#convert should add base path to RemoteAction params action endpoint if not set and action is RemoteAction`() {
        assertEquals(
            "http://localhost/health-plan",
            SectionResponseConverter.convert(
                buttonSectionWith(
                    onTapAction = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = mapOf(
                            "action" to RemoteAction(
                                method = RemoteActionMethod.GET,
                                endpoint = "/health-plan"
                            )
                        )
                    )
                )
            ).data.button?.onTapAction?.params?.get("action")?.asMap()?.get("endpoint")
        )
    }

    @Test
    fun `SectionResponseConverter#convert should convert ShortcutSection`() {
        val id = RangeUUID.generate().toString()
        val title = "shortcut-title"
        val icon = "shortcut-icon"
        val action = RemoteAction(endpoint = "/action")
        val badge = ShortcutSectionBadge(text = "badge-text", animated = true)

        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.SHORTCUT_SECTION,
                data = ShortcutSection(
                    id = id,
                    title = title,
                    icon = icon,
                    action = action,
                    badge = badge,
                )
            )
        )

        val expected = MemberApiSectionData(
            id = id,
            title = title,
            icon = icon,
            action = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/action"),
            badge = badge,
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert HealthProfessionalInfoCardSection`() {
        val id = RangeUUID.generate()
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CARD_SECTION,
                data = HealthProfessionalInfoCardSection(
                    type = CardType.HEALTH_PROFESSIONAL_INFO,
                    healthProfessionalInfo = HealthProfessionalInfo(
                        id = id,
                        name = "some name",
                        avatarUrl = "some url",
                        council = "123/SP",
                        tag = Tag(
                            icon = "some icon",
                            text = "some text",
                            colorScheme = TagColorScheme.MAGENTA
                        )
                    ),
                    expandableCardVariant = ExpandableCardVariant(
                        metrics = listOf(Metric(value = "some value", label = "some label")),
                        content = "some content",
                    ),
                    onCardClick = RemoteAction(endpoint = "/on card click")
                )
            )
        )

        val expected = MemberApiSectionData(
            type = CardType.HEALTH_PROFESSIONAL_INFO,
            healthProfessionalInfo = HealthProfessionalInfo(
                id = id,
                name = "some name",
                avatarUrl = "some url",
                council = "123/SP",
                tag = Tag(
                    icon = "some icon",
                    text = "some text",
                    colorScheme = TagColorScheme.MAGENTA
                )
            ),
            expandableCardVariant = ExpandableCardVariant(
                metrics = listOf(Metric(value = "some value", label = "some label")),
                content = "some content",
                colorScheme = BackgroundColor.BACKGROUND_DEFAULT
            ),
            onCardClick = br.com.alice.member.api.models.appContent.RemoteAction(endpoint = "http://localhost/on card click"),
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert HealthProfessionalInfoSection`() {
        val id = RangeUUID.generate()
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION,
                data = HealthProfessionalInfoSection(
                    healthProfessionalInfo = HealthProfessionalInfo(
                        id = id,
                        name = "some name",
                        avatarUrl = "some url",
                        council = "123/SP",
                        tag = Tag(
                            icon = "some icon",
                            text = "some text",
                            colorScheme = TagColorScheme.MAGENTA
                        )
                    )
                )
            )
        )

        val expected = MemberApiSectionData(
            healthProfessionalInfo = HealthProfessionalInfo(
                id = id,
                name = "some name",
                avatarUrl = "some url",
                council = "123/SP",
                tag = Tag(
                    icon = "some icon",
                    text = "some text",
                    colorScheme = TagColorScheme.MAGENTA
                )
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert LargeListCardSection`() {
        val id = RangeUUID.generate().toString()
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.HEALTH_PROFESSIONAL_INFO_SECTION,
                data = LargeListCardSection(
                    body = listOf(
                        Section(
                            id = id,
                            type = SectionType.CHECKBOX_SECTION,
                            data = CheckboxSection(items = listOf("option 1", "option 2")),
                            minAppVersion = "4.0",
                        )
                    )
                )
            )
        )

        val expected = MemberApiSectionData(
            body = listOf(
                br.com.alice.member.api.models.appContent.Section(
                    id = id,
                    type = SectionType.CHECKBOX_SECTION,
                    data = MemberApiSectionData(items = listOf("option 1", "option 2")),
                    minAppVersion = "4.0",
                )
            )
        )

        assertEquals(converted.data, expected)
    }

    @Test
    fun `SectionResponseConverter#convert should convert ExpandableCardSection`() {
        val converted = SectionResponseConverter.convert(
            section(
                type = SectionType.CARD_SECTION,
                data = ExpandableCardSection(
                    type = CardType.EXPANDABLE,
                    expandableCardVariant = ExpandableCardVariant(
                        metrics = listOf(Metric(value = "some value", label = "some label")),
                        content = "some content",
                        trackingParameters = mapOf("id" to "some id", "name" to "some name")
                    )
                )
            )
        )

        val expected = MemberApiSectionData(
            type = CardType.EXPANDABLE,
            expandableCardVariant = ExpandableCardVariant(
                metrics = listOf(Metric(value = "some value", label = "some label")),
                content = "some content",
                colorScheme = BackgroundColor.BACKGROUND_DEFAULT,
                trackingParameters = mapOf("id" to "some id", "name" to "some name")
            )
        )

        assertEquals(converted.data, expected)
    }
}
