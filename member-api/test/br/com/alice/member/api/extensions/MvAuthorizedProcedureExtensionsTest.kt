package br.com.alice.member.api.extensions

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.GlossAuthorizationInfoData
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.member.api.extensions.cleanedGloss
import br.com.alice.member.api.extensions.cleanedStatus
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.util.UUID
import kotlin.test.Test

class MvAuthorizedProcedureExtensionsTest {

    private var mvAuthorizedProcedure = TestModelFactory.buildMvAuthorizedProcedure(gloss = listOf(
        GlossAuthorizationInfoData(UUID.randomUUID(), description = "Gloss description", title = "Gloss title", code = "TEST")
    ))
    private var totvsGuia = TestModelFactory.buildTotvsGuia()

    @Nested
    inner class CleanedStatus {
        @Test
        fun `#cleanedStatus should return PENDING status when totvsGuia is PENDING`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.AUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.PENDING)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.PENDING)
        }

        @Test
        fun `#cleanedStatus should return CANCELLED status when totvsGuia is CANCELLED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.AUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.CANCELLED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.CANCELLED)
        }

        @Test
        fun `#cleanedStatus should return PENDING status when procedure is PENDING`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.PENDING)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.PENDING)
        }

        @Test
        fun `#cleanedStatus should return PENDING status when procedure is ACTIVE`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.ACTIVE)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.PENDING)
        }

        @Test
        fun `#cleanedStatus should return AUTHORIZED status when procedure is AUTHORIZED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.AUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.AUTHORIZED)
        }

        @Test
        fun `#cleanedStatus should return AUTHORIZED status when procedure is PRE_EXECUTED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.PRE_EXECUTED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.AUTHORIZED)
        }

        @Test
        fun `#cleanedStatus should return EXECUTED status when procedure is EXECUTED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.EXECUTED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.EXECUTED)
        }

        @Test
        fun `#cleanedStatus should return UNAUTHORIZED status when procedure is UNAUTHORIZED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.UNAUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedStatus(totvsGuia)
            Assertions.assertThat(result).isEqualTo(MvAuthorizedProcedureStatus.UNAUTHORIZED)
        }

        @Test
        fun `#cleanedGloss should return the first gloss when guia status is UNAUTHORIZED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.UNAUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.UNAUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedGloss(totvsGuia)
            Assertions.assertThat(result).isEqualTo(mvAuthorizedProcedure.gloss?.first())
        }

        @Test
        fun `#cleanedGloss should return the first gloss when guia status is PARTIALLY_AUTHORIZED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.UNAUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.PARTIALLY_AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedGloss(totvsGuia)
            Assertions.assertThat(result).isEqualTo(mvAuthorizedProcedure.gloss?.first())
        }

        @Test
        fun `#cleanedGloss should not return gloss when guia status is AUTHORIZED`() {
            mvAuthorizedProcedure = mvAuthorizedProcedure.copy(status = MvAuthorizedProcedureStatus.UNAUTHORIZED)
            totvsGuia = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

            val result = mvAuthorizedProcedure.cleanedGloss(totvsGuia)
            Assertions.assertThat(result).isNull()
        }
    }

}
