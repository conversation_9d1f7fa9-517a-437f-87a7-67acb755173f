package br.com.alice.member.api

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.healthlogic.client.HealthScoreResultService
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.controllers.HealthScoreHistoryController
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthScoreHistoryRoutesTest : RoutesTestHelper() {

    private val healthScoreResultService: HealthScoreResultService = mockk()
    private val person = TestModelFactory.buildPerson()
    private val token = RangeUUID.generate().toString()
    private val generalResultToken = "SCORE_MAGENTA"
    private val scoreMagentaOutcomeConf = TestModelFactory.buildOutcomeConf(
        key = "SCORE_MAGENTA",
        referenceRange = listOf(
            OutcomeConf.ReferenceRange(
                description = "MODERATE",
                lowerLimit = BigDecimal(0),
                upperLimit = BigDecimal(500),
            ),
            OutcomeConf.ReferenceRange(
                description = "GOOD",
                lowerLimit = BigDecimal(500),
                upperLimit = BigDecimal(750),

                ),
            OutcomeConf.ReferenceRange(
                description = "EXCELLENT",
                lowerLimit = BigDecimal(750),
                upperLimit = BigDecimal(1000)
            )
        )
    )
    private val temporalFirstDayOfYear = TemporalAdjusters.firstDayOfYear()
    private val temporalLastDayOfYear = TemporalAdjusters.lastDayOfYear()

    private val generalClinicalOutcomeRecordsMock = listOf(
        TestModelFactory.buildClinicalOutcomeRecord(
            personId = person.id,
            outcomeConfId = scoreMagentaOutcomeConf.id
        ),
        TestModelFactory.buildClinicalOutcomeRecord(
            personId = person.id,
            outcomeConfId = scoreMagentaOutcomeConf.id
        )
    )

    private val pillarsOutcomeConfs = listOf(
        "EUROQOL_SCORE_MAGENTA",
        "FOOD_SCORE_MAGENTA",
        "HABITS_SCORE_MAGENTA",
        "MSQ_SCORE_MAGENTA",
        "IPAQ_SCORE_MAGENTA",
        "MENTAL_SCORE_MAGENTA"
    ).map {
        TestModelFactory.buildOutcomeConf(
            key = it,
            referenceRange = listOf(
                OutcomeConf.ReferenceRange(
                    description = "MODERATE",
                    lowerLimit = BigDecimal(0),
                    upperLimit = BigDecimal(500),
                ),
                OutcomeConf.ReferenceRange(
                    description = "GOOD",
                    lowerLimit = BigDecimal(500),
                    upperLimit = BigDecimal(750),

                    ),
                OutcomeConf.ReferenceRange(
                    description = "EXCELLENT",
                    lowerLimit = BigDecimal(750),
                    upperLimit = BigDecimal(1000)
                )
            )
        )
    }
    private val pillarsResultMock = pillarsOutcomeConfs.map {
        EnrichedClinicalOutcomeRecord(
            TestModelFactory.buildClinicalOutcomeRecord(
                personId = person.id,
                outcomeConfId = it.id
            ),
            it
        )
    }

    private val localDateTimeNow = LocalDateTime.now()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            HealthScoreHistoryController(
                healthScoreResultService
            )
        }

        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns localDateTimeNow
    }

    @Test
    fun `#HealthScoreResultsHistory returns 200 OK and valid history result`() {
        val startDate = localDateTimeNow.with(temporalFirstDayOfYear).atBeginningOfTheDay()
        val endDate = localDateTimeNow.with(temporalLastDayOfYear).atEndOfTheDay()

        val serviceResponse = generalClinicalOutcomeRecordsMock.map {
            EnrichedClinicalOutcomeRecord(it, scoreMagentaOutcomeConf)
        }
        coEvery {
            healthScoreResultService.hasHealthScoreResultBetweenDates(
                person.id,
                startDate,
                endDate,
                generalResultToken
            )
        } returns true.success()
        coEvery {
            healthScoreResultService.getHealthScoreHistoryResultsByTypeAndDate(
                person.id,
                startDate,
                endDate,
                generalResultToken
            )
        } returns serviceResponse.success()

        coEvery {
            healthScoreResultService.getAvailableYearsOfHealthScoreHistory(person.id, generalResultToken)
        } returns emptyList<String>().success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/history/general") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#HealthScoreResultsHistory returns 200 OK and valid last year history result`() {
        val startDate = localDateTimeNow.with(temporalFirstDayOfYear).atBeginningOfTheDay()
        val endDate = localDateTimeNow.with(temporalLastDayOfYear).atEndOfTheDay()

        val serviceResponse = generalClinicalOutcomeRecordsMock.map {
            EnrichedClinicalOutcomeRecord(it, scoreMagentaOutcomeConf)
        }
        coEvery {
            healthScoreResultService.hasHealthScoreResultBetweenDates(
                person.id,
                startDate,
                endDate,
                generalResultToken
            )
        } returns false.success()
        coEvery {
            healthScoreResultService.getHealthScoreHistoryResultsByTypeAndDate(
                person.id,
                startDate.minusYears(1),
                endDate.minusYears(1),
                generalResultToken
            )
        } returns serviceResponse.success()

        coEvery {
            healthScoreResultService.getAvailableYearsOfHealthScoreHistory(person.id, generalResultToken)
        } returns emptyList<String>().success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/history/general") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#HealthScoreResultsHistory returns 400 Bad Request and empty result`() {
        val startDate = localDateTimeNow.with(temporalFirstDayOfYear).atBeginningOfTheDay()
        val endDate = localDateTimeNow.with(temporalLastDayOfYear).atEndOfTheDay()
        coEvery {
            healthScoreResultService.hasHealthScoreResultBetweenDates(
                person.id,
                startDate,
                endDate,
                generalResultToken
            )
        } returns false.success()
        coEvery {
            healthScoreResultService.getHealthScoreHistoryResultsByTypeAndDate(
                person.id,
                startDate.minusYears(1),
                endDate.minusYears(1),
                generalResultToken
            )
        } returns emptyList<EnrichedClinicalOutcomeRecord>().success()

        coEvery {
            healthScoreResultService.getAvailableYearsOfHealthScoreHistory(person.id, generalResultToken)
        } returns emptyList<String>().success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/history/general") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }

    }

    @Test
    fun `#getHealthScoreHistoryTypesById returns 200 OK and valid history result`() {
        val generalResultId = "c93be088-d12b-4981-9099-a8e0f4a8fec1"
        coEvery {
            healthScoreResultService.getHealthScorePartials(UUID.fromString(generalResultId))
        } returns pillarsResultMock.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/pillars/$generalResultId") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#getHealthScoreHistoryTypesById returns 200 OK and valid history result with reference delta id`() {
        val generalResultId = "c93be088-d12b-4981-9099-a8e0f4a8fec1"
        val referenceDeltaId = "ac83b5a9-479b-42f3-9829-94d3db2f9bd8"
        coEvery {
            healthScoreResultService.getHealthScorePartials(UUID.fromString(generalResultId))
        } returns pillarsResultMock.success()

        coEvery {
            healthScoreResultService.getHealthScorePartials(UUID.fromString(referenceDeltaId))
        } returns pillarsResultMock.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/pillars/$generalResultId?=reference_delta_id=$referenceDeltaId") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }
    }

    @Test
    fun `#getHealthScoreHistoryTypesById returns 200 OK and empty result`() {
        val generalResultId = "c93be088-d12b-4981-9099-a8e0f4a8fec1"
        coEvery {
            healthScoreResultService.getHealthScorePartials(
                UUID.fromString(generalResultId)
            )
        } returns emptyList<EnrichedClinicalOutcomeRecord>().success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/score_magenta/pillars/$generalResultId") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }
    }
}

