package br.com.alice.business.model

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.normalizeCnpjWithoutMask
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.MemberStatus
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Ignore
import kotlin.test.Test

class BeneficiaryTransportTest {

    private val basePerson = TestModelFactory.buildPerson(
        sex = Sex.FEMALE,
        dateOfBirth = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
        phoneNumber = "***********",
        tags = listOf("tag1", "b2b"),
    )
    private val personNullable = basePerson.copy(
        nickName = null,
        identityDocument = null,
        identityDocumentIssuingBody = null,
        userType = null
    )
    private val memberId = RangeUUID.generate()
    private val beneficiary = TestModelFactory.buildBeneficiary(personId = personNullable.id, memberId = memberId)
        .copy(memberStatus = MemberStatus.PENDING)

    private val beneficiaryTransport = BeneficiaryTransport(
        companyId = beneficiary.companyId,
        companyCNPJ = "00.000.000/0001-00",
        parentBeneficiary = beneficiary.parentBeneficiary,
        type = beneficiary.type,
        contractType = beneficiary.contractType,
        parentBeneficiaryRelationType = beneficiary.parentBeneficiaryRelationType,
        activatedAt = beneficiary.activatedAt,
        hiredAt = beneficiary.hiredAt,
        cnpj = beneficiary.cnpj,
        birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
        address = personNullable.addresses[0],
        email = personNullable.email,
        firstName = personNullable.firstName,
        lastName = personNullable.lastName,
        nationalId = personNullable.nationalId,
        phoneNumber = personNullable.phoneNumber.orEmpty(),
        sex = personNullable.sex!!,
        initialProductId = null,
        tags = listOf("tag1"),
        mothersName = personNullable.mothersName.orEmpty(),
        flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
        companySubContractId = beneficiary.companySubContractId
    )

    private val beneficiaryAndPersonRequest = BeneficiaryAndPersonRequest(
        parentBeneficiary = beneficiary.parentBeneficiary,
        companyId = beneficiary.companyId,
        companyCNPJ = "00.000.000/0001-00",
        contractType = beneficiary.contractType,
        parentBeneficiaryRelationType = beneficiary.parentBeneficiaryRelationType,
        activatedAt = beneficiary.activatedAt.toLocalDate().toString(),
        email = basePerson.email,
        nationalId = basePerson.nationalId,
        firstName = basePerson.firstName,
        lastName = basePerson.lastName,
        socialName = null,
        sex = basePerson.sex,
        dateOfBirth = basePerson.dateOfBirth?.toLocalDate().toString(),
        phoneNumber = basePerson.phoneNumber,
        tags = beneficiaryTransport.tags?.joinToString(","),
        addressPostalCode = basePerson.addresses.first().postalCode,
        addressState = basePerson.addresses.first().state.name,
        addressCity = basePerson.addresses.first().city,
        addressNeighborhood = basePerson.addresses.first().neighbourhood,
        addressStreet = basePerson.addresses.first().street,
        addressNumber = basePerson.addresses.first().number.toIntOrNull() ?: 0,
        addressComplement = basePerson.addresses.first().complement,
        mothersName = basePerson.mothersName.orEmpty(),
        flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
        initialProductId = null,
        cassiAccountNumber = null,
        cassiStartDate = null,
        cassiExpirationDate = null,
        cnpj = beneficiary.cnpj?.normalizeCnpjWithoutMask(),
        hiredAt = beneficiary.hiredAt?.toLocalDate()?.atStartOfDay()?.toLocalDate().toString(),
    )

    @Test
    fun `#BeneficiaryTransport_toBeneficiary converts correctly`() {
        val expected = beneficiaryTransport.toBeneficiary(memberId, personNullable.id)
        assertThat(beneficiary).usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt", "address.lat", "address.lng")
            .isEqualTo(expected)
    }

    @Ignore
    @Test
    fun `#BeneficiaryTransport_toPerson converts correctly`() {
        val expected = beneficiaryTransport.toPerson()
        assertThat(personNullable.copy(nationalId = personNullable.nationalId.onlyNumbers()))
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#BeneficiaryAndPersonRequest_toBeneficiaryTransport converts correctly`() {
        val expected = beneficiaryAndPersonRequest.toBeneficiaryTransport()
        val expectedActivatedAt = beneficiaryTransport.activatedAt.atBeginningOfTheDay()
        val expectedHiredAt = beneficiaryTransport.hiredAt?.atBeginningOfTheDay()

        assertThat(
            beneficiaryTransport.copy(
                activatedAt = expectedActivatedAt,
                hiredAt = expectedHiredAt
            )
        )
            .usingRecursiveComparison().ignoringFields("address.lat", "address.lng", "companySubContractId")
            .isEqualTo(expected)
    }

}
