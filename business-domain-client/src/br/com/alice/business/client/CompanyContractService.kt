package br.com.alice.business.client

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanyContractStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CompanyContractService : Service {
    override val namespace get() = "business"
    override val serviceName get() = "company_contract"

    suspend fun add(companyContract: CompanyContract, sendEvent: Boolean = true): Result<CompanyContract, Throwable>
    suspend fun update(companyContract: CompanyContract, sendEvent: Boolean = true): Result<CompanyContract, Throwable>

    suspend fun delete(companyContract: CompanyContract): Result<Boolean, Throwable>

    suspend fun get(id: UUID): Result<CompanyContract, Throwable>
    suspend fun getByExternalIdAndGroupCompany(
        externalId: String,
        groupCompany: String
    ): Result<CompanyContract, Throwable>

    suspend fun findByIds(ids: List<UUID>): Result<List<CompanyContract>, Throwable>

    suspend fun findByTitle(title: String): Result<List<CompanyContract>, Throwable>

    suspend fun findBySubcontractId(id: UUID): Result<CompanyContract, Throwable>

    suspend fun findByIdsAndStatuses(
        ids: List<UUID>,
        statuses: List<CompanyContractStatus>
    ): Result<List<CompanyContract>, Throwable>

    class InvalidFormatException(
        message: String,
        code: String = "invalid_format",
        cause: Throwable? = null
    ) : BadRequestException(message, code, cause) {
        constructor(description: String) : this(
            message = "Field $description with invalid format"
        )
    }

    class RequiredMainContractException(
        message: String = "Required at least one main contract",
        code: String = "required_main_contract",
        cause: Throwable? = null
    ) : BadRequestException(message, code, cause)
}
