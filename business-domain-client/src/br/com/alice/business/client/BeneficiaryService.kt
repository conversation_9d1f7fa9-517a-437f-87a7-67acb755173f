package br.com.alice.business.client

import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.MemberLifeCycleEvents
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Product
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface BeneficiaryService : Service {
    override val namespace get() = "business"
    override val serviceName get() = "beneficiary"

    suspend fun addBeneficiaryForExistentMember(
        beneficiary: Beneficiary,
        initialProductId: UUID,
        flowType: BeneficiaryOnboardingFlowType,
        phase: BeneficiaryOnboardingPhaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
        ignoreHiredAtValidation: Boolean = false,
    ): Result<Beneficiary, Throwable>

    suspend fun archiveBeneficiaryById(id: UUID): Result<Beneficiary, Throwable>

    suspend fun createBeneficiary(
        beneficiaryTransport: BeneficiaryTransport,
        initialProductId: UUID?,
        flowType: BeneficiaryOnboardingFlowType,
        cassiMemberInfo: CassiMemberInfo?,
        createOptions: CreateOptions = CreateOptions(),
        metadata: Map<String, String>? = null,
    ): Result<Beneficiary, Throwable>

    suspend fun countByFilters(
        personId: PersonId?,
        companyId: UUID?,
        parentId: UUID?,
        currentPhase: BeneficiaryOnboardingPhaseType?
    ): Result<Int, Throwable>

    suspend fun cancelBeneficiaryById(
        beneficiaryId: UUID,
        beneficiaryCancelation: BeneficiaryCancelation
    ): Result<Beneficiary, Throwable>

    suspend fun abortBeneficiaryScheduledCancel(beneficiaryId: UUID): Result<Beneficiary, Throwable>

    suspend fun cancelActivationById(beneficiaryId: UUID): Result<Beneficiary, Throwable>

    suspend fun get(
        id: UUID,
        findOptions: FindOptions = FindOptions(withOnboarding = false)
    ): Result<Beneficiary, Throwable>

    suspend fun withOnboardingAndDependents(
        beneficiary: Beneficiary
    ): Result<Beneficiary, Throwable>

    suspend fun update(model: Beneficiary): Result<Beneficiary, Throwable>
    suspend fun validateToUpdate(model: Beneficiary): Result<Beneficiary, Throwable>
    suspend fun updateInBatch(beneficiaries: List<Beneficiary>): Result<List<Beneficiary>, Throwable>
    suspend fun delete(model: Beneficiary): Result<Boolean, Throwable>
    suspend fun findByCompanyId(
        companyId: UUID,
        findOptions: FindOptions = FindOptions(),
        range: IntRange? = null
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByCompanySubContractId(
        companySubContractId: UUID,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByMemberId(
        memberId: UUID,
        findOptions: FindOptions = FindOptions()
    ): Result<Beneficiary, Throwable>

    suspend fun findByMemberIds(
        memberIds: List<UUID>,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByIds(
        ids: List<UUID>,
        findOptions: FindOptions = FindOptions(withOnboarding = false)
    ): Result<List<Beneficiary>, Throwable>

    suspend fun countByIds(ids: List<UUID>): Result<Int, Throwable>

    suspend fun findByParentId(
        parentId: UUID,
        findOptions: FindOptions = FindOptions(),
        onlyDirectDependents: Boolean = false
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByParentPerson(
        parentPersonId: PersonId,
        memberStatuses: List<MemberStatus>,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Beneficiary>, Throwable>


    suspend fun findByPersonId(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<Beneficiary, Throwable>

    suspend fun findByPersonIds(
        personIds: List<PersonId>,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByCompanyIdAndPersonIds(
        companyId: UUID,
        personIds: List<PersonId>,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findByFilters(
        personId: PersonId?,
        companyId: UUID?,
        parentId: UUID?,
        currentPhase: BeneficiaryOnboardingPhaseType?,
        range: IntRange,
        findOptions: FindOptions = FindOptions(),
    ): Result<List<Beneficiary>, Throwable>

    suspend fun getByIdAndPersonId(id: UUID, personId: PersonId): Result<Beneficiary, Throwable>

    suspend fun findByCanceledAtBetweenInclusivePaginated(
        startDate: LocalDate,
        endDate: LocalDate,
        offset: Int = 0,
        limit: Int = 100,
    ): Result<List<Beneficiary>, Throwable>

    suspend fun findPending(
        findOptions: FindOptions,
        offset: Int = 0,
        limit: Int = 100,
    ): Result<List<Beneficiary>, Throwable>

    suspend fun getCompanyContractStarted(memberId: UUID): Result<LocalDate, Throwable>

    suspend fun triggerCassiAccountNumberForBeneficiaries(
        offset: Int = 0,
        limit: Int = 100,
    ): Result<Int, Throwable>

    suspend fun canPersonHaveThisProduct(personId: PersonId, product: Product): Result<Product, Throwable>

    suspend fun reactivateMembership(beneficiaryId: UUID): Result<Beneficiary, Throwable>

    suspend fun reactivateMembershipByMember(
        memberId: UUID,
        params: MemberLifeCycleEvents? = null
    ): Result<Beneficiary, Throwable>

    suspend fun countUniquePerPersonByCompanyId(companyId: UUID): Result<Int, Throwable>

    suspend fun checkBeneficiaryFlowType(
        personId: PersonId,
        beneficiaryOnboardingFlowType: BeneficiaryOnboardingFlowType
    ): Result<Boolean, Throwable>

    suspend fun triggerCassiAccountNumberForBeneficiariesWithOlderExpirationDate(): Result<Boolean, Throwable>

    suspend fun updateCassiMembershipByExpirationDate(limit: Int = 100, offset: Int = 0): Result<Boolean, Throwable>

    suspend fun updateDependentWithNewParent(
        dependentWithOnboarding: Beneficiary,
        newParent: Beneficiary
    ): Result<Beneficiary, Throwable>

    suspend fun activateBeneficiary(beneficiaryId: UUID): Result<Beneficiary, Throwable>

    suspend fun countActiveWithNoPendingCancellation(
        companyId: UUID,
        type: BeneficiaryType? = null
    ): Result<Int, Throwable>

    data class FindOptions(
        val withOnboarding: Boolean = false,
        val withDependents: Boolean = false,
    )

    data class CreateOptions(
        val changeProduct: Boolean = false,
        val ignoreMembershipValidation: Boolean = false,
        val ignoreHiredAtValidation: Boolean = false,
        val shouldValidateAdditionalInfo: Boolean = true
    )

    data class BeneficiaryFilter(
        val companyId: UUID,
        val personIds: List<PersonId>,
        val type: BeneficiaryType,
        val hasCanceledAt: Boolean? = null,
    )

    suspend fun checkContractAlreadySigned(memberId: UUID): Result<Boolean, Throwable>
}

data class CassiMemberInfo(
    val accountNumber: String? = null,
    val startDate: String? = null,
    val expirationDate: String? = null,
) {

    fun toCassiMember(memberId: UUID) = CassiMember(
        memberId = memberId,
        accountNumber = this.accountNumber,
        startDate = this.startDate?.let { LocalDate.parse(it).atStartOfDay() },
        expirationDate = this.expirationDate?.let { LocalDate.parse(it).atStartOfDay() },
    )
}
