package br.com.alice.business.events

import br.com.alice.business.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import java.util.UUID

class CompanyContractDocumentGeneratedEvent(
    companyId: UUID,
    documentId: String,
) : NotificationEvent<CompanyContractDocumentGeneratedEvent.Payload>(
    name = name,
    producer = SERVICE_NAME,
    payload = Payload(companyId, documentId)
) {
    companion object {
        const val name = "COMPANY-CONTRACT-DOCUMENT-GENERATED"
    }

    data class Payload(
        val companyId: UUID,
        val documentId: String,
    )
}
