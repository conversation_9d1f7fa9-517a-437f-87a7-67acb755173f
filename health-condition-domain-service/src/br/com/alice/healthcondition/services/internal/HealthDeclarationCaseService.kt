package br.com.alice.healthcondition.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.pmapEachNotNull
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.CaseRecordCreatedByType
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseRecordReferenceModel
import br.com.alice.data.layer.models.CaseStatus.PENDING
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.models.HealthDeclarationAnswer
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.DuplicatedDescriptionException
import br.com.alice.healthcondition.converters.HealthConditionCaseRecordConverter
import br.com.alice.membership.client.onboarding.HealthDeclarationService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class HealthDeclarationCaseService(
    private val caseRecordService: CaseRecordService,
    private val healthDeclarationService: HealthDeclarationService
) {

    suspend fun changeResponsible(schedule: AppointmentSchedule): Result<Any, Throwable> =
        healthDeclarationService.findByPerson(schedule.personId)
            .flatMap { healthDeclaration ->
                CaseRecordReference(
                    id = healthDeclaration.id.toString(),
                    model = CaseRecordReferenceModel.HEALTH_DECLARATION
                ).let { addResponsible(it, schedule) }
            }.coFoldNotFound {
                logger.error(
                    "Health Declaration not found",
                    "personId" to schedule.personId
                )
                true.success()
            }

    private suspend fun addResponsible(healthDeclarationReference: CaseRecordReference, schedule: AppointmentSchedule): Result<List<CaseRecord>, Throwable> =
        caseRecordService.hasCaseCreatedByReference(
            personId = schedule.personId,
            reference = healthDeclarationReference
        ).pmapEachNotNull { case ->
            case.takeIf { it.responsibleStaffId != schedule.staffId && it.status == PENDING }
                ?.let { record ->
                    caseRecordService.add(
                        record.copy(
                            id = RangeUUID.generate(),
                            addedByStaffId = null,
                            caseCreatedBy = CaseRecordCreatedByType.SYSTEM,
                            responsibleStaffId = schedule.staffId!!,
                            addedAt = LocalDateTime.now()
                        )
                    )
                }
        }.get().filterNot { it.component2() is DuplicatedDescriptionException }.lift()

    suspend fun createCaseRecords(healthDeclaration: HealthDeclaration): Result<List<CaseRecord>, Throwable> {
        val conditions = healthDeclaration.conditions

        val mapConditions = conditions.associateWith { healthCondition ->
            healthDeclaration.answers.find { answer ->
                answer.healthConditions?.map { it.id }?.contains(healthCondition.id) ?: false
            }
        }

        val caseRecords = mutableListOf<CaseRecord>()
        val freeTextConditions = mutableMapOf<HealthCondition, HealthDeclarationAnswer>()

        mapConditions.map { (healthCondition, answer) ->
            buildCaseRecord(
                healthDeclarationId = healthDeclaration.id,
                personId = healthDeclaration.personId,
                healthCondition = healthCondition
            )?.let {
                caseRecords.add(it)
            } ?: freeTextConditions.put(healthCondition, answer!!)
        }

        if(freeTextConditions.isNotEmpty()) {
            buildCaseRecordForFreeText(
                healthDeclarationId = healthDeclaration.id,
                personId = healthDeclaration.personId,
                conditions = freeTextConditions
            )?.let { caseRecords.add(it) }
        }

        return caseRecords
            .map { caseRecord ->
                caseRecordService.add(caseRecord)
                    .foldError(DuplicatedDescriptionException::class to { caseRecord.success() })
            }.lift()
    }

    private fun buildCaseRecord(
        healthDeclarationId: UUID,
        personId: PersonId,
        healthCondition: HealthCondition
    ): CaseRecord? =
        HealthConditionCaseRecordConverter.convert(
            personId = personId,
            healthDeclarationId = healthDeclarationId,
            source = healthCondition
        )

    private fun buildCaseRecordForFreeText(
        healthDeclarationId: UUID,
        personId: PersonId,
        conditions: Map<HealthCondition, HealthDeclarationAnswer>
    ): CaseRecord? =
        HealthConditionCaseRecordConverter.convertFromFreeText(
            personId = personId,
            healthDeclarationId = healthDeclarationId,
            conditions = conditions
        )
}
