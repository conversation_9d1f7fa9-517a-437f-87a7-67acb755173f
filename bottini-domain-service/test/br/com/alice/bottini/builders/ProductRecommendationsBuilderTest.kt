package br.com.alice.bottini.builders

import br.com.alice.bottini.constants.PME_AGES_DEFAULT_TO_SIMULATION
import br.com.alice.bottini.models.PriceByAge
import br.com.alice.bottini.models.ProductBundleRecommendation
import br.com.alice.bottini.models.ProductRecommendation
import br.com.alice.bottini.models.RecommendedProduct
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.ProductAnchor
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.product.client.ProductBundleWithProviders
import br.com.alice.product.model.ProductWithBundlesWithProviders
import org.assertj.core.api.Assertions
import java.math.BigDecimal
import kotlin.test.Test

class ProductRecommendationsBuilderTest {
    private val ages = listOf(28, 3)
    private val lab = TestModelFactory.buildProvider()
    private val hospital = TestModelFactory.buildProvider()
    private val hospitals = listOf(hospital)
    private val laboratories = listOf(lab)
    private val labBundle =
        TestModelFactory.buildProductBundle(type = ProductBundleType.LABORATORY, providerIds = listOf(lab.id))
    private val hospBundle =
        TestModelFactory.buildProductBundle(type = ProductBundleType.HOSPITAL, providerIds = listOf(hospital.id))
    private val bundles = listOf(hospBundle, labBundle)
    private val firstPriceListingItem1 =
        TestModelFactory.buildPriceListingItem(minAge = 0, maxAge = 27, amount = BigDecimal(200))
    private val secondPriceListingItem1 =
        TestModelFactory.buildPriceListingItem(minAge = 27, maxAge = Int.MAX_VALUE, amount = BigDecimal(500))
    private val product1 = TestModelFactory.buildProduct(
        title = "Produto selecionado",
        priceListing = TestModelFactory.buildPriceListing(
            ranges = listOf(firstPriceListingItem1, secondPriceListingItem1)
        ),
        accommodation = AccommodationType.NURSERY
    )
    private val productWithBundlesWithProviders1 = ProductWithBundlesWithProviders(
        product1, listOf(
            ProductBundleWithProviders(labBundle, listOf(lab)),
            ProductBundleWithProviders(hospBundle, listOf(hospital))
        )
    )

    private val firstPriceListingItem2 =
        TestModelFactory.buildPriceListingItem(minAge = 0, maxAge = 27, amount = BigDecimal(300))
    private val secondPriceListingItem2 =
        TestModelFactory.buildPriceListingItem(minAge = 27, maxAge = Int.MAX_VALUE, amount = BigDecimal(800))
    private val product2 = TestModelFactory.buildProduct(
        title = "Produto ancora",
        anchor = ProductAnchor.ALICE_EINSTEIN,
        priceListing = TestModelFactory.buildPriceListing(
            ranges = listOf(firstPriceListingItem2, secondPriceListingItem2)
        ),
        accommodation = AccommodationType.ROOM
    )
    private val productWithBundlesWithProviders2 = ProductWithBundlesWithProviders(
        product2, listOf(
            ProductBundleWithProviders(labBundle, listOf(lab)),
            ProductBundleWithProviders(hospBundle, listOf(hospital))
        )
    )

    private val recommendedProduct1 = RecommendedProduct(
        product = productWithBundlesWithProviders1.product,
        productBundles = bundles,
        laboratories = laboratories,
        hospitals = hospitals,
        hospitalsCount = 2,
        laboratoriesCount = 3
    )
    private val recommendedProduct2 = RecommendedProduct(
        product = productWithBundlesWithProviders2.product,
        productBundles = bundles,
        laboratories = laboratories,
        hospitals = hospitals,
        hospitalsCount = 4,
        laboratoriesCount = 6
    )
    private val simulation = TestModelFactory.buildHealthProductSimulation(
        type = HealthProductSimulationType.MICRO_COMPANY,
        answers = listOf(
            HealthProductSimulationAnswer(
                answer = "2",
                questionType = HealthProductSimulationQuestionType.PEOPLE_NUMBER,
            )
        ),
        version = 8
    )

    @Test
    fun `#build build ProductRecommendations`() {
        val expected = listOf(
            ProductRecommendation(
                title = product2.anchor!!.title,
                name = product2.salesProductName,
                isSelected = false,
                pricesByAge = listOf(
                    PriceByAge(ages[0], product2.getPrice(ages[0])!!),
                    PriceByAge(ages[1], product2.getPrice(ages[1])!!)
                ),
                totalPrice = firstPriceListingItem2.amount + secondPriceListingItem2.amount,
                hospitals = productWithBundlesWithProviders2.hospitals,
                laboratories = productWithBundlesWithProviders2.labs,
                hospitalsCount = 4,
                laboratoriesCount = 6,
                anchor = product2.anchor,
                id = product2.id.toString(),
                accommodation = AccommodationType.ROOM,
                coPayment = product2.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = null,
                employeeNumberOfTotalPrice = null,
            ),
            ProductRecommendation(
                title = product1.title,
                name = product1.salesProductName,
                isSelected = true,
                pricesByAge = listOf(
                    PriceByAge(ages[0], product1.getPrice(ages[0])!!),
                    PriceByAge(ages[1], product1.getPrice(ages[1])!!)
                ),
                totalPrice = firstPriceListingItem1.amount + secondPriceListingItem1.amount,
                hospitals = productWithBundlesWithProviders1.hospitals,
                laboratories = productWithBundlesWithProviders1.labs,
                hospitalsCount = 2,
                laboratoriesCount = 3,
                anchor = product1.anchor,
                id = product1.id.toString(),
                accommodation = AccommodationType.NURSERY,
                coPayment = product1.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = null,
                employeeNumberOfTotalPrice = null,
            ),
        )

        val result = ProductRecommendationsBuilder.build(
            listOf(recommendedProduct2),
            ages,
            selectedProduct = recommendedProduct1,
            simulation,
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#build ProductRecommendations when simulation type is company`() {
        val simulation = TestModelFactory.buildHealthProductSimulation(
            type = HealthProductSimulationType.COMPANY,
            answers = listOf(
                HealthProductSimulationAnswer(
                    answer = "10-29",
                    questionType = HealthProductSimulationQuestionType.PEOPLE_NUMBER,
                )
            ),
            version = 8
        )
        val totalPriceAnchor = PriceByAge(36, product2.getPrice(36)!!).price * BigDecimal(20)
        val totalPriceSelectedProduct = PriceByAge(36, product1.getPrice(36)!!).price * BigDecimal(20)

        val expected = listOf(
            ProductRecommendation(
                title = product2.anchor!!.title,
                name = "${product2.displayName} ${product2.complementName}",
                isSelected = false,
                pricesByAge = PME_AGES_DEFAULT_TO_SIMULATION.map {
                    PriceByAge(it, product2.getPrice(it)!!)
                },
                totalPrice = totalPriceAnchor,
                hospitals = productWithBundlesWithProviders2.hospitals,
                laboratories = productWithBundlesWithProviders2.labs,
                hospitalsCount = 4,
                laboratoriesCount = 6,
                anchor = product2.anchor,
                id = product2.id.toString(),
                accommodation = AccommodationType.ROOM,
                coPayment = product2.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = product2.getPrice(36),
                employeeNumberOfTotalPrice = 20,
            ),
            ProductRecommendation(
                title = product1.title,
                name = "${product1.displayName} ${product1.complementName}",
                isSelected = true,
                pricesByAge = PME_AGES_DEFAULT_TO_SIMULATION.map {
                    PriceByAge(it, product1.getPrice(it)!!)
                },
                totalPrice = totalPriceSelectedProduct,
                hospitals = productWithBundlesWithProviders1.hospitals,
                laboratories = productWithBundlesWithProviders1.labs,
                hospitalsCount = 2,
                laboratoriesCount = 3,
                anchor = product1.anchor,
                id = product1.id.toString(),
                accommodation = AccommodationType.NURSERY,
                coPayment = product1.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = product1.getPrice(36),
                employeeNumberOfTotalPrice = 20,
            ),
        )

        val result = ProductRecommendationsBuilder.build(
            listOf(recommendedProduct2),
            ages,
            selectedProduct = recommendedProduct1,
            simulation,
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }
    @Test
    fun `#build ProductRecommendations when simulation version is 11`() {
        val simulation = TestModelFactory.buildHealthProductSimulation(
            type = HealthProductSimulationType.COMPANY,
            answers = listOf(
                HealthProductSimulationAnswer(
                    answer = "6-29",
                    questionType = HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER,
                )
            ),
            version = 11
        )
        val totalPriceAnchor = PriceByAge(36, product2.getPrice(36)!!).price * BigDecimal(6)
        val totalPriceSelectedProduct = PriceByAge(36, product1.getPrice(36)!!).price * BigDecimal(6)

        val expected = listOf(
            ProductRecommendation(
                title = product2.anchor!!.title,
                name = "${product2.displayName} ${product2.complementName}",
                isSelected = false,
                pricesByAge = PME_AGES_DEFAULT_TO_SIMULATION.map {
                    PriceByAge(it, product2.getPrice(it)!!)
                },
                totalPrice = totalPriceAnchor,
                hospitals = productWithBundlesWithProviders2.hospitals,
                laboratories = productWithBundlesWithProviders2.labs,
                hospitalsCount = 4,
                laboratoriesCount = 6,
                anchor = product2.anchor,
                id = product2.id.toString(),
                accommodation = AccommodationType.ROOM,
                coPayment = product2.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = product2.getPrice(36),
                employeeNumberOfTotalPrice = 6,
            ),
            ProductRecommendation(
                title = product1.title,
                name = "${product1.displayName} ${product1.complementName}",
                isSelected = true,
                pricesByAge = PME_AGES_DEFAULT_TO_SIMULATION.map {
                    PriceByAge(it, product1.getPrice(it)!!)
                },
                totalPrice = totalPriceSelectedProduct,
                hospitals = productWithBundlesWithProviders1.hospitals,
                laboratories = productWithBundlesWithProviders1.labs,
                hospitalsCount = 2,
                laboratoriesCount = 3,
                anchor = product1.anchor,
                id = product1.id.toString(),
                accommodation = AccommodationType.NURSERY,
                coPayment = product1.coPayment,
                bundles = bundles.map { ProductBundleRecommendation(it.id, it.type) },
                priceByEmployeeRange = product1.getPrice(36),
                employeeNumberOfTotalPrice = 6,
            ),
        )

        val result = ProductRecommendationsBuilder.build(
            listOf(recommendedProduct2),
            ages,
            selectedProduct = recommendedProduct1,
            simulation,
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }
}
