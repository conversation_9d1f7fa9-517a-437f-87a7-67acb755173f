package br.com.alice.bottini.consumers

import br.com.alice.acquisition.client.OfferService
import br.com.alice.acquisition.model.Offer
import br.com.alice.bottini.builders.OfferBuilder
import br.com.alice.bottini.client.SalesAgentService
import br.com.alice.bottini.converters.OngoingCompanyDealConverter
import br.com.alice.bottini.events.CompanyCreatedOnHubspotEvent
import br.com.alice.bottini.events.OngoingCompanyDealCreatedOnHubspotEvent
import br.com.alice.bottini.services.DealSalesInfoService
import br.com.alice.common.RangeUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.crm.sales.b2b.AssociationResultV2
import br.com.alice.communication.crm.sales.b2b.DealResultV2
import br.com.alice.communication.crm.sales.b2b.DealWithStage
import br.com.alice.communication.crm.sales.b2b.DealsResult
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipeline
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.membership.client.PromoCodeService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class CompanyCreatedOnHubspotConsumerTest : ConsumerTest() {
    private val hubspotSalesFunnelPipeline: HubspotSalesFunnelPipeline = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val dealSalesInfoService: DealSalesInfoService = mockk()
    private val promoCodeService: PromoCodeService = mockk()
    private val offerService: OfferService = mockk()
    private val salesAgentService: SalesAgentService = mockk()

    private val consumer = CompanyCreatedOnHubspotConsumer(
        hubspotSalesFunnelPipeline,
        kafkaProducerService,
        dealSalesInfoService,
        promoCodeService,
        offerService,
        salesAgentService,
    )
    private val lead = TestModelFactory.buildLead(
        promoCodeId = RangeUUID.generate(),
    )
    private val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()
    private val simulation = TestModelFactory.buildHealthProductSimulation(
        type = HealthProductSimulationType.MICRO_COMPANY,
        answers = listOf(
            HealthProductSimulationAnswer(
                questionType = HealthProductSimulationQuestionType.COMPANY_CNPJ,
                answer = "12345678901234",
            ),
            HealthProductSimulationAnswer(
                questionType = HealthProductSimulationQuestionType.COMPANY_NAME,
                answer = "Alice",
            ),
            HealthProductSimulationAnswer(
                questionType = HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER,
                answer = "1-5",
            ),
        )
    )
    private val personSalesInfo = TestModelFactory.buildPersonSalesInfo(hubspotContactId = "123")
    private val event = CompanyCreatedOnHubspotEvent(
        lead,
        ongoingCompanyDeal,
        simulation,
        personSalesInfo,
    )
    private val promoCode = TestModelFactory.buildPromoCode(
        id = lead.promoCodeId!!,
    )

    @Test
    fun `#createDealOnHubspot should create the deal (when the deal does not exist) and associate the contact`() = runBlocking {
        val deal = DealsResult(
            deals = emptyList(),
        )
        val dealResult = DealResultV2(id = "123")
        val opportunity = TestModelFactory.buildOpportunity(
            simulationId = simulation.id,
        )
        val dealSalesInfo = TestModelFactory.buildDealSalesInfo(
            simulationId = simulation.id,
            lastOpportunityId = opportunity.id,
            hubspotDealId = dealResult.id,
        )

        coEvery {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(ongoingCompanyDeal.cnpj)
        } returns deal
        coEvery {
            promoCodeService.get(lead.promoCodeId!!)
        } returns promoCode.success()
        coEvery {
            hubspotSalesFunnelPipeline.createDeal(
                OngoingCompanyDealConverter.toDealV2(
                    lead,
                    simulation,
                    ongoingCompanyDeal,
                    promoCode,
                )
            )
        } returns dealResult
        coEvery {
            dealSalesInfoService.add(match {
                it.simulationId == simulation.id &&
                        it.hubspotDealId == dealResult.id
            })
        } returns dealSalesInfo.success()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealResult.id,
                dealSalesInfo.hubspotDealId!!,
            )
        } returns AssociationResultV2(true)
        coEvery {
            kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
        } returns mockk()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealId = "34544",
                contactId = personSalesInfo.hubspotContactId!!,
            )
        } returns mockk()
        coEvery {
            salesAgentService.distributeLeads(
                simulation = simulation,
                externalOfferId = dealResult.id,
                externalCommercialRepresentative = personSalesInfo.hubspotContactId!!,
            )
        } returns true.success()

        consumer.createDealOnHubspot(event)

        coVerifyNone {
            hubspotSalesFunnelPipeline.findDealByCnpj(any())
            hubspotSalesFunnelPipeline.getAssociationFromDealToContact(any())
        }
        coVerifyOnce {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(any())
            hubspotSalesFunnelPipeline.createDeal(any())
            promoCodeService.get(any())
            dealSalesInfoService.add(any())
            hubspotSalesFunnelPipeline.associateContactToDeal(any(), any())
            kafkaProducerService.produce(any())
            salesAgentService.distributeLeads(any(), any(), any())
        }
    }

    @Test
    fun `#createDealOnHubspot should create the deal (when the deal does not exist) and should not associate the contact`() = runBlocking {
        val deal = DealsResult(
            deals = emptyList(),
        )
        val dealResult = DealResultV2(id = "123")
        val opportunity = TestModelFactory.buildOpportunity(
            simulationId = simulation.id,
        )
        val dealSalesInfo = TestModelFactory.buildDealSalesInfo(
            simulationId = simulation.id,
            lastOpportunityId = opportunity.id,
            hubspotDealId = dealResult.id,
        )

        coEvery {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(ongoingCompanyDeal.cnpj)
        } returns deal
        coEvery {
            promoCodeService.get(lead.promoCodeId!!)
        } returns promoCode.success()
        coEvery {
            hubspotSalesFunnelPipeline.createDeal(
                OngoingCompanyDealConverter.toDealV2(
                    lead,
                    simulation,
                    ongoingCompanyDeal,
                    promoCode,
                )
            )
        } returns dealResult
        coEvery {
            dealSalesInfoService.add(match {
                it.simulationId == simulation.id &&
                        it.hubspotDealId == dealResult.id
            })
        } returns dealSalesInfo.success()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealResult.id,
                personSalesInfo.hubspotContactId!!,
            )
        } returns AssociationResultV2(true)
        coEvery {
            kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
        } returns mockk()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealId = "34544",
                contactId = personSalesInfo.hubspotContactId!!,
            )
        } returns mockk()
        coEvery {
            salesAgentService.distributeLeads(
                simulation = simulation,
                externalOfferId = dealResult.id,
                externalCommercialRepresentative = personSalesInfo.hubspotContactId!!,
            )
        } returns true.success()

        consumer.createDealOnHubspot(event)

        coVerifyNone {
            hubspotSalesFunnelPipeline.findDealByCnpj(any())
            hubspotSalesFunnelPipeline.getAssociationFromDealToContact(any())
        }
        coVerifyOnce {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(any())
            hubspotSalesFunnelPipeline.createDeal(any())
            promoCodeService.get(any())
            dealSalesInfoService.add(any())
            hubspotSalesFunnelPipeline.associateContactToDeal(any(), any())
            kafkaProducerService.produce(any())
            salesAgentService.distributeLeads(any(), any(), any())
        }
    }

    @Test
    fun `#createDealOnHubspot should not create the deal (when the deal already exists and it is not LOST_LEAD or CANCELED) and should associate the contact`() = runBlocking {
        val deal = DealsResult(
            deals = listOf(
                DealWithStage(
                    id = "34544",
                    stage = "*********",
                )
            )
        )

        coEvery {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(ongoingCompanyDeal.cnpj)
        } returns deal
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                contactId = "123",
                dealId = "34544",
            )
        } returns mockk()

        consumer.createDealOnHubspot(event)

        coVerifyNone {
            hubspotSalesFunnelPipeline.findDealByCnpj(any())
            hubspotSalesFunnelPipeline.createDeal(any())
            kafkaProducerService.produce(any())
            dealSalesInfoService.add(any())
            promoCodeService.get(any())
            salesAgentService.distributeLeads(any(), any(), any())
        }
        coVerifyOnce {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(any())
            hubspotSalesFunnelPipeline.associateContactToDeal(any(), any())
        }
    }

    @Test
    fun `#createDealOnHubspot should not distribute lead when simulation is more than 5`() = runBlocking {
        val simulation = TestModelFactory.buildHealthProductSimulation(
            type = HealthProductSimulationType.COMPANY,
            answers = listOf(
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_CNPJ,
                    answer = "12345678901234",
                ),
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_NAME,
                    answer = "Alice",
                ),
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER,
                    answer = "6-29",
                ),
            )
        )
        val deal = DealsResult(
            deals = emptyList(),
        )
        val dealResult = DealResultV2(id = "123")
        val opportunity = TestModelFactory.buildOpportunity(
            simulationId = simulation.id,
        )
        val dealSalesInfo = TestModelFactory.buildDealSalesInfo(
            simulationId = simulation.id,
            lastOpportunityId = opportunity.id,
            hubspotDealId = dealResult.id,
        )
        val createDeal = OngoingCompanyDealConverter.toDealV2(
            lead,
            simulation,
            ongoingCompanyDeal,
            promoCode,
        )
        val event = CompanyCreatedOnHubspotEvent(
            lead,
            ongoingCompanyDeal,
            simulation,
            personSalesInfo,
        )

        coEvery {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(ongoingCompanyDeal.cnpj)
        } returns deal
        coEvery {
            promoCodeService.get(lead.promoCodeId!!)
        } returns promoCode.success()
        coEvery {
            hubspotSalesFunnelPipeline.createDeal(createDeal)
        } returns dealResult
        coEvery {
            dealSalesInfoService.add(match {
                it.simulationId == simulation.id &&
                        it.hubspotDealId == dealResult.id
            })
        } returns dealSalesInfo.success()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealResult.id,
                personSalesInfo.hubspotContactId!!,
            )
        } returns AssociationResultV2(true)
        coEvery {
            kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
        } returns mockk()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealId = "34544",
                contactId = personSalesInfo.hubspotContactId!!,
            )
        } returns mockk()

        consumer.createDealOnHubspot(event)

        coVerifyNone {
            hubspotSalesFunnelPipeline.findDealByCnpj(any())
            hubspotSalesFunnelPipeline.getAssociationFromDealToContact(any())
        }
        coVerifyOnce {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(any())
            hubspotSalesFunnelPipeline.createDeal(any())
            promoCodeService.get(any())
            dealSalesInfoService.add(any())
            hubspotSalesFunnelPipeline.associateContactToDeal(any(), any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `should process deal by acquisition domain when feature is enabled`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.BOTTINI,
            "deal_should_be_processed_by_acquisition_domain",
            true,
        ) {
            val offer = OfferBuilder.build(
                ongoingCompanyDeal,
                lead,
                simulation,
                "1",
                personSalesInfo.hubspotContactId
            ).copy(
                additionalProperties = mapOf(
                    "pipelineExternalId" to "123",
                    "pipelineExternalContactId" to "123",
                )
            )
            val dealSalesInfo = TestModelFactory.buildDealSalesInfo(
                simulationId = simulation.id,
                lastOpportunityId = RangeUUID.generate(),
                hubspotDealId = RangeUUID.generate().toString(),
            )

            val emptyOffers = emptyList<Offer>()

            coEvery {
                offerService.searchOffersByCnpj(ongoingCompanyDeal.cnpj)
            } returns emptyOffers.success()

            coEvery {
                offerService.createOffer(any(), any(), any())
            } returns offer.success()

            coEvery {
                dealSalesInfoService.add(any())
            } returns dealSalesInfo.success()

            coEvery {
                salesAgentService.distributeLeads(any(), any(), any())
            } returns true.success()

            coEvery {
                kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
            } returns mockk()

            consumer.createDealOnHubspot(event)

            coVerifyOnce {
                offerService.searchOffersByCnpj(any())
                offerService.createOffer(any(), any(), any())
                dealSalesInfoService.add(any())
                salesAgentService.distributeLeads(any(), any(), any())
                kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
            }
        }
    }

    @Test
    fun `#createDealOnHubspot should not distribute lead when simulation has test email`() = runBlocking {
        val simulation = TestModelFactory.buildHealthProductSimulation(
            type = HealthProductSimulationType.COMPANY,
            answers = listOf(
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_CNPJ,
                    answer = "12345678901234",
                ),
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_NAME,
                    answer = "Alice",
                ),
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.EMAIL,
                    answer = "<EMAIL>",
                ),
                HealthProductSimulationAnswer(
                    questionType = HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER,
                    answer = "6-29",
                ),
            )
        )
        val deal = DealsResult(
            deals = emptyList(),
        )
        val dealResult = DealResultV2(id = "123")
        val opportunity = TestModelFactory.buildOpportunity(
            simulationId = simulation.id,
        )
        val dealSalesInfo = TestModelFactory.buildDealSalesInfo(
            simulationId = simulation.id,
            lastOpportunityId = opportunity.id,
            hubspotDealId = dealResult.id,
        )
        val createDeal = OngoingCompanyDealConverter.toDealV2(
            lead,
            simulation,
            ongoingCompanyDeal,
            promoCode,
        )
        val event = CompanyCreatedOnHubspotEvent(
            lead,
            ongoingCompanyDeal,
            simulation,
            personSalesInfo,
        )

        coEvery {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(ongoingCompanyDeal.cnpj)
        } returns deal
        coEvery {
            promoCodeService.get(lead.promoCodeId!!)
        } returns promoCode.success()
        coEvery {
            hubspotSalesFunnelPipeline.createDeal(createDeal)
        } returns dealResult
        coEvery {
            dealSalesInfoService.add(match {
                it.simulationId == simulation.id &&
                        it.hubspotDealId == dealResult.id
            })
        } returns dealSalesInfo.success()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealResult.id,
                personSalesInfo.hubspotContactId!!,
            )
        } returns AssociationResultV2(true)
        coEvery {
            kafkaProducerService.produce(match { it is OngoingCompanyDealCreatedOnHubspotEvent })
        } returns mockk()
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToDeal(
                dealId = "34544",
                contactId = personSalesInfo.hubspotContactId!!,
            )
        } returns mockk()

        consumer.createDealOnHubspot(event)

        coVerifyNone {
            hubspotSalesFunnelPipeline.findDealByCnpj(any())
            hubspotSalesFunnelPipeline.getAssociationFromDealToContact(any())
        }
        coVerifyOnce {
            hubspotSalesFunnelPipeline.findAllDealsByCnpj(any())
            hubspotSalesFunnelPipeline.createDeal(any())
            promoCodeService.get(any())
            dealSalesInfoService.add(any())
            hubspotSalesFunnelPipeline.associateContactToDeal(any(), any())
            kafkaProducerService.produce(any())
        }
    }
}
