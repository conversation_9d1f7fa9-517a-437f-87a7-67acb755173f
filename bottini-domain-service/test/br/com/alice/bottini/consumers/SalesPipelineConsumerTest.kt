package br.com.alice.bottini.consumers

import br.com.alice.bottini.client.SalesPipelineService
import br.com.alice.bottini.events.OpportunityCreatedEvent
import br.com.alice.bottini.events.PMELeadUpsertedEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.Opportunity
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class SalesPipelineConsumerTest: ConsumerTest() {
    private val salesPipelineService: SalesPipelineService = mockk()

    private val consumer = SalesPipelineConsumer(
        salesPipelineService,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
    }

    @Test
    fun `#upsertDeal should return true`(): Unit = runBlocking {
        val simulation = TestModelFactory.buildHealthProductSimulation()
        val product = TestModelFactory.buildProduct()
        val opportunity = Opportunity(
            simulationId = simulation.id,
            prices = product.prices,
            productId = product.id,
            expiresAt = LocalDateTime.now().plusDays(30)
        )

        coEvery {
            salesPipelineService.upsertDeal(
                opportunity = opportunity,
            )
        } returns true.success()

        val response = consumer.upsertDeal(
            OpportunityCreatedEvent(
                opportunity,
            )
        )

        assertThat(response).isSuccess()
    }

    @Test
    fun `upsertEntitiesForPMELead should return true`() = runBlocking<Unit> {
        val lead = TestModelFactory.buildLead()
        val trackingInfo = TestModelFactory.buildTrackingInfo()
        val simulation = TestModelFactory.buildHealthProductSimulation(
            answers = listOf(
                TestModelFactory.buildHealthProductSimulationAnswer(
                    "500+",
                    HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER
                ),
                TestModelFactory.buildHealthProductSimulationAnswer(
                    "76.417.873/0001-66",
                    HealthProductSimulationQuestionType.COMPANY_CNPJ
                ),
                TestModelFactory.buildHealthProductSimulationAnswer(
                "company name",
                    HealthProductSimulationQuestionType.COMPANY_NAME
                ),
                TestModelFactory.buildHealthProductSimulationAnswer(
                    "BH",
                    HealthProductSimulationQuestionType.COMPANY_CITY
                )
            ),
            trackingInfo = trackingInfo,
            leadId = lead.id,
            type = HealthProductSimulationType.COMPANY
        )

        coEvery { salesPipelineService.upsertEntitiesForPMELead(lead, simulation) } returns true.success()

        val response = consumer.upsertEntitiesForPMELead(PMELeadUpsertedEvent(lead, simulation))

        assertThat(response).isSuccess()
    }
}
