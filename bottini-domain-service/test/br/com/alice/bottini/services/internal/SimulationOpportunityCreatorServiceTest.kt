package br.com.alice.bottini.services.internal

import br.com.alice.bottini.client.OpportunityService
import br.com.alice.bottini.services.SimulationMockkHelper
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class SimulationOpportunityCreatorServiceTest: SimulationMockkHelper(){

    private val productService: ProductService = mockk()
    private val opportunityService: OpportunityService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val personService: PersonService = mockk()
    private val productRecommendationService: SimulationProductRecommendationService = mockk()

    private val service = SimulationOpportunityCreatorService(
        productService,
        opportunityService,
        kafkaProducerService,
        personService,
        productRecommendationService
    )

    private val currentProductPriceListing = TestModelFactory.buildProductPriceListing(id = mainProduct.id)
    val person = TestModelFactory.buildPerson(opportunityId = opportunity.id)

    @Test
    fun `#create should create an opportunity for main simulation when the user enters more than one age but should not produce OpportunityCreatedEvent, since the employee number is equal to 6-10`() = runBlocking {
        val simulation = mainSimulation.copy(
            answers = mainSimulation.answers +
                    listOf(HealthProductSimulationAnswer("6-9", HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER))
        )

        coEvery { productRecommendationService.recommend(any()) } returns mainProduct.success()
        coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing.success()
        coEvery { opportunityService.getLastBySimulation(any()) } returns opportunity.success()

        coEvery { opportunityService.addList(any())
        } answers {
            listOf(opportunity).success()
        }

        coEvery { personService.findByOpportunities(any())
        } answers {
            listOf(person).success()
        }

        val result = service.createOpportunitiesInBatch(simulation, emptyList()).get()

        coVerifyOnce { productRecommendationService.recommend(any()) }
        coVerifyOnce { productService.getCurrentProductPriceListing(any()) }
        coVerify(exactly = 1) { opportunityService.getLastBySimulation(any()) }
        coVerifyOnce { personService.findByOpportunities(any()) }
        coVerifyOnce { opportunityService.addList(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }

        assertEquals(result.size, 1)
    }

    @Test
    fun `#create should create an opportunity for main simulation when the user enters more than one age but should not produce OpportunityCreatedEvent, since the employee number is equal to 10-29`() = runBlocking {
        val simulation = mainSimulation.copy(
            answers = mainSimulation.answers +
                    listOf(HealthProductSimulationAnswer("10-29", HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER))
        )

        coEvery { productRecommendationService.recommend(any()) } returns mainProduct.success()
        coEvery { productService.getCurrentProductPriceListing(any()) } returns currentProductPriceListing.success()
        coEvery { opportunityService.getLastBySimulation(any()) } returns opportunity.success()

        coEvery { opportunityService.addList(any())
        } answers {
            listOf(opportunity).success()
        }

        coEvery { personService.findByOpportunities(any())
        } answers {
            listOf(person).success()
        }

        val result = service.createOpportunitiesInBatch(simulation, emptyList()).get()

        coVerifyOnce { productRecommendationService.recommend(any()) }
        coVerifyOnce { productService.getCurrentProductPriceListing(any()) }
        coVerify(exactly = 1) { opportunityService.getLastBySimulation(any()) }
        coVerifyOnce { personService.findByOpportunities(any()) }
        coVerifyOnce { opportunityService.addList(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }

        assertEquals(result.size, 1)
    }
}
