package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class NameQuestionAlgorithmTest {

    private val simulation = HealthProductSimulation()

    @Test
    fun `#getQuestion should return expected question whether the response`() = runBlocking<Unit> {
        val actual = NameQuestionAlgorithm.getQuestion()
        val expected = HealthProductSimulationQuestion(type = HealthProductSimulationQuestionType.NAME)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#nextQuestion should return null`() = runBlocking<Unit> {
        val actual =
            NameQuestionAlgorithm.nextQuestion(simulation.copy(simulatorVersion = HealthProductSimulationSimulatorVersion.V2))

        assertThat(actual).isEqualTo(null)
    }

    @Test
    fun `#defineSimulationType should return null`() = runBlocking<Unit> {
        val actual = NameQuestionAlgorithm.defineSimulationType("")

        assertThat(actual).isEqualTo(null)
    }

}
