package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.bottini.models.HealthProductSimulationQuestionOption
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class MicroCompanyDataAlgorithmTest {
    @Test
    fun `#getQuestion should return expected question options`() = runBlocking<Unit> {
        val question = MicroCompanyDataAlgorithm.getQuestion()
        val expectedQuestion =
            HealthProductSimulationQuestion(
                type = HealthProductSimulationQuestionType.MICRO_COMPANY_DATA,
                options = listOf(
                    HealthProductSimulationQuestionOption(text = "São Paulo", value = "São Paulo"),
                    HealthProductSimulationQuestionOption(text = "Barueri", value = "Barueri"),
                    HealthProductSimulationQuestionOption(text = "Campinas", value = "Campinas"),
                    HealthProductSimulationQuestionOption(text = "Carapicuíba", value = "Carapicuíba"),
                    HealthProductSimulationQuestionOption(text = "Cotia", value = "Cotia"),
                    HealthProductSimulationQuestionOption(text = "Diadema", value = "Diadema"),
                    HealthProductSimulationQuestionOption(text = "Guarulhos", value = "Guarulhos"),
                    HealthProductSimulationQuestionOption(text = "Jundiaí", value = "Jundiaí"),
                    HealthProductSimulationQuestionOption(text = "Osasco", value = "Osasco"),
                    HealthProductSimulationQuestionOption(text = "Santana de Parnaíba", value = "Santana de Parnaíba"),
                    HealthProductSimulationQuestionOption(text = "Santo André", value = "Santo André"),
                    HealthProductSimulationQuestionOption(text = "São Bernardo do Campo", value = "São Bernardo do Campo"),
                    HealthProductSimulationQuestionOption(text = "São Caetano do Sul", value = "São Caetano do Sul"),
                    HealthProductSimulationQuestionOption(text = "São José dos Campos", value = "São José dos Campos"),
                    HealthProductSimulationQuestionOption(text = "Sorocaba", value = "Sorocaba"),
                    HealthProductSimulationQuestionOption(text = "Taboão da Serra", value = "Taboão da Serra"),
                    HealthProductSimulationQuestionOption(text = "Outras Cidades", value = "Outras Cidades")
                )
            )

        Assertions.assertThat(question).isEqualTo(expectedQuestion)
    }

    @Test
    fun `#nextQuestion should return expected question whether the response`() = runBlocking<Unit> {
        val question = MicroCompanyDataAlgorithm.nextQuestion(HealthProductSimulation())

        Assertions.assertThat(question).isEqualTo(null)
    }

    @Test
    fun `#defineSimulationType should return mei when mei is true`() =
        runBlocking<Unit> {
            val result = MicroCompanyDataAlgorithm.defineSimulationType("true",
                HealthProductSimulation(
                    answers = listOf(
                        HealthProductSimulationAnswer(
                            questionType = HealthProductSimulationQuestionType.MEI,
                            answer = "true",
                        )
                    )
                )
            )

            Assertions.assertThat(result).isEqualTo(HealthProductSimulationType.MEI)
        }

    @Test
    fun `#defineSimulationType should return micro company when mei is false`() =
        runBlocking<Unit> {
            val result = MicroCompanyDataAlgorithm.defineSimulationType("false",
                HealthProductSimulation(
                    answers = listOf(
                        HealthProductSimulationAnswer(
                            questionType = HealthProductSimulationQuestionType.MEI,
                            answer = "false",
                        )
                    )
                )
            )

            Assertions.assertThat(result).isEqualTo(HealthProductSimulationType.MICRO_COMPANY)
        }
}
