package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class CompanyResponsiblePersonDataAlgorithmTest {
    @Test
    fun `#getQuestion should return expected question whether the response`() = runBlocking<Unit> {
        val question = CompanyResponsiblePersonDataAlgorithm.getQuestion()
        val expectedQuestion =
            HealthProductSimulationQuestion(type = HealthProductSimulationQuestionType.COMPANY_RESPONSIBLE_PERSON_DATA)

        Assertions.assertThat(question).isEqualTo(expectedQuestion)
    }

    @Test
    fun `#nextQuestion should return expected question whether the response`() = runBlocking<Unit> {
        val question = CompanyResponsiblePersonDataAlgorithm.nextQuestion(HealthProductSimulation())
        val expectedQuestion = HealthProductSimulationQuestionType.COMPANY_DATA

        Assertions.assertThat(question).isEqualTo(expectedQuestion)
    }

    @Test
    fun `#defineSimulationType should return null`() = runBlocking<Unit> {
        val actual = CompanyResponsiblePersonDataAlgorithm.defineSimulationType("")

        Assertions.assertThat(actual).isEqualTo(null)
    }
}
