package br.com.alice.bottini.services

import br.com.alice.bottini.client.HealthProductSimulationService
import br.com.alice.bottini.models.ProductComparison
import br.com.alice.bottini.models.ProductWithHospitalsAndLaboratories
import br.com.alice.bottini.models.SimulationWithOpportunity
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.B2BSubTypes
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.ProductAnchor
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.product.client.ProductBundleService
import br.com.alice.product.client.ProductService
import br.com.alice.provider.client.OrderBy
import br.com.alice.provider.client.ProviderService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProductComparisonServiceImplTest {

    private val healthProductSimulationService: HealthProductSimulationService = mockk()
    private val productService: ProductService = mockk()
    private val productBundleService: ProductBundleService = mockk()
    private val providerService: ProviderService = mockk()

    private val service = ProductComparisonServiceImpl(
        healthProductSimulationService,
        productService,
        productBundleService,
        providerService
    )

    private val mainProviders = TestModelFactory.buildProviders()
    private val mainBundles = listOf(
        TestModelFactory.buildProductBundle(
            providerIds = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
            type = ProductBundleType.LABORATORY
        ),
        TestModelFactory.buildProductBundle(
            providerIds = mainProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                .map { it.id },
            type = ProductBundleType.HOSPITAL
        ),
        TestModelFactory.buildProductBundle(
            providerIds = mainProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
            type = ProductBundleType.MATERNITY
        ),
    )
    private val mainProduct = TestModelFactory.buildProduct(
        bundleIds = mainBundles.map { it.id },
        title = "main product"
    )
    private val anchoredProviders = TestModelFactory.buildProviders()

    private val simulation = TestModelFactory.buildHealthProductSimulation(
        answers = listOf(
            HealthProductSimulationAnswer("<EMAIL>", HealthProductSimulationQuestionType.EMAIL),
            HealthProductSimulationAnswer("11999999999", HealthProductSimulationQuestionType.PHONE_NUMBER)
        )
    )
    private val opportunity = TestModelFactory.buildOpportunity(
        productId = mainProduct.id,
        simulationId = simulation.id
    )

    private val simulationWithOpportunity = SimulationWithOpportunity(
        simulation = simulation,
        opportunity = opportunity
    )

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @AfterTest
    fun afterTest() {
        unmockkAll()
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison for consumer simulation`(): Unit = runBlocking {
        val othersProviders = anchoredProviders + mainProviders
        val othersBundles = listOf(
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                type = ProductBundleType.LABORATORY
            ),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                    .map { it.id }),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                type = ProductBundleType.MATERNITY
            ),
        )
        val otherProduct = TestModelFactory.buildProduct(
            anchor = ProductAnchor.ALICE_FULL,
            bundleIds = othersBundles.map { it.id },
            title = "achored product"
        )

        // Get Simulation
        coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()

        // Get available products and bundles
        coEvery {
            productService.listVisibleForSaleProducts(
                findOptions = ProductService.FindOptions(withPriceListing = true),
                types = listOf(ProductType.B2C)
            )
        } returns listOf(mainProduct, otherProduct).success()

        coEvery {
            productBundleService.findActivesByIdsAndTypes(
                (mainBundles + othersBundles).map { it.id },
                listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
            )
        } returns (mainBundles + othersBundles).success()

        // Get selected product providers
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

        val expectedResult = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = mainBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = mainBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = othersBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = othersBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                )
            ),
            selectedProductId = mainProduct.id
        )

        val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison for consumer simulation without HAOC hospitals`(): Unit = runBlocking {
        val mainProviders = mainProviders.filter { it.id != mainProviders[0].id && it.id != mainProviders[1].id }
        val bundles = listOf(
            TestModelFactory.buildProductBundle(
                providerIds = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                type = ProductBundleType.LABORATORY
            ),
            TestModelFactory.buildProductBundle(
                providerIds = mainProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                    .map { it.id },
                type = ProductBundleType.HOSPITAL
            ),
            TestModelFactory.buildProductBundle(
                providerIds = mainProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                type = ProductBundleType.MATERNITY
            ),
        )
        val mainProduct = TestModelFactory.buildProduct(
            bundleIds = bundles.map { it.id },
            title = "main product"
        )
        coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()
        coEvery {
            productService.listVisibleForSaleProducts(
                findOptions = ProductService.FindOptions(withPriceListing = true),
                types = listOf(ProductType.B2C),
            )
        } returns listOf(mainProduct).success()
        coEvery {
            productBundleService.findActivesByIdsAndTypes(
                bundles.map { it.id },
                listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
            )
        } returns (bundles).success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

        val expectedResult = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = bundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = bundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                ),
            ),
            selectedProductId = mainProduct.id
        )

        val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison for micro company simulation`(): Unit = runBlocking {
        val simulation = simulation.copy(type = HealthProductSimulationType.MICRO_COMPANY)
        val othersProviders = anchoredProviders + mainProviders
        val othersBundles = listOf(
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                type = ProductBundleType.LABORATORY
            ),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                    .map { it.id }),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                type = ProductBundleType.MATERNITY
            ),
        )
        val otherProduct = TestModelFactory.buildProduct(
            anchor = ProductAnchor.ALICE_FULL_B2B,
            bundleIds = othersBundles.map { it.id },
            title = "achored product"
        )

        // Get Simulation
        coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()

        // Get available products and bundles
        coEvery {
            productService.listVisibleForSaleProducts(
                findOptions = ProductService.FindOptions(withPriceListing = true),
                types = listOf(ProductType.B2B),
                subType = B2BSubTypes.ME
            )
        } returns listOf(mainProduct, otherProduct).success()

        coEvery {
            productBundleService.findActivesByIdsAndTypes(
                (mainBundles + othersBundles).map { it.id },
                listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
            )
        } returns (mainBundles + othersBundles).success()

        // Get selected product providers
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

        val expectedResult = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = mainBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = mainBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = othersBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = othersBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                )
            ),
            selectedProductId = mainProduct.id
        )

        val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison for mei simulation`(): Unit = runBlocking {
        val simulation = simulation.copy(type = HealthProductSimulationType.MEI)
        val othersProviders = anchoredProviders + mainProviders
        val othersBundles = listOf(
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                type = ProductBundleType.LABORATORY
            ),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                    .map { it.id }),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                type = ProductBundleType.MATERNITY
            ),
        )
        val otherProduct = TestModelFactory.buildProduct(
            anchor = ProductAnchor.ALICE_FULL_B2B,
            bundleIds = othersBundles.map { it.id },
            title = "achored product"
        )

        // Get Simulation
        coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()

        // Get available products and bundles
        coEvery {
            productService.listVisibleForSaleProducts(
                findOptions = ProductService.FindOptions(withPriceListing = true),
                types = listOf(ProductType.B2B),
                subType = B2BSubTypes.ME
            )
        } returns listOf(mainProduct, otherProduct).success()

        coEvery {
            productBundleService.findActivesByIdsAndTypes(
                (mainBundles + othersBundles).map { it.id },
                listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
            )
        } returns (mainBundles + othersBundles).success()

        // Get selected product providers
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

        val expectedResult = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = mainBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = mainBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = othersBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = othersBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                )
            ),
            selectedProductId = mainProduct.id
        )

        val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison for company simulation`(): Unit = runBlocking {
        val simulation = simulation.copy(type = HealthProductSimulationType.COMPANY)
        val othersProviders = anchoredProviders + mainProviders
        val othersBundles = listOf(
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                type = ProductBundleType.LABORATORY
            ),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                    .map { it.id }),
            TestModelFactory.buildProductBundle(
                providerIds = othersProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                type = ProductBundleType.MATERNITY
            ),
        )
        val otherProduct = TestModelFactory.buildProduct(
            anchor = ProductAnchor.ALICE_FULL_B2B,
            bundleIds = othersBundles.map { it.id },
            title = "achored product"
        )

        // Get Simulation
        coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()

        // Get available products and bundles
        coEvery {
            productService.listVisibleForSaleProducts(
                findOptions = ProductService.FindOptions(withPriceListing = true),
                types = listOf(ProductType.B2B),
                subType = B2BSubTypes.P
            )
        } returns listOf(mainProduct, otherProduct).success()

        coEvery {
            productBundleService.findActivesByIdsAndTypes(
                (mainBundles + othersBundles).map { it.id },
                listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
            )
        } returns (mainBundles + othersBundles).success()

        // Get selected product providers
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
        coEvery {
            providerService.searchByIdsAndNameWithRange(
                ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                range = IntRange(0, 7),
                orderBy = OrderBy.FLAGSHIP
            )
        } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

        val expectedResult = ProductComparison(
            simulation = simulation,
            productWithHospitalsAndLaboratories = listOf(
                ProductWithHospitalsAndLaboratories(
                    product = mainProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = mainBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = mainBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                ),
                ProductWithHospitalsAndLaboratories(
                    product = otherProduct,
                    hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                    laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                    totalOfHospitals = othersBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                        .map { it.providerIds }.flatten().size,
                    totalOfLaboratories = othersBundles.filter { ProductBundleType.LABORATORY == it.type }
                        .map { it.providerIds }.flatten().size,
                )
            ),
            selectedProductId = mainProduct.id
        )

        val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

        Assertions.assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getProductOptionsToComparison return product comparison when selected product is not available`(): Unit =
        runBlocking {
            val othersProviders = anchoredProviders + mainProviders
            val othersBundles = listOf(
                TestModelFactory.buildProductBundle(
                    providerIds = othersProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                    type = ProductBundleType.LABORATORY
                ),
                TestModelFactory.buildProductBundle(
                    providerIds = othersProviders.filter { it.type == ProviderType.HOSPITAL || it.type == ProviderType.CHILDREN }
                        .map { it.id }),
                TestModelFactory.buildProductBundle(
                    providerIds = othersProviders.filter { it.type == ProviderType.MATERNITY }.map { it.id },
                    type = ProductBundleType.MATERNITY
                ),
            )
            val otherProduct = TestModelFactory.buildProduct(
                anchor = ProductAnchor.ALICE_FULL,
                bundleIds = othersBundles.map { it.id },
                title = "achored product"
            )

            // Get Simulation
            coEvery { healthProductSimulationService.get(simulation.id) } returns simulation.success()

            // Get available products and bundles
            coEvery {
                productService.listVisibleForSaleProducts(
                    findOptions = ProductService.FindOptions(withPriceListing = true),
                    types = listOf(ProductType.B2C)
                )
            } returns listOf(otherProduct).success()
            coEvery {
                productService.getProduct(
                    id = mainProduct.id,
                    findOptions = ProductService.FindOptions(withPriceListing = true),
                )
            } returns mainProduct.success()

            coEvery {
                productBundleService.findActivesByIdsAndTypes(
                    (mainBundles + othersBundles).map { it.id },
                    listOf(ProductBundleType.HOSPITAL, ProductBundleType.MATERNITY, ProductBundleType.LABORATORY)
                )
            } returns (mainBundles + othersBundles).success()

            // Get selected product providers
            coEvery {
                providerService.searchByIdsAndNameWithRange(
                    ids = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.map { it.id },
                    range = IntRange(0, 7),
                    orderBy = OrderBy.FLAGSHIP
                )
            } returns mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) }.success()
            coEvery {
                providerService.searchByIdsAndNameWithRange(
                    ids = mainProviders.filter { it.type == ProviderType.LABORATORY }.map { it.id },
                    range = IntRange(0, 7),
                    orderBy = OrderBy.FLAGSHIP
                )
            } returns mainProviders.filter { it.type == ProviderType.LABORATORY }.success()

            val expectedResult = ProductComparison(
                simulation = simulation,
                productWithHospitalsAndLaboratories = listOf(
                    ProductWithHospitalsAndLaboratories(
                        product = mainProduct,
                        hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                        laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                        totalOfHospitals = mainBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                            .map { it.providerIds }.flatten().size,
                        totalOfLaboratories = mainBundles.filter { ProductBundleType.LABORATORY == it.type }
                            .map { it.providerIds }.flatten().size,
                    ),
                    ProductWithHospitalsAndLaboratories(
                        product = otherProduct,
                        hospitals = mainProviders.filter { ProviderType.hospitalTypes().contains(it.type) },
                        laboratories = mainProviders.filter { it.type == ProviderType.LABORATORY },
                        totalOfHospitals = othersBundles.filter { ProductBundleType.hospitalTypes().contains(it.type) }
                            .map { it.providerIds }.flatten().size,
                        totalOfLaboratories = othersBundles.filter { ProductBundleType.LABORATORY == it.type }
                            .map { it.providerIds }.flatten().size,
                    )
                ),
                selectedProductId = mainProduct.id
            )

            val result = service.getProductOptionsToComparison(simulation.id, mainProduct.id, 8).get()

            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

}
