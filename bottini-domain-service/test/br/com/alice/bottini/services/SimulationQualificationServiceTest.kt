package br.com.alice.bottini.services

import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test


class SimulationQualificationServiceTest {

    @Test
    fun `#qualify simulation is target`() = runBlocking {
        val agesAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.AGES,
            answer = """[{"age":"23"},{"age":"90"}]"""
        )

        val postalCodeAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.POSTAL_CODE,
            answer = "01303001"
        )
        val simulation = TestModelFactory.buildHealthProductSimulation(
            answers = listOf(agesAnswer, postalCodeAnswer))

        withFeatureFlag(FeatureNamespace.MEMBERSHIP, "target_postal_codes", listOf("013")) {
            val result = SimulationQualificationService.qualify(simulation)
            assertThat(result.isSuperTarget).isFalse
            assertThat(result.isTarget).isTrue
        }
    }

    @Test
    fun `#qualify simulation is super target`() = runBlocking {
        val agesAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.AGES,
            answer = """[{"age":"23"},{"age":"90"}]"""
        )

        val postalCodeAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.POSTAL_CODE,
            answer = "01303001"
        )
        val simulation = TestModelFactory.buildHealthProductSimulation(
            answers = listOf(agesAnswer, postalCodeAnswer))

        withFeatureFlag(FeatureNamespace.MEMBERSHIP, "super_target_postal_codes", listOf("013")) {
            val result = SimulationQualificationService.qualify(simulation)
            assertThat(result.isSuperTarget).isTrue
            assertThat(result.isTarget).isTrue
        }
    }

    @Test
    fun `#qualify simulation is not in target or super target because of ages`() {
        val agesAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.AGES,
            answer = """[{"age":"67"},{"age":"90"}]"""
        )

        val postalCodeAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.POSTAL_CODE,
            answer = "01303001"
        )
        val simulation = TestModelFactory.buildHealthProductSimulation(
            answers = listOf(agesAnswer, postalCodeAnswer))

        val result = SimulationQualificationService.qualify(simulation)
        assertThat(result.isSuperTarget).isFalse
        assertThat(result.isTarget).isFalse
    }

    @Test
    fun `#qualify simulation is not in target or super target because of postal code`() {
        val agesAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.AGES,
            answer = """[{"age":"18"},{"age":"25"}]"""
        )

        val postalCodeAnswer = TestModelFactory.buildHealthProductSimulationAnswer(
            questionType = HealthProductSimulationQuestionType.POSTAL_CODE,
            answer = "01303001"
        )
        val simulation = TestModelFactory.buildHealthProductSimulation(
            answers = listOf(agesAnswer, postalCodeAnswer))

        val result = SimulationQualificationService.qualify(simulation)
        assertThat(result.isSuperTarget).isFalse
        assertThat(result.isTarget).isFalse
    }
}
