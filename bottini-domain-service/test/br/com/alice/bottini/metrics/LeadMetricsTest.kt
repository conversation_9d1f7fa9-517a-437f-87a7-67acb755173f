package br.com.alice.bottini.metrics

import br.com.alice.bottini.metrics.LeadMetrics.incrementSimulationUpsertedLead
import br.com.alice.common.observability.metrics.Metric
import io.micrometer.core.instrument.Counter
import br.com.alice.common.redis.Result
import org.assertj.core.api.Assertions
import kotlin.test.BeforeTest
import kotlin.test.Test

class LeadMetricsTest {

    @BeforeTest
    fun setup() {
        Metric.meterRegistry.meters.map { meter -> Metric.meterRegistry.remove(meter.id) }
    }


    @Test
    fun `#incrementSimulationUpsertedLead counts metric with expected labels`() {

        incrementSimulationUpsertedLead(
            result = Result.SUCCESS
        )

        val counter = Metric.meterRegistry.meters.find {
            it.id.name == "alice.simulation_upserted_lead"
                    && it.id.getTag("result") == Result.SUCCESS.toLabel()
        } as Counter

        Assertions.assertThat(counter.count()).isEqualTo(1.0)
    }
}
