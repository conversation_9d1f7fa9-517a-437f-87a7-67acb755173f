package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.bottini.models.HealthProductSimulationQuestionOption
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion
import br.com.alice.data.layer.models.HealthProductSimulationType

object PeopleNumberPmeVersionAlgorithm : HealthProductSimulationQuestionAlgorithm {
    override suspend fun getQuestion(simulation: HealthProductSimulation?): HealthProductSimulationQuestion {
        val options = listOf(
            HealthProductSimulationQuestionOption(text = "Entre 1 e 5 pessoas", value = "1-5"),
            HealthProductSimulationQuestionOption(text = "Entre 6 e 9 pessoas", value = "6-9"),
            HealthProductSimulationQuestionOption(text = "Entre 10 e 29 pessoas", value = "10-29"),
            HealthProductSimulationQuestionOption(text = "Entre 30 e 79 pessoas", value = "30-79"),
            HealthProductSimulationQuestionOption(text = "Mais de 80 pessoas", value = "80+")
        )

        return HealthProductSimulationQuestion(
            type = HealthProductSimulationQuestionType.PEOPLE_NUMBER,
            options = options
        )
    }

    override suspend fun nextQuestion(
        simulation: HealthProductSimulation
    ): HealthProductSimulationQuestionType {
        val lastAnswer = simulation.answers.last()

        return if (lastAnswer.answer == "1-5") {
            HealthProductSimulationQuestionType.MICRO_COMPANY_AGES
        } else {
            if (simulation.simulatorVersion == HealthProductSimulationSimulatorVersion.V10) HealthProductSimulationQuestionType.COMPANY_CITY
            else HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER
        }
    }

    override fun defineSimulationType(
        answer: String,
        simulation: HealthProductSimulation?,
    ): HealthProductSimulationType? {
        val type = if (answer != "1-5") HealthProductSimulationType.COMPANY else null

        logger.info(
            "PeopleNumberAlgorithm#defineSimulationType",
            "type" to type,
            "simulation" to simulation,
        )
        return type
    }
}
