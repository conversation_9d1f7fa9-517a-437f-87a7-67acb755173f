package br.com.alice.bottini.services.questions

import br.com.alice.bottini.models.HealthProductSimulationQuestion
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.models.HealthProductSimulationType

interface HealthProductSimulationQuestionAlgorithm {

    suspend fun getQuestion(simulation: HealthProductSimulation? = null): HealthProductSimulationQuestion

    suspend fun nextQuestion(simulation: HealthProductSimulation): HealthProductSimulationQuestionType?

    fun defineSimulationType(
        answer: String,
        simulation: HealthProductSimulation? = null,
    ): HealthProductSimulationType?
}
