package br.com.alice.bottini.services.internal

import br.com.alice.bottini.client.HealthProductSimulationService
import br.com.alice.data.layer.models.AgesAnswer
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationAnswer
import br.com.alice.data.layer.models.HealthProductSimulationGroup
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType
import br.com.alice.data.layer.services.HealthProductSimulationGroupDataService

class SimulationGroupCreatorService(
    private val healthProductSimulationService: HealthProductSimulationService,
    private val dataService: HealthProductSimulationGroupDataService
) {

    suspend fun createAndGroupChildSimulations(mainSimulation: HealthProductSimulation): List<HealthProductSimulation> {
        val agesAnswer = mainSimulation.getAgesAnswer()
        if (agesAnswer != null && agesAnswer.size > 1) {
            val group = createGroupAndAssociateMainSimulation(mainSimulation)
            return createChildSimulations(mainSimulation, agesAnswer, group)
        }

        return emptyList()
    }

    private suspend fun createGroupAndAssociateMainSimulation(mainSimulation: HealthProductSimulation) : HealthProductSimulationGroup {
        val group = dataService.add(HealthProductSimulationGroup(mainSimulationId = mainSimulation.id)).get()
        healthProductSimulationService.update(mainSimulation.copy(groupId = group.id))
        return group
    }

    private suspend fun createChildSimulations(
        mainSimulation: HealthProductSimulation,
        agesAnswer: List<AgesAnswer>,
        group: HealthProductSimulationGroup): List<HealthProductSimulation> {

        val childSimulations = agesAnswer.map {age ->
            buildChildSimulation(mainSimulation, age, group)
        }

        return healthProductSimulationService.addList(childSimulations).get()
    }

    private fun buildChildSimulation(
        mainSimulation: HealthProductSimulation,
        agesAnswer: AgesAnswer,
        group: HealthProductSimulationGroup
    ) = HealthProductSimulation(
        answers = mainSimulation.answers + HealthProductSimulationAnswer(
            answer = agesAnswer.age,
            questionType = HealthProductSimulationQuestionType.AGE
        ),
        type = mainSimulation.type,
        simulatorVersion = mainSimulation.simulatorVersion,
        groupId = group.id,
        trackingInfo = mainSimulation.trackingInfo,
        leadId = mainSimulation.leadId,
    )
}
