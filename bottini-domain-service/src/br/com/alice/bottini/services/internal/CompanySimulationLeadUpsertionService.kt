package br.com.alice.bottini.services.internal

import br.com.alice.bottini.builders.LeadBuilder
import br.com.alice.bottini.client.LeadService
import br.com.alice.bottini.metrics.LeadMetrics
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.Lead
import br.com.alice.data.layer.models.TrackingInfo
import com.github.kittinunf.result.Result
import br.com.alice.common.redis.Result as MetricResult
import com.github.kittinunf.result.flatMap

class CompanySimulationLeadUpsertionService(
    private val leadService: LeadService,
) {

    suspend fun upsert(
        simulation: HealthProductSimulation,
        trackingInfo: TrackingInfo
    ): Result<Lead, Throwable> =
        leadService.findByEmail(simulation.email!!).flatMap { existingLead ->
            val lead = LeadBuilder.buildExistingFromSimulation(simulation, existingLead, trackingInfo)
            leadService.updateLead(lead)
        }.coFoldNotFound {
            val lead = LeadBuilder.buildNewFromSimulation(simulation, trackingInfo)
            leadService.create(lead)
        }.then {
            LeadMetrics.incrementSimulationUpsertedLead(MetricResult.SUCCESS)
        }.thenError {
            LeadMetrics.incrementSimulationUpsertedLead(MetricResult.FAILURE)
        }
}
