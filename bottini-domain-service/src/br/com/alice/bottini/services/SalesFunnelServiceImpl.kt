package br.com.alice.bottini.services

import br.com.alice.acquisition.client.CommercialRepresentativeService
import br.com.alice.acquisition.model.CommercialRepresentative
import br.com.alice.bottini.builders.CommercialRepresentativeBuilder
import br.com.alice.bottini.builders.ContactBuilderV2
import br.com.alice.bottini.builders.OngoingCompanyDealBuilder
import br.com.alice.bottini.client.SalesFunnelService
import br.com.alice.bottini.events.ContactCreatedOnHubspotEvent
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotContactConflictException
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotInvalidEmailException
import br.com.alice.communication.crm.sales.b2b.ContactResultV2
import br.com.alice.communication.crm.sales.b2b.ContactV2
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipeline
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipelineException
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.HealthProductSimulationType
import br.com.alice.data.layer.models.Lead
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.PersonSalesInfo
import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class SalesFunnelServiceImpl(
    private val salesFirmService: SalesFirmService,
    private val hubspotSalesFunnelPipeline: HubspotSalesFunnelPipeline,
    private val kafkaProducerService: KafkaProducerService,
    private val promoCodeService: PromoCodeService,
    private val personSalesService: PersonSalesService,
    private val commercialRepresentativeService: CommercialRepresentativeService,
): SalesFunnelService {
    override suspend fun createContactOnHubspot(
        simulation: HealthProductSimulation,
        lead: Lead,
        simulationUrl: String?,
    ): Result<Boolean, Throwable> {
        val salesFirmAlice = salesFirmService.getSalesFirmByName("Alice").get()

        val ongoingCompanyDeal = OngoingCompanyDealBuilder.buildOngoingCompanyDeal(
            lead,
            simulation,
            salesFirmAlice,
        )

        val commercialRepresentative = commercialRepresentativeService.getOrCreate(
            CommercialRepresentativeBuilder.build(lead),
            CommercialRepresentativeBuilder.buildMetadata(lead),
        ).get()

        val contactResponse = ContactResultV2(id = commercialRepresentative.additionalProperties[CommercialRepresentative.PIPELINE_EXTERNAL_ID_KEY]!!)

        val personSalesInfo = createPersonSalesInfo(lead, contactResponse)
        kafkaProducerService.produce(
            ContactCreatedOnHubspotEvent(
                lead,
                ongoingCompanyDeal,
                simulation,
                personSalesInfo,
            )
        )

        return true.success()
    }

    private suspend fun createContactAndContinueToDealCreation(
        ongoingCompanyDeal: OngoingCompanyDeal,
        lead: Lead,
        simulation: HealthProductSimulation,
        salesFirmAlice: SalesFirm,
        simulationUrl: String?
    ): ContactResultV2 {
        logger.info(
            "Creating contact on hubspot",
            "ongoing_company_deal" to ongoingCompanyDeal,
            "lead_id" to lead.id,
            "simulation_id" to simulation.id,
            "sales_firm_id" to salesFirmAlice.id,
        )

        val contact = buildContact(lead, simulation)

        return try {
            createContactOrUpdateOnHubspot(contact, ongoingCompanyDeal, lead, simulation)
        } catch (e: HubspotInvalidEmailException) {
            logger.error(
                "Cannot create contact on Hubspot - invalid email",
                "lead_id" to lead.id,
                "contact_number" to lead.phoneNumber,
                "nick_name" to lead.nickName,
                "hubspot_message_error" to e.message,
            )
            throw e
        }
    }

    private suspend fun createPersonSalesInfo(
        lead: Lead,
        contactResponse: ContactResultV2,
    ): PersonSalesInfo {
        val existingPersonSalesInfo = personSalesService.findByLeadId(lead.id)

        return if (existingPersonSalesInfo != null) {
            personSalesService.update(
                existingPersonSalesInfo.copy(hubspotContactId = contactResponse.id)
            ).get()
        } else {
            val personSales = PersonSalesInfo(
                leadId = lead.id,
                hubspotContactId = contactResponse.id
            )
            personSalesService.add(personSales).get()
        }
    }

    private suspend fun buildContact(
        lead: Lead,
        simulation: HealthProductSimulation,
    ): ContactV2 =
        if (simulation.type == HealthProductSimulationType.COMPANY){
            ContactBuilderV2.buildPMEContact(lead)
        } else {
            val promoCode = if (lead.promoCodeId != null) promoCodeService.get(lead.promoCodeId!!).get() else null
            ContactBuilderV2.buildContact(lead, promoCode, simulation)
        }

    private suspend fun createContactOrUpdateOnHubspot(
        contact: ContactV2,
        ongoingCompanyDeal: OngoingCompanyDeal,
        lead: Lead,
        simulation: HealthProductSimulation,
    ) =
        try {
            hubspotSalesFunnelPipeline.createNewContact(contact)
        } catch (e: HubspotContactConflictException) {
            logger.info(
                "SalesFunnelServiceImpl#createContactOnHubspot: contact already exists on hubspot, so updating it",
                "ongoing_company_deal" to ongoingCompanyDeal.id,
                "lead_id" to lead.id,
                "simulation_id" to simulation.id,
                "contact_id_from_hubspot" to e.contactId,
            )
            hubspotSalesFunnelPipeline.updateContact(e.contactId, contact)
        } catch (ex: Exception) {
            logger.error(
                "Error while creating contact on hubspot",
                "ongoing_company_deal" to ongoingCompanyDeal.id,
                "lead_id" to lead.id,
                "simulation_id" to simulation.id,
                "error" to ex.message,
            )
            throw HubspotSalesFunnelPipelineException(
                ex.message ?: "Error while creating contact on hubspot",
            )
        }
}
