package br.com.alice.bottini.builders

import br.com.alice.bottini.models.FinishedSimulationPerson
import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.models.Person

object FinishedSimulationPersonBuilder {

    fun build(simulation: HealthProductSimulation, person: Person?) = FinishedSimulationPerson(
        personId = person?.id,
        simulationId = simulation.id,
        age = person?.possibleAge?.toString() ?: simulation.ages!![0].toString(),
        alreadyConverted = person != null,
        firstName = person?.getSocialOrFirstName,
        lastName = person?.lastName,
        nationalId = person?.obfuscatedNationalId,
        phoneNumber = person?.obfuscatedPhoneNumber,
        email = person?.obfuscatedEmail,
    )
}
