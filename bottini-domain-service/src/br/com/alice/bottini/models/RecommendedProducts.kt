package br.com.alice.bottini.models

import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.Provider
import br.com.alice.product.client.ProductBundleWithProviders

data class RecommendedProducts(
    val selectedProduct: RecommendedProduct,
    val anchorsProduct: List<RecommendedProduct>
)

data class RecommendedProduct(
    val product: Product,
    val productBundles: List<ProductBundle> = emptyList(),
    val hospitals: List<Provider> = emptyList(),
    val laboratories: List<Provider> = emptyList(),
    val hospitalsCount: Int = 0,
    val laboratoriesCount: Int = 0,
) {
    val labsBundle get() = productBundles.filter { it.type == ProductBundleType.LABORATORY }
    val hospitalsBundles get() = productBundles.filter { it.type in ProductBundleType.hospitalTypes() }

}
