package br.com.alice.bottini.route

import br.com.alice.bottini.consumers.AcquisitionAnalyticsConsumer
import br.com.alice.bottini.consumers.CompanyCreatedOnHubspotConsumer
import br.com.alice.bottini.consumers.CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedConsumer
import br.com.alice.bottini.consumers.ContactCreatedOnHubspotConsumer
import br.com.alice.bottini.consumers.DealProgressConsumer
import br.com.alice.bottini.consumers.OngoingCompanyDealCreatedOnHubspotConsumer
import br.com.alice.bottini.consumers.ProductUpdatedConsumer
import br.com.alice.bottini.consumers.SalesPipelineConsumer
import br.com.alice.bottini.consumers.SimulationFinishedConsumer
import br.com.alice.bottini.events.CompanyCreatedOnHubspotEvent
import br.com.alice.bottini.events.ContactCreatedOnHubspotEvent
import br.com.alice.bottini.events.LeadCreatedEvent
import br.com.alice.bottini.events.OngoingCompanyDealCreatedOnHubspotEvent
import br.com.alice.bottini.events.OpportunityCreatedEvent
import br.com.alice.bottini.events.PMELeadUpsertedEvent
import br.com.alice.bottini.events.SimulationFinishedEvent
import br.com.alice.business.events.CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.membership.model.events.SignedContractEvent
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import br.com.alice.moneyin.event.InvoiceSentEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestDeclinedEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestInProgressEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestSubmittedEvent
import br.com.alice.person.br.com.alice.person.model.events.FirstAccessEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.PersonCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.product.model.events.ProductUpdatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent

fun ConsumerJob.Configuration.kafkaRoutes() {
    val salesPipelineConsumer by inject<SalesPipelineConsumer>()
    consume("upsert-deal", OpportunityCreatedEvent.name, salesPipelineConsumer::upsertDeal)
    consume("upsert-entities-pme-lead", PMELeadUpsertedEvent.name, salesPipelineConsumer::upsertEntitiesForPMELead)
    val dealProgressConsumer by inject<DealProgressConsumer>()
    consume("first-access", FirstAccessEvent.name, dealProgressConsumer::firstAccess)
    consume(
        "move-deal-shopping-finished-stage",
        ShoppingFinishedEvent.name,
        dealProgressConsumer::moveDealStageOnShoppingFinished
    )
    consume(
        "move-deal-contract-started-stage",
        ContractPhaseStartedEvent.name,
        dealProgressConsumer::moveDealStageOnContractStarted
    )
    consume(
        "move-deal-portability-submitted",
        InsurancePortabilityRequestSubmittedEvent.name,
        dealProgressConsumer::moveDealOnPortabilitySubmitted
    )
    consume(
        "move-deal-portability-declined",
        InsurancePortabilityRequestDeclinedEvent.name,
        dealProgressConsumer::moveDealOnPortabilityDecline
    )
    consume(
        "move-deal-portability-in-progress",
        InsurancePortabilityRequestInProgressEvent.name,
        dealProgressConsumer::moveDealOnPortabilityInProgress
    )
    consume(
        "move-deal-registration-finished",
        PersonRegistrationFinishedEvent.name,
        dealProgressConsumer::moveDealStageOnRegistrationFinished
    )
    consume(
        "move-deal-appointment-scheduled",
        AppointmentScheduleCreatedEvent.name,
        dealProgressConsumer::moveDealOnAppointmentCreated
    )
    consume(
        "move-deal-appointment-cancellation",
        AppointmentScheduleCancelledEvent.name,
        dealProgressConsumer::moveDealOnAppointmentCancellation
    )
    consume(
        "move-deal-appointment-no-show",
        AppointmentScheduleNoShowEvent.name,
        dealProgressConsumer::moveDealOnAppointmentNoShow
    )
    consume("move-deal-invoice-sent", InvoiceSentEvent.name, dealProgressConsumer::moveDealOnInvoiceSent)
    consume("move-deal-member-activated", MemberActivatedEvent.name, dealProgressConsumer::moveDealOnMemberActivated)
    consume(
        "add-cancellation-data-to-deal",
        MemberCancelledEvent.name,
        dealProgressConsumer::addCancellationDataToDeal
    )
    val acquisitionAnalyticsConsumer by inject<AcquisitionAnalyticsConsumer>()
    consume("lead-created", LeadCreatedEvent.name, acquisitionAnalyticsConsumer::leadCreated)
    consume("simulation-finished", SimulationFinishedEvent.name, acquisitionAnalyticsConsumer::simulationFinished)
    consume("member-activated", MemberActivatedEvent.name, acquisitionAnalyticsConsumer::memberActivated)
    consume("first-app-access", FirstAccessEvent.name, acquisitionAnalyticsConsumer::firstAccess)
    consume("shopping-finished-access", ShoppingFinishedEvent.name, acquisitionAnalyticsConsumer::shoppingFinished)
    consume(
        "registration-finished-access",
        PersonRegistrationFinishedEvent.name,
        acquisitionAnalyticsConsumer::registrationFinished
    )
    consume("invoice-sent", InvoicePaymentCreatedEvent.name, acquisitionAnalyticsConsumer::invoiceSent)
    consume(
        "portability-requested",
        InsurancePortabilityRequestInProgressEvent.name,
        acquisitionAnalyticsConsumer::portabilityRequested
    )
    consume(
        "contract-was-signed",
        SignedContractEvent.name,
        acquisitionAnalyticsConsumer::contractSigned
    )

    val simulationFinishedConsumer by inject<SimulationFinishedConsumer>()
    consume("create-ongoing-company-deal-simulation-finished", SimulationFinishedEvent.name, simulationFinishedConsumer::createOngoingCompanyDeal)

    val ongoingCompanyDealCreatedConsumer by inject<OngoingCompanyDealCreatedOnHubspotConsumer>()
    consume("upsert-ongoing-company-deal-and-update-lead", OngoingCompanyDealCreatedOnHubspotEvent.name, ongoingCompanyDealCreatedConsumer::createOngoingCompanyDealAndUpdateLead)

    val contactCreatedOnHubspotConsumer by inject<ContactCreatedOnHubspotConsumer>()
    consume("create-company-on-hubspot", ContactCreatedOnHubspotEvent.name, contactCreatedOnHubspotConsumer::createCompanyOnHubspot)

    val companyCreatedOnHubspotConsumer by inject<CompanyCreatedOnHubspotConsumer>()
    consume(
        handlerName = "create-deal-on-hubspot",
        topicName = CompanyCreatedOnHubspotEvent.name,
        handler = companyCreatedOnHubspotConsumer::createDealOnHubspot,
        additionalProperties = AdditionalProperties(retries = 0),
    )

    val companyWasUpdatedOnHubspotAfterCognitoIsPopulatedConsumer by inject<CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedConsumer>()
    consume("company-was-updated-on-hubspot-after-cognito-is-populated-update-ongoing-company-deal", CompanyWasUpdatedOnHubspotAfterCognitoIsPopulatedEvent.name, companyWasUpdatedOnHubspotAfterCognitoIsPopulatedConsumer::updateOngoingCompanyDeal)

    val productUpdatedConsumer by inject<ProductUpdatedConsumer>()
    consume("product-updated-update-vic-product-option-product-name", ProductUpdatedEvent.name, productUpdatedConsumer::updateVicProductOptionProductName)
}
