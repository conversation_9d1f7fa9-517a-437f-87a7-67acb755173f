package br.com.alice.bottini.analytics

import br.com.alice.common.logging.logger
import com.segment.analytics.kotlin.core.Analytics
import com.segment.analytics.kotlin.core.BaseEvent
import com.segment.analytics.kotlin.core.TrackEvent
import com.segment.analytics.kotlin.core.platform.EventPlugin
import com.segment.analytics.kotlin.core.platform.Plugin
import com.segment.analytics.kotlin.core.utilities.getString
import com.segment.analytics.kotlin.core.utilities.updateJsonObject

class EventAttributesFilterPlugin : EventPlugin {
    override val type = Plugin.Type.Enrichment
    override lateinit var analytics: Analytics

    companion object {
        const val USER_ID_KEY = "user_id"
        const val ANONYMOUS_ID_KEY = "anonymous_id"
        val EVENT_ATTRIBUTE_KEYS = listOf(USER_ID_KEY, ANONYMOUS_ID_KEY)
    }

    override fun track(payload: TrackEvent): BaseEvent? {
        val event = super.track(payload)

        if (event is TrackEvent) {
            EVENT_ATTRIBUTE_KEYS.forEach { key ->
                event.apply {

                    properties.getString(key)?.let { value ->
                        when (key) {
                            USER_ID_KEY -> {
                                userId = value
                            }

                            ANONYMOUS_ID_KEY -> {
                                anonymousId = value
                            }
                        }
                    }

                    properties = updateJsonObject(properties) {
                        it.remove(key)
                    }
                }
            }
        }

        return payload
    }
}

