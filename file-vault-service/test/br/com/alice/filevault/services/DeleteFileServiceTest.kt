package br.com.alice.filevault.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.services.FileVaultDataService
import br.com.alice.data.layer.services.FileVaultPiiDataService
import br.com.alice.data.layer.services.GenericFileVaultDataService
import br.com.alice.filevault.models.GenericVaultDelete
import br.com.alice.filevault.models.PersonVaultDelete
import br.com.alice.filevault.storage.FileStorage
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertFailsWith

class DeleteFileServiceTest {

    private val fileStorage: FileStorage = mockk()
    private val fileVaultDataService: FileVaultDataService = mockk()
    private val fileVaultPiiDataService: FileVaultPiiDataService = mockk()
    private val genericFileVaultDataService: GenericFileVaultDataService = mockk()
    private val deleteFileService = DeleteFileService(fileStorage, fileVaultDataService, fileVaultPiiDataService, genericFileVaultDataService)

    private val s3Url = "s3"
    private val personVaultDelete = PersonVaultDelete(
        domain = "domain",
        namespace = "namespace",
        fileName = "743a5962-152b-426a-a711-b35791ea5490.pdf",
        personId = PersonId()
    )

    private val genericVaultDelete = GenericVaultDelete(
        domain = "domain",
        namespace = "namespace",
        fileName = "743a5962-152b-426a-a711-b35791ea5490.pdf",
    )

    @Test
    fun `#process should delete file from file storage and database`() = runBlocking {
        val fileVaultId = personVaultDelete.fileName.substringBefore(".").toUUID()

        coEvery {
            fileStorage.delete(
                match {
                    it.bucketName == "file-vault.test" &&
                            it.filePath.contains(".pdf") &&
                            it.filePath.contains("domain")
                }
            )
        } returns s3Url
        coEvery { fileVaultDataService.delete(match { it.id == fileVaultId }) } returns true

        val result = deleteFileService.process(personVaultDelete)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#process should delete file from PII file storage and database`() = runBlocking {
        val fileVaultId = personVaultDelete.fileName.substringBefore(".").toUUID()
        val piiVaultDelete = personVaultDelete.copy(shouldGetAsPii = true)
        coEvery {
            fileStorage.delete(
                match {
                    it.bucketName == "file-vault.test" &&
                            it.filePath.contains(".pdf") &&
                            it.filePath.contains("domain")
                }
            )
        } returns s3Url
        coEvery { fileVaultPiiDataService.delete(match { it.id == fileVaultId }) } returns true

        val result = deleteFileService.process(piiVaultDelete)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#process should not delete file from file storage when error while deleting file from database`() =
        runBlocking {
            coEvery { fileVaultDataService.delete(any()) } returns Exception()

            val result = deleteFileService.process(personVaultDelete)
            assertFailsWith<Exception> { result.get() }
            coVerify { fileStorage wasNot called }
        }

    @Test
    fun `#processGeneric should delete file from file storage and database`() = runBlocking {
        val fileVaultId = genericVaultDelete.fileName.substringBefore(".").toUUID()

        coEvery {
            fileStorage.delete(
                match {
                    it.bucketName == "file-vault.test" &&
                            it.filePath.contains(".pdf") &&
                            it.filePath.contains("domain")
                }
            )
        } returns s3Url
        coEvery { genericFileVaultDataService.delete(match { it.id == fileVaultId }) } returns true

        val result = deleteFileService.processGeneric(genericVaultDelete)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#processGeneric should not delete file from file storage when error while deleting file from database`() =
        runBlocking {
            coEvery { genericFileVaultDataService.delete(any()) } returns Exception()

            val result = deleteFileService.processGeneric(genericVaultDelete)
            assertFailsWith<Exception> { result.get() }
            coVerify { fileStorage wasNot called }
        }

}
