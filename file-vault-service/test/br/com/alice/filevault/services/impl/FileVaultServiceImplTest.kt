package br.com.alice.filevault.services.impl

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.services.FileVaultDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class FileVaultServiceImplTest {

    private val fileVaultDataService: FileVaultDataService = mockk()
    private val fileVaultService = FileVaultServiceImpl(
        fileVaultDataService
    )

    private val person = TestModelFactory.buildPerson()

    private val channelReferencedLink = FileVault.ReferencedLink(
        id = "007IX5gg4qzGcrLiX129",
        model = FileVault.ReferenceLinkModel.CHANNEL
    )

    private val files: List<FileVault> = listOf(
        TestModelFactory.buildFileVault(
            personId = person.id,
            referencedLinks = listOf(channelReferencedLink)
        )
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        fileVaultDataService
    )

    @Test
    fun `#getByPersonAndReferencedLink should return list of files`() = runBlocking {
        coEvery{
            fileVaultDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id) and this.referencedLinks.contains(channelReferencedLink)
                    }
                }
            )
        } returns files.success()

        val result = fileVaultService.getByPersonAndReferencedLink(
            person.id,
            channelReferencedLink
        )

        assertThat(result).isSuccessWithData(files)

        coVerifyOnce { fileVaultDataService.find(any()) }
    }

    @Test
    fun `#getByPersonAndReferencedLink should return throws error`() = runBlocking {
        coEvery{
            fileVaultDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id) and this.referencedLinks.contains(channelReferencedLink)
                    }
                }
            )
        } returns NotFoundException("not_found").failure()

        val result = fileVaultService.getByPersonAndReferencedLink(
            person.id,
            channelReferencedLink
        )

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { fileVaultDataService.find(any()) }
    }

    @Test
    fun `#updateFromReference should update file with reference`() = runBlocking {
        val oldReference = FileVault.ReferencedLink(
            id = "024C3N44NIH15SGTHC1P",
            model = FileVault.ReferenceLinkModel.CHANNEL
        )
        val fileWithoutGivedReference = TestModelFactory.buildFileVault(
            personId = person.id,
            referencedLinks = listOf(oldReference)
        )

        coEvery{
            fileVaultDataService.get(fileWithoutGivedReference.id)
        } returns fileWithoutGivedReference.success()

        val fileToUpdate = fileWithoutGivedReference.copy(
            referencedLinks = listOf(oldReference, channelReferencedLink)
        )

        coEvery{
            fileVaultDataService.update(fileToUpdate)
        } returns fileToUpdate.success()

        val result = fileVaultService.updateFromReference(
            fileWithoutGivedReference.id,
            channelReferencedLink
        )

        assertThat(result).isSuccessWithData(fileToUpdate)

        coVerifyOnce { fileVaultDataService.get(any()) }
        coVerifyOnce { fileVaultDataService.update(any()) }
    }

    @Test
    fun `#updateFromReference should update file adding the first reference`() = runBlocking {
        val fileWithoutReference = TestModelFactory.buildFileVault(
            personId = person.id
        )

        coEvery{
            fileVaultDataService.get(fileWithoutReference.id)
        } returns fileWithoutReference.success()

        val fileToUpdate = fileWithoutReference.copy(
            referencedLinks = listOf(channelReferencedLink)
        )

        coEvery{
            fileVaultDataService.update(fileToUpdate)
        } returns fileToUpdate.success()

        val result = fileVaultService.updateFromReference(
            fileWithoutReference.id,
            channelReferencedLink
        )

        assertThat(result).isSuccessWithData(fileToUpdate)

        coVerifyOnce { fileVaultDataService.get(any()) }
        coVerifyOnce { fileVaultDataService.update(any()) }
    }

    @Test
    fun `#updateFromReference should avoid call update when file already has the same reference`() = runBlocking {
        val fileWithReference = TestModelFactory.buildFileVault(
            personId = person.id,
            referencedLinks = listOf(channelReferencedLink)
        )

        coEvery{
            fileVaultDataService.get(fileWithReference.id)
        } returns fileWithReference.success()

        val result = fileVaultService.updateFromReference(
            fileWithReference.id,
            channelReferencedLink
        )

        assertThat(result).isSuccessWithData(fileWithReference)

        coVerifyOnce { fileVaultDataService.get(any()) }
        coVerifyNone { fileVaultDataService.update(any()) }
    }

    @Test
    fun `#updateFromReference should throw error when file does not exist`() = runBlocking {
        val fileWithoutReference = TestModelFactory.buildFileVault(
            personId = person.id
        )

        coEvery{
            fileVaultDataService.get(fileWithoutReference.id)
        } returns NotFoundException("not_found").failure()

        val result = fileVaultService.updateFromReference(
            fileWithoutReference.id,
            channelReferencedLink
        )

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { fileVaultDataService.get(any()) }
        coVerifyNone { fileVaultDataService.update(any()) }
    }

    @Test
    fun `#updateFromReference should throw error when update throw error`() = runBlocking {
        val fileWithoutReference = TestModelFactory.buildFileVault(
            personId = person.id
        )

        coEvery{
            fileVaultDataService.get(fileWithoutReference.id)
        } returns fileWithoutReference.success()

        val fileToUpdate = fileWithoutReference.copy(
            referencedLinks = listOf(channelReferencedLink)
        )

        coEvery{
            fileVaultDataService.update(fileToUpdate)
        } returns Exception("ex").failure()

        val result = fileVaultService.updateFromReference(
            fileWithoutReference.id,
            channelReferencedLink
        )

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { fileVaultDataService.get(any()) }
        coVerifyOnce { fileVaultDataService.update(any()) }
    }
}
