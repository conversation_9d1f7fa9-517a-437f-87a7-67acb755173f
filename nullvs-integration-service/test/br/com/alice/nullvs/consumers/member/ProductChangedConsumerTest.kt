package br.com.alice.nullvs.consumers.member

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Person
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.person.model.events.ProductChangedEvent
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProductChangedConsumerTest : ConsumerTest() {
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService = mockk()

    private val consumer =
        ProductChangedConsumer(totvsMemberIntegrationService, LocalProducer)

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    private fun buildMetadata(event: ProductChangedEvent, member: Member, sequence: Int): Meta {
        val eventId = RangeUUID.generateFromUUID(event.messageId, (sequence).toByte())

        return Meta(
            eventId = eventId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = member.id,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = ProductChangedEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )
    }

    private fun buildNullvsMemberBatchRequest(
        person: Person,
        member: Member,
        meta: Meta,
        actionType: NullvsActionType
    ) = NullvsMemberBatchRequest(
        meta,
        actionType,
        TotvsMemberRequest(
            company = TotvsGroupCompany.ALICE_INDIVIDUAL,
            client = "member",
            createdAt = LocalDateTime.now(),
            ANSProductId = "ans-product-id",
            idPayload = 1,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = TotvsUser.HOLDER,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = person.createdAt,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = "ans-id",
                    canceledReason = null,
                    canceledAt = null,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "XPTO",
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 730L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 300L
                        )
                    ),
                ),
            ),
        ),
    )

    @Nested
    inner class ChangeProductAtTotvs {
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1989, 9, 14, 16, 20),
        )

        private val previousMember = TestModelFactory.buildMember(personId = person.id)
        private val newMember = TestModelFactory.buildMember(personId = person.id, status = MemberStatus.ACTIVE)
        private val event = ProductChangedEvent(previousMember, newMember)

        private val metadataPreviousMember = buildMetadata(event, previousMember, 1)
        private val metadataNewMember = buildMetadata(event, newMember, 2)

        private val previousMemberBatchRequest = buildNullvsMemberBatchRequest(
            person, previousMember, metadataPreviousMember, NullvsActionType.CANCEL
        )

        private val newMemberBatchRequest = buildNullvsMemberBatchRequest(
            person, previousMember, metadataNewMember, NullvsActionType.CREATE
        )

        @Test
        fun `#should produce two NullvsMemberBatchRequestEvent to cancel and then create beneficiary with new product - in this order`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metadataPreviousMember,
                        previousMember
                    )
                } returns previousMemberBatchRequest
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataNewMember,
                        newMember
                    )
                } returns newMemberBatchRequest

                val result = consumer.changeProductAtTotvs(event)

                ResultAssert.assertThat(result).isSuccess()

                val cancelEventPayload = LocalProducer.events.get(0).payload
                val createEventPayload = LocalProducer.events.get(1).payload

                Assertions.assertThat((cancelEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
                    .isEqualTo(NullvsActionType.CANCEL)
                Assertions.assertThat((createEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
                    .isEqualTo(NullvsActionType.CREATE)

                Assertions.assertThat(LocalProducer.events).hasSize(2)
                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metadataPreviousMember,
                        previousMember
                    )
                }
                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(metadataNewMember, newMember) }
            }

        @Test
        fun `#should return an AutoRetryableException in case of TotvsMemberNotFoundByInternalIdException when creating beneficiary`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metadataPreviousMember,
                        previousMember
                    )
                } returns previousMemberBatchRequest
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataNewMember,
                        newMember
                    )
                } returns TotvsMemberNotFoundByInternalIdException(person.id.toUUID()).failure()

                val result = consumer.changeProductAtTotvs(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)
                assert(result.component2()!!.cause is TotvsMemberNotFoundByInternalIdException)

                coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(any(), any()) }
                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(any(), any()) }
            }

        @Test
        fun `should return success when NullANSFieldException happens to avoid dlq`() = runBlocking<Unit> {
            coEvery {
                totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                    metadataPreviousMember,
                    previousMember
                )
            } returns NullANSFieldException("NullANSFieldException").failure()
            coEvery {
                totvsMemberIntegrationService.createBeneficiaryTotvs(
                    metadataNewMember,
                    newMember
                )
            } returns NullANSFieldException("NullANSFieldException").failure()

            val result = consumer.changeProductAtTotvs(event)

            ResultAssert.assertThat(result).isSuccess()
        }

        @Test
        fun `should return success when DependsOnModelException happens to avoid dlq`() = runBlocking<Unit> {
            coEvery {
                totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                    metadataPreviousMember,
                    previousMember
                )
            } returns previousMemberBatchRequest

            coEvery {
                totvsMemberIntegrationService.createBeneficiaryTotvs(
                    metadataNewMember,
                    newMember
                )
            } returns DependsOnModelException(newMember.id, InternalModelType.MEMBER).failure()


            val result = consumer.changeProductAtTotvs(event)

            ResultAssert.assertThat(result).isSuccess()

            val cancelEventPayload = LocalProducer.events.get(0).payload
            Assertions.assertThat((cancelEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
                .isEqualTo(NullvsActionType.CANCEL)

        }
    }
}
