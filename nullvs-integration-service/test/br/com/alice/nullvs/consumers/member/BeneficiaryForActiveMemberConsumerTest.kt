package br.com.alice.nullvs.consumers.member

import br.com.alice.business.events.BeneficiaryCreatedForActiveMemberEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.exceptions.CityCodeNotFoundException
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.NonActiveMember
import br.com.alice.nullvs.exceptions.NotFullMothersNameException
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.exceptions.NullPersonalFieldException
import br.com.alice.nullvs.exceptions.PersonAddressFieldException
import br.com.alice.nullvs.exceptions.SkipException
import br.com.alice.nullvs.exceptions.TestPersonalRegisterException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsParentMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BeneficiaryForActiveMemberConsumerTest : ConsumerTest() {
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService = mockk()
    private val memberService: MemberService = mockk()

    private val consumer =
        BeneficiaryForActiveMemberConsumer(totvsMemberIntegrationService, memberService, LocalProducer)

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            NullANSFieldException(""),
            NullPersonalFieldException(""),
            NotFullMothersNameException(""),
            TestPersonalRegisterException(""),
            PersonAddressFieldException(""),
            CityCodeNotFoundException(""),
            SkipException(""),
            NonActiveMember(""),
            TotvsParentMemberNotFoundByInternalIdException(""),
            DependsOnModelException(UUID.randomUUID(), InternalModelType.MEMBER)
        )
    }

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    private fun buildMetadata(
        event: BeneficiaryCreatedForActiveMemberEvent,
        beneficiary: Beneficiary
    ): Meta {
        val eventId = RangeUUID.generateFromUUID(event.messageId)

        return Meta(
            eventId = eventId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = beneficiary.memberId,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = BeneficiaryCreatedForActiveMemberEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )
    }

    private fun buildNullvsMemberBatchRequest(
        person: Person,
        member: Member,
        meta: Meta
    ) = NullvsMemberBatchRequest(
        meta,
        NullvsActionType.CREATE,
        TotvsMemberRequest(
            company = TotvsGroupCompany.ALICE_INDIVIDUAL,
            client = "member",
            createdAt = LocalDateTime.now(),
            ANSProductId = "ans-product-id",
            idPayload = 1,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = TotvsUser.HOLDER,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = person.createdAt,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = "ans-id",
                    canceledReason = null,
                    canceledAt = null,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "XPTO",
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 730L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 300L
                        )
                    ),
                ),
            ),
        ),
    )

    @Nested
    inner class BeneficiaryForActiveMember {
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1989, 9, 14, 16, 20),
        )

        private val member = TestModelFactory.buildMember(personId = person.id)
        private val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
        private val event = BeneficiaryCreatedForActiveMemberEvent(beneficiary)

        private val metadataMember = buildMetadata(event, beneficiary)

        private val newMemberBatchRequest = buildNullvsMemberBatchRequest(
            person, member, metadataMember
        )

        @Test
        fun `#should produce two NullvsMemberBatchRequestEvent to cancel and then create beneficiary with new beneficiary relationship is assigned - in this order`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataMember,
                        member,
                    )
                } returns newMemberBatchRequest

                coEvery { memberService.get(member.id) } returns member

                val result = consumer.beneficiaryCreatedForActiveMember(event)

                ResultAssert.assertThat(result).isSuccess()

                val createEventPayload = LocalProducer.events[0].payload

                Assertions.assertThat((createEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
                    .isEqualTo(NullvsActionType.CREATE)

                Assertions.assertThat(LocalProducer.events).hasSize(1)
                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue

                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(metadataMember, member) }
            }

        @Test
        fun `#should return an AutoRetryableException in case of TotvsMemberNotFoundByInternalIdException when creating beneficiary`() =
            runBlocking {
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataMember,
                        member,
                    )
                } returns TotvsMemberNotFoundByInternalIdException(member.id).failure()

                coEvery { memberService.get(member.id) } returns member

                val result = consumer.beneficiaryCreatedForActiveMember(event)

                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)
                assert(result.component2()!!.cause is TotvsMemberNotFoundByInternalIdException)

                Assertions.assertThat(LocalProducer.events).hasSize(0)
                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(metadataMember, member) }
            }

        @ParameterizedTest(name = "should return success when exception {0} happens to avoid dlq")
        @MethodSource("br.com.alice.nullvs.consumers.member.BeneficiaryForActiveMemberConsumerTest#exceptions")
        fun `should return success when some exceptions happens to avoid dlq`(exception: BadRequestException) =
            runBlocking<Unit> {
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataMember,
                        member,
                    )
                } returns exception.failure()

                coEvery { memberService.get(member.id) } returns member

                val result = consumer.beneficiaryCreatedForActiveMember(event)

                ResultAssert.assertThat(result).isSuccess()
            }
    }
}
