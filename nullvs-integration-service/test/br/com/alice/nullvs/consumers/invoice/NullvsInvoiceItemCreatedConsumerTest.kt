package br.com.alice.nullvs.consumers.invoice

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.nullvs.clients.TotvsInvoiceItemClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsInvoiceItemCreatedEvent
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsInvoiceItemResponse
import br.com.alice.nullvs.models.payment.Level
import br.com.alice.nullvs.models.payment.NullvsInvoiceItemCreated
import br.com.alice.nullvs.models.payment.ValueType
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.success
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import junit.framework.TestCase.assertTrue
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class NullvsInvoiceItemCreatedConsumerTest : ConsumerTest() {
    private val totvsInvoiceItemClient: TotvsInvoiceItemClient = mockk()
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()

    val consumer = NullvsInvoiceItemCreatedConsumer(totvsInvoiceItemClient, nullvsIntegrationLogService)
    private val idAdditionalBilling = RangeUUID.generate()
    val event = NullvsInvoiceItemCreatedEvent(
        nullvsInvoiceItemCreated = NullvsInvoiceItemCreated(
            meta = Meta(
                eventId = RangeUUID.generate(),
                eventName = "event01",
                internalId = RangeUUID.generate(),
                internalModelName = InternalModelType.INVOICE_ITEM,
                integrationEventName = "integration01",
                externalId = null,
                externalModelName = ExternalModelType.INVOICE_ITEM,
                integratedAt = LocalDateTime.now().minusDays(1),
                originalTopic = "original01",
            ),
            action = NullvsActionType.CREATE,
            invoiceItemCreated = NullvsInvoiceItemCreated.InvoiceItemCreated(
                level = Level.SUBCONTRACT,
                cpfCnpj = "cpfCnpj",
                month = 1,
                year = 2024,
                code = "code",
                valueType = ValueType.PERCENTAGE,
                value = 10.0,
                numberOfInstallments = "2",
                observation = "observation",
                idAddBilling = idAdditionalBilling,
            ),
        ),
    )

    private val totvsResponse = TotvsInvoiceItemResponse(
        batch = "1000122740",
        idSoc = "99999917",
        action = "I",
        httpStatus = "created",
    )

    @Test
    fun `processInvoiceItemCreated should return success`() = runBlocking {
        coEvery { totvsInvoiceItemClient.addBilling(any()) } returns totvsResponse.success()

        val result = consumer.processInvoiceItemCreated(event)

        assertTrue(result.isSuccess())
    }

    @Test
    fun `processInvoiceItemCreated should return failure`() = runBlocking {
        coEvery { totvsInvoiceItemClient.addBilling(any()) } returns Exception("generic_error").failure()
        coEvery { nullvsIntegrationLogService.add(any()) } returns mockk()

        val result = consumer.processInvoiceItemCreated(event)

        assertTrue(result.isFailure())
    }
}
