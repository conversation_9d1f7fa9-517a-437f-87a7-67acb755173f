package br.com.alice.nullvs.consumers.invoice

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsIntegrationLog
import br.com.alice.nullvs.events.NullvsInvoiceBatchResponseEvent
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import br.com.alice.nullvs.models.payment.TotvsInvoiceResponse
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class NullvsInvoiceResponseConsumerTest : ConsumerTest() {
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()

    private val consumer =
        NullvsInvoiceResponseConsumer(
            nullvsIntegrationLogService,
        )

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.MEMBER_INVOICE,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.INVOICE,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val totvsInvoiceResponse = TotvsInvoiceResponse(
        httpStatus = "200",
        batch = "batch01",
        idSoc = "idSoc01",
        action = "action01",
    )

    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(NullvsInvoiceConverter)
    }

    @Nested
    inner class CreateNullvsIntegrationLogForSyncedInvoice {
        @Test
        fun `#should add NullvsIntegrationLog for synced MemberInvoice as expected`() = runBlocking {
            val nullvsInvoiceBatchResponse = NullvsInvoiceBatchResponse(meta, totvsInvoiceResponse)
            val event = NullvsInvoiceBatchResponseEvent(nullvsInvoiceBatchResponse)
            val expectedNullvsIntegrationLog = NullvsIntegrationLog(
                eventId = meta.eventId,
                eventName = meta.eventName,
                integrationEventName = meta.integrationEventName,
                internalId = meta.internalId,
                internalModelName = meta.internalModelName,
                externalModelName = meta.externalModelName,
                batchId = totvsInvoiceResponse.batch,
                idSoc = totvsInvoiceResponse.idSoc,
                batchType = BatchType.CREATE,
                payloadSequenceId = 1,
                description = null,
                status = LogStatus.PENDING,
            )

            every { nullvsInvoiceBatchResponse.toNullvsIntegrationLog() } returns expectedNullvsIntegrationLog
            coEvery { nullvsIntegrationLogService.add(any()) } returns expectedNullvsIntegrationLog

            val result = consumer.saveSyncedInvoiceToNullvsLog(event)

            ResultAssert.assertThat(result).isSuccessWithData(expectedNullvsIntegrationLog)

            coVerifyOnce { nullvsIntegrationLogService.add(expectedNullvsIntegrationLog) }
        }
    }
}

