package br.com.alice.nullvs.consumers.invoice

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.nullvs.clients.TotvsInvoiceClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsInvoiceBatchResponseEvent
import br.com.alice.nullvs.events.NullvsInvoiceRequestEvent
import br.com.alice.nullvs.exceptions.InvalidNullvsInvoiceRequestForCreateInvoice
import br.com.alice.nullvs.exceptions.InvalidNullvsInvoiceRequestForPaymentUpdate
import br.com.alice.nullvs.exceptions.TotvsInvoiceClientPostException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceData
import br.com.alice.nullvs.models.payment.TotvsInvoiceResponse
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.Test

class NullvsInvoiceBatchRequestConsumerTest : ConsumerTest() {

    private val invoiceClient: TotvsInvoiceClient = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()

    private val consumer = NullvsInvoiceBatchRequestConsumer(invoiceClient, kafkaProducerService,
        nullvsIntegrationLogService)

    private val eventId = RangeUUID.generate()
    private val internalId = RangeUUID.generate()

    private val meta = Meta(
        eventId = eventId,
        eventName = "event01",
        internalId = internalId,
        internalModelName = InternalModelType.MEMBER_INVOICE_GROUP,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.INVOICE,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val nullvsInvoiceData = NullvsInvoiceData(
        titlePrefix = "F",
        emittedDate = "2023-01-01".toLocalDate(),
        dueDate = "2023-03-01".toLocalDate(),
        amount = 1000.0,
        status = NullvsInvoiceData.Status.OPEN,
        financialHistory = "Some financial history",
        clientCode = "12345",
        type = "RA",
        discount = 50.0,
        fine = 10.0,
        interest = 5.0,
        monetaryCorrection = 0.0,
        natureCode = "001",
        titleNumber = "titleNumber",
        titleInstallment = "titleInstallment",
        paidAt = "2023-02-01".toLocalDate(),
    )

    private val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog()


    @Test
    fun `#ignore event when action is not the correct one`() = runBlocking {
        val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE, nullvsInvoiceData)
        val event = NullvsInvoiceRequestEvent(request)

        val result = consumer.reportInvoicePayment(event)

        assertThat(result).isSuccess()

        coVerifyNone { invoiceClient.postInvoicePayment(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }


    @Nested
    inner class ReportInvoicePayment {
        @Test
        fun `#call client and produce response event when action and attributes are correct`() = runBlocking<Unit> {
            val totvsCreateInvoiceResponse = TotvsInvoiceResponse("200", "batch", "idSoc", "action")
            val nullvsInvoiceCreationBatchResponse = NullvsInvoiceBatchResponse(meta, totvsCreateInvoiceResponse)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)
            coEvery { invoiceClient.postInvoicePayment(request) } returns nullvsInvoiceCreationBatchResponse.success()
            coEvery {
                kafkaProducerService.produce(match { it: NullvsInvoiceBatchResponseEvent ->
                    it.payload.nullvsInvoiceBatchResponse == nullvsInvoiceCreationBatchResponse
                })
            } returns mockk()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isSuccess()
        }

        @Test
        fun `#return failure when required attributes titleNumber is missing`() = runBlocking {
            val nullvsInvoiceData = nullvsInvoiceData.copy(titleNumber = null)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(InvalidNullvsInvoiceRequestForPaymentUpdate::class)
            coVerifyNone { invoiceClient.postInvoicePayment(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(match {
                it.eventId == meta.eventId &&
                        it.eventName == meta.eventName &&
                        it.integrationEventName == meta.integrationEventName &&
                        it.internalId == meta.internalId &&
                        it.internalModelName == meta.internalModelName &&
                        it.externalModelName == meta.externalModelName &&
                        it.batchType == BatchType.UPDATE &&
                        it.description == "NullvsInvoiceRequest has one or more required fields nullable" &&
                        it.status == LogStatus.TOTVS_NOT_CALLED
            }) }
        }

        @Test
        fun `#return failure when required attributes titleInstallment is missing`() = runBlocking {
            val nullvsInvoiceData = nullvsInvoiceData.copy(titleInstallment = null)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(InvalidNullvsInvoiceRequestForPaymentUpdate::class)
            coVerifyNone { invoiceClient.postInvoicePayment(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(match {
                it.eventId == meta.eventId &&
                        it.eventName == meta.eventName &&
                        it.integrationEventName == meta.integrationEventName &&
                        it.internalId == meta.internalId &&
                        it.internalModelName == meta.internalModelName &&
                        it.externalModelName == meta.externalModelName &&
                        it.batchType == BatchType.UPDATE &&
                        it.description == "NullvsInvoiceRequest has one or more required fields nullable" &&
                        it.status == LogStatus.TOTVS_NOT_CALLED
            }) }
        }

        @Test
        fun `#return failure when required attributes paidAt is missing`() = runBlocking {
            val nullvsInvoiceData = nullvsInvoiceData.copy(paidAt = null)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(InvalidNullvsInvoiceRequestForPaymentUpdate::class)
            coVerifyNone { invoiceClient.postInvoicePayment(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(match {
                it.eventId == meta.eventId &&
                        it.eventName == meta.eventName &&
                        it.integrationEventName == meta.integrationEventName &&
                        it.internalId == meta.internalId &&
                        it.internalModelName == meta.internalModelName &&
                        it.externalModelName == meta.externalModelName &&
                        it.batchType == BatchType.UPDATE &&
                        it.description == "NullvsInvoiceRequest has one or more required fields nullable" &&
                        it.status == LogStatus.TOTVS_NOT_CALLED
            }) }
        }

        @Test
        fun `#should throw AutoRetryableException on TotvsInvoiceClientPostException`() = runBlocking {
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { invoiceClient.postInvoicePayment(request) } returns
                    TotvsInvoiceClientPostException(NullvsActionType.UPDATE_PAYMENT, RuntimeException("GAVE BAD")).failure()
            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(AutoRetryableException::class)
            coVerifyOnce { invoiceClient.postInvoicePayment(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(any()) }
        }
    }

    @Nested
    inner class CreateInvoice {
        @Test
        fun `#call client and produce response event when action and attributes are correct`() = runBlocking<Unit> {
            val totvsCreateInvoiceResponse = TotvsInvoiceResponse("200", "batch", "idSoc", "action")
            val nullvsInvoiceCreationBatchResponse = NullvsInvoiceBatchResponse(meta, totvsCreateInvoiceResponse)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)
            coEvery { invoiceClient.postInvoice(request) } returns nullvsInvoiceCreationBatchResponse.success()
            coEvery {
                kafkaProducerService.produce(match { it: NullvsInvoiceBatchResponseEvent ->
                    it.payload.nullvsInvoiceBatchResponse == nullvsInvoiceCreationBatchResponse
                })
            } returns mockk()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isSuccess()
        }
        @Test
        fun `#return failure when required attributes paidAt is missing`() = runBlocking {
            val nullvsInvoiceData = nullvsInvoiceData.copy(paidAt = null)
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(InvalidNullvsInvoiceRequestForCreateInvoice::class)

            coVerifyNone { invoiceClient.postInvoicePayment(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(match {
                it.eventId == meta.eventId &&
                        it.eventName == meta.eventName &&
                        it.integrationEventName == meta.integrationEventName &&
                        it.internalId == meta.internalId &&
                        it.internalModelName == meta.internalModelName &&
                        it.externalModelName == meta.externalModelName &&
                        it.batchType == BatchType.CREATE &&
                        it.description == "NullvsInvoiceRequest has one or more required fields nullable" &&
                        it.status == LogStatus.TOTVS_NOT_CALLED
            }) }
        }

        @Test
        fun `#should throw AutoRetryableException on TotvsInvoiceClientPostException`() = runBlocking {
            val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)
            val event = NullvsInvoiceRequestEvent(request)

            coEvery { invoiceClient.postInvoice(request) } returns
                    TotvsInvoiceClientPostException(NullvsActionType.CREATE, RuntimeException("GAVE BAD")).failure()
            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            val result = consumer.reportInvoicePayment(event)

            assertThat(result).isFailureOfType(AutoRetryableException::class)
            coVerifyOnce { invoiceClient.postInvoice(any()) }
            coVerifyNone { kafkaProducerService.produce(any()) }
            coVerifyOnce { nullvsIntegrationLogService.add(any()) }
        }
    }
}
