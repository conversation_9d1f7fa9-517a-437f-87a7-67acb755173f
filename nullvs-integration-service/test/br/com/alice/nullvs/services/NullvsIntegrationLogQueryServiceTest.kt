package br.com.alice.nullvs.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.nullvs.converters.toModel
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking

import kotlin.test.Test

class NullvsIntegrationLogQueryServiceTest {
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val service = NullvsIntegrationLogQueryServiceImpl(nullvsIntegrationLogService)

    @Test
    fun `#findSubContractLogs should list `() = runBlocking {
        val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
            internalModelName = InternalModelType.SUBCONTRACT
        )
        val nullvsIntegrationLogModel = nullvsIntegrationLog.toModel()

        coEvery {
            nullvsIntegrationLogService.findByInternalIdAndModel(
                nullvsIntegrationLog.internalId,
                InternalModelType.SUBCONTRACT
            )
        } returns listOf(nullvsIntegrationLogModel)

        val result = service.findSubContractLogs(nullvsIntegrationLog.internalId)

        assertThat(result).isSuccessWithData(listOf(nullvsIntegrationLog))
        coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any())}
    }

    @Test
    fun `#findMemberLogs should list `() = runBlocking {
        val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
            internalModelName = InternalModelType.MEMBER
        )
        val nullvsIntegrationLogModel = nullvsIntegrationLog.toModel()

        coEvery {
            nullvsIntegrationLogService.findByInternalIdAndModel(
                nullvsIntegrationLog.internalId,
                InternalModelType.MEMBER
            )
        } returns listOf(nullvsIntegrationLogModel)

        val result = service.findMemberLogs(nullvsIntegrationLog.internalId)

        assertThat(result).isSuccessWithData(listOf(nullvsIntegrationLog))
        coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any())}
    }

    @Test
    fun `#findBillingAccountablePartyLogs should list `() = runBlocking {
        val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
            internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY
        )
        val nullvsIntegrationLogModel = nullvsIntegrationLog.toModel()

        coEvery {
            nullvsIntegrationLogService.findByInternalIdAndModel(
                nullvsIntegrationLog.internalId,
                InternalModelType.BILLING_ACCOUNTABLE_PARTY
            )
        } returns listOf(nullvsIntegrationLogModel)

        val result = service.findBillingAccountablePartyLogs(nullvsIntegrationLog.internalId)

        assertThat(result).isSuccessWithData(listOf(nullvsIntegrationLog))
        coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any())}
    }

    @Test
    fun `#findContractLogs should list `() = runBlocking {
        val nullvsIntegrationLog = TestModelFactory.buildNullvsIntegrationLog(
            internalModelName = InternalModelType.CONTRACT
        )
        val nullvsIntegrationLogModel = nullvsIntegrationLog.toModel()

        coEvery {
            nullvsIntegrationLogService.findByInternalIdAndModel(
                nullvsIntegrationLog.internalId,
                InternalModelType.CONTRACT
            )
        } returns listOf(nullvsIntegrationLogModel)

        val result = service.findContractLogs(nullvsIntegrationLog.internalId)

        assertThat(result).isSuccessWithData(listOf(nullvsIntegrationLog))
        coVerifyOnce { nullvsIntegrationLogService.findByInternalIdAndModel(any(), any())}
    }
}
