package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.money
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberLifecycleEventType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatusHistoryEntry
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PriceListingItem
import br.com.alice.data.layer.models.ProductType
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.HealthInstitution
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.membership.client.MemberLifeCycleEventsService
import br.com.alice.membership.client.MemberPrice
import br.com.alice.membership.client.MemberPriceService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.nullvs.clients.ViaCEPClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.exceptions.CityCodeNotFoundException
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.ManySubcontractsToContractException
import br.com.alice.nullvs.exceptions.NoSubcontractForCompanyException
import br.com.alice.nullvs.exceptions.NonActiveMember
import br.com.alice.nullvs.exceptions.NonHadPreviousActivationException
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.exceptions.NullPersonalFieldException
import br.com.alice.nullvs.exceptions.PersonAddressFieldException
import br.com.alice.nullvs.exceptions.SkipException
import br.com.alice.nullvs.exceptions.TestPersonalRegisterException
import br.com.alice.nullvs.exceptions.TotvsClientNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.logics.NullvsIntegrationRecordLogic.totvsBeneficiaryRegistryTofamilyCode
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.ViaCEPAddress
import br.com.alice.nullvs.models.company.CompanyInfo
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test
import br.com.alice.ehr.model.GracePeriodType as EhrGracePeriodType

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsMemberIntegrationServiceTest {
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService = mockk()
    private val personService: PersonService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val ansNumberCacheService: AnsNumberCacheService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val memberPriceService: MemberPriceService = mockk()
    private val viaCEPClient: ViaCEPClient = mockk()
    private val memberCptsService: MemberCptsService = mockk()
    private val nullvsIntegrationLogService: NullvsIntegrationLogService = mockk()
    private val memberService: MemberService = mockk()
    private val memberLifeCycleEventsService: MemberLifeCycleEventsService = mockk()
    private val companyInfoCacheService: CompanyInfoCacheService = mockk()

    private val service = TotvsMemberIntegrationService(
        nullvsIntegrationRecordService,
        personService,
        memberService,
        beneficiaryService,
        billingAccountablePartyService,
        viaCEPClient,
        memberCptsService,
        nullvsIntegrationLogService,
        memberPriceService,
        memberLifeCycleEventsService,
        ansNumberCacheService,
        companyInfoCacheService,
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.MEMBER,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.BENEFICIARY,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val person =
        TestModelFactory.buildPerson(
            mothersName = "Maria Pereira",
            phoneNumber = "***********",
            sex = Sex.FEMALE,
            dateOfBirth = LocalDateTime.of(1994, 10, 17, 14, 0),
        )
    private val ansNumber = "491.970/22-0"
    private val productId = RangeUUID.generate()
    private val memberId = RangeUUID.generate()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
    private val clientNullvsIntegrationRecord = TestModelFactory.buildNullvsIntegrationRecord(
        internalId = billingAccountableParty.id,
        internalModelName = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
    )
    private val memberNullvsIntegrationRecord = TestModelFactory.buildNullvsIntegrationRecord(
        internalId = memberId,
        internalModelName = InternalModelType.MEMBER,
        externalId = "*****************",
        externalModelName = ExternalModelType.BENEFICIARY,
    )
    private val company =
        TestModelFactory.buildCompany(totvsContract = "contract-code", totvsSubcontract = "subcontract-code")
    val companyInfo = CompanyInfo(
        company.id,
        company.cnpj,
        contractNumber = "00001",
        subcontractNumber = "0001",
        hasCompanyProductPriceListing = true
    )
    private val parentBeneficiary = TestModelFactory.buildBeneficiary()
    private val beneficiary =
        TestModelFactory.buildBeneficiary(
            personId = person.id,
            memberId = memberId,
            companyId = company.id,
            parentBeneficiary = parentBeneficiary.id,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.BROTHER_SISTER,
        )
    private val memberPrice = MemberPrice(
        memberId = memberId,
        items = listOf(
            PriceListingItem(
                minAge = 1,
                maxAge = 18,
                amount = 100.money,
                priceAdjustment = BigDecimal("20.00")
            ),
            PriceListingItem(
                minAge = 18,
                maxAge = 10000,
                amount = 200.money,
                priceAdjustment = BigDecimal("10.00"),
            )
        ),
        startDate = LocalDate.now()
    )
    private val nullvsIntegrationLog =
        TestModelFactory.buildNullvsIntegrationLog(eventId = meta.eventId, internalId = meta.internalId)

    private val gracePeriod = listOf(
        CptGracePeriod(
            condition = "Parto",
            validUntil = LocalDate.now().plusDays(300L).toBrazilianDateFormat(),
            baseDate = LocalDate.now(),
            type = EhrGracePeriodType.BIRTH,
            periodInDays = 300L
        )
    )
    private val conditions = listOf(
        CptCondition(
            name = "Cólera não especificada",
            cid = "A009",
            validUntil = LocalDate.now().plusDays(730L).toBrazilianDateFormat(),
            baseDate = LocalDate.now(),
            periodInDays = 730L
        )
    )

    fun nullvsMemberBatchRequest(
        person: Person,
        member: Member,
        nullvsActionType: NullvsActionType,
        groupCompany: TotvsGroupCompany? = null,
        totvsContract: String? = null,
        totvsSubcontract: String? = null,
        client: String? = null,
        familyTotvsCode: String? = null,
        userType: TotvsUser? = null,
        parentBeneficiaryRelationType: TotvsRelationType? = null,
        priceListing: List<TotvsMemberRequest.TotvsBeneficiary.BillingRange>? = null,
        idTotvs: String? = null,
        cnpj: String? = null,
        ansNumber: String?,
        cityCode: TotvsCityCode? = TotvsCityCode("3550308", "SAO PAULO", "SP"),
        cpts: List<TotvsMemberRequest.TotvsBeneficiary.CPT> = emptyList(),
        gracePeriods: List<TotvsMemberRequest.TotvsBeneficiary.GracePeriod> = emptyList(),
    ) = NullvsMemberBatchRequest(
        meta,
        nullvsActionType,
        TotvsMemberRequest(
            company = groupCompany,
            totvsContract = totvsContract,
            totvsSubContract = totvsSubcontract,
            client = client,
            familyTotvsCode = familyTotvsCode,
            createdAt = if (member.status == MemberStatus.ACTIVE) member.activationDate!! else member.createdAt,
            ANSProductId = ansNumber?.onlyDigits(),
            idPayload = 1,
            cnpj = cnpj,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = userType,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    idTotvs = idTotvs,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = cityCode,
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = if (nullvsActionType == NullvsActionType.CREATE) if (member.status == MemberStatus.ACTIVE) member.activationDate!! else member.createdAt else null,
                    parentBeneficiaryRelationType = parentBeneficiaryRelationType,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = if (nullvsActionType == NullvsActionType.CREATE) ansNumber?.onlyDigits() else null,
                    canceledAt = null,
                    canceledReason = null,
                    priceListing = priceListing,
                    cpts = cpts,
                    gracePeriods = gracePeriods,
                ),
            ),
        ),
    )

    @Nested
    inner class CreateBeneficiaryTotvsB2C {

        private val member = TestModelFactory.buildMember(
            id = memberId,
            personId = person.id,
            productId = productId,
            productType = ProductType.B2C,
            status = MemberStatus.ACTIVE,
        )

        @Test
        fun `should create a NullvsMemberBatchRequest`() =
            runBlocking {
                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice

                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt(
                    gracePeriod = gracePeriod,
                    conditions = conditions
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns clientNullvsIntegrationRecord

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        TotvsGroupCompany.ALICE_INDIVIDUAL,
                        client = clientNullvsIntegrationRecord.externalId,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        priceListing = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 1,
                                toAge = 18,
                                value = BigDecimal("100.00"),
                                startDate = memberPrice.startDate,
                            ),
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 18,
                                toAge = 999,
                                value = BigDecimal("200.00"),
                                startDate = memberPrice.startDate,
                            ),
                        ),
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now(),
                                periodInDays = 730L
                            ),
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.now(),
                                periodInDays = 300L
                            ),
                        ),
                        ansNumber = ansNumber,
                    ),
                )

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest to B2C even member product is B2B when they are a ei juvenil by product`() =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "ei_jr_products", listOf(productId)) {
                    coEvery { personService.get(person.id) } returns person
                    coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                    coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

                    coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                    coEvery {
                        memberCptsService.buildPersonCpts(person)
                    } returns MemberCpt()

                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountableParty.id,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns clientNullvsIntegrationRecord

                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isSuccessWithData(
                        nullvsMemberBatchRequest(
                            person.copy(firstName = "Jose"),
                            member,
                            NullvsActionType.CREATE,
                            TotvsGroupCompany.ALICE_EI_JUVENIL,
                            client = clientNullvsIntegrationRecord.externalId,
                            userType = TotvsUser.HOLDER,
                            parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                            priceListing = listOf(
                                TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                    fromAge = 1,
                                    toAge = 18,
                                    value = BigDecimal("100.00"),
                                    startDate = memberPrice.startDate,
                                ),
                                TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                    fromAge = 18,
                                    toAge = 999,
                                    value = BigDecimal("200.00"),
                                    startDate = memberPrice.startDate,
                                ),
                            ),
                            ansNumber = ansNumber,
                        ),
                    )

                    coVerifyOnce {
                        personService.get(person.id)
                        ansNumberCacheService.getByProductId(productId)
                        billingAccountablePartyService.getCurrent(person.id)
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountableParty.id,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    }
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest when the city code is not found, but got from viaCEP api`() =
            runBlocking {
                val mockedViaCEPAddress = ViaCEPAddress(
                    cep = "********",
                    ibge = "nao-existo",
                    logradouro = "logradouro",
                    bairro = "bairro",
                    localidade = "São Paulo",
                    uf = "SP",
                )
                val person =
                    person.copy(
                        addresses = listOf(
                            TestModelFactory.buildAddress(
                                city = "Uma cidade que não existe",
                                state = State.SP
                            )
                        )
                    )

                val personUpdated = person.copy(
                    addresses = listOf(
                        person.mainAddress!!.copy(
                            city = "São Paulo",
                            neighbourhood = "bairro",
                        )
                    )
                )

                val member = member.copy(personId = personUpdated.id)

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                coEvery { viaCEPClient.getAddressFromPostalCode(person.mainAddress!!.postalCode!!) } returns mockedViaCEPAddress
                coEvery { personService.update(personUpdated) } returns personUpdated

                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice

                coEvery {
                    memberCptsService.buildPersonCpts(personUpdated)
                } returns MemberCpt(
                    gracePeriod = gracePeriod,
                    conditions = conditions
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns clientNullvsIntegrationRecord

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        personUpdated.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        TotvsGroupCompany.ALICE_INDIVIDUAL,
                        client = clientNullvsIntegrationRecord.externalId,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        priceListing = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 1,
                                toAge = 18,
                                value = BigDecimal("100.00"),
                                startDate = memberPrice.startDate,
                            ),
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 18,
                                toAge = 999,
                                value = BigDecimal("200.00"),
                                startDate = memberPrice.startDate,
                            ),
                        ),
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now(),
                                periodInDays = 730L
                            ),
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.now(),
                                periodInDays = 300L
                            ),
                        ),
                        ansNumber = ansNumber,
                    ),
                )

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    personService.update(personUpdated)
                    memberCptsService.buildPersonCpts(personUpdated)
                    memberPriceService.getByMemberId(memberId)
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest when the city code is not found, but got from viaCEP api but shouldn't update person`() =
            runBlocking {
                val mockedViaCEPAddress = ViaCEPAddress(
                    cep = "********",
                    ibge = "nao-existo",
                    logradouro = "logradouro",
                    bairro = "bairro",
                    localidade = "São Paulo",
                    uf = "SP",
                )
                val person =
                    person.copy(
                        addresses = listOf(
                            TestModelFactory.buildAddress(
                                city = "São Paulo",
                                neighbourhood = "bairro",
                                state = State.SP,
                            )
                        )
                    )

                val member = member.copy(personId = person.id)

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                coEvery { viaCEPClient.getAddressFromPostalCode(person.mainAddress!!.postalCode!!) } returns mockedViaCEPAddress
                coEvery { personService.update(person) } returns person

                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice

                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt(
                    gracePeriod = gracePeriod,
                    conditions = conditions
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns clientNullvsIntegrationRecord

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        TotvsGroupCompany.ALICE_INDIVIDUAL,
                        client = clientNullvsIntegrationRecord.externalId,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        priceListing = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 1,
                                toAge = 18,
                                value = BigDecimal("100.00"),
                                startDate = memberPrice.startDate,
                            ),
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 18,
                                toAge = 999,
                                value = BigDecimal("200.00"),
                                startDate = memberPrice.startDate,
                            ),
                        ),
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now(),
                                periodInDays = 730L
                            ),
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.now(),
                                periodInDays = 300L
                            ),
                        ),
                        ansNumber = ansNumber,
                    ),
                )

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    memberCptsService.buildPersonCpts(person)
                    memberPriceService.getByMemberId(memberId)
                }
                coVerifyNone { personService.update(person) }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest when member is a dependent`() =
            runBlocking {
                val dependentPerson = person.copy(id = PersonId())
                val dependentMember = member.copy(
                    id = RangeUUID.generate(),
                    personId = dependentPerson.id,
                    parentMember = member.id,
                    parentPerson = member.personId,
                    status = MemberStatus.ACTIVE,
                )

                coEvery { personService.get(dependentPerson.id) } returns dependentPerson
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                coEvery { memberService.get(member.id) } returns member
                coEvery { billingAccountablePartyService.getCurrent(member.personId) } returns billingAccountableParty

                coEvery { memberPriceService.getByMemberId(dependentMember.id) } returns memberPrice

                coEvery {
                    memberCptsService.buildPersonCpts(dependentPerson)
                } returns MemberCpt(
                    gracePeriod = gracePeriod,
                    conditions = conditions
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns clientNullvsIntegrationRecord

                val result = service.createBeneficiaryTotvs(meta, dependentMember)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        dependentPerson.copy(firstName = "Jose"),
                        dependentMember,
                        NullvsActionType.CREATE,
                        TotvsGroupCompany.ALICE_INDIVIDUAL,
                        client = clientNullvsIntegrationRecord.externalId,
                        familyTotvsCode = "000001",
                        userType = TotvsUser.DEPENDENT,
                        parentBeneficiaryRelationType = TotvsRelationType.OTHER,
                        priceListing = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 1,
                                toAge = 18,
                                value = BigDecimal("100.00"),
                                startDate = memberPrice.startDate,
                            ),
                            TotvsMemberRequest.TotvsBeneficiary.BillingRange(
                                fromAge = 18,
                                toAge = 999,
                                value = BigDecimal("200.00"),
                                startDate = memberPrice.startDate,
                            ),
                        ),
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now(),
                                periodInDays = 730L
                            ),
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.now(),
                                periodInDays = 300L
                            ),
                        ),
                        ansNumber = ansNumber,
                    ),
                )
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the member is canceled`() =
            runBlocking {
                val member = member.copy(status = MemberStatus.CANCELED)

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isFailureOfType(SkipException::class)

                coVerifyNone {
                    personService.get(any())
                    ansNumberCacheService.getByProductId(any())
                    billingAccountablePartyService.getCurrent(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    nullvsIntegrationLogService.add(any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the member is pending`() =
            runBlocking {
                val member = member.copy(status = MemberStatus.PENDING)

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isFailureOfType(NonActiveMember::class)

                coVerifyNone {
                    personService.get(any())
                    ansNumberCacheService.getByProductId(any())
                    billingAccountablePartyService.getCurrent(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    nullvsIntegrationLogService.add(any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the client totvs registry is not found`() =
            runBlocking {
                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt()
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns TotvsClientNotFoundByInternalIdException(billingAccountableParty.id)
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TotvsClientNotFoundByInternalIdException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Totvs mapping not found to billingAccountablePartyId: ") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the client totvs registry is not found - feature flag disabled`() =
            runBlocking {
                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", false) {

                    coEvery { personService.get(person.id) } returns person
                    coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                    coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                    coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                    coEvery {
                        memberCptsService.buildPersonCpts(person)
                    } returns MemberCpt()
                    coEvery {
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountableParty.id,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                    } returns TotvsClientNotFoundByInternalIdException(billingAccountableParty.id)
                    coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog

                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TotvsClientNotFoundByInternalIdException::class)

                    coVerifyOnce {
                        personService.get(person.id)
                        ansNumberCacheService.getByProductId(productId)
                        billingAccountablePartyService.getCurrent(person.id)
                        nullvsIntegrationRecordService.findByInternalIdAndModel(
                            billingAccountableParty.id,
                            InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                        )
                        nullvsIntegrationLogService.add(match {
                            it.eventId == meta.eventId &&
                                    it.eventName == meta.eventName &&
                                    it.integrationEventName == meta.integrationEventName &&
                                    it.internalId == meta.internalId &&
                                    it.internalModelName == meta.internalModelName &&
                                    it.externalModelName == meta.externalModelName &&
                                    it.batchType == BatchType.CREATE &&
                                    it.description!!.startsWith("Totvs mapping not found to billingAccountablePartyId: ") &&
                                    it.status == LogStatus.TOTVS_NOT_CALLED
                        })
                    }

                    coVerifyNone {
                        nullvsIntegrationLogService.checkToCreateErrorLog(any())
                    }
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the ANS code is not informed`() =
            runBlocking {
                val ansNumber = null

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NullANSFieldException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)

                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Missing the required field ANS product code for product") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the Person is test`() =
            runBlocking {
                val testPerson = person.copy(isTest = true)

                coEvery { personService.get(person.id) } returns testPerson
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(testPerson.id) } returns billingAccountableParty
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TestPersonalRegisterException::class)
                }

                coVerifyOnce {
                    personService.get(testPerson.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("This is a test user, it will not be sent to TOTVS") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }
                coVerifyNone {
                    billingAccountablePartyService.getCurrent(testPerson.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when some personal info is null`() =
            runBlocking {
                val person = person.copy(dateOfBirth = null)

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NullPersonalFieldException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Missing the required field dateOfBirth for person") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }
    }

    @Nested
    inner class CreateBeneficiaryTotvsB2B {
        private val member =
            TestModelFactory.buildMember(
                id = memberId,
                personId = person.id,
                productId = productId,
                productType = ProductType.B2B,
                status = MemberStatus.ACTIVE,
            )

        @Test
        fun `should create a NullvsMemberBatchRequest to dependent`() =
            runBlocking {
                coEvery { personService.get(person.id) } returns person
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { beneficiaryService.get(parentBeneficiary.id) } returns parentBeneficiary

                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt(
                    gracePeriod = gracePeriod,
                    conditions = conditions,
                    healthInstitutions = listOf(
                        HealthInstitution(
                            cnpj = listOf("cnpj01"),
                            startedAt = LocalDateTime.now()
                        )
                    )
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        familyTotvsCode = "*****************".substring(8, 14),
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.DEPENDENT,
                        parentBeneficiaryRelationType = TotvsRelationType.OTHER,
                        cnpj = company.cnpj,
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now(),
                                periodInDays = 730L
                            ),
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                startedAt = LocalDate.now(),
                                cnpj = "cnpj01",
                                cid = null,
                                periodInDays = 0L
                            ),
                        ),
                        gracePeriods = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                                type = GracePeriodType.BIRTH,
                                startedAt = LocalDate.now(),
                                periodInDays = 300L
                            ),
                        ),
                        ansNumber = ansNumber,
                    ),
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    beneficiaryService.get(parentBeneficiary.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                    ansNumberCacheService.getByProductId(productId)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                }
                coVerifyNone {
                    memberPriceService.getByMemberId(any())
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest to the holder and get the contract and subcontract from own entities`() =
            runBlocking {
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt()

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        cnpj = company.cnpj,
                        ansNumber = ansNumber,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    ansNumberCacheService.getByProductId(productId)
                }

                coVerifyNone {
                    memberPriceService.getByMemberId(any())
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                }
            }


        @Test
        fun `should create a NullvsMemberBatchRequest when the beneficiary does not have a subcontract set and the contract does not any subcontracts`() =
            runBlocking {
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                    companySubContractId = null,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns NoSubcontractForCompanyException(
                    ""
                )
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NoSubcontractForCompanyException::class)
                }

                coVerifyNone {
                    beneficiaryService.update(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                }

                coVerifyOnce {
                    personService.get(person.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    beneficiaryService.findByMemberId(member.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == nullvsIntegrationLog.eventId &&
                                it.internalId == nullvsIntegrationLog.internalId
                    })
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest when the beneficiary does not have a subcontract set and the contract have many subcontracts`() =
            runBlocking {
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                    companySubContractId = null
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber

                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns ManySubcontractsToContractException(
                    ""
                )

                coEvery {
                    nullvsIntegrationLogService.checkToCreateErrorLog(any())
                } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(ManySubcontractsToContractException::class)
                }

                coVerifyNone {
                    beneficiaryService.update(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                }

                coVerifyOnce {
                    personService.get(person.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    beneficiaryService.findByMemberId(member.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(any())
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest to holder when the product type is ADESAO`() =
            runBlocking {
                val member =
                    member.copy(selectedProduct = member.selectedProduct.copy(type = ProductType.ADESAO))
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber

                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberCptsService.buildPersonCpts(person) } returns MemberCpt()

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        TotvsGroupCompany.DUQUESA_ADESAO,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        cnpj = null,
                        ansNumber = ansNumber,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    ansNumberCacheService.getByProductId(productId)
                    companyInfoCacheService.getByBeneficiary(beneficiary)

                }
                coVerifyNone {
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                    memberPriceService.getByMemberId(any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the member is not active`() =
            runBlocking {
                val member = member.copy(status = MemberStatus.PENDING)

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isFailureOfType(NonActiveMember::class)

                coVerifyNone {
                    personService.get(any())
                    beneficiaryService.findByMemberId(any())
                    beneficiaryService.get(any())
                    ansNumberCacheService.getByProductId(any())
                    companyInfoCacheService.getByBeneficiary(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                    nullvsIntegrationLogService.add(any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the parent totvs registry is not found`() =
            runBlocking {
                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { beneficiaryService.get(parentBeneficiary.id) } returns parentBeneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber

                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                } returns NotFoundException("")
                coEvery { nullvsIntegrationLogService.findByInternalId(parentBeneficiary.memberId) } returns emptyList()
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(DependsOnModelException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    beneficiaryService.get(parentBeneficiary.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                    nullvsIntegrationLogService.findByInternalId(parentBeneficiary.memberId)
                    nullvsIntegrationLogService.checkToCreateErrorLog((match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description == "For this action the MEMBER(${parentBeneficiary.memberId}) external id is necessary" &&
                                it.status == LogStatus.WAITING &&
                                it.dependsOnId == parentBeneficiary.memberId &&
                                it.dependsOnModel == InternalModelType.MEMBER
                    }))
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the ANS code is not informed`() =
            runBlocking {
                val ansNumber = null

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NullANSFieldException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Missing the required field ANS product code for product") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(any())
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the city code is not found`() =
            runBlocking {
                val mockedViaCEPAddress = ViaCEPAddress(
                    cep = "********",
                    ibge = "nao-existo",
                    logradouro = "logradouro",
                    bairro = "bairro",
                    localidade = "localidade",
                    uf = "SP",
                )

                val person =
                    person.copy(addresses = listOf(TestModelFactory.buildAddress(city = "Uma cidade que não existe")))

                val personUpdated = person.copy(
                    addresses = listOf(
                        person.mainAddress!!.copy(
                            city = "localidade",
                            neighbourhood = "bairro",
                        )
                    )
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { viaCEPClient.getAddressFromPostalCode(person.mainAddress!!.postalCode!!) } returns mockedViaCEPAddress
                coEvery { personService.update(personUpdated) } returns personUpdated
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(CityCodeNotFoundException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog((match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description == "City code is not found for city LOCALIDADE and state SP" &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    }))
                    personService.update(personUpdated)
                }
                coVerifyNone {
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(any())
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the neighbourhood is not found`() =
            runBlocking {
                val mockedViaCEPAddress = ViaCEPAddress(
                    cep = "********",
                    ibge = "nao-existo",
                    logradouro = "logradouro",
                    bairro = "",
                    localidade = "localidade",
                    uf = "SP",
                )

                val person =
                    person.copy(addresses = listOf(TestModelFactory.buildAddress(neighbourhood = "")))

                val personUpdated = person.copy(
                    addresses = listOf(
                        person.mainAddress!!.copy(
                            city = "localidade",
                            neighbourhood = "",
                        )
                    )
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { viaCEPClient.getAddressFromPostalCode(person.mainAddress!!.postalCode!!) } returns mockedViaCEPAddress
                coEvery { personService.update(personUpdated) } returns personUpdated
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(PersonAddressFieldException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    personService.update(personUpdated)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Missing the required field address neighbourhood for person") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(any())
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when some personal info is null`() =
            runBlocking {
                val person = person.copy(sex = null, dateOfBirth = null)

                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NullPersonalFieldException::class)
                }


                coVerifyOnce {
                    personService.get(person.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("Missing the required field dateOfBirth for person") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }
                coVerifyNone {
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(any(), any())
                }

            }

        @Test
        fun `should create a NullvsMemberBatchRequest with null priceListing when cppl exists`() =
            runBlocking {

                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber

                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt()

                val result = service.createBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CREATE,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        cnpj = company.cnpj,
                        priceListing = null,
                        ansNumber = ansNumber,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    ansNumberCacheService.getByProductId(productId)
                }

                coVerifyNone {
                    memberPriceService.getByMemberId(memberId) wasNot Called
                    beneficiaryService.get(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                }
            }

        @Test
        fun `shouldnt create a NullvsMemberBatchRequest when member price has empty item list`() =
            runBlocking {
                val companyInfo = companyInfo.copy(hasCompanyProductPriceListing = false)
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                )

                val memberPrice = memberPrice.copy(items = emptyList())

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt()
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.createBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(NotFoundException::class)
                }

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    ansNumberCacheService.getByProductId(productId)

                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    memberPriceService.getByMemberId(memberId)
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.CREATE &&
                                it.description!!.startsWith("List of entities is empty") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    beneficiaryService.get(any())

                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        InternalModelType.MEMBER,
                    )
                }
            }
    }

    @Nested
    inner class CancelBeneficiaryTotvsB2C {
        @Test
        fun `should create a NullvsMemberBatchRequest`() = runBlocking {
            val member =
                TestModelFactory.buildMember(
                    id = memberId,
                    personId = person.id,
                    productId = productId,
                    productType = ProductType.B2C
                )
            coEvery { personService.get(person.id) } returns person
            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    memberId,
                    InternalModelType.MEMBER,
                )
            } returns memberNullvsIntegrationRecord

            val result = service.cancelBeneficiaryTotvs(meta, member)

            assertThat(result).isSuccessWithData(
                nullvsMemberBatchRequest(
                    person.copy(firstName = "Jose"),
                    member,
                    NullvsActionType.CANCEL,
                    TotvsGroupCompany.ALICE_INDIVIDUAL,
                    client = null,
                    userType = null,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    priceListing = null,
                    familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                    idTotvs = memberNullvsIntegrationRecord.externalId,
                    ansNumber = null,
                ),
            )

            coVerifyOnce {
                personService.get(person.id)
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    memberId,
                    InternalModelType.MEMBER,
                )
            }
            coVerifyNone {
                billingAccountablePartyService.getCurrent(person.id)
                memberPriceService.getByMemberId(memberId)
                ansNumberCacheService.getByProductId(any())
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    any(),
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            }

        }
    }

    @Nested
    inner class CancelBeneficiaryTotvsB2B {
        val contract = TestModelFactory.buildCompanyContract(externalId = "00001")
        val subcontract =
            TestModelFactory.buildCompanySubContract(externalId = "0001", contractId = contract.id)

        @Test
        fun `should create a NullvsMemberBatchRequest to dependent`() =
            runBlocking {
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )
                val beneficiary = beneficiary.copy(companySubContractId = subcontract.id)

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                val result = service.cancelBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CANCEL,
                        familyTotvsCode = "*****************".substring(8, 14),
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = null,
                        parentBeneficiaryRelationType = null,
                        priceListing = null,
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                }

                coVerifyNone {
                    beneficiaryService.get(parentBeneficiary.id)
                    ansNumberCacheService.getByProductId(productId)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                }
            }

        @Test
        fun `should ignore the synchronization event at TOTVS when the member Alice does not have a previous activation history`() =
            runBlocking {
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B,
                        statusHistory = listOf(
                            MemberStatusHistoryEntry(
                                MemberStatus.PENDING,
                                LocalDateTime.now().minusDays(10).toString()
                            ),
                            MemberStatusHistoryEntry(MemberStatus.CANCELED, LocalDateTime.now().toString())
                        )
                    )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER
                    )
                } returns NotFoundException().failure()

                val result = service.cancelBeneficiaryTotvs(meta, member)
                assertThat(result).isFailureOfType(NonHadPreviousActivationException::class)

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        any(),
                        any(),
                    )
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest to holder`() =
            runBlocking {
                val person = person.copy(tags = listOf("pitaya"))
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                    companySubContractId = subcontract.id,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary

                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                val result = service.cancelBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CANCEL,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = null,
                        parentBeneficiaryRelationType = null,
                        priceListing = null,
                        familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                }
                coVerifyNone {
                    beneficiaryService.get(any())
                    ansNumberCacheService.getByProductId(any())
                }
            }
    }

    @Nested
    inner class CancelBeneficiaryDuquesa {

        val contract = TestModelFactory.buildCompanyContract(externalId = "00001")
        val subcontract =
            TestModelFactory.buildCompanySubContract(externalId = "0001", contractId = contract.id)

        @Test
        fun `#should cancel a dependent member Duquesa without previous activation and with internal record`() =
            runBlocking {
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B,
                        statusHistory = listOf(
                            MemberStatusHistoryEntry(
                                MemberStatus.PENDING,
                                LocalDateTime.now().minusDays(10).toString()
                            ),
                            MemberStatusHistoryEntry(MemberStatus.CANCELED, LocalDateTime.now().toString())
                        ),
                        brand = Brand.DUQUESA
                    )

                val beneficiary = beneficiary.copy(companySubContractId = subcontract.id)

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary

                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                val result = service.cancelBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CANCEL,
                        familyTotvsCode = "*****************".substring(8, 14),
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = null,
                        parentBeneficiaryRelationType = null,
                        priceListing = null,
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    personService.get(any())
                    beneficiaryService.findByMemberId(any())
                    companyInfoCacheService.getByBeneficiary(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                }
                coVerifyNone {
                    beneficiaryService.get(any())
                    ansNumberCacheService.getByProductId(any())
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                }
            }

        @Test
        fun `#should cancel a holder member Duquesa without previous activation and with internal record`() =
            runBlocking {
                val person = person.copy(tags = listOf("duquesa"))
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B,
                        brand = Brand.DUQUESA,
                        statusHistory = listOf(
                            MemberStatusHistoryEntry(
                                MemberStatus.PENDING,
                                LocalDateTime.now().minusDays(10).toString()
                            ),
                            MemberStatusHistoryEntry(MemberStatus.CANCELED, LocalDateTime.now().toString())
                        )
                    )
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                    companySubContractId = subcontract.id,
                )

                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord

                val result = service.cancelBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person.copy(firstName = "Jose"),
                        member,
                        NullvsActionType.CANCEL,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = null,
                        parentBeneficiaryRelationType = null,
                        priceListing = null,
                        familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                }
                coVerifyNone {
                    beneficiaryService.get(any())
                    ansNumberCacheService.getByProductId(any())
                }
            }

        @Test
        fun `#should not cancel a holder member Duquesa without previous activation and without internal record`() =
            runBlocking {
                val person = person.copy(tags = listOf("duquesa"))
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B,
                        brand = Brand.DUQUESA,
                        statusHistory = listOf(
                            MemberStatusHistoryEntry(
                                MemberStatus.PENDING,
                                LocalDateTime.now().minusDays(10).toString()
                            ),
                            MemberStatusHistoryEntry(MemberStatus.CANCELED, LocalDateTime.now().toString())
                        )
                    )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                } returns NotFoundException().failure()

                val result = service.cancelBeneficiaryTotvs(meta, member)

                assertThat(result).isFailureOfType(NonHadPreviousActivationException::class)

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        memberId,
                        InternalModelType.MEMBER,
                    )
                }
            }
    }

    @Nested
    inner class UpdateBeneficiaryTotvsB2C {
        @Test
        fun `should create a NullvsMemberBatchRequest`() = runBlocking {
            val person = person.copy(firstName = "Updated")

            val member =
                TestModelFactory.buildMember(
                    id = memberId,
                    personId = person.id,
                    productId = productId,
                    productType = ProductType.B2C
                )

            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    member.id,
                    InternalModelType.MEMBER,
                )
            } returns memberNullvsIntegrationRecord
            coEvery { personService.get(person.id) } returns person
            coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

            coEvery {
                memberCptsService.buildPersonCpts(person)
            } returns MemberCpt(
                gracePeriod = gracePeriod.plus(
                    CptGracePeriod(
                        condition = "Cirurgias e Internações eletivas",
                        validUntil = LocalDate.now().plusDays(180L).toBrazilianDateFormat(),
                        baseDate = LocalDate.now(),
                        type = EhrGracePeriodType.ELECTIVE_SURGERY,
                        periodInDays = 180L
                    )
                ).reversed(),
                conditions = conditions,
                healthInstitutions = listOf(
                    HealthInstitution(
                        cnpj = listOf("cnpj01"),
                        startedAt = LocalDateTime.now()
                    )
                )
            )
            coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    billingAccountableParty.id,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            } returns clientNullvsIntegrationRecord

            val result = service.updateBeneficiaryTotvs(meta, member)

            assertThat(result).isSuccessWithData(
                nullvsMemberBatchRequest(
                    person,
                    member,
                    NullvsActionType.UPDATE,
                    TotvsGroupCompany.ALICE_INDIVIDUAL,
                    client = clientNullvsIntegrationRecord.externalId,
                    userType = null,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    priceListing = null,
                    familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                    idTotvs = memberNullvsIntegrationRecord.externalId,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "A009",
                            startedAt = LocalDate.now(),
                            periodInDays = 730L
                        ),
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            startedAt = LocalDate.now(),
                            cnpj = "cnpj01",
                            cid = null,
                            periodInDays = 0L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.ELECTIVE_SURGERY,
                            startedAt = LocalDate.now(),
                            periodInDays = 180L
                        ),
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.now(),
                            periodInDays = 300L
                        ),
                    ),
                    ansNumber = null,
                ),
            )

            coVerifyOnce {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    member.id,
                    InternalModelType.MEMBER,
                )
                personService.get(person.id)
                billingAccountablePartyService.getCurrent(person.id)
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    billingAccountableParty.id,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            }

            coVerifyNone {
                ansNumberCacheService.getByProductId(productId)
            }
        }

        @Test
        fun `should create a NullvsMemberBatchRequest even when the city code is not found`() = runBlocking {
            val person =
                person.copy(
                    firstName = "Updated",
                    addresses = listOf(
                        TestModelFactory.buildAddress(
                            city = "Uma cidade que não existe",
                            state = State.SP
                        )
                    )
                )

            val member =
                TestModelFactory.buildMember(
                    id = memberId,
                    personId = person.id,
                    productId = productId,
                    productType = ProductType.B2C
                )

            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    member.id,
                    InternalModelType.MEMBER,
                )
            } returns memberNullvsIntegrationRecord
            coEvery { viaCEPClient.getAddressFromPostalCode(person.mainAddress!!.postalCode!!) } returns Exception("Not found")
            coEvery { personService.get(person.id) } returns person
            coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
            coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty

            coEvery {
                memberCptsService.buildPersonCpts(person)
            } returns MemberCpt(
                gracePeriod = gracePeriod.plus(
                    CptGracePeriod(
                        condition = "Cirurgias e Internações eletivas",
                        validUntil = LocalDate.now().plusDays(180L).toBrazilianDateFormat(),
                        baseDate = LocalDate.now(),
                        periodInDays = 180L,
                        type = EhrGracePeriodType.ELECTIVE_SURGERY
                    )
                ).reversed(),
                conditions = conditions,
                healthInstitutions = listOf(
                    HealthInstitution(
                        cnpj = listOf("cnpj01"),
                        startedAt = LocalDateTime.now()
                    )
                )
            )
            coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    billingAccountableParty.id,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            } returns clientNullvsIntegrationRecord

            val result = service.updateBeneficiaryTotvs(meta, member)

            assertThat(result).isSuccessWithData(
                nullvsMemberBatchRequest(
                    person,
                    member,
                    NullvsActionType.UPDATE,
                    TotvsGroupCompany.ALICE_INDIVIDUAL,
                    client = clientNullvsIntegrationRecord.externalId,
                    userType = null,
                    cityCode = null,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    priceListing = null,
                    familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                    idTotvs = memberNullvsIntegrationRecord.externalId,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "A009",
                            startedAt = LocalDate.now(),
                            periodInDays = 730L
                        ),
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            startedAt = LocalDate.now(),
                            cnpj = "cnpj01",
                            cid = null,
                            periodInDays = 0L
                        ),
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.ELECTIVE_SURGERY,
                            startedAt = LocalDate.now(),
                            periodInDays = 180L
                        ),
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.now(),
                            periodInDays = 300L
                        ),
                    ),
                    ansNumber = null,
                ),
            )

            coVerifyOnce {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    member.id,
                    InternalModelType.MEMBER,
                )
                personService.get(person.id)
                billingAccountablePartyService.getCurrent(person.id)
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    billingAccountableParty.id,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                )
            }

            coVerifyNone { ansNumberCacheService.getByProductId(any()) }
        }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the client totvs registry is not found`() =
            runBlocking {
                val person = person.copy(firstName = "Updated")

                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2C
                    )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord
                coEvery { personService.get(person.id) } returns person
                coEvery { ansNumberCacheService.getByProductId(productId) } returns ansNumber
                coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    memberCptsService.buildPersonCpts(person)
                } returns MemberCpt()
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                } returns TotvsClientNotFoundByInternalIdException(billingAccountableParty.id)
                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(any()) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.updateBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TotvsClientNotFoundByInternalIdException::class)
                }

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                    personService.get(person.id)
                    billingAccountablePartyService.getCurrent(person.id)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        billingAccountableParty.id,
                        InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    )
                    nullvsIntegrationLogService.checkToCreateErrorLog(match {
                        it.eventId == meta.eventId &&
                                it.eventName == meta.eventName &&
                                it.integrationEventName == meta.integrationEventName &&
                                it.internalId == meta.internalId &&
                                it.internalModelName == meta.internalModelName &&
                                it.externalModelName == meta.externalModelName &&
                                it.batchType == BatchType.UPDATE &&
                                it.description!!.startsWith("Totvs mapping not found to billingAccountablePartyId: ") &&
                                it.status == LogStatus.TOTVS_NOT_CALLED
                    })
                }

                coVerifyNone {
                    ansNumberCacheService.getByProductId(any())
                }
            }

    }

    @Nested
    inner class UpdateBeneficiaryTotvsB2B {
        val contract = TestModelFactory.buildCompanyContract(externalId = "00001")
        val subcontract =
            TestModelFactory.buildCompanySubContract(externalId = "0001", contractId = contract.id)

        @Test
        fun `should create a NullvsMemberBatchRequest to dependent`() =
            runBlocking {
                val person = person.copy(firstName = "Updated")

                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )

                val parentRecord = TestModelFactory.buildNullvsIntegrationRecord(
                    internalModelName = InternalModelType.MEMBER,
                    externalId = "00010003000001910"
                )

                val beneficiary = beneficiary.copy(companySubContractId = subcontract.id)

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord
                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { beneficiaryService.get(parentBeneficiary.id) } returns parentBeneficiary
                coEvery { memberCptsService.buildPersonCpts(person) } returns MemberCpt(
                    conditions = listOf(
                        CptCondition(
                            name = "Cólera não especificada",
                            cid = "A009",
                            validUntil = LocalDate.now().plusDays(640L).toBrazilianDateFormat(),
                            baseDate = LocalDate.now().minusDays(90L),
                            periodInDays = 730L
                        )
                    ),
                )
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                } returns parentRecord

                val result = service.updateBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person,
                        member,
                        NullvsActionType.UPDATE,
                        familyTotvsCode = memberNullvsIntegrationRecord.externalId.substring(8, 14),
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.DEPENDENT,
                        parentBeneficiaryRelationType = TotvsRelationType.OTHER,
                        priceListing = null,
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        cpts = listOf(
                            TotvsMemberRequest.TotvsBeneficiary.CPT(
                                cid = "A009",
                                startedAt = LocalDate.now().minusDays(90L),
                                periodInDays = 730L
                            ),
                        ),
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    beneficiaryService.get(parentBeneficiary.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                }

                coVerifyNone {
                    memberPriceService.getByMemberId(memberId)
                    ansNumberCacheService.getByProductId(any())
                }
            }

        @Test
        fun `should create a NullvsMemberBatchRequest to holder`() =
            runBlocking {
                val person = person.copy(firstName = "Updated", tags = listOf("pitaya"))
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )
                val beneficiary = beneficiary.copy(
                    parentBeneficiary = null,
                    parentBeneficiaryRelationType = null,
                    companySubContractId = subcontract.id,
                )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns memberNullvsIntegrationRecord
                coEvery { personService.get(person.id) } returns person
                coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary
                coEvery { companyInfoCacheService.getByBeneficiary(beneficiary) } returns companyInfo
                coEvery { memberPriceService.getByMemberId(memberId) } returns memberPrice
                coEvery { memberCptsService.buildPersonCpts(person) } returns MemberCpt()

                val result = service.updateBeneficiaryTotvs(meta, member)

                assertThat(result).isSuccessWithData(
                    nullvsMemberBatchRequest(
                        person,
                        member,
                        NullvsActionType.UPDATE,
                        totvsContract = companyInfo.contractNumber,
                        totvsSubcontract = companyInfo.subcontractNumber,
                        userType = TotvsUser.HOLDER,
                        parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                        priceListing = null,
                        familyTotvsCode = totvsBeneficiaryRegistryTofamilyCode(memberNullvsIntegrationRecord.externalId).getOrNullIfNotFound(),
                        idTotvs = memberNullvsIntegrationRecord.externalId,
                        cnpj = company.cnpj,
                        ansNumber = null,
                    )
                )

                coVerifyOnce {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                }
                coVerifyNone {
                    memberPriceService.getByMemberId(memberId)
                    beneficiaryService.get(any())
                    ansNumberCacheService.getByProductId(any())
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the member totvs registry is not found`() =
            runBlocking {

                val log = TestModelFactory.buildNullvsIntegrationLog(internalId = memberId)
                val person = person.copy(firstName = "Updated")

                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns NotFoundException()

                coEvery {
                    nullvsIntegrationLogService.findByInternalId(member.id)
                } returns listOf(log)

                coEvery {
                    nullvsIntegrationLogService.checkToCreateErrorLog(any())
                } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {
                    val result = service.updateBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TotvsMemberNotFoundByInternalIdException::class)
                }

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                    nullvsIntegrationLogService.checkToCreateErrorLog(any())
                }
                coVerifyNone {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    beneficiaryService.get(parentBeneficiary.id)
                    ansNumberCacheService.getByProductId(productId)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                }
            }

        @Test
        fun `shouldn't create a NullvsMemberBatchRequest when the member totvs registry is not found and returns the TotvsMemberNotFoundByInternalIdException even when nullvs log service fails`() =
            runBlocking {
                val person = person.copy(firstName = "Updated")
                val member =
                    TestModelFactory.buildMember(
                        id = memberId,
                        personId = person.id,
                        productId = productId,
                        productType = ProductType.B2B
                    )

                coEvery {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                } returns NotFoundException()

                coEvery {
                    nullvsIntegrationLogService.findByInternalId(member.id)
                } returns Exception("")

                coEvery { nullvsIntegrationLogService.checkToCreateErrorLog(match { it.eventId == meta.eventId }) } returns true.success()

                withFeatureFlag(FeatureNamespace.NULLVS, "is_check_error_validation_enabled", true) {

                    val result = service.updateBeneficiaryTotvs(meta, member)

                    assertThat(result).isFailureOfType(TotvsMemberNotFoundByInternalIdException::class)
                }

                coVerifyOnce {
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        member.id,
                        InternalModelType.MEMBER,
                    )
                    nullvsIntegrationLogService.checkToCreateErrorLog(any())
                }
                coVerifyNone {
                    personService.get(person.id)
                    beneficiaryService.findByMemberId(member.id)
                    beneficiaryService.get(parentBeneficiary.id)
                    ansNumberCacheService.getByProductId(productId)
                    companyInfoCacheService.getByBeneficiary(beneficiary)
                    nullvsIntegrationRecordService.findByInternalIdAndModel(
                        parentBeneficiary.memberId,
                        InternalModelType.MEMBER,
                    )
                }
            }
    }

    @Nested
    inner class ReactivateBeneficiaryTotvs {

        private val member = TestModelFactory.buildMember(
            id = memberId,
            personId = person.id,
            productId = productId,
            productType = ProductType.B2C,
            status = MemberStatus.ACTIVE,
        )

        private val mlcEvent = TestModelFactory.buildMemberLifeCycleEvents(
            memberId = member.id,
            type = MemberLifecycleEventType.REACTIVATION,
        )

        @Test
        fun `should create a NullvsMemberReactivationBatchRequest`() = runBlocking {
            coEvery {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    memberId,
                    InternalModelType.MEMBER,
                )
            } returns memberNullvsIntegrationRecord
            coEvery {
                nullvsIntegrationRecordService.update(any())
            } returns memberNullvsIntegrationRecord.copy(canceledAt = null)
            coEvery {
                memberLifeCycleEventsService.findByMemberIdAndTypes(
                    member.id,
                    listOf(MemberLifecycleEventType.REACTIVATION)
                )
            } returns listOf(mlcEvent)


            val result = service.reactivateMemberTotvs(meta, member)

            assertThat(result).isSuccess()

            coVerifyOnce {
                nullvsIntegrationRecordService.findByInternalIdAndModel(
                    memberId,
                    InternalModelType.MEMBER,
                )

                nullvsIntegrationRecordService.update(any())

                memberLifeCycleEventsService.findByMemberIdAndTypes(
                    member.id,
                    listOf(MemberLifecycleEventType.REACTIVATION)
                )
            }
        }
    }
}
