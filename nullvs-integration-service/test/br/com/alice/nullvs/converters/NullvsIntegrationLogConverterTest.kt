package br.com.alice.nullvs.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.data.layer.models.NullvsIntegrationLogModel
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class NullvsIntegrationLogConverterTest {

    private val nullvsIntegrationLog = NullvsIntegrationLog(
        eventId = RangeUUID.generate(),
        eventName = "eventName",
        integrationEventName = "integrationEventName",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.MEMBER,
        externalModelName = ExternalModelType.BENEFICIARY,
        batchId = "batchId",
        idSoc = "idSoc",
        batchType = BatchType.CREATE,
        payloadSequenceId = 1,
        description = "description",
        status = LogStatus.PENDING,
        hash = "hash",
        groupId = RangeUUID.generate()
    )

    private val nullvsIntegrationLogModel = NullvsIntegrationLogModel(
        id = nullvsIntegrationLog.id,
        version = nullvsIntegrationLog.version,
        createdAt = nullvsIntegrationLog.createdAt,
        updatedAt = nullvsIntegrationLog.updatedAt,
        eventId = nullvsIntegrationLog.eventId,
        eventName = "eventName",
        integrationEventName = "integrationEventName",
        internalId = nullvsIntegrationLog.internalId,
        internalModelName = InternalModelType.MEMBER,
        externalModelName = ExternalModelType.BENEFICIARY,
        batchId = "batchId",
        idSoc = "idSoc",
        batchType = BatchType.CREATE,
        payloadSequenceId = 1,
        description = "description",
        status = LogStatus.PENDING,
        hash = "hash",
        groupId = nullvsIntegrationLog.groupId
    )

    @Test
    fun testToTransport() {
        assertThat(nullvsIntegrationLogModel.toTransport()).isEqualTo(nullvsIntegrationLog)
    }

    @Test
    fun testToModel() {
        assertThat(nullvsIntegrationLog.toModel()).isEqualTo(nullvsIntegrationLogModel)
    }

}
