package br.com.alice.nullvs.models

import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test

class InternalTotvsModelsTest {

    companion object {
        @JvmStatic
        fun otherRelationType() = listOf(
            ParentBeneficiaryRelationType.GRANDMOTHER_GRANDFATHER,
            ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW,
            ParentBeneficiaryRelationType.STEP_MOTHER_FATHER,
            ParentBeneficiaryRelationType.GRANDCHILD,
            ParentBeneficiaryRelationType.GREATGRANDCHILD,
            ParentBeneficiaryRelationType.NIECE_NEPHEW,
        )
    }

    @Nested
    inner class RequestTotvsBeneficiaryRegistryTofamilyCode {
        @Test
        fun `#TotvsMaritalStatus getDefault should get single`() {
            val result = TotvsMaritalStatus.getDefault()

            assertThat(result).isEqualTo((TotvsMaritalStatus.SINGLE))
        }

        @Test
        fun `#TotvsDegreeOfKinship getByParentBeneficiaryRelationType should works for male child`() {
            val result =
                TotvsRelationType.getByParentBeneficiaryRelationType(ParentBeneficiaryRelationType.CHILD, Sex.MALE)

            assertThat(result).isEqualTo((TotvsRelationType.SON))
        }

        @Test
        fun `#TotvsDegreeOfKinship getByParentBeneficiaryRelationType should works for female child`() {
            val result =
                TotvsRelationType.getByParentBeneficiaryRelationType(ParentBeneficiaryRelationType.CHILD, Sex.FEMALE)

            assertThat(result).isEqualTo((TotvsRelationType.DAUGHTER))
        }

        @ParameterizedTest(name = "TotvsDegreeOfKinship getByParentBeneficiaryRelationType should works for not mapped code {0}")
        @MethodSource("br.com.alice.nullvs.models.InternalTotvsModelsTest#otherRelationType")
        fun `#TotvsDegreeOfKinship getByParentBeneficiaryRelationType should works for not mapped codes`(type: ParentBeneficiaryRelationType) {
            val result = TotvsRelationType.getByParentBeneficiaryRelationType(type, Sex.FEMALE)
            assertThat(result).isEqualTo((TotvsRelationType.OTHER))
        }

    }

}
