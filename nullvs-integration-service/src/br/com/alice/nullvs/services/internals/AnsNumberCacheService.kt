package br.com.alice.nullvs.services.internals

import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.models.Product
import br.com.alice.nullvs.logics.CacheHelper
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.map
import redis.clients.jedis.exceptions.JedisException
import java.util.UUID

class AnsNumberCacheService(
    private val cache: GenericCache,
    private val productService: ProductService
) {
    private suspend fun wrappedGetProduct(productId: UUID) = productService.getProduct(
        productId,
        ProductService.FindOptions(withPriceListing = false, withBundles = false),
    ).then {
        logger.info("product found", "product_id" to it.id, "ans_number" to it.ansNumber?.onlyDigits())
    }.map { it.ansNumber }.get() ?: ""

    suspend fun getByProductId(productId: UUID): String? = try {
        logger.info("AnsNumberCachedService::getAnsNumber", "product_id" to productId)

        cache.get(
            CacheHelper.getAnsNumberForProductIdKey(productId),
            String::class,
            expirationTime = CacheHelper.TWO_DAYS_EXPIRATION_IN_SECONDS,
        ) {
            wrappedGetProduct(productId)
                .also {
                    logger.info(
                        "The ans number was cached",
                        "product_id" to productId,
                    )
                }
        }
    } catch (err: JedisException) {
        logger.error(
            "Something wrong happend when trying to cache the ans number",
            "product_id" to productId,
            "message" to err.message,
            err,
        )

        wrappedGetProduct(productId)
    }.nullIfBlank()


    suspend fun cacheOrClear(product: Product) {
        logger.info("AnsNumberCachedService::cacheOrClearAnsNumber", "product_id" to product.id)

        val key = CacheHelper.getAnsNumberForProductIdKey(product.id)

        product.ansNumber.nullIfBlank()?.let {
            cache.put(
                key,
                String::class,
                expirationTime = CacheHelper.TWO_DAYS_EXPIRATION_IN_SECONDS
            ) {
                it
            }.also {
                logger.info("The ans number was cached")
            }
        } ?: cache.invalidateKey(
            key
        ).also {
            logger.info("The ans number cache was invalidated")
        }
    }
}
