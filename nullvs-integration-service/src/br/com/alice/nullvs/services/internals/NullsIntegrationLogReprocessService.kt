package br.com.alice.nullvs.services.internals

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.nullvs.client.exceptions.TotvsMemberGetException
import br.com.alice.nullvs.clients.TotvsMemberClient
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.toModel
import br.com.alice.nullvs.converters.toTransport
import br.com.alice.nullvs.events.NullvsSyncCompanyContractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.nullvs.models.CancelWaitingLogsRequest
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.map
import java.time.LocalDateTime
import java.util.UUID

class NullsIntegrationLogReprocessService(
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
    private val memberService: MemberService,
    private val kafkaProducerService: KafkaProducerService,
    private val totvsMemberClient: TotvsMemberClient,
    private val subContractService: CompanySubContractService,
    private val contractService: CompanyContractService
) {

    suspend fun processWaitingLogItems() {

        logger.info(
            "NullsIntegrationLogReprocessService:: starting processWaitingLogItems",
        )

        findWaitingLogs().then { waitingProcessItems ->

            logger.info(
                "NullsIntegrationLogReprocessService:: waitingProcessItems",
                "waitingProcessItems" to waitingProcessItems.size
            )

            waitingProcessItems.groupBy { Pair(it.internalId, it.internalModelName) }
                .map { (ids, list) ->


                    logger.info(
                        "RecurrentController::processWaitingLogItems - prepare validation",
                        "internal_id" to "ID-${ids.first}-ID",
                        "internal_model_name",
                        ids.second,
                        "total_items",
                        list.size
                    )

                    val orderedLogs = list.sortedByDescending { it.createdAt }
                    val updateOnly = list.all { it.batchType == BatchType.UPDATE }
                    val lastVersion = orderedLogs.first()
                    val currentLogs = nullvsIntegrationLogService.findByInternalIdAndModel(
                        lastVersion.internalId,
                        lastVersion.internalModelName
                    ).get()
                    val hasError =
                        currentLogs.firstOrNull { log -> (log.status == LogStatus.FAILURE || log.status == LogStatus.TOTVS_NOT_CALLED) && log.batchType == BatchType.CREATE }
                    val hasCreated =
                        currentLogs.firstOrNull { log -> log.status == LogStatus.FINISHED && log.batchType == BatchType.CREATE }
                    val hasPending =
                        currentLogs.firstOrNull { log -> log.status == LogStatus.PENDING && log.batchType == BatchType.CREATE }
                    val hasOlderPending = hasPending?.let {
                        hasPending.createdAt <= LocalDateTime.now().minusDays(7)
                    } ?: false
                    var canceledLogs =
                        list.map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }.map { it.toModel() }
                    if (hasError != null) {
                        canceledLogs = canceledLogs + listOf(hasError.copy(status = LogStatus.SYNC_CANCELLED))
                    }
                    if (hasOlderPending) {
                        canceledLogs = canceledLogs + listOf(hasPending!!.copy(status = LogStatus.SYNC_CANCELLED))
                    }

                    val executeUpdateLogs = when (lastVersion.internalModelName) {
                        InternalModelType.MEMBER -> {
                            logger.info(
                                "RecurrentController::processWaitingLogItems - verify upsert to member",
                                "membership_id" to "ID-${lastVersion.internalId}-ID"
                            )
                            upsertBeneficiaryTotvs(
                                id = lastVersion.internalId,
                                hasError = hasError?.let { true } ?: false,
                                hasCreated = hasCreated?.let { true } ?: false,
                                hasPending = hasPending?.let { true } ?: false,
                                updateOnly = updateOnly,
                                hasOlderPending = hasOlderPending
                            )
                        }

                        InternalModelType.CONTRACT -> {
                            upsertContractTotvs(
                                id = lastVersion.internalId,
                                hasError = hasError?.let { true } ?: false,
                                hasCreated = hasCreated?.let { true } ?: false,
                                hasPending = hasPending?.let { true } ?: false,
                                updateOnly = updateOnly,
                                hasOlderPending = hasOlderPending
                            )
                        }

                        InternalModelType.SUBCONTRACT -> {
                            upsertSubcontractTotvs(
                                id = lastVersion.internalId,
                                hasError = hasError?.let { true } ?: false,
                                hasCreated = hasCreated?.let { true } ?: false,
                                hasPending = hasPending?.let { true } ?: false,
                                updateOnly = updateOnly,
                                hasOlderPending = hasOlderPending
                            )
                        }

                        else -> {
                            logger.info("RecurrentController::processWaitingLogItems - model not mapped yet")
                            false
                        }
                    }

                    if (executeUpdateLogs) {

                        logger.info(
                            "NullsIntegrationLogReprocessService:: set older logs to SYNC_CANCELLED",
                            "group_id" to (canceledLogs.firstOrNull()?.internalId ?: "")
                        )

                        nullvsIntegrationLogService.updateList(canceledLogs.map { it.toTransport() })
                    }
                }
        }

        logger.info(
            "NullsIntegrationLogReprocessService:: ending processWaitingLogItems",
        )

    }


    private suspend fun findWaitingLogs() = coResultOf<List<NullvsIntegrationLog>, Throwable> {

        val waitingLogs = mutableListOf<NullvsIntegrationLog>()

        var offset = 0
        val limit = 500
        var page = 0

        do {
            val fetched =
                nullvsIntegrationLogService.findByStatusIgnoringDependentCasesPaginated(
                    LogStatus.WAITING,
                    offset,
                    limit,
                )
                    .then { waitingLogs.addAll(it) }.get()

            offset += limit
            page += page
        } while (fetched.size == limit)

        waitingLogs
    }

    suspend fun cancelWaitingLogs(params: CancelWaitingLogsRequest) {
        nullvsIntegrationLogService.findByStatusAndInternalIdsAndModel(
            LogStatus.WAITING, params.ids, params.internalModelName
        ).then { waitingProcessItems ->
            waitingProcessItems.chunked(50)
                .pmap { nullvsIntegrationLogService.updateList(it.map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }) }
        }
    }

    suspend fun cancelAllWaitingLogs() {
        findWaitingLogs().then { waitingProcessItems ->
            waitingProcessItems.chunked(50)
                .pmap { nullvsIntegrationLogService.updateList(it.map { log -> log.copy(status = LogStatus.SYNC_CANCELLED) }) }
        }
    }

    private suspend fun checkExistsActiveMembership(internalId: UUID) =
        memberService.get(internalId).fold({ it.active }, { if (it is NotFoundException) false else throw it })

    private suspend fun checkExistsTotvsMember(internalId: UUID) =
        totvsMemberClient.getMember(internalId)
            .fold({ true }, { if (it is TotvsMemberGetException) false else throw it })

    private suspend fun checkSubContractHasTotvsId(internalId: UUID) =
        subContractService.get(internalId)
            .fold({ it.externalId != null }, { if (it is NotFoundException) false else throw it })

    private suspend fun checkContractHasTotvsId(internalId: UUID) =
        contractService.get(internalId)
            .fold({ it.externalId != null }, { if (it is NotFoundException) false else throw it })

    private suspend fun upsertBeneficiaryTotvs(
        id: UUID,
        hasError: Boolean,
        hasCreated: Boolean,
        hasPending: Boolean,
        updateOnly: Boolean,
        hasOlderPending: Boolean
    ): Boolean {

        logger.info(
            "upsertBeneficiaryTotvs",
            "membership_id" to "ID-${id}-ID",
            "has_error" to hasError,
            "has_created" to hasCreated,
            "has_pending" to hasPending,
            "update_only" to updateOnly,
            "has_older_pending" to hasOlderPending
        )

        var memberActive = false
        var totvsMemberExists = false

        if (hasPending && !hasOlderPending)
            return false

        if (hasOlderPending || updateOnly) {

            memberActive = checkExistsActiveMembership(id)
            if (memberActive) {
                totvsMemberExists = checkExistsTotvsMember(id)
            }
        }

        return if (hasError && !hasCreated || hasOlderPending && (memberActive && !totvsMemberExists) || (updateOnly && !totvsMemberExists && memberActive)
        ) {
            createBeneficiaryTotvs(id)
            logger.info(
                "upsertBeneficiaryTotvs:: created event",
                "membership_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "member_active" to memberActive,
                "totvs_member_exists" to totvsMemberExists
            )
            true
        } else if (hasCreated || (updateOnly && !hasOlderPending && memberActive) || hasOlderPending && memberActive
        ) {
            logger.info(
                "upsertBeneficiaryTotvs:: update event",
                "membership_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "member_active" to memberActive,
                "totvs_member_exists" to totvsMemberExists
            )
            updateBeneficiaryTotvs(id)
            true
        } else if (updateOnly && !hasOlderPending && !memberActive) {
            logger.info(
                "upsertBeneficiaryTotvs:: skip event - member is not active yet",
                "membership_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "member_active" to memberActive,
                "totvs_member_exists" to totvsMemberExists
            )
            true
        } else {
            logger.info(
                "upsertBeneficiaryTotvs:: skip event",
                "membership_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "member_active" to memberActive,
                "totvs_member_exists" to totvsMemberExists
            )
            hasOlderPending && !checkExistsActiveMembership(id)
        }
    }

    private suspend fun upsertSubcontractTotvs(
        id: UUID,
        hasError: Boolean,
        hasCreated: Boolean,
        hasPending: Boolean,
        updateOnly: Boolean,
        hasOlderPending: Boolean
    ): Boolean {

        logger.info(
            "upsertSubcontractTotvs",
            "sub_contract_id" to "ID-${id}-ID",
            "has_error" to hasError,
            "has_created" to hasCreated,
            "has_pending" to hasPending,
            "update_only" to updateOnly,
            "has_older_pending" to hasOlderPending
        )

        var subContractHasTotvsId = checkSubContractHasTotvsId(id)

        if (hasPending && !hasOlderPending)
            return false

        return if (!subContractHasTotvsId && (!hasCreated || hasError)) {
            logger.info(
                "upsertSubcontractTotvs:: created event",
                "sub_contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "sub_contract_has_totvs_id" to subContractHasTotvsId,
            )
            createSubContractTotvs(id)
            true
        } else if (hasCreated || (updateOnly && !hasOlderPending) || hasOlderPending) {
            logger.info(
                "upsertSubcontractTotvs:: update event",
                "sub_contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "sub_contract_has_totvs_id" to subContractHasTotvsId,
            )
            updateSubContractTotvs(id)
            true
        } else {
            logger.info(
                "upsertSubcontractTotvs:: skip event",
                "sub_contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "sub_contract_has_totvs_id" to subContractHasTotvsId,
            )
            false
        }

    }

    private suspend fun upsertContractTotvs(
        id: UUID,
        hasError: Boolean,
        hasCreated: Boolean,
        hasPending: Boolean,
        updateOnly: Boolean,
        hasOlderPending: Boolean
    ): Boolean {

        logger.info(
            "upsertContractTotvs",
            "contract_id" to "ID-${id}-ID",
            "has_error" to hasError,
            "has_created" to hasCreated,
            "has_pending" to hasPending,
            "update_only" to updateOnly,
            "has_older_pending" to hasOlderPending
        )

        var contractHasTotvsId = checkContractHasTotvsId(id)

        if (hasPending && !hasOlderPending)
            return false

        return if (!contractHasTotvsId && (!hasCreated || hasError)) {
            logger.info(
                "upsertContractTotvs:: created event",
                "contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "contract_has_totvs_id" to contractHasTotvsId,
            )
            createContractTotvs(id)
            true
        } else if (hasCreated || (updateOnly && !hasOlderPending) || hasOlderPending) {
            logger.info(
                "upsertContractTotvs:: update event",
                "contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "contract_has_totvs_id" to contractHasTotvsId,
            )
            updateContractTotvs(id)
            true
        } else {
            logger.info(
                "upsertContractTotvs:: skip event",
                "contract_id" to "ID-${id}-ID",
                "has_error" to hasError,
                "has_created" to hasCreated,
                "has_pending" to hasPending,
                "update_only" to updateOnly,
                "has_older_pending" to hasOlderPending,
                "contract_has_totvs_id" to contractHasTotvsId,
            )
            false
        }

    }

    private suspend fun createBeneficiaryTotvs(id: UUID) {
        memberService.get(id).map { member ->
            kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.CREATE))
        }
    }

    private suspend fun updateBeneficiaryTotvs(id: UUID) {
        memberService.get(id).map { member ->
            kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, NullvsActionType.UPDATE))
        }
    }

    private suspend fun createSubContractTotvs(id: UUID) {
        subContractService.get(id).map {
            kafkaProducerService.produce(NullvsSyncCompanySubcontractRequestEvent(it, NullvsActionType.CREATE))
        }
    }

    private suspend fun updateSubContractTotvs(id: UUID) {
        subContractService.get(id).map {
            kafkaProducerService.produce(NullvsSyncCompanySubcontractRequestEvent(it, NullvsActionType.UPDATE))
        }
    }

    private suspend fun createContractTotvs(id: UUID) {
        contractService.get(id).map {
            kafkaProducerService.produce(NullvsSyncCompanyContractRequestEvent(it, NullvsActionType.CREATE))
        }
    }

    private suspend fun updateContractTotvs(id: UUID) {
        contractService.get(id).map {
            kafkaProducerService.produce(NullvsSyncCompanyContractRequestEvent(it, NullvsActionType.UPDATE))
        }
    }
}
