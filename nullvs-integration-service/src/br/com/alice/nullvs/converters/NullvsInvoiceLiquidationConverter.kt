package br.com.alice.nullvs.converters

import br.com.alice.common.core.extensions.money
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.ProductType
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toMeta
import br.com.alice.nullvs.logics.InvoiceBatchLogic.toTotvsInvoiceIndex
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceData


object NullvsInvoiceLiquidationConverter {

    fun InvoiceLiquidation.toNullvsInvoiceRequestEvent(
        event: NotificationEvent<*>,
        invoicePayment: InvoicePayment,
        clientCode: String,
        actionType: NullvsActionType,
    ) = this.toNullvsInvoiceRequest(
        event.toMeta(this.id, InternalModelType.INVOICE_LIQUIDATION),
        actionType,
        invoicePayment.reason?.let { if (it.isB2B()) ProductType.B2B else ProductType.B2C }
            ?: ProductType.B2C,
        invoicePayment,
        clientCode,
    )

    private fun InvoiceLiquidation.toNullvsInvoiceRequest(
        meta: Meta,
        actionType: NullvsActionType = NullvsActionType.UPDATE_PAYMENT,
        productType: ProductType,
        invoicePayment: InvoicePayment,
        clientCode: String,
    ): NullvsInvoiceBatchRequest {
        val titleIndexFields = externalId.toTotvsInvoiceIndex()
        val amount = invoicePayment.amount
        val amountPaid = invoicePayment.amountPaid ?: invoicePayment.amount
        val zero = 0.money
        val fine = invoicePayment.fine?.let { if (amountPaid <= amount) zero else it } ?: zero
        val interest = invoicePayment.interest?.let { if (amountPaid <= amount) zero else it } ?: zero
        val totalPaid = invoicePayment.amountPaid?.let {
            val total = it - fine - interest

            if (total > amount) {
                amount
            } else {
                total
            }
        } ?: invoicePayment.amount

        return NullvsInvoiceBatchRequest(
            meta = meta,
            action = actionType,
            invoiceCreationData = NullvsInvoiceData(
                titlePrefix = titleIndexFields.prefix,
                titleNumber = titleIndexFields.number,
                titleInstallment = titleIndexFields.installment,
                type = titleIndexFields.type,
                dueDate = this.dueDate,
                amount = totalPaid.toDouble(),
                financialHistory = productType.getFinancialHistory(),
                discount = 0.0,
                status = this.status.toNullvsInvoiceDataStatus(),
                clientCode = clientCode,
                emittedDate = this.dueDate,
                fine = fine.toDouble(),
                interest = interest.toDouble(),
                natureCode = productType.getNatureCode(),
                monetaryCorrection = 0.0,
                paidAt = invoicePayment.approvedAt?.toSaoPauloTimeZone()?.toLocalDate(),
                externalPaymentId = invoicePayment.id.toString(),
            )
        )
    }

    private fun InvoiceLiquidationStatus.toNullvsInvoiceDataStatus() = when (this) {
        InvoiceLiquidationStatus.PROCESSING, InvoiceLiquidationStatus.WAITING_PAYMENT, InvoiceLiquidationStatus.PROCESSED -> NullvsInvoiceData.Status.OPEN
        InvoiceLiquidationStatus.PAID, InvoiceLiquidationStatus.PARTIALLY_PAID -> NullvsInvoiceData.Status.PAID
        InvoiceLiquidationStatus.CANCELED -> NullvsInvoiceData.Status.CANCELED
    }

    private fun ProductType.getFinancialHistory() =
        if (this == ProductType.B2B) "RECEITAS B2B - ALICE" else "RECEITAS B2C - ALICE"

    private fun ProductType.getNatureCode() = if (this == ProductType.B2B) "20009" else "20010"
}



