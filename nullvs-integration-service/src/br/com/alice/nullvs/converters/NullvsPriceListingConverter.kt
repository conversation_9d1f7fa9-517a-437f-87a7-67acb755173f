package br.com.alice.nullvs.converters

import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.PriceListingItem
import br.com.alice.nullvs.common.fromTotvsFormatDateDMY
import br.com.alice.nullvs.common.fromTotvsFormatDateYMD
import br.com.alice.nullvs.common.fromTotvsFormatOptionalDateDMY
import br.com.alice.nullvs.common.fromTotvsFormatOptionalDateYMD
import br.com.alice.nullvs.events.NullvsPriceListingWebhookReceived
import br.com.alice.nullvs.models.NullvsMemberPriceListingInsert
import br.com.alice.nullvs.models.NullvsPriceListingItem
import br.com.alice.nullvs.models.NullvsSubContractPriceListingInsert
import br.com.alice.nullvs.models.TotvsBeneficiaryTablePriceResponse
import br.com.alice.nullvs.models.TotvsSubcontractTablePriceResponse
import java.util.UUID

object NullvsPriceListingConverter {

    fun NullvsPriceListingWebhookReceived.Payload.toNullvsSubContractPriceListingInsert(
        subContract: CompanySubContract
    ): NullvsSubContractPriceListingInsert {
        return NullvsSubContractPriceListingInsert(
            subContract = subContract,
            ansNumber = ansProductCode.trim(),
            readjustBaseDate = readjustBaseDate?.fromTotvsFormatOptionalDateYMD(),
            startDate = this.startDate.fromTotvsFormatDateYMD()!!,
            previousStartDate = this.previousStartDate?.fromTotvsFormatOptionalDateYMD(),
            priceListing = this.prices.map { it.toNullvsPriceListingItem() }
        )
    }

    fun NullvsPriceListingWebhookReceived.PriceRangePayload.toNullvsPriceListingItem() =
        NullvsPriceListingItem(
            minAge = this.minAge,
            maxAge = this.maxAge,
            value = this.value,
            appliedReadjust = this.appliedReadjust,
        )

    fun TotvsSubcontractTablePriceResponse.SubcontractTablePricePayload.toNullvsSubContractPriceListingInsert(subContract: CompanySubContract) =
        NullvsSubContractPriceListingInsert(
            subContract = subContract,
            ansNumber = this.ansProductCode.trim(),
            readjustBaseDate = null,
            startDate = this.startDate.fromTotvsFormatDateDMY(),
            previousStartDate = this.previousStartDate?.fromTotvsFormatDateDMY(),
            priceListing = this.tablePrice.map { it.toNullvsPriceListingItem() }
        )

    fun TotvsSubcontractTablePriceResponse.SubcontractTablePricePayload.TablePrice.toNullvsPriceListingItem() =
        NullvsPriceListingItem(
            minAge = this.minAge,
            maxAge = this.maxAge,
            value = this.value,
        )

    fun NullvsPriceListingItem.toPriceListingItem() =
        PriceListingItem(
            minAge = this.minAge,
            maxAge = this.maxAge,
            amount = this.value,
            priceAdjustment = this.appliedReadjust,
        )

    fun NullvsPriceListingWebhookReceived.Payload.toNullvsMemberPriceListingInsert(memberId: UUID) =
        NullvsMemberPriceListingInsert(
            memberId = memberId,
            ansNumber = this.ansProductCode.trim(),
            readjustBaseDate = this.readjustBaseDate?.fromTotvsFormatDateYMD(),
            previousStartDate = this.previousStartDate?.fromTotvsFormatDateYMD(),
            startDate = this.startDate.fromTotvsFormatDateYMD()!!,
            priceListing = this.prices.map { it.toNullvsPriceListingItem() },
        )

    fun TotvsBeneficiaryTablePriceResponse.BeneficiaryTablePricePayload.toNullvsMemberPriceListingInsert(memberId: UUID) =
        NullvsMemberPriceListingInsert(
            memberId = memberId,
            ansNumber = this.ansProductCode.trim(),
            priceListing = this.tablePrice.map { it.toNullvsPriceListingItem() },
            startDate = this.startDate.fromTotvsFormatDateDMY(),
            readjustBaseDate = null,
            previousStartDate = this.previousStartDate?.fromTotvsFormatOptionalDateDMY(),
        )

    fun TotvsBeneficiaryTablePriceResponse.BeneficiaryTablePricePayload.BeneficiaryTablePrice.toNullvsPriceListingItem() =
        NullvsPriceListingItem(
            minAge = this.minAge,
            maxAge = this.maxAge,
            value = this.value,
        )

}
