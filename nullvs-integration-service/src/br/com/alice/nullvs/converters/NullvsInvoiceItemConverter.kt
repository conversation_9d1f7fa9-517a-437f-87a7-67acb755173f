package br.com.alice.nullvs.converters

import br.com.alice.nullvs.models.TotvsInvoiceItemCanceledRequest
import br.com.alice.nullvs.models.TotvsInvoiceItemCreatedRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceItemCanceled
import br.com.alice.nullvs.models.payment.NullvsInvoiceItemCreated
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID

object NullvsInvoiceItemConverter {

    fun  NullvsInvoiceItemCreated.InvoiceItemCreated.convertToTotvsInvoiceItemCreatedRequest(eventId: UUID): TotvsInvoiceItemCreatedRequest {
        val invoiceItemCreated =
            TotvsInvoiceItemCreatedRequest.Payload(
                nivelAdicional = this.level.description,
                cpfCnpj = this.cpfCnpj,
                idSubcontrato = this.subcontractId?.toString(),
                mesAdicional = this.month.toString(),
                anoAdicional = this.year.toString(),
                codigoLancamento = this.code,
                tipoValor = this.valueType.description,
                valorLancamento = this.value.toString(),
                qtdParcelas = this.numberOfInstallments,
                observacao = this.observation,
                idAddBilling = this.idAddBilling.toString(),
            )

        return TotvsInvoiceItemCreatedRequest(
            total = 1,
            idsoc = eventId.toString(),
            tipo = "cadastro",
            entidade = "adicionais",
            acao = "I",
            data = LocalDate.now().format(DateTimeFormatter.ofPattern("ddMMyyyy")),
            usuario = "Nullvs",
            payload = listOf(invoiceItemCreated)
        )
    }

    fun  NullvsInvoiceItemCanceled.InvoiceItemCanceled.convertToTotvsInvoiceItemCanceledRequest(eventId: UUID): TotvsInvoiceItemCanceledRequest {
        val invoiceItemCanceled =
            TotvsInvoiceItemCanceledRequest.Payload(
                idAddBilling = this.idAddBilling.toString(),
                idTotvs = this.idTotvs,
                cpf = this.cpf,
                nivelAdicional = this.level.toString(),
            )

        return TotvsInvoiceItemCanceledRequest(
            total = 1,
            idsoc = eventId.toString(),
            tipo = "cadastro",
            entidade = "adicionais",
            acao = "U",
            data = LocalDate.now().format(DateTimeFormatter.ofPattern("ddMMyyyy")),
            usuario = "Nullvs",
            payload = listOf(invoiceItemCanceled)
        )
    }
}
