package br.com.alice.nullvs.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.data.layer.models.NullvsIntegrationLogModel

object NullvsIntegrationLogConverter : Converter<NullvsIntegrationLogModel, NullvsIntegrationLog>(
    NullvsIntegrationLogModel::class,
    NullvsIntegrationLog::class,
)

fun NullvsIntegrationLog.toModel() = NullvsIntegrationLogConverter.unconvert(this)
fun NullvsIntegrationLogModel.toTransport() = NullvsIntegrationLogConverter.convert(this)
