package br.com.alice.nullvs.clients

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.nullvs.client.exceptions.TotvsCompanyContractGetException
import br.com.alice.nullvs.client.exceptions.TotvsCompanySubcontractGetException
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.encodeBase64
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toNullvsCompanyContractBatchResponse
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toNullvsCompanySubContractBatchResponse
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toTotvsCompanyContractBatchRequest
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toTotvsCompanySubContractBatchRequest
import br.com.alice.nullvs.exceptions.TotvsContractClientPostException
import br.com.alice.nullvs.exceptions.TotvsPostBatchDuplicationException
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.nullvs.models.company.NullvsCompanyContractBatchRequest
import br.com.alice.nullvs.models.company.NullvsCompanyContractBatchResponse
import br.com.alice.nullvs.models.company.NullvsCompanySubContractBatchRequest
import br.com.alice.nullvs.models.company.NullvsCompanySubContractBatchResponse
import br.com.alice.nullvs.models.company.TotvsCompanyContractBatchResponse
import br.com.alice.nullvs.models.company.TotvsCompanySubContractBatchResponse
import br.com.alice.nullvs.models.company.TotvsContractGetWebServiceResponse
import br.com.alice.nullvs.models.company.TotvsSubcontractGetWebServiceResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import java.util.UUID


class TotvsCompanyClient(
    private val baseUrl: String,
    private val secretKey: String,
    private val client: HttpClient,
) {
    private suspend fun <T> handleErrorsOnPost(
        entityName: String,
        methodName: String,
        action: NullvsActionType,
        metricMethod: TotvsIntegrationMetric.Method,
        block: suspend () -> Result<T, Throwable>,
    ): Result<T, Throwable> = try {
        block()
    } catch (e: ClientRequestException) {
        logger.error("TotvsClient::$methodName - post $entityName request to totvs failed", e)

        TotvsIntegrationMetric.countTotvsIntegration(metricMethod, TotvsIntegrationMetric.Status.FAILURE)

        if (e.message.lowercase().contains("o lote de id lote") && e.message.lowercase()
                .contains("já existe no protheus a partir do dia")
        ) {
            TotvsPostBatchDuplicationException(action.toString(), entityName).failure()
        } else {
            TotvsContractClientPostException(action, e).failure()
        }
    } catch (e: Exception) {
        logger.error("TotvsClient::$methodName - post request to totvs failed", e)

        TotvsIntegrationMetric.countTotvsIntegration(metricMethod, TotvsIntegrationMetric.Status.FAILURE)
        TotvsContractClientPostException(action, e).failure()
    }

    suspend fun postContract(request: NullvsCompanyContractBatchRequest): Result<NullvsCompanyContractBatchResponse, Throwable> {
        logger.info("TotvsMemberClient::postContract - starting post contract request to totvs")
        return handleErrorsOnPost("contract", "postContract", request.action, TotvsIntegrationMetric.Method.CONTRACT) {
            val totvsCompanyContractBatchRequest = request.toTotvsCompanyContractBatchRequest().also {
                logger.info(
                    "Request",
                    "url" to "$baseUrl/rest/UFINA100/",
                    "body" to encodeBase64(gson.toJson(it)),
                    "request" to encodeBase64(it.toString())
                )
            }

            val responseString = client.post("$baseUrl/rest/UFINA100/") {
                headers {
                    append("Authorization", "Basic $secretKey")
                    append("tenantID", "01,01")
                }
                contentType(ContentType.Application.Json)
                setBody(totvsCompanyContractBatchRequest)
            }.bodyAsText()

            logger.info("totvs response", "response" to encodeBase64(responseString))

            val response: TotvsCompanyContractBatchResponse = gson.fromJson(responseString)

            TotvsIntegrationMetric.countTotvsIntegration(
                TotvsIntegrationMetric.Method.CONTRACT,
                TotvsIntegrationMetric.Status.SUCCESS
            )
            response.toNullvsCompanyContractBatchResponse(request.meta).success()
        }
    }

    suspend fun getContract(companyContractId: UUID): Result<TotvsContractGetWebServiceResponse, Throwable> {
        logger.info(
            "TotvsCompanyClient::getContract",
            "companyContractId" to companyContractId,
        )
        return try {
            val responseString = client.get("$baseUrl/rest/UPLSB001/${companyContractId}/") {
                headers {
                    append(HttpHeaders.Authorization, "Basic $secretKey")
                }
                contentType(ContentType.Application.Json)
            }.bodyAsText()
            val response: TotvsContractGetWebServiceResponse = gson.fromJson(responseString)
            response.let { it.copy(internalId = it.internalId.trim()) }.success()
        } catch (e: Exception) {
            logger.error("TotvsCompanyClient::getContract", e)
            TotvsCompanyContractGetException(companyContractId).failure()
        }
    }

    suspend fun getSubcontract(companySubcontractId: UUID): Result<TotvsSubcontractGetWebServiceResponse, Throwable> {
        logger.info(
            "TotvsCompanyClient::getSubcontract",
            "companySubcontractId" to companySubcontractId,
        )

        return try {
            val responseString = client.get("$baseUrl/rest/UPLSB003/${companySubcontractId}/") {
                headers {
                    append(HttpHeaders.Authorization, "Basic $secretKey")
                }
                contentType(ContentType.Application.Json)
            }.bodyAsText()
            val response: TotvsSubcontractGetWebServiceResponse = gson.fromJson(responseString)
            response.let { it.copy(internalId = it.internalId.trim()) }.success()
        } catch (e: Exception) {
            logger.error("TotvsCompanyClient::getSubcontract", e)
            TotvsCompanySubcontractGetException(companySubcontractId).failure()
        }
    }

    suspend fun postSubContract(request: NullvsCompanySubContractBatchRequest): Result<NullvsCompanySubContractBatchResponse, Throwable> {
        logger.info("TotvsMemberClient::postSubContract - starting post contract request to totvs")
        return handleErrorsOnPost(
            "subcontract",
            "postSubContract",
            request.action,
            TotvsIntegrationMetric.Method.SUBCONTRACT
        ) {
            val totvsCompanyContractBatchRequest = request.toTotvsCompanySubContractBatchRequest().also {
                logger.info(
                    "Request",
                    "url" to "$baseUrl/rest/UFINA100/",
                    "body" to encodeBase64(gson.toJson(it)),
                    "request" to encodeBase64(it.toString())
                )
            }

            val responseString = client.post("$baseUrl/rest/UFINA100/") {
                headers {
                    append("Authorization", "Basic $secretKey")
                    append("tenantID", "01,01")
                }
                contentType(ContentType.Application.Json)
                setBody(totvsCompanyContractBatchRequest)
            }.bodyAsText()

            logger.info("totvs response", "response" to encodeBase64(responseString))

            val response: TotvsCompanySubContractBatchResponse = gson.fromJson(responseString)

            TotvsIntegrationMetric.countTotvsIntegration(
                TotvsIntegrationMetric.Method.SUBCONTRACT,
                TotvsIntegrationMetric.Status.SUCCESS
            )
            response.toNullvsCompanySubContractBatchResponse(request.meta).success()
        }
    }
}
