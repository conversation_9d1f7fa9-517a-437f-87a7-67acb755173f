package br.com.alice.nullvs.consumers

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CompanyContractStatus
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.NullvsIntegrationRecord
import br.com.alice.nullvs.events.NullvsCreateIntegrationRecordEvent
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.dependency.NullvsDependencyService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class NullvsCreateIntegrationRecordConsumer(
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val companyContractService: CompanyContractService,
    private val companySubContractService: CompanySubContractService,
    private val nullvsDependencyService: NullvsDependencyService,
) : Consumer() {

    suspend fun createNullvsIntegrationRecord(event: NullvsCreateIntegrationRecordEvent) = withSubscribersEnvironment {
        val nullvsIntegrationRecord = event.payload.nullvsIntegrationRecord

        logger.info(
            "Consuming NullvsCreateIntegrationRecordEvent on NullvsCreateIntegrationRecordConsumer",
            "nullvs_integration_record" to nullvsIntegrationRecord,
        )

        nullvsIntegrationRecordService.add(nullvsIntegrationRecord)
            .then { updateModel(it) }
            .then {
                logger.info("NullvsIntegrationRecord created successfully", "response" to it)
            }
            .thenError {
                logger.error("NullvsIntegrationRecord creation error", "exception" to it)
            }
            .then {
                nullvsDependencyService.reprocessItsDependents(
                    nullvsIntegrationRecord.internalId,
                    nullvsIntegrationRecord.internalModelName,
                )
            }
            .map { true }
            .coFoldDuplicated { true.success() }
    }

    private suspend fun updateModel(nullvsIntegrationRecord: NullvsIntegrationRecord) =
        when (nullvsIntegrationRecord.internalModelName) {
            InternalModelType.CONTRACT -> updateContract(nullvsIntegrationRecord)
            InternalModelType.SUBCONTRACT -> updateSubcontract(nullvsIntegrationRecord)

            else -> {
                logger.info(
                    "Did not need to update this model",
                    "id" to nullvsIntegrationRecord.id,
                    "internal_model_name" to nullvsIntegrationRecord.internalModelName,
                )
                true.success()
            }
        }

    private suspend fun updateContract(nullvsIntegrationRecord: NullvsIntegrationRecord) =
        companyContractService.get(nullvsIntegrationRecord.internalId).flatMap {
            logger.info(
                "Update the contract from nullvs integration record",
                "id" to nullvsIntegrationRecord.id,
                "internal_id" to nullvsIntegrationRecord.internalId,
            )
            companyContractService.update(
                it.copy(
                    externalId = nullvsIntegrationRecord.externalId,
                    status = CompanyContractStatus.ACTIVE
                )
            )
        }

    private suspend fun updateSubcontract(nullvsIntegrationRecord: NullvsIntegrationRecord) =
        companySubContractService.get(nullvsIntegrationRecord.internalId).flatMap {
            logger.info(
                "Update the subcontract from nullvs integration record",
                "id" to nullvsIntegrationRecord.id,
                "internal_id" to nullvsIntegrationRecord.internalId,
            )
            companySubContractService.update(
                it.copy(
                    externalId = nullvsIntegrationRecord.externalId,
                )
            )
        }
}
