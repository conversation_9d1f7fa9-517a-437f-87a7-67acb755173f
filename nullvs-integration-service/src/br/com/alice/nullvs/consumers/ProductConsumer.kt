package br.com.alice.nullvs.consumers

import br.com.alice.common.logging.logger
import br.com.alice.nullvs.services.internals.AnsNumberCacheService
import br.com.alice.product.model.events.ProductUpdatedEvent
import com.github.kittinunf.result.success

class ProductConsumer(
    private val ansNumberCacheService: AnsNumberCacheService
) : Consumer() {

    suspend fun cacheAnsNumber(event: ProductUpdatedEvent) = withSubscribersEnvironment {
        val product = event.payload.newProduct

        logger.info(
            "Consuming ProductUpdateEvent on ProductConsumer",
            "product_id" to product.id,
        )

        ansNumberCacheService.cacheOrClear(product)
        true.success()
    }
}
