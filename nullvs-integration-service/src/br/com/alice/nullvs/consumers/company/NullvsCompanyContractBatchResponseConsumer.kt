package br.com.alice.nullvs.consumers.company

import br.com.alice.common.logging.logger
import br.com.alice.nullvs.consumers.Consumer
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toContractNullvsIntegrationLog
import br.com.alice.nullvs.events.NullvsCompanyContractBatchResponseEvent
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import com.github.kittinunf.result.mapError

class NullvsCompanyContractBatchResponseConsumer(
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
) : Consumer() {

    suspend fun createNullvsIntegrationLog(event: NullvsCompanyContractBatchResponseEvent) = withSubscribersEnvironment {
        val nullvsCompanyContractResponse = event.payload.response

        logger.info(
            "Consuming NullvsCompanyContractBatchResponseEvent",
            "nullvs_company_contract_request" to nullvsCompanyContractResponse,
        )

        nullvsIntegrationLogService.add(nullvsCompanyContractResponse.toContractNullvsIntegrationLog()).mapError { exception ->
            logger.error("NullvsCompanyContractBatchResponse creation error", "exception" to exception)
            exception
        }
    }
}
