package br.com.alice.nullvs.consumers.company

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.CompanyContractIntegrationStatus
import br.com.alice.data.layer.models.CompanyContractStatus
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.Consumer
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toNullvsIntegrationRecord
import br.com.alice.nullvs.events.NullvsCompanyContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanyContractWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.countNullvsWebhookConsumer
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.models.TotvsStatus.Companion.toNullvsLog
import br.com.alice.nullvs.models.company.NullvsCompanyContractWebhookReceived
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class NullvsCompanyContractWebhookReceivedConsumer(
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val companyContractService: CompanyContractService,
    private val companySubContractService: CompanySubContractService,
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {
    suspend fun processedAtTotvs(event: NullvsCompanyContractWebhookReceivedEvent) =
        withSubscribersEnvironment {
            val response = event.payload.response

            logger.info(
                "Consume the NullvsCompanyContractWebhookReceivedEvent message",
                "message_event_id" to event.messageId,
                "event_date" to event.eventDate,
                "nullvs_webhook_response" to response,
            )

            nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                response.batchId,
                response.idSoc,
                1
            )
                .coFoldNotFound {
                    logger.info(
                        "NullvsCompanyContractWebhookReceivedConsumer.processedAtTotvs: Creating the log when it is not found",
                        "message_event_id" to event.messageId,
                        "event_date" to event.eventDate,
                    )

                    nullvsIntegrationLogService.add(
                        NullvsIntegrationLog(
                            eventId = event.messageId,
                            eventName = event.name,
                            integrationEventName = event.name,
                            internalId = response.internalId!!,
                            internalModelName = InternalModelType.CONTRACT,
                            externalModelName = ExternalModelType.CONTRACT,
                            batchId = response.batchId,
                            idSoc = response.idSoc,
                            batchType = response.type,
                            payloadSequenceId = 1,
                            description = response.description,
                            status = response.status.toNullvsLog(),
                        )
                    )
                }
                .flatMap {
                    logger.info("found the integration log", "model" to it)
                    when (it.batchType) {
                        BatchType.CREATE -> createRecord(response, it)
                        BatchType.CANCEL -> cancelRecord(response, it)
                        BatchType.UPDATE -> updateRecord(response, it)
                        else -> Exception("TODO").failure()
                    }
                }.then {
                    logger.info("The ${it.batchType} integration worked", "nullvs_log_id" to it.id)
                    countNullvsWebhookConsumer(Method.CONTRACT, Status.SUCCESS)
                }
                .thenError {
                    countNullvsWebhookConsumer(Method.CONTRACT, Status.FAILURE)
                    logger.info(
                        "NullvsCompanyContractWebhookReceivedConsumer:: Error on consumingSubcontract contract webhook",
                        "error_message" to response.errorMessage,
                        "batch_id" to response.batchId,
                        "id_soc," to response.idSoc,
                        "action_type" to response.type
                    )
                    throw it
                }
        }

    private suspend fun createRecord(response: NullvsCompanyContractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS ->
                getAndUpdateContract(response)
                    .flatMap { nullvsIntegrationRecordService.add(response.toNullvsIntegrationRecord(log.id)) }
                    .then { logger.info("The nullvs record is registered", "record_id" to it.id) }
                    .flatMap { nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED)) }
                    .flatMap {
                        syncSubcontractByTotvsContract(response)
                        Result.of { it }
                    }

            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            ).andThen {
                val internalId = response.internalId?.let { response.internalId } ?: log.internalId
                logger.info(
                    "NullvsCompanyContractWebhookReceivedConsumer:: Updating contract status to FAILED",
                    "contract_id" to internalId
                )
                updateContractStatus(internalId, CompanyContractIntegrationStatus.FAILED)
            }
        }

    private suspend fun cancelRecord(response: NullvsCompanyContractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS ->
                nullvsIntegrationRecordService.findByInternalIdAndModel(log.internalId, InternalModelType.CONTRACT)
                    .flatMap { nullvsIntegrationRecordService.update(it.copy(canceledAt = LocalDateTime.now())) }
                    .flatMap { nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED)) }
                    .map { it }

            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            )
        }


    private suspend fun updateRecord(response: NullvsCompanyContractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS -> nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED))
            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            )
        }

    private suspend fun getAndUpdateContract(response: NullvsCompanyContractWebhookReceived) =
        companyContractService.get(response.internalId!!)
            .map {
                logger.info(
                    "The contract is found",
                    "contract_id" to it.id,
                    "contract_title" to it.title,
                    "totvs_contract_number" to response.externalId,
                )
                it.copy(
                    externalId = response.externalId,
                    status = CompanyContractStatus.ACTIVE,
                    integrationStatus = CompanyContractIntegrationStatus.PROCESSED,
                )
            }
            .flatMap { companyContractService.update(it, false) }
            .then { logger.info("The contract is updated") }
            .thenError {
                logger.info(
                    "Something is wrong when tried to update the contract",
                    "contract_id" to response.internalId,
                    "ex" to it
                )
            }

    private suspend fun updateContractStatus(contractId: UUID, status: CompanyContractIntegrationStatus) =
        companyContractService.get(contractId).then {
            companyContractService.update(it.copy(integrationStatus = status), false)
        }

    private fun shouldUseNullvsDependencySystem() =
        FeatureService.get(FeatureNamespace.NULLVS, "use_dependency_service", true)

    private suspend fun syncSubcontractByTotvsContract(response: NullvsCompanyContractWebhookReceived) =
        withSubscribersEnvironment {

            logger.info(
                "Consume the NullvsCompanyContractWebhookReceivedEvent message to sync subcontracts",
                "nullvs_webhook_response" to response,
            )

            when (response.status) {
                TotvsStatus.SUCCESS -> {

                    val internalId = when (response.internalId?.toString()) {
                        null, "" -> {
                            nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                                batchId = response.batchId,
                                idSoc = response.idSoc,
                                payloadSequenceId = 1
                            ).get().internalId
                        }

                        else -> response.internalId!!
                    }

                    if (!shouldUseNullvsDependencySystem()) {
                        companySubContractService.findByContractId(internalId)
                            .map { companySubContracts -> companySubContracts.filter { it.externalId.isNullOrBlank() } }
                            .pmapEach {
                                logger.info(
                                    "NullvsCompanyContractWebhookReceivedConsumer:: Producing NullvsSyncCompanySubcontractRequestEvent",
                                    "subcontract_id" to it.id,
                                    "subcontract_title" to it.title,
                                    "totvs_subcontract_number" to it.externalId,
                                )
                                kafkaProducerService.produce(
                                    NullvsSyncCompanySubcontractRequestEvent(
                                        it,
                                        NullvsActionType.CREATE
                                    )
                                )
                            }
                    }

                    kafkaProducerService.produce(
                        NullvsCompanyContractActivatedEvent(
                            internalId,
                            response.externalId!!,
                            response.groupCompany!!.code,
                        )
                    )

                    true.success()
                }

                TotvsStatus.FAILURE -> {
                    logger.info(
                        "NullvsCompanyContractWebhookReceivedConsumer:: Error to sync subcontracts " +
                                "because TOTVS status is FAILURE",
                        "error_message" to response.errorMessage,
                        "batch_id" to response.batchId,
                        "id_soc," to response.idSoc,
                        "action_type" to response.type
                    )
                    response.success()
                }
            }
        }
}


