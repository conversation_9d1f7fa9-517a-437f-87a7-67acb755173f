import com.google.gson.annotations.SerializedName

data class BundleWebhookResponse(
	@SerializedName("idlote")
	val idSoc: String,
	@SerializedName("lote")
	val batchId: String,
	val payload: List<BundleWebhookResponsePayload>,
)

data class BundleWebhookResponsePayload(
	@SerializedName("BLZ_FILIAL")
	val subsidiaryId: String? = null,
	@SerializedName("BLZ_CODINT")
	val operatorId: String? = null,
	@SerializedName("BLZ_CODRDA")
	val providerExternalId: String? = null,
	@SerializedName("BLZ_CODPAD")
	val tableType: String? = null,
	@SerializedName("BLZ_CODPRO")
	val code: String? = null,
	@SerializedName("BLZ_DTNEGO")
	val dealDate: String? = null,
	@SerializedName("BLZ_VIGINI")
	val validAfter: String? = null,
	@SerializedName("BLZ_XCLINC")
	val xclinic: String? = null,
	@SerializedName("BLZ_XIDALI")
	val compositionHash: String? = null,
	@SerializedName("errorCode")
	val errorCode: String? = null,
	@SerializedName("errorMessage")
	val errorMessage: String? = null,
)
