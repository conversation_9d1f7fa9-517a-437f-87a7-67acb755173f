package br.com.alice.nullvs.models

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class TotvsPriceListingWebhookResponse(
    @SerializedName("total")
    val total: Number,
    @SerializedName("tipo")
    val type: String,
    @SerializedName("entidade")
    val entity: String,
    @SerializedName("data")
    val dateStamp: String,
    @SerializedName("usuario")
    val user: String,
    @SerializedName("acao")
    val action: String,
    @SerializedName("payload")
    val payload: List<TotvsPriceListingWebhookResponsePayload>
)
data class TotvsPriceListingWebhookResponsePayload(
    @SerializedName("BTN_CODIGO")
    val btnCode: String? = null,
    @SerializedName("BTN_SUBCON")
    val subContractExternalId: String? = null,
    @SerializedName("BTN_NUMCON")
    val contractExternalId: String? = null,
    @SerializedName("MATRICULAFAMILIA", alternate=["MATRICULABENEF"])
    val memberRegistration: String? = null,
    @SerializedName("IDTOTVS")
    val idTotvs: String,
    @SerializedName("REGANS")
    val ansProductCode: String,
    @SerializedName("BTN_PERREJ", alternate=["BBU_PERREJ", "PERREJ"])
    val readjustBaseDate: String? = null,
    @SerializedName("VIGINI")
    val startDate: String,
    @SerializedName("VIGINI_ANTERIOR")
    val previousStartDate: String? = null,
    @SerializedName("Faixas")
    val ranges: List<TotvsPriceListingPricingPerAgeRangeWebhookResponsePayload>,
)

data class TotvsPriceListingPricingPerAgeRangeWebhookResponsePayload(
    @SerializedName("BTN_REJAPL", alternate=["BBU_REJAPL"])
    val appliedReadjust: BigDecimal? = null,
    @SerializedName("BTN_IDAINI", alternate=["BBU_IDAINI", "BDK_IDAINI"])
    val minAge: Int,
    @SerializedName("BTN_IDAFIN", alternate=["BBU_IDAFIN", "BDK_IDAFIN"])
    val maxAge: Int,
    @SerializedName("BTN_VALFAI", alternate=["BBU_VALFAI", "BDK_VALOR"])
    val value: BigDecimal,
    @SerializedName("BTN_VLRANT", alternate=["BBU_VLRANT", "BDK_VLRANT"])
    val oldValue: Number? = null,
)
