package br.com.alice.acquisition.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.ACQUISITION_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.SalesAgent


class SalesAgentWasAssignedEvent (
    eventPayload: Payload
) : NotificationEvent<SalesAgentWasAssignedEvent.Payload>(
    name = name,
    producer = ACQUISITION_ROOT_SERVICE_NAME,
    payload = eventPayload
) {
    companion object {
        const val name = "SALES-AGENT-WAS-ASSIGNED"
    }

    data class Payload(
        val hubspotDealId: String,
        val hubspotContactId: String,
        val salesAgent: SalesAgent,
    )
}
