package br.com.alice.api.eitaexternal.routes

import br.com.alice.api.eitaexternal.controllers.AuthController
import br.com.alice.api.eitaexternal.controllers.SpecialtyEligibilityController
import br.com.alice.common.coHandler
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.handler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import org.koin.ktor.ext.inject

fun Routing.apiRoutes() {
    val healthController by inject<HealthController>()
    get("/") { handler(healthController::checkHealth) }

    val authController by inject<AuthController>()
    post("/auth") {
        coHandler(authController::authorize)
    }

    authenticate {
        val specialtyEligibilityController by inject<SpecialtyEligibilityController>()

        post ("/checkSpecialistEligibility") {
            co<PERSON>and<PERSON>(specialtyEligibilityController::check)
        }
    }
}
