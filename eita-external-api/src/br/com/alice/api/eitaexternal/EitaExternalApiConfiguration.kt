package br.com.alice.api.eitaexternal


import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object EitaExternalApiConfiguration {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    private fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun einsteinCredential() = Credentials(
        config.property("${environment().value.lowercase()}.einsteinClientId").getString(),
        config.property("${environment().value.lowercase()}.einsteinClientSecret").getString()
    )

    fun livanceCredential() = Credentials(
        config.property("${environment().value.lowercase()}.livanceClientId").getString(),
        config.property("${environment().value.lowercase()}.livanceClientSecret").getString()
    )

    fun secretKey() : ByteArray {
        var defaultKey = config.property("${environment().value.lowercase()}.secretKey").getString()
        while (defaultKey.length < 48) {
            defaultKey += defaultKey
        }
        return defaultKey.toByteArray()
    }
}

data class Credentials(
    val clientId: String,
    val clientSecret: String,
)
