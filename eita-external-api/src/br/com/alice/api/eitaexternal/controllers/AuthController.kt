package br.com.alice.api.eitaexternal.controllers

import br.com.alice.api.eitaexternal.metrics.AuthenticationLabel
import br.com.alice.api.eitaexternal.metrics.AuthenticationMetric
import br.com.alice.api.eitaexternal.services.AuthService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.logging.logger
import io.ktor.http.HttpStatusCode

class AuthController : Controller() {

    fun authorize(request: AuthRequest): Response {
        val result = AuthService.authorize(request.clientId, request.clientSecret)

        if (result.isNullOrEmpty()) {
            logger.info("EITA API unauthorized", request.clientId)
            AuthenticationMetric.increment(AuthenticationLabel.FAILURE)
            return Response(HttpStatusCode.Unauthorized)
        }

        logger.info("EITA API authorized", request.clientId)
        AuthenticationMetric.increment(AuthenticationLabel.SUCCESS)
        return Response(HttpStatusCode.OK, AuthResponse(result))
    }
}

data class AuthRequest(
    val clientId: String,
    val clientSecret: String
)

data class AuthResponse(
    val token: String
)
