ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [br.com.alice.api.einstein.ApplicationKt.module]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    clientId = "clientId"
    clientSecret = "clientSecret"
    secretKey = "secretKey"

    einsteinEligibleLifeClientId = "eliClientId"
    einsteinEligibleLifeSecretKey = "eliSecretKey"
}

test {
    clientId = "clientId"
    clientSecret = "clientSecret"
    secretKey = "secretKey"

    einsteinEligibleLifeClientId = "eliClientId"
    einsteinEligibleLifeSecretKey = "eliSecretKey"
}

production {
    clientId = ""
    clientId = ${?EINSTEIN_INTEGRATION_CLIENT_ID}
    clientSecret = ""
    clientSecret = ${?EINSTEIN_INTEGRATION_CLIENT_SECRET}
    secretKey = ""
    secretKey = ${?EINSTEIN_INTEGRATION_SECRET_KEY}

    einsteinEligibleLifeClientId = ""
    einsteinEligibleLifeClientId = ${?EINSTEIN_ELIGIBLE_LIFE_CLIENT_ID}
    einsteinEligibleLifeSecretKey = ""
    einsteinEligibleLifeSecretKey = ${?EINSTEIN_ELIGIBLE_LIFE_SECRET_KEY}
}
