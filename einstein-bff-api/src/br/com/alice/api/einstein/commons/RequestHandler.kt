package br.com.alice.api.einstein.commons

import br.com.alice.api.einstein.EinsteinBffApiConfiguration
import br.com.alice.common.Response
import br.com.alice.common.getGenericParameter
import br.com.alice.common.principalPathKey
import br.com.alice.common.rateLimited
import br.com.alice.common.respond
import br.com.alice.common.withContexts
import com.google.gson.Gson
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.request.header
import io.ktor.util.pipeline.PipelineContext

suspend inline fun <reified T : Any> PipelineContext<*, ApplicationCall>.coHandlerLimitedEinsteinEligible(
    crossinline controllerAction: suspend (T) -> Response,
    gson: Gson? = null,
    times: Int? = null,
    duration: Long? = null,
    noinline keyGenerator: ((T) -> String)? = null
) =
    withContexts {
        val defaultKeyGenerator: (T) -> String = { p -> p.hashCode().toString() }

        val genericParameter = getGenericParameter<T>(call, gson)

        val key = keyGenerator?.invoke(genericParameter) ?: principalPathKey() ?: defaultKeyGenerator.invoke(genericParameter)

        val clientIdHeader = call.request.header("CLIENT_ID")
        val secretKeyHeader = call.request.header("SECRET_KEY")

        if (!isAuthenticated(clientIdHeader, secretKeyHeader))
            return@withContexts this.respond(response = Response(HttpStatusCode.Unauthorized))

        rateLimited(
            key = key,
            times = times,
            duration = duration
        ) {
            controllerAction(genericParameter)
        }.apply {
            respond(this)
        }
    }


fun isAuthenticated(clientId: String?, secretKey: String?): Boolean {
    return (clientId == EinsteinBffApiConfiguration.eligibleLifeClientId()
            && secretKey == EinsteinBffApiConfiguration.eligibleLifeSecretKey())
}
