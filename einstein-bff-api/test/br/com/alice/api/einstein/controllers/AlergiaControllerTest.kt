package br.com.alice.api.einstein.controllers

import br.com.alice.api.einstein.ControllerTestHelper
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.models.Alergia
import br.com.alice.data.layer.models.Conselho
import br.com.alice.data.layer.models.EinsteinAlergia
import br.com.alice.data.layer.models.EinsteinAtendimento
import br.com.alice.data.layer.models.EinsteinProfissional
import br.com.alice.data.layer.models.Motivo
import br.com.alice.einsteinintegrationclient.client.EinsteinAlergiaService
import br.com.alice.einsteinintegrationclient.client.EinsteinAtendimentoService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class AlergiaControllerTest : ControllerTestHelper() {

    private val alergiaService: EinsteinAlergiaService = mockk()
    private val atendimentoService: EinsteinAtendimentoService = mockk()
    private val alergiaController = AlergiaController(alergiaService, atendimentoService)
    private val personId = PersonId()
    private val motivo = Motivo(
        categorizacao = "categorizacao",
        historiaMolestia = "historia molestia",
        declaracao = "declaracao"
    )

    private val conselho = Conselho(
        tipoConselho = "tipo conselho",
        ufConselho = "uf conselho",
        numeroRegistro = "numero registro"
    )

    private val profissional = EinsteinProfissional(
        nomeProfissional = "nome profissional",
        ocupacao = "ocupacao",
        conselho = conselho
    )
    private val einsteinAtendimento = EinsteinAtendimento(
        personId = personId,
        identificacao = "indentificacao",
        passagem = "passagem",
        local = "local",
        estabelecimento = "estabelecimento",
        procedencia = "procedencia",
        dataChegada = "01/01/2020",
        dataAtendimento = "01/01/2020",
        modalidade = "modalidade",
        profissionais = listOf(profissional),
        motivos = listOf(motivo)
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { alergiaController }
    }

    @Test
    fun `should return http code 200`() {
        val alergia = Alergia(
            categoriaAgente = "categoriaAgente",
            agente = "agente",
            manifestacao = "manifestacao",
            criticidade = "criticidade",
            dataReacao = "01/01/2020",
            evolucao = "evolucao"
        )
        val einsteinAlergia = EinsteinAlergia(
            personId = personId,
            passagem = "passagem",
            alergias = listOf(alergia)
        )
        coEvery { atendimentoService.getByPassagem(any()) } returns einsteinAtendimento.success()
        coEvery { alergiaService.add(any()) } returns einsteinAlergia.success()

        val body = CreateAlergiaRequest(
            passagem = "passagem",
            alergias = listOf(alergia)
        )

        authenticated(idToken) {
            post("/alergia", body = body) { response -> assertThat(response).isOK() }
            coVerify(exactly = 1) {
                alergiaService.add(match {
                    it.passagem == body.passagem
                            && it.alergias == body.alergias.map { it.convertTo(Alergia::class) }
                })
            }
        }
    }

    @Test
    fun `should return error if passagem was not found`() {
        coEvery { atendimentoService.getByPassagem(any()) } returns NotFoundException("resource_not_found").failure()

        val body = CreateAlergiaRequest(
            passagem = "passagem",
            alergias = emptyList()
        )

        authenticated(idToken) {
            post("/alergia", body = body) { response -> assertThat(response).isNotFound() }
            coVerify { alergiaService wasNot called}
        }
    }
}
