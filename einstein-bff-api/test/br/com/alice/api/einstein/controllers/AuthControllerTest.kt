package br.com.alice.api.einstein.controllers

import br.com.alice.api.einstein.ControllerTestHelper
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthControllerTest : ControllerTestHelper() {

    private val authController = AuthController()
    private val request = AuthRequest(
        clientId = "clientId",
        clientSecret = "clientSecret"
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { authController }
    }

    @Test
    fun `#authorize should return 200 with token if valid credentials`() {

        post("/autenticar", body = request) { response ->
            val content = response.bodyAsJson<AuthResponse>()
            assertThat(response).isOK()
            assertThat(content.token).isNotNull()
        }
    }

    @Test
    fun `#authorize should return 401 if invalid clientId`() {
        val request = request.copy(clientId = "invalid")
        post("/autenticar", body = request) { response ->
            assertThat(response).isUnauthorized()
        }
    }

    @Test
    fun `#authorize should return 401 if invalid clientSecret`() {
        val request = request.copy(clientSecret = "invalid")
        post("/autenticar", body = request) { response ->
            assertThat(response).isUnauthorized()
        }
    }

}
