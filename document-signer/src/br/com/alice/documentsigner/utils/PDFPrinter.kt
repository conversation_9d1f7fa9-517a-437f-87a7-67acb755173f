package br.com.alice.documentsigner.utils

import br.com.alice.common.core.extensions.baseFormatter
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalCouncil
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.PrescriptionMedicineType.SPECIAL
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.documentsigner.LibConfig
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.client.j2se.MatrixToImageConfig
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.common.BitMatrix
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import java.awt.image.BufferedImage
import java.io.ByteArrayOutputStream
import java.time.LocalDateTime
import org.apache.pdfbox.multipdf.PDFMergerUtility
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDPageContentStream
import org.apache.pdfbox.pdmodel.font.PDTrueTypeFont
import org.apache.pdfbox.pdmodel.font.encoding.WinAnsiEncoding
import org.apache.pdfbox.pdmodel.graphics.image.JPEGFactory
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject

object PDFPrinter {

    private const val defaultFontSize = 11f
    private const val physicianAddress = "Av. Rebouças, 3506 - Pinheiros - São Paulo / SP - 05402-600"
    private const val leading = 1.5f * defaultFontSize
    private const val linesPerPage = 16
    private const val charsPerLine = 80
    private const val bodyContentX = 70f

    private const val examRequestTemplatePDF = "new_link_exam_request.pdf"
    private const val signedExamRequestTemplatePDF = "new_link_exam_request_signed.pdf"
    private const val excuseNotesTemplatePDF = "new_link_excuse_notes_with_qrcode.pdf"
    private const val simplePhysicianPrescriptionTemplatePDF = "new_link_physician_normal_prescription.pdf"
    private const val specialPhysicianPrescriptionTemplatePDF = "new_link_physician_special_prescription.pdf"
    private const val simpleNursePrescriptionTemplatePDF = "new_link_nurse_normal_prescription.pdf"
    private const val specialNursePrescriptionTemplatePDF = "new_link_nurse_special_prescription.pdf"
    private const val referralSignedTemplate = "referral_signed_template.pdf"

    private val qrCodeBaseUrl = LibConfig.PDF.baseUrl
    private val qrCodeExcuseNoteBaseUrl = LibConfig.PDF.baseExcuseNoteUrl

    fun generateSpecialPrescription(
        prescription: Prescription,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        logger.info("PDFPrinter.generateSpecialPrescription", "prescription" to prescription)
        val pdfMerger = PDFMergerUtility()
        val outputStream = ByteArrayOutputStream(4096)
        pdfMerger.destinationStream = outputStream

        if (prescription.medicine?.type == SPECIAL) {
            repeat(2) {
                pdfMerger.addSource(
                    createSpecialPrescription(prescription, date, healthProfessional, person).inputStream()
                )
            }
        }

        pdfMerger.mergeDocuments(null)

        return outputStream.toByteArray()
    }

    fun generatePrescriptions(
        prescriptions: List<Prescription>,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        logger.info("PDFPrinter.generatePrescriptions", "prescriptions" to prescriptions)
        val pdfMerger = PDFMergerUtility()
        val outputStream = ByteArrayOutputStream(4096)
        pdfMerger.destinationStream = outputStream

        if (prescriptions.isNotEmpty()) {
            pdfMerger.addSource(
                createSimplePrescription(prescriptions, date, healthProfessional, person).inputStream()
            )
        }

        pdfMerger.mergeDocuments(null)

        return outputStream.toByteArray()
    }

    fun generateExamRequest(
        date: LocalDateTime,
        testRequests: List<TestRequest>,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 150f, date.formatForPDF()),
            physician = PdfStringContent(340f, 132f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 155f, healthProfessional.name)
        )

        return drawPdf(examRequestTemplatePDF, person, healthProfessional, testRequests.map { it.formattedTitle() }, fixedContent)
    }

    fun generateSignedExamRequest(
        date: LocalDateTime,
        testRequests: List<TestRequest>,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        val lastTestRequest = testRequests.last()
        val token = lastTestRequest.token ?: throw IllegalArgumentException("test request have no token")

        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 150f, date.formatForPDF()),
            physician = PdfStringContent(340f, 136f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 158f, healthProfessional.name),
            qrCode = PdfStringContent(275f, 136f, "$qrCodeBaseUrl/${lastTestRequest.shortId}"),
            token = PdfStringContent(445f, 92f, token),
        )

        return drawPdf(
            signedExamRequestTemplatePDF,
            person,
            healthProfessional,
            testRequests.map { it.formattedTitle() },
            fixedContent,
        )
    }

    fun generateExcuseNotes(
        appointment: Appointment,
        healthProfessional: HealthProfessional,
        person: Person,
        token: String
    ): ByteArray {
        if (appointment.excuseNotes.isEmpty()) throw IllegalArgumentException("The appointment has no excuse notes")

        val date = appointment.createdAt.toSaoPauloTimeZone()

        val content = appointment.excuseNotes.map { it.description }

        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 145f, date.formatForPDF()),
            physician = PdfStringContent(340f, 130f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 150f, healthProfessional.name),
            qrCode = PdfStringContent(
                270f,
                130f,
                "$qrCodeExcuseNoteBaseUrl/${appointment.id}/${appointment.excuseNotes.first().id}"
            ),
            token = PdfStringContent(445f, 76f, token)
        )

        return drawPdf(excuseNotesTemplatePDF, person, healthProfessional, content, fixedContent)
    }

    fun generateSignedReferral(
        referral: Referral,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        logger.info("PDFPrinter.generateSignedReferral", "referral" to referral)
        val token = referral.token ?: throw IllegalArgumentException("Referral have no token")
        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 150f, date.formatForPDF()),
            physician = PdfStringContent(340f, 136f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 158f, healthProfessional.name),
            qrCode = PdfStringContent(275f, 136f, "$qrCodeBaseUrl/${referral.shortId}"),
            token = PdfStringContent(441f, 92f, token)
        )

        return drawPdf(referralSignedTemplate, person, healthProfessional, listOf(referral.therapySentence()), fixedContent)
    }

    private fun createSpecialPrescription(
        prescription: Prescription,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        val token = prescription.token ?: throw IllegalArgumentException("Prescription have no token")
        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 330f, date.formatForPDF()),
            physician = PdfStringContent(350f, 318f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 336f, healthProfessional.name),
            qrCode = PdfStringContent(270f, 318f, "$qrCodeBaseUrl/${prescription.shortId}"),
            token = PdfStringContent(444f, 272f, token)
        )

        val prescriptionDescriptions = listOf(prescription.formattedTitle(), "• ${prescription.fullSentence()}")

        val template = if (healthProfessional.councilName == HealthProfessionalCouncil.CRM.toString()) {
            specialPhysicianPrescriptionTemplatePDF
        } else {
            specialNursePrescriptionTemplatePDF
        }

        return drawPdf(template, person, healthProfessional, prescriptionDescriptions, fixedContent)
    }

    private fun createSimplePrescription(
        prescriptions: List<Prescription>,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        val lastPrescription = prescriptions.last()
        val token = lastPrescription.token ?: throw IllegalArgumentException("Prescription have no token")
        val fixedContent = PdfFixedContent(
            date = PdfStringContent(73f, 150f, date.formatForPDF()),
            physician = PdfStringContent(340f, 136f, getPhysicianNameWithCouncilData(healthProfessional)),
            signature = PdfStringContent(350f, 158f, healthProfessional.name),
            qrCode = PdfStringContent(275f, 136f, "$qrCodeBaseUrl/${lastPrescription.shortId}"),
            token = PdfStringContent(441f, 92f, token)
        )

        val prescriptionDescriptions = prescriptions.map { prescription ->
            listOf(prescription.formattedTitle(), "• ${prescription.fullSentence()}")
        }.flatten()

        val template = if (healthProfessional.councilName == HealthProfessionalCouncil.CRM.toString()) {
            simplePhysicianPrescriptionTemplatePDF
        } else {
            simpleNursePrescriptionTemplatePDF
        }

        return drawPdf(template, person, healthProfessional, prescriptionDescriptions, fixedContent)
    }

    private fun drawPdf(
        template: String,
        person: Person,
        healthProfessional: HealthProfessional,
        content: List<String>,
        fixedContent: PdfFixedContent? = null,
    ): ByteArray {
        val document = PDDocument()
        val font = PDTrueTypeFont.load(document, getFontResource(), WinAnsiEncoding.INSTANCE)
        val fontSignature = PDTrueTypeFont.load(document, getSignatureFontResource(), WinAnsiEncoding.INSTANCE)
        val personAddress: Address? = if (person.addresses.isEmpty()) null else person.addresses[0]
        val formattedPersonAddress =
            if (personAddress != null) handleWithSpecialChars(personAddress.toString()) else "N/A"
        val personPersonData = getPersonPersonData(person)

        val defaultX = 105f

        val currentItemX = bodyContentX
        val currentItemY = 530f

        // serialize content to count characters per line and lines per page
        val allDataToString = content
            .map { MarkdownEraser.erase(it) }
            .joinToString(separator = System.lineSeparator()) { handleWithSpecialChars(it) }

        val contentListByNewLine = allDataToString.split(System.lineSeparator())
        val contentListByWordsPerLine = contentListByNewLine.map { it.chunked(charsPerLine) }
        val chunkedContent = contentListByWordsPerLine.flatten().chunked(linesPerPage)

        chunkedContent.forEach { contentList ->
            // create page
            val templateDocument = PDDocument.load(getResourceAsStream(template))
            val templatePage = templateDocument.getPage(0)
            val contentStream = PDPageContentStream(document, templatePage, PDPageContentStream.AppendMode.APPEND, true)
            contentStream.setFont(font, defaultFontSize)

            // draw header
            contentStream.drawString(defaultX, 697f, healthProfessional.name)
            contentStream.drawString(369f, 697f, healthProfessional.council.toString())
            contentStream.drawString(defaultX, 665f, physicianAddress)
            contentStream.drawString(defaultX, 608f, personPersonData)
            contentStream.drawString(defaultX, 575f, formattedPersonAddress)

            // draw items per page
            contentStream.drawList(currentItemX, currentItemY, contentList)

            // draw fixed items per page
            fixedContent?.let { content ->
                contentStream.drawContent(content.date)

                contentStream.setFont(font, defaultFontSize - 3)
                contentStream.drawContent(content.physician)

                content.qrCode?.let { contentStream.drawQRCode(document, it) }
                content.token?.let { contentStream.drawContent(it) }

                contentStream.setFont(fontSignature, 30f)
                contentStream.drawContent(content.signature)
            }

            contentStream.close()
            document.addPage(templatePage)
        }

        val output = ByteArrayOutputStream(1024)
        document.save(output)
        document.close()
        return output.toByteArray()
    }

    private fun PDPageContentStream.drawString(x: Float, y: Float, text: String) {
        beginText()
        newLineAtOffset(x, y)
        text.chunked(charsPerLine).forEach { content ->
            showText(content)
            newLineAtOffset(0f, -leading)
        }
        endText()
    }

    private fun PDPageContentStream.drawList(x: Float, y: Float, lines: List<String>) {
        beginText()
        newLineAtOffset(x, y)
        lines.forEach { content ->
            showText(handleWithSpecialChars(content))
            newLineAtOffset(0f, -leading)
        }
        endText()
    }

    private fun PDPageContentStream.drawContent(content: PdfStringContent) {
        beginText()
        newLineAtOffset(content.x, content.y)
        showText(handleWithSpecialChars(content.content))
        endText()
    }

    private fun PDPageContentStream.drawQRCode(document: PDDocument, content: PdfStringContent) {
        val hintMap = mutableMapOf<EncodeHintType, Any>(
            EncodeHintType.MARGIN to 1,
            EncodeHintType.ERROR_CORRECTION to ErrorCorrectionLevel.L
        )

        val matrix: BitMatrix = MultiFormatWriter().encode(
            String(content.content.toByteArray(), Charsets.UTF_8),
            BarcodeFormat.QR_CODE, 100, 100, hintMap
        )

        val config = MatrixToImageConfig(MatrixToImageConfig.BLACK, MatrixToImageConfig.WHITE)
        val bImage: BufferedImage = MatrixToImageWriter.toBufferedImage(matrix, config)
        val image: PDImageXObject = JPEGFactory.createFromImage(document, bImage)
        this.drawImage(image, content.x, content.y, 62f, 62f)
    }

    private fun getResourceAsStream(fileName: String) =
        javaClass.classLoader.getResourceAsStream("pdf_templates/$fileName")!!

    private fun getFontResource() =
        getResourceAsStream("poligon.ttf")

    private fun getSignatureFontResource() =
        getResourceAsStream("bayshore.ttf")

    private fun handleWithSpecialChars(string: String) =
        string.replace("[\r]".toRegex(), "")
            .replace("[\t]".toRegex(), "    ")
            .replace('\u00A0', ' ')
            .replace('\u00AD', ' ')
            .replace('\u2981', ' ')
            .replace('\u0009', ' ')
            .replace('\u1BCA', ' ')
            .replace('\u0327', ' ')
            .replace('\u0303', ' ')
            .replace('\u0301', ' ')
            .replace('\ufeff', ' ')

    private fun getPhysicianNameWithCouncilData(healthProfessional: HealthProfessional): String =
        handleWithSpecialChars("${healthProfessional.name} ${healthProfessional.councilSignature}")

    private fun getPersonPersonData(person: Person): String = handleWithSpecialChars(person.fullSocialName)

    private fun LocalDateTime.formatForPDF() = format(baseFormatter("dd         MM         yyyy"))

    data class PdfFixedContent(
        val date: PdfStringContent,
        val physician: PdfStringContent,
        val signature: PdfStringContent,
        val qrCode: PdfStringContent? = null,
        val token: PdfStringContent? = null
    )

    data class PdfStringContent(
        val x: Float,
        val y: Float,
        val content: String
    )
}
