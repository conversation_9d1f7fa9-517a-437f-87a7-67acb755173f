package br.com.alice.healthlogicsapi.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.ServiceScriptOperator.EQUALITY
import br.com.alice.healthlogicsapi.models.ConditionResponse
import br.com.alice.healthlogicsapi.models.ServiceScriptRelationshipRequest
import br.com.alice.healthlogicsapi.models.ServiceScriptRelationshipResponse
import br.com.alice.healthlogicsapi.models.SymptomItems
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ServiceScriptRelationshipConverterTest {

    private val symptomConditionResponse = ConditionResponse(
        items = listOf(
            SymptomItems(
                id = "0c56bf1b-bfa6-4cff-964b-99473917a36e".toUUID(),
                description = "Dor de garganta"
            )
        ),
        operator = EQUALITY
    )
    private val relationship = TestModelFactory.buildServiceScriptRelationship(
        parentId = RangeUUID.generate(),
        conditions = listOf(
            Condition(
                key = ConditionOptions.SYMPTOM_ID.key,
                value = listOf("0c56bf1b-bfa6-4cff-964b-99473917a36e".toUUID()),
                operator = EQUALITY
            )
        )
    )
    private val relationshipRequest = ServiceScriptRelationshipRequest(
        name = relationship.name,
        nodeParentId = relationship.nodeParentId,
        nodeChildId = relationship.nodeChildId,
        status = relationship.status,
        priority = relationship.priority,
        version = relationship.version,
        symptoms = symptomConditionResponse
    )

    @Test
    fun `#ServiceScriptRelationshipConverter converts ServiceScriptRelationshipRequest to a ServiceScriptRelationship when has id`() {
        val result = ServiceScriptRelationshipConverter.convert(relationshipRequest, relationship.id)

        assertThat(result).isEqualTo(relationship)
    }

    @Test
    fun `#ServiceScriptRelationshipConverter converts ServiceScriptRelationshipRequest to a ServiceScriptRelationship when has no id`() = mockRangeUUID(relationship.id) {
        val result = ServiceScriptRelationshipConverter.convert(relationshipRequest)

        assertThat(result).isEqualTo(relationship)
    }

    @Test
    fun `#ServiceScriptRelationshipResponseConverter converts ServiceScriptRelationship to a ServiceScriptRelationshipResponse`() {
        val relationshipResponse = ServiceScriptRelationshipResponse(
            id = relationship.id,
            name = relationship.name,
            nodeParentId = relationship.nodeParentId,
            nodeChildId = relationship.nodeChildId,
            status = relationship.status,
            priority = relationship.priority,
            version = relationship.version,
            symptoms = symptomConditionResponse
        )

        val result = ServiceScriptRelationshipResponseConverter.convert(relationship, relationshipRequest)

        assertThat(result).isEqualTo(relationshipResponse)
    }
}
