package br.com.alice.healthlogicsapi.services

import br.com.alice.authentication.Authenticator
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.firebase.auth.FirebaseToken
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class AuthServiceTest {
    private val staffService: StaffService = mockk()
    private val firebaseToken: FirebaseToken = mockk()
    private val authService = AuthService(staffService)

    private val staff = TestModelFactory.buildStaff()
    private val accessToken = "idToken"

    @Test
    fun `#signIn should return true if sign in`() = mockAuth {
        coEvery { staffService.findByEmail(staff.email) } returns staff.success()

        val actual = authService.signIn("idToken")
        assertThat(actual).isTrue()
    }

    @Test
    fun `#signIn should return false if staff is null`() = mockAuth {
        coEvery { staffService.findByEmail(staff.email) } returns NotFoundException().failure()

        val actual = authService.signIn(accessToken)
        assertThat(actual).isFalse()
    }

    private fun mockAuth(testFunction: suspend () -> Unit) = runBlocking {
        mockkObject(Authenticator) {

            val claims = mapOf(
                Authenticator.USER_TYPE_KEY to staff::class.simpleName,
                Authenticator.USER_ID_KEY to staff.id.toString(),
                Authenticator.ROLES_KEY to staff.allRoles().map { it.name }
            )

            every { Authenticator.verifyIdToken(accessToken, true) } returns firebaseToken
            every { Authenticator.generateCustomToken(staff.email, "Unauthenticated") } returns "environmentToken"
            every { Authenticator.setCustomUserClaims(firebaseToken, claims) } just Runs

            every { firebaseToken.email } returns staff.email
            every { firebaseToken.isEmailVerified } returns true

            testFunction()
        }
    }
}
