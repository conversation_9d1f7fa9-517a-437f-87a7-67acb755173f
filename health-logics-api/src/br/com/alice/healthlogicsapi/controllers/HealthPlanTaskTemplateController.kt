package br.com.alice.healthlogicsapi.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.healthlogicsapi.converters.HealthPlanTaskTemplateConverter
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import io.ktor.http.Parameters

class HealthPlanTaskTemplateController(
    private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService
) : Controller(){


    suspend fun index(parameters: Parameters): Response {
        val sort = parseSort(parameters)
        val range = parseRange(parameters)
        val filter = parseFilter(parameters) ?: emptyMap()

        return healthPlanTaskTemplateService.getFilteredByRange(range, filter, sort)
            .foldResponse({ HealthPlanTaskTemplateConverter.convert(it) })
    }
}
