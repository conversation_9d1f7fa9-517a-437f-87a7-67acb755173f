package br.com.alice.healthlogicsapi.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.healthlogic.client.OutcomeConfService
import br.com.alice.healthlogicsapi.models.OutcomeConfResponse
import io.ktor.http.Parameters

class OutcomeConfController(
    private val outcomeConfService: OutcomeConfService
): Controller() {

    suspend fun search(parameters: Parameters): Response =
        outcomeConfService.findOutcomeConf(
            key = parseQuery(parameters),
            range = IntRange(0, 20),
            status = null
        ).foldResponse({
            it.map { outcome ->
                OutcomeConfResponse(
                    id = outcome.id,
                    key = outcome.key,
                    description = outcome.description,
                    status = outcome.status
                )
            }
        })
}
