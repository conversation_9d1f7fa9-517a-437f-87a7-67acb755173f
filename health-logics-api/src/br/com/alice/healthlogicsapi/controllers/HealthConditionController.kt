package br.com.alice.healthlogicsapi.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.foldResponse
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.models.HealthConditionCodeType.CIAP_2
import br.com.alice.data.layer.models.HealthConditionCodeType.CID_10
import br.com.alice.data.layer.models.HealthConditionCodeType.GOAL
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthlogicsapi.converters.HealthConditionConverter
import br.com.alice.healthlogicsapi.models.Classification
import com.github.kittinunf.result.map
import io.ktor.http.Parameters


class HealthConditionController(
    private val healthConditionService: HealthConditionService
): Controller() {

    suspend fun search(parameters: Parameters): Response {
        val query = parameters["query"]
            ?: throw IllegalArgumentException("Missing parameter query")

        val types = getTypes(parameters)

        return healthConditionService.searchByType(query, types).map {
            HealthConditionConverter.convert(it)
        }.foldResponse()
    }

    private fun getTypes(parameters: Parameters): List<HealthConditionCodeType> =
        getClassification(parameters).map{
            HealthConditionCodeType.valueOf(it.diseaseType.toString())
        }

    private fun getClassification(parameters: Parameters): List<Classification> =
        parameters["types"]?.let { types ->
            try {
                gson.fromJson<List<Classification>>(types)
            } catch (e: Exception) {
                throw IllegalArgumentException("Invalid types: $types")
            }
        } ?: emptyList()
}
