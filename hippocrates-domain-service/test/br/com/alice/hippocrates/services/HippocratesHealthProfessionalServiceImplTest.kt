package br.com.alice.hippocrates.services

import kotlin.test.Test
import io.mockk.coEvery
import kotlinx.coroutines.runBlocking
import com.github.kittinunf.result.success
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.services.HippocratesHealthcareProfessionalModelDataService
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.mockk
import br.com.alice.hippocrates.converters.toModel
import com.github.kittinunf.result.failure

class HippocratesHealthProfessionalServiceImplTest {
    private val hippocratesHealthProfessionalDataService: HippocratesHealthcareProfessionalModelDataService = mockk()
    private val hippocratesHealthProfessionalService = HippocratesHealthProfessionalServiceImpl(
        hippocratesHealthProfessionalDataService
    )

    private val healthProfessional = TestModelFactory.buildHippocratesHealthProfessional()
    private val healthProfessionalModel = healthProfessional.toModel()

    @Test
    fun `#findBySearchTokensAndRange should return hippocrates health professional list`() = runBlocking {
        val range = IntRange(0, 10)
        val expectedResult = listOf(healthProfessional)
        val term = "HEALTH PROFESSIONAL"

        coEvery {
            hippocratesHealthProfessionalDataService.find(queryEq {
                where { searchTokens.search(term) }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(healthProfessionalModel).success()

        val result = hippocratesHealthProfessionalService.findBySearchTokensAndRange(term, range)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { hippocratesHealthProfessionalDataService.find(any()) }
    }

    @Test
    fun `#findByRange should return hippocrates health professional list`() = runBlocking {
        val range = IntRange(0, 10)
        val expectedResult = listOf(healthProfessional)

        coEvery {
            hippocratesHealthProfessionalDataService.find(queryEq {
                sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
            })
        } returns listOf(healthProfessionalModel).success()

        val result = hippocratesHealthProfessionalService.findByRange(range)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { hippocratesHealthProfessionalDataService.find(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count returns a total hippocrates health professional count`() = runBlocking {
        coEvery {
            hippocratesHealthProfessionalDataService.count(queryEq { all() })
        } returns 10.success()

        val result = hippocratesHealthProfessionalService.count()

        assertThat(result).isSuccessWithData(10)

        coVerifyOnce { hippocratesHealthProfessionalDataService.count(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count returns an error`() = runBlocking {
        coEvery {
            hippocratesHealthProfessionalDataService.count(queryEq { all() })
        } returns Exception().failure()

        val result = hippocratesHealthProfessionalService.count()

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { hippocratesHealthProfessionalDataService.count(any()) }
    }
}
