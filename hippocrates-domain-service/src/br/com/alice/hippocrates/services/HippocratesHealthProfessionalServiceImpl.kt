package br.com.alice.hippocrates.services

import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.data.layer.models.HippocratesHealthcareProfessional
import br.com.alice.data.layer.services.HippocratesHealthcareProfessionalModelDataService
import br.com.alice.hippocrates.client.HippocratesHealthcareProfessionalService
import br.com.alice.hippocrates.converters.toModel
import br.com.alice.hippocrates.converters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID
import br.com.alice.common.useReadDatabase

class HippocratesHealthProfessionalServiceImpl(
    private val dataService: HippocratesHealthcareProfessionalModelDataService
) : HippocratesHealthcareProfessionalService {

    @OptIn(QueryAllUsage::class)
    override suspend fun findBySearchTokens(token: String?) =
        dataService.find {
            token?.let { where { this.searchTokens.search(token) }.limit { 20 } } ?: all().limit { 20 }
        }.mapEach { it.toTransport() }

    override suspend fun add(
        hippocratesHealthcareProfessional: HippocratesHealthcareProfessional
    ): Result<HippocratesHealthcareProfessional, Throwable> =
        dataService.add(hippocratesHealthcareProfessional.toModel()).map { it.toTransport() }

    override suspend fun update(
        hippocratesHealthcareProfessional: HippocratesHealthcareProfessional
    ) = dataService.findOne {
        where {
            this.id.eq(hippocratesHealthcareProfessional.id.toString())
        }
    }.flatMap {
        dataService.update(
            hippocratesHealthcareProfessional.toModel().copy(version = it.version)
        ).map { it.toTransport() }
    }

    override suspend fun get(id: UUID) =
        dataService.get(id).map { it.toTransport() }

    override suspend fun findByRange(
        range: IntRange,
    ): Result<List<HippocratesHealthcareProfessional>, Throwable> =
        dataService.find {
                sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun count(): Result<Int, Throwable> =
        useReadDatabase {
            dataService.count { all() }
        }

    override suspend fun countByToken(term: String): Result<Int, Throwable> =
        useReadDatabase {
            dataService.count {
                where { this.searchTokens.search(term) }
            }
        }

    override suspend fun findBySearchTokensAndRange(
        term: String,
        range: IntRange
    ): Result<List<HippocratesHealthcareProfessional>, Throwable> =
        dataService.find {
            where { this.searchTokens.search(term) }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }
}
