package br.com.alice.dragonradar.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.dragonradar.DragonRadarDomainClientConfiguration
import br.com.alice.dragonradar.SERVICE_NAME
import br.com.alice.dragonradar.client.CalculateEmergencyRecommendationService
import br.com.alice.dragonradar.client.CalculateEmergencyRecommendationServiceClient
import br.com.alice.dragonradar.client.EmergencyRecommendationProviderService
import br.com.alice.dragonradar.client.EmergencyRecommendationProviderServiceClient
import br.com.alice.dragonradar.client.EmergencyRecommendationService
import br.com.alice.dragonradar.client.EmergencyRecommendationServiceClient
import org.koin.core.qualifier.named
import org.koin.dsl.module

val DragonRadarDomainClientModule = module {

    val baseUrl = DragonRadarDomainClientConfiguration.baseUrl()
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 10_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<EmergencyRecommendationService> { EmergencyRecommendationServiceClient(invoker) }
    single<EmergencyRecommendationProviderService> { EmergencyRecommendationProviderServiceClient(invoker) }
    single<CalculateEmergencyRecommendationService> { CalculateEmergencyRecommendationServiceClient(invoker) }
}
