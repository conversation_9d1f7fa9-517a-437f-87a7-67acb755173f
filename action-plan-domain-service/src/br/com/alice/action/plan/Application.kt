package br.com.alice.action.plan

import br.com.alice.action.plan.ioc.DataLayerServiceModule
import br.com.alice.action.plan.ioc.ServiceModule
import br.com.alice.action.plan.metrics.Metrics.registerMetrics
import br.com.alice.action.plan.routes.apiRoutes
import br.com.alice.action.plan.routes.kafkaRoutes
import br.com.alice.action.plan.routes.recurringRoutes
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.ACTION_PLAN_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.server.application.Application
import io.ktor.server.routing.routing
import org.koin.core.module.Module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        ServiceModule,
        StaffDomainClientModule,
        AppointmentScheduleDomainClientModule,
        HealthConditionDomainClientModule,
        KafkaProducerModule,
        CoverageDomainClientModule,
        HealthPlanDomainClientModule
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, ACTION_PLAN_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
            recurringRoutes()
        }

        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.ALICE_APP,
            FeatureNamespace.QUESTIONNAIRE,
            FeatureNamespace.HEALTH_PLAN,
        )
        registerMetrics()
    }
}
