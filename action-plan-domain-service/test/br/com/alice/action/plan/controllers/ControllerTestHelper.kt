package br.com.alice.action.plan.controllers

import br.com.alice.action.plan.module
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.featureconfig.core.FeaturePopulateService
import io.ktor.server.application.Application
import io.mockk.clearAllMocks
import io.mockk.mockk
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import kotlin.test.BeforeTest

abstract class ControllerTestHelper : RoutesTestHelper() {

    private val featurePopulateService: FeaturePopulateService = mockk()

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { featurePopulateService } }
            )
        )
    }

    @BeforeTest
    open fun before() {
        clearAllMocks()
    }
}
