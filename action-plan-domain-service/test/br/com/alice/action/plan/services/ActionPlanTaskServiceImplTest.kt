package br.com.alice.action.plan.services

import br.com.alice.action.plan.client.ACTION_PLAN_TASK_DEFAULT_LIMIT
import br.com.alice.action.plan.client.ACTION_PLAN_TASK_DEFAULT_OFFSET
import br.com.alice.action.plan.converter.ActionPlanTasksTransportConverter
import br.com.alice.action.plan.model.ActionPlanTaskDetails
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.and
import br.com.alice.coverage.client.City
import br.com.alice.coverage.client.LocationService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskSourceType
import br.com.alice.data.layer.models.ActionPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.ActionPlanTaskStatus.ACTIVE_ON_GOING
import br.com.alice.data.layer.models.ActionPlanTaskStatus.DELETED
import br.com.alice.data.layer.models.ActionPlanTaskStatus.DELETED_BY_MEMBER
import br.com.alice.data.layer.models.ActionPlanTaskStatus.DELETED_BY_STAFF
import br.com.alice.data.layer.models.ActionPlanTaskStatus.DONE
import br.com.alice.data.layer.models.ActionPlanTaskStatus.EXPIRED
import br.com.alice.data.layer.models.ActionPlanTaskStatus.OVERDUE
import br.com.alice.data.layer.models.ActionPlanTaskStatus.SCHEDULED
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.ActionPlanTaskType.EMERGENCY
import br.com.alice.data.layer.models.ActionPlanTaskType.FOLLOW_UP_REQUEST
import br.com.alice.data.layer.models.ActionPlanTaskType.REFERRAL
import br.com.alice.data.layer.models.ActionPlanTaskType.SLEEP
import br.com.alice.data.layer.models.ActionPlanTaskType.SURGERY_PRESCRIPTION
import br.com.alice.data.layer.models.ActionPlanTaskType.TEST_REQUEST
import br.com.alice.data.layer.models.ActionPlanTaskUpdatedBy
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.STATUS_PERMITTED_BEFORE_SCHEDULED
import br.com.alice.data.layer.models.copy
import br.com.alice.data.layer.services.ActionPlanTaskDataService
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class ActionPlanTaskServiceImplTest {
    private val actionPlanTaskDataService: ActionPlanTaskDataService = mockk()
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService = mockk()
    private val locationService: LocationService = mockk()
    private val staffService: StaffService = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()
    private val caseRecordService: CaseRecordService = mockk()
    private val localDateTimeNow = LocalDateTime.now()

    private val actionPlanTaskService = ActionPlanTaskServiceImpl(
        actionPlanTaskDataService,
        healthPlanTaskGroupService,
        locationService,
        staffService,
        kafkaProducer,
        caseRecordService,
    )
    private val person = TestModelFactory.buildPerson()
    private val actionPlanTask = TestModelFactory.buildActionPlanTask(
        personId = person.id,
        status = ACTIVE,
        type = REFERRAL
    )
    private val producerResult = ProducerResult(LocalDateTime.now(), "topic", 100)

    private val actionPlanTasks = listOf(actionPlanTask)
    private val actionPlanTaskIds = actionPlanTasks.map { it.id }
    private val daysToExpireTask = 180

    @BeforeTest
    fun setup() {
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns localDateTimeNow
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#createFromHealthPlanTask should create task from healthPlanTask successfully`() = runBlocking {
        val healthPlanTask = TestModelFactory.buildHealthPlanTask(
            personId = person.id,
            status = HealthPlanTaskStatus.ACTIVE,
        )
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = healthPlanTask.id
        )

        coEvery { actionPlanTaskDataService.add(any()) } returns actionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns producerResult

        val result = actionPlanTaskService.createFromHealthPlanTask(healthPlanTask)
        assertThat(result).isSuccessWithData(actionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.add(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#updateFromHealthPlanTask should update task from healthPlanTask successfully`() = runBlocking {
        val healthPlanTask = TestModelFactory.buildHealthPlanTask(
            personId = person.id,
            status = HealthPlanTaskStatus.ACTIVE,
        )
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = healthPlanTask.id
        )

        coEvery { actionPlanTaskDataService.update(any()) } returns actionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns producerResult

        val result = actionPlanTaskService.updateFromHealthPlanTask(healthPlanTask, actionPlanTask)
        assertThat(result).isSuccessWithData(actionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#updateFromHealthPlanTask should update task from healthPlanTask successfully but maintaining the current status`() =
        runBlocking {
            val healthPlanTask = TestModelFactory.buildHealthPlanTask(
                personId = person.id,
                status = HealthPlanTaskStatus.ACTIVE,
            )
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                id = healthPlanTask.id
            )

            coEvery { actionPlanTaskDataService.update(any()) } returns actionPlanTask.success()

            coEvery {
                kafkaProducer.produce(any(), any())
            } returns producerResult

            val result = actionPlanTaskService.updateFromHealthPlanTask(healthPlanTask, actionPlanTask)
            assertThat(result).isSuccessWithData(actionPlanTask)

            coVerifyOnce { actionPlanTaskDataService.update(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
        }

    @Test
    fun `#getUnacknowlegedTasksByDate should return unacknowleged tasks by date`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            acknowledgedAt = null
        )
        val expectedResponse = listOf(actionPlanTask)

        val date = LocalDateTime.now()

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.createdAt.greaterEq(date))
                            .and(this.acknowledgeAt.isNull())
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }
                }
            )
        } returns expectedResponse.success()

        val result = actionPlanTaskService.getUnacknowlegedTasksByDate(person.id, date)
        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getMultipleTaskDetailsById should get taskDetails in details Format`() = runBlocking {
        val staff = TestModelFactory.buildStaff()
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            groupId = RangeUUID.generate(),
            staffId = staff.id,
            acknowledgedAt = LocalDateTime.now()
        )

        val taskInList = listOf(actionPlanTask)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(id.inList(listOf(actionPlanTask.id)))
                    }
                }
            )
        } returns taskInList.success()

        coEvery {
            locationService.getCities(emptyList())
        } returns emptyList<City>().success()

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns listOf(staff).success()

        val expected = ActionPlanTaskDetails(
            task = taskInList,
            staffs = listOf(staff),
            city = null,
        )

        val result = actionPlanTaskService.getMultipleTaskDetailsById(listOf(actionPlanTask.id), person.id)
        assertThat(result).isSuccessWithData(expected)

        coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        coVerifyNone { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#getMultipleTaskDetailsById should get taskDetails in details Format and acknowledge unacknowledgedTask`() =
        runBlocking {
            val mockNow = LocalDateTime.now()

            mockkStatic(LocalDateTime::class) {
                coEvery { LocalDateTime.now() } returns mockNow

                val staff = TestModelFactory.buildStaff()
                val actionPlanTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    groupId = RangeUUID.generate(),
                    staffId = staff.id,
                    acknowledgedAt = null,
                    date = mockNow
                )

                val taskInList = listOf(actionPlanTask)
                val acknowledgedTaskInList = taskInList.map { it.copy(acknowledgedAt = mockNow) }

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id)
                                    .and(id.inList(listOf(actionPlanTask.id)))
                            }
                        }
                    )
                } returns taskInList.success()

                coEvery {
                    actionPlanTaskDataService.updateList(any())
                } returns acknowledgedTaskInList.success()

                coEvery {
                    locationService.getCities(emptyList())
                } returns emptyList<City>().success()

                coEvery {
                    staffService.findByList(listOf(staff.id))
                } returns listOf(staff).success()

                coEvery {
                    kafkaProducer.produce(any(), any())
                } returns producerResult

                actionPlanTaskService.getMultipleTaskDetailsById(listOf(actionPlanTask.id), person.id)
            }

            coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
        }

    @Test
    fun `#getByPerson should get tasks (with SCHEDULED status) in transport Format`() = runBlocking {
        val staff = TestModelFactory.buildStaff()

        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            groupId = RangeUUID.generate(),
            staffId = staff.id,
            status = SCHEDULED
        )

        val group = HealthPlanTaskGroupTransport(
            id = RangeUUID.generate(),
            healthPlanId = actionPlanTask.healthPlanId,
            name = "Correr Maratona",
            personId = actionPlanTask.personId
        )

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id).and(this.status.inList(VALID_STATUSES))
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        coEvery {
            locationService.getCities(emptyList())
        } returns emptyList<City>().success()

        coEvery {
            healthPlanTaskGroupService.findByIds(listOf(actionPlanTask.groupId!!))
        } returns listOf(group).success()

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns listOf(staff).success()

        val taskInList = listOf(actionPlanTask)
        val associatedGroups = listOf(group).associateBy { it.id!! }
        val associatedStaffs = listOf(staff).associateBy { it.id }

        val mappedByTypeTasks = taskInList.groupBy { it.type }.mapValues { it.value }

        val actionPlanTransport = ActionPlanTasksTransportConverter.convert(
            mappedByTypeTasks,
            associatedStaffs,
            associatedGroups,
            emptyMap(),
        )

        val result = actionPlanTaskService.getByPerson(person.id)
        assertThat(result).isSuccessWithData(actionPlanTransport)
    }

    @Test
    fun `#markTasksAsOverdue should update tasks successfully marking them as overdue`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            deadline = Deadline(
                unit = PeriodUnit.DAY,
                date = LocalDateTime.now().minusDays(1),
                quantity = 1
            ),
        )
        val actionPlanTaskList = listOf(actionPlanTask)

        val actionPlanTaskOverdue = actionPlanTask.copy(status = OVERDUE)
        val actionPlanTaskOverdueList = listOf(actionPlanTaskOverdue)

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        } returns actionPlanTaskList.success()

        coEvery { actionPlanTaskDataService.updateList(any()) } returns actionPlanTaskOverdueList.success()

        val result = actionPlanTaskService.markTasksAsOverdue(person.id)
        assertThat(result).isSuccessWithData(actionPlanTaskOverdueList)

        coVerifyOnce {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
    }

    @Test
    fun `#markTasksAsOverdue should not update tasks marking them as overdue when list is empty`() = runBlocking {
        val mockNow = LocalDateTime.now()

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns mockNow

            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                deadline = Deadline(
                    unit = PeriodUnit.DAY,
                    date = mockNow,
                    quantity = 1
                ),
            )
            val actionPlanTaskList = listOf(actionPlanTask)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            } returns actionPlanTaskList.success()


            val result = actionPlanTaskService.markTasksAsOverdue(person.id)
            assertThat(result).isSuccessWithData(emptyList())

            coVerifyOnce {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            }
            coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        }
    }

    @Test
    fun `#markTasksAsOverdue should not update tasks marking them as overdue when query list is empty`() = runBlocking {
        val actionPlanTaskList = emptyList<ActionPlanTask>()


        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        } returns actionPlanTaskList.success()


        val result = actionPlanTaskService.markTasksAsOverdue(person.id)
        assertThat(result).isSuccessWithData(actionPlanTaskList)

        coVerifyOnce {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        }
        coVerifyNone { actionPlanTaskDataService.updateList(any()) }
    }


    @Test
    fun `#markTasksAsExpired should update tasks successfully marking them as expired`() =
        mockLocalDateTime { localDateTime ->
            val task = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                deadline = Deadline(
                    unit = PeriodUnit.DAY,
                    date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                    quantity = 1
                )
            )
            val tasks = listOf(task)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id) and
                                    this.status.inList(listOf(ACTIVE, OVERDUE))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            } returns tasks.success()

            val expiredTasks = tasks.map { it.copy(status = EXPIRED) }

            coEvery { actionPlanTaskDataService.updateList(any()) } returns expiredTasks.success()

            coEvery { kafkaProducer.produce(any(), any()) } returns mockk()

            val result = actionPlanTaskService.markTasksAsExpired(person.id)
            assertThat(result).isSuccessWithData(expiredTasks)

            coVerifyOnce { actionPlanTaskDataService.find(any()) }
            coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
        }

    @Test
    fun `#markTasksAsExpired should update tasks successfully marking them as expired using new deadline calculation flow`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(
                FeatureNamespace.HEALTH_PLAN, mapOf(
                    "should_use_new_health_plan_task_deadline_calculation_flow" to true,
                    "new_health_plan_task_deadline_calculation_flow_base_date" to "2030-01-01",
                )
            ) {
                val task = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    )
                )
                val tasks = listOf(task)

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(ACTIVE, OVERDUE))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                } returns tasks.success()

                val expiredTasks = tasks.map { it.copy(status = EXPIRED) }

                coEvery { actionPlanTaskDataService.updateList(any()) } returns expiredTasks.success()

                coEvery { kafkaProducer.produce(any(), any()) } returns mockk()

                val result = actionPlanTaskService.markTasksAsExpired(person.id)
                assertThat(result).isSuccessWithData(expiredTasks)

                coVerifyOnce { actionPlanTaskDataService.find(any()) }
                coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
                coVerifyOnce { kafkaProducer.produce(any(), any()) }
            }
        }

    @Test
    fun `#markTasksAsExpired should update tasks successfully marking them as expired using new deadline calculation flow using date`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(
                FeatureNamespace.HEALTH_PLAN, mapOf(
                    "should_use_new_health_plan_task_deadline_calculation_flow" to false,
                    "new_health_plan_task_deadline_calculation_flow_base_date" to localDateTime.toLocalDate()
                        .minusDays(2).toString(),
                )
            ) {
                val taskToExpire = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    )
                )
                val taskToIgnore = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    )
                ).copy(createdAt = localDateTime.minusDays(3))

                val tasks = listOf(taskToExpire, taskToIgnore)

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(ACTIVE, OVERDUE))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                } returns tasks.success()

                val expiredTasks = listOf(taskToExpire.copy(status = EXPIRED))

                coEvery { actionPlanTaskDataService.updateList(any()) } returns expiredTasks.success()

                coEvery { kafkaProducer.produce(any(), any()) } returns mockk()

                val result = actionPlanTaskService.markTasksAsExpired(person.id)
                assertThat(result).isSuccessWithData(expiredTasks)

                coVerifyOnce { actionPlanTaskDataService.find(any()) }
                coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
                coVerifyOnce { kafkaProducer.produce(any(), any()) }
            }
        }

    @Test
    fun `#markTasksAsExpired should update tasks successfully marking them as expired when needed`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlag(FeatureNamespace.ALICE_APP, "days_to_expire_task_after_due_date", daysToExpireTask) {
                val emergencyTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    ),
                    type = EMERGENCY
                )
                val followUpRequestActionPlanTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    type = FOLLOW_UP_REQUEST,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    ),
                )
                val surgeryActionPlanTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    type = SURGERY_PRESCRIPTION,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(30),
                        quantity = 1
                    ),
                )
                val activeTasks = listOf(
                    emergencyTask.copy(releasedAt = localDateTime.minusDays(5)),
                    followUpRequestActionPlanTask,
                    surgeryActionPlanTask
                )

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(ACTIVE, OVERDUE))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                } returns activeTasks.success()

                val expiredTasks = activeTasks
                    .filter { task -> task.deadline?.date?.isBefore(localDateTime.minusDays(60)) == true }
                    .let { tasks -> tasks.map { it.copy(status = EXPIRED) } }

                coEvery { actionPlanTaskDataService.updateList(any()) } returns expiredTasks.success()

                coEvery { kafkaProducer.produce(any(), any()) } returns mockk()

                val result = actionPlanTaskService.markTasksAsExpired(person.id)
                assertThat(result).isSuccessWithData(expiredTasks)
                assertThat(expiredTasks.any { it.type == SURGERY_PRESCRIPTION }).isFalse()

                coVerifyOnce { actionPlanTaskDataService.find(any()) }
                coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
                coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
            }
        }

    @Test
    fun `#markTasksAsExpired should update tasks successfully marking them as expired when type is SLEEP`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlag(FeatureNamespace.ALICE_APP, "days_to_expire_task_after_due_date", daysToExpireTask) {
                val task = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = localDateTime.minusDays(1 + daysToExpireTask.toLong()),
                        quantity = 1
                    ),
                    type = SLEEP
                )
                val tasks = listOf(task.copy(dueDate = localDateTime.toLocalDate().minusDays(32)))

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id) and
                                        this.status.inList(listOf(ACTIVE, OVERDUE))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                } returns tasks.success()

                val expiredTasks = tasks.map { it.copy(status = EXPIRED) }

                coEvery { actionPlanTaskDataService.updateList(any()) } returns expiredTasks.success()

                coEvery { kafkaProducer.produce(any(), any()) } returns mockk()

                val result = actionPlanTaskService.markTasksAsExpired(person.id)
                assertThat(result).isSuccessWithData(expiredTasks)

                coVerifyOnce { actionPlanTaskDataService.find(any()) }
                coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
                coVerifyOnce { kafkaProducer.produce(any(), any()) }
            }
        }

    @Test
    fun `#markTasksAsExpired should not update tasks successfully marking them as expired when tasks should not be expired`() =
        runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                deadline = Deadline(
                    unit = PeriodUnit.DAY,
                    date = LocalDateTime.now().minusDays(daysToExpireTask.toLong() - 1),
                    quantity = 1
                )
            )
            val actionPlanTaskList = listOf(actionPlanTask)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            } returns actionPlanTaskList.success()

            val result = actionPlanTaskService.markTasksAsExpired(person.id)
            assertThat(result).isSuccessWithData(emptyList())

            coVerifyOnce { actionPlanTaskDataService.find(any()) }
            coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        }

    @Test
    fun `#markTasksAsExpired should not update tasks successfully marking them as expired when tasks should not be expired when type is EMERGENCY`() =
        runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                deadline = Deadline(
                    unit = PeriodUnit.DAY,
                    date = LocalDateTime.now().minusDays(daysToExpireTask.toLong() - 1),
                    quantity = 1
                ),
                type = EMERGENCY
            )
            val actionPlanTaskList = listOf(actionPlanTask)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            } returns actionPlanTaskList.success()

            val result = actionPlanTaskService.markTasksAsExpired(person.id)
            assertThat(result).isSuccessWithData(emptyList())

            coVerifyOnce { actionPlanTaskDataService.find(any()) }
            coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        }

    @Test
    fun `#markTasksAsExpired should not update tasks marking them as overdue when list is empty`() = runBlocking {
        val mockNow = LocalDateTime.now()

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns mockNow

            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                deadline = Deadline(
                    unit = PeriodUnit.DAY,
                    date = mockNow.plusDays(30),
                    quantity = 1
                ),
            )
            val actionPlanTaskList = listOf(actionPlanTask)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            } returns actionPlanTaskList.success()


            val result = actionPlanTaskService.markTasksAsExpired(person.id)
            assertThat(result).isSuccessWithData(emptyList())

            coVerifyOnce {
                actionPlanTaskDataService.find(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                        }.sortOrder {
                            desc
                        }
                    }
                )
            }
            coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        }
    }

    @Test
    fun `#markTasksAsExpired should not update tasks marking them as overdue when list is empty when type is EMERGENCY`() =
        runBlocking {
            val mockNow = LocalDateTime.now()

            mockkStatic(LocalDateTime::class) {
                coEvery { LocalDateTime.now() } returns mockNow

                val actionPlanTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    deadline = Deadline(
                        unit = PeriodUnit.DAY,
                        date = mockNow.plusDays(30),
                        quantity = 1
                    ),
                    type = EMERGENCY
                )
                val actionPlanTaskList = listOf(actionPlanTask)

                coEvery {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id)
                                    .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                } returns actionPlanTaskList.success()


                val result = actionPlanTaskService.markTasksAsExpired(person.id)
                assertThat(result).isSuccessWithData(emptyList())

                coVerifyOnce {
                    actionPlanTaskDataService.find(
                        queryEq {
                            where {
                                this.personId.eq(person.id)
                                    .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                            }.sortOrder {
                                desc
                            }
                        }
                    )
                }
                coVerifyNone { actionPlanTaskDataService.updateList(any()) }
            }
        }

    @Test
    fun `#markTasksAsExpired should not update tasks marking them as overdue when query list is empty`() = runBlocking {
        val actionPlanTaskList = emptyList<ActionPlanTask>()

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        } returns actionPlanTaskList.success()


        val result = actionPlanTaskService.markTasksAsExpired(person.id)
        assertThat(result).isSuccessWithData(actionPlanTaskList)

        coVerifyOnce {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.inList(listOf(ACTIVE, OVERDUE)))
                    }.sortOrder {
                        desc
                    }
                }
            )
        }
        coVerifyNone { actionPlanTaskDataService.updateList(any()) }
    }

    @Test
    fun `#setAsActive should set actionPlanTask as Active`() = runBlocking {
        val scheduledAt = LocalDateTime.now()
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = SCHEDULED,
            scheduledAt = scheduledAt
        )

        val activeActionPlanTask = actionPlanTask.copy(
            status = ACTIVE,
            scheduledAt = null
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()
        coEvery {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id
                        && it.status == ACTIVE
                        && it.scheduledAt == null
            })
        } returns activeActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns producerResult

        val result = actionPlanTaskService.setAsActive(actionPlanTask.id)
        assertThat(result).isSuccessWithData(activeActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyOnce {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id
                        && it.status == ACTIVE
                        && it.scheduledAt == null
            })
        }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#setAsActive should throw error when task into database is not scheduled`() = runBlocking {
        val scheduledAt = LocalDateTime.now()
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = DONE,
            scheduledAt = scheduledAt
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        val result = actionPlanTaskService.setAsActive(actionPlanTask.id)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyNone { actionPlanTaskDataService.update(any()) }
        coVerifyNone { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#setAsActiveOnGoing should throw error when task into database is not active`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = DONE,
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        val result = actionPlanTaskService.setAsActiveOnGoing(actionPlanTask.id)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyNone { actionPlanTaskDataService.update(any()) }
        coVerifyNone { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#setAsActiveOnGoing should throw error when task into database is not referral`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = DONE,
            type = SLEEP
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        val result = actionPlanTaskService.setAsActiveOnGoing(actionPlanTask.id)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyNone { actionPlanTaskDataService.update(any()) }
        coVerifyNone { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#setAsActiveOnGoing should throw error when task into database is active but don't have multiple sessions`() =
        runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = ACTIVE,
                content = mapOf("sessionsQuantity" to 1)
            )

            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.setAsActiveOnGoing(actionPlanTask.id)
            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
            coVerifyNone { actionPlanTaskDataService.update(any()) }
            coVerifyNone { kafkaProducer.produce(any(), any()) }
        }

    @Test
    fun `#setAsActiveOnGoing should update task with multiple sessions and active status`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE,
            content = mapOf("sessionsQuantity" to 10)
        )

        val actionOnGoingTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE_ON_GOING,
            content = mapOf("sessionsQuantity" to 10)
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        coEvery {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id && it.status == ACTIVE_ON_GOING
            })
        } returns actionOnGoingTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns producerResult

        val result = actionPlanTaskService.setAsActiveOnGoing(actionPlanTask.id)
        assertThat(result).isSuccess()

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyOnce { actionPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#setAsActiveOnGoing should update task with multiple sessions and schedule status`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = SCHEDULED,
            scheduledAt = LocalDateTime.now(),
            content = mapOf("sessionsQuantity" to 10)
        )

        val actionOnGoingTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE_ON_GOING,
            content = mapOf("sessionsQuantity" to 10)
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        coEvery {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id && it.status == ACTIVE_ON_GOING
            })
        } returns actionOnGoingTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns producerResult

        val result = actionPlanTaskService.setAsActiveOnGoing(actionPlanTask.id)
        assertThat(result).isSuccess()

        coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
        coVerifyOnce { actionPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#getSessionsQuantityFromReferralTaskById get sessionsQuantity from actionPlanTask when it is referral and has sessionsQuantity`() =
        runBlocking {
            val expectedSessionsQuantity = 10
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = SCHEDULED,
                scheduledAt = LocalDateTime.now(),
                content = mapOf("sessionsQuantity" to expectedSessionsQuantity),
                type = REFERRAL
            )


            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.getSessionsQuantityFromReferralTaskById(actionPlanTask.id)
            assertThat(result).isSuccessWithData(expectedSessionsQuantity)
        }

    @Test
    fun `#getSessionsQuantityFromReferralTaskById do not get sessionsQuantity from actionPlanTask when it is not referral`() =
        runBlocking {
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = ACTIVE,
                scheduledAt = LocalDateTime.now(),
                type = ActionPlanTaskType.EATING
            )


            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.getSessionsQuantityFromReferralTaskById(actionPlanTask.id)
            assertThat(result).isFailureOfType(InvalidArgumentException::class)
        }

    @Test
    fun `#getSessionsQuantityFromReferralTaskById get sessionsQuantity equals 1 from actionPlanTask when it is referral and has sessionsQuantity null`() =
        runBlocking {
            val expectedSessionsQuantity = 1
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = SCHEDULED,
                scheduledAt = LocalDateTime.now(),
                type = REFERRAL
            )


            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.getSessionsQuantityFromReferralTaskById(actionPlanTask.id)
            assertThat(result).isSuccessWithData(expectedSessionsQuantity)
        }

    @Test
    fun `#getSessionsQuantityFromReferralTaskById get sessionsQuantity equals 1 from actionPlanTask when it is referral and has sessionsQuantity equals 1`() =
        runBlocking {
            val expectedSessionsQuantity = 1
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = SCHEDULED,
                scheduledAt = LocalDateTime.now(),
                content = mapOf("sessionsQuantity" to expectedSessionsQuantity),
                type = REFERRAL
            )


            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.getSessionsQuantityFromReferralTaskById(actionPlanTask.id)
            assertThat(result).isSuccessWithData(expectedSessionsQuantity)
        }

    @Test
    fun `#getReferralTaskById get referral task when it is referral`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = SCHEDULED,
            scheduledAt = LocalDateTime.now(),
            content = mapOf("sessionsQuantity" to 1),
            type = REFERRAL
        )
        val expectedResult = actionPlanTask.specialize<ReferralNew>()


        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        val result = actionPlanTaskService.getReferralTaskById(actionPlanTask.id)
        assertThat(result).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#getReferralTaskById should get error when it is not referral`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE,
            scheduledAt = LocalDateTime.now(),
            type = ActionPlanTaskType.EATING
        )

        coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

        val result = actionPlanTaskService.getReferralTaskById(actionPlanTask.id)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)
    }

    @Test
    fun `#getHighlightTask get highlight scheduled task`() = runBlocking {

        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = RangeUUID.generate(),
            scheduledAt = localDateTimeNow,
            status = SCHEDULED
        )

        coEvery {
            actionPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(SCHEDULED))
                            .and(this.scheduledAt.isNotNull())
                            .and(this.scheduledAt.greaterEq(localDateTimeNow))
                    }.orderBy { this.scheduledAt }.sortOrder { asc }
                }
            )
        } returns actionPlanTask.success()

        val result = actionPlanTaskService.getHighlightTask(person.id)

        assertThat(result).isSuccessWithData(listOf(actionPlanTask))

        coVerifyOnce { actionPlanTaskDataService.findOne(any()) }
        coVerifyNone { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getHighlightTask get highlight recent active task`() = runBlocking {

        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = RangeUUID.generate(),
            status = ACTIVE,
            date = localDateTimeNow
        )

        coEvery {
            actionPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(SCHEDULED))
                            .and(this.scheduledAt.isNotNull())
                            .and(this.scheduledAt.greaterEq(localDateTimeNow))
                    }.orderBy { this.scheduledAt }.sortOrder { asc }
                }
            )
        } returns NotFoundException("not_found").failure()

        coEvery {
            actionPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(ACTIVE))
                            .and(this.createdAt.greaterEq(localDateTimeNow.minusDays(15)))
                    }.orderBy { this.createdAt }.sortOrder { desc }
                }
            )
        } returns actionPlanTask.success()

        val result = actionPlanTaskService.getHighlightTask(person.id)

        assertThat(result).isSuccessWithData(listOf(actionPlanTask))

        coVerify(exactly = 2) { actionPlanTaskDataService.findOne(any()) }
        coVerifyNone { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getHighlightTask get test request highlight task`() = runBlocking {

        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = RangeUUID.generate(),
            status = ACTIVE,
            date = localDateTimeNow,
            type = TEST_REQUEST
        )

        val testRequestTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            id = RangeUUID.generate(),
            status = ACTIVE,
            date = localDateTimeNow,
            type = TEST_REQUEST
        )

        coEvery {
            actionPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(SCHEDULED))
                            .and(this.scheduledAt.isNotNull())
                            .and(this.scheduledAt.greaterEq(localDateTimeNow))
                    }.orderBy { this.scheduledAt }.sortOrder { asc }
                }
            )
        } returns NotFoundException("not_found").failure()

        coEvery {
            actionPlanTaskDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(ACTIVE))
                            .and(this.createdAt.greaterEq(localDateTimeNow.minusDays(15)))
                    }.orderBy { this.createdAt }.sortOrder { desc }
                }
            )
        } returns actionPlanTask.success()

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(status.inList(listOf(ACTIVE, SCHEDULED)))
                            .and(this.type.eq(actionPlanTask.type))
                    }.orderBy { this.createdAt }.sortOrder { desc }
                }
            )
        } returns listOf(testRequestTask).success()

        val result = actionPlanTaskService.getHighlightTask(person.id)

        assertThat(result).isSuccessWithData(listOf(actionPlanTask, testRequestTask))

        coVerify(exactly = 2) { actionPlanTaskDataService.findOne(any()) }
        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#favorite - returns error if fails to find tasks by ids`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(any())
        } returns Exception("").failure()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isFailure()

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - ignore task if status DELETED`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - ignore task if status DELETED_BY_STAFF`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED_BY_STAFF)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - ignore task if status DELETED_BY_MEMBER`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED_BY_MEMBER)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - ignore task if has already been favorite`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = true)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - favorite task if has correct status and not favorite before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = false)
        val expectedActionPlanTask = listOf(actionPlanTask.copy(favorite = true))

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.favorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - favorite one in batch if has correct status and has been favorite before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = false)
        val actionPlanTask2 = TestModelFactory.buildActionPlanTask(status = ACTIVE, favorite = true)
        val expectedActionPlanTask = listOf(
            actionPlanTask.copy(favorite = true)
        )

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id, actionPlanTask2.id))
                    }
                }
            )
        } returns listOf(actionPlanTask, actionPlanTask2).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.favorite(
            listOf(actionPlanTask.id, actionPlanTask2.id)
        )

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#favorite - favorite tasks in batch if has correct status and not favorite before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = false)
        val actionPlanTask2 = TestModelFactory.buildActionPlanTask(status = ACTIVE, favorite = false)
        val expectedActionPlanTask = listOf(
            actionPlanTask.copy(favorite = true), actionPlanTask2.copy(favorite = true)
        )

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id, actionPlanTask2.id))
                    }
                }
            )
        } returns listOf(actionPlanTask, actionPlanTask2).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.favorite(
            listOf(actionPlanTask.id, actionPlanTask2.id)
        )

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - returns error if fails to find tasks by ids`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(any())
        } returns Exception("").failure()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isFailure()

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - ignore task if status DELETED`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - ignore task if status DELETED_BY_STAFF`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED_BY_STAFF)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - ignore task if status DELETED_BY_MEMBER`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = DELETED_BY_MEMBER)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - ignore task if has already been favorite`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = false)
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(emptyList())

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyNone { kafkaProducer.produce(any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - remove fav task if has correct status and favorite before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = true)
        val expectedActionPlanTask = listOf(actionPlanTask.copy(favorite = false))

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id))
                    }
                }
            )
        } returns listOf(actionPlanTask).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { !it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.unfavorite(listOf(actionPlanTask.id))

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - favorite one in batch if has correct status and has been removed before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = true)
        val actionPlanTask2 = TestModelFactory.buildActionPlanTask(status = ACTIVE, favorite = false)
        val expectedActionPlanTask = listOf(
            actionPlanTask.copy(favorite = false)
        )

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id, actionPlanTask2.id))
                    }
                }
            )
        } returns listOf(actionPlanTask, actionPlanTask2).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { !it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.unfavorite(
            listOf(actionPlanTask.id, actionPlanTask2.id)
        )

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#unFavorite - remove fav tasks in batch if has correct status and favorite before`() = runBlocking {
        val actionPlanTask = actionPlanTask.copy(status = ACTIVE, favorite = true)
        val actionPlanTask2 = TestModelFactory.buildActionPlanTask(status = ACTIVE, favorite = true)
        val expectedActionPlanTask = listOf(
            actionPlanTask.copy(favorite = false), actionPlanTask2.copy(favorite = false)
        )

        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        id.inList(listOf(actionPlanTask.id, actionPlanTask2.id))
                    }
                }
            )
        } returns listOf(actionPlanTask, actionPlanTask2).success()

        coEvery {
            actionPlanTaskDataService.updateList(
                match { result ->
                    result.all { !it.favorite }
                }
            )
        } returns expectedActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val actual = actionPlanTaskService.unfavorite(
            listOf(actionPlanTask.id, actionPlanTask2.id)
        )

        assertThat(actual).isSuccessWithData(expectedActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
        coVerifyOnce { actionPlanTaskDataService.updateList(any()) }
        coVerify(exactly = 2) { kafkaProducer.produce(any(), any()) }
        confirmVerified(actionPlanTaskDataService, kafkaProducer)
    }

    @Test
    fun `#countHighlightTasks should count highlight tasks`() = runBlocking {
        val count = 10
        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id) and
                                this.status.eq(SCHEDULED) and
                                this.scheduledAt.greaterEq(LocalDateTime.now())

                    }
                }
            )
        } returns count.success()

        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id) and
                                this.status.eq(ACTIVE) and
                                this.createdAt.greaterEq(LocalDateTime.now().minusDays(15))

                    }
                }
            )
        } returns count.success()

        val result = actionPlanTaskService.countHighlightTasks(person.id)

        assertThat(result).isSuccessWithData(count + count)

        coVerify(exactly = 2) { actionPlanTaskDataService.count(any()) }
    }


    @Nested
    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    inner class TaskUpdatedToScheduleStatus {
        @ParameterizedTest
        @MethodSource("statusPermittedBeforeScheduled")
        fun `#setAsScheduled should set task as scheduled`(actionPlanTask: ActionPlanTask) = runBlocking {
            val scheduledAt = LocalDateTime.now()
            val scheduledActionPlanTask = actionPlanTask.copy(
                status = SCHEDULED,
                scheduledAt = scheduledAt
            )

            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()
            coEvery {
                actionPlanTaskDataService.update(match {
                    it.id == actionPlanTask.id
                            && it.status == SCHEDULED
                            && it.scheduledAt == scheduledAt
                })
            } returns scheduledActionPlanTask.success()

            coEvery {
                kafkaProducer.produce(any(), any())
            } returns producerResult

            val result = actionPlanTaskService.setAsScheduled(actionPlanTask.id, scheduledAt)
            assertThat(result).isSuccessWithData(scheduledActionPlanTask)

            coVerifyOnce { actionPlanTaskDataService.get(any()) }
            coVerifyOnce { actionPlanTaskDataService.update(any()) }
            coVerifyOnce { kafkaProducer.produce(any(), any()) }
        }

        private fun statusPermittedBeforeScheduled() = STATUS_PERMITTED_BEFORE_SCHEDULED.map {
            TestModelFactory.buildActionPlanTask(status = it)
        }

        @Test
        fun `#setAsScheduled should throw error when task into database is not active or scheduled`() = runBlocking {
            val scheduledAt = LocalDateTime.now()
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                status = DONE,
                scheduledAt = scheduledAt
            )

            coEvery { actionPlanTaskDataService.get(actionPlanTask.id) } returns actionPlanTask.success()

            val result = actionPlanTaskService.setAsScheduled(actionPlanTask.id, scheduledAt)
            assertThat(result).isFailureOfType(InvalidArgumentException::class)

            coVerifyOnce { actionPlanTaskDataService.get(actionPlanTask.id) }
            coVerifyNone { actionPlanTaskDataService.update(any()) }
            coVerifyNone { kafkaProducer.produce(any(), any()) }
        }
    }

    @Test
    fun `#getTasksByFilters returns to do tasks with default pagination`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        ACTIVE,
                                        SCHEDULED,
                                        OVERDUE,
                                        ACTIVE_ON_GOING,
                                    )
                                )
                            )
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds
            ).withTodoStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns history tasks with default pagination`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        EXPIRED,
                                        DONE,
                                        DELETED_BY_MEMBER
                                    )
                                )
                            )
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds
            ).withInactiveStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns to do and favorites tasks with default pagination`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        ACTIVE,
                                        SCHEDULED,
                                        OVERDUE,
                                        ACTIVE_ON_GOING,
                                    )
                                )
                            )
                            .and(this.favorite.eq(true))
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds,
                favorite = true
            ).withTodoStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns to do tasks filtered by type with default pagination`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        ACTIVE,
                                        SCHEDULED,
                                        OVERDUE,
                                        ACTIVE_ON_GOING,
                                    )
                                )
                            )
                            .and(this.type.inList(listOf(TEST_REQUEST)))
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds,
                types = listOf(TEST_REQUEST)
            ).withTodoStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns tasks using filters dueDate, types and statuses`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.status.inList(listOf(OVERDUE))
                            .and(this.type.inList(listOf(REFERRAL)))
                            .and(this.dueDate.lessEq(LocalDate.now().minusDays(180L)))
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                dueDateLessEq = LocalDate.now().minusDays(180L),
                types = listOf(REFERRAL),
                statuses = listOf(OVERDUE)
            )
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns tasks using filters releasedAt, types and statuses`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.status.inList(listOf(OVERDUE))
                            .and(this.type.inList(listOf(REFERRAL)))
                            .and(this.releasedAt.lessEq(LocalDateTime.now().minusDays(180L)))
                    }.orderBy {
                        createdAt
                    }.sortOrder {
                        desc
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                releasedAtLessEq = LocalDateTime.now().minusDays(180L),
                types = listOf(REFERRAL),
                statuses = listOf(OVERDUE)
            )
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#getTasksByFilters returns to do tasks when order by is FALSE`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        ACTIVE,
                                        SCHEDULED,
                                        OVERDUE,
                                        ACTIVE_ON_GOING,
                                    )
                                )
                            )
                    }.offset {
                        ACTION_PLAN_TASK_DEFAULT_OFFSET
                    }.limit {
                        ACTION_PLAN_TASK_DEFAULT_LIMIT
                    }
                }
            )
        } returns actionPlanTasks.success()

        val result = actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds,
                shouldSort = false
            ).withTodoStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks)

        coVerifyOnce { actionPlanTaskDataService.find(any()) }
    }

    @Test
    fun `#countTasksByFilters returns count for to do tasks with all filters`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        ACTIVE,
                                        SCHEDULED,
                                        OVERDUE,
                                        ACTIVE_ON_GOING,
                                    )
                                )
                            )
                            .and(this.favorite.eq(true))
                            .and(this.type.inList(listOf(TEST_REQUEST)))
                    }
                }
            )
        } returns actionPlanTasks.size.success()

        val result = actionPlanTaskService.countTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds,
                favorite = true,
                types = listOf(TEST_REQUEST)
            ).withTodoStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks.size)

        coVerifyOnce { actionPlanTaskDataService.count(any()) }
    }

    @Test
    fun `#countTasksByFilters returns count for inactive tasks`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(actionPlanTaskIds))
                            .and(
                                this.status.inList(
                                    listOf(
                                        EXPIRED,
                                        DONE,
                                        DELETED_BY_MEMBER
                                    )
                                )
                            )
                    }
                }
            )
        } returns actionPlanTasks.size.success()

        val result = actionPlanTaskService.countTasksByFilters(
            ActionPlanTaskFilters(
                personId = person.id,
                taskIds = actionPlanTaskIds
            ).withInactiveStatuses()
        )

        assertThat(result).isSuccessWithData(actionPlanTasks.size)

        coVerifyOnce { actionPlanTaskDataService.count(any()) }
    }

    @Test
    fun `#countTasksByFilters returns count filters by types, statuses and dueDate`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.status.inList(listOf(OVERDUE))
                            .and(this.type.inList(listOf(REFERRAL)))
                            .and(this.dueDate.lessEq(LocalDate.now().minusDays(180L)))
                    }
                }
            )
        } returns actionPlanTasks.size.success()

        val result = actionPlanTaskService.countTasksByFilters(
            ActionPlanTaskFilters(
                types = listOf(REFERRAL),
                statuses = listOf(OVERDUE),
                dueDateLessEq = LocalDate.now().minusDays(180L)
            )
        )

        assertThat(result).isSuccessWithData(actionPlanTasks.size)

        coVerifyOnce { actionPlanTaskDataService.count(any()) }
    }

    @Test
    fun `#countTasksByFilters returns count filters by releasedAt, statuses and types`() = runBlocking {
        coEvery {
            actionPlanTaskDataService.count(
                queryEq {
                    where {
                        this.status.inList(listOf(OVERDUE))
                            .and(this.type.inList(listOf(REFERRAL)))
                            .and(this.releasedAt.lessEq(LocalDateTime.now().minusDays(180L)))
                    }
                }
            )
        } returns actionPlanTasks.size.success()

        val result = actionPlanTaskService.countTasksByFilters(
            ActionPlanTaskFilters(
                types = listOf(REFERRAL),
                statuses = listOf(OVERDUE),
                releasedAtLessEq = LocalDateTime.now().minusDays(180L)

            )
        )

        assertThat(result).isSuccessWithData(actionPlanTasks.size)

        coVerifyOnce { actionPlanTaskDataService.count(any()) }
    }

    @Test
    fun `#expireTask should expire task`() = runBlocking {
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE,
            type = EMERGENCY
        )

        val expiredActionPlanTask = actionPlanTask.copy(status = EXPIRED)

        coEvery {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id
                        && it.status == EXPIRED
            })
        } returns expiredActionPlanTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val result = actionPlanTaskService.expireTask(actionPlanTask)
        assertThat(result).isSuccessWithData(expiredActionPlanTask)

        coVerifyOnce { actionPlanTaskDataService.update(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#countTasksByType should return correct task counts grouped by type`() = runBlocking {
        val expectedCounts = listOf(
            CountByValues(listOf(REFERRAL.name), 5),
            CountByValues(listOf(EMERGENCY.name), 3),
        )

        val taskIds = listOf(
            RangeUUID.generate(),
            RangeUUID.generate(),
        )

        val dueDateLessEq = LocalDate.now()
        val releasedAtLessEq = LocalDateTime.now().minusDays(1)

        coEvery {
            actionPlanTaskDataService.countGrouped(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.id.inList(taskIds))
                            .and(this.status.inList(ActionPlanTaskFilters.TO_DO_STATUSES))
                            .and(this.favorite.eq(true))
                            .and(this.type.inList(ActionPlanTaskType.values().toList()))
                            .and(this.dueDate.lessEq(dueDateLessEq))
                            .and(this.releasedAt.lessEq(releasedAtLessEq))
                    }.groupBy { listOf(this.type) }
                }
            )
        } returns expectedCounts.success()

        val result = actionPlanTaskService.countTasksByType(
            ActionPlanTaskFilters(
                taskIds = taskIds,
                personId = person.id,
                types = ActionPlanTaskType.values().toList(),
                statuses = ActionPlanTaskFilters.TO_DO_STATUSES,
                limit = 100,
                offset = 2,
                favorite = true,
                dueDateLessEq = dueDateLessEq,
                releasedAtLessEq = releasedAtLessEq,
            )
        )

        assertThat(result).isSuccessWithData(expectedCounts)

        coVerifyOnce { actionPlanTaskDataService.countGrouped(any()) }
    }

    @Test
    fun `#createQuestionnaireTask should create action-plan-task with specified params`() = runBlocking {
        val demandName = "Test Demand"
        val sourceId = RangeUUID.generate().toString()
        val caseRecord = TestModelFactory.buildCaseRecord()
        val endAt = LocalDate.now().plusDays(10)
        val diseaseDetails = DiseaseDetails(
            id = caseRecord.description.id.toString(),
            type = caseRecord.description.type,
            value = caseRecord.description.value,
            description = caseRecord.description.description,
        )
        val now = LocalDateTime.now()

        val expectedTask = ActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            title = "Atualização sobre caso de saúde",
            description = "Olá! Gostaria de saber como você tem se sentido em relação ao seu quadro de $demandName.",
            content = mapOf(
                "healthFormAnswerSource" to mapOf(
                    "id" to sourceId,
                    "type" to "OUTCOME_REQUEST_SCHEDULING",
                ),
                "questionnaireKey" to "Q_KEY",
            ),
            dueDate = endAt,
            status = ACTIVE,
            lastRequesterStaffId = caseRecord.responsibleStaffId,
            type = ActionPlanTaskType.QUESTIONNAIRE,
            releasedAt = now,
            createdAt = now,
            updatedAt = now,
            createdBy = ActionPlanTaskUpdatedBy(source = ActionPlanTaskSourceType.SYSTEM),
            favorite = false,
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    caseId = caseRecord.caseId,
                    severity = caseRecord.severity,
                    description = diseaseDetails,
                    observation = caseRecord.observation,
                    seriousness = caseRecord.seriousness,
                    cipes = null,
                )
            ),
            version = 0,
            originalUpdatedAt = now,
        )

        coEvery {
            caseRecordService.getByIds(listOf(caseRecord.id))
        } returns listOf(caseRecord).success()

        coEvery {
            actionPlanTaskDataService.add(match {
                it.personId == person.id &&
                        it.title == expectedTask.title &&
                        it.description == expectedTask.description &&
                        it.content == expectedTask.content &&
                        it.dueDate == expectedTask.dueDate &&
                        it.status == expectedTask.status &&
                        it.lastRequesterStaffId == expectedTask.lastRequesterStaffId &&
                        it.type == expectedTask.type &&
                        it.createdBy == expectedTask.createdBy &&
                        it.caseRecordDetails == expectedTask.caseRecordDetails
            })
        } returns expectedTask.success()

        coEvery {
            kafkaProducer.produce(any(), any())
        } returns mockk()

        val result = actionPlanTaskService.createQuestionnaireTask(
            personId = person.id,
            sourceId = sourceId,
            sourceType = HealthFormAnswerSourceType.OUTCOME_REQUEST_SCHEDULING,
            questionnaireKey = "Q_KEY",
            demandName = demandName,
            caseRecordIds = setOf(caseRecord.id),
            endAt = endAt,
        )

        assertThat(result).isSuccess()

        coVerifyOnce { caseRecordService.getByIds(any()) }
        coVerifyOnce { actionPlanTaskDataService.add(any()) }
        coVerifyOnce { kafkaProducer.produce(any(), any()) }
    }

    @Test
    fun `#getByQuestionnaireKey should call data service with provider param and return ActionPlanTask`() =
        runBlocking {
            val questionnaireKey = "Q_KEY"
            val healthFormAnswerSource = HealthFormAnswerSource(
                id = RangeUUID.generate().toString(),
                type = HealthFormAnswerSourceType.OUTCOME_REQUEST_SCHEDULING,
            )
            val actionPlanTask = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                type = ActionPlanTaskType.QUESTIONNAIRE,
                content = mapOf("questionnaireKey" to questionnaireKey)
            )

            coEvery {
                actionPlanTaskDataService.findOne(
                    queryEq {
                        where {
                            this.personId.eq(person.id)
                                .and(this.type.eq(ActionPlanTaskType.QUESTIONNAIRE))
                                .and(this.questionnaireKey.eq(questionnaireKey))
                                .and(this.healthFormAnswerSource.eq(healthFormAnswerSource))
                                .and(this.status.eq(ACTIVE))
                        }
                    }
                )
            } returns actionPlanTask.success()

            val result = actionPlanTaskService.getByQuestionnaireKeyAndSource(
                personId = person.id,
                questionnaireKey = questionnaireKey,
                source = healthFormAnswerSource,
            )

            assertThat(result).isSuccessWithData(actionPlanTask)

            coVerifyOnce { actionPlanTaskDataService.findOne(any()) }
        }

    @Test
    fun `#markAsDone should mark task as done and update related fields`() = runBlocking {
        val now = LocalDateTime.now()
        val actionPlanTask = TestModelFactory.buildActionPlanTask(
            personId = person.id,
            status = ACTIVE
        )

        val doneActionPlanTask = actionPlanTask.copy(
            status = DONE,
            finishedAt = now,
            finishedBy = ActionPlanTaskUpdatedBy(source = ActionPlanTaskSourceType.SYSTEM),
        )

        coEvery {
            actionPlanTaskDataService.get(actionPlanTask.id)
        } returns actionPlanTask.success()

        coEvery {
            actionPlanTaskDataService.update(match {
                it.id == actionPlanTask.id
                        && it.status == DONE
                        && it.finishedAt == now
                        && it.finishedBy == ActionPlanTaskUpdatedBy(source = ActionPlanTaskSourceType.SYSTEM)
            })
        } returns doneActionPlanTask.success()

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns now

            val result = actionPlanTaskService.markAsDone(actionPlanTask.id)
            assertThat(result).isSuccessWithData(doneActionPlanTask)
        }

        coVerifyOnce { actionPlanTaskDataService.update(any()) }
    }

    @Test
    fun `#removeAppointmentScheduleFromTasks given non existent appointment schedule id when update tasks then anyone task is updated`() =
        runBlocking {
            // given
            val appointmentScheduleId = RangeUUID.generate()
            coEvery {
                actionPlanTaskDataService.find(
                    queryEq { where { this.appointmentScheduleId.eq(appointmentScheduleId) } }
                )
            } returns emptyList<ActionPlanTask>().success()

            //when
            val result = actionPlanTaskService.removeAppointmentScheduleFromTasks(appointmentScheduleId)

            //then
            assertThat(result).isSuccessWithData(emptyList())
            coVerifyNone { actionPlanTaskDataService.updateList(any()) }
        }

    @Test
    fun `#removeAppointmentScheduleFromTasks given existent appointment schedule id when remove then update tasks`() =
        runBlocking {
            // given
            val appointmentScheduleId = RangeUUID.generate()
            val actionPlanTask = TestModelFactory.buildActionPlanTask(appointmentScheduleId = appointmentScheduleId)

            coEvery {
                actionPlanTaskDataService.find(
                    queryEq { where { this.appointmentScheduleId.eq(appointmentScheduleId) } }
                )
            } returns listOf(actionPlanTask).success()


            coEvery {
                actionPlanTaskDataService.updateList(any(), false)
            } returns listOf(actionPlanTask).success()

            // when
            actionPlanTaskService.removeAppointmentScheduleFromTasks(appointmentScheduleId)

            // then
            coVerifyOnce { actionPlanTaskDataService.updateList(any(), false) }
        }

}
