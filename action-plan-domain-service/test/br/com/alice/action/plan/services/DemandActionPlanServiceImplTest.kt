package br.com.alice.action.plan.services

import br.com.alice.action.plan.client.DEMAND_ACTION_PLAN_DEFAULT_LIMIT
import br.com.alice.action.plan.client.DEMAND_ACTION_PLAN_DEFAULT_OFFSET
import br.com.alice.action.plan.model.DemandActionPlanFilters
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.DemandActionPlanReferenceModel
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.services.DemandActionPlanDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class DemandActionPlanServiceImplTest {
    private val demandActionPlanDataService: DemandActionPlanDataService = mockk()

    private val demandActionPlanServiceImpl = DemandActionPlanServiceImpl(demandActionPlanDataService)

    private val person = TestModelFactory.buildPerson()
    private val caseRecordDetails = CaseRecordDetails(
        description = DiseaseDetails(
            type = Disease.Type.CID_10,
            value = "10",
            description = "Unha Encravada",
        ),
        severity = CaseSeverity.ONGOING,
        caseId = RangeUUID.generate(),
    )

    private val actionPlanTask = TestModelFactory.buildActionPlanTask(
        personId = person.id,
        caseRecordDetails = listOf(caseRecordDetails)
    )

    private val healthCondition = TestModelFactory.buildHealthCondition(
        memberFriendlyName = "Testadona"
    )

    private val healthConditionGroup = TestModelFactory.buildHealthConditionGroup(
        name = "Nome Amigável",
        healthConditionIds = listOf(healthCondition.id)
    )

    private val demandActionPlan = TestModelFactory.buildDemandActionPlan(
        personId = person.id,
        referencedModelId = healthConditionGroup.id,
        referencedModelClass = DemandActionPlanReferenceModel.HEALTH_DEMAND,
        description = healthConditionGroup.name,
        relatedTaskIds = listOf(actionPlanTask.id),
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(demandActionPlanDataService)

    @Test
    fun `#findActiveByPersonPaginated should find demandActionPlan by person with default pagination attributes`() = runBlocking {
        val demandActionPlans = listOf(demandActionPlan)

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(demandActionPlan.personId)
                            .and(this.status.eq(DemandActionPlanStatus.ACTIVE))
                    }.orderBy {
                        lastTaskCreatedAt
                    }.sortOrder {
                        desc
                    }.offset {
                        DEMAND_ACTION_PLAN_DEFAULT_OFFSET
                    }.limit {
                        DEMAND_ACTION_PLAN_DEFAULT_LIMIT
                    }
                }
            )
        } returns demandActionPlans.success()

        val result = demandActionPlanServiceImpl.findActiveByPersonPaginated(demandActionPlan.personId)
        assertThat(result).isSuccessWithData(demandActionPlans)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#findActiveByPersonPaginated should find demandActionPlan by person`() = runBlocking {
        val demandActionPlans = listOf(demandActionPlan)
        val limit = 10
        val page = 2
        val offset = limit * page

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(demandActionPlan.personId)
                            .and(this.status.eq(DemandActionPlanStatus.ACTIVE))
                    }.orderBy {
                        lastTaskCreatedAt
                    }.sortOrder {
                        desc
                    }.offset {
                        offset
                    }.limit {
                        limit
                    }
                }
            )
        } returns demandActionPlans.success()

        val result = demandActionPlanServiceImpl.findActiveByPersonPaginated(
            demandActionPlan.personId,
            offset = offset,
            limit = limit,
        )
        assertThat(result).isSuccessWithData(demandActionPlans)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#getHealthDemands should find demandActionPlan from HEALTH_DEMANDs with default pagination attributes`() = runBlocking {
        val demandActionPlans = listOf(demandActionPlan)

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.referencedModelClass.eq(DemandActionPlanReferenceModel.HEALTH_DEMAND)
                    }.offset {
                        DEMAND_ACTION_PLAN_DEFAULT_OFFSET
                    }.limit {
                        DEMAND_ACTION_PLAN_DEFAULT_LIMIT
                    }
                }
            )
        } returns demandActionPlans.success()

        val result = demandActionPlanServiceImpl.getHealthDemands()
        assertThat(result).isSuccessWithData(demandActionPlans)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#getHealthDemands should find demandActionPlan from HEALTH_DEMANDs`() = runBlocking {
        val demandActionPlans = listOf(demandActionPlan)
        val limit = 10
        val page = 2
        val offset = limit * page

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.referencedModelClass.eq(DemandActionPlanReferenceModel.HEALTH_DEMAND)
                    }.offset {
                        offset
                    }.limit {
                        limit
                    }
                }
            )
        } returns demandActionPlans.success()

        val result = demandActionPlanServiceImpl.getHealthDemands(
            offset = offset,
            limit = limit,
        )
        assertThat(result).isSuccessWithData(demandActionPlans)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#get should get demandActionPlan by Id`() = runBlocking {
        coEvery {
            demandActionPlanDataService.get(demandActionPlan.id)
        } returns demandActionPlan.success()

        val result = demandActionPlanServiceImpl.get(demandActionPlan.id)
        assertThat(result).isSuccessWithData(demandActionPlan)

        coVerifyOnce { demandActionPlanDataService.get(any()) }
    }

    @Test
    fun `#findInactiveByPersonPaginated should list demands`() = runBlocking {
        val demands = listOf(demandActionPlan)

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(DemandActionPlanStatus.DONE))
                    }.orderBy {
                        lastTaskCreatedAt
                    }.sortOrder {
                        desc
                    }.offset {
                        DEMAND_ACTION_PLAN_DEFAULT_OFFSET
                    }.limit {
                        DEMAND_ACTION_PLAN_DEFAULT_LIMIT
                    }
                }
            )
        } returns demands.success()

        val result = demandActionPlanServiceImpl.findInactiveByPersonPaginated(person.id)

        assertThat(result).isSuccessWithData(demands)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#findActiveByPersonPaginated should list demands`() = runBlocking {
        val demands = listOf(demandActionPlan)

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                            .and(this.status.eq(DemandActionPlanStatus.ACTIVE))
                    }.orderBy {
                        lastTaskCreatedAt
                    }.sortOrder {
                        desc
                    }.offset {
                        DEMAND_ACTION_PLAN_DEFAULT_OFFSET
                    }.limit {
                        DEMAND_ACTION_PLAN_DEFAULT_LIMIT
                    }
                }
            )
        } returns demands.success()

        val result = demandActionPlanServiceImpl.findActiveByPersonPaginated(person.id)

        assertThat(result).isSuccessWithData(demands)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#countByFilters should count demands with all filters`() = runBlocking {
        coEvery {
            demandActionPlanDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id) and this.status.inList(listOf(DemandActionPlanStatus.ACTIVE))
                    }
                }
            )
        } returns 1.success()

        val result = demandActionPlanServiceImpl.countByFilters(
            DemandActionPlanFilters(
                personId = person.id,
                statuses = listOf(DemandActionPlanStatus.ACTIVE)
            )
        )

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { demandActionPlanDataService.count(any()) }
    }

    @Test
    fun `#countByFilters should count demands with required filter`() = runBlocking {
        coEvery {
            demandActionPlanDataService.count(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                    }
                }
            )
        } returns 1.success()

        val result = demandActionPlanServiceImpl.countByFilters(
            DemandActionPlanFilters(
                personId = person.id
            )
        )

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { demandActionPlanDataService.count(any()) }
    }

    @Test
    fun `#findByFilters should find demands with all filters`() = runBlocking {
        val demands = listOf(demandActionPlan)
        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id) and this.status.inList(listOf(DemandActionPlanStatus.ACTIVE))
                    }
                }
            )
        } returns demands.success()

        val result = demandActionPlanServiceImpl.findByFilters(
            DemandActionPlanFilters(
                personId = person.id,
                statuses = listOf(DemandActionPlanStatus.ACTIVE)
            )
        )

        assertThat(result).isSuccessWithData(demands)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#findByFilters should find demands with required filter`() = runBlocking {
        val demands = listOf(demandActionPlan)
        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                    }
                }
            )
        } returns demands.success()

        val result = demandActionPlanServiceImpl.findByFilters(
            DemandActionPlanFilters(
                personId = person.id,
                statuses = listOf()
            )
        )

        assertThat(result).isSuccessWithData(demands)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }

    @Test
    fun `#findByPersonIdLimited should find demandActionPlan by person with default pagination attributes`() = runBlocking {
        val demandActionPlans = listOf(demandActionPlan)

        coEvery {
            demandActionPlanDataService.find(
                queryEq {
                    where {
                        this.personId.eq(demandActionPlan.personId)
                    }.orderBy {
                        lastTaskCreatedAt
                    }.sortOrder {
                        desc
                    }.offset {
                        DEMAND_ACTION_PLAN_DEFAULT_OFFSET
                    }.limit {
                        DEMAND_ACTION_PLAN_DEFAULT_LIMIT
                    }
                }
            )
        } returns demandActionPlans.success()

        val result = demandActionPlanServiceImpl.findByPersonIdLimited(demandActionPlan.personId)
        assertThat(result).isSuccessWithData(demandActionPlans)

        coVerifyOnce { demandActionPlanDataService.find(any()) }
    }
}
