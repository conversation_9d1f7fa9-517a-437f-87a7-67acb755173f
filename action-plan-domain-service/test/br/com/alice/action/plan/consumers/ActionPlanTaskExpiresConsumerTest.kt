package br.com.alice.action.plan.consumers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.notifier.ActionPlanTaskExpiresEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ActionPlanTaskExpiresConsumerTest: ConsumerTest() {
    private val actionPlanTaskService: ActionPlanTaskService = mockk()
    private val actionPlanTaskExpiresConsumer = ActionPlanTaskExpiresConsumer(actionPlanTaskService)

    @Test
    fun `expiresTasks should expire tasks`() = runBlocking {
        val task = TestModelFactory.buildActionPlanTask()
        val event = ActionPlanTaskExpiresEvent(task)
        coEvery { actionPlanTaskService.expireTask(task) } returns task.success()

        val response = actionPlanTaskExpiresConsumer.expiresTasks(event)

        assertThat(response).isSuccessWithData(task)

        coVerifyOnce { actionPlanTaskService.expireTask(task) }
    }
}
