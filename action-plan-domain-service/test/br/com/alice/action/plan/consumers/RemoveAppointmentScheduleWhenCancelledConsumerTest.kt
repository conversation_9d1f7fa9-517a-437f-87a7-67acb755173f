package br.com.alice.action.plan.consumers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class RemoveAppointmentScheduleWhenCancelledConsumerTest : ConsumerTest() {

    private val actionPlanTaskService: ActionPlanTaskService = mockk()
    private val consumer = RemoveAppointmentScheduleWhenCancelledConsumer(actionPlanTaskService)

    @Test
    fun `given an event when handle then remove schedule from tasks`() {
        // Given
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule = TestModelFactory.buildAppointmentSchedule())

        coEvery {
            actionPlanTaskService.removeAppointmentScheduleFromTasks(
                appointmentScheduleId = event.payload.id,
                newStatus = ActionPlanTaskStatus.ACTIVE
            )
        } returns listOf(TestModelFactory.buildActionPlanTask())

        // When
        val result = runBlocking { consumer.handle(event) }

        // Then
        assertThat(result).isSuccess()

        coVerifyOnce {
            actionPlanTaskService.removeAppointmentScheduleFromTasks(
                appointmentScheduleId = event.payload.id,
                newStatus = ActionPlanTaskStatus.ACTIVE
            )
        }
    }

    @Test
    fun `given an event when action plan returns error then fail it`() {
        // Given
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule = TestModelFactory.buildAppointmentSchedule())

        coEvery {
            actionPlanTaskService.removeAppointmentScheduleFromTasks(
                appointmentScheduleId = event.payload.id,
                newStatus = ActionPlanTaskStatus.ACTIVE
            )
        } returns Exception()

        // When
        val result = runBlocking { consumer.handle(event) }

        // Then
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce {
            actionPlanTaskService.removeAppointmentScheduleFromTasks(
                appointmentScheduleId = event.payload.id,
                newStatus = ActionPlanTaskStatus.ACTIVE
            )
        }
    }
}
