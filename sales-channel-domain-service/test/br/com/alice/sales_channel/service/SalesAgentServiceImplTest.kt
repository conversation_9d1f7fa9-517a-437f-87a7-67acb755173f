package br.com.alice.sales_channel.br.com.alice.sales_channel.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.data.layer.models.SalesFirmStaff
import br.com.alice.data.layer.models.SalesFirmStaffRole
import br.com.alice.data.layer.services.SalesAgentDataService
import br.com.alice.sales_channel.events.SalesAgentUpdatedEvent
import br.com.alice.sales_channel.exceptions.NoActiveSaleAgentsOnCampaignException
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesAgentServiceImpl
import br.com.alice.sales_channel.service.SalesFirmStaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class SalesAgentServiceImplTest {
    private val salesAgentDataService: SalesAgentDataService = mockk()
    private val salesFirmStaffService: SalesFirmStaffService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val salesAgentService: SalesAgentService = SalesAgentServiceImpl(salesAgentDataService, kafkaProducerService, salesFirmStaffService)

    private val salesAgent = getSalesAgent()
    private val salesAgentList = listOf(salesAgent)
    private val range = IntRange(0, 9)

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#add should create and then produce SalesAgentCreatedEvent`() = runBlocking {
        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(salesAgent.email)
                        .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                }
            })
        } returns listOf<SalesAgent>().success()
        coEvery {
            salesFirmStaffService.getByEmail(salesAgent.email)
        } returns NotFoundException().failure()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns null
        coEvery {
            salesAgentDataService.add(salesAgent)
        } returns salesAgent.success()
        coEvery {
            kafkaProducerService.produce(any())
        } returns mockk()

        salesAgentService.add(salesAgent)
        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
        coVerifyOnce { salesFirmStaffService.getByEmail(any()) }
        coVerifyOnce { salesAgentDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#add should return SalesAgentAlreadyExistsException when other sales agent already exists with email provided`() = runBlocking {
        val mockExistentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), phoneNumber = "***********")

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(salesAgent.email)
                        .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                }
            })
        } returns listOf(mockExistentSalesAgent).success()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns null

        val result = salesAgentService.add(salesAgent)

        assertThat(result.failure().message).isEqualTo("Sales agent email already exists")

        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#add should return SalesAgentAlreadyExistsException when sales agent already exists with phoneNumber provided`() = runBlocking {
        val mockExistentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), email = "<EMAIL>")

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(salesAgent.email)
                        .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                }
            })
        } returns listOf(mockExistentSalesAgent).success()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns null

        val result = salesAgentService.add(salesAgent)

        assertThat(result.failure().message).isEqualTo("Sales agent phone number already exists")

        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#add should return SalesAgentAlreadyExistsException when sales agent already exists with email and phoneNumber provided`() = runBlocking {
        val mockExistentSalesAgent = salesAgent.copy(id = RangeUUID.generate())

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(salesAgent.email)
                        .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                }
            })
        } returns listOf(mockExistentSalesAgent).success()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns null

        val result = salesAgentService.add(salesAgent)

        assertThat(result.failure().message).isEqualTo("Sales agent email and phone number already exists")

        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#add should return SalesAgentEmailDuplicationException when a sales firm staff already exists with email provided`() = runBlocking {
        val mockExistentSalesFirmStaff = SalesFirmStaff(
            salesFirmId = RangeUUID.generate(),
            firstName = "Firm Staff Name",
            lastName = "Firm Staf LastName",
            email = salesAgent.email,
            role = SalesFirmStaffRole.MAIN_STAFF
        )

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(salesAgent.email)
                        .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                }
            })
        } returns listOf<SalesAgent>().success()
        coEvery {
            salesFirmStaffService.getByEmail(salesAgent.email)
        } returns mockExistentSalesFirmStaff.success()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns null

        val result = salesAgentService.add(salesAgent)

        assertThat(result.failure().message).isEqualTo("Sales agent email already exists")

        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesFirmStaffService.getByEmail(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
    }

    @Test
    fun `#search should return a list of sales agent`() = runBlocking {
        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.searchTokens.search(salesAgent.name)
                }.offset { range.first }.limit { range.count() }
            })
        } returns salesAgentList.success()

        salesAgentService.search(
            searchToken = salesAgent.name,
            range = range,
            salesFirmId = null,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#search should return a list of sales agent filtered by sales firm id when sales firm id is not null`() = runBlocking {
        val (agent1) = listOf(getSalesAgent(), getSalesAgent())

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.salesFirmId.eq(agent1.salesFirmId) }.offset { range.first }.limit { range.count()
                }
            })
        } returns listOf(agent1).success()

        salesAgentService.search(
            searchToken = "",
            range = range,
            salesFirmId = agent1.salesFirmId,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#search should return a list of sales agent filtered by sales firm id when sales firm id is null and sales agent ids is not null`() = runBlocking {
        val (agent1) = listOf(getSalesAgent(), getSalesAgent())

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.id.inList(listOf(agent1.id)) }.offset { range.first }.limit { range.count()
                }
            })
        } returns listOf(agent1).success()

        salesAgentService.search(
            searchToken = "",
            range = range,
            salesFirmId = null,
            salesAgentIds = listOf(agent1.id),
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#search should return a list of sales agent filtered by sales firm id and search token when sales firm id is not nul and sales agent ids is null`() = runBlocking {
        val (agent1) = listOf(getSalesAgent(), getSalesAgent())
        val searchToken = agent1.name

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.salesFirmId
                        .eq(agent1.salesFirmId)
                        .and(this.searchTokens.search(searchToken))
                }
                    .offset { range.first }.limit { range.count() }
            })
        } returns listOf(agent1).success()

        salesAgentService.search(
            searchToken = searchToken,
            range = range,
            salesFirmId = agent1.salesFirmId,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#search should return a list of sales agent filtered by sales firm id and search token when sales firm id is null and sales agent ids is not null`() = runBlocking {
        val (agent1) = listOf(getSalesAgent(), getSalesAgent())
        val searchToken = agent1.name

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.id
                        .inList(listOf(agent1.id))
                        .and(this.searchTokens.search(searchToken))
                }
                    .offset { range.first }.limit { range.count() }
            })
        } returns listOf(agent1).success()

        salesAgentService.search(
            searchToken = searchToken,
            range = range,
            salesFirmId = null,
            salesAgentIds = listOf(agent1.id),
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#search should return a list of sales agent filtered by sales firm id and search token when sales firm id is not null and sales agent ids is not null`() = runBlocking {
        val (agent1) = listOf(getSalesAgent(), getSalesAgent())
        val searchToken = agent1.name

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.salesFirmId
                        .eq(agent1.salesFirmId)
                        .and(this.searchTokens.search(searchToken))
                }
                    .offset { range.first }.limit { range.count() }
            })
        } returns listOf(agent1).success()

        salesAgentService.search(
            searchToken = searchToken,
            range = range,
            salesFirmId = agent1.salesFirmId,
            salesAgentIds = listOf(agent1.id),
        )
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#searchCount should return total filtered sales agent`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.searchTokens.search(salesAgent.name)
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = salesAgent.name,
            salesFirmId = null,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#searchCount should return total filtered by sales firm id when sales firm is not null and sales agent ids is null`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.salesFirmId.eq(salesAgent.salesFirmId)
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = "",
            salesFirmId = salesAgent.salesFirmId,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#searchCount should return total filtered by sales firm id when sales firm is null and sales agent ids is not null`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.id.inList(listOf(salesAgent.id))
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = "",
            salesFirmId = null,
            salesAgentIds = listOf(salesAgent.id),
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#searchCount should return total filtered by sales firm id and search token when sales firm id is not null and sales agent ids is null`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.salesFirmId.eq(salesAgent.salesFirmId).and(this.searchTokens.search(salesAgent.name))
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = salesAgent.name,
            salesFirmId = salesAgent.salesFirmId,
            salesAgentIds = null,
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#searchCount should return total filtered by sales firm id and search token when sales firm id is null and sales agent ids is not null`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.id.inList(listOf(salesAgent.id)).and(this.searchTokens.search(salesAgent.name))
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = salesAgent.name,
            salesFirmId = null,
            salesAgentIds = listOf(salesAgent.id),
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#searchCount should return total filtered by sales firm id and search token when sales firm id is not null and sales agent ids is not null`() = runBlocking {
        coEvery {
            salesAgentDataService.count(queryEq {
                where {
                    this.id.inList(listOf(salesAgent.id)).and(this.searchTokens.search(salesAgent.name))
                }
            })
        } returns salesAgentList.count().success()

        salesAgentService.searchCount(
            searchToken = salesAgent.name,
            salesFirmId = salesAgent.salesFirmId,
            salesAgentIds = listOf(salesAgent.id),
        )
        coVerifyOnce { salesAgentDataService.count(any()) }
    }

    @Test
    fun `#get should return a sales agent`() = runBlocking {

        coEvery {
            salesAgentDataService.findOne(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns salesAgent.success()

        salesAgentService.get(salesAgent.id)
        coVerifyOnce { salesAgentDataService.findOne(any()) }
    }

    @Test
    fun `#listByFirmId should return a list of sales agents given firmId`() = runBlocking {

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.salesFirmId.eq(salesAgent.salesFirmId)
                }
            })
        } returns listOf(salesAgent).success()

        salesAgentService.listByFirmId(salesAgent.salesFirmId)
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#getByIds should return a list of sales agents by id`() = runBlocking {
        val ids = listOf(RangeUUID.generate(), RangeUUID.generate())

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.id.inList(ids)
                }
            })
        } returns listOf(salesAgent).success()

        salesAgentService.getByIds(ids)
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update should return an updated sales agent`() = runBlocking {
        val newSalesAgent = salesAgent.copy(name = "Alice Jr")

        withFeatureFlags(
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_everyone" to false,
            ),
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_sales_firm_list" to listOf(salesAgent.salesFirmId),
            ),
        ) {
            coEvery {
                salesAgentDataService.find(queryEq {
                    where {
                        this.email.eq(salesAgent.email)
                            .or(this.phoneNumber.eq(salesAgent.phoneNumber))
                    }
                })
            } returns listOf<SalesAgent>().success()

            coEvery {
                salesAgentDataService.findOneOrNull(queryEq {
                    where {
                        this.id.eq(salesAgent.id)
                    }
                })
            } returns salesAgent

            coEvery {
                salesAgentDataService.findOne(queryEq {
                    where {
                        this.id.eq(salesAgent.id)
                    }
                })
            } returns salesAgent.success()

            coEvery {
                salesFirmStaffService.getByEmail(salesAgent.email)
            } returns NotFoundException().failure()

            coEvery {
                salesAgentDataService.update(newSalesAgent)
            } returns newSalesAgent.success()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()

            salesAgentService.update(newSalesAgent)

            coVerifyOnce { salesAgentDataService.find(any()) }
            coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
            coVerifyOnce { salesAgentDataService.update(any()) }
            coVerifyOnce { salesFirmStaffService.getByEmail(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update should not proceed if the new email provided already exists`() = runBlocking {
        val newSalesAgent = salesAgent.copy(name = "Alice Jr", email = "<EMAIL>")
        val existentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), email = "<EMAIL>", phoneNumber = "***********")

        coEvery {
            salesAgentDataService.find(queryEq {
                where {
                    this.email.eq(newSalesAgent.email)
                        .or(this.phoneNumber.eq(newSalesAgent.phoneNumber))
                }
            })
        } returns listOf(existentSalesAgent).success()
        coEvery {
            salesAgentDataService.findOneOrNull(queryEq {
                where {
                    this.id.eq(salesAgent.id)
                }
            })
        } returns salesAgent

        val result = salesAgentService.update(newSalesAgent)

        assertThat(result.failure().message).isEqualTo("Sales agent email already exists")

        coVerifyOnce { salesAgentDataService.find(any()) }
        coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update should not proceed if the new phone number provided already exists`() = runBlocking {
        val newSalesAgent = salesAgent.copy(name = "Alice Jr", phoneNumber = "***********")

        withFeatureFlags(
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_everyone" to false,
            ),
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_sales_firm_list" to listOf(newSalesAgent.salesFirmId),
            ),
        ) {
            val existentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), phoneNumber = "***********", email = "<EMAIL>")

            coEvery {
                salesAgentDataService.find(queryEq {
                    where {
                        this.email.eq(newSalesAgent.email)
                            .or(this.phoneNumber.eq(newSalesAgent.phoneNumber))
                    }
                })
            } returns listOf(existentSalesAgent).success()
            coEvery {
                salesAgentDataService.findOneOrNull(queryEq {
                    where {
                        this.id.eq(salesAgent.id)
                    }
                })
            } returns salesAgent

            val result = salesAgentService.update(newSalesAgent)

            assertThat(result.failure().message).isEqualTo("Sales agent phone number already exists")

            coVerifyOnce { salesAgentDataService.find(any()) }
            coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update should not proceed if the new email and new phone number provided already exists`() = runBlocking {
        val newSalesAgent = salesAgent.copy(name = "Alice Jr", email = "<EMAIL>", phoneNumber = "***********")

        withFeatureFlags(
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_everyone" to false,
            ),
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_sales_firm_list" to listOf(newSalesAgent.salesFirmId),
            ),
        ) {
            val existentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), email = "<EMAIL>", phoneNumber = "***********")

            coEvery {
                salesAgentDataService.find(queryEq {
                    where {
                        this.email.eq(newSalesAgent.email)
                            .or(this.phoneNumber.eq(newSalesAgent.phoneNumber))
                    }
                })
            } returns listOf(existentSalesAgent).success()
            coEvery {
                salesAgentDataService.findOneOrNull(queryEq {
                    where {
                        this.id.eq(salesAgent.id)
                    }
                })
            } returns salesAgent

            val result = salesAgentService.update(newSalesAgent)

            assertThat(result.failure().message).isEqualTo("Sales agent email and phone number already exists")

            coVerifyOnce { salesAgentDataService.find(any()) }
            coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update should not proceed if the new email and new phone number provided already exists, allowing validation for all companies`() = runBlocking {
        val newSalesAgent = salesAgent.copy(name = "Alice Jr", email = "<EMAIL>", phoneNumber = "***********")

        withFeatureFlags(
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_everyone" to true,
            ),
            FeatureNamespace.BUSINESS to mapOf(
                "internal_create_deal_for_sales_firm_list" to emptyList<String>(),
            ),
        ) {
            val existentSalesAgent = salesAgent.copy(id = RangeUUID.generate(), email = "<EMAIL>", phoneNumber = "***********")

            coEvery {
                salesAgentDataService.find(queryEq {
                    where {
                        this.email.eq(newSalesAgent.email)
                            .or(this.phoneNumber.eq(newSalesAgent.phoneNumber))
                    }
                })
            } returns listOf(existentSalesAgent).success()
            coEvery {
                salesAgentDataService.findOneOrNull(queryEq {
                    where {
                        this.id.eq(salesAgent.id)
                    }
                })
            } returns salesAgent

            val result = salesAgentService.update(newSalesAgent)

            assertThat(result.failure().message).isEqualTo("Sales agent email and phone number already exists")

            coVerifyOnce { salesAgentDataService.find(any()) }
            coVerifyOnce { salesAgentDataService.findOneOrNull(any()) }
        }
    }

    @Test
    fun `#getNextThatShouldReceiveLead should return all sales agents that are active in campaign`() = runBlocking {
        coEvery {
            salesAgentDataService.findOne(queryEq {
                where {
                    this.isActiveInCampaign.eq(true)
                }.orderByList(
                    {
                        listOf(
                            this.leadsFromCampaignRoundField,
                            this.totalLeadsFromCampaignField,
                        )
                    },
                    {
                        listOf(SortOrder.Ascending, SortOrder.Ascending)
                    }
                )
            })
        } returns salesAgent.success()

        salesAgentService.getNextThatShouldReceiveLead()
        coVerifyOnce { salesAgentDataService.findOne(any()) }
    }

    @Test
    fun `#distributeLeads should give the lead to the sales agent with the lowest number of leads in the round`() = runBlocking {
        val salesAgent1 = TestModelFactory.buildSalesAgent(
            name = "Panqueca",
        ).copy(
            leadsFromCampaignRound = 0,
            totalLeadsFromCampaign = 2,
        )

        coEvery {
            salesAgentService.getNextThatShouldReceiveLead()
        } returns salesAgent1.success()

        val result = salesAgentService.distributeLeads()

        ResultAssert.assertThat(result).isSuccessWithData(salesAgent1)

        coVerifyOnce { salesAgentService.getNextThatShouldReceiveLead() }
    }

    @Test
    fun `#distributeLeads should return NoActiveSaleAgentsOnCampaignException if there is no sales agent active in campaign`() = runBlocking {
        coEvery {
            salesAgentService.getNextThatShouldReceiveLead()
        } returns NotFoundException().failure()

        val result = salesAgentService.distributeLeads()
        ResultAssert.assertThat(result).isFailureOfType(NoActiveSaleAgentsOnCampaignException::class)

        coVerifyOnce { salesAgentService.getNextThatShouldReceiveLead() }
    }

    @Test
    fun `#updateSalesAgentLeadNumbers should update successfully`() = runBlocking {
        val salesAgent1 = TestModelFactory.buildSalesAgent(
            name = "Panqueca",
        ).copy(
            leadsFromCampaignRound = 0,
            totalLeadsFromCampaign = 2,
        )
        val salesAgentUpdated = salesAgent1.copy(
            leadsFromCampaignRound = 1,
            totalLeadsFromCampaign = 3,
        )

        coEvery {
            salesAgentDataService.update(salesAgentUpdated)
        } returns salesAgentUpdated.success()
        coEvery {
            salesAgentDataService.findOne(queryEq {
                where {
                    this.id.eq(salesAgent1.id)
                }
            })
        } returns salesAgent.success()
        coEvery {
            salesAgentDataService.update(salesAgentUpdated)
        } returns salesAgentUpdated.success()
        coEvery {
            kafkaProducerService.produce(any<SalesAgentUpdatedEvent>())
        } returns mockk()

        val result = salesAgentService.updateSalesAgentLeadNumbers(
            salesAgent1,
        ).get()

        assertEquals(salesAgentUpdated, result)

        coVerifyOnce { salesAgentDataService.update(any())}
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateSalesAgentLeadNumbers should throw the exception when an error occurs on update`() = runBlocking {
        val salesAgent1 = TestModelFactory.buildSalesAgent(
            name = "Panqueca",
        ).copy(
            leadsFromCampaignRound = 0,
            totalLeadsFromCampaign = 2,
        )
        val salesAgentUpdated = salesAgent1.copy(
            leadsFromCampaignRound = 1,
            totalLeadsFromCampaign = 3,
        )

        coEvery {
            salesAgentDataService.update(salesAgentUpdated)
        } returns NotFoundException().failure()

        coEvery {
            salesAgentDataService.findOne(queryEq {
                where {
                    this.id.eq(salesAgent1.id)
                }
            })
        } returns salesAgent.success()

        assertFailsWith<NotFoundException> {
            salesAgentService.updateSalesAgentLeadNumbers(
                salesAgent1,
            ).get()
        }

        coVerifyOnce { salesAgentDataService.update(any())}
    }

    @Test
    fun `#resetLeadTotalNumber should set leadsFromCampaignRound to zero for all sales agents active on campaign`() = runBlocking {
        val salesAgent = TestModelFactory
            .buildSalesAgent()
            .copy(
                totalLeadsFromCampaign = 4,
                leadsFromCampaignRound = 1,
                isActiveInCampaign = true,
            )
        val salesAgentUpdated = salesAgent.copy(leadsFromCampaignRound = 0)

        coEvery {
            salesAgentDataService.find(
                queryEq {
                    where {
                        this.isActiveInCampaign.eq(true)
                    }
                }
            )
        } returns listOf(salesAgent).success()
        coEvery {
            salesAgentDataService.update(salesAgentUpdated)
        } returns salesAgentUpdated.success()

        val result = salesAgentService.resetLeadTotalNumber(salesAgent)
        ResultAssert.assertThat(result).isSuccessWithData(listOf(salesAgentUpdated))

        coVerifyOnce { salesAgentDataService.update(any()) }
        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#resetLeadTotalNumber should not set leadsFromCampaignRound to zero since new sales agent is not on campaign`() = runBlocking {
        val salesAgent = TestModelFactory
            .buildSalesAgent()
            .copy(
                totalLeadsFromCampaign = 0,
                leadsFromCampaignRound = 0,
                isActiveInCampaign = false,
            )

        val result = salesAgentService.resetLeadTotalNumber(salesAgent)
        ResultAssert.assertThat(result).isSuccessWithData(emptyList())

        coVerifyNone { salesAgentDataService.update(any()) }
        coVerifyNone { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#getByEmail should get the sales agent by email`() = runBlocking {
        val salesAgent = TestModelFactory
            .buildSalesAgent()

        coEvery {
            salesAgentDataService.findOne(
                queryEq {
                    where {
                        this.email.inList(listOf(salesAgent.email, salesAgent.email.uppercase(), salesAgent.email.lowercase()))
                    }
                }
            )
        } returns salesAgent.success()

        val result = salesAgentService.getByEmail(salesAgent.email)
        ResultAssert.assertThat(result).isSuccessWithData(salesAgent)

        coVerifyOnce { salesAgentDataService.findOne(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#getByRange should get by limit and offset`() = runBlocking {
        val limit = 10
        val offset = 0

        val salesAgent = TestModelFactory
            .buildSalesAgent()

        coEvery {
            salesAgentDataService.find(
                queryEq {
                    all()
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { offset }
                        .limit { limit }
                }
            )
        } returns listOf(salesAgent).success()

        val result = salesAgentService.getByRange(
            offset = offset,
            limit = limit,
        )
        ResultAssert.assertThat(result).isSuccessWithData(listOf(salesAgent))

        coVerifyOnce { salesAgentDataService.find(any()) }
    }

    @Test
    fun `#resetCampaign should reset counters and remove all brokers from the current campaign`() = runBlocking {
        coEvery {
            salesAgentDataService.find(
                queryEq {
                    where { this.isActiveInCampaign.eq(true) }
                }
            )
        } returns listOf(salesAgent).success()

        salesAgentService.resetCampaign()
        coVerifyOnce { salesAgentDataService.update(any()) }
    }

    private fun getSalesAgent(): SalesAgent {
        return SalesAgent(
            name = "Alice Silva",
            documentNumber = "00100100111",
            birthDate = LocalDate.of(2000, 1, 1),
            salesFirmId = RangeUUID.generate(),
            email = "<EMAIL>",
            phoneNumber = "11988344567"
        )
    }
}
