package br.com.alice.sales_channel.br.com.alice.sales_channel.controller

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.data.layer.models.SalesFirmAgentPartnership
import br.com.alice.sales_channel.controller.BackfillRequest
import br.com.alice.sales_channel.controller.BackfillSalesAgentController
import br.com.alice.sales_channel.controller.SalesAgentLeadsNumberRequest
import br.com.alice.sales_channel.model.SalesAgentRequest
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesFirmAgentPartnershipService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillSalesAgentControllerTest():  ControllerTestHelper() {
    private val salesAgentService: SalesAgentService = mockk()
    private val salesFirmAgentPartnershipService: SalesFirmAgentPartnershipService = mockk()
    private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()

    private val backfillSalesAgentController = BackfillSalesAgentController(salesAgentService, salesFirmAgentPartnershipService, ongoingCompanyDealService)

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(salesAgentService)
        module.single { backfillSalesAgentController }
    }

    private val salesAgent = TestModelFactory.buildSalesAgent(
        email = "<EMAIL>",
        name = "Isabela Martins",
        documentNumber = "12345678901",
        phoneNumber = "11*********",
        birthDate = LocalDate.parse("1990-01-01"),
        salesFirmId = RangeUUID.generate(),
        image = "https://www.al.com"
    )

    @Test
    fun `#createOrUpdate should create the sales agent`() = runBlocking {
        val request = SalesAgentRequest(
            name = "isabela martins",
            documentNumber = "123.456.789-01",
            email = "<EMAIL>",
            phoneNumber = "11 *********",
            birthDate = "01/01/1990",
            salesFirmId = salesAgent.salesFirmId.toString(),
            hasPortalAccess = true,
            image = "https://www.al.com",
        )

        coEvery {
            salesAgentService.getByDocument(request.documentNumber)
        } returns NotFoundException("Sales agent not found").failure()
        coEvery {
            salesAgentService.add(
                match {
                    it.name == salesAgent.name &&
                    it.documentNumber == salesAgent.documentNumber &&
                    it.email == salesAgent.email &&
                    it.phoneNumber == salesAgent.phoneNumber &&
                    it.birthDate == salesAgent.birthDate &&
                    it.salesFirmId == salesAgent.salesFirmId &&
                    it.image == salesAgent.image &&
                    it.additionalInfo?.hasPortalAccess == true &&
                    it.additionalInfo?.isFirstAccess == true
                }
            )
        } returns salesAgent.success()

        post(to = "/backfill/sales_agent", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByDocument(any()) }
        coVerifyOnce { salesAgentService.add(any()) }
        coVerifyNone { salesAgentService.update(any()) }
    }

    @Test
    fun `#createOrUpdate should update the sales agent`() = runBlocking {
        val request = SalesAgentRequest(
            name = "isabela martins",
            documentNumber = "123.456.789-01",
            email = "<EMAIL>",
            phoneNumber = "11 *********",
            birthDate = "01/01/1990",
            salesFirmId = salesAgent.salesFirmId.toString(),
            hasPortalAccess = true,
        )

        coEvery {
            salesAgentService.getByDocument(request.documentNumber)
        } returns salesAgent.success()
        coEvery {
            salesAgentService.update(
                match {
                    it.name == salesAgent.name &&
                    it.documentNumber == salesAgent.documentNumber &&
                    it.email == salesAgent.email &&
                    it.phoneNumber == salesAgent.phoneNumber &&
                    it.birthDate == salesAgent.birthDate &&
                    it.salesFirmId == salesAgent.salesFirmId &&
                    it.image == null &&
                    it.additionalInfo?.hasPortalAccess == true &&
                    it.additionalInfo?.isFirstAccess == true
                }
            )
        } returns salesAgent.success()

        post(to = "/backfill/sales_agent", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByDocument(any()) }
        coVerifyOnce { salesAgentService.update(any()) }
        coVerifyNone { salesAgentService.add(any()) }
    }

    @Test
    fun `#updateSalesAgentLeadsNumber should update the sales agent leads number`() = runBlocking {
        val salesAgent = TestModelFactory.buildSalesAgent().copy(isActiveInCampaign = true)
        val request = SalesAgentLeadsNumberRequest(
            salesAgentId = salesAgent.id,
            totalLeadsFromCampaign = 10,
            leadsFromCampaignRound = 5,
        )
        val updated = salesAgent.copy(
            totalLeadsFromCampaign = request.totalLeadsFromCampaign,
            leadsFromCampaignRound = request.leadsFromCampaignRound,
        )

        coEvery {
            salesAgentService.get(salesAgent.id)
        } returns salesAgent.success()
        coEvery {
            salesAgentService.update(
                match {
                    it.totalLeadsFromCampaign == request.totalLeadsFromCampaign &&
                    it.leadsFromCampaignRound == request.leadsFromCampaignRound
                }
            )
        } returns updated.success()

        post(to = "/backfill/update_sales_agent_leads_number", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.get(any()) }
        coVerifyOnce { salesAgentService.update(any()) }
    }

    @Test
    fun `#updateSalesAgentLeadsNumber should not update the sales agent leads number when sales agent is not active in campaign`() = runBlocking {
        val salesAgent = TestModelFactory.buildSalesAgent().copy(isActiveInCampaign = false)
        val request = SalesAgentLeadsNumberRequest(
            salesAgentId = salesAgent.id,
            totalLeadsFromCampaign = 10,
            leadsFromCampaignRound = 5,
        )

        coEvery {
            salesAgentService.get(salesAgent.id)
        } returns salesAgent.success()

        post(to = "/backfill/update_sales_agent_leads_number", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.get(any()) }
        coVerifyNone { salesAgentService.update(any()) }
    }

    @Test
    fun `createSalesFirmAgentPartnership should create partnerships and update deals`() = runBlocking {
        val request = BackfillRequest(offset = 0, limit = 10)
        val salesAgent = TestModelFactory.buildSalesAgent()
        val salesFirmAgentPartnership = SalesFirmAgentPartnership(
            salesFirmId = salesAgent.salesFirmId,
            salesAgentId = salesAgent.id
        )
        val deals = listOf(TestModelFactory.buildOngoingCompanyDeal())

        coEvery { salesAgentService.getByRange(request.offset, request.limit) } returns listOf(salesAgent).success()
        coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesFirmAgentPartnership.salesFirmId, salesFirmAgentPartnership.salesAgentId) } returns NotFoundException("Partnership not found").failure()
        coEvery { salesFirmAgentPartnershipService.create(any()) } returns salesFirmAgentPartnership.success()
        coEvery { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) } returns deals.success()
        coEvery { ongoingCompanyDealService.update(any()) } returns deals.first().success()

        post(to = "/backfill/sales_firm_agent_partnership", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByRange(request.offset, request.limit) }
        coVerifyOnce { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyOnce { salesFirmAgentPartnershipService.create(any()) }
        coVerifyOnce { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) }
    }

    @Test
    fun `createSalesFirmAgentPartnership should handle no sales agents found`() = runBlocking {
        val request = BackfillRequest(offset = 0, limit = 10)

        coEvery { salesAgentService.getByRange(request.offset, request.limit) } returns emptyList<SalesAgent>().success()

        post(to = "/backfill/sales_firm_agent_partnership", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByRange(request.offset, request.limit) }
        coVerifyNone { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyNone { salesFirmAgentPartnershipService.create(any()) }
        coVerifyNone { ongoingCompanyDealService.getBySalesAgentDocument(any(), any()) }
        coVerifyNone { ongoingCompanyDealService.update(any()) }
    }

    @Test
    fun `createSalesFirmAgentPartnership should handle no deals found for sales agent`() = runBlocking {
        val request = BackfillRequest(offset = 0, limit = 10)
        val salesAgent = TestModelFactory.buildSalesAgent()
        val salesFirmAgentPartnership = SalesFirmAgentPartnership(
            salesFirmId = salesAgent.salesFirmId,
            salesAgentId = salesAgent.id
        )

        coEvery { salesAgentService.getByRange(request.offset, request.limit) } returns listOf(salesAgent).success()
        coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesFirmAgentPartnership.salesFirmId, salesFirmAgentPartnership.salesAgentId) } returns NotFoundException("Partnership not found").failure()
        coEvery { salesFirmAgentPartnershipService.create(any()) } returns salesFirmAgentPartnership.success()
        coEvery { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) } returns NotFoundException("No deals found").failure()

        post(to = "/backfill/sales_firm_agent_partnership", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByRange(request.offset, request.limit) }
        coVerifyOnce { salesFirmAgentPartnershipService.create(any()) }
        coVerifyOnce { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyOnce { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) }
        coVerifyNone { ongoingCompanyDealService.update(any()) }
    }

    @Test
    fun `createSalesFirmAgentPartnership should fetch partnership and update deals`() = runBlocking {
        val request = BackfillRequest(offset = 0, limit = 10)
        val salesAgent = TestModelFactory.buildSalesAgent()
        val salesFirmAgentPartnership = SalesFirmAgentPartnership(
            salesFirmId = salesAgent.salesFirmId,
            salesAgentId = salesAgent.id
        )
        val deals = listOf(TestModelFactory.buildOngoingCompanyDeal())

        coEvery { salesAgentService.getByRange(request.offset, request.limit) } returns listOf(salesAgent).success()
        coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesFirmAgentPartnership.salesFirmId, salesFirmAgentPartnership.salesAgentId) } returns salesFirmAgentPartnership.success()
        coEvery { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) } returns deals.success()
        coEvery { ongoingCompanyDealService.update(any()) } returns deals.first().success()

        post(to = "/backfill/sales_firm_agent_partnership", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { salesAgentService.getByRange(request.offset, request.limit) }
        coVerifyOnce { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyNone { salesFirmAgentPartnershipService.create(any()) }
        coVerifyOnce { ongoingCompanyDealService.getBySalesAgentDocument(salesAgent.documentNumber, salesAgent.salesFirmId) }
    }
}

