package br.com.alice.sales_channel.br.com.alice.sales_channel.consumer

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.TicketStage
import br.com.alice.data.layer.models.getTicketStage
import br.com.alice.data.layer.models.toHubspotStage
import br.com.alice.sales_channel.consumer.HubspotWebhookConsumer
import br.com.alice.sales_channel.events.CreateDealFromHubspotWebhookEvent
import br.com.alice.sales_channel.events.DealChangedEvent
import br.com.alice.sales_channel.events.TicketUpsertEvent
import br.com.alice.sales_channel.exception.SalesFirmNotFoundException
import br.com.alice.sales_channel.service.HubspotTicketService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesFirmAgentPartnershipService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class HubspotWebhookConsumerTest: ConsumerTest() {
    private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()
    private val salesFirmService: SalesFirmService = mockk()
    private val hubspotTicketService: HubspotTicketService = mockk()
    private val salesAgentService: SalesAgentService = mockk()
    private val salesFirmAgentPartnershipService: SalesFirmAgentPartnershipService = mockk()

    private val consumer = HubspotWebhookConsumer(
        ongoingCompanyDealService,
        hubspotTicketService,
        salesFirmService,
        salesAgentService,
        salesFirmAgentPartnershipService,
    )

    @Nested
    inner class UpdateEventTests {
        private val dealEvent = buildEvent()
        private val salesFirm = TestModelFactory.buildSalesFirm()
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()
        private val salesAgent = TestModelFactory.buildSalesAgent(
            documentNumber = "64239977000149",
            salesFirmId = salesFirm.id,
        )
        private val partnership = TestModelFactory.buildSalesFirmAgentPartnership(
            salesFirmId = salesAgent.salesFirmId,
            salesAgentId = salesAgent.id,
        )

        @Test
        fun `#handleUpdateEvent should create company deal when has seller`(): Unit = runBlocking {
            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
            coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns salesFirm.success()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any(), false) } returns ongoingCompanyDeal.success()

            val response = consumer.handleUpdateEvent(dealEvent)
            ResultAssert.assertThat(response).isSuccess()
        }

        @Test
        fun `#handleUpdateEvent should not create company deal when there is no seller`(): Unit = runBlocking {
            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns SalesFirmNotFoundException("").failure()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any()) } returns ongoingCompanyDeal.success()

            val response = consumer.handleUpdateEvent(dealEvent)
            ResultAssert.assertThat(response).isFailure()
        }

        @Test
        fun `#handleUpdateEvent should not create company deal when it's from broker channel and seller name is blank`(): Unit = runBlocking {
            val dealFromBroker = ongoingCompanyDeal.copy(channel = DealChannel.BROKER)

            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns dealFromBroker.success()

            val response = consumer.handleUpdateEvent(dealEvent.copy(sellerName = " "))

            ResultAssert.assertThat(response).fails().withMessage("Seller name cannot be blank")
        }

        @Test
        fun `#handleUpdateEvent should create company deal with channel INTERNAL when sales firm is Alice`(): Unit =
            runBlocking {
                val dealEvent = buildEventInternal()

                coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
                coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
                coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
                coEvery { salesFirmService.getSalesFirmByName("Alice") } returns salesFirm.success()
                coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any(), false) } returns ongoingCompanyDeal.success()

                val response = consumer.handleUpdateEvent(dealEvent)
                ResultAssert.assertThat(response).isSuccess()
            }

        @Test
        fun `#upsertTicketEvent should upsert hubspotTicket`(): Unit = runBlocking {
            val ticketEvent = buildTicketUpsertEvent()
            val deal = TestModelFactory.buildOngoingCompanyDeal()
            val hubspotTicket = TestModelFactory.buildHubspotTicket(
                dealId = ticketEvent.dealId,
                ticketId = ticketEvent.id,
                pendingTaskDescription = ticketEvent.pendingTaskDescription,
                stage = getTicketStage(ticketEvent.stage),
            )

            coEvery { ongoingCompanyDealService.getBySourceId(hubspotTicket.dealId) } returns deal.success()
            coEvery {
                hubspotTicketService.upsert(
                    match {
                        it.dealId == hubspotTicket.dealId &&
                                it.ongoingCompanyDealId == hubspotTicket.ongoingCompanyDealId &&
                                it.ticketId == hubspotTicket.ticketId &&
                                it.pendingTaskDescription == hubspotTicket.pendingTaskDescription
                        it.stage == hubspotTicket.stage
                    }, any()
                )
            } returns hubspotTicket.success()

            val response = consumer.upsertTicketEvent(ticketEvent)
            ResultAssert.assertThat(response).isSuccess()

            coVerifyOnce {
                ongoingCompanyDealService.getBySourceId(hubspotTicket.dealId)
                hubspotTicketService.upsert(any(), any())
            }
        }

        @Test
        fun `#upsertTicketEvent should upsert hubspotTicket by deal not synced`(): Unit = runBlocking {
            val ticketEvent = buildTicketUpsertEvent()
            val hubspotTicket = TestModelFactory.buildHubspotTicket(
                dealId = ticketEvent.dealId,
                ticketId = ticketEvent.id,
                pendingTaskDescription = ticketEvent.pendingTaskDescription,
                stage = getTicketStage(ticketEvent.stage),
            )

            coEvery { ongoingCompanyDealService.getBySourceId(hubspotTicket.dealId) } returns NotFoundException("").failure()

            val response = consumer.upsertTicketEvent(ticketEvent)
            ResultAssert.assertThat(response).isFailureOfType(NotFoundException::class)

            coVerifyOnce {
                ongoingCompanyDealService.getBySourceId(hubspotTicket.dealId)
            }
            coVerifyNone {
                hubspotTicketService.upsert(any(), any())
            }
        }

        @Test
        fun `#upsertTicketEvent should not upsert hubspotTicket`(): Unit = runBlocking {
            val ticketEvent = buildTicketUpsertEvent()

            coEvery { hubspotTicketService.upsert(any(), any()) } returns NotFoundException().failure()

            val response = consumer.upsertTicketEvent(ticketEvent)
            ResultAssert.assertThat(response).isFailure()
        }

        @Test
        fun `#handleUpdateEvent should update deal with compulsory membership`(): Unit = runBlocking {
            val dealEvent = buildEventWithCompulsoryMembership()

            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
            coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns salesFirm.success()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any(), false) } returns ongoingCompanyDeal.success()

            val response = consumer.handleUpdateEvent(dealEvent)
            ResultAssert.assertThat(response).isSuccess()
        }

        private fun buildEvent(): DealChangedEvent {
            return DealChangedEvent(
                id = "1234",
                stage = DealStage.BACKGROUND_CHECK,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                sellerName = "AFFINITY Corretora",
                sellerDocument = "64239977000149",
                companyName = "Selecao de ataque 2002",
                companyDocument = "50229672000141",
                companyLegalName = "Selecao Brasileira de ataque da copa de 2002",
                employeeCount = 50,
                livesCount = 8,
                contractModel = "CLT"
            )
        }

        private fun buildTicketUpsertEvent(): TicketUpsertEvent {
            return TicketUpsertEvent(
                id = "1234",
                stage = TicketStage.SALES_PENDING.toHubspotStage(),
                ongoingCompanyDealId = UUID.randomUUID(),
                dealId = "1234",
                pendingTaskDescription = "Pending task",
                originalEventDate = LocalDateTime.now()
            )
        }

        private fun buildEventInternal(): DealChangedEvent {
            return DealChangedEvent(
                id = "1234",
                stage = DealStage.BACKGROUND_CHECK,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                sellerName = "Alice",
                sellerDocument = "64239977000149",
                companyName = "Selecao de ataque 2002",
                companyDocument = "50229672000141",
                companyLegalName = "Selecao Brasileira de ataque da copa de 2002",
                employeeCount = 50,
                livesCount = 8,
                contractModel = "CLT"
            )
        }

        private fun buildEventWithCompulsoryMembership(): DealChangedEvent {
            return DealChangedEvent(
                id = "1234",
                stage = DealStage.BACKGROUND_CHECK,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                sellerName = "AFFINITY Corretora",
                sellerDocument = "64239977000149",
                companyName = "Selecao de ataque 2002",
                companyDocument = "50229672000141",
                companyLegalName = "Selecao Brasileira de ataque da copa de 2002",
                isCompulsoryMembership = true,
                compulsoryMembershipType = "type",
                compulsoryMembershipResponse = "response",
                employeeCount = 50,
                livesCount = 8,
                contractModel = "CLT"
            )
        }
    }

    @Nested
    inner class CreationEventTests {
        val dealEvent = buildEvent()
        val salesFirm = TestModelFactory.buildSalesFirm()
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()
        val salesAgent = TestModelFactory.buildSalesAgent(
            documentNumber = "64239977000149",
            salesFirmId = salesFirm.id,
        )
        val partnership = TestModelFactory.buildSalesFirmAgentPartnership(
            salesFirmId = salesAgent.salesFirmId,
            salesAgentId = salesAgent.id,
        )

        @Test
        fun `#handleCreationEvent should create company deal when has seller`(): Unit = runBlocking {
            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
            coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns salesFirm.success()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any()) } returns ongoingCompanyDeal.success()

            val response = consumer.handleCreationEvent(dealEvent)
            ResultAssert.assertThat(response).isSuccess()
        }

        @Test
        fun `#handleCreationEvent should not create company deal when there is no seller`(): Unit = runBlocking {
            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns SalesFirmNotFoundException("").failure()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any()) } returns ongoingCompanyDeal.success()

            val response = consumer.handleCreationEvent(dealEvent)
            ResultAssert.assertThat(response).isFailure()
        }

        @Test
        fun `#handleCreationEvent should create company deal with channel INTERNAL when sales firm is Alice`(): Unit =
            runBlocking {
                val dealEvent = buildEventInternal()

                coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
                coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
                coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
                coEvery { salesFirmService.getSalesFirmByName("Alice") } returns salesFirm.success()
                coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any()) } returns ongoingCompanyDeal.success()

                val response = consumer.handleCreationEvent(dealEvent)
                ResultAssert.assertThat(response).isSuccess()
            }

        @Test
        fun `#handleCreationEvent should create deal with compulsory membership`() = runBlocking<Unit> {
            coEvery { ongoingCompanyDealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
            coEvery { salesAgentService.getByDocument(salesAgent.documentNumber) } returns salesAgent.success()
            coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesAgent.salesFirmId, salesAgent.id) } returns partnership.success()
            coEvery { salesFirmService.getSalesFirmByName("AFFINITY Corretora") } returns salesFirm.success()
            coEvery { ongoingCompanyDealService.upsertOngoingCompanyDeal(any()) } returns ongoingCompanyDeal.success()

            val response = consumer.handleCreationEvent(dealEvent)
            ResultAssert.assertThat(response).isSuccess()
        }

        private fun buildEvent(): CreateDealFromHubspotWebhookEvent {
            return CreateDealFromHubspotWebhookEvent(
                id = "1234",
                stage = DealStage.BACKGROUND_CHECK,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                sellerName = "AFFINITY Corretora",
                sellerDocument = "64239977000149",
                companyName = "Selecao de ataque 2002",
                companyDocument = "50229672000141",
                companyLegalName = "Selecao Brasileira de ataque da copa de 2002",
                employeeCount = 50,
                livesCount = 8,
                contractModel = "CLT"
            )
        }

        private fun buildEventInternal(): CreateDealFromHubspotWebhookEvent {
            return CreateDealFromHubspotWebhookEvent(
                id = "1234",
                stage = DealStage.BACKGROUND_CHECK,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                sellerName = "Alice",
                sellerDocument = "64239977000149",
                companyName = "Selecao de ataque 2002",
                companyDocument = "50229672000141",
                companyLegalName = "Selecao Brasileira de ataque da copa de 2002",
                employeeCount = 50,
                livesCount = 8,
                contractModel = "CLT"
            )
        }
    }

}
