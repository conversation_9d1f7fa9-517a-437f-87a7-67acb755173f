package br.com.alice.sales_channel.br.com.alice.sales_channel.consumer

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.sales_channel.consumer.SalesAgentCreatedConsumer
import br.com.alice.sales_channel.events.SalesAgentCreatedEvent
import br.com.alice.sales_channel.service.SalesAgentService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class SalesAgentCreatedConsumerTest: ConsumerTest() {
    @BeforeTest
    fun setup() {
        super.before()
        mockkObject(logger)
    }

    private val salesAgentService: SalesAgentService = mockk()
    private val salesAgent = TestModelFactory.buildSalesAgent()

    private val consumer = SalesAgentCreatedConsumer(
        salesAgentService = salesAgentService,
    )

    @Test
    fun `#handleSalesAgentCreated should reset lead total number`() = runBlocking {
        val event = SalesAgentCreatedEvent(
            salesAgent = salesAgent,
        )

        coEvery {
            salesAgentService.resetLeadTotalNumber(salesAgent)
        } returns listOf(salesAgent).success()

        val result = consumer.handleSalesAgentCreated(event)

        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { salesAgentService.resetLeadTotalNumber(any()) }
    }
}
