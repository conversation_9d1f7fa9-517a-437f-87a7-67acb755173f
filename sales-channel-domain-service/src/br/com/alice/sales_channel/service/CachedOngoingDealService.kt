package br.com.alice.sales_channel.service

import br.com.alice.common.core.extensions.toCPFMask
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.services.OngoingCompanyDealDataService
import br.com.alice.sales_channel.model.dto.OngoingDealsCountByStatus
import com.github.kittinunf.result.map
import java.util.UUID

class CachedOngoingDealService(
    private val cache: GenericCache,
    private val ongoingCompanyDealDataService: OngoingCompanyDealDataService
) {
    suspend fun getCountBySalesFirmGroupedByStatus(salesFirmId: UUID) =
        coResultOf<OngoingDealsCountByStatus, Throwable> {
            cache.get(
                key = salesFirmId.toCacheByStatusKey(),
                type = OngoingDealsCountByStatus::class,
                expirationTime = 5 * 60L
            ) {
                ongoingCompanyDealDataService.countGrouped {
                    where {
                        this.salesFirmId.eq(salesFirmId)
                    }.groupBy { listOf(this.status) }
                }.map {
                    OngoingDealsCountByStatus(
                        salesFirmId = salesFirmId,
                        results = it
                    )
                }.get()
            }
        }

    suspend fun getCountBySalesAgentDocumentGroupedByStatus(salesAgentDocument: String) =
        coResultOf<OngoingDealsCountByStatus, Throwable> {
            cache.get(
                key = salesAgentDocument.toCacheByStatusKey(),
                type = OngoingDealsCountByStatus::class,
                expirationTime = 5 * 60L
            ) {
                ongoingCompanyDealDataService.countGrouped {
                    where {
                        this.salesAgentDocument.inList(
                            listOf(
                                salesAgentDocument,
                                salesAgentDocument.toCPFMask(),
                            )
                        )
                    }.groupBy { listOf(this.status) }
                }.map {
                    OngoingDealsCountByStatus(
                        salesAgentDocument = salesAgentDocument,
                        results = it
                    )
                }.get()
            }
        }

    suspend fun updateCountBySalesFirmGroupedByStatus(salesFirmId: UUID) {
        cache.invalidateKeys(salesFirmId.toCacheByStatusKey())
        // force a Get after invalidating, so it updates the cache
        getCountBySalesFirmGroupedByStatus(salesFirmId)
    }

    private fun UUID.toCacheByStatusKey() =
        ":sales-firm-deals-by-status:${this}"

    private fun String.toCacheByStatusKey() =
        ":sales-agent-sales-firm-deals-by-status:${this}"
}
