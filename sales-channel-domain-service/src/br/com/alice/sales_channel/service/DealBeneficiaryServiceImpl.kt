package br.com.alice.sales_channel.service

import br.com.alice.business.client.BeneficiaryCompiledViewFilters
import br.com.alice.business.client.BeneficiaryCompiledViewService
import br.com.alice.business.client.CompanyService
import br.com.alice.common.core.extensions.normalizeCnpjWithoutMask
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.data.layer.models.BeneficiaryCompiledView
import br.com.alice.sales_channel.converter.toDealBeneficiaryStage
import br.com.alice.sales_channel.model.DealBeneficiary
import br.com.alice.sales_channel.model.DealBeneficiaryProduct
import br.com.alice.sales_channel.model.DealBeneficiaryStage
import br.com.alice.sales_channel.model.DealBeneficiaryStageDetails
import com.github.kittinunf.result.flatMap
import java.util.UUID

class DealBeneficiaryServiceImpl(
    private val ongoingCompanyDealService: OngoingCompanyDealService,
    private val companyService: CompanyService,
    private val beneficiaryCompiledViewService: BeneficiaryCompiledViewService,
) : DealBeneficiaryService {
    override suspend fun searchByOngoingDeal(id: UUID, query: String?, offset: Int, limit: Int) =
        getCompanyByDeal(id).flatMap {
            beneficiaryCompiledViewService.findByFilters(
                BeneficiaryCompiledViewFilters(
                    companyId = it.id,
                    range = IntRange(offset, offset +  limit),
                    search = query
                )
            )
        }.mapEach {
            it.toDealBeneficiary()
        }

    private suspend fun getCompanyByDeal(id: UUID) = ongoingCompanyDealService.get(id)
        .flatMap {
            companyService.findByCnpjs(
                listOf(
                    it.cnpj,
                    it.cnpj.normalizeCnpjWithoutMask()
                )
            ).mapFirst()
        }
}

private fun BeneficiaryCompiledView.toDealBeneficiary(): DealBeneficiary {
    val convertedStage = this.lastOnboardingPhase.toDealBeneficiaryStage()
    return DealBeneficiary(
        id = this.beneficiaryId,
        personId = this.personId,
        name = this.personFullSocialName,
        updatedAt = this.viewUpdatedAt,
        memberId = this.memberId,
        stage = DealBeneficiaryStageDetails(
            name = convertedStage.name,
            friendlyName = convertedStage.value,
            count = DealBeneficiaryStage.totalStages(),
            completed = convertedStage.toStageNumber(),
            owner = convertedStage.owner.toString()
        ),
        product = DealBeneficiaryProduct(
            id = this.productId,
            friendlyName = this.productDisplayName ?: this.productTitle
        )
    )
}
