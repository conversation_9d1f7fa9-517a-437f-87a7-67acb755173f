package br.com.alice.sales_channel.consumer

import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.CompanyActivatedEvent
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.DealStage
import br.com.alice.sales_channel.service.OngoingCompanyDealMovementService
import java.util.UUID

class CompanyActivatedConsumer(
    private val ongoingCompanyDealMovementService: OngoingCompanyDealMovementService,
    private val companyService: CompanyService
): Consumer()  {


    suspend fun handleCompanyActivated(event: CompanyActivatedEvent) = withSubscribersEnvironment {
        logger.info("Consuming company activated event",
            "company_id" to event.payload.id,
            "company_name" to event.payload.companyName,
            "company_cnpj" to event.payload.cnpj)


        val company = getCompany(event.payload.id).get()
        ongoingCompanyDealMovementService.move(company, DealStage.PROCESS_FINISHED)

    }

    private suspend fun getCompany(id: UUID) =
        companyService.get(id)
            .then {
                logger.info("Company found to id: ${id} "
                    , "company_id" to id
                    , "company_name" to it.name
                    , "company_cnpj" to it.cnpj
                )
            }
            .thenError {
                logger.error("No company found for id", "company_id" to id)
                it
            }
}

