package br.com.alice.sales_channel.consumer

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CompulsoryMembership
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HubspotTicket
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.OngoingCompanyDealDetails
import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.data.layer.models.SalesFirmAgentPartnership
import br.com.alice.data.layer.models.getTicketStage
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.sales_channel.events.CreateDealFromHubspotWebhookEvent
import br.com.alice.sales_channel.events.DealChangedEvent
import br.com.alice.sales_channel.events.TicketUpsertEvent
import br.com.alice.sales_channel.exception.SalesFirmNotFoundException
import br.com.alice.sales_channel.service.HubspotTicketService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesAgentService
import br.com.alice.sales_channel.service.SalesFirmAgentPartnershipService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class HubspotWebhookConsumer(
    private val ongoingCompanyDealService: OngoingCompanyDealService,
    private val hubspotTicketService: HubspotTicketService,
    private val salesFirmService: SalesFirmService,
    private val salesAgentService: SalesAgentService,
    private val salesFirmAgentPartnershipService: SalesFirmAgentPartnershipService,
): Consumer() {

    suspend fun handleUpdateEvent(event: DealChangedEvent) = withSubscribersEnvironment {
        val payload = event.payload

        logger.info("Consuming deal changed event",
            "company_name" to  payload.companyName,
            "company_document" to  payload.companyDocument,
            "seller_name" to  payload.sellerName,
            "seller_document" to  payload.sellerDocument,
            "created_at" to  payload.createdAt,
            "updated_at" to  payload.updatedAt,
            "stage" to  payload.stage,
            "portability_type" to  payload.portabilityType,
            "portability_response" to  payload.portabilityResponse,
            "portability_declined_proceed" to  payload.portabilityDeclinedProceed
        )

        val currentDeal = ongoingCompanyDealService.getBySourceId(payload.id).getOrNull()

        if (currentDeal?.channel == DealChannel.BROKER && payload.sellerName.isBlank()) {
            logger.error(
                "Cannot update deal - seller name is blank",
                "deal_id" to payload.id,
                "company_name" to  payload.companyName,
                "company_document" to  payload.companyDocument,
                "seller_document" to  payload.sellerDocument,
                "stage" to  payload.stage
            )

            throw IllegalArgumentException("Seller name cannot be blank")
        }

        val salesAgent = payload.sellerDocument?.let { salesAgentService.getByDocument(it).getOrNull() }

        logger.info(
            "Searched for ongoing company deal with source id",
            "source_id" to payload.id,
            "current_deal" to currentDeal,
            "sales_agent" to salesAgent,
        )

        salesFirmService
            .getSalesFirmByName(payload.sellerName)
            .foldError(NotFoundException::class to { SalesFirmNotFoundException(payload.sellerName).failure()})
            .flatMap {
                val salesPartnership = salesAgent?.let { salesAgent ->
                    findOrCreateSalesPartnership(
                        salesFirm = it,
                        agentId = salesAgent.id,
                    ).get()
                }

                logger.info(
                    "Searched for sales firm",
                    "sales_firm" to it,
                    "sales_partnership" to salesPartnership,
                )

                ongoingCompanyDealService.upsertOngoingCompanyDeal(
                    ongoingCompanyDeal = OngoingCompanyDeal(
                        id = currentDeal?.id ?: UUID.randomUUID(),
                        name = payload.companyName,
                        cnpj = payload.companyDocument,
                        salesAgentDocument = payload.sellerDocument,
                        status = payload.stage,
                        legalName = payload.companyLegalName,
                        dealDetails = OngoingCompanyDealDetails(
                            payload.employeeCount,
                            payload.livesCount,
                            payload.contractModel,
                            CompulsoryMembership(
                                payload.isCompulsoryMembership,
                                payload.compulsoryMembershipType,
                                payload.compulsoryMembershipResponse
                            )
                        ),
                        sourceCreatedAt = payload.createdAt,
                        sourceUpdatedAt = payload.updatedAt,
                        sourceId = payload.id,
                        salesFirmId = it.id,
                        salesAgentId = salesAgent?.id,
                        channel = getChannel(it),
                        companyId = payload.companyId,
                        portabilityType = payload.portabilityType,
                        portabilityResponse = payload.portabilityResponse,
                        portabilityDeclinedProceed = payload.portabilityDeclinedProceed,
                        salesFirmAgentPartnershipId = salesPartnership?.id,
                    ),
                    shouldValidateStatus = false,
                )
            }
            .flatMap {
                if (shouldCreateContract(event, currentDeal)) {
                    logger.info("Creating contract for deal", "deal_id" to currentDeal?.id)
                    ongoingCompanyDealService.sendContract(it.id)
                }
                it.success()
            }
    }

    private fun shouldCreateContract(event: DealChangedEvent, currentDeal: OngoingCompanyDeal?): Boolean {
        if (!FeatureService.get(FeatureNamespace.BUSINESS, "b2b_should_create_contract_on_deal_change", false)) {
            return false
        }

        if (currentDeal?.status == DealStage.RISK_FLOW && event.payload.stage == DealStage.CONTRACT_PREPARATION) {
            return true
        }

        return false
    }

    suspend fun handleCreationEvent(event: CreateDealFromHubspotWebhookEvent) = withSubscribersEnvironment {
        val payload = event.payload

        logger.info(
            "Consuming deal creation event",
            "company_name" to payload.companyName,
            "company_document" to payload.companyDocument,
            "seller_name" to payload.sellerName,
            "seller_document" to payload.sellerDocument,
            "created_at" to payload.createdAt,
            "updated_at" to payload.updatedAt,
            "stage" to payload.stage,
            "portability_type" to payload.portabilityType,
            "portability_response" to payload.portabilityResponse,
            "portability_declined_proceed" to payload.portabilityDeclinedProceed
        )

        val salesAgent = payload.sellerDocument?.let { salesAgentService.getByDocument(it).getOrNull() }

        salesFirmService.getSalesFirmByName(payload.sellerName).flatMap {
            val salesPartnership = salesAgent?.let { salesAgent ->
                findOrCreateSalesPartnership(
                    salesFirm = it,
                    agentId = salesAgent.id,
                ).get()
            }

            val ongoingCompanyDeal = OngoingCompanyDeal(
                name = payload.companyName,
                cnpj = payload.companyDocument,
                salesAgentDocument = payload.sellerDocument,
                status = payload.stage,
                legalName = payload.companyLegalName,
                dealDetails = OngoingCompanyDealDetails(
                    payload.employeeCount,
                    payload.livesCount,
                    payload.contractModel,
                    CompulsoryMembership(
                        payload.isCompulsoryMembership,
                        payload.compulsoryMembershipType,
                        payload.compulsoryMembershipResponse
                    )
                ),
                sourceCreatedAt = payload.createdAt,
                sourceUpdatedAt = payload.updatedAt,
                sourceId = payload.id,
                salesFirmId = it.id,
                salesAgentId = salesAgent?.id,
                channel = getChannel(it),
                companyId = payload.companyId,
                portabilityType = payload.portabilityType,
                portabilityResponse = payload.portabilityResponse,
                portabilityDeclinedProceed = payload.portabilityDeclinedProceed,
                salesFirmAgentPartnershipId = salesPartnership?.id,
            )
            ongoingCompanyDealService.upsertOngoingCompanyDeal(ongoingCompanyDeal)
        }.foldError(NotFoundException::class to { SalesFirmNotFoundException(payload.sellerName).failure() })
    }

    suspend fun upsertTicketEvent(event: TicketUpsertEvent) = withSubscribersEnvironment {
        val payload = event.payload

        logger.info(
            "Consuming ticket upsert event",
            "id" to payload.id,
            "stage" to payload.stage,
            "ongoingCompanyDealId" to payload.ongoingCompanyDealId,
            "dealId" to payload.dealId,
            "pendingTasksDescription" to payload.pendingTaskDescription,
            "originalEventDate" to payload.originalEventDate,
        )

        ongoingCompanyDealService
            .getBySourceId(payload.dealId)
            .map { ongoingCompanyDeal ->
                val hubspotTicket = HubspotTicket(
                    dealId = payload.dealId,
                    ongoingCompanyDealId = ongoingCompanyDeal.id,
                    ticketId = payload.id,
                    pendingTaskDescription = payload.pendingTaskDescription,
                    stage = getTicketStage(payload.stage),
                )
                hubspotTicketService.upsert(hubspotTicket, eventDate = payload.originalEventDate)
            }
    }

    private fun getChannel(salesFirm: SalesFirm) =
        if (salesFirm.name == "Alice") DealChannel.INTERNAL else DealChannel.BROKER

    private suspend fun findOrCreateSalesPartnership(
        salesFirm: SalesFirm,
        agentId: UUID
    ) : Result<SalesFirmAgentPartnership, Throwable> {
        logger.info(
            "Checking if sales partnership exists",
            "salesFirmId" to salesFirm.id,
            "agentId" to agentId
        )
        val partnership = salesFirmAgentPartnershipService.getBySalesFirmAndAgent(
            salesFirmId = salesFirm.id,
            salesAgentId = agentId
        ).getOrNullIfNotFound()

        return if (partnership == null) {
            logger.info(
                "Creating new sales partnership",
                "salesFirmId" to salesFirm.id,
                "agentId" to agentId
            )
            salesFirmAgentPartnershipService.create(
                SalesFirmAgentPartnership(
                    salesFirmId = salesFirm.id,
                    salesAgentId = agentId
                )
            )
        } else {
            logger.info(
                "Sales partnership already exists",
                "salesFirmId" to salesFirm.id,
                "agentId" to agentId
            )
            partnership.success()
        }
    }
}
