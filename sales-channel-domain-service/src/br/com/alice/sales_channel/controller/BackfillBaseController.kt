package br.com.alice.sales_channel.controller

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME

abstract class BackfillBaseController: Controller() {

    suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

}
