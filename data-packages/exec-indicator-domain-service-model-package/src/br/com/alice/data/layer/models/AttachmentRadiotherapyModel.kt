package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class AttachmentRadiotherapyModel(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val totvsGuiaId: UUID,
    val status: AttachmentStatus = AttachmentStatus.UNKNOWN,
    val oncologicalDiagnosisRadio: OncologicalDiagnosisRadioModel,
    val fieldsQuantity: Int,
    val fieldDose: Int,
    val totalDose: Int,
    val daysQuantity: Int,
    val expectedStartDate: LocalDate,
    val observation: String? = null,
) : Model

data class OncologicalDiagnosisRadioModel(
    val diagnosisDate: LocalDate,
    val imageDiagnosis: AnsImageDiagnosis,
    val stage: AnsStage,
    val purpose: AnsPurpose,
    val healthCondition: RadiotherapyHealthConditionModel,
    val ecoGt: AnsEcoGT,
    val histopathological: String? = null,
    val relevantInformations: String? = null
): JsonSerializable

data class RadiotherapyHealthConditionModel(
    val name: String? = null,
    val code: String? = null,
)
