package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.GlossAuthorizationInfoModel
import java.util.UUID
import com.github.kittinunf.result.Result

@RemoteService
interface GlossAuthorizationInfoModelDataService: Service,
    Finder<GlossAuthorizationInfoModelDataService.FieldOptions, GlossAuthorizationInfoModelDataService.OrderingOptions, GlossAuthorizationInfoModel>,
    Updater<GlossAuthorizationInfoModel>,
    Getter<GlossAuthorizationInfoModel>,
    Adder<GlossAuthorizationInfoModel> {

    override val namespace: String
        get() = "exec_indicator"

    override val serviceName: String
        get() = "gloss_authorization"

    class CodeField: Field.TextField(GlossAuthorizationInfoModel::code) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val code = CodeField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        GlossAuthorizationInfoModelDataService.FieldOptions(),
        GlossAuthorizationInfoModelDataService.OrderingOptions()
    )

    override suspend fun add(model: GlossAuthorizationInfoModel): Result<GlossAuthorizationInfoModel, Throwable>
    override suspend fun get(id: UUID): Result<GlossAuthorizationInfoModel, Throwable>
    override suspend fun update(model: GlossAuthorizationInfoModel): Result<GlossAuthorizationInfoModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<GlossAuthorizationInfoModel>, Throwable>
}

