package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthProfessionalOpsProfileModel
import br.com.alice.data.layer.services.HealthProfessionalOpsProfileModelDataService.FieldOptions
import br.com.alice.data.layer.services.HealthProfessionalOpsProfileModelDataService.OrderingOptions
import java.util.UUID
import com.github.kittinunf.result.Result

@RemoteService
interface HealthProfessionalOpsProfileModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, HealthProfessionalOpsProfileModel>,
    Adder<HealthProfessionalOpsProfileModel>,
    Updater<HealthProfessionalOpsProfileModel> {

    override val namespace: String
        get() = "exec_indicator"

    override val serviceName: String
        get() = "health_professional_ops_profile"

    class HealthProfessionalId : Field.UUIDField(HealthProfessionalOpsProfileModel::healthProfessionalId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val healthProfessionalId = HealthProfessionalId()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<HealthProfessionalOpsProfileModel>, Throwable>
    override suspend fun add(model: HealthProfessionalOpsProfileModel): Result<HealthProfessionalOpsProfileModel, Throwable>
    override suspend fun update(model: HealthProfessionalOpsProfileModel): Result<HealthProfessionalOpsProfileModel, Throwable>

}
