package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.models.SpecialistTier
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class ResourceBundleSpecialtyPricingModel(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    @Transient
    override var updatedBy: UpdatedBy? = null,

    val resourceBundleSpecialtyId: UUID,
    val beginAt: LocalDate,
    val endAt: LocalDate? = null,
    val prices: List<ResourceBundleSpecialtyPriceModel> = emptyList(),
) : Model, UpdatedByReference

data class ResourceBundleSpecialtyPriceModel(
    val tier: SpecialistTier,
    val productTier: TierType,
    val price: BigDecimal,
)
