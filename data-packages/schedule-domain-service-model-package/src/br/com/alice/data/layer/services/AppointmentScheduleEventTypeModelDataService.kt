package br.com.alice.data.layer.services

import br.com.alice.common.core.Status
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeModel
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.HealthcareModelType
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentScheduleEventTypeModelDataService : Service,
    Finder<AppointmentScheduleEventTypeModelDataService.FieldOptions, AppointmentScheduleEventTypeModelDataService.OrderingOptions, AppointmentScheduleEventTypeModel>,
    Counter<AppointmentScheduleEventTypeModelDataService.FieldOptions, AppointmentScheduleEventTypeModelDataService.OrderingOptions, AppointmentScheduleEventTypeModel>,
    Adder<AppointmentScheduleEventTypeModel>,
    Getter<AppointmentScheduleEventTypeModel>,
    Updater<AppointmentScheduleEventTypeModel> {

    override val namespace: String
        get() = "scheduler"
    override val serviceName: String
        get() = "appointment_schedule_event_type"

    class Id : Field.UUIDField(AppointmentScheduleEventTypeModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Title : Field.TextField(AppointmentScheduleEventTypeModel::title) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class SearchTokens : Field.TextField(AppointmentScheduleEventTypeModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class Category : Field.TextField(AppointmentScheduleEventTypeModel::category) {
        fun eq(value: AppointmentScheduleType) = Predicate.eq(this, value)
        fun inList(values: List<AppointmentScheduleType>) = Predicate.inList(this, values)
    }

    class StatusField : Field.TextField(AppointmentScheduleEventTypeModel::status) {
        fun eq(value: Status) = Predicate.eq(this, value)
        fun inList(values: List<Status>) = Predicate.inList(this, values)
    }

    class Specialty : Field.UUIDField(AppointmentScheduleEventTypeModel::specialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(values: List<UUID>) = Predicate.inList(this, values)
    }

    class SubSpecialtyIds : Field.JsonbField(AppointmentScheduleEventTypeModel::subSpecialtyIds) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(values: List<UUID>) = Predicate.containsAny(this, values)
    }

    class CreatedAt : Field.DateTimeField(AppointmentScheduleEventTypeModel::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class IsMultiProfessionalReferral : Field.BooleanField(AppointmentScheduleEventTypeModel::isMultiProfessionalReferral)
    class HealthcareModelTypeField : Field.TextField(AppointmentScheduleEventTypeModel::healthcareModelType) {
        fun eq(value: HealthcareModelType) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val title = Title()
        val searchTokens = SearchTokens()
        val category = Category()
        val status = StatusField()
        val specialty = Specialty()
        val subSpecialtyIds = SubSpecialtyIds()
        val isMultiProfessionalReferral = IsMultiProfessionalReferral()
        val modelType = HealthcareModelTypeField()
    }

    class OrderingOptions {
        val title = Title()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AppointmentScheduleEventTypeModel): Result<AppointmentScheduleEventTypeModel, Throwable>
    override suspend fun get(id: UUID): Result<AppointmentScheduleEventTypeModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentScheduleEventTypeModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun update(model: AppointmentScheduleEventTypeModel): Result<AppointmentScheduleEventTypeModel, Throwable>

}
