package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.StaffSchedulePreferenceModel
import br.com.alice.data.layer.services.StaffSchedulePreferenceModelDataService.FieldOptions
import br.com.alice.data.layer.services.StaffSchedulePreferenceModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface StaffSchedulePreferenceModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, StaffSchedulePreferenceModel>,
    Counter<FieldOptions, OrderingOptions, StaffSchedulePreferenceModel>,
    Updater<StaffSchedulePreferenceModel>,
    Adder<StaffSchedulePreferenceModel>,
    Getter<StaffSchedulePreferenceModel> {

    override val namespace: String
        get() = "scheduler"
    override val serviceName: String
        get() = "staff_schedule_preference"

    class Id : Field.UUIDField(StaffSchedulePreferenceModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class StaffId : Field.UUIDField(StaffSchedulePreferenceModel::staffId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class SearchTokens : Field.TextField(StaffSchedulePreferenceModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class HasStaffSchedules : Field.BooleanField(StaffSchedulePreferenceModel::hasStaffSchedules)

    class StaffName: Field.TextField(StaffSchedulePreferenceModel::staffName)

    class StaffRole: Field.TextField(StaffSchedulePreferenceModel::staffRole) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val staffId = StaffId()
        val searchTokens = SearchTokens()
        val hasStaffSchedules = HasStaffSchedules()
        val staffRole = StaffRole()
    }

    class OrderingOptions {
        val id = Id()
        val staffId = StaffId()
        val staffName = StaffName()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<StaffSchedulePreferenceModel>, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun add(model: StaffSchedulePreferenceModel): Result<StaffSchedulePreferenceModel, Throwable>
    override suspend fun update(model: StaffSchedulePreferenceModel): Result<StaffSchedulePreferenceModel, Throwable>
    override suspend fun get(id: UUID): Result<StaffSchedulePreferenceModel, Throwable>
}
