package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentReminderModel(
    val appointmentScheduleId: UUID,
    val provider: AppointmentReminderProvider,
    val notificationTime: AppointmentReminderNotificationTime,
    override val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model
