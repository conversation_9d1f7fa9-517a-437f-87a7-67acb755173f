package br.com.alice.data.layer.services

import br.com.alice.common.core.Status
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.SchedulePreferenceModel
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface SchedulePreferenceModelDataService : Service,
    Finder<SchedulePreferenceModelDataService.FieldOptions, SchedulePreferenceModelDataService.OrderingOptions, SchedulePreferenceModel>,
    Counter<SchedulePreferenceModelDataService.FieldOptions, SchedulePreferenceModelDataService.OrderingOptions, SchedulePreferenceModel>,
    Updater<SchedulePreferenceModel>,
    Adder<SchedulePreferenceModel>,
    Getter<SchedulePreferenceModel> {

    override val namespace: String
        get() = "scheduler"
    override val serviceName: String
        get() = "schedule_preference"

    class Id: Field.UUIDField(SchedulePreferenceModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class StaffId: Field.UUIDField(SchedulePreferenceModel::staffId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class GoogleCalendarWebhookExpiration: Field.DateTimeField(SchedulePreferenceModel::googleCalendarWebhookExpiration) {
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun less(value: LocalDateTime) = Predicate.less(this, value)
    }

    class GoogleCalendarWebhookChannelId: Field.UUIDField(SchedulePreferenceModel::googleCalendarWebhookChannelId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class GoogleRefreshToken: Field.TextField(SchedulePreferenceModel::googleRefreshToken) {
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class ZoomRefreshToken: Field.TextField(SchedulePreferenceModel::zoomRefreshToken) {
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class HasStaffSchedules: Field.BooleanField(SchedulePreferenceModel::hasStaffSchedules)

    class StatusField: Field.TextField(SchedulePreferenceModel::status) {
        fun eq(value: Status) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val staffId = StaffId()
        val googleCalendarWebhookExpiration = GoogleCalendarWebhookExpiration()
        val googleCalendarWebhookChannelId = GoogleCalendarWebhookChannelId()
        val googleRefreshToken = GoogleRefreshToken()
        val hasStaffSchedules = HasStaffSchedules()
        val zoomRefreshToken = ZoomRefreshToken()
        val status = StatusField()
    }

    class OrderingOptions {
        val id = Id()
        val staffId = StaffId()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<SchedulePreferenceModel>, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun add(model: SchedulePreferenceModel): Result<SchedulePreferenceModel, Throwable>
    override suspend fun update(model: SchedulePreferenceModel): Result<SchedulePreferenceModel, Throwable>
    override suspend fun get(id: UUID): Result<SchedulePreferenceModel, Throwable>
}
