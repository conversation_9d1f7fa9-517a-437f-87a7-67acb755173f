package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.math.BigDecimal
import java.util.UUID

data class PersonDefaulterModel(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val type: PersonDefaulterType,
    val value: BigDecimal,
    val version: Int = 0,
): Model, PersonReference
