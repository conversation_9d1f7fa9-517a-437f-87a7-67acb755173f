package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberProductPriceAdjustmentModel(
    override val id: UUID = RangeUUID.generate(),
    val memberId: UUID,
    val productPriceAdjustmentId: UUID,
    val baseMemberProductPriceId: UUID,
    val adjustedMemberProductPriceId: UUID,
    val referenceDate: LocalDate,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): Model
