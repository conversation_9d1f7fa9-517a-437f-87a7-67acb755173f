plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.data.models.person-domain-service"
version = aliceDataLayerVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    kapt(project(":common"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
