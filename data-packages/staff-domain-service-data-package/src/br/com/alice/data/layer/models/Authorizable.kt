package br.com.alice.data.layer.models

import br.com.alice.common.core.Role
import br.com.alice.common.core.Role.*

interface Authorizable {
    val role: Role

    fun allRoles() = listOf(role)

    fun isProductTech() = listOf(
        PRODUCT_TECH,
        PRODUCT_TECH_HEALTH,
        PRODUCT_TECH_STAFF_EDITOR,
        PRODUCT_TECH_HEALTH_STAFF_EDITOR
    ).contains(role)

    fun isProductTechHealth() = listOf(PRODUCT_TECH_HEALTH, PRODUCT_TECH_HEALTH_STAFF_EDITOR).contains(role)

    fun isProductTechStaffEditor() = listOf(PRODUCT_TECH_STAFF_EDITOR, PRODUCT_TECH_HEALTH_STAFF_EDITOR).contains(role)

    fun isDeIdentifiedHIViewer() = role == DE_IDENTIFIED_HI_VIEWER

    fun isEitaUser() =
        this.isDigitalCareNurse() ||
                this.isCareCoordNurse() ||
                this.isHealthcareTeamNurse() ||
                this.isHealthOps() ||
                this.isInsuranceOpsHealthInstitutionOps() ||
                this.isOnSiteNurse() ||
                this.isTechniqueNurse() ||
                this.isInsuranceOpsCommunitySuccess() ||
                this.isRiskNurse() ||
                this.isChiefNavigator() || this.isChiefDigitalCareNurse() ||
                this.isMedRisk() ||
                this.isNavigator() ||
                this.isScreeningNurse() ||
                this.isChiefRisk() ||
                this.isQualityNurse()

    fun isAssistanceCare() = isDigitalCareNurse() || isDigitalCarePhysician()

    fun isCommunity() = role == COMMUNITY || role == HEALTH_COMMUNITY

    fun isChief() = isChiefPhysician() || isChiefDigitalCare() || isChiefNavigator() || isChiefDigitalCareNurse()

    fun isChiefPhysician() = role == CHIEF_PHYSICIAN

    fun isChiefDigitalCareNurse() = role == CHIEF_DIGITAL_CARE_NURSE

    fun isChiefDigitalCarePhysician() = role == CHIEF_DIGITAL_CARE_PHYSICIAN

    fun isChiefDigitalCare() = isChiefDigitalCareNurse() ||
            isChiefDigitalCarePhysician()

    fun isCXOps() = role == CX_OPS

    fun isFromMultiTeam() = isNutritionist() ||
            isPhysicalEducator() ||
            isPsychologist()

    fun isHealthCommunity() = role == HEALTH_COMMUNITY

    fun isInsuranceOpsHealthInstitutionOps() = role == INSURANCE_OPS_HEALTH_INSTITUTION_OPS

    fun isRiskNurse() =
        listOf(HEALTH_DECLARATION_NURSE, RISK_NURSE, CHIEF_RISK).contains(role)

    fun isHealthDeclarationNurse() =
        listOf(HEALTH_DECLARATION_NURSE, RISK_INTERMITTENT_NURSE).contains(role)

    fun isIntermittentRiskNurse() =
        listOf(RISK_INTERMITTENT_NURSE).contains(role)

    fun isHealthProfessional() = isPhysician() || isNurse() || isFromMultiTeam() || isCommunity()

    fun isHealthProfessionalOrNavigator() = isPhysician() ||
            isNurseOrNavigator() ||
            isFromMultiTeam() ||
            isCommunity() ||
            isManager() ||
            isMedEx()

    fun isAliceHealthProfessional() = isPhysician() || isNurse() || isFromMultiTeam()

    fun isAliceHealthProfessionalOrNavigator() = isPhysician() ||
            isNurseOrNavigator() ||
            isFromMultiTeam() ||
            isManager() ||
            isMedEx()

    fun isAllowedToGetChat() = isDigitalCareNurse() ||
            isDigitalCarePhysician() ||
            isNavigator() ||
            isChiefDigitalCareNurse() ||
            isQualityNurse() ||
            isVirtualClinicPhysician() ||
            isScreeningNurse()

    fun isAllowedToBeAddedToChannel() = isAliceHealthProfessionalOrNavigator()

    fun isAllowedToPublishPrescriptions() = listOf(
        CHIEF_DIGITAL_CARE_PHYSICIAN,
        DIGITAL_CARE_PHYSICIAN,
        ON_SITE_PHYSICIAN,
        CHIEF_PHYSICIAN,
        MANAGER_PHYSICIAN,
        VIRTUAL_CLINIC_PHYSICIAN,
        VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN
    ).contains(role)

    fun isImmersionTeamNurse() = role == HEALTHCARE_TEAM_NURSE

    fun isImmersionTeamPhysician() = role == MANAGER_PHYSICIAN

    fun isImmersionTeam() = isImmersionTeamNurse() || isImmersionTeamPhysician()

    fun isDigitalCareNurse() = listOf(DIGITAL_CARE_NURSE, CHIEF_DIGITAL_CARE_NURSE).contains(role)

    fun isDigitalScreeningNurse() = listOf(DIGITAL_SCREENING_NURSE).contains(role)

    fun isCareCoordNurse() = role == CARE_COORD_NURSE

    fun isHealthcareTeamNurse() = role == HEALTHCARE_TEAM_NURSE

    fun isOnSiteNurse() = role == ON_SITE_NURSE

    fun isDigitalCarePhysician() = listOf(
        DIGITAL_CARE_PHYSICIAN,
        CHIEF_DIGITAL_CARE_PHYSICIAN
    ).contains(role)

    fun isVirtualClinicPhysician() = listOf(
        VIRTUAL_CLINIC_PHYSICIAN,
        VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN
    ).contains(role)

    fun isScreeningNurse() = role == SCREENING_NURSE

    fun isMedEx() = listOf(MED_EX, QUALITY_NURSE).contains(role)

    fun isNurse() = listOf(
        CARE_COORD_NURSE,
        CHIEF_DIGITAL_CARE_NURSE,
        DIGITAL_CARE_NURSE,
        DIGITAL_SCREENING_NURSE,
        HEALTH_DECLARATION_NURSE,
        RISK_NURSE,
        CHIEF_RISK,
        RISK_INTERMITTENT_NURSE,
        HEALTHCARE_TEAM_NURSE,
        OBSTETRICIAN,
        ON_SITE_NURSE,
        SCREENING_NURSE,
        TECHNIQUE_NURSE,
        QUALITY_NURSE
    ).contains(role)

    fun isNurseOrNavigator() = isNurse() || listOf(
        CHIEF_NAVIGATOR,
        CHIEF_NAVIGATOR_OPS,
        NAVIGATOR,
        NAVIGATOR_OPS,
    ).contains(role)

    fun isManager() = listOf(
        MANAGER_NUTRITIONIST,
        MANAGER_PSYCHOLOGIST,
        MANAGER_PHYSICAL_EDUCATOR
    ).contains(role)

    fun isPhysician() = listOf(
        CHIEF_DIGITAL_CARE_PHYSICIAN,
        CHIEF_PHYSICIAN,
        DIGITAL_CARE_PHYSICIAN,
        ON_SITE_PHYSICIAN,
        MANAGER_PHYSICIAN,
        MED_RISK,
        VIRTUAL_CLINIC_PHYSICIAN,
        VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN
    ).contains(role)

    fun isNutritionist() = listOf(NUTRITIONIST, MANAGER_NUTRITIONIST).contains(role)

    fun isPhysicalEducator() = listOf(
        PHYSICAL_EDUCATOR,
        MANAGER_PHYSICAL_EDUCATOR,
    ).contains(role)

    fun isPsychologist() = listOf(
        PSYCHOLOGIST,
        MANAGER_PSYCHOLOGIST,
    ).contains(role)

    fun isChiefRisk() = listOf(CHIEF_RISK).contains(role)

    fun isMedRisk() = role == MED_RISK

    fun isHealthOps() = listOf(
        HEALTH_OPS_LEAD,
        HEALTH_OPS,
    ).contains(role)

    fun isHealthOpsLead() = role == HEALTH_OPS_LEAD

    fun isChiefNavigator() = listOf(
        CHIEF_NAVIGATOR,
        CHIEF_NAVIGATOR_OPS
    ).contains(role)

    fun isNavigator() = listOf(
        CHIEF_NAVIGATOR,
        CHIEF_NAVIGATOR_OPS,
        NAVIGATOR,
        NAVIGATOR_OPS
    ).contains(role)

    fun isNavigatorOps() = listOf(NAVIGATOR_OPS, CHIEF_NAVIGATOR_OPS).contains(role)

    fun isOnSiteProfessional() = listOf(
        ON_SITE_NURSE,
        ON_SITE_PHYSICIAN,
    ).contains(role)

    fun isInsuranceOpsCommunitySuccess() = role == INSURANCE_OPS_COMMUNITY_SUCCESS

    fun isTechniqueNurse() = role == TECHNIQUE_NURSE

    fun isQualityNurse() = role == QUALITY_NURSE

}
