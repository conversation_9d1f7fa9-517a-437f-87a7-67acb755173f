package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class MemberHealthMetric(
    override val personId: PersonId,
    val memberActivationDate: LocalDateTime,
    val memberActivationToImmersionScheduleInDays: Int?,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0
) : Model, PersonReference
