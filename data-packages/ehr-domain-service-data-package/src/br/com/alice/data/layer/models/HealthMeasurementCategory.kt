package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class HealthMeasurementCategory(
    val key: String,
    val name: String,
    val active: Boolean,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override var updatedBy: UpdatedBy? = null,
): Model, UpdatedByReference
