package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.storage.AliceFile
import java.time.LocalDateTime
import java.util.UUID

data class TestResultFile(
    override val personId: PersonId,
    val staffId: UUID? = null,
    val performedAt: LocalDateTime,
    val file: AliceFile? = null,
    val thumbnail: AliceFile? = null,
    val description: String,
    val fileExtension: String,
    val status: TestResultFileStatus = TestResultFileStatus.ACTIVE,
    val uploadedBy: UploadedBy,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, HealthInformation {

    override fun sanitize(): TestResultFile {
        if (description.isBlank()) throw IllegalArgumentException("description is blank")
        return copy(description = description.trim())
    }

    val s3Key
        get() = "$id.$fileExtension"

}

enum class TestResultFileStatus{
    DELETED, ACTIVE
}

enum class UploadedBy{
    MEMBER, STAFF
}
