package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class HealthSpecialistResourceBundle(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val primaryTuss: String,
    var secondaryResources: List<UUID> = emptyList<UUID>(),
    val executionAmount: Int = 1,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY,
    val description: String,
    val code: String = generateRandomCode(),
    val status: Status,
    val searchTokens: String? = null,
    val serviceType: HealthSpecialistResourceBundleServiceType,
    val tempOldAliceCode: String? = null,
    var updatedBy: UpdatedBy? = null
) {
    companion object {
        fun generateRandomCode(): String {
            // We are generating the code with the prefix "80" to avoid conflicts with the old Alice code and
            // to make it easier to identify the new codes by creating a pattern.
            return "80" + (1..6).map { (0..9).random() }.joinToString("")
        }
    }
}


enum class HealthSpecialistProcedureExecutionEnvironment(val description: String) {
    SURGICAL("Cirúrgico"),
    OUTPATIENT("Ambulatorial"),
    DOES_NOT_APPLY("Não se aplica"),
}

enum class HealthSpecialistResourceBundleServiceType(val description: String) {
    PROCEDURE("Procedimento"),
    EXAM("Exame"),
    CONSULTATION("Consulta"),
    UNDEFINED("Indefinido")
}
