package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class Provider(
    val id: UUID = RangeUUID.generate(),
    val name: String,
    val site: String?,
    val cnpj: String?,
    val phones: List<PhoneNumber>,
    val imageUrl: String?,
    val urlSlug: String? = null,
    val searchTokens: String? = null,
    val type: ProviderType,
    val flagship: Boolean = false,
    val description: String? = null,
    val icon: String? = null,
    val logo: String? = null,
    val thumbnail: String? = null,
    val about: String? = null,
    val daysForPayment: Int = 30,
    val version: Int = 0,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val status: Status = Status.ACTIVE,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    var updatedBy: UpdatedBy? = null,
)

enum class ProviderType {
    HOSPITAL,
    LABORATORY,
    ACCOMMODATION,
    MATERNITY,
    CHILDREN,
    CLINICAL,
    CLINICAL_COMMUNITY,
    MEDICAL_COMPANY,
    VACCINE;

    companion object {
        fun hospitalTypes(): List<ProviderType> = listOf(HOSPITAL, MATERNITY, CHILDREN)
        fun hospitalAndLabsTypes(): List<ProviderType> = hospitalTypes() + listOf(LABORATORY)
        fun hospitalTypesAndAccommodation(): List<ProviderType> = hospitalTypes() + listOf(ACCOMMODATION)
    }
}
