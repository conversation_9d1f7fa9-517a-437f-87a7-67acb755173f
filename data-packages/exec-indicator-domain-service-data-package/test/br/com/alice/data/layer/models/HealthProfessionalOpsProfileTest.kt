package br.com.alice.data.layer.models

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class HealthProfessionalOpsProfileTest {

    @Test
    fun `#validate returns success when on call info is valid`() = runBlocking<Unit> {
        val healthProfessionalOpsProfileNotAttendsToOnCall = TestModelFactory.buildHealthProfessionalOpsProfile(
            attendsToOnCall = false,
            onCallPaymentMethod = null
        )
        val resultNotAttendsToOnCall = healthProfessionalOpsProfileNotAttendsToOnCall.validate()
        ResultAssert.assertThat(resultNotAttendsToOnCall).isSuccess()

        val healthProfessionalOpsProfileAttendsToOnCall = TestModelFactory.buildHealthProfessionalOpsProfile(
            attendsToOnCall = true,
            onCallPaymentMethod = OnCallPaymentMethod.ALICE
        )
        val resultAttendsToOnCall = healthProfessionalOpsProfileAttendsToOnCall.validate()
        ResultAssert.assertThat(resultAttendsToOnCall).isSuccess()
    }

    @Test
    fun `#validate returns error when attends to on call and have payment method`() = runBlocking<Unit> {
        val healthProfessionalOpsProfile = TestModelFactory.buildHealthProfessionalOpsProfile(
            attendsToOnCall = true,
            onCallPaymentMethod = null
        )
        val result = healthProfessionalOpsProfile.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade que atende em retaguarda deve ter um método de pagamento definido")
            .ofType(InvalidArgumentException::class)
    }

}
