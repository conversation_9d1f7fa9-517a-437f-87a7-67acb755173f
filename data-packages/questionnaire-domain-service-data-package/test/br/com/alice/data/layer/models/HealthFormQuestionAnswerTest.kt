package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthFormQuestionAnswerTest {

    @Test
    fun `#getAnswerText should return free text after @ in multi select`() {
        val result = TestModelFactory.buildHealthFormQuestionAnswer(answer = "<PERSON><PERSON><PERSON>;Pai;Irm<PERSON>@To suave").getAnswerText()

        assertThat(result).isEqualTo("To suave")
    }

    @Test
    fun `#getAnswerText should return free text after @ in select`() {
        val result = TestModelFactory.buildHealthFormQuestionAnswer(answer = "Sim@To suave").getAnswerText()

        assertThat(result).isEqualTo("To suave")
    }

    @Test
    fun `#getAnswers should return answers from multi select`() {
        val result = TestModelFactory.buildHealthFormQuestionAnswer(answer = "<PERSON><PERSON><PERSON>;<PERSON><PERSON>;<PERSON><PERSON><PERSON>@To suave").getAnswers()

        assertThat(result).isEqualTo(listOf("<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"))
    }

    @Test
    fun `#getAnswers should return answers from select`() {
        val result = TestModelFactory.buildHealthFormQuestionAnswer(answer = "Mãe@To suave").getAnswers()

        assertThat(result).isEqualTo(listOf("Mãe"))
    }

    @Test
    fun `#getAnswersForMultiDimension should return answers from select`() {
        val result = TestModelFactory.buildHealthFormQuestionAnswer(answer = "seg:leve@60;seg:intenso@10;ter:moderado@30").getAnswersForMultiDimension()
        val expectedResult = arrayListOf(
            mapOf(
                "keys" to listOf("seg", "leve"),
                "value" to "60"
            ),
            mapOf(
                "keys" to listOf("seg", "intenso"),
                "value" to "10"
            ),
            mapOf(
                "keys" to listOf("ter", "moderado"),
                "value" to "30"
            ),
        )
        assertThat(result).isEqualTo(expectedResult)
    }

    @Test
    fun `#getAnswer should return a list if answer contains multiple values`() {
        val answer = TestModelFactory.buildHealthFormQuestionAnswer(answer = "seg:leve@60;seg:intenso@10;ter:moderado@30")

        val expectedResult = arrayListOf(
            mapOf(
                "keys" to listOf("seg", "leve"),
                "value" to "60"
            ),
            mapOf(
                "keys" to listOf("seg", "intenso"),
                "value" to "10"
            ),
            mapOf(
                "keys" to listOf("ter", "moderado"),
                "value" to "30"
            ),
        )

        assertThat(answer.getAnswer()).isEqualTo(expectedResult)
    }

    @Test
    fun `#getAnswer should return a list if answer is empty string`() {
        val answer = TestModelFactory.buildHealthFormQuestionAnswer(answer = "")

        val expectedResult = emptyList<Map<String, Any>>(
        )

        assertThat(answer.getAnswer()).isEqualTo(expectedResult)
    }

    @Test
    fun `#getAnswer should return a double if answer does not contains multiple values`() {
        val answer = TestModelFactory.buildHealthFormQuestionAnswer(answer = "1.0")

        assertThat(answer.getAnswer()).isEqualTo(1.0)

    }
}
