package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.EinsteinStructuredTestResult
import br.com.alice.data.layer.services.EinsteinStructuredTestResultDataService.FieldOptions
import br.com.alice.data.layer.services.EinsteinStructuredTestResultDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface EinsteinStructuredTestResultDataService : Service,
    Finder<FieldOptions, OrderingOptions, EinsteinStructuredTestResult>,
    Adder<EinsteinStructuredTestResult>,
    Get<PERSON><EinsteinStructuredTestResult> {

    override val namespace: String
        get() = "einstein"
    override val serviceName: String
        get() = "structured_test_result"

    class EinsteinCodeField : Field.TextField(EinsteinStructuredTestResult::einsteinCode) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class EinsteinTestResultField : Field.UUIDField(EinsteinStructuredTestResult::einsteinTestResultId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonIdField : Field.TableIdField(EinsteinStructuredTestResult::personId)

    class FieldOptions {
        val einsteinCode = EinsteinCodeField()
        val einsteinTestResultId = EinsteinTestResultField()
        val personId = PersonIdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: EinsteinStructuredTestResult): Result<EinsteinStructuredTestResult, Throwable>
    override suspend fun get(id: UUID): Result<EinsteinStructuredTestResult, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<EinsteinStructuredTestResult>, Throwable>
}
