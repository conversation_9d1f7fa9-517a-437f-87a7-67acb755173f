package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AbTestSpecialistRecommendation
import br.com.alice.data.layer.models.HealthConditionTemplate
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthConditionTemplateDataService : Service,
    Adder<HealthConditionTemplate>,
    Getter<HealthConditionTemplate>,
    Updater<HealthConditionTemplate>,
    Finder<HealthConditionTemplateDataService.FieldOptions, HealthConditionTemplateDataService.OrderingOptions, HealthConditionTemplate>,
    Counter<HealthConditionTemplateDataService.FieldOptions, HealthConditionTemplateDataService.OrderingOptions, HealthConditionTemplate> {

    override val namespace: String
        get() = "health_condition"
    override val serviceName: String
        get() = "health_condition_template"

    class FieldOptions {
        val id = Id()
        val healthConditionId = HealthConditionId()
    }

    class OrderingOptions {
    }

    class Id : Field.UUIDField(HealthConditionTemplate::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class HealthConditionId : Field.UUIDField(HealthConditionTemplate::healthConditionId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: HealthConditionTemplate): Result<HealthConditionTemplate, Throwable>
    override suspend fun update(model: HealthConditionTemplate): Result<HealthConditionTemplate, Throwable>
    override suspend fun get(id: UUID): Result<HealthConditionTemplate, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthConditionTemplate>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
