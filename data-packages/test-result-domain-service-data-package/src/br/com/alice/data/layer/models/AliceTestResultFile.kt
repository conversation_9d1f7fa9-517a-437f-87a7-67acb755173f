package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import com.google.gson.annotations.Expose
import java.util.UUID

data class AliceTestResultFile(
    override val id: UUID = RangeUUID.generate(),
    @Expose
    override val personId: PersonId,
    val referencedModelId: UUID,
    val referencedModelClass: String,
    val referencedFileId: String,
    val version: Int = 0,
) : Model, PersonReference, ExternalHealthInformation
