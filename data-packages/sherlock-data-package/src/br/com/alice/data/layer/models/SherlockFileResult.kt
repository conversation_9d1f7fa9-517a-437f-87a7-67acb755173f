package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class SherlockFileResult(
    val staffId: UUID,
    val fileId: UUID,
    val fileType: VaultFileType,
    val reason: String,
    val status: QueryStatus,
    val url: String? = null,
    val errorMessage: String? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
) : Model

enum class VaultFileType {
    PERSON,
    STAFF,
    PUBLIC,
    EXEC_AUTHORIZER
}
