package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class TissBatchErrorModel(
    override val id: UUID = RangeUUID.generate(),
    val description: String,
    val lineNumber: Int? = null,
    val tissBatchId: UUID,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model
