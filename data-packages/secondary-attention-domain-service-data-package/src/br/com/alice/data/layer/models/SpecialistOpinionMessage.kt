package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class SpecialistOpinionMessage(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val specialistOpinionId: UUID,
    val staffId: UUID,
    val message: String,
    val followUpWith: TypeFollowUpWith? = null
) : Model

enum class TypeFollowUpWith {
    PRIMARY_ATTENTION, SECONDARY_ATTENTION
}

