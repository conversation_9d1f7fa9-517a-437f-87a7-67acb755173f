package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CounterReferral(
    override val personId: PersonId,
    val referralId: UUID?,
    val healthCommunityUnreferencedAccessId: UUID? = null,
    @Deprecated("Use staffId instead")
    val healthCommunitySpecialistId: UUID? = null,
    val staffId: UUID,
    val typeOfService: CounterReferralTypeOfService? = null,
    val appointmentDate: LocalDate,
    val typeOfAppointment: CounterReferralTypeOfAppointment? = null,
    val presential: Boolean? = null,
    val includedInPackage: Boolean = false,
    val attendanceType: AttendanceType? = null,
    val diagnosticHypothesis: String? = null,
    val clinicalEvaluation: String? = null,
    val memberOrientation: String? = null,
    val healthcareTeamOrientation: String,
    val perceptionOfCase: String? = null,
    val therapeuticPlan: String? = null,
    val lastSession: Boolean? = null,
    val followUpDate: LocalDate? = null,
    val followUpDays: Int? = null,
    val testRequests: List<CounterReferralGenericTask>? = emptyList(),
    val referrals: List<CounterReferralGenericTask>? = emptyList(),
    val procedures: List<CounterReferralGenericTask>? = emptyList(),
    val surgicalReferrals: List<CounterReferralGenericTask>? = emptyList(),
    val medicines: List<CounterReferralGenericTask>? = emptyList(),
    val appointmentOccurred: Boolean? = null,
    val notOccurredReason: NotOccurredReason? = null,
    val isEligible: Boolean? = null,
    val healthcareTeamOrientationDetail: String? = null,
    val discussWithHealthProfessional: String? = null,
    val caseRecordDetails: List<CaseRecordDetails> = emptyList(),
    val nextStepSpecialistInfo: NextStepSpecialistInfo? = null,
    val nextStepPrimaryAttentionInfo: NextStepPrimaryAttentionInfo? = null,
    val appointmentId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference, HealthInformation {
    fun isCoordinated(): Boolean {
        return this.healthCommunityUnreferencedAccessId == null
    }

    fun isAppointmentOccurred(): Boolean {
        return this.appointmentOccurred != false
    }

    fun hasCoordinatedFollowUp(): Boolean =
        this.referralId != null && (this.followUpDays != null || hasExceptionFollowUp())

    fun hasFollowUpWithoutBaseReferral(): Boolean = this.referralId == null && this.followUpDays != null
    fun hasCoordinatedTestRequests(): Boolean = this.referralId != null && this.testRequests.isNotNullOrEmpty()
    fun hasTestRequestsWithoutBaseReferral(): Boolean = this.referralId == null && this.testRequests.isNotNullOrEmpty()

    fun hasExceptionFollowUp(): Boolean =
        this.nextStepSpecialistInfo?.type == NextStepSpecialistType.EXCEPTION_FOLLOW_UP

    fun isHospitalAttendance() = this.attendanceType != null && this.attendanceType == AttendanceType.HOSPITAL
}

data class NextStepPrimaryAttentionInfo(
    val type: NextStepPrimaryAttentionType,
    val recommendations: String? = null
) : JsonSerializable

enum class NextStepPrimaryAttentionType {
    YES, NO
}

data class NextStepSpecialistInfo(
    val type: NextStepSpecialistType,
    val followUpInDays: Int? = null
) : JsonSerializable

enum class NextStepSpecialistType {
    DISCHARGED,
    FOLLOW_UP_APPOINTMENT,
    EXCEPTION_FOLLOW_UP,
    FOLLOW_UP_SESSIONS,
    STOPPED_BY_MISSING_REFERRAL
}

enum class CounterReferralTypeOfService(val description: String) {
    APPOINTMENT_ONLY("Somente consulta"),
    OUTPATIENT_PROCEDURE_ONLY("Somente procedimento ambulatorial"),
    OUTPATIENT_PROCEDURE("Consultas e procedimentos ambulatoriais"),
    SURGICAL_PROCEDURE("Procedimento cirúrgico")
}

fun CounterReferralTypeOfService.notSirurgicalReferral(): Boolean =
    this != CounterReferralTypeOfService.SURGICAL_PROCEDURE

enum class CounterReferralTypeOfAppointment(val description: String) {
    REGULAR("Regular"),
    FOLLOW_UP("Retorno"),
    CONTINUOUS_CARE("Contínuo - Terapia")
}

data class CounterReferralGenericTask(
    val description: String? = "",
    val code: String? = "",
    val attachments: List<Attachment>? = emptyList(),
    val caseRecordValues: List<String>? = emptyList()
)

enum class AttendanceType(val description: String) {
    CLINIC("Consultório"),
    RESIDENTIAL("Residencial"),
    HOSPITAL("Hospitalar"),
    VIRTUAL("Virtual"),
}

enum class NotOccurredReason(val description: String) {
    NO_SHOW("No show"),
    LATE_CANCEL("Late cancel")
}
