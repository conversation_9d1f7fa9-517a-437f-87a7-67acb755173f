package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ZendeskExternalReference.DestinationModel
import br.com.alice.data.layer.models.ZendeskExternalReference.OriginModel
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import kotlin.test.Test
class ZendeskExternalReferenceTest {

    @Nested
    inner class ZendeskExternalReferencePerson {

        @Test
        fun `#create ZendeskExternalReference from person should define correct destination model`() {
            val personReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.PERSON,
                RangeUUID.generate(RangeUUID.PERSON_ID_RANGE).toString(),
                233
            )

            Assertions.assertThat(personReference.destinationModel).isEqualTo(DestinationModel.USER)
        }

        @Test
        fun `#create ZendeskExternalReference from person with wrong destination should override with correct destination`() {
            val personMessyReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.PERSON,
                RangeUUID.generate(RangeUUID.PERSON_ID_RANGE).toString(),
                4343,
                destinationModel = DestinationModel.ORGANIZATION,
            )
            Assertions.assertThat(personMessyReference.destinationModel).isEqualTo(DestinationModel.USER)
        }

    }

    @Nested
    inner class ZendeskExternalReferenceCompany {
        @Test
        fun `#create ZendeskExternalReference from company should define correct destination model`() {
            val companyReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.COMPANY,
                RangeUUID.generate().toString(),
                123
            )
            Assertions.assertThat(companyReference.destinationModel).isEqualTo(DestinationModel.ORGANIZATION)
        }

        @Test
        fun `#create ZendeskExternalReference from company with wrong destination should override with correct destination`() {
            val companyMessyReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.COMPANY,
                RangeUUID.generate().toString(),
                123,
                destinationModel = DestinationModel.USER
            )
            Assertions.assertThat(companyMessyReference.destinationModel).isEqualTo(DestinationModel.ORGANIZATION)
        }

    }

    @Nested
    inner class ZendeskExternalReferenceProvider {
        @Test
        fun `#create ZendeskExternalReference from provider should define correct destination model`() {
            val providerReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.PROVIDER,
                RangeUUID.generate().toString(),
                444
            )
            Assertions.assertThat(providerReference.destinationModel).isEqualTo(DestinationModel.ORGANIZATION)
        }

        @Test
        fun `#create ZendeskExternalReference from provider with wrong destination should override with correct destination`() {
            val providerMessyReference = ZendeskExternalReference(
                RangeUUID.generate(),
                OriginModel.PROVIDER,
                RangeUUID.generate().toString(),
                444,
                destinationModel = DestinationModel.USER
            )
            Assertions.assertThat(providerMessyReference.destinationModel).isEqualTo(DestinationModel.ORGANIZATION)
        }

    }
}
