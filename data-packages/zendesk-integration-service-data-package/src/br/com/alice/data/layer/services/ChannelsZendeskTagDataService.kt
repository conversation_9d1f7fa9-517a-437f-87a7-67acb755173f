package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ChannelsZendeskTag
import com.github.kittinunf.result.Result
import java.util.UUID


@RemoteService
interface ChannelsZendeskTagDataService: Service,
    Finder<ChannelsZendeskTagDataService.FieldOptions,
            ChannelsZendeskTagDataService.OrderingOptions,
            ChannelsZendeskTag>,
    Updater<ChannelsZendeskTag>,
    Getter<ChannelsZendeskTag>,
    Adder<ChannelsZendeskTag> {

    override val namespace: String
        get() = "zendesk"

    override val serviceName: String
        get() = "channels_zendesk_tag"

    class ChannelTagField: Field.TextField(ChannelsZendeskTag::channelTag) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val channelTag = ChannelTagField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ChannelsZendeskTag): Result<ChannelsZendeskTag, Throwable>
    override suspend fun get(id: UUID): Result<ChannelsZendeskTag, Throwable>
    override suspend fun update(model: ChannelsZendeskTag): Result<ChannelsZendeskTag, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ChannelsZendeskTag>, Throwable>
    suspend fun findByChannelTag(channelTag: String): Result<ChannelsZendeskTag, Throwable> =
        findOne {
            where { this.channelTag.eq(channelTag) }
        }
    suspend fun findByChannelTags(tags: List<String>): Result<ChannelsZendeskTag, Throwable> =
        findOne {
            where { this.channelTag.inList(tags) }
        }
    @OptIn(QueryAllUsage::class)
    suspend fun getAll(): Result<List<ChannelsZendeskTag>, Throwable> = find { all() }
}
