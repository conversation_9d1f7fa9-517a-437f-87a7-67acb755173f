package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ZendeskExternalReference
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ZendeskExternalReferenceDataService : Service,
Finder<ZendeskExternalReferenceDataService.FieldOptions,
        ZendeskExternalReferenceDataService.OrderingOptions, ZendeskExternalReference>,
        Adder<ZendeskExternalReference>,
        Getter<ZendeskExternalReference>,
        Updater<ZendeskExternalReference> {

    override val namespace: String
        get() = "zendesk"
    override val serviceName: String
        get() = "zendesk_external_reference"

    class OriginModelId : Field.TextField(ZendeskExternalReference::originModelId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class OriginModel : Field.TextField(ZendeskExternalReference::originModel) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class DestinationId : Field.LongField(ZendeskExternalReference::destinationId) {
        fun eq(value: Long) = Predicate.eq(this, value)
    }

    class DestinationModel : Field.TextField(ZendeskExternalReference::destinationModel) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class CreatedAtField : Field.DateTimeField(ZendeskExternalReference::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class FieldOptions {
        val originModelId = OriginModelId()
        val originModel = OriginModel()
        val destinationId = DestinationId()
        val destinationModel = DestinationModel()
        val createdAt = CreatedAtField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun findByOriginIdAndOriginModel(id: String, modelOrigin: ZendeskExternalReference.OriginModel) =
        findOne {
            where { this.originModelId.eq(id) and this.originModel.eq(modelOrigin.name) }
        }

    suspend fun findByDestinationIdAndDestinationModel(id: Long, destinationModel: ZendeskExternalReference.DestinationModel) =
        findOne {
            where { this.destinationId.eq(id) and this.destinationModel.eq(destinationModel.name) }
        }

    override suspend fun get(id: UUID): Result<ZendeskExternalReference, Throwable>

    override suspend fun add(model: ZendeskExternalReference): Result<ZendeskExternalReference, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<ZendeskExternalReference>, Throwable>

    override suspend fun update(model: ZendeskExternalReference): Result<ZendeskExternalReference, Throwable>

}
