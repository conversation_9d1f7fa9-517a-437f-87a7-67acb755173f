package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class FaqContent(
    val id: UUID = RangeUUID.generate(),
    val title: String,
    val description: String? = null,
    val groupIds: List<UUID> = emptyList(),
    val imageUrl: String? = null,
    val active: Boolean,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
