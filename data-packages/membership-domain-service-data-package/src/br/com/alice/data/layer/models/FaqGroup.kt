package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class FaqGroup(
    val id: UUID = RangeUUID.generate(),
    val title: String,
    val description: String? = null,
    val featured: Boolean,
    val active: Boolean,
    val brand: Brand? = Brand.ALICE,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
