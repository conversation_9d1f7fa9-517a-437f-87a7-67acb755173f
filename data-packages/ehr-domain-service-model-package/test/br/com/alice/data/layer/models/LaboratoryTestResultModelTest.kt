package br.com.alice.data.layer.models

import br.com.alice.common.core.PersonId
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import java.time.LocalDateTime
import kotlin.test.Test

class LaboratoryTestResultModelTest {

    @Test
    fun `sanitize trim value`() {
        val laboratoryTestResult = LaboratoryTestResultModel(PersonId(), LocalDateTime.now(), LaboratoryTestResultModel.Key.ACIDO_URICO, null, " 12 ")

        assertThat(laboratoryTestResult.sanitize().value).isEqualTo("12")
    }

    @Test
    fun `sanitize cannot have blank value`() {
        val laboratoryTestResult = LaboratoryTestResultModel(PersonId(), LocalDateTime.now(), LaboratoryTestResultModel.Key.ACIDO_URICO, null, " ")

        assertThatThrownBy {
            laboratoryTestResult.sanitize()
        }.isInstanceOf(IllegalArgumentException::class.java)
            .hasMessage("value is blank")
    }

}
