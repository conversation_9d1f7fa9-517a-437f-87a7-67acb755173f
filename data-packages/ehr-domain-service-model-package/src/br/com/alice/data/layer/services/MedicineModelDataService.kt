package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MedicineModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MedicineModelDataService : Service,
    Finder<MedicineModelDataService.FieldOptions, MedicineModelDataService.OrderingOptions, MedicineModel>,
    Counter<MedicineModelDataService.FieldOptions, MedicineModelDataService.OrderingOptions, MedicineModel>,
    Adder<MedicineModel>,
    Getter<MedicineModel>,
    Updater<MedicineModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "medicine"

    class Ean: Field.TextField(MedicineModel::ean) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class Name: Field.TextField(MedicineModel::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class SearchTokens: Field.TextField(MedicineModel::searchTokens) {
        fun search(value: String) = Predicate.rankedSearch(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class Id : Field.UUIDField(MedicineModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Portaria344 : Field.TextField(MedicineModel::portaria344) {
        fun isNull() = Predicate.isNull(this)
    }

    class AtcCode : Field.TextField(MedicineModel::atcCode) {
        @OptIn(LikePredicateUsage::class)
        fun startsWith(value: String) = Predicate.startsWith(this, value)
    }

    class FieldOptions {
        val ean = Ean()
        val name = Name()
        val id = Id()
        val searchTokens = SearchTokens()
        val portaria344 = Portaria344()
        val atcCode = AtcCode()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: MedicineModel): Result<MedicineModel, Throwable>
    override suspend fun update(model: MedicineModel): Result<MedicineModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<MedicineModel>, Throwable>
    override suspend fun get(id: UUID): Result<MedicineModel, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
