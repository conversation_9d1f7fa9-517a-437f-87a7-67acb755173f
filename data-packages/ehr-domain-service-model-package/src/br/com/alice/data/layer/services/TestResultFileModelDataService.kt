package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.TestResultFileModel
import br.com.alice.data.layer.models.TestResultFileStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TestResultFileModelDataService : Service,
    Finder<TestResultFileModelDataService.FieldOptions, TestResultFileModelDataService.OrderingOptions, TestResultFileModel>,
    Updater<TestResultFileModel>,
    Adder<TestResultFileModel>,
    Getter<TestResultFileModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "test_result_file"

    class PersonIdField : Field.TableIdField(TestResultFileModel::personId)
    class StaffIdField: Field.UUIDField(TestResultFileModel::staffId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class StatusField : Field.TextField(TestResultFileModel::status) {
        fun eq(value: TestResultFileStatus) = Predicate.eq(this, value)
    }

    class IdField: Field.UUIDField(TestResultFileModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val status = StatusField()
        val staffId = StaffIdField()
        val id = IdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun findByPersonId(personId: String) =
        find { where { this.personId.eq(personId).and(status.eq(TestResultFileStatus.ACTIVE)) } }

    override suspend fun findByQuery(query: Query): Result<List<TestResultFileModel>, Throwable>
    override suspend fun add(model: TestResultFileModel): Result<TestResultFileModel, Throwable>
    override suspend fun get(id: UUID): Result<TestResultFileModel, Throwable>
    override suspend fun update(model: TestResultFileModel): Result<TestResultFileModel, Throwable>

}
