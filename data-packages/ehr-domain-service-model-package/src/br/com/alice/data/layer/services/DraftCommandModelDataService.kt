package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.DraftCommandModel
import br.com.alice.data.layer.services.DraftCommandModelDataService.FieldOptions
import br.com.alice.data.layer.services.DraftCommandModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface DraftCommandModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, DraftCommandModel>,
    Counter<FieldOptions, OrderingOptions, DraftCommandModel>,
    Adder<DraftCommandModel>,
    Getter<DraftCommandModel>,
    Updater<DraftCommandModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "draft_command"

    class AppointmentIdField: Field.UUIDField(DraftCommandModel::appointmentId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ActionField: Field.TextField(DraftCommandModel::action) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ReferencedModelField: Field.TextField(DraftCommandModel::referencedModel) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class StatusField: Field.TextField(DraftCommandModel::status) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class CreatedAtField: Field.DateTimeField(DraftCommandModel::createdAt)

    class FieldOptions {
        val appointmentId = AppointmentIdField()
        val action = ActionField()
        val referencedModel = ReferencedModelField()
        val status = StatusField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<DraftCommandModel, Throwable>
    override suspend fun add(model: DraftCommandModel): Result<DraftCommandModel, Throwable>
    override suspend fun update(model: DraftCommandModel): Result<DraftCommandModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<DraftCommandModel>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
}
