package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MemberOnboardingTemplate
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingTemplateDataService : Service,
    Adder<MemberOnboardingTemplate>,
    Finder<MemberOnboardingTemplateDataService.FieldOptions, MemberOnboardingTemplateDataService.OrderingOptions, MemberOnboardingTemplate>,
    Counter<MemberOnboardingTemplateDataService.FieldOptions, MemberOnboardingTemplateDataService.OrderingOptions, MemberOnboardingTemplate>,
    Updater<MemberOnboardingTemplate>,
    Getter<MemberOnboardingTemplate> {

    override val namespace: String
        get() = "person"
    override val serviceName: String
        get() = "member_onboarding_template"

    class IdField: Field.UUIDField(MemberOnboardingTemplate::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class MemberOnboardingTemplateType: Field.TextField(MemberOnboardingTemplate::type) {
        fun eq(value: MemberOnboardingTemplate.MemberOnboardingTemplateType) = Predicate.eq(this, value)
    }

    class ActiveField: Field.BooleanField(MemberOnboardingTemplate::active)

    class CreatedAt: Field.DateTimeField(MemberOnboardingTemplate::createdAt)

    class FieldOptions {
        val id = IdField()
        val type = MemberOnboardingTemplateType()
        val active = ActiveField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override suspend fun add(model: MemberOnboardingTemplate): Result<MemberOnboardingTemplate, Throwable>

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<MemberOnboardingTemplate>, Throwable>
    override suspend fun get(id: UUID): Result<MemberOnboardingTemplate, Throwable>
    override suspend fun update(model: MemberOnboardingTemplate): Result<MemberOnboardingTemplate, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
}
