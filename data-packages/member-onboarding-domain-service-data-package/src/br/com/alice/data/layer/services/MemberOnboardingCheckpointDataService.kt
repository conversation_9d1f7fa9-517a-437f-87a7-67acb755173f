package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingCheckpointDataService : Service,
    Adder<MemberOnboardingCheckpoint>,
    Finder<MemberOnboardingCheckpointDataService.FieldOptions, MemberOnboardingCheckpointDataService.OrderingOptions, MemberOnboardingCheckpoint>,
    Counter<MemberOnboardingCheckpointDataService.FieldOptions, MemberOnboardingCheckpointDataService.OrderingOptions, MemberOnboardingCheckpoint>,
    Updater<MemberOnboardingCheckpoint>,
    Getter<MemberOnboardingCheckpoint> {

    override val namespace: String
        get() = "person"
    override val serviceName: String
        get() = "member_onboarding_checkpoint"

    class IdField: Field.UUIDField(MemberOnboardingCheckpoint::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonIdField: Field.TableIdField(MemberOnboardingCheckpoint::personId)

    class CreatedAt: Field.DateTimeField(MemberOnboardingCheckpoint::createdAt)

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override suspend fun add(model: MemberOnboardingCheckpoint): Result<MemberOnboardingCheckpoint, Throwable>

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<MemberOnboardingCheckpoint>, Throwable>
    override suspend fun get(id: UUID): Result<MemberOnboardingCheckpoint, Throwable>
    override suspend fun update(model: MemberOnboardingCheckpoint): Result<MemberOnboardingCheckpoint, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
}
