package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.*
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingDataService: Service,
    Adder<MemberOnboarding>,
    Getter<MemberOnboarding>,
    Updater<MemberOnboarding>,
    Finder<MemberOnboardingDataService.FieldOptions, MemberOnboardingDataService.OrderingOptions, MemberOnboarding>,
    Counter<MemberOnboardingDataService.FieldOptions, MemberOnboardingDataService.OrderingOptions, MemberOnboarding>,
    Deleter<MemberOnboarding>,
    DeleterList<MemberOnboarding> {

    override val namespace: String
        get() = "member_onboarding"
    override val serviceName: String
        get() = "member_onboarding"

    class IdField: Field.UUIDField(MemberOnboarding::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField: Field.UUIDField(MemberOnboarding::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class ReferencedLinkModelField: Field.JsonbField(MemberOnboarding::referencedLinks) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: MemberOnboardingReferencedLinkModel) = Predicate.jsonSearch(this, "[{\"model\": \"${value}\"}]")
    }

    class CompletedField: Field.BooleanField(MemberOnboarding::completed)

    class CreatedAtField: Field.DateTimeField(MemberOnboarding::createdAt)

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val completed = CompletedField()
        val referencedLinkModel = ReferencedLinkModelField()
    }

    class OrderingOptions {
        val id = IdField()
        val personId = PersonIdField()
        val completed = CompletedField()
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: MemberOnboarding): Result<MemberOnboarding, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<MemberOnboarding>, Throwable>
    override suspend fun get(id: UUID): Result<MemberOnboarding, Throwable>
    override suspend fun update(model: MemberOnboarding): Result<MemberOnboarding, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun delete(model: MemberOnboarding): Result<Boolean, Throwable>
    override suspend fun deleteList(models: List<MemberOnboarding>): Result<List<Boolean>, Throwable>
}
