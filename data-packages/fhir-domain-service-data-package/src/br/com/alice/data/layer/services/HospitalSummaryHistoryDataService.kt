package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HospitalSummaryHistory
import br.com.alice.data.layer.models.SummaryHistoryEventType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HospitalSummaryHistoryDataService : Service, Adder<HospitalSummaryHistory>, Getter<HospitalSummaryHistory>,
    Updater<HospitalSummaryHistory>,
    Finder<HospitalSummaryHistoryDataService.FieldOptions, HospitalSummaryHistoryDataService.OrderingOptions, HospitalSummaryHistory> {

    override val namespace: String
        get() = "interop"
    override val serviceName: String
        get() = "hospital_summary_history"

    class Id : Field.UUIDField(HospitalSummaryHistory::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class EventId : Field.UUIDField(HospitalSummaryHistory::eventId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class EventType : Field.TextField(HospitalSummaryHistory::eventType) {
        fun eq(value: SummaryHistoryEventType) = Predicate.eq(this, value)
        fun inList(value: List<SummaryHistoryEventType>) = Predicate.inList(this, value)
    }

    class PersonId : Field.TableIdField(HospitalSummaryHistory::personId)
    class FieldOptions {
        val id = Id()
        val eventId = EventId()
        val eventType = EventType()
        val personId = PersonId()
    }

    class OrderingOptions {
        val id = Id()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(), OrderingOptions()
    )

    override suspend fun add(model: HospitalSummaryHistory): Result<HospitalSummaryHistory, Throwable>
    override suspend fun update(model: HospitalSummaryHistory): Result<HospitalSummaryHistory, Throwable>
    override suspend fun get(id: UUID): Result<HospitalSummaryHistory, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HospitalSummaryHistory>, Throwable>
}
