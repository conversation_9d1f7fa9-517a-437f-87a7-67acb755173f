package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.FhirProviderAccess
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FhirProviderAccessDataService : Service,
    Adder<FhirProviderAccess>,
    Finder<FhirProviderAccessDataService.FieldOptions, FhirProviderAccessDataService.OrderingOptions, FhirProviderAccess>,
    Counter<FhirProviderAccessDataService.FieldOptions, FhirProviderAccessDataService.OrderingOptions, FhirProviderAccess>,
    Updater<FhirProviderAccess>,
    Getter<FhirProviderAccess>
{

    override val namespace: String get() = "fhir"
    override val serviceName: String get() = "provider_access"

    class ProviderField: Field.TextField(FhirProviderAccess::provider) {
        fun eq(value: String) = Predicate.eq(this, value)
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class ClientIdField: Field.UUIDField(FhirProviderAccess::clientId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ActiveField: Field.BooleanField(FhirProviderAccess::active)

    class FieldOptions {
        val provider = ProviderField()
        val clientId = ClientIdField()
        val active = ActiveField()
    }

    class OrderingOptions {
        val provider = ProviderField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions(),
    )

    override suspend fun add(model: FhirProviderAccess): Result<FhirProviderAccess, Throwable>
    override suspend fun get(id: UUID): Result<FhirProviderAccess, Throwable>
    override suspend fun update(model: FhirProviderAccess): Result<FhirProviderAccess, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<FhirProviderAccess>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
