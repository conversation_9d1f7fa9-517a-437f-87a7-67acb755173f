package br.com.alice.data.layer.models

data class FhirObservation(
    override val id: String? = null,
    override val meta: FhirMeta? = null,
    override val implicitRules: String? = null,
    override val language: String? = null,
    override val resourceType: String,
    override val valueQuantity: FhirQuantity? = null,
    override val valueCodeableConcept: FhirCodeableConcept? = null,
    override val valueString: String? = null,
    override val valueBoolean: Boolean? = null,
    override val valueInteger: Int? = null,
    override val valueTime: String? = null,
    override val valueDateTime: String? = null,
    override val valuePeriod: String? = null,
    val extension: List<FhirExtension>? = emptyList(),
    val identifier: List<FhirIdentifier>? = emptyList(),
    val status: String? = null,
    val category: List<FhirCodeableConcept>? = emptyList(),
    val performer: List<FhirReference>? = emptyList(),
    val code: FhirCodeableConcept?,
    val effectiveDateTime: String?,
    val dataAbsentReason: FhirCodeableConcept? = null,
    val interpretation: List<FhirCodeableConcept>? = emptyList(),
    val referenceRange: List<FhirReferenceRange>? = emptyList(),
    val component: List<FhirComponent>? = emptyList(),
    val basedOn: List<FhirReference>? = emptyList()
) : FhirResource, FhirBasedValues {

    fun isImagingCode(): Boolean {
        val imagingCode = category?.find { it.coding?.first()?.code?.lowercase() == "imaging" }
        return imagingCode !== null
    }

    fun isPathologicFindings(): Boolean {
        val pathologicFindingsCode =
            category?.find { it.coding?.first()?.display?.lowercase() == "pathologic findings" }
        return pathologicFindingsCode !== null
    }

    fun getExamName(): String? = try {
        code?.coding?.firstOrNull()?.display ?: code?.text
    } catch (ex: Exception) {
        null
    }
}
