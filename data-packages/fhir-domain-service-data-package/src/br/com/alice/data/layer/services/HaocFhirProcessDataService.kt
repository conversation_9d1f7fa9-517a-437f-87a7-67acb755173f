package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HaocFhirProcess
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HaocFhirProcessDataService :
    Service,
    Finder<HaocFhirProcessDataService.FieldOptions, HaocFhirProcessDataService.OrderingOptions, HaocFhirProcess>,
    Getter<HaocFhirProcess>,
    <PERSON>der<HaocFhirProcess>,
    Deleter<HaocFhirProcess>,
    Updater<HaocFhirProcess> {

    override val namespace: String get() = "fhir"
    override val serviceName: String get() = "haoc_process"

    class PersonIdField : Field.TableIdField(HaocFhirProcess::personId)

    class ProcessedAt : Field.DateTimeField(HaocFhirProcess::processedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
    }

    class OrderingOptions {
        val processedAt = ProcessedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: HaocFhirProcess): Result<HaocFhirProcess, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HaocFhirProcess>, Throwable>
    override suspend fun get(id: UUID): Result<HaocFhirProcess, Throwable>
    override suspend fun update(model: HaocFhirProcess): Result<HaocFhirProcess, Throwable>
    override suspend fun delete(model: HaocFhirProcess): Result<Boolean, Throwable>
}
