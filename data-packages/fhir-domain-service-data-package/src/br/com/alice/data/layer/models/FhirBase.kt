package br.com.alice.data.layer.models

import br.com.alice.common.serialization.JsonSerializable

data class FhirMeta(
    val lastUpdate: String?,
    val profile: List<String>? = emptyList(),
    val source: String?,
) : JsonSerializable

interface FhirResource : JsonSerializable {
    val id: String?
    val meta: FhirMeta?
    val implicitRules: String?
    val language: String?
    val resourceType: String
}

data class FhirResourceObject(
    override val id: String?,
    override val meta: FhirMeta?,
    override val implicitRules: String?,
    override val language: String?,
    override val resourceType: String,
) : FhirResource

interface FhirBasedValues : JsonSerializable {
    val valueQuantity: FhirQuantity?
    val valueCodeableConcept: FhirCodeableConcept?
    val valueString: String?
    val valueBoolean: Boolean?
    val valueInteger: Int?
    val valueTime: String?
    val valueDateTime: String?
    val valuePeriod: String?
}

data class FhirCoding(
    val system: String? = null,
    val version: String? = null,
    val code: String? = null,
    val display: String? = null,
    val userSelected: Boolean? = null,
) : JsonSerializable

data class FhirCodeableConcept(
    val coding: List<FhirCoding>? = emptyList(),
    val text: String? = null,
) : JsonSerializable {
    fun getExamName() =
        try {
            coding?.firstOrNull()?.display ?: text
        } catch (ex: Exception) {
            null
        }
}

data class FhirReference(
    val type: String? = null,
    val identifier: FhirIdentifier? = null,
    val reference: String? = null,
    val display: String? = null,
) : JsonSerializable

data class FhirAttachment(
    val contentType: String?,
    val language: String?,
    val data: String?,
    val url: String?,
    val title: String?,
) : JsonSerializable

data class FhirPeriod(
    val start: String?,
    val end: String?,
) : JsonSerializable

data class FhirIdentifier(
    val use: String? = null,
    val type: FhirCodeableConcept? = null,
    val system: String? = null,
    val value: String? = null,
) : JsonSerializable

data class FhirQuantity(
    val value: Number?,
    val comparator: String?,
    val unit: String?,
    val system: String?,
    val code: String?,
) : JsonSerializable

data class FhirReferenceRange(
    val text: String?,
) : JsonSerializable

data class FhirComponent(
    val dataAbsentReason: FhirCodeableConcept? = null,
    val interpretation: FhirCodeableConcept? = null,
    val referenceRange: List<FhirReferenceRange>? = emptyList(),
    val code: FhirCodeableConcept,
    override val valueQuantity: FhirQuantity? = null,
    override val valueCodeableConcept: FhirCodeableConcept? = null,
    override val valueString: String? = null,
    override val valueBoolean: Boolean? = null,
    override val valueInteger: Int? = null,
    override val valueTime: String? = null,
    override val valueDateTime: String? = null,
    override val valuePeriod: String? = null,
) : JsonSerializable, FhirBasedValues {
    fun getExamName(): String? = try {
        code.coding?.firstOrNull()?.display ?: code.text
    } catch (ex: Exception) {
        null
    }
}

data class FhirHumanName(
    val use: String?,
    val text: String?,
    val family: String?,
    val given: List<String>?,
    val period: FhirPeriod?,
) : JsonSerializable

// This code aren't in line with FHIR V4 pattern. Needs to fix the Dasa Data tho change.
data class FhirClass(
    val type: FhirCodeableConcept?,
    val value: String?,
    val name: String?,
) : JsonSerializable

data class FhirStatusHistory(
    val status: String,
    val period: FhirPeriod
) : JsonSerializable

data class FhirClassHistory(
    val `class`: FhirCoding,
    val period: FhirPeriod
) : JsonSerializable

data class FhirParticipant(
    val type: List<FhirCodeableConcept>? = emptyList(),
    val period: FhirPeriod?,
    val individual: FhirReference?
) : JsonSerializable

data class FhirAnnotation(
    val text: String
) : JsonSerializable

data class FhirLocation(
    val location: FhirReference?
) : JsonSerializable

data class FhirExtension(
    val url: String? = null,
    val valueCode: Int? = null,
    override val valueQuantity: FhirQuantity? = null,
    override val valueCodeableConcept: FhirCodeableConcept? = null,
    override val valueString: String? = null,
    override val valueBoolean: Boolean? = null,
    override val valueInteger: Int? = null,
    override val valueTime: String? = null,
    override val valueDateTime: String? = null,
    override val valuePeriod: String? = null,
) : JsonSerializable, FhirBasedValues
