package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentCoordination
import br.com.alice.data.layer.models.AppointmentCoordination.Type
import br.com.alice.data.layer.services.AppointmentCoordinationDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentCoordinationDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentCoordinationDataService : Service,
    Finder<FieldOptions, OrderingOptions, AppointmentCoordination>,
    Adder<AppointmentCoordination>,
    Getter<AppointmentCoordination>,
    Updater<AppointmentCoordination> {

    override val namespace: String
        get() = "wanda"
    override val serviceName: String
        get() = "appointment_coordination"

    class AppointmentIdField : Field.UUIDField(AppointmentCoordination::appointmentId){
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class AppointmentTypeField : Field.TextField(AppointmentCoordination::appointmentType) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<Type>) = Predicate.inList(this, value)
    }

    class HealthPlanTaskIdTypeField : Field.UUIDField(AppointmentCoordination::healthPlanTaskId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonIdField : Field.TableIdField(AppointmentCoordination::personId)

    class CreatedAtField : Field.DateTimeField(AppointmentCoordination::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class FieldOptions {
        val appointmentId = AppointmentIdField()
        val appointmentType = AppointmentTypeField()
        val personId = PersonIdField()
        val healthPlanTaskId = HealthPlanTaskIdTypeField()
        val createdAt = CreatedAtField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder (
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AppointmentCoordination): Result<AppointmentCoordination, Throwable>
    override suspend fun get(id: UUID): Result<AppointmentCoordination, Throwable>
    override suspend fun update(model: AppointmentCoordination): Result<AppointmentCoordination, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentCoordination>, Throwable>
}
