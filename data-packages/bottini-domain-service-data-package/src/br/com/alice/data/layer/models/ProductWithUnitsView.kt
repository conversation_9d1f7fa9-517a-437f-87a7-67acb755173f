package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class ProductWithUnitsView(
    val productId: UUID,
    val title: String,
    val displayName: String? = null,
    val type: ProductType,
    val priceListing: PriceListing? = null,
    val anchor: ProductAnchor? = null,
    val accommodation: AccommodationType? = null,
    val refund: RefundType,
    val coPayment: CoPaymentType,
    val active: Boolean,
    val isVisibleForSale: Boolean,
    val tier: TierType? = null,
    val healthcareModelType: HealthcareModelType,
    val primaryAttention: PrimaryAttentionType,
    val characteristics: List<ProductCharacteristic>,

    val bundleId: UUID,
    val bundleType: ProductBundleType,

    val providerId: UUID,
    val providerFlagship: Boolean,
    val providerName: String,
    val providerLogo: String? = null,
    val providerThumbnail: String? = null,
    val providerAbout: String? = null,
    val providerType: ProviderType,
    val providerIcon: String? = null,
    val providerImageUrl: String? = null,

    val unitId: UUID,
    val unitName: String,
    val unitType: ProviderUnit.Type,
    val providerUnitGroupId: UUID? = null,
    val localityId: UUID? = null,
    val localityLat: Float? = null,
    val localityLon: Float? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model

data class FormattedProductWithUnitsView(
    val id: UUID,
    val title: String,
    val displayName: String? = null,
    val type: ProductType,
    val priceListing: PriceListing? = null,
    val anchor: ProductAnchor? = null,
    val accommodation: AccommodationType? = null,
    val refund: RefundType,
    val coPayment: CoPaymentType,
    val active: Boolean,
    val isVisibleForSale: Boolean,
    val bundles: List<ProductBundleView>
) {

    fun getPrice(age: Int) = priceListing?.items?.find { it.minAge <= age && age <= it.maxAge }?.amount

    val specialtyTiers get() = bundles.filter { it.type === ProductBundleType.SPECIALITY_TIERS }
    val laboratories get() = bundles.filter { it.type === ProductBundleType.LABORATORY }
    val hospitals get() = bundles.filter { it.type === ProductBundleType.HOSPITAL }
    val maternities get() = bundles.filter { it.type === ProductBundleType.MATERNITY }
}

data class ProductBundleView(
    val id: UUID,
    val type: ProductBundleType,
    val providers: List<ProviderView>
) : JsonSerializable

data class ProviderView(
    val id: UUID,
    val flagship: Boolean,
    val name: String,
    val logo: String? = null,
    val thumbnail: String? = null,
    val about: String? = null,
    val type: ProviderType,
    val icon: String? = null,
    val imageUrl: String? = null,
    val units: List<ProviderUnitView>
) : JsonSerializable

data class ProviderUnitView(
    val id: UUID,
    val name: String,
    val type: ProviderUnit.Type,
    val groupId: UUID? = null,
    val localityId: UUID? = null,
    val localityLat: Float? = null,
    val localityLon: Float? = null,
) : JsonSerializable
