plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = true
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.data.packs.feature-config-domain-service"
version = aliceDataLayerVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

publishing {
    publications {
        create<MavenPublication>("nexus") {
            from(components["java"])
        }
    }

    repositories {
        maven {
            name = "nexus"
            url = uri("https://nexus.devtools.alice.tools/repository/releases/")
            credentials {
                username = System.getenv()["NEXUS_USER"] ?: ""
                password = System.getenv()["NEXUS_PASSWORD"] ?: ""
            }
        }
    }
}

dependencies {
    implementation(project(":common"))
    kapt(project(":common"))
    testImplementation(project(":common-tests"))
}
