package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.util.UUID

open class CalculatorConf(
    @Transient
    open val strategy: Strategy,
    @Transient
    open val parameters: Map<String, Any>? = emptyMap(),
    @Transient
    open val outcomeConfId: UUID,
    @Transient
    open val version: Int,
    @Transient
    override val id: UUID = RangeUUID.generate()
): Model {
    enum class Strategy {
        LINEAR_EQUATION,
        CONSTANT,
        THRESHOLD,
        SUM,
        AVERAGE,
        PRODUCT,
        MULTI_ANSWER_KEYS_AGGREGATOR,
        MATRIX
    }
}
