package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthLogicTracking.HealthLogicTrackingModel
import br.com.alice.data.layer.models.ProtocolTracking
import br.com.alice.data.layer.services.ProtocolTrackingDataService.FieldOptions
import br.com.alice.data.layer.services.ProtocolTrackingDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProtocolTrackingDataService : Service,
    Finder<FieldOptions, OrderingOptions, ProtocolTracking>,
    Adder<ProtocolTracking>
{

    override val namespace: String
        get() = "health-logics"
    override val serviceName: String
        get() = "protocol-tracking"


    class TypeField: Field.TextField(ProtocolTracking::type) {
        fun eq(type: HealthLogicTrackingModel) = Predicate.eq(this, type)
    }

    class ExternalIdField: Field.UUIDField(ProtocolTracking::externalId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class ServiceScriptActionIdField: Field.UUIDField(ProtocolTracking::serviceScriptActionId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class ServiceScriptNavigationIdField: Field.UUIDField(ProtocolTracking::serviceScriptNavigationId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class FieldOptions {
        val type = TypeField()
        val externalId = ExternalIdField()
        val serviceScriptActionId = ServiceScriptActionIdField()
        val serviceScriptNavigationId = ServiceScriptNavigationIdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ProtocolTracking): Result<ProtocolTracking, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ProtocolTracking>, Throwable>
}
