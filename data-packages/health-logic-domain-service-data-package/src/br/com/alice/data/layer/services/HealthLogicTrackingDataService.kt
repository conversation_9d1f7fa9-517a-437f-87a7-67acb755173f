package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthLogicTracking
import br.com.alice.data.layer.models.HealthLogicTracking.HealthLogicTrackingModel
import br.com.alice.data.layer.services.HealthLogicTrackingDataService.FieldOptions
import br.com.alice.data.layer.services.HealthLogicTrackingDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthLogicTrackingDataService : Service,
    Finder<FieldOptions, OrderingOptions, HealthLogicTracking>,
    Adder<HealthLogicTracking>
{

    override val namespace: String
        get() = "health-logics"
    override val serviceName: String
        get() = "health-logic-tracking"


    class TypeField: Field.TextField(HealthLogicTracking::type) {
        fun eq(type: HealthLogicTrackingModel) = Predicate.eq(this, type)
    }

    class ExternalIdField: Field.UUIDField(HealthLogicTracking::externalId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class ServiceScriptActionIdField: Field.UUIDField(HealthLogicTracking::serviceScriptActionId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class HealthLogicRecordIdField: Field.UUIDField(HealthLogicTracking::healthLogicRecordId) {
        fun eq(id: UUID) = Predicate.eq(this, id)
    }

    class FieldOptions {
        val type = TypeField()
        val externalId = ExternalIdField()
        val serviceScriptActionId = ServiceScriptActionIdField()
        val healthLogicRecordId = HealthLogicRecordIdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: HealthLogicTracking): Result<HealthLogicTracking, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthLogicTracking>, Throwable>
}
