package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ServiceScriptExecution
import br.com.alice.data.layer.services.ServiceScriptExecutionDataService.FieldOptions
import br.com.alice.data.layer.services.ServiceScriptExecutionDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ServiceScriptExecutionDataService : Service,
    Finder<FieldOptions, OrderingOptions, ServiceScriptExecution>,
    Counter<FieldOptions, OrderingOptions, ServiceScriptExecution>,
    Updater<ServiceScriptExecution>,
    Getter<ServiceScriptExecution>,
    Adder<ServiceScriptExecution> {

    override val namespace: String
        get() = "digital_care"
    override val serviceName: String
        get() = "service_script_execution"

    class TriggerId : Field.TextField(ServiceScriptExecution::triggerId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val triggerId = TriggerId()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ServiceScriptExecution, Throwable>
    override suspend fun add(model: ServiceScriptExecution): Result<ServiceScriptExecution, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ServiceScriptExecution>, Throwable>
    override suspend fun update(model: ServiceScriptExecution): Result<ServiceScriptExecution, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
