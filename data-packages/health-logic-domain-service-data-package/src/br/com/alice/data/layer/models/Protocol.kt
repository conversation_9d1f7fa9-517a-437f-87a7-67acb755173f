package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Role
import br.com.alice.common.core.Role.Companion.healthProfessionalStaffs
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class Protocol (
    val healthConditionIds: List<UUID>,
    val rootNodeId: UUID,
    val title: String,
    val metadata: ProtocolMetadata = ProtocolMetadata(),
    val attributes: ProtocolAttributes = ProtocolAttributes(),
    val searchTokens: String? = null,
    val status: ProtocolStatus,
    val relatedTerms: List<String> = emptyList(),
    val typeOfService: List<ProtocolService> = emptyList(),
    val category: ProtocolCategory,
    val attentionLevels: List<ProtocolAttentionLevel> = emptyList(),
    val targets: List<ProtocolTargets> = emptyList(),
    val typeOfMaterial: ProtocolTypeOfMaterial? = null,
    val staffRoles: List<Role> = emptyList(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
): Model, JsonSerializable {

    fun isActive() = status == ProtocolStatus.ACTIVE

    data class ProtocolMetadata(
        val healthConditionCodes: List<String> = emptyList(),
        val healthConditionNames: List<String> = emptyList()
    ) : JsonSerializable

    data class ProtocolAttributes(
        val healthConditions: List<HealthConditionLight> = emptyList()
    ) : JsonSerializable

    data class HealthConditionLight(
        val code: String,
        val name: String,
        val codeType: HealthConditionCodeType
    ) : JsonSerializable

    enum class ProtocolStatus() {
        ACTIVE,
        INACTIVE;
    }

    enum class ProtocolService {
        ACUTE,
        LONGITUDINAL
    }

    enum class ProtocolCategory {
        ADMINISTRATIVE,
        ASSISTANCE,
        SCREENING
    }

    enum class ProtocolAttentionLevel {
        PRIMARY,
        SECONDARY,
        TERTIARY
    }

    enum class ProtocolTypeOfMaterial {
        CARE_LINE,
        PROTOCOL,
        SOP
    }

    enum class ProtocolTargets(val description: String) {
        INFANCY("Infancy - 0 a 1 ano"),
        EARLY_CHILDHOOD("Early childhood - 1 a 4 anos"),
        MIDDLE_CHILDHOOD("Middle childhood - 4 a 10 anos"),
        ADOLESCENCE("Adolescence - 10 a 14 anos"),
        ADULT("Adulto - 15 a 60 anos"),
        ELDERLY("Idoso - >60 anos"),
        FEMALE_BIOLOGICAL_SEX("Sexo biológico feminino"),
        MALE_BIOLOGICAL_SEX("Sexo biológico masculino"),
        PREGNANT("Gestantes"),
        ALL("Todos")
    }

    companion object {
        fun getRoleByAttentionLevel(attentionLevel: ProtocolAttentionLevel): List<Role> =
            when (attentionLevel) {
                ProtocolAttentionLevel.PRIMARY -> healthProfessionalStaffs()
                ProtocolAttentionLevel.SECONDARY -> listOf(Role.HEALTH_COMMUNITY)
                ProtocolAttentionLevel.TERTIARY -> listOf(Role.HEALTH_ADMINISTRATIVE)
            }

        fun getAllRoles(attentionLevels: List<ProtocolAttentionLevel>): List<Role> =
            attentionLevels.map { level ->
                getRoleByAttentionLevel(level)
            }.flatten()
    }
}
