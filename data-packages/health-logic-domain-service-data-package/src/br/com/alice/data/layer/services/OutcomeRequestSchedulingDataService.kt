package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.OutcomeRequestScheduling
import br.com.alice.data.layer.services.OutcomeRequestSchedulingDataService.FieldOptions
import br.com.alice.data.layer.services.OutcomeRequestSchedulingDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface OutcomeRequestSchedulingDataService : Service,
    Finder<FieldOptions, OrderingOptions, OutcomeRequestScheduling>,
    Adder<OutcomeRequestScheduling>,
    Getter<OutcomeRequestScheduling>,
    Updater<OutcomeRequestScheduling> {

    override val namespace: String
        get() = "health_logics"
    override val serviceName: String
        get() = "outcome_request_scheduling"

    class IdField : Field.UUIDField(OutcomeRequestScheduling::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonIdField : Field.TableIdField(OutcomeRequestScheduling::personId)

    class StatusField: Field.TextField(OutcomeRequestScheduling::status) {
        fun eq(value: OutcomeRequestScheduling.SchedulingStatus) = Predicate.eq(this, value)
        fun inList(value: List<OutcomeRequestScheduling.SchedulingStatus>) = Predicate.inList(this, value)
    }

    class ScheduledForField: Field.DateTimeField(OutcomeRequestScheduling::scheduledFor) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class HealthDemandMonitoringId: Field.UUIDField(OutcomeRequestScheduling::healthDemandMonitoringId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val status = StatusField()
        val scheduledFor = ScheduledForField()
        val healthDemandMonitoringId = HealthDemandMonitoringId()
        val personId = PersonIdField()
    }

    class OrderingOptions {
        val scheduledFor = ScheduledForField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: OutcomeRequestScheduling): Result<OutcomeRequestScheduling, Throwable>
    override suspend fun get(id: UUID): Result<OutcomeRequestScheduling, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<OutcomeRequestScheduling>, Throwable>
    override suspend fun update(model: OutcomeRequestScheduling): Result<OutcomeRequestScheduling, Throwable>
}
