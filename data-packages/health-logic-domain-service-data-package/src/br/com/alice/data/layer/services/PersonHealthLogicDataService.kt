package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.PersonHealthLogic
import br.com.alice.data.layer.services.PersonHealthLogicDataService.FieldOptions
import br.com.alice.data.layer.services.PersonHealthLogicDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PersonHealthLogicDataService: Service,
    Finder<FieldOptions, OrderingOptions, PersonHealthLogic>,
    Adder<PersonHealthLogic>,
    Getter<PersonHealthLogic>,
    Updater<PersonHealthLogic> {

    override val namespace: String
        get() = "health_logic"
    override val serviceName: String
        get() = "person_health_logic"

    class PersonIdField : Field.TableIdField(PersonHealthLogic::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class HealthLogicNodeIdField : Field.UUIDField(PersonHealthLogic::healthLogicNodeId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CurrentNodeIdField : Field.UUIDField(PersonHealthLogic::currentNodeId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class AddedAtField : Field.DateTimeField(PersonHealthLogic::addedAt)


    class FieldOptions {
        val personId = PersonIdField()
        val healthLogicNodeId = HealthLogicNodeIdField()
        val currentNodeId = CurrentNodeIdField()
    }

    class OrderingOptions {
        val addedAt = AddedAtField()
    }


    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<PersonHealthLogic>, Throwable>
    override suspend fun add(model: PersonHealthLogic): Result<PersonHealthLogic, Throwable>
    override suspend fun get(id: UUID): Result<PersonHealthLogic, Throwable>
    override suspend fun update(model: PersonHealthLogic): Result<PersonHealthLogic, Throwable>
}
