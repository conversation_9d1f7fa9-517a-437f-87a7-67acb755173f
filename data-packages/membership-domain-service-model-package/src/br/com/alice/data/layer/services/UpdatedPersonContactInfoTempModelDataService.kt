package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.UpdatedPersonContactInfoTempModel
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface UpdatedPersonContactInfoTempModelDataService: Service,
    Finder<UpdatedPersonContactInfoTempModelDataService.FieldOptions, UpdatedPersonContactInfoTempModelDataService.OrderingOptions, UpdatedPersonContactInfoTempModel>,
    Getter<UpdatedPersonContactInfoTempModel>,
    Adder<UpdatedPersonContactInfoTempModel>,
    Updater<UpdatedPersonContactInfoTempModel> {

    override val namespace: String
        get() = "onboarding"
    override val serviceName: String
        get() = "updated_person_contact_info_temp"

    class IdField : Field.TableIdField(UpdatedPersonContactInfoTempModel::id)

    class PersonIdField : Field.TableIdField(UpdatedPersonContactInfoTempModel::personId)

    class UsedField : Field.BooleanField(UpdatedPersonContactInfoTempModel::used)
    class ValidUntil : Field.DateTimeField(UpdatedPersonContactInfoTempModel::validUntil) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class Token: Field.TextField(UpdatedPersonContactInfoTempModel::token) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val used = UsedField()
        val validUntil = ValidUntil()
        val token = Token()
    }

    class OrderingOptions {
        val id = IdField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<UpdatedPersonContactInfoTempModel, Throwable>
    suspend fun findByPersonId(id: PersonId) = findOne {
        where {
            this.personId.eq(id).and(this.used.eq(false))
        }
    }

    suspend fun findPending(id: PersonId) = find {
        where {
            this.personId.eq(id) and
                    this.used.eq(false) and
                    this.validUntil.greater(LocalDateTime.now())
        }
    }
    override suspend fun add(model: UpdatedPersonContactInfoTempModel): Result<UpdatedPersonContactInfoTempModel, Throwable>
    override suspend fun update(model: UpdatedPersonContactInfoTempModel): Result<UpdatedPersonContactInfoTempModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<UpdatedPersonContactInfoTempModel>, Throwable>

    }
