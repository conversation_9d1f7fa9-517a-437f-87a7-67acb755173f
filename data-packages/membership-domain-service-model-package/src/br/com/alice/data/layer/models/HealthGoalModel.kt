package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID


data class HealthGoalModel(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val imageUrl: String,
    val active: Boolean,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
): Model
