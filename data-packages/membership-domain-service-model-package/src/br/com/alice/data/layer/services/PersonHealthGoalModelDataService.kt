package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.PersonHealthGoalModel
import br.com.alice.data.layer.services.PersonHealthGoalModelDataService.FieldOptions
import br.com.alice.data.layer.services.PersonHealthGoalModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PersonHealthGoalModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, PersonHealthGoalModel>,
    Updater<PersonHealthGoalModel>,
    Getter<PersonHealthGoalModel>,
    Counter<FieldOptions, OrderingOptions, PersonHealthGoalModel>,
    Adder<PersonHealthGoalModel> {

    override val namespace: String
        get() = "membership"
    override val serviceName: String
        get() = "person_health_goal"

    class PersonIdField : Field.TableIdField(PersonHealthGoalModel::personId)

    class CreatedAt: Field.DateTimeField(PersonHealthGoalModel::createdAt)

    class FieldOptions {
        val personId = PersonIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun findByPersonId(personId: PersonId) = find { where { this.personId.eq(personId) } }

    override suspend fun get(id: UUID): Result<PersonHealthGoalModel, Throwable>
    override suspend fun add(model: PersonHealthGoalModel): Result<PersonHealthGoalModel, Throwable>
    override suspend fun update(model: PersonHealthGoalModel): Result<PersonHealthGoalModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<PersonHealthGoalModel>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
}
