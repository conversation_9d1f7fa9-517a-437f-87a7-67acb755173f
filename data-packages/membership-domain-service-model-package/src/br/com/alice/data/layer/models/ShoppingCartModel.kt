package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class ShoppingCartModel(
    val leadId: UUID,
    val promoCodeId: UUID? = null,
    val healthcareTeam: String? = null,
    val providerIds: List<String>,
    val productId: UUID,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, JsonSerializable
