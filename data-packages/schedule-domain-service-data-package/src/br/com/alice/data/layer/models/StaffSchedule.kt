package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.fromUTCToSaoPauloTimeZone
import br.com.alice.common.core.extensions.nextDay
import br.com.alice.common.core.extensions.previousDay
import io.ktor.util.date.WeekDay
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class StaffSchedule(
    val staffId: UUID,
    val healthProfessionalId: UUID? = null,
    val startHour: LocalTime,
    val untilHour: LocalTime,
    var weekDay: WeekDay,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val status: StaffScheduleStatus = StaffScheduleStatus.ACTIVE,
    val type: StaffScheduleType = StaffScheduleType.HAD,
    val providerUnitId: UUID? = null,
    val alsoDigital: <PERSON>ole<PERSON>,
    val exceptionEventTypes: List<UUID> = emptyList(),
    val lastUpdatedBy: UUID? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0
) {

    fun withUTCWeekDay(): StaffSchedule {
        val startTimeInBRTTimezoneIsAfterUTC = this.startHour.fromUTCToSaoPauloTimeZone().isAfter(this.startHour)
        return if (startTimeInBRTTimezoneIsAfterUTC) this.copy(weekDay = this.weekDay.nextDay()) else this
    }

    fun withBRTWeekDay(): StaffSchedule {
        val startTimeInUTCTimezoneIsBeforeBRT = this.startHour.isBefore(this.startHour.fromUTCToSaoPauloTimeZone())
        return if (startTimeInUTCTimezoneIsBeforeBRT) this.copy(weekDay = this.weekDay.previousDay()) else this
    }

    fun isDedicatedToAppointments() = type == StaffScheduleType.HAD

    fun isActive() = this.status == StaffScheduleStatus.ACTIVE

}

enum class StaffScheduleStatus {
    ACTIVE,
    INACTIVE,
}

enum class StaffScheduleType {
    HAD, HAI, ADM
}

