package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class PersonClinicalAccount(
    override val personId: PersonId,
    val healthcareTeamId: UUID,
    val channelId: String? = null,
    val multiStaffIds: List<UUID> = emptyList(),
    val referenceNursesGroupId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference
