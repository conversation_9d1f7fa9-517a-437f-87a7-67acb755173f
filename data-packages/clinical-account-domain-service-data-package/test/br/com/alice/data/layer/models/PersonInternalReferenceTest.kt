package br.com.alice.data.layer.models

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PersonInternalReferenceTest {

    @Test
    fun `#generateInternalCode starts with NC1 and has more 4 alphanumeric chars`() {
        val internalCode = PersonInternalReference.generateInternalCode()
        assertThat(internalCode.length).isEqualTo(7)
        assertThat(internalCode).startsWith("NC1")
        val suffix = internalCode.takeLast(4)
        val alphanumericChars: List<Char> = ('A'..'Z') + ('0'..'9')
        suffix.forEach { char ->
            assertThat(alphanumericChars).contains(char)
        }
    }

}
