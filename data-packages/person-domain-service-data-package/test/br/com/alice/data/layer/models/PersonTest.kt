package br.com.alice.data.layer.models

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.PersonPII
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class PersonTest {

    private val person = TestModelFactory.buildPerson(
        firstName = "José",
        lastName = "da <PERSON>",
        socialName = "Josefa",
        socialFirstName = "Josefa",
        socialLastName = "Silva",
    )

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#obfuscatedEmail should show first char and obfuscate other chars with asterisk before @`() {
        assertThat(person.obfuscatedEmail).isEqualTo("j***@gmail.com")
    }

    @Test
    fun `#sanitize should use lower case and trim on email`() {
        val email = " <EMAIL> "
        val updatedPerson = person.copy(email = email).sanitize()

        assertThat(updatedPerson.email).isEqualTo("<EMAIL>")
    }

    @Test
    fun `#sanitize should remove any char on national id`() {
        val nationalId = " 672.953.553-91 "
        val updatedPerson = person.copy(nationalId = nationalId).sanitize()

        assertThat(updatedPerson.nationalId).isEqualTo("67295355391")
    }

    @Test
    fun `#sanitize should remove all chars on phone number`() {
        val phoneNumber = " (11) 98733-4433 "
        val updatedPerson = person.copy(phoneNumber = phoneNumber).sanitize()

        assertThat(updatedPerson.phoneNumber).isEqualTo("11987334433")
    }

    @Test
    fun `#sanitize should trim first name, last name and nick name`() {
        val updatedPerson = person.copy(firstName = " José ", lastName = " da Silva ", nickName = " Zé ").sanitize()

        assertThat(updatedPerson.firstName).isEqualTo("José")
        assertThat(updatedPerson.lastName).isEqualTo("da Silva")
        assertThat(updatedPerson.nickName).isEqualTo("Zé")
    }

    @Test
    fun `#sanitize should replace blank nick name with null`() {
        val updatedPerson = person.copy(firstName = " José ", lastName = " da Silva ", nickName = "  ").sanitize()

        assertThat(updatedPerson.firstName).isEqualTo("José")
        assertThat(updatedPerson.lastName).isEqualTo("da Silva")
        assertThat(updatedPerson.nickName).isNull()
    }

    @Test
    fun `#sanitize address`() {
        val updatedPerson = person.copy(
            addresses = listOf(
                Address(
                    State.SP,
                    "\tSão Paulo ",
                    " Rua Canada\t",
                    "6047",
                    "ap 54",
                    "Jardim\tEuropa",
                    "01448-040"
                )
            )
        ).sanitize()

        assertThat(updatedPerson.addresses).isEqualTo(
            listOf(
                Address(
                    State.SP,
                    "São Paulo",
                    "Rua Canada",
                    "6047",
                    "ap 54",
                    "Jardim Europa",
                    "01448-040"
                )
            )
        )
    }

    @Test
    fun `#age should calc the diff between current date and date of birth`() {
        val dateOfBirth = LocalDateTime.of(1988, 7, 9, 0, 0, 0)

        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns LocalDateTime.of(2020, 2, 17, 17, 30, 0)

        val updatedPerson = person.copy(dateOfBirth = dateOfBirth)
        assertThat(updatedPerson.age).isEqualTo(31)
    }

    @Test
    fun `#age should return 0 when date of birth is not present`() {
        val updatedPerson = person.copy(dateOfBirth = null)
        assertThat(updatedPerson.age).isEqualTo(0)
    }

    @Test
    fun `#formattedNationalId returns formatted nationalId`() {
        val updatedPerson = person.copy(nationalId = "67295355391")
        assertThat(updatedPerson.formattedNationalId).isEqualTo("672.953.553-91")
    }

    @Test
    fun `#formattedNationalId returns itself when size is not 11`() {
        val updatedPerson = person.copy(nationalId = "672953553910")
        assertThat(updatedPerson.formattedNationalId).isEqualTo("672953553910")
    }

    @Test
    fun `#sanitize should replace whitespace to underscore in tags field`() {
        val tags = listOf("AlgUma TaG", "somethIng")
        val updatedPerson = person.copy(tags = tags).sanitize()

        val expectedTagList = listOf("alguma_tag", "something")
        assertThat(updatedPerson.tags).isEqualTo(expectedTagList)
    }

    @Test
    fun `#addTag should add a new tag to person`() {
        val personWithTag = person.addTag("internal")
        assertThat(personWithTag.containsTag("internal")).isTrue
    }

    @Test
    fun `#obfuscatedPhoneNumber should take last 4 numbers of phone number`() {
        val personWithTag = person.copy(phoneNumber = "11973500888")
        assertThat(personWithTag.obfuscatedPhoneNumber).isEqualTo("***0888")
    }

    @Test
    fun `#fullRegisterName should get firstName + lastName`() {
        assertThat(person.fullRegisterName).isEqualTo("José da Silva")
    }

    @Test
    fun `#fullRegisterName should get firstName + lastName is empty`() {
        val person = person.copy(
            firstName = "José da Silva",
            lastName = "",
            socialName = null,
            socialFirstName = null,
            socialLastName = null
        )
        assertThat(person.fullRegisterName).isEqualTo("José da Silva")
    }

    @Test
    fun `#fullSocialName should get socialFirstName + socialLastName`() {
        assertThat(person.fullSocialName).isEqualTo("Josefa Silva")
    }

    @Test
    fun `#fullSocialName should get socialName + lastName when socialFirstName is null`() {
        val personWithoutSocialFirstName = person.copy(
            socialFirstName = null,
            socialLastName = null,
        )
        assertThat(personWithoutSocialFirstName.fullSocialName).isEqualTo("Josefa da Silva")
    }

    @Test
    fun `#fullSocialName should get fullRegisterName when socialName is null`() {
        val personWithoutSocialName = person.copy(
            socialName = null,
            socialFirstName = null,
            socialLastName = null,
        )
        assertThat(personWithoutSocialName.fullSocialName).isEqualTo("José da Silva")
    }

    @Test
    fun `#fullSocialName should get socialName when socialFirstName is blank`() {
        val personWithoutSocialFirstName = person.copy(
            socialFirstName = "",
            socialLastName = "",
        )
        assertThat(personWithoutSocialFirstName.fullSocialName).isEqualTo("Josefa da Silva")
    }

    @Test
    fun `#fullSocialName should get fullRegisterName when socialName is blank`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
        )
        assertThat(personWithoutSocialName.fullSocialName).isEqualTo("José da Silva")
    }

    @Test
    fun `#getSocialOrFirstName returns socialFirstName when socialFirstName is not blank`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "Test",
            socialLastName = "",
        )
        assertThat(personWithoutSocialName.getSocialOrFirstName).isEqualTo("Test")
    }

    @Test
    fun `#getSocialOrFirstName returns socialName when socialName is not blank`() {
        val personWithoutSocialName = person.copy(
            socialName = "Test",
            socialFirstName = "",
            socialLastName = "",
        )
        assertThat(personWithoutSocialName.getSocialOrFirstName).isEqualTo("Test")
    }

    @Test
    fun `#getSocialOrFirstName returns firstName when socialFirstName and socialName is blank`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
        )
        assertThat(personWithoutSocialName.getSocialOrFirstName).isEqualTo("José")
    }

    @Test
    fun `#is3P returns true if tag 3P is inside tags`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
            tags = listOf("3P", "b2b", "Acme"),
        )
        assertThat(personWithoutSocialName.is3p()).isTrue
    }

    @Test
    fun `#is3P returns true if tag 3p is inside tags`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
            tags = listOf("3p", "b2b", "Acme"),
        )
        assertThat(personWithoutSocialName.is3p()).isTrue
    }

    @Test
    fun `#is3P returns false if tag 3p is not inside tags`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
            tags = listOf("b2b", "Acme")
        )
        assertThat(personWithoutSocialName.is3p()).isFalse
    }

    @Test
    fun `#is3P returns false if tags is null`() {
        val personWithoutSocialName = person.copy(
            socialName = "",
            socialFirstName = "",
            socialLastName = "",
            tags = null
        )
        assertThat(personWithoutSocialName.is3p()).isFalse
    }

    @Test
    fun `#toPersonPII returns PersonPII`() {
        val personPII = PersonPII(
            person.firstName,
            person.lastName,
            person.socialName,
            person.nickName,
            person.nationalId,
            person.formattedNationalId
        )

        assertThat(person.toPersonPII()).isEqualTo(personPII)
    }

    @Test
    fun `#validateAddressInfo should throw exception if mainAddress does not exist`() {
        val personWithoutAddress = person.copy(addresses = emptyList())

        ResultAssert.assertThat(personWithoutAddress.validateAddressInfo())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateAddressInfo should throw exception if neighbourhood does not exist`() {
        val personWithoutAddress = person.copy(addresses = listOf(person.addresses.first().copy(neighbourhood = null)))

        ResultAssert.assertThat(personWithoutAddress.validateAddressInfo())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateAddressInfo should throw exception if postalCode does not exist`() {
        val personWithoutAddress = person.copy(addresses = listOf(person.addresses.first().copy(postalCode = null)))

        ResultAssert.assertThat(personWithoutAddress.validateAddressInfo())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateAddressInfo should throw exception if postalCode is invalid`() {
        val personWithInvalidZipcode =
            person.copy(addresses = listOf(person.addresses.first().copy(postalCode = "0123456789")))

        ResultAssert.assertThat(personWithInvalidZipcode.validateAddressInfo())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateAddressInfo should throw exception if street is invalid`() {
        val personWithInvalidStreet = person.copy(addresses = listOf(person.addresses.first().copy(street = "")))

        ResultAssert.assertThat(personWithInvalidStreet.validateAddressInfo())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateMothersName should return true if mothersName is valid`() {
        val personWithValidMotherName = person.copy(mothersName = "Mãe da Silva")

        ResultAssert.assertThat(personWithValidMotherName.validateMothersName()).isSuccess()
    }

    @Test
    fun `#validateMothersName should throw exception if mothersName is invalid`() {
        val personWithInvalidMotherName = person.copy(mothersName = "Mãe")

        ResultAssert.assertThat(personWithInvalidMotherName.validateMothersName())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#validateMothersName should throw exception if mothersName is blank`() {
        val personWithInvalidMotherName = person.copy(mothersName = "")

        ResultAssert.assertThat(personWithInvalidMotherName.validateMothersName())
            .isFailureOfType(IllegalArgumentException::class)
    }

    @Test
    fun `#age tests person age`() {
        val adult = person.copy(dateOfBirth = LocalDateTime.now().minusYears(18))
        val child = person.copy(dateOfBirth = LocalDateTime.now().minusYears(10))
        val minor = person.copy(dateOfBirth = LocalDateTime.now().minusYears(17))

        val resultAdult = adult.isAdult
        val resultChild = child.isChild
        val resultMinor = minor.isMinor
        val resultAdultChild = adult.isChild
        val resultAdultMinor = adult.isMinor
        val resultChildAdult = child.isAdult
        val resultChildMinor = child.isMinor
        val resultMinorAdult = minor.isAdult
        val resultMinorChild = minor.isChild

        assertThat(resultAdult).isTrue
        assertThat(resultChild).isTrue
        assertThat(resultMinor).isTrue
        assertThat(resultAdultChild).isFalse
        assertThat(resultAdultMinor).isFalse
        assertThat(resultChildAdult).isFalse
        assertThat(resultChildMinor).isTrue
        assertThat(resultMinorAdult).isFalse
        assertThat(resultMinorChild).isFalse
    }

    @Test
    fun `#generatePiiInternalCode starts with PNC and has more 5 alphanumeric chars`() {
        val piiInternalCode = Person.generatePiiInternalCode()
        assertThat(piiInternalCode.length).isEqualTo(8)
        assertThat(piiInternalCode).startsWith("PNC")
        val suffix = piiInternalCode.takeLast(5)
        val alphanumericChars: List<Char> = ('A'..'Z') + ('0'..'9')
        suffix.forEach { char ->
            assertThat(alphanumericChars).contains(char)
        }
    }
}
