package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ServiceScriptNavigationGroup
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.services.ServiceScriptNavigationGroupDataService.FieldOptions
import br.com.alice.data.layer.services.ServiceScriptNavigationGroupDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ServiceScriptNavigationGroupDataService : Service,
    Finder<FieldOptions, OrderingOptions, ServiceScriptNavigationGroup>,
    Adder<ServiceScriptNavigationGroup>,
    Updater<ServiceScriptNavigationGroup>,
    Getter<ServiceScriptNavigationGroup> {

    override val namespace: String
        get() = "bud"
    override val serviceName: String
        get() = "service_script_navigation_group"

    class PersonIdField : Field.UUIDField(ServiceScriptNavigationGroup::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class ScriptNodeIdField : Field.UUIDField(ServiceScriptNavigationGroup::scriptNodeId) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        fun inList(values: List<UUID>) = Predicate.inList(this, values)
    }

    class SourceField: Field.JsonbField(ServiceScriptNavigationGroup::source) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eq(value: ServiceScriptNavigationSource?) =
            if (value == null) Predicate.isNull(this)
            else Predicate.jsonSearch(this, "{\"id\":\"${value.id}\", \"type\":\"${value.type}\"}")
    }

    class SourceIdField: Field.JsonbField(ServiceScriptNavigationGroup::source) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eq(value: String) =
            Predicate.jsonSearch(this, "{\"id\":\"${value}\"}")
    }

    class FinishedAtField: Field.DateTimeField(ServiceScriptNavigationGroup::finishedAt) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)

        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class StartedAtField: Field.DateTimeField(ServiceScriptNavigationGroup::startedAt)

    class FieldOptions {
        val personId = PersonIdField()
        val scriptNodeId = ScriptNodeIdField()
        val source = SourceField()
        val sourceId = SourceIdField()
        val finishedAt = FinishedAtField()
    }

    class OrderingOptions {
        val startedAt = StartedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ServiceScriptNavigationGroup, Throwable>
    override suspend fun add(model: ServiceScriptNavigationGroup): Result<ServiceScriptNavigationGroup, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ServiceScriptNavigationGroup>, Throwable>
    override suspend fun update(model: ServiceScriptNavigationGroup): Result<ServiceScriptNavigationGroup, Throwable>
}
