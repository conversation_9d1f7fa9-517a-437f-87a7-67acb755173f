package br.com.alice.data.layer.services.impl

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.converter.toModel
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ResourceSignTokenType
import br.com.alice.data.layer.services.ResourceSignTokenModelDataService
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.math.sign
import kotlin.test.Test

class ResourceSignTokenServiceImplTest : MockedTestHelper() {
    private val dataService: ResourceSignTokenModelDataService = mockk()
    private val service = ResourceSignTokenServiceImpl(dataService)

    @Test
    fun `#generateResourceSignToken should generate a resource sign token`() =
        mockLocalDateTime { now ->
            val uuid = RangeUUID.generate()
            val uuidV7 = RangeUUID.generateUUIDv7()
            mockkObject(RangeUUID)
            every { RangeUUID.generateUUIDv7() } returns uuidV7
            every { RangeUUID.generate() } returns uuid

            val expected = TestModelFactory.buildResourceSignToken(
                id = uuid,
                signUuid = uuidV7,
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
                createdAt = now,
                updatedAt = now
            )
            val expectedModel = expected.toModel()

            coEvery {
                dataService.add(expectedModel)
            } returns expectedModel

            val result = service.generateResourceSignToken(
                resourceId = expected.resourceId,
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT
            )

            ResultAssert.assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { dataService.add(any()) }
        }

    @Test
    fun `#isSignTokenValid should return false when sign token is expired`() =
        mockRangeUuidAndDateTime { uuid, now ->
            val uuidString = "01856aa0-c800-7933-aeb4-4bba851b1b11" // UUIDv7 for 2023-01-01:00:00:00
            val expected = TestModelFactory.buildResourceSignToken(
                id = uuid,
                signUuid = UUID.fromString(uuidString),
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
                createdAt = now,
                updatedAt = now
            )
            val expectedModel = expected.toModel()

            coEvery {
                dataService.findOne(queryEq {
                    where {
                        this.signUuid.eq(expected.signUuid)
                        this.resourceType.eq(expected.resourceType)
                        this.resourceId.eq(expected.resourceId)
                    }
                })
            } returns expectedModel

            val result = service.isSignTokenValid(
                signUuid = expected.signUuid,
                resource = expected.resourceType,
                expirationDate = Duration.of(1, ChronoUnit.DAYS),
                resourceId = expected.resourceId
            )

            ResultAssert.assertThat(result).isSuccessWithData(false)

            coVerifyOnce { dataService.findOne(any()) }
        }

    @Test
    fun `#isSignTokenValid should return true when sign token is valid and not expired`() =
        mockRangeUuidAndDateTime { uuid, now ->
            val uuidV7 = RangeUUID.generateUUIDv7()
            val expected = TestModelFactory.buildResourceSignToken(
                id = uuid,
                signUuid = uuidV7,
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
                createdAt = now,
                updatedAt = now
            )

            coEvery {
                dataService.findOne(queryEq {
                    where {
                        this.signUuid.eq(expected.signUuid)
                        this.resourceType.eq(expected.resourceType)
                        this.resourceId.eq(expected.resourceId)
                    }
                })
            } returns expected.toModel()

            val result = service.isSignTokenValid(
                signUuid = expected.signUuid,
                resource = expected.resourceType,
                expirationDate = Duration.of(1, ChronoUnit.DAYS),
                resourceId = expected.resourceId
            )

            ResultAssert.assertThat(result).isSuccessWithData(true)

            coVerifyOnce { dataService.findOne(any()) }
        }

    @Test
    fun `#isSignTokenValid should return false when sign token is not found`() =
        mockRangeUuidAndDateTime { uuid, now ->
            val uuidString = "01856aa0-c800-7933-aeb4-4bba851b1b11" // UUIDv7 for 2023-01-01:00:00:00

            val expected = TestModelFactory.buildResourceSignToken(
                id = uuid,
                signUuid = UUID.fromString(uuidString),
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT,
                createdAt = now,
                updatedAt = now
            )

            coEvery {
                dataService.findOne(queryEq {
                    where {
                        this.signUuid.eq(expected.signUuid)
                        this.resourceType.eq(expected.resourceType)
                        this.resourceId.eq(expected.resourceId)
                    }
                })
            } returns NotFoundException()

            val result = service.isSignTokenValid(
                signUuid = expected.signUuid,
                resource = expected.resourceType,
                expirationDate = Duration.of(10, ChronoUnit.MINUTES),
                resourceId = expected.resourceId
            )

            ResultAssert.assertThat(result).isSuccessWithData(false)

            coVerifyOnce { dataService.findOne(any()) }
        }

    @Test
    fun `#findSignTokenByResourceIdAndResourceType should return a resource sign token`() =
        mockLocalDateTime { now ->
            val expected = TestModelFactory.buildResourceSignToken(
                signUuid = RangeUUID.generateUUIDv7()
            )
            val expectedModel = expected.toModel()

            coEvery {
                dataService.findOne(queryEq {
                    where {
                        this.resourceId.eq(expected.resourceId) and
                                this.resourceType.eq(expected.resourceType)
                    }
                })
            } returns expectedModel

            val result = service.findSignTokenByResourceIdAndResourceType(
                resourceId = expected.resourceId,
                resourceType = ResourceSignTokenType.INVOICE_PAYMENT
            )

            ResultAssert.assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { dataService.findOne(any()) }
        }

    @Test
    fun `#softDeleteSignToken succeed`() = runBlocking {
        val uuid = RangeUUID.generate()
        val expected = TestModelFactory.buildResourceSignToken(signUuid = RangeUUID.generateUUIDv7())
        val expectedModel = expected.toModel()

        coEvery {
            dataService.get(uuid)
        } returns expectedModel

        coEvery {
            dataService.softDelete(expectedModel)
        } returns true

        val result = service.softDeleteSignToken(uuid)

        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { dataService.get(any()) }
        coVerifyOnce { dataService.softDelete(any()) }
    }
}
