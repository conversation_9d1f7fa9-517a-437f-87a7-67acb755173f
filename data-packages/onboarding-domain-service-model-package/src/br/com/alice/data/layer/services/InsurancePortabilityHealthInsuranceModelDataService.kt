package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.InsurancePortabilityHealthInsuranceModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface InsurancePortabilityHealthInsuranceModelDataService : Service,
    Finder<InsurancePortabilityHealthInsuranceModelDataService.FieldOptions, InsurancePortabilityHealthInsuranceModelDataService.OrderingOptions, InsurancePortabilityHealthInsuranceModel>,
    Getter<InsurancePortabilityHealthInsuranceModel>,
    Updater<InsurancePortabilityHealthInsuranceModel>,
    Adder<InsurancePortabilityHealthInsuranceModel>,
    Deleter<InsurancePortabilityHealthInsuranceModel>,
    Counter<InsurancePortabilityHealthInsuranceModelDataService.FieldOptions, InsurancePortabilityHealthInsuranceModelDataService.OrderingOptions, InsurancePortabilityHealthInsuranceModel> {

    override val namespace: String
        get() = "onboarding"
    override val serviceName: String
        get() = "insurance_portability_health_insurance"

    class CreatedAt: Field.DateTimeField(InsurancePortabilityHealthInsuranceModel::createdAt)

    class Id: Field.UUIDField(InsurancePortabilityHealthInsuranceModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Name: Field.TextField(InsurancePortabilityHealthInsuranceModel::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class FieldOptions {
        val id = Id()
        val name = Name()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<InsurancePortabilityHealthInsuranceModel, Throwable>
    override suspend fun add(model: InsurancePortabilityHealthInsuranceModel): Result<InsurancePortabilityHealthInsuranceModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<InsurancePortabilityHealthInsuranceModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun update(model: InsurancePortabilityHealthInsuranceModel): Result<InsurancePortabilityHealthInsuranceModel, Throwable>
    override suspend fun delete(model: InsurancePortabilityHealthInsuranceModel): Result<Boolean, Throwable>
}
