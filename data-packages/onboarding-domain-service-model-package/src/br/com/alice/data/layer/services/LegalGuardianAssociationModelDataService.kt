package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusType
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface LegalGuardianAssociationModelDataService : Service,
    Finder<LegalGuardianAssociationModelDataService.FieldOptions, LegalGuardianAssociationModelDataService.OrderingOptions, LegalGuardianAssociationModel>,
    Getter<LegalGuardianAssociationModel>,
    Updater<LegalGuardianAssociationModel>,
    Adder<LegalGuardianAssociationModel>,
    Deleter<LegalGuardianAssociationModel> {

    override val namespace: String
        get() = "onboarding"
    override val serviceName: String
        get() = "legal_guardian_association"

    class PersonIdField : Field.TableIdField(LegalGuardianAssociationModel::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class IdField : Field.TableIdField(LegalGuardianAssociationModel::id)

    class GuardianIdField : Field.TableIdField(LegalGuardianAssociationModel::guardianId)

    class StatusHistoryField : Field.JsonbField(LegalGuardianAssociationModel::statusHistory)

    class StatusField : Field.TextField(LegalGuardianAssociationModel::status) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun dif(value: String) = not(Predicate.eq(this, value))
        fun inList(value: List<LegalGuardianAssociationStatusType>) = Predicate.inList(this, value)
    }

    class CreatedAtField: Field.DateTimeField(LegalGuardianAssociationModel::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val guardianId = GuardianIdField()
        val statusHistoryField = StatusHistoryField()
        val status = StatusField()
        val createdAt = CreatedAtField()
    }

    class OrderingOptions {
        val id = IdField()
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<LegalGuardianAssociationModel, Throwable>
    suspend fun findById(id: PersonId) = findOne {
        where {
            this.personId.eq(id) and this.status.dif(LegalGuardianAssociationStatusType.ARCHIVED.name)
        }.orderBy { this.createdAt }.sortOrder { desc }
    }

    suspend fun findByIdOrNull(id: PersonId) = findOneOrNull {
        where {
            this.personId.eq(id) and this.status.dif(LegalGuardianAssociationStatusType.ARCHIVED.name)
        }.orderBy { this.createdAt }.sortOrder { desc }
    }

    override suspend fun add(model: LegalGuardianAssociationModel): Result<LegalGuardianAssociationModel, Throwable>
    override suspend fun update(model: LegalGuardianAssociationModel): Result<LegalGuardianAssociationModel, Throwable>
    override suspend fun delete(model: LegalGuardianAssociationModel): Result<Boolean, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<LegalGuardianAssociationModel>, Throwable>
}
