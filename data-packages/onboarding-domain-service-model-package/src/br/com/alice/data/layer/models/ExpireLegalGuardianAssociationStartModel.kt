package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class ExpireLegalGuardianAssociationStartModel(
    val datesToSearch: Pair<LocalDateTime, LocalDateTime>,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
): Model
