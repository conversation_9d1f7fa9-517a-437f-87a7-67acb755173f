package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class NullvsIntegrationLogModel(
    override val id: UUID = RangeUUID.generate(),
    val eventId: UUID,
    val eventName: String,
    val integrationEventName: String,
    val internalId: UUID,
    val internalModelName: InternalModelType,
    val externalModelName: ExternalModelType,
    val dependsOnId: UUID? = null,
    val dependsOnModel: InternalModelType? = null,
    val batchId: String,
    val idSoc: String,
    val batchType: BatchType,
    val payloadSequenceId: Int,
    val description: String?,
    val status: LogStatus = LogStatus.PENDING,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val hash: String? = null,
    val groupId: UUID? = null
) : Model
{
    @Transient
    val isFailed get() = status == LogStatus.FAILURE
}
