package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.JsonSerializable
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberInvoiceModel(
    override val id: UUID = RangeUUID.generate(),
    val memberId: UUID,
    override val personId: PersonId,
    val totalAmount: BigDecimal,
    val status: InvoiceStatus = InvoiceStatus.OPEN,
    val type: MemberInvoiceType? = null,
    val canceledReason: CancellationReason? = null,
    val referenceDate: LocalDate,
    val dueDate: LocalDateTime,
    val paidAt: LocalDateTime? = null,
    val batchLineId: UUID? = null,
    val memberInvoiceGroupId: UUID? = null,
    val preActivationPaymentId: UUID? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Transient
    val invoicePayments: List<InvoicePaymentModel>? = null,
    val invoiceItems: List<InvoiceItemModel>? = null,
    val invoiceBreakdown: InvoiceBreakdownModel? = null,
) : Model, PersonReference {

    fun markAsPaid(paidAt: LocalDateTime? = null) = copy(
        status = InvoiceStatus.PAID,
        paidAt = paidAt ?: LocalDateTime.now()
    )

    fun markAsPaidByLiquidation() = copy(
        status = InvoiceStatus.CANCELED_BY_LIQUIDATION,
        paidAt =  LocalDateTime.now()
    )

    fun changeReferenceDate(paidAt: LocalDateTime? = null) =
        if (type?.isFirstPayment() == true && paidAt != null) {
            val referenceDate = referenceDate.withYear(paidAt.year).withMonth(paidAt.monthValue)

            logger.info(
                "Should change the reference date",
                "current_reference_date" to this.referenceDate,
                "new_reference_date" to referenceDate,
            )
            copy(referenceDate = referenceDate)
        } else {
            logger.info("Should not change the reference date")
            this
        }

    fun cancel(cancellationReason: CancellationReason) = copy(
        status = InvoiceStatus.CANCELED,
        canceledReason = cancellationReason
    )

    @Transient
    val isOpen = status == InvoiceStatus.OPEN
    @Transient
    val wasCanceled = status == InvoiceStatus.CANCELED
    @Transient
    val alreadyPaid = paidAt != null && status == InvoiceStatus.PAID
    @Transient
    val isPaidByLiquidation = status == InvoiceStatus.CANCELED_BY_LIQUIDATION

    @Transient
    val isOverdue = status == InvoiceStatus.OPEN && LocalDateTime.now().isAfter(dueDate)
}

fun MemberInvoiceModel.withInvoicePayments(invoicePayments: List<InvoicePaymentModel>): MemberInvoiceModel {
    return this.copy(invoicePayments = invoicePayments)
}

data class InvoiceBreakdownModel(
    val totalAmount: BigDecimal,
    val productPrice: BigDecimal? = null,
    val promoCode: BigDecimal? = null,
    val proRation: BigDecimal? = null,
    val copay: BigDecimal? = null,
    val productChange: BigDecimal? = null,
    val sales: BigDecimal? = null,
    val discount: BigDecimal? = null,
    val addition: BigDecimal? = null,
    val promoCodeResult: BigDecimal? = null,
    val salesResult: BigDecimal? = null,
    val readjustment: BigDecimal? = null,
    val operationalAdjustment: BigDecimal? = null,
    val retroactiveMonthlyFeeBilling: BigDecimal? = null,
    val others: BigDecimal? = null,
    val invoiceItems: List<InvoiceItemModel>? = null,
) : JsonSerializable
