package br.com.alice.data.layer.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.InvoicePaymentStatus
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface InvoicePaymentModelDataService : Service,
    Finder<InvoicePaymentModelDataService.FieldOptions, InvoicePaymentModelDataService.OrderingOptions, InvoicePaymentModel>,
    Getter<InvoicePaymentModel>,
    Updater<InvoicePaymentModel>,
    Adder<InvoicePaymentModel>,
    UpdaterList<InvoicePaymentModel> {

    override val namespace: String
        get() = "money_in"

    override val serviceName: String
        get() = "invoice_payment"

    class Id : Field.UUIDField(InvoicePaymentModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ExternalId : Field.TextField(InvoicePaymentModel::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class MemberInvoiceIds : Field.JsonbField(InvoicePaymentModel::memberInvoiceIds) {
        @OptIn(ContainsPredicateUsage::class) // TODO: create index
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(ContainsAnyPredicateUsage::class) // TODO: create index
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)
    }

    class Status : Field.TextField(InvoicePaymentModel::status) {
        fun eq(value: InvoicePaymentStatus) = Predicate.eq(this, value)
    }

    class InvoiceGroupId : Field.UUIDField(InvoicePaymentModel::invoiceGroupId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class InvoiceLiquidationId : Field.UUIDField(InvoicePaymentModel::invoiceLiquidationId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PreActivationPaymentId : Field.UUIDField(InvoicePaymentModel::preActivationPaymentId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }


    class Method : Field.TextField(InvoicePaymentModel::method) {
        fun eq(value: PaymentMethod) = Predicate.eq(this, value)
    }

    class BillingAccountablePartyId : Field.UUIDField(InvoicePaymentModel::billingAccountablePartyId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class CreatedAt : Field.DateTimeField(InvoicePaymentModel::createdAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class CanceledReason : Field.TextField(InvoicePaymentModel::canceledReason) {
        fun eq(value: CancellationReason) = Predicate.eq(this, value)
    }

    class Source : Field.TextField(InvoicePaymentModel::source) {
        fun eq(value: InvoicePaymentSource) = Predicate.eq(this, value)
    }

    class AmountPaid : Field.BigDecimalField(InvoicePaymentModel::amountPaid) {
        fun isNull() = Predicate.isNull(this)
    }

    class FieldOptions {
        val id = Id()
        val memberInvoiceIds = MemberInvoiceIds()
        val invoiceGroupId = InvoiceGroupId()
        val preActivationPaymentId = PreActivationPaymentId()
        val status = Status()
        val method = Method()
        val billingAccountablePartyId = BillingAccountablePartyId()
        val externalId = ExternalId()
        val source = Source()
        val createdAt = CreatedAt()
        val canceledReason = CanceledReason()
        val amountPaid = AmountPaid()
        val invoiceLiquidationId = InvoiceLiquidationId()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<InvoicePaymentModel>, Throwable>

    override suspend fun add(model: InvoicePaymentModel): Result<InvoicePaymentModel, Throwable>

    override suspend fun get(id: UUID): Result<InvoicePaymentModel, Throwable>

    override suspend fun update(model: InvoicePaymentModel): Result<InvoicePaymentModel, Throwable>

    override suspend fun updateList(
        models: List<InvoicePaymentModel>,
        returnOnFailure: Boolean
    ): Result<List<InvoicePaymentModel>, Throwable>
}
