package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InvoiceLiquidationModel(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String,
    val amount: BigDecimal,
    val addition: BigDecimal = BigDecimal.ZERO,
    val discount: BigDecimal = BigDecimal.ZERO,
    val dueDate: LocalDate,
    val memberInvoiceGroupIds: List<UUID>,
    val status: InvoiceLiquidationStatus,
    val billingAccountablePartyId: UUID,
    val companyIds: List<UUID>,
    val subcontractIds: List<UUID>,
    @Deprecated("Use companyIds instead")
    val companyId: UUID?,
    val subcontractId: UUID?,
    val installment: Int,
    val totalInstallments: Int,
    override val version: Int = 0,
    val businessType: BusinessType,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference
