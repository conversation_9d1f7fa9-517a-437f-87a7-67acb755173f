package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.LegalGuardianAssociationReference
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.DependentInformation
import java.time.LocalDateTime
import java.util.UUID

data class FinancialDataModel(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val bankCode: String,
    val bankName: String? = null,
    val bankAgency: String,
    val accountNumber: String,
    val nationalId: String,
    val accountNickname: String? = null,
    val active: Boolean = false,
) : Model, PersonReference, LegalGuardianAssociationReference, DependentInformation {
    override fun getLegalGuardianPersonId() = this.personId
}
