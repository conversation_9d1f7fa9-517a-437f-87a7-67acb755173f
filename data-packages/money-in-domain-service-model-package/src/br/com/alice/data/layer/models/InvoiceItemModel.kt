package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.money
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InvoiceItemModel(
    override val id: UUID = RangeUUID.generate(),
    val referenceDate: LocalDate,
    val operation: InvoiceItemOperation,
    val type: InvoiceItemType,
    val notes: String? = null,
    val status: InvoiceItemStatus,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val percentageValue: BigDecimal? = null,
    val absoluteValue: BigDecimal? = null,
    @Transient
    var resolvedValue: BigDecimal? = null,
    val personId: PersonId? = null,
    val companyId: UUID? = null,
    val companySubcontractId: UUID? = null,
    val externalId: String? = null
): Model {

    @Transient
    private val isDiscount = operation == InvoiceItemOperation.DISCOUNT

    fun resolveValue(full: BigDecimal): BigDecimal {
        val operator = (if (isDiscount) -1 else 1).money

        val result = if (absoluteValue != null)
            operator * absoluteValue
        else
            operator * full * percentageValue!! / 100.money

        this.resolvedValue = result.money
        return this.resolvedValue!!
    }

    override fun sanitize() = copy(
        percentageValue = percentageValue?.money,
        absoluteValue = absoluteValue?.money,
    )
}
