package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import org.assertj.core.api.Assertions
import java.util.UUID
import kotlin.test.Test

class ProductBundleModelTest {

    @Test
    fun `#sanitize validates specialistIds when type is SPECIALIST`() {
        val productBundle = buildProductBundle().copy(
            type = ProductBundleType.SPECIALIST,
            specialistIds = listOf(RangeUUID.generate())
        )

        val result = productBundle.sanitize()

        Assertions.assertThat(result).isEqualTo(productBundle)
    }

    @Test
    fun `#sanitize throws exception when specialistIds is filled and type is not SPECIALIST`() {
        val productBundle = buildProductBundle().copy(
            type = ProductBundleType.HOSPITAL,
            specialistIds = listOf(RangeUUID.generate())
        )

        Assertions.assertThatThrownBy {
            productBundle.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage("Bundle do tipo ESPECIALISTA deve ter o campo specialistIds preenchido")
    }

    @Test
    fun `#sanitize validates providerIds when type is not SPECIALIST`() {
        val productBundle = buildProductBundle().copy(
            type = ProductBundleType.HOSPITAL,
            providerIds = listOf(RangeUUID.generate())
        )

        val result = productBundle.sanitize()

        Assertions.assertThat(result).isEqualTo(productBundle)
    }

    @Test
    fun `#sanitize throws exception when providerIds is filled and type is SPECIALIST`() {
        val productBundle = buildProductBundle().copy(
            type = ProductBundleType.SPECIALIST,
            providerIds = listOf(RangeUUID.generate())
        )

        Assertions.assertThatThrownBy {
            productBundle.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage(
                "Bundle do tipo HOSPITAL, MATERNIDADE OU LABORATÓRIO deve ter o campo providerIds preenchido"
            )
    }

    @Test
    fun `#sanitize throws exception when providerIds and specialistIds are empty`() {
        val productBundle = buildProductBundle()

        Assertions.assertThatThrownBy {
            productBundle.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage(
                "Não é permitido preencher ambos os campos providerIds e specialistIds. Preencha somente um deles."
            )
    }

    private fun buildProductBundle(
        name: String = "Pacote 01",
        type: ProductBundleType = ProductBundleType.HOSPITAL,
        imageUrl: String? = "image.png",
        priceScale: Int = 0,
        id: UUID = RangeUUID.generate(),
        providerIds: List<UUID> = emptyList(),
        specialistIds: List<UUID> = emptyList(),
        specialtyTiers: List<SpecialtyTiersModel> = emptyList(),
        externalSpecialists: List<UUID> = emptyList(),
    ) = ProductBundleModel(
        id = id,
        name = name,
        type = type,
        imageUrl = imageUrl,
        priceScale = priceScale,
        active = true,
        providerIds = providerIds,
        specialistIds = specialistIds,
        specialtyTiers = specialtyTiers,
        externalSpecialists = externalSpecialists,
    )
}
