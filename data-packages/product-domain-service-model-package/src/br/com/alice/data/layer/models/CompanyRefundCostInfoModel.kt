package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyRefundCostInfoModel(
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val prices: List<CompanyRefundCostInfoPricesModel>,
    val tier: TierType,
    val referenceDate: LocalDate,
    val fileVaultId: UUID,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

data class CompanyRefundCostInfoPricesModel(
    val eventType: RefundEventTypeModel,
    val event: String,
    val value: BigDecimal?
)
