package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.PriceListingModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PriceListingModelDataService: Service,
    Finder<PriceListingModelDataService.FieldOptions, PriceListingModelDataService.OrderingOptions, PriceListingModel>,
    Counter<PriceListingModelDataService.FieldOptions, PriceListingModelDataService.OrderingOptions, PriceListingModel>,
    Getter<PriceListingModel>,
    Adder<PriceListingModel>,
    Updater<PriceListingModel>,
    Deleter<PriceListingModel> {
    override val namespace: String
        get() = "member"
    override val serviceName: String
        get() = "price_listing"

    class Id: Field.UUIDField(PriceListingModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CreatedAt: Field.DateTimeField(PriceListingModel::createdAt)

    class Title: Field.TextField(PriceListingModel::title) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class FieldOptions {
        val id = Id()
        val title = Title()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<PriceListingModel, Throwable>
    override suspend fun add(model: PriceListingModel): Result<PriceListingModel, Throwable>
    override suspend fun update(model: PriceListingModel): Result<PriceListingModel, Throwable>
    override suspend fun delete(model: PriceListingModel): Result<Boolean, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<PriceListingModel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
