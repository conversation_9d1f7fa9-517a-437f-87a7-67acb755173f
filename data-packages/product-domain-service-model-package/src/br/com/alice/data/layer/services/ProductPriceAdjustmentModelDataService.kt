package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ProductPriceAdjustmentModel
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface ProductPriceAdjustmentModelDataService: Service,
    Finder<ProductPriceAdjustmentModelDataService.FieldOptions, ProductPriceAdjustmentModelDataService.OrderingOptions, ProductPriceAdjustmentModel>,
    Updater<ProductPriceAdjustmentModel>,
    Getter<ProductPriceAdjustmentModel>,
    Adder<ProductPriceAdjustmentModel>,
    Deleter<ProductPriceAdjustmentModel> {

    override val namespace: String
        get() = "member"
    override val serviceName: String
        get() = "product_price_adjustment"

    class IdField: Field.UUIDField(ProductPriceAdjustmentModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class StartDateField: Field.DateField(ProductPriceAdjustmentModel::startDate) {
        fun lessEq(value: LocalDate) = Predicate.lessEq(this, value)
    }

    class EndDateField: Field.DateField(ProductPriceAdjustmentModel::endDate) {
        fun greaterEq(value: LocalDate) = Predicate.greaterEq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val startDate = StartDateField()
        val endDate = EndDateField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ProductPriceAdjustmentModel, Throwable>

    override suspend fun add(model: ProductPriceAdjustmentModel): Result<ProductPriceAdjustmentModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ProductPriceAdjustmentModel>, Throwable>
    override suspend fun update(model: ProductPriceAdjustmentModel): Result<ProductPriceAdjustmentModel, Throwable>
    override suspend fun delete(model: ProductPriceAdjustmentModel): Result<Boolean, Throwable>
}
