package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class Product(
    val title: String,
    val externalIds: List<ExternalId>,
    @Deprecated("Please use priceListing instead")
    val prices: List<ProductPrice>,
    val ansNumber: String? = null,
    // Before using this field, check if you want to use the property salesProductName instead. Because you probably want.
    val displayName: String? = null,
    val complementName: String? = null,
    val previousDisplayName: String? = null,
    val active: Boolean = false,
    val reference: Boolean = false,
    val bundleIds: List<UUID>? = null,
    val anchor: ProductAnchor? = null,
    val priceListing: PriceListing? = null,
    val type: ProductType = ProductType.B2C,
    val subType: B2BSubTypes? = null,
    val minEmployeeNumber: Int? = null,
    val maxEmployeeNumber: Int? = null,
    val accommodation: AccommodationType? = null,
    val refund: RefundType = RefundType.NONE,
    val coPayment: CoPaymentType = CoPaymentType.FULL,
    val hasNationalCoverage: Boolean = false,
    val coverageType: ProductCoverageType? = null,
    val isVisibleForSale: Boolean = false,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val tier: TierType? = null,
    val healthcareModelType: HealthcareModelType = HealthcareModelType.V2,
    val primaryAttention: PrimaryAttentionType? = PrimaryAttentionType.ALICE,
    val characteristics: List<ProductCharacteristic>? = emptyList(),
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    var updatedBy: UpdatedBy? = null,
) : JsonSerializable {

    val mvId get() = getExternalIdString(ExternalIdKey.MV_PLAN)
    val mvPrice get() = getExternalIdString(ExternalIdKey.MV_PRICE)
    val mvGracePeriod get() = getExternalIdString(ExternalIdKey.MV_GRACE_PERIOD)

    fun getPrice(age: Int) = if (priceListing != null)
        priceListing.items.find { it.minAge <= age && age <= it.maxAge }?.amount
    else
        prices.find { it.minAge <= age && age <= it.maxAge }?.amount

    fun getExternalId(id: ExternalIdKey) = externalIds
        .find { it.key == id }?.value?.toLong()

    private fun getExternalIdString(id: ExternalIdKey) = externalIds
        .find { it.key == id }?.value

    val isB2B get() = type == ProductType.B2B

    val isB2C get() = type == ProductType.B2C

    val isADESAO get() = type == ProductType.ADESAO

    val isB2BOrADESAO get() = isB2B || isADESAO

    fun isFullCopay() = coPayment == CoPaymentType.FULL

    val isAliceBrand get() = this.brand?.let { it == Brand.ALICE } ?: true

    val salesProductName get(): String =
        if (complementName != null) {
            "$displayName $complementName"
        } else {
            displayName ?: title
        }
}

fun Product.withPriceListing(priceListing: PriceListing?): Product =
    copy(priceListing = priceListing)

fun getSubTypeRange(subType: B2BSubTypes?): SubTypeRange? =
    subType?.let {
        val subTypeRangeMap = mapOf(
            B2BSubTypes.MEI to SubTypeRange(
                minEmployeeNumber = 1,
                maxEmployeeNumber = 1
            ),
            B2BSubTypes.ME to SubTypeRange(
                minEmployeeNumber = 1,
                maxEmployeeNumber = 5
            ),
            B2BSubTypes.P to SubTypeRange(
                minEmployeeNumber = 6,
                maxEmployeeNumber = 29
            ),
            B2BSubTypes.M to SubTypeRange(
                minEmployeeNumber = 30,
                maxEmployeeNumber = 99
            ),
            B2BSubTypes.G to SubTypeRange(
                minEmployeeNumber = 100,
                maxEmployeeNumber = 999999999
            )
        )
        subTypeRangeMap[it]
    }


data class SubTypeRange(
    val minEmployeeNumber: Int,
    val maxEmployeeNumber: Int,
)

@Deprecated("Please use PriceListing")
data class ProductPrice(
    val id: String,
    val title: String,
    val minAge: Int,
    val maxAge: Int,
    val amount: BigDecimal,
    val priceAdjustment: BigDecimal? = null
) : JsonSerializable

data class ExternalId(
    val key: ExternalIdKey,
    val value: String
) : JsonSerializable

enum class ExternalIdKey {
    MV_PLAN,
    MV_PRICE,
    MV_GRACE_PERIOD;
}

data class MemberProduct(
    val id: UUID,
    @Deprecated("Please use priceListing instead")
    val prices: List<ProductPrice>,
    val type: ProductType,
    val priceListing: PriceListing? = null,
    val productPriceListingId: UUID? = null
) : JsonSerializable {

    fun getPrice(age: Int) = if (priceListing != null)
        priceListing.items.find { it.minAge <= age && age <= it.maxAge }?.amount
    else
        prices.find { it.minAge <= age && age <= it.maxAge }?.amount

    fun getPriceWithDiscount(age: Int, discountPercentage: Int): BigDecimal? {
        val discountFactor = (0.01 * (100 - discountPercentage)).toBigDecimal()
        val originalPrice = getPrice(age)

        return originalPrice?.let { it * discountFactor }
    }
}

enum class ProductAnchor(val title: String) {
    ALICE_FULL("Menor preço"),
    ALICE_MEDIUM("Intermediário"),
    ALICE_EINSTEIN("Alice + Einstein"),
    ALICE_CUSTOM("Customizado"),
    ALICE_TIER_1("Alice tier 1"),
    ALICE_TIER_2("Alice tier 2"),
    ALICE_TIER_3("Alice tier 3"),
    ALICE_MEDIUM_B2B("Intermediário"),
    ALICE_FULL_B2B("Menor preço"),
    ALICE_EINSTEIN_B2B("Alice + Einstein"),
    ALICE_CUSTOM_B2B("Customizado"),
    ALICE_BALANCE_MEI("Equilíbrio MEI"),
    ALICE_BALANCE_ME("Equilíbrio ME"),
    ALICE_BALANCE_P("Equilíbrio P"),
    ALICE_BALANCE_M("Equilíbrio M"),
    ALICE_BALANCE_G("Equilíbrio G"),
    ALICE_CONFORT_MEI("Conforto MEI"),
    ALICE_CONFORT_ME("Conforto ME"),
    ALICE_CONFORT_P("Conforto P"),
    ALICE_CONFORT_M("Conforto M"),
    ALICE_CONFORT_G("Conforto G"),
    ALICE_EXCLUSIVE_MEI("Exclusivo MEI"),
    ALICE_EXCLUSIVE_ME("Exclusivo ME"),
    ALICE_EXCLUSIVE_P("Exclusivo P"),
    ALICE_EXCLUSIVE_M("Exclusivo M"),
    ALICE_EXCLUSIVE_G("Exclusivo G"),
    ALICE_CUSTOM_MEI("Customizado MEI"),
    ALICE_CUSTOM_ME("Customizado ME"),
    ALICE_CUSTOM_P("Customizado P"),
    ALICE_CUSTOM_M("Customizado M"),
    ALICE_CUSTOM_G("Customizado G");

    companion object {
        fun b2cAnchors() = listOf(
            ALICE_FULL,
            ALICE_MEDIUM,
            ALICE_EINSTEIN,
            ALICE_TIER_1,
            ALICE_TIER_2,
            ALICE_TIER_3,
            ALICE_CUSTOM
        )

        fun b2bAnchors() = listOf(
            ALICE_FULL_B2B,
            ALICE_MEDIUM_B2B,
            ALICE_EINSTEIN_B2B,
            ALICE_CUSTOM_B2B
        ) + balanceAnchors() + confortAnchors() + exclusiveAnchors() + customAnchors()

        fun balanceAnchors() = listOf(
            ALICE_BALANCE_MEI,
            ALICE_BALANCE_ME,
            ALICE_BALANCE_P,
            ALICE_BALANCE_M,
            ALICE_BALANCE_G
        )
        fun confortAnchors() = listOf(
            ALICE_CONFORT_MEI,
            ALICE_CONFORT_ME,
            ALICE_CONFORT_P,
            ALICE_CONFORT_M,
            ALICE_CONFORT_G
        )
        fun exclusiveAnchors() = listOf(
            ALICE_EXCLUSIVE_MEI,
            ALICE_EXCLUSIVE_ME,
            ALICE_EXCLUSIVE_P,
            ALICE_EXCLUSIVE_M,
            ALICE_EXCLUSIVE_G
        )

        fun customAnchors() = listOf(
            ALICE_CUSTOM_MEI,
            ALICE_CUSTOM_ME,
            ALICE_CUSTOM_P,
            ALICE_CUSTOM_M,
            ALICE_CUSTOM_G
        )

        fun allPAnchors() = listOf(
            ALICE_BALANCE_P,
            ALICE_CONFORT_P,
            ALICE_EXCLUSIVE_P
        )

        fun oldB2BAnchors() = listOf(
            ALICE_FULL_B2B,
            ALICE_MEDIUM_B2B,
            ALICE_EINSTEIN_B2B
        )

        fun oldB2CAnchors() = listOf(
            ALICE_FULL,
            ALICE_MEDIUM,
            ALICE_EINSTEIN
        )
    }
}

enum class ProductType {
    B2C,
    B2B,
    ADESAO,
}

enum class AccommodationType(val title: String) {
    NURSERY("Enfermaria"),
    ROOM("Apartamento individual")
}

enum class RefundType(val title: String) {
    FULL("Reembolso sempre"),
    NONE("Sem reembolso")
}

enum class B2BSubTypes(val title: String) {
    MEI("Micro Empreendedor Individual"),
    ME("Micro Empresa"),
    P("Pequena Empresa"),
    M("Média Empresa"),
    G("Grande Empresa")
}
enum class CoPaymentType(val title: String) {
    FULL("Coparticipação total"),
    UNCOORDINATED("Coparticipação por descoordenação"),
    EMERGENCY_UNIT("Coparticipação para Pronto Socorro"),
    NONE("Sem coparticipação")
}

enum class TierType(val title: String, val order: Int) {
    TIER_0("Tier 0", 1),
    TIER_1("Tier 1", 2),
    TIER_2("Tier 2", 3),
    TIER_3("Tier 3", 4),
    TIER_4("Tier 4", 5);
}

enum class HealthcareModelType(val title: String) {
    THIRD_PARTY("Terceiro"),
    V1("Time de saúde"),
    V2("Liga de saúde"),
    V3("Setembro 2023");
}

enum class PrimaryAttentionType(val title: String) {
    ALICE("Alice"),
    CIA("CIA"),
    EINSTEIN("Einstein"),
    FLEURY("Fleury");
}

enum class ProductCharacteristic(val description: String) {
    OBSTETRICIAN("Possui obstetra"),
    TRAVEL_INSURANCE("Possui seguro viagem"),
    CHECKUP_PLAN("Possui checkup"),
}

enum class ProductCoverageType(val description: String) {
    NATIONAL("Nacional"),
    CITY("Municipal"),
    CITY_GROUP("Grupo de municípios"),
}
