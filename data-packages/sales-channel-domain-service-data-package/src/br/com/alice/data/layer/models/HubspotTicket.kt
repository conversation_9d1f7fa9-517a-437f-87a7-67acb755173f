package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class HubspotTicket(
    override val id: UUID = RangeUUID.generate(),
    val dealId: String,
    val ongoingCompanyDealId: UUID,
    val ticketId: String,
    val pendingTaskDescription: String? = null,
    val stage: TicketStage,

    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model


enum class TicketStage (val friendlyStageName: String) {
    SALES_PENDING( "Vendas - Pendências"),
    CLOSED_MLC( "Fechado - MLC"),
    CLOSED_AGENT( "Fechado - Corretores"),
    FALLBACK("Em atualização");
}

fun getTicketStage(hubspotStageName: String): TicketStage = when (hubspotStageName) {
    // Unified pipeline
    "*********" -> TicketStage.SALES_PENDING
    "*********" -> TicketStage.CLOSED_AGENT
    "*********" -> TicketStage.CLOSED_MLC
    // Staging pipeline
    "1" -> TicketStage.SALES_PENDING
    "2" -> TicketStage.CLOSED_AGENT
    "3" -> TicketStage.CLOSED_MLC
    // Fallback
    else -> TicketStage.FALLBACK
}

fun TicketStage.toHubspotStage(): String {
    return when (this) {
        TicketStage.SALES_PENDING -> "*********"
        TicketStage.CLOSED_AGENT -> "*********"
        TicketStage.CLOSED_MLC -> "*********"
        //TODO Criar fallback no Hubspot
        TicketStage.FALLBACK -> "127229094"
    }
}
