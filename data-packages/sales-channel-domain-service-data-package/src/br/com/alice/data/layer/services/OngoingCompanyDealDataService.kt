package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.OngoingCompanyDeal
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID


@RemoteService
interface OngoingCompanyDealDataService: Service,
    Finder<OngoingCompanyDealDataService.FieldOptions, OngoingCompanyDealDataService.OrderingOptions, OngoingCompanyDeal>,
    Adder<OngoingCompanyDeal>,
    Getter<OngoingCompanyDeal>,
    Updater<OngoingCompanyDeal>,
    Counter<OngoingCompanyDealDataService.FieldOptions, OngoingCompanyDealDataService.OrderingOptions, OngoingCompanyDeal> {

    override val namespace: String
        get() = "sales_channel"
    override val serviceName: String
        get() = "ongoing_company_deal"

    class IdField: Field.UUIDField(OngoingCompanyDeal::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SalesFirmIdField: Field.UUIDField(OngoingCompanyDeal::salesFirmId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class SourceIdField: Field.TextField(OngoingCompanyDeal::sourceId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class NameField: Field.TextField(OngoingCompanyDeal::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class SalesAgentDocumentField: Field.TextField(OngoingCompanyDeal::salesAgentDocument) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class CNPJField: Field.TextField(OngoingCompanyDeal::cnpj) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)

        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class SearchTokensField: Field.TextField(OngoingCompanyDeal::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class CreatedAtField: Field.DateTimeField(OngoingCompanyDeal::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class DeletedAtField: Field.DateTimeField(OngoingCompanyDeal::deletedAt) {
        fun isNull() = Predicate.isNull(this)
    }

    class StatusField: Field.TextField(OngoingCompanyDeal::status) {
        fun eq(value: DealStage) = Predicate.eq(this, value)
        fun inList(value: List<DealStage>) = Predicate.inList(this, value)
    }

    class ChannelField: Field.TextField(OngoingCompanyDeal::channel) {
        fun eq(value: DealChannel) = Predicate.eq(this, value)
    }

    class CompanyIdField: Field.UUIDField(OngoingCompanyDeal::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class FieldOptions {
        val id = IdField()
        val salesFirmId = SalesFirmIdField()
        val sourceId = SourceIdField()
        val name = NameField()
        val cnpj = CNPJField()
        val salesAgentDocument = SalesAgentDocumentField()
        val searchTokens = SearchTokensField()
        val deletedAt = DeletedAtField()
        val status = StatusField()
        val channel = ChannelField()
        val companyId = CompanyIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<OngoingCompanyDeal, Throwable>
    override suspend fun add(model: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable>
    override suspend fun update(model: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<OngoingCompanyDeal>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
