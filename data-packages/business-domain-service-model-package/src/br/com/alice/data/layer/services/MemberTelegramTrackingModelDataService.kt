package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MemberTelegramTrackingModel
import br.com.alice.data.layer.services.MemberTelegramTrackingModelDataService.FieldOptions
import br.com.alice.data.layer.services.MemberTelegramTrackingModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface MemberTelegramTrackingModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, MemberTelegramTrackingModel>,
    Getter<MemberTelegramTrackingModel>,
    Adder<MemberTelegramTrackingModel>,
    Updater<MemberTelegramTrackingModel>,
    Counter<FieldOptions, OrderingOptions, MemberTelegramTrackingModel> {

    override val namespace: String
        get() = "business"
    override val serviceName: String
        get() = "member_telegram_tracking"

    class Name : Field.TextField(MemberTelegramTrackingModel::name) {
        fun eq(value: String) = Predicate.eq(this, value)

        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class ExternalId : Field.TextField(MemberTelegramTrackingModel::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)

        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class Tracking : Field.TextField(MemberTelegramTrackingModel::tracking) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class Status : Field.TextField(MemberTelegramTrackingModel::status) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun diff(value: String) = Predicate.diff(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class StatusDelivery : Field.TextField(MemberTelegramTrackingModel::statusDelivery) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class Tag : Field.TextField(MemberTelegramTrackingModel::tag) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class DateLastChange : Field.DateTimeField(MemberTelegramTrackingModel::dateLastChange) {
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(MemberTelegramTrackingModel::createdAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class UpdatedAt : Field.DateTimeField(MemberTelegramTrackingModel::updatedAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class SearchTokens : Field.TextField(MemberTelegramTrackingModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }


    class FieldOptions {
        val name = Name()
        val externalId = ExternalId()
        val tracking = Tracking()
        val status = Status()
        val statusDelivery = StatusDelivery()
        val tag = Tag()
        val searchTokens = SearchTokens()
    }

    class OrderingOptions {
        val dateLastChange = DateLastChange()
        val name = Name()
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: MemberTelegramTrackingModel): Result<MemberTelegramTrackingModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<MemberTelegramTrackingModel>, Throwable>
    override suspend fun update(model: MemberTelegramTrackingModel): Result<MemberTelegramTrackingModel, Throwable>
    override suspend fun get(id: UUID): Result<MemberTelegramTrackingModel, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
