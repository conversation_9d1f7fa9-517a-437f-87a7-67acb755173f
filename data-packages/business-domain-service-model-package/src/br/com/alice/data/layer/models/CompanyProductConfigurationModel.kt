package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.util.UUID

data class CompanyProductConfigurationModel(
    override val id: UUID = RangeUUID.generate(),
    val companyProductType: CompanyProductType,
    val availableProductIds: List<UUID>,
    val productPriceListIds: List<ProductPriceListingConfigurationModel> = emptyList(),
    val configurationVersion: ConfigurationVersion = ConfigurationVersion.STABLE,
    val version: Int = 0
) : Model

data class ProductPriceListingConfigurationModel(
    val productId: UUID,
    val priceListingId: UUID,
)

