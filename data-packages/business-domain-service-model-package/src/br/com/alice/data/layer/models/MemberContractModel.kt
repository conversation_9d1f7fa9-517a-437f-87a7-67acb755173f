package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.MemberReference
import br.com.alice.common.core.Model
import br.com.alice.common.models.DependentInformation
import kotlinx.serialization.Transient
import java.util.UUID

data class MemberContractModel(
    override val id: UUID = RangeUUID.generate(),
    override val memberId: UUID,
    @Transient
    val terms: List<MemberContractTerm>? = null,
    val signature: UserSignature? = null,
    val version: Int = 0,
) : Model, MemberReference, DependentInformation {
    @Transient
    val isSigned get() = signature != null

    fun sign(userSignature: UserSignature) = copy(signature = userSignature)
}

fun MemberContractModel.withTerms(terms: List<MemberContractTerm>?): MemberContractModel =
    this.copy(terms = terms)
