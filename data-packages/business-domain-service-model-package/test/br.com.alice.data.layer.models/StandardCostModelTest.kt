package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class StandardCostModelTest {

    @Test
    fun `#getValue should take right value when is dependent and male`() {
        val standardCost = buildStandardCostModel().copy(
            costByPersonDetail = CostByPersonDetailModel(
                holder = GenderDetailModel(male = emptyList(), female = emptyList()),
                dependent = GenderDetailModel(
                    male = listOf(
                        CostByAgeRangeModel(
                            minAge = 0,
                            maxAge = 15,
                            cost = BigDecimal.valueOf(120.0)
                        ),
                        CostByAgeRangeModel(
                            minAge = 16,
                            maxAge = 50,
                            cost = BigDecimal.valueOf(200.0)
                        )
                    ),
                    female = emptyList()
                )
            )
        )
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(30), sex = Sex.MALE)

        val result = standardCost.getValue(true, person.sex!!, person.age)

        assertThat(result).isEqualTo(BigDecimal.valueOf(200.0))
    }

    @Test
    fun `#getValue should take right value when is dependent and female`() {
        val standardCost = buildStandardCostModel().copy(
            costByPersonDetail = CostByPersonDetailModel(
                holder = GenderDetailModel(male = emptyList(), female = emptyList()),
                dependent = GenderDetailModel(
                    male = emptyList(),
                    female = listOf(
                        CostByAgeRangeModel(
                            minAge = 0,
                            maxAge = 15,
                            cost = BigDecimal.valueOf(120.0)
                        ),
                        CostByAgeRangeModel(
                            minAge = 16,
                            maxAge = 50,
                            cost = BigDecimal.valueOf(200.0)
                        )
                    ),
                )
            )
        )
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(12), sex = Sex.FEMALE)

        val result = standardCost.getValue(true, person.sex!!, person.age)

        assertThat(result).isEqualTo(BigDecimal.valueOf(120.0))
    }

    @Test
    fun `#getValue should take right value when is holder and male`() {
        val standardCost = buildStandardCostModel().copy(
            costByPersonDetail = CostByPersonDetailModel(
                dependent = GenderDetailModel(male = emptyList(), female = emptyList()),
                holder = GenderDetailModel(
                    male = listOf(
                        CostByAgeRangeModel(
                            minAge = 0,
                            maxAge = 15,
                            cost = BigDecimal.valueOf(120.0)
                        ),
                        CostByAgeRangeModel(
                            minAge = 16,
                            maxAge = 50,
                            cost = BigDecimal.valueOf(200.0)
                        )
                    ),
                    female = emptyList()
                )
            )
        )
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(30), sex = Sex.MALE)

        val result = standardCost.getValue(false, person.sex!!, person.age)

        assertThat(result).isEqualTo(BigDecimal.valueOf(200.0))
    }

    @Test
    fun `#getValue should take right value when is holder and female`() {
        val standardCost = TestModelFactory.buildStandardCost().copy(
            costByPersonDetail = CostByPersonDetail(
                dependent = GenderDetail(male = emptyList(), female = emptyList()),
                holder = GenderDetail(
                    male = emptyList(),
                    female = listOf(
                        CostByAgeRange(
                            minAge = 0,
                            maxAge = 15,
                            cost = BigDecimal.valueOf(120.0)
                        ),
                        CostByAgeRange(
                            minAge = 16,
                            maxAge = 50,
                            cost = BigDecimal.valueOf(200.0)
                        )
                    ),
                )
            )
        )
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(12), sex = Sex.FEMALE)

        val result = standardCost.getValue(false, person.sex!!, person.age)

        assertThat(result).isEqualTo(BigDecimal.valueOf(120.0))
    }

    private fun buildStandardCostModel(
        id: UUID = RangeUUID.generate(),
        companyBusinessUnit: CompanyBusinessUnit = CompanyBusinessUnit.CORRETORES,
        companySize: CompanySize = CompanySize.MICRO,
        adhesion: Adhesion = Adhesion.OPTIONAL,
        ansNumber: String = "123456",
        costByPersonDetail: CostByPersonDetailModel = CostByPersonDetailModel(
            holder = GenderDetailModel(
                male = listOf(
                    CostByAgeRangeModel(
                        minAge = 0,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(150.0)
                    )
                ),
                female = listOf(
                    CostByAgeRangeModel(
                        minAge = 8,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(285.0)
                    )
                )
            ),
            dependent = GenderDetailModel(
                male = listOf(
                    CostByAgeRangeModel(
                        minAge = 0,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(150.0)
                    )
                ),
                female = listOf(
                    CostByAgeRangeModel(
                        minAge = 8,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(285.0)
                    )
                )
            )
        ),
        updatedBy: UpdatedBy? = null
    ) = StandardCostModel(
        id = id,
        companySize = companySize,
        companyBusinessUnit = companyBusinessUnit,
        adhesion = adhesion,
        ansNumber = ansNumber,
        costByPersonDetail = costByPersonDetail,
        updatedBy = updatedBy
    )
}
