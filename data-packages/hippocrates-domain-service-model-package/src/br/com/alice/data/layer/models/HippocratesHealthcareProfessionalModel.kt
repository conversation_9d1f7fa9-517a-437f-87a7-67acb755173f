package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.models.State
import java.time.LocalDateTime
import java.util.UUID

data class HippocratesHealthcareProfessionalModel(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val fullName: String,
    val councilName: HippocratesHealthProfessionalCouncil,
    val councilState: State,
    val councilNumber: String,
    val searchTokens: String? = null,
    val specialties: List<String> = emptyList()
) : Model
