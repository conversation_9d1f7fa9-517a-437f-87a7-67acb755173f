package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class DbResultadoProcedimentoTest {

    @Test
    fun `#validResults returns results withou null or blank`() {
        val validResult = TestModelFactory.buildDbResultadoTexto(valorResultado = "123")
        val dbResultadoProcedimento = TestModelFactory.buildDbResultadoProcedimento(listaResultadoText = listOf(
            validResult,
            TestModelFactory.buildDbResultadoTexto(valorResultado = " "),
            TestModelFactory.buildDbResultadoTexto(valorResultado = null)
        ))

        assertThat(dbResultadoProcedimento.validResults()).isEqualTo(listOf(validResult))
    }

}
