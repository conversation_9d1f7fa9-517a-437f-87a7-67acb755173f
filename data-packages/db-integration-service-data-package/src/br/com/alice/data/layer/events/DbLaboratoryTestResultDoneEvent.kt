package br.com.alice.data.layer.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.DbLaboratoryTestResult

data class DbLaboratoryTestResultDoneEvent(
    val dbLaboratoryTestResult: DbLaboratoryTestResult
) : NotificationEvent<DbLaboratoryTestResultDonePayload>(
    name = name,
    producer = "db-integration-service",
    payload = DbLaboratoryTestResultDonePayload(
        dbLaboratoryTestResult = dbLaboratoryTestResult
    )
) {
    companion object {
        const val name = "db-laboratory-test-result-done"
    }
}

data class DbLaboratoryTestResultDonePayload(
    val dbLaboratoryTestResult: DbLaboratoryTestResult
)
