package br.com.alice.data.layer.models

import br.com.alice.common.BeneficiaryType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberInvoiceGroupInfoResponse(
    val memberInvoiceGroupId: UUID,
    val memberInvoicesInfo: List<MemberInvoiceInfo>,
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: MemberInvoiceGroupStatus,
    val totalAmount: BigDecimal,
    val globalItems: List<InvoiceItemResponse>?
)

data class MemberInvoiceInfo(
    val memberInvoiceId: UUID,
    val beneficiaryInfo: BeneficiaryInfo,
    val amount: BigDecimal,
    val status: InvoiceStatus,
    val canceledReason: CancellationReason? = null,
    val referenceDate: LocalDate,
    val dueDate: LocalDateTime,
    val paidAt: LocalDateTime? = null,
    val invoicePayments: List<InvoicePaymentResponse>? = emptyList(),
    val invoiceItems: List<InvoiceItemResponse>? = null,
    val invoiceBreakdown: List<InvoiceBreakdownItem>? = null,
)

data class BeneficiaryInfo(
    val memberId: UUID,
    val firstName: String,
    val lastName: String,
    val nationalId: String,
    val nationalIdHolder: String? = null,
    val type: BeneficiaryType,
    val productTitle: String,
    val productDisplayName: String? = null,
    val isMemberActive: Boolean,
)

data class InvoiceItemsResponse(
    val invoiceItems: List<InvoiceItemResponse>
)

data class InvoiceItemResponse(
    val id: UUID,
    val referenceDate: String,
    val operation: InvoiceItemOperation,
    val type: InvoiceItemType,
    val notes: String?,
    val value: BigDecimal,
    val unit: InvoiceItemValueUnit,
    val status: InvoiceItemStatus,
    val displayValue: BigDecimal? = null,
    val companySubcontractId: UUID? = null,
    val externalId: String? = null,
    val createdAt: LocalDateTime
)

data class CreateInvoiceItemsRequest(
    val fromDate: String,
    val operation: InvoiceItemOperation,
    val type: InvoiceItemTypeRequest,
    val notes: String?,
    val unit: InvoiceItemValueUnit,
    val value: BigDecimal,
    val recurringTimes: Int
)

data class UpdateInvoiceItemRequest(
    val id: UUID?,
    val referenceDate: String?,
    val operation: InvoiceItemOperation?,
    val type: InvoiceItemTypeRequest?,
    val notes: String?,
    val unit: InvoiceItemValueUnit?,
    val value: BigDecimal?,
    val status: InvoiceItemStatus?,
    val companySubcontractId: UUID?
)


enum class InvoiceItemTypeRequest {
    PROMO_CODE,
    PRORATION,
    FARE,
    COPAY,
    PRODUCT_CHANGE,
    SALES,
    SALES_RESULT,
    PROMO_CODE_RESULT,
    PLAN_READJUSTMENT,
    OPERATIONAL_ADJUSTMENT,
    INTEREST_OR_FINE,
    MONTHLY_ORGANIC_INCLUSION,
    OTHERS
}

data class InvoiceBreakdownItem(
    val title: String,
    val amount: BigDecimal,
)

