package br.com.alice.data.layer.models

import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.Test
import kotlin.test.assertEquals

class MemberInvoiceGroupTest {

    @Test
    fun `#calculateTotalAmount should calculate totalAmount with globalItems`() {
        val globalItems = listOf(
            TestModelFactory.buildInvoiceItem(operation = InvoiceItemOperation.CHARGE, resolvedValue = BigDecimal(5)),
            TestModelFactory.buildInvoiceItem(operation = InvoiceItemOperation.CHARGE, resolvedValue = BigDecimal(5)),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                resolvedValue = BigDecimal(-10)
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                absoluteValue = BigDecimal(10)
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                percentageValue = BigDecimal(10),
                absoluteValue = null
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.CHARGE,
                percentageValue = BigDecimal(5),
                absoluteValue = null
            )
        )
        val memberInvoices = listOf(
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500)),
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500))
        )
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoices.map { it.id },
            globalItems = globalItems
        ).copy(totalAmount = null)
        val expectedResult = memberInvoiceGroup.copy(totalAmount = BigDecimal("940.00").money)

        val result = memberInvoiceGroup.calculateTotalAmount(memberInvoices)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `#calculateTotalAmount should calculate totalAmount without globalItems`() {
        val memberInvoices = listOf(
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500)),
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500))
        )
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoices.map { it.id },
            globalItems = null
        ).copy(totalAmount = null)
        val expectedResult = memberInvoiceGroup.copy(totalAmount = BigDecimal("1000").money)

        val result = memberInvoiceGroup.calculateTotalAmount(memberInvoices)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `#markAsPaid should change the status to PAID`() {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            .markAsPaid()
        Assertions.assertThat(memberInvoiceGroup.status).isEqualTo(MemberInvoiceGroupStatus.PAID)
    }

    @Nested
    inner class ChangeReferenceDate {
        @Test
        fun `#should change the reference date and the due date when it is not a first payment`() {
            val date = LocalDate.now().withDayOfMonth(1)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                type = MemberInvoiceType.B2B_FIRST_PAYMENT,
                dueDate = date.minusDays(15),
                referenceDate = date.minusMonths(2),
            )
                .changeReferenceDate(date.atStartOfDay())

            Assertions.assertThat(memberInvoiceGroup.referenceDate).isEqualTo(date)
            Assertions.assertThat(memberInvoiceGroup.dueDate).isEqualTo(date)
        }

        @Test
        fun `#should not change the reference date and the due date when it is not a first payment`() {
            val date = LocalDate.now().withDayOfMonth(1)
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                type = MemberInvoiceType.REGULAR_PAYMENT,
                dueDate = date.minusDays(15),
                referenceDate = date.minusMonths(2),
            )
                .changeReferenceDate(date.atStartOfDay())

            Assertions.assertThat(memberInvoiceGroup.referenceDate).isEqualTo(date.minusMonths(2))
            Assertions.assertThat(memberInvoiceGroup.dueDate).isEqualTo(date.minusDays(15))
        }

    }
}
