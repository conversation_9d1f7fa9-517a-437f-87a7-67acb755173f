package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class ChannelFup(
    val name: String,
    val question: String,
    val answers: List<ChannelFupAnswer>,
    val status: Status,
    val listed: Boolean = true,
    override val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0
) : Model

data class ChannelFupAnswer(
    val icon: String? = "",
    val key: String,
    val label: String,
    val nextChannelFup: String? = "",
    val starred: Boolean? = false
)
