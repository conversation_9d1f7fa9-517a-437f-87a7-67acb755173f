package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.Channel
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.services.ChannelDataService.FieldOptions
import br.com.alice.data.layer.services.ChannelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ChannelDataService : Service,
    Finder<FieldOptions, OrderingOptions, Channel>,
    Counter<FieldOptions, OrderingOptions, Channel>,
    Adder<Channel>,
    Getter<Channel>,
    Updater<Channel> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "channel"

    class IdField: Field.TableIdField(Channel::id) {
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class ChannelIdField: Field.TextField(Channel::channelId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class PersonIdField: Field.UUIDField(Channel::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class StaffField: Field.JsonbField(Channel::staff) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value.map { "{\"$it\": {\"id\": \"$it\"}}" })
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ChannelTypeField: Field.TextField(Channel::type) {
        fun inList(value: List<ChannelType>) = Predicate.inList(this, value)
        fun eq(value: ChannelType) = Predicate.eq(this, value)
    }

    class StatusField: Field.TextField(Channel::status) {
        fun inList(value: List<ChannelStatus>) = Predicate.inList(this, value)
        fun eq(value: ChannelStatus) = Predicate.eq(this, value)
    }

    class ChannelCreatedAtField: Field.DateTimeField(Channel::channelCreatedAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class ChannelUpdatedAtField: Field.DateTimeField(Channel::channelUpdatedAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class NameField: Field.TextField(Channel::name) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class KindField: Field.TextField(Channel::kind) {
        fun eq(value: ChannelKind) = Predicate.eq(this, value)
    }

    class CategoryField: Field.TextField(Channel::category) {
        fun eq(value: ChannelCategory) = Predicate.eq(this, value)
    }

    class SubCategoryField: Field.TextField(Channel::subCategory) {
        fun eq(value: ChannelSubCategory) = Predicate.eq(this, value)
        fun inList(value: List<ChannelSubCategory>) = Predicate.inList(this, value)
    }

    class TimeLastMessageField: Field.DateTimeField(Channel::timeLastMessage) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class ArchivedReasonField: Field.TextField(Channel::archivedReason) {
        fun inList(value: List<ChannelArchivedReason>) = Predicate.inList(this, value)
    }

    class OriginField: Field.TextField(Channel::origin) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ChannelArchivedAtField: Field.DateTimeField(Channel::archivedAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class ScreeningNavigationIdField: Field.UUIDField(Channel::screeningNavigationId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class DemandsField: Field.JsonbField(Channel::demands) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value.map { "[{\"description\": {\"id\": \"$it\"}}]" })
    }


    class FieldOptions {
        val id = IdField()
        val channelId = ChannelIdField()
        val personId = PersonIdField()
        val staff = StaffField()
        val type = ChannelTypeField()
        val status = StatusField()
        val channelCreatedAt = ChannelCreatedAtField()
        val channelUpdatedAt = ChannelUpdatedAtField()
        val archivedAt = ChannelArchivedAtField()
        val name = NameField()
        val kind = KindField()
        val category = CategoryField()
        val subCategory = SubCategoryField()
        val timeLastMessage = TimeLastMessageField()
        val archivedReason = ArchivedReasonField()
        val origin = OriginField()
        val screeningNavigationId = ScreeningNavigationIdField()
        val demands = DemandsField()
    }

    class OrderingOptions {
        val channelCreatedAt = ChannelCreatedAtField()
        val channelUpdatedAt = ChannelUpdatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun getByChannelId(channelId: String) = findOne { where { this.channelId.eq(channelId) } }

    override suspend fun get(id: UUID): Result<Channel, Throwable>
    override suspend fun add(model: Channel): Result<Channel, Throwable>
    override suspend fun update(model: Channel): Result<Channel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<Channel>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
