package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class ChannelTag(
    val name: String,
    val categories: List<ChannelCategory>? = emptyList(),
    val active: Boolean = true,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0
) : Model


