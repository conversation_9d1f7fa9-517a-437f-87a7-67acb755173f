package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.GenericFileVault
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface GenericFileVaultDataService : Service,
    Finder<GenericFileVaultDataService.FieldOptions, GenericFileVaultDataService.OrderingOptions, GenericFileVault>,
    Adder<GenericFileVault>,
    Getter<GenericFileVault>,
    Deleter<GenericFileVault> {

    override val namespace: String
        get() = "generic_file_vault"
    override val serviceName: String
        get() = "file"

    class IdField: Field.UUIDField(GenericFileVault::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val id = IdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<GenericFileVault, Throwable>
    override suspend fun add(model: GenericFileVault): Result<GenericFileVault, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<GenericFileVault>, Throwable>
    override suspend fun delete(model: GenericFileVault): Result<Boolean, Throwable>
}
