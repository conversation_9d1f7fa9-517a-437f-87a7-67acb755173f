package br.com.alice.itau.clients

import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.serialization.gson
import br.com.alice.itau.ItauPixConfig
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauErrorException
import br.com.alice.itau.clients.models.ItauErrorResponse
import br.com.alice.itau.clients.models.ItauPixCancelRequest
import br.com.alice.itau.clients.models.ItauPixCreateRequest
import br.com.alice.itau.clients.models.ItauPixCreateResponse
import br.com.alice.itau.clients.models.ItauPixGetResponse
import br.com.alice.itau.encodeBase64
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode

class ItauPixClient(
    private val itauAuthClient: ItauAuthClient,
    private val client: HttpClient = ItauClientHelper.itauDefaultHttpClient(),
) : Spannable {
    open val config: ItauPixConfig = ServiceConfig.itauPixConfig

    suspend fun create(request: ItauPixCreateRequest): Result<ItauPixCreateResponse, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        return span("post") { span ->
            val url = config.baseUrl
            span.setAttribute("url", url)
            try {
                val response = client.post(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                    setBody(request)
                }
                span.setStatus(StatusCode.OK)
                response.body<ItauPixCreateResponse>().success()
            } catch (e: Exception) {
                handleError("createPix", e, span).failure()
            }
        }
    }

    suspend fun get(id: String): Result<ItauPixGetResponse, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        return span("get") { span ->
            val url = "${config.baseUrl}/$id"
            span.setAttribute("url", url)
            try {
                val response = client.get(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                }
                span.setStatus(StatusCode.OK)
                response.body<ItauPixGetResponse>().success()
            } catch (e: Exception) {
                handleError("getPix", e, span).failure()
            }
        }
    }

    suspend fun cancel(id: String): Result<Boolean, Throwable> {
        val accessToken = itauAuthClient.getAccessToken().get()
        return span("cancel") { span ->
            val url = "${config.baseUrl}/$id"
            span.setAttribute("url", url)
            try {
                val response = client.patch(url) {
                    contentType(ContentType.Application.Json)
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                    header("x-itau-apikey", config.apiKey)
                    setBody(ItauPixCancelRequest())
                }
                span.setStatus(StatusCode.OK)
                true.success()
            } catch (e: Exception) {
                handleError("cancelPix", e, span).failure()
            }
        }
    }

    private suspend fun handleError(requestName: String, e: Exception, span: Span): Exception {
        val (error, statusCode) = handleException(e, requestName, span)
        return ItauErrorException(error.message, statusCode)
    }

    private suspend fun handleException(
        e: Exception,
        requestName: String,
        span: Span
    ): Pair<ItauErrorResponse, HttpStatusCode> {
        logger.error("ItauPixClient:: error executing $requestName", e)
        span.recordException(e)
        return when (e) {
            is ResponseException -> handleResponseException(e) //400 + 500 family
            else -> throw e
        }
    }

    private suspend fun handleResponseException(re: ResponseException): Pair<ItauErrorResponse, HttpStatusCode> {
        try {
            val bodyText = re.response.bodyAsText()
            logger.error(
                "ItauAuthClient::post : error executing ItauAuth request",
                "body" to bodyText.encodeBase64()
            )
            return gson.fromJson(bodyText, ItauErrorResponse::class.java) to re.response.status
        } catch (e: Exception) {
            logger.error("ItauPixClient::post : error parsing Itau error response", e.message)
            throw e
        }
    }
}
