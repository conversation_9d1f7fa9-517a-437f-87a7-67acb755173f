package br.com.alice.itau.services.internal

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ItauPaymentModelDataService
import br.com.alice.itau.converter.toModel
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ItauPaymentServiceTest : MockedTestHelper() {
    private val itauPaymentModelDataService: ItauPaymentModelDataService = mockk()
    private val service = ItauPaymentService(itauPaymentModelDataService)
    private val serviceSpy = spyk(service)

    @Test
    fun `#create should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment()
        val itauPaymentModel = itauPayment.toModel()

        coEvery { itauPaymentModelDataService.add(itauPaymentModel) } returns itauPaymentModel
        coEvery { itauPaymentModelDataService.get(itauPaymentModel.id) } returns itauPaymentModel

        val result = service.add(itauPayment)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.add(any()) }
        coVerifyOnce { itauPaymentModelDataService.get(any()) }
    }

    @Test
    fun `#get should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment()
        val itauPaymentModel = itauPayment.toModel()

        coEvery { itauPaymentModelDataService.get(itauPayment.id) } returns itauPaymentModel

        val result = service.get(itauPayment.id)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.get(any()) }
    }

    @Test
    fun `#getByPaymentId should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment()
        val itauPaymentModel = itauPayment.toModel()

        coEvery {
            itauPaymentModelDataService.findOne(queryEq {
                where {
                    this.invoicePaymentId.eq(itauPayment.invoicePaymentId)
                }
            })
        } returns itauPaymentModel

        val result = service.getByPaymentId(itauPayment.invoicePaymentId)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.findOne(any()) }
    }

    @Test
    fun `#getByPixId should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment(pixId = "pixId")
        val itauPaymentModel = itauPayment.toModel()

        coEvery {
            itauPaymentModelDataService.findOne(queryEq {
                where {
                    this.pixId.eq(itauPayment.pixId!!)
                }
            })
        } returns itauPaymentModel

        val result = service.getByPixId(itauPayment.pixId!!)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.findOne(any()) }
    }

    @Test
    fun `#getByBoletoId should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment(boletoId = "boletoId")
        val itauPaymentModel = itauPayment.toModel()

        coEvery {
            itauPaymentModelDataService.findOne(queryEq {
                where {
                    this.boletoId.eq(itauPayment.boletoId!!)
                }
            })
        } returns itauPaymentModel

        val result = service.getByBoletoId(itauPayment.boletoId!!)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.findOne(any()) }
    }

    @Test
    fun `#getByOurNumber should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment(ourNumber = 1)
        val itauPaymentModel = itauPayment.toModel()

        coEvery {
            itauPaymentModelDataService.findOne(queryEq {
                where {
                    this.ourNumber.eq(itauPayment.ourNumber!!)
                }
            })
        } returns itauPaymentModel

        val result = service.getByOurNumber(itauPayment.ourNumber!!)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.findOne(any()) }
    }

    @Test
    fun `#createOrGetByPaymentId should try retrieve ItauPayment`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment(
            ourNumber = null,
        )
        val itauPaymentModel = itauPayment.toModel()

        coEvery {
            serviceSpy.getByPaymentId(itauPaymentModel.invoicePaymentId)
        } returns itauPayment

        val result = serviceSpy.createOrGetByPaymentId(itauPayment.invoicePaymentId)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { serviceSpy.getByPaymentId(any()) }
    }

    @Test
    fun `#createOrGetByPaymentId should try retrieve ItauPayment and add when its not found by invoice payment id`() =
        mockRangeUuidAndDateTime { uuid, localDateTime ->
            val itauPayment = TestModelFactory.buildItauPayment(
                id = uuid,
                ourNumber = null,
                createdAt = localDateTime,
                updatedAt = localDateTime
            )

            coEvery {
                serviceSpy.getByPaymentId(uuid)
            } returns NotFoundException().failure()

            coEvery {
                serviceSpy.add(itauPayment)
            } returns itauPayment

            val result = serviceSpy.createOrGetByPaymentId(uuid)

            ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

            coVerifyOnce { serviceSpy.getByPaymentId(any()) }
            coVerifyOnce { serviceSpy.add(any()) }
        }

    @Test
    fun `#update should call data service and retrieve its value`() = runBlocking {
        val itauPayment = TestModelFactory.buildItauPayment()
        val itauPaymentModel = itauPayment.toModel()

        coEvery { itauPaymentModelDataService.update(itauPaymentModel) } returns itauPaymentModel

        val result = service.update(itauPayment)

        ResultAssert.assertThat(result).isSuccessWithData(itauPayment)

        coVerifyOnce { itauPaymentModelDataService.update(any()) }
    }
}
