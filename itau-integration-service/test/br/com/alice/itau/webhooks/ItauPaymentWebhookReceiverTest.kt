package br.com.alice.itau.webhooks

import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.readFile
import br.com.alice.itau.events.ItauBoletoPaymentWebhookReceivedEvent
import br.com.alice.itau.events.ItauBoletoPaymentWebhookReceivedEventPayload
import br.com.alice.itau.events.ItauPixPaymentWebhookReceivedEvent
import br.com.alice.itau.events.ItauPixPaymentWebhookReceivedEventPayload
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.BeforeTest

class ItauPaymentWebhookReceiverTest : BaseWebhookReceiverTest() {

    private val kafkaProducerService: KafkaProducerService = mockk()
    private val itauPaymentWebhookReceiver = ItauPaymentWebhookReceiver(kafkaProducerService)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { itauPaymentWebhookReceiver }
    }

    @Test
    fun `success processing webhook pix`() {
        val event = ItauPixPaymentWebhookReceivedEvent(
            payload = ItauPixPaymentWebhookReceivedEventPayload(
                endToEndId = "E18236120202503291358s042c6d342d",
                pixId = "344e422b6c1e4e90b91fe37cf38189e8",
                amountPaid = "123.45",
                paidAt = "2025-03-29T13:58:12.095Z",
                pixKey = "34266553000102",
                responsibleInfo = "Alice",
            )
        )
        val successResponseJSON =
            readFile("testResources/itau-webhook-pix-success-response.json")
        coEvery {
            kafkaProducerService.produce(match {
                it as ItauPixPaymentWebhookReceivedEvent
                it.payload.pixId == event.payload.pixId &&
                        it.payload.paidAt == event.payload.paidAt &&
                        it.payload.amountPaid == event.payload.amountPaid
            })
        } returns mockk()

        post("/webhooks/pix", body = successResponseJSON) {
            ResponseAssert.assertThat(it).isOK()
        }

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `error processing webhook pix`() {
        val errorResponseJSON =
            readFile("testResources/itau-webhook-pix-fail-response.json")
        post("/webhooks/pix", body = errorResponseJSON) {
            ResponseAssert.assertThat(it).isBadRequest()
        }
    }

    @Test
    fun `success processing webhook boleto`() {
        val event = ItauBoletoPaymentWebhookReceivedEvent(
            payload = ItauBoletoPaymentWebhookReceivedEventPayload(
                boletoId = "2d2fce4a-4dd1-41bd-ad04-6d4ca6024a70",
                ourNumber = "00000034",
                amountPaid = "0.50",
                amountReceived = "0.50",
                paidAt = "2025-05-11",
                paidType = "06",
                personType = "F",
                cpf = "14337864601",
                cnpj = null,
                payerName = "Teste Integração Itau Teste Integração Itau",
                notificationDate = "2025-05-11",
                notificationTime = "18:56",
            )
        )
        val successResponseJSON =
            readFile("testResources/itau-webhook-boleto-success-response.json")
        coEvery {
            kafkaProducerService.produce(match {
                it as ItauBoletoPaymentWebhookReceivedEvent
                it.payload.boletoId == event.payload.boletoId &&
                        it.payload.paidAt == event.payload.paidAt &&
                        it.payload.amountPaid == event.payload.amountPaid
            })
        } returns mockk()

        post("/webhooks/boleto", body = successResponseJSON) {
            ResponseAssert.assertThat(it).isOK()
        }

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `error processing webhook boleto`() {
        val errorResponseJSON =
            readFile("testResources/itau-webhook-boleto-fail-response.json")
        post("/webhooks/pix", body = errorResponseJSON) {
            ResponseAssert.assertThat(it).isBadRequest()
        }
    }

}
