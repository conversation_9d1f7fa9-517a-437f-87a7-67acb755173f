package br.com.alice.membership.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.PersonHealthcareTeamRecommendation
import br.com.alice.data.layer.models.PersonHealthcareTeamRecommendationModel
import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test
import java.time.LocalDateTime
import java.util.UUID

class PersonHealthcareTeamRecommendationConverterTest {

    val personHealthcareTeamRecommendation = PersonHealthcareTeamRecommendation(
        id = RangeUUID.generate(),
        personId = PersonId(),
        recommendedTeamId = RangeUUID.generate(),
        recommendedTeamRules = listOf("rule1", "rule2"),
        otherTeams = listOf(RangeUUID.generate(), RangeUUID.generate()),
        chosenTeamId = RangeUUID.generate(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        version = 1
    )

    val personHealthcareTeamRecommendationModel = PersonHealthcareTeamRecommendationModel(
        id = personHealthcareTeamRecommendation.id,
        personId = personHealthcareTeamRecommendation.personId,
        recommendedTeamId = personHealthcareTeamRecommendation.recommendedTeamId,
        recommendedTeamRules = personHealthcareTeamRecommendation.recommendedTeamRules,
        otherTeams = personHealthcareTeamRecommendation.otherTeams,
        chosenTeamId = personHealthcareTeamRecommendation.chosenTeamId,
        createdAt = personHealthcareTeamRecommendation.createdAt,
        updatedAt = personHealthcareTeamRecommendation.updatedAt,
        version = personHealthcareTeamRecommendation.version
    )

    @Test
    fun `#toModel`() {
        assertEquals(personHealthcareTeamRecommendationModel, personHealthcareTeamRecommendation.toModel())
    }

    @Test
    fun `#toTransport`() {
        assertEquals(personHealthcareTeamRecommendation, personHealthcareTeamRecommendationModel.toTransport())
    }
} 
