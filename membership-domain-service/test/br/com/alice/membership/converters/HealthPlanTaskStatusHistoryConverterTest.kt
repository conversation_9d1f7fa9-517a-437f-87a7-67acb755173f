package br.com.alice.membership.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistory
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistoryBlameUserType
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistoryModel
import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test
import java.time.LocalDateTime
import java.util.UUID

class HealthPlanTaskStatusHistoryConverterTest {

    val healthPlanTaskStatusHistory = HealthPlanTaskStatusHistory(
        id = RangeUUID.generate(),
        healthPlanTaskId = RangeUUID.generate(),
        personId = PersonId(),
        previousStatus = HealthPlanTaskStatus.DRAFT,
        currentStatus = HealthPlanTaskStatus.ACTIVE,
        version = 1,
        blameUser = RangeUUID.generate(),
        blameUserType = HealthPlanTaskStatusHistoryBlameUserType.STAFF,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    val healthPlanTaskStatusHistoryModel = HealthPlanTaskStatusHistoryModel(
        id = healthPlanTaskStatusHistory.id,
        healthPlanTaskId = healthPlanTaskStatusHistory.healthPlanTaskId,
        personId = healthPlanTaskStatusHistory.personId,
        previousStatus = healthPlanTaskStatusHistory.previousStatus,
        currentStatus = healthPlanTaskStatusHistory.currentStatus,
        version = healthPlanTaskStatusHistory.version,
        blameUser = healthPlanTaskStatusHistory.blameUser,
        blameUserType = healthPlanTaskStatusHistory.blameUserType,
        createdAt = healthPlanTaskStatusHistory.createdAt,
        updatedAt = healthPlanTaskStatusHistory.updatedAt
    )

    @Test
    fun `#toModel`() {
        assertEquals(healthPlanTaskStatusHistoryModel, healthPlanTaskStatusHistory.toModel())
    }

    @Test
    fun `#toTransport`() {
        assertEquals(healthPlanTaskStatusHistory, healthPlanTaskStatusHistoryModel.toTransport())
    }
} 
