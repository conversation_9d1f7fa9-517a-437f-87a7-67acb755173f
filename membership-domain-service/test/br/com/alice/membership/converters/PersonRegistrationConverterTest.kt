package br.com.alice.membership.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.PersonRegistration
import br.com.alice.data.layer.models.PersonRegistrationAnswer
import br.com.alice.data.layer.models.PersonRegistrationAnswerModel
import br.com.alice.data.layer.models.PersonRegistrationModel
import br.com.alice.data.layer.models.PersonRegistrationStep
import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test
import java.time.LocalDateTime
import java.util.UUID

class PersonRegistrationConverterTest {

    val personRegistrationAnswer = PersonRegistrationAnswer(
        step = PersonRegistrationStep.NAME_CONFIRMATION,
        value = "John Doe"
    )

    val personRegistrationAnswerModel = PersonRegistrationAnswerModel(
        step = PersonRegistrationStep.NAME_CONFIRMATION,
        value = "John Doe"
    )

    val personRegistration = PersonRegistration(
        personId = PersonId(),
        currentStep = PersonRegistrationStep.NAME_CONFIRMATION,
        answers = listOf(personRegistrationAnswer),
        finishedAt = LocalDateTime.now(),
        archived = false,
        id = RangeUUID.generate(),
        version = 1,
        createdAt = LocalDateTime.now()
    )

    val personRegistrationModel = PersonRegistrationModel(
        personId = personRegistration.personId,
        currentStep = personRegistration.currentStep,
        answers = listOf(personRegistrationAnswerModel),
        finishedAt = personRegistration.finishedAt,
        archived = personRegistration.archived,
        id = personRegistration.id,
        version = personRegistration.version,
        createdAt = personRegistration.createdAt
    )

    @Test
    fun `#toModel`() {
        assertEquals(personRegistrationModel, personRegistration.toModel())
    }

    @Test
    fun `#toTransport`() {
        assertEquals(personRegistration, personRegistrationModel.toTransport())
    }
} 
