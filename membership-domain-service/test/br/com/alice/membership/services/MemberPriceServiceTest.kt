package br.com.alice.membership.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.getPrice
import br.com.alice.membership.ioc.MemberPriceConverter.toMemberPrice
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MemberPriceServiceTest {

    private val personService: PersonService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val memberService: MemberService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val memberProductPriceService: MemberProductPriceService = mockk()

    private val memberPriceService = MemberPriceServiceImpl(
        personService,
        beneficiaryService,
        memberService,
        companyProductPriceListingService,
        memberProductPriceService,
    )

    @Test
    fun `#getByMemberId get MemberPrice for member b2b`() = runBlocking {
        val member = TestModelFactory.buildMember(productType = ProductType.B2B)
        val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = RangeUUID.generate())
        val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing()
        coEvery { memberService.get(any()) } returns member
        coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(
                any(),
                any()
            )
        } returns companyProductPriceListing
        val result = memberPriceService.getByMemberId(member.id)

        assertThat(result).isSuccessWithData(companyProductPriceListing.toMemberPrice())
    }

    @Test
    fun `#getByMemberId get MemberPrice for member b2b but the beneficiary belongs to a company found into FF`() =
        runBlocking {
            val companyId = RangeUUID.generate()
            withFeatureFlag(
                FeatureNamespace.MEMBERSHIP,
                "should_create_mpp_for_this_companies",
                listOf(companyId.toString())
            ) {

                val member = TestModelFactory.buildMember(productType = ProductType.B2B)
                val beneficiary = TestModelFactory.buildBeneficiary(
                    companySubContractId = RangeUUID.generate(),
                    companyId = companyId,
                )
                val priceListItems = listOf(TestModelFactory.buildPriceListingItem(0, 999))
                val mppl = TestModelFactory.buildMemberProductPrice(items = priceListItems)

                coEvery { memberService.get(any()) } returns member
                coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
                coEvery { memberProductPriceService.findCurrent(any()) } returns mppl
                val result = memberPriceService.getByMemberId(member.id)

                assertThat(result).isSuccessWithData(mppl.toMemberPrice())

                coVerifyNone {
                    companyProductPriceListingService.findCurrentBySubContractIdAndProductId(
                        any(),
                        any()
                    )
                    memberProductPriceService.setCurrent(any(), any(), any())
                }
            }
        }

    @Test
    fun `#getByMemberId get MemberPrice for member b2b but the beneficiary belongs to a company found into FF and create a new mpp when it not found`() =
        runBlocking {
            val companyId = RangeUUID.generate()
            withFeatureFlag(
                FeatureNamespace.MEMBERSHIP,
                "should_create_mpp_for_this_companies",
                listOf(companyId.toString())
            ) {

                val member = TestModelFactory.buildMember(productType = ProductType.B2B)
                val beneficiary = TestModelFactory.buildBeneficiary(
                    companySubContractId = RangeUUID.generate(),
                    companyId = companyId,
                )
                val priceListItems = listOf(TestModelFactory.buildPriceListingItem(0, 999))
                val mppl = TestModelFactory.buildMemberProductPrice(items = priceListItems)

                coEvery { memberService.get(any()) } returns member
                coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
                coEvery { memberProductPriceService.findCurrent(any()) } returns NotFoundException("")

                coEvery { memberProductPriceService.setCurrent(any(), any(), any()) } returns mppl


                val result = memberPriceService.getByMemberId(member.id)

                assertThat(result).isSuccessWithData(mppl.toMemberPrice())

                coVerifyNone {
                    companyProductPriceListingService.findCurrentBySubContractIdAndProductId(
                        any(),
                        any()
                    )
                }
            }
        }

    @Test
    fun `#getByMemberId get MemberPrice for member b2c`() = runBlocking {
        val priceListItems = listOf(TestModelFactory.buildPriceListingItem(0, 999))
        val member = TestModelFactory.buildMember(productType = ProductType.B2C)
        val mppl = TestModelFactory.buildMemberProductPrice(items = priceListItems)

        coEvery { memberService.get(any()) } returns member
        coEvery { memberProductPriceService.findCurrent(any()) } returns mppl

        val result = memberPriceService.getByMemberId(member.id)

        assertThat(result).isSuccessWithData(mppl.toMemberPrice())
    }

    @Test
    fun `#getCurrentPriceForMember get current price for member b2b`() = runBlocking {
        val priceListItems = listOf(TestModelFactory.buildPriceListingItem(0, 999))
        val member = TestModelFactory.buildMember(productType = ProductType.B2B)
        val person = TestModelFactory.buildPerson()
        val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = RangeUUID.generate())
        val companyProductPriceListing =
            TestModelFactory.buildCompanyProductPriceListing(priceListItems = priceListItems)
        coEvery { memberService.get(any()) } returns member
        coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
        coEvery { personService.get(any()) } returns person
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(
                any(),
                any()
            )
        } returns companyProductPriceListing
        val result = memberPriceService.getCurrentPriceForMember(member.id)

        assertThat(result).isSuccessWithData(companyProductPriceListing.priceListItems.getPrice(person.age)!!)
    }

    @Test
    fun `#getCurrentPriceForMember get current price for member b2c`() = runBlocking {
        val priceListItems = listOf(TestModelFactory.buildPriceListingItem(0, 999))
        val member = TestModelFactory.buildMember(productType = ProductType.B2C)
        val person = TestModelFactory.buildPerson()
        val mppl = TestModelFactory.buildMemberProductPrice(items = priceListItems)

        coEvery { memberService.get(any()) } returns member
        coEvery { memberProductPriceService.findCurrent(any()) } returns mppl
        coEvery { personService.get(any()) } returns person

        val result = memberPriceService.getCurrentPriceForMember(member.id)

        assertThat(result).isSuccessWithData(priceListItems.getPrice(person.age)!!)
    }
}
