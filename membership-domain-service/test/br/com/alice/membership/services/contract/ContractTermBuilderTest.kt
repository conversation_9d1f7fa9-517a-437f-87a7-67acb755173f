package br.com.alice.membership.services.contract

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.document.pdf.PDFConverter
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthDeclaration
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.client.onboarding.ProductOrderService
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.membership.storage.MemberDocuments
import br.com.alice.membership.storage.MemberVaultDocuments
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductNotFoundException
import br.com.alice.product.model.ProductWithProviders
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.slot
import io.mockk.unmockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ContractTermBuilderTest {

    private val productOrderService: ProductOrderService = mockk()

    private val insurancePortabilityService: InsurancePortabilityService = mockk()

    private val healthDeclarationForm: HealthDeclarationForm = mockk()

    private val memberDocuments: MemberDocuments = mockk()

    private val personService: PersonService = mockk()

    private val beneficiaryService: BeneficiaryService = mockk()

    private val memberVaultDocuments: MemberVaultDocuments = mockk()

    private val contractTermBuilder = ContractTermBuilder(
        productOrderService,
        healthDeclarationForm,
        memberDocuments,
        insurancePortabilityService,
        personService,
        beneficiaryService,
        memberVaultDocuments,
    )

    private val personId1 = PersonId()
    private val person = TestModelFactory.buildPerson(
        personId = personId1,
        socialName = "Pedro",
        lastName = "Nogueira",
        nationalId = "12345678900",
        dateOfBirth = LocalDateTime.now().minusYears(35)
    )

    private val product = TestModelFactory.buildProduct(accommodation = AccommodationType.ROOM)
    private val provider = TestModelFactory.buildProvider()

    private val productOrder = TestModelFactory.buildProductOrder(person.id, product)

    private val productWithComponents = ProductWithProviders(product, listOf(provider))
    private val orderWithProductWithProviders = OrderWithProductWithProviders(productOrder, productWithComponents)

    private val personId2 = PersonId()
    private val personSpouseDependent = TestModelFactory.buildPerson(
        personId = personId2,
        firstName = "Maria",
        lastName = "Antonia",
        nationalId = "78253525052",
        dateOfBirth = LocalDateTime.now().minusYears(30),
    )

    private val beneficiaryHolder = TestModelFactory.buildBeneficiary(
        id = RangeUUID.generate(),
        personId = person.id
    )

    private val beneficiarySpouseDependent = TestModelFactory.buildBeneficiary(
        personId = personSpouseDependent.id,
        parentBeneficiary = beneficiaryHolder.id,
        parentBeneficiaryRelationType = ParentBeneficiaryRelationType.SPOUSE,
        type = BeneficiaryType.DEPENDENT,
    )

    @BeforeTest
    fun setup() {
        coEvery { insurancePortabilityService.findApprovedByPerson(person.id) } returns NotFoundException("not_found")
    }

    @Test
    fun `#generateDocument should throw exception when a product hasn't been found`() = runBlocking {
        coEvery {
            productOrderService.findOrderWithProductAndProvidersByPersonId(
                person.id,
                ProductOrderService.FindOptions(withPriceListing = true)
            )
        } returns NotFoundException("ProductOrder not found")

        val documentResult = contractTermBuilder.generateDocument(person)
        assertThat(documentResult).isFailureOfType(ProductNotFoundException::class)
    }

    @Test
    fun `#generateDocument should throw exception when a health declaration hasn't been found`() = runBlocking {
        coEvery {
            productOrderService.findOrderWithProductAndProvidersByPersonId(
                person.id,
                ProductOrderService.FindOptions(withPriceListing = true)
            )
        } returns orderWithProductWithProviders
        coEvery { healthDeclarationForm.findByPerson(person.id) } returns NotFoundException("not_found")

        val documentResult = contractTermBuilder.generateDocument(person)
        assertThat(documentResult).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#generateDocument should upload a contract`() = runBlocking {
        coEvery {
            productOrderService.findOrderWithProductAndProvidersByPersonId(
                person.id,
                ProductOrderService.FindOptions(withPriceListing = true)
            )
        } returns orderWithProductWithProviders
        coEvery { healthDeclarationForm.findByPerson(person.id) } returns buildHealthDeclaration(person.id)

        val pdfFile = slot<ByteArray>()
        coEvery { memberDocuments.uploadContract(person, capture(pdfFile)) } returns "path"

        val documentResult = contractTermBuilder.generateDocument(person)
        val pdf = pdfFile.captured

        val expectedDocument = ContractDocument(person, orderWithProductWithProviders.order, "path")
        assertThat(pdf).isNotEmpty()
        assertThat(documentResult).isSuccessWithData(expectedDocument)
    }

    @Test
    fun `#generateDocument should upload a contract (no CPT)`() = runBlocking {
        coEvery {
            productOrderService.findOrderWithProductAndProvidersByPersonId(
                person.id,
                ProductOrderService.FindOptions(withPriceListing = true)
            )
        } returns orderWithProductWithProviders
        coEvery { healthDeclarationForm.findByPerson(person.id) } returns buildHealthDeclaration(person.id).copy(cpts = emptyList())

        val pdfFile = slot<ByteArray>()
        coEvery { memberDocuments.uploadContract(person, capture(pdfFile)) } returns "path"

        val documentResult = contractTermBuilder.generateDocument(person)
        val pdf = pdfFile.captured

        val expectedDocument = ContractDocument(person, orderWithProductWithProviders.order, "path")
        assertThat(pdf).isNotEmpty()
        assertThat(documentResult).isSuccessWithData(expectedDocument)
    }

    @Test
    fun `#generateDocument should return an error when PDF could not be created`() = runBlocking {
        coEvery {
            productOrderService.findOrderWithProductAndProvidersByPersonId(
                person.id,
                ProductOrderService.FindOptions(withPriceListing = true)
            )
        } returns orderWithProductWithProviders
        coEvery { healthDeclarationForm.findByPerson(person.id) } returns buildHealthDeclaration(person.id)

        mockkObject(PDFConverter)
        coEvery { PDFConverter.htmlToPDF(any()) } throws Exception()

        val documentResult = contractTermBuilder.generateDocument(person)
        assertThat(documentResult).isFailureOfType(Exception::class)
        coVerifyNone { memberDocuments.uploadContract(any(), any()) }

        unmockkObject(PDFConverter)
    }

    @Test
    fun `#generateHealthDeclarationDocumentB2B should generate contract and upload it for B2B member successfully`() =
        runBlocking {
            val file = TestModelFactory.buildFileVault()

            coEvery { healthDeclarationForm.findByPerson(personSpouseDependent.id) } returns buildHealthDeclaration(
                personSpouseDependent.id
            )
            coEvery { beneficiaryService.get(beneficiarySpouseDependent.parentBeneficiary!!) } returns beneficiaryHolder
            coEvery { personService.get(beneficiaryHolder.personId) } returns person
            coEvery { memberVaultDocuments.uploadPdf(personSpouseDependent.id, "term", any()) } returns file

            val pdfFile = contractTermBuilder.generateHealthDeclarationDocumentB2B(
                personSpouseDependent,
                beneficiarySpouseDependent
            )

            assertThat(pdfFile).isSuccessWithData(file)
        }

    @Test
    fun `#generateHealthDeclarationDocumentB2B should generate contract and upload it for B2B member successfully (No CPT)`() =
        runBlocking {
            val file = TestModelFactory.buildFileVault()

            coEvery { healthDeclarationForm.findByPerson(personSpouseDependent.id) } returns buildHealthDeclaration(
                personSpouseDependent.id
            ).copy(cpts = emptyList())
            coEvery { beneficiaryService.get(beneficiarySpouseDependent.parentBeneficiary!!) } returns beneficiaryHolder
            coEvery { personService.get(beneficiaryHolder.personId) } returns person
            coEvery { memberVaultDocuments.uploadPdf(personSpouseDependent.id, "term", any()) } returns file

            val pdfFile = contractTermBuilder.generateHealthDeclarationDocumentB2B(
                personSpouseDependent,
                beneficiarySpouseDependent
            )

            assertThat(pdfFile).isSuccessWithData(file)
        }

}
