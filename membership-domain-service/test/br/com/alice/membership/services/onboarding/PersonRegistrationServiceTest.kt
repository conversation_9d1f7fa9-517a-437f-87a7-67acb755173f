package br.com.alice.membership.services.onboarding

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.OnboardingPhase.CHILD_VIDEOS_REQUEST
import br.com.alice.data.layer.models.OnboardingPhase.REGISTRATION
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.data.layer.models.PersonRegistration
import br.com.alice.data.layer.models.PersonRegistrationAnswer
import br.com.alice.data.layer.models.PersonRegistrationModel
import br.com.alice.data.layer.models.PersonRegistrationStep.CONCLUSION
import br.com.alice.data.layer.models.PersonRegistrationStep.MOTHERS_NAME
import br.com.alice.data.layer.models.PersonRegistrationStep.NAME_CONFIRMATION
import br.com.alice.data.layer.models.PersonRegistrationStep.NICK_NAME_CHANGE
import br.com.alice.data.layer.models.PersonRegistrationStep.TERMS_ACCEPTANCE
import br.com.alice.data.layer.services.PersonRegistrationModelDataService
import br.com.alice.membership.client.onboarding.InvalidAnswerException
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.RegistrationAlreadyFinishedException
import br.com.alice.membership.client.onboarding.RegistrationAlreadyStartedException
import br.com.alice.membership.client.onboarding.RegistrationNotStartedException
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.model.onboarding.ConclusionStateAnswers
import br.com.alice.membership.model.onboarding.NameConfirmationStateAnswers
import br.com.alice.membership.model.onboarding.PersonRegistrationState
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class PersonRegistrationServiceTest {

    private val dataService: PersonRegistrationModelDataService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val personService: PersonService = mockk()

    init {
        every { onboardingService.path() } returns "membership/onboarding"
    }

    private val person = TestModelFactory.buildPerson()
    private val child = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(3))

    private val personRegistrationService =
        PersonRegistrationServiceImpl(dataService, onboardingService, personService, LocalProducer)

    @Test
    fun `#startRegistrationProcess should start a new onboarding for a specific person if he didnt started yet`() =
        runBlocking {
            val firstState = PersonRegistrationState.firstState()

            val expectedPersonRegistration = PersonRegistration(
                personId = person.id,
                currentStep = firstState.step,
                answers = emptyList()
            )

            coEvery { dataService.add(any()) } returns Result.success(expectedPersonRegistration.toModel())
            coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns
                    emptyList<PersonRegistrationModel>().success()

            val result = personRegistrationService.create(person.id)

            ResultAssert.assertThat(result).isSuccess()
            assertThat(result.get().personId).isEqualTo(person.id)

            coVerify(exactly = 1) { dataService.add(any()) }
        }

    @Test
    fun `#startRegistrationProcess should not start when a user already started a registration process`() =
        runBlocking {
            val expectedPersonRegistration = PersonRegistration(
                personId = person.id,
                currentStep = PersonRegistrationState.firstState().step,
                answers = emptyList()
            )

            coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
                expectedPersonRegistration.toModel()
            ).success()

            val result = personRegistrationService.create(person.id)
            ResultAssert.assertThat(result).isFailureOfType(RegistrationAlreadyStartedException::class)

            coVerify(exactly = 0) { dataService.add(any()) }
        }

    @Test
    fun `#answer should return onboarding_not_started when it does not exists`() = runBlocking {
        coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns
                emptyList<PersonRegistrationModel>().success()

        val result = personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = NAME_CONFIRMATION,
                value = "Bene"
            )
        )

        ResultAssert.assertThat(result).isFailureOfType(RegistrationNotStartedException::class)
    }

    @Test
    fun `#answer should return onboarding_already_finished when process is fully answered`() = runBlocking {
        val finishedOnboarding = PersonRegistration(
            personId = person.id,
            currentStep = TERMS_ACCEPTANCE,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CONTINUE),
                PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true")
            ),
            finishedAt = LocalDateTime.now()
        )

        coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
            finishedOnboarding.toModel()
        ).success()

        val result = personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = NAME_CONFIRMATION,
                value = "Bene"
            )
        )

        ResultAssert.assertThat(result).isFailureOfType(RegistrationAlreadyFinishedException::class)
    }

    @Test
    fun `#answer should return answer_not_valid when the answer is not valid for the step`() = runBlocking {
        val finishedOnboarding = PersonRegistration(
            personId = person.id,
            currentStep = NAME_CONFIRMATION,
            answers = emptyList()
        )

        coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
            finishedOnboarding.toModel()
        ).success()

        val result = personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = NAME_CONFIRMATION,
                value = ""
            )
        )

        ResultAssert.assertThat(result).isFailureOfType(InvalidAnswerException::class)
    }

    @Test
    fun `#answer should return REGISTRATION as the next phase when some answer was edited`() = runBlocking {
        val finishedOnboardingState = PersonRegistration(
            personId = person.id,
            currentStep = TERMS_ACCEPTANCE,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CONTINUE),
                PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene")
            )
        )

        val updatedOnboardingState = finishedOnboardingState.copy(
            currentStep = NICK_NAME_CHANGE,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene")
            )
        )

        coEvery { dataService.update(match { it.currentStep == NICK_NAME_CHANGE }) } returns Result.success(
            updatedOnboardingState.toModel()
        )
        coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
            finishedOnboardingState.toModel()
        ).success()

        val result = personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = NAME_CONFIRMATION,
                value = NameConfirmationStateAnswers.CHANGE
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        assertThat(result.get().onboardingPhase).isEqualTo(REGISTRATION)

        coVerify(exactly = 1) { dataService.update(match { it.currentStep == NICK_NAME_CHANGE }) }
    }

    @Test
    fun `#answer should return REGISTRATION as the next phase when a step was answered`() = runBlocking {
        val firstOnboardingState = PersonRegistration(
            personId = person.id,
            currentStep = NAME_CONFIRMATION,
            answers = emptyList()
        )

        val updatedOnboardingState = firstOnboardingState.copy(
            currentStep = NICK_NAME_CHANGE,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE)
            )
        )

        coEvery { dataService.update(match { it.currentStep == NICK_NAME_CHANGE }) } returns Result.success(
            updatedOnboardingState.toModel()
        )
        coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
            firstOnboardingState.toModel()
        ).success()

        val result = personRegistrationService.answer(
            person, PersonRegistrationAnswer(
                step = NAME_CONFIRMATION,
                value = NameConfirmationStateAnswers.CHANGE
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        assertThat(result.get().onboardingPhase).isEqualTo(REGISTRATION)

        coVerify(exactly = 1) { dataService.update(match { it.currentStep == NICK_NAME_CHANGE }) }
    }

    @Test
    fun `#answer should change onboarding phase to HEALTH_DECLARATION_APPOINTMENT when the last step was answered and person is older than 10 years`() =
        runBlocking {
            val personRegistration = PersonRegistration(
                personId = person.id,
                currentStep = CONCLUSION,
                answers = listOf(
                    PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                    PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                    PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true"),
                    PersonRegistrationAnswer(MOTHERS_NAME, "Nome da Mãe")
                )
            )

            val updatedPersonRegistration = personRegistration.copy(
                answers = personRegistration.answers.plus(
                    PersonRegistrationAnswer(
                        CONCLUSION,
                        ConclusionStateAnswers.FINISH_CHOICE
                    )
                ),
                finishedAt = LocalDateTime.now()
            )

            coEvery { dataService.find(queryEq { where { this.personId.eq(person.id) and this.archived.eq(false) } }) } returns listOf(
                personRegistration.toModel()
            ).success()
            coEvery { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) } returns Result.success(
                updatedPersonRegistration.toModel()
            )
            coEvery { onboardingService.changePhaseTo(person.id, REGISTRATION.nextPhase()) } returns Result.success(
                PersonOnboarding(
                    personId = person.id,
                    currentPhase = REGISTRATION.nextPhase()
                )
            )

            coEvery { personService.updatePerson(updatedPersonRegistration) } returns Result.success(person)

            val result = personRegistrationService.answer(
                person, PersonRegistrationAnswer(
                    step = CONCLUSION,
                    value = ConclusionStateAnswers.FINISH_CHOICE
                )
            )

            ResultAssert.assertThat(result).isSuccess()
            assertThat(result.get().onboardingPhase).isEqualTo(REGISTRATION.nextPhase())

            coVerify(exactly = 1) { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) }
            coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, REGISTRATION.nextPhase()) }
        }

    @Test
    fun `#answer should change onboarding phase to CHILD_VIDEOS_REQUEST when the last step was answered and person age is under 10 and feature flag is true`() =
        runBlocking {
            val personRegistration = PersonRegistration(
                personId = child.id,
                currentStep = CONCLUSION,
                answers = listOf(
                    PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                    PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                    PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true"),
                    PersonRegistrationAnswer(MOTHERS_NAME, "Nome da Mãe")
                )
            )

            val updatedPersonRegistration = personRegistration.copy(
                answers = personRegistration.answers.plus(
                    PersonRegistrationAnswer(
                        CONCLUSION,
                        ConclusionStateAnswers.FINISH_CHOICE
                    )
                ),
                finishedAt = LocalDateTime.now()
            )

            coEvery { dataService.find(queryEq { where { this.personId.eq(child.id) and this.archived.eq(false) } }) } returns listOf(
                personRegistration.toModel()
            ).success()
            coEvery { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) } returns Result.success(
                updatedPersonRegistration.toModel()
            )
            coEvery { onboardingService.changePhaseTo(child.id, CHILD_VIDEOS_REQUEST) } returns Result.success(
                PersonOnboarding(
                    personId = child.id,
                    currentPhase = CHILD_VIDEOS_REQUEST
                )
            )

            coEvery { personService.updatePerson(updatedPersonRegistration) } returns Result.success(child)

            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "should_go_to_child_onboarding", true) {
                val result = personRegistrationService.answer(
                    child, PersonRegistrationAnswer(
                        step = CONCLUSION,
                        value = ConclusionStateAnswers.FINISH_CHOICE
                    )
                )

                ResultAssert.assertThat(result).isSuccess()
                assertThat(result.get().onboardingPhase).isEqualTo(CHILD_VIDEOS_REQUEST)
            }

            coVerify(exactly = 1) { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) }
            coVerify(exactly = 1) { onboardingService.changePhaseTo(child.id, CHILD_VIDEOS_REQUEST) }
        }

    @Test
    fun `#answer should change onboarding phase to HEALTH_DECLARATION_APPOINTMENT when the last step was answered and person age is under 10 and feature flag is false`() =
        runBlocking {
            val personRegistration = PersonRegistration(
                personId = child.id,
                currentStep = CONCLUSION,
                answers = listOf(
                    PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                    PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                    PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true"),
                    PersonRegistrationAnswer(MOTHERS_NAME, "Nome da Mãe")
                )
            )

            val updatedPersonRegistration = personRegistration.copy(
                answers = personRegistration.answers.plus(
                    PersonRegistrationAnswer(
                        CONCLUSION,
                        ConclusionStateAnswers.FINISH_CHOICE
                    )
                ),
                finishedAt = LocalDateTime.now()
            )

            coEvery { dataService.find(queryEq { where { this.personId.eq(child.id) and this.archived.eq(false) } }) } returns listOf(
                personRegistration.toModel()
            ).success()
            coEvery { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) } returns Result.success(
                updatedPersonRegistration.toModel()
            )
            coEvery { onboardingService.changePhaseTo(child.id, REGISTRATION.nextPhase()) } returns Result.success(
                PersonOnboarding(
                    personId = child.id,
                    currentPhase = REGISTRATION.nextPhase()
                )
            )

            coEvery { personService.updatePerson(updatedPersonRegistration) } returns Result.success(child)

            val result = personRegistrationService.answer(
                child, PersonRegistrationAnswer(
                    step = CONCLUSION,
                    value = ConclusionStateAnswers.FINISH_CHOICE
                )
            )

            ResultAssert.assertThat(result).isSuccess()
            assertThat(result.get().onboardingPhase).isEqualTo(REGISTRATION.nextPhase())

            coVerify(exactly = 1) { dataService.update(match { it.currentStep == CONCLUSION && it.hasFinished() }) }
            coVerify(exactly = 1) { onboardingService.changePhaseTo(child.id, REGISTRATION.nextPhase()) }
        }

    @Test
    fun `#findAndArchive should change person registration archived status to true `() = runBlocking {
        val personRegistration = PersonRegistration(
            personId = child.id,
            currentStep = CONCLUSION,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true"),
                PersonRegistrationAnswer(MOTHERS_NAME, "Nome da Mãe")
            )
        )

        val updatedPersonRegistration = personRegistration.archive()

        coEvery { dataService.get(any()) } returns personRegistration.toModel().success()
        coEvery { dataService.update(match { it.archived == true }) } returns Result.success(updatedPersonRegistration.toModel())

        val result = personRegistrationService.findAndArchive(personRegistration.id.toString())

        ResultAssert.assertThat(result).isSuccess()
        assertThat(result.get()).isEqualTo(updatedPersonRegistration)

        coVerify(exactly = 1) { dataService.update(match { it.archived == true }) }
    }

    @Test
    fun `#unarchive should change person registration archived status to false `() = runBlocking {
        val personRegistration = PersonRegistration(
            personId = child.id,
            currentStep = CONCLUSION,
            answers = listOf(
                PersonRegistrationAnswer(NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(NICK_NAME_CHANGE, "Bene"),
                PersonRegistrationAnswer(TERMS_ACCEPTANCE, "true"),
                PersonRegistrationAnswer(MOTHERS_NAME, "Nome da Mãe")
            ),
            archived = true,
        )

        val updatedPersonRegistration = personRegistration.copy(archived = false)

        coEvery { dataService.get(any()) } returns personRegistration.toModel().success()
        coEvery { dataService.update(match { it.archived == false }) } returns Result.success(updatedPersonRegistration.toModel())

        val result = personRegistrationService.unarchive(personRegistration.id.toString())

        ResultAssert.assertThat(result).isSuccess()
        assertThat(result.get()).isEqualTo(updatedPersonRegistration)

        coVerify(exactly = 1) { dataService.update(match { it.archived == false }) }
    }
}
