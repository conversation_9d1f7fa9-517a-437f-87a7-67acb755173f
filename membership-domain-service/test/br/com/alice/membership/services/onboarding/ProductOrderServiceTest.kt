package br.com.alice.membership.services.onboarding

import br.com.alice.bottini.client.LeadService
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ProductOrderModelDataService
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.product.client.ProductPriceListingService
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithProviders
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProductOrderServiceTest {

    private val productOrderModelDataService: ProductOrderModelDataService = mockk()
    private val leadService: LeadService = mockk()
    private val promoCodeService: PromoCodeService = mockk()
    private val productService: ProductService = mockk()
    private val productPriceListingService: ProductPriceListingService = mockk()

    private val person = TestModelFactory.buildPerson()

    private val productOrderService = ProductOrderServiceImpl(
        productOrderModelDataService,
        leadService,
        promoCodeService,
        productService,
        productPriceListingService,
    )

    @BeforeTest
    fun setup() {

        coEvery { productService.path() } returns "membership/product"
        coEvery { promoCodeService.path() } returns "membership/promo_code"
        coEvery { leadService.path() } returns "membership/lead"
    }

    @Test
    fun `#getByPersonIdWithDiscount should return ProductOrder with PromoCode as expected`() = runBlocking<Unit> {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id)
        coEvery { productOrderModelDataService.findByPerson(person.id) } returns productOrder.toModel().success()

        val promoCode = TestModelFactory.buildPromoCode()
        val lead = TestModelFactory.buildLead(nationalId = person.nationalId, promoCodeId = promoCode.id)

        coEvery { leadService.findByNationalId(person.id.toString()) } returns lead.success()
        coEvery { promoCodeService.get(lead.promoCodeId!!) } returns promoCode.success()

        val expectedProductOrder = productOrder.setPromoCode(promoCode)

        val result = productOrderService.getByPersonIdWithDiscount(person.id)

        ResultAssert.assertThat(result).isSuccessWithData(expectedProductOrder)

    }

    @Test
    fun `#getByPersonIdWithDiscount should return ProductOrder without PromoCode as expected`() = runBlocking<Unit> {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id)
        coEvery { productOrderModelDataService.findByPerson(person.id) } returns productOrder.toModel().success()

        val lead = TestModelFactory.buildLead(nationalId = person.nationalId, promoCodeId = null)
        coEvery { leadService.findByNationalId(person.id.toString()) } returns lead.success()

        val result = productOrderService.getByPersonIdWithDiscount(person.id)

        ResultAssert.assertThat(result).isSuccessWithData(productOrder)

        coVerify { promoCodeService.get(any()) wasNot called }
    }

    @Test
    fun `#findOrderWithProductAndProvidersByPersonId should return ProductOrder with product and providers`() =
        runBlocking<Unit> {
            val provider = TestModelFactory.buildProvider()
            val product = TestModelFactory.buildProduct()
            val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product = product)
            val productWithProviders = ProductWithProviders(
                product = product,
                providers = listOf(provider),
            )
            coEvery { productOrderModelDataService.findByPerson(person.id) } returns productOrder.toModel().success()

            coEvery { productService.getProductWithProviders(productOrder.productId) } returns productWithProviders.success()

            val result = productOrderService.findOrderWithProductAndProvidersByPersonId(person.id)

            ResultAssert.assertThat(result).isSuccessWithData(
                OrderWithProductWithProviders(
                    order = productOrder,
                    product = productWithProviders,
                )
            )
        }
}
