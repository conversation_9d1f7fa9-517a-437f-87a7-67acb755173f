package br.com.alice.membership.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.membership.storage.MemberDocuments
import br.com.alice.membership.storage.MemberVaultDocuments
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class DocumentServiceImplTest {

    private val memberDocuments: MemberDocuments = mockk()
    private val memberVaultDocuments: MemberVaultDocuments = mockk()
    private val service = DocumentServiceImpl(memberDocuments, memberVaultDocuments)

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @AfterTest
    fun cleanup() {
        unmockkAll()
    }
    private val idDocument = RangeUUID.generate()

    @Test
    fun `#getContract should call memberDocuments`() = runBlocking {
        val content: ByteArray = "content".toByteArray(Charsets.UTF_8)
        coEvery { memberDocuments.getContractContent(any()) } returns content

        val result = service.getContract("url")

        assertThat(result).isSuccessWithData(content)

        coVerify(exactly = 1) { memberDocuments.getContractContent("url") }
    }

    @Test
    fun `#getContract should return failure when any exception is thrown`() = runBlocking {
        coEvery { memberDocuments.getContractContent(any()) } throws Exception("")

        val result = service.getContract("url")

        assertThat(result).isFailure()

        coVerify(exactly = 1) { memberDocuments.getContractContent("url") }
    }

    @Test
    fun `#getTerm should call memberVaultDocuments`() = runBlocking {
        val content: ByteArray = "content".toByteArray(Charsets.UTF_8)
        coEvery { memberVaultDocuments.getContentById(any()) } returns content.success()

        val result = service.getTerm(idDocument)

        assertThat(result).isSuccessWithData(content)

        coVerify(exactly = 1) { memberVaultDocuments.getContentById(idDocument) }
    }

}
