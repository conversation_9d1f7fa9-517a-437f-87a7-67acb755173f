package br.com.alice.membership.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthcareTeamRecommendation
import br.com.alice.data.layer.services.PersonHealthcareTeamRecommendationModelDataService
import br.com.alice.membership.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PersonHealthcareTeamRecommendationServiceTest {

    val person = TestModelFactory.buildPerson()

    private val personHealthcareTeamRecommendationModelDataService: PersonHealthcareTeamRecommendationModelDataService = mockk()

    private val service = PersonHealthcareTeamRecommendationServiceImpl(personHealthcareTeamRecommendationModelDataService)

    private val recommendation = PersonHealthcareTeamRecommendation(personId = person.id)

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#add should return expected PersonHealthcareTeamRecommendation`() = runBlocking {
        coEvery { personHealthcareTeamRecommendationModelDataService.add(recommendation.toModel()) } returns recommendation.toModel().success()

        val result = service.add(recommendation)
        assertThat(result).isSuccessWithData(recommendation)
    }

    @Test
    fun `#saveChosenHealthcareTeam should return expected PersonHealthcareTeamRecommendation`() = runBlocking {
        val chosenHealthcareTeamId = RangeUUID.generate()
        val expected = recommendation.copy(chosenTeamId = chosenHealthcareTeamId)

        coEvery { personHealthcareTeamRecommendationModelDataService.find(any()) } returns listOf(recommendation.toModel()).success()
        coEvery { personHealthcareTeamRecommendationModelDataService.update(any()) } returns expected.toModel().success()

        val result = service.saveChosenHealthcareTeam(person.id, chosenHealthcareTeamId)
        assertThat(result).isSuccessWithData(expected)
    }
}
