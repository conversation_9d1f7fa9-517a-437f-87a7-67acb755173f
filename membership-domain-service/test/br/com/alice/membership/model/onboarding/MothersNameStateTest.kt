package br.com.alice.membership.model.onboarding

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class MothersNameStateTest {

    private val state = MothersNameState()

    @Test
    fun `#validate should return true if the answer is not empty`() {
        assertThat(state.validate("someName")).isTrue()
    }

    @Test
    fun `#validate should return false if the answer is empty`() {
        assertThat(state.validate("")).isFalse()
    }

    @Test
    fun `#validate should return false if the answer is blank`() {
        assertThat(state.validate("         ")).isFalse()
    }

    @Test
    fun `#next should go to SelfiePhotoState`() {
        assertThat(state.next(answer = "Mothers Name")).isInstanceOf(HealthDeclarationState::class.java)
    }

}
