package br.com.alice.membership.model.onboarding

import br.com.alice.common.RangeUUID
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class NameConfirmationStateTest {

    private val state = NameConfirmationState()

    @Test
    fun `#validate should return false if answer is diff from CHANGE or CONTINUE`() {
        val randomAnswer = RangeUUID.generate().toString()
        assertThat(state.validate(randomAnswer)).isFalse()
    }

    @Test
    fun `#validate should return true if answer is CHANGE or CONTINUE`() {
        assertThat(state.validate(NameConfirmationStateAnswers.CHANGE)).isTrue()
        assertThat(state.validate(NameConfirmationStateAnswers.CONTINUE)).isTrue()
    }

    @Test
    fun `#next should be NickNameChange if the answer is 'change'`() {
        assertThat(state.next(answer = NameConfirmationStateAnswers.CHANGE)).isInstanceOf(
            NickNameChangeState::class.java
        )
    }

    @Test
    fun `#next should be SexSelectionState if the answer is 'continue'`() {
        assertThat(state.next(answer = NameConfirmationStateAnswers.CONTINUE)).isInstanceOf(
            SexSelectionState::class.java
        )
    }

}
