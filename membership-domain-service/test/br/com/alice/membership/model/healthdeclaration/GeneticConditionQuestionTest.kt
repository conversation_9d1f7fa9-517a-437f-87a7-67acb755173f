package br.com.alice.membership.model.healthdeclaration

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.model.onboarding.HealthDeclarationAnswerOption
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.GeneticConditionQuestion
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.BrainQuestion
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.MalformationQuestion


import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.Test

class GeneticConditionQuestionTest {
    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(20))

    @Test
    fun `#answerOptions must have expected options when the person is older than 18`() {
        val geneticConditionQuestion = GeneticConditionQuestion(person)
        Assertions.assertThat(geneticConditionQuestion.question)
            .isEqualTo("Você tem ou já teve alguma condição **genética ou congênita** (que vem desde o nascimento)?")
        Assertions.assertThat(geneticConditionQuestion.answerOptions).hasSameElementsAs(
            listOf(
                HealthDeclarationAnswerOption(label = "Síndrome de Marfan", value = "Q87"),
                HealthDeclarationAnswerOption(label = "Síndrome de down", value = "Q90"),
                HealthDeclarationAnswerOption(label = "Síndrome de Turner", value = "Q96"),
                HealthDeclarationAnswerOption(
                    label = "Malformação congênita",
                    value = "",
                    subQuestion = MalformationQuestion(person)
                ),
                HealthDeclarationAnswerOption(
                    label = "Não, nenhuma",
                    value = "",
                    exclusive = true
                ),
            )
        )
    }

    @Test
    fun `#answerOptions must have expected options when the person is not older than 18`() {
        val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(2))
        val geneticConditionQuestion = GeneticConditionQuestion(person)

        Assertions.assertThat(geneticConditionQuestion.question)
            .isEqualTo("Seu dependente tem ou já teve alguma condição **genética ou congênita** (que vem desde o nascimento)?")
        Assertions.assertThat(geneticConditionQuestion.answerOptions).hasSameElementsAs(
            listOf(
                HealthDeclarationAnswerOption(label = "Síndrome do X Frágil", value = "Q99"),
                HealthDeclarationAnswerOption(label = "Síndrome de down", value = "Q90"),
                HealthDeclarationAnswerOption(label = "Síndrome de Turner", value = "Q96"),
                HealthDeclarationAnswerOption(
                    label = "Malformação congênita",
                    value = "",
                    subQuestion = MalformationQuestion(person)
                ),
                HealthDeclarationAnswerOption(
                    label = "Não, nenhuma",
                    value = "",
                    exclusive = true
                ),
            )
        )
    }

    @Test
    fun `#getNext should return GeneticConditionQuestion no matter the answer is`() {
        val geneticConditionQuestion = GeneticConditionQuestion(person)

        val nextQuestionB2C = geneticConditionQuestion.getNextQuestionFor(person)
        Assertions.assertThat(nextQuestionB2C).isInstanceOf(BrainQuestion::class.java)
    }
}
