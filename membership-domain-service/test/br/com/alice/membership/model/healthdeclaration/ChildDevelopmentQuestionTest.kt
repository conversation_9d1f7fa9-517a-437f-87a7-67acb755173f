package br.com.alice.membership.model.healthdeclaration

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.model.onboarding.HealthDeclarationAnswerOption
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.ChildDevelopmentQuestion
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.SurgeryQuestion
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.Test

class ChildDevelopmentQuestionTest {

    @Test
    fun `#answerOptions must have expected options`() {
        val childDevelopmentQuestion = ChildDevelopmentQuestion()
        Assertions.assertThat(childDevelopmentQuestion.question)
            .isEqualTo("Seu dependente tem algum atraso ou transtorno no **desenvolvimento**?")
        Assertions.assertThat(childDevelopmentQuestion.answerOptions).hasSameElementsAs(
            listOf(
                HealthDeclarationAnswerOption(label = "Transtorno do espectro autista (TEA)", value = "F84"),
                HealthDeclarationAnswerOption(label = "Atraso do desenvolvimento da fala", value = "F80"),
                HealthDeclarationAnswerOption(label = "Atraso do desenvolvimento motor", value = "F82"),
                HealthDeclarationAnswerOption(label = "Atraso do desenvolvimento intelectual", value = "F83"),
                HealthDeclarationAnswerOption(
                    label = "Não, nenhuma",
                    value = "",
                    exclusive = true
                ),
            )
        )
    }

    @Test
    fun `#getNext should return ChildDevelopmentQuestion no matter the answer is`() {
        val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(20))

        val childDevelopmentQuestion = ChildDevelopmentQuestion()

        val nextQuestion = childDevelopmentQuestion.getNextQuestionFor(person)
        Assertions.assertThat(nextQuestion).isInstanceOf(SurgeryQuestion::class.java)
    }
}
