package br.com.alice.membership.model.healthdeclaration

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.SurgerySubQuestion
import br.com.alice.membership.model.onboarding.HealthDeclarationEngine.ConfirmationQuestion
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.Test

class SurgerySubQuestionTest {
    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(20))

    @Test
    fun `#answerOptions must have expected options`() {
        val surgerySubQuestion = SurgerySubQuestion(person)
        Assertions.assertThat(surgerySubQuestion.question)
            .isEqualTo("Quais procedimentos cirúrgicos realizados?")
        Assertions.assertThat(surgerySubQuestion.answerOptions).hasSameElementsAs(
            emptyList()
        )
    }

    @Test
    fun `#getNext should return SurgerySubQuestion no matter the answer is`() {
        val surgerySubQuestion = SurgerySubQuestion(person)

        val nextQuestionB2C = surgerySubQuestion.getNextQuestionFor(person)
        Assertions.assertThat(nextQuestionB2C).isInstanceOf(ConfirmationQuestion::class.java)
    }
}
