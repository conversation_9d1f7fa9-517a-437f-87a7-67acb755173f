package br.com.alice.membership.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.event.ClinicalBackgroundUpdatedEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.membership.client.CptsService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ClinicalBackgroundUpdatedConsumerTest : ConsumerTest() {
    private val cptsService: CptsService = mockk()
    private val consumer = ClinicalBackgroundUpdatedConsumer(cptsService)

    @Test
    fun `#applyCpts should receive clinical background update change event and call update cpts`() = runBlocking<Unit> {
        val healthDeclaration = TestModelFactory.buildHealthDeclaration()
        val clinicalBackground = TestModelFactory.buildDiseaseClinicalBackground()
        val event = ClinicalBackgroundUpdatedEvent(clinicalBackground)

        coEvery {
            cptsService.updateCpts(event.clinicalBackground)
        } returns healthDeclaration.success()

        consumer.applyCpts(event)

        coVerify(exactly = 1) { cptsService.updateCpts(event.clinicalBackground) }
    }

    @Test
    fun `#applyCpts should receive AutoRetryableException when the applyCpts method result is a NotFound exception`() =
        runBlocking<Unit> {
            val clinicalBackground = TestModelFactory.buildDiseaseClinicalBackground()
            val event = ClinicalBackgroundUpdatedEvent(clinicalBackground)

            coEvery {
                cptsService.updateCpts(event.clinicalBackground)
            } returns NotFoundException("").failure()

            val result = consumer.applyCpts(event)

            assertThat(result).isFailureOfType(AutoRetryableException::class)

            coVerify(exactly = 1) { cptsService.updateCpts(event.clinicalBackground) }
        }
}
