package br.com.alice.membership.model.term

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.UserSignature
import br.com.alice.data.layer.models.UserSignatureModel
import java.util.UUID

data class CertificateTermData(
    val personId: PersonId,
    val memberId: UUID,
    val termId: UUID = RangeUUID.generate(),
    val signature: UserSignatureModel,
)
