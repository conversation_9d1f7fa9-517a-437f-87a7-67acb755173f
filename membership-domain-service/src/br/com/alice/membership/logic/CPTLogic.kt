package br.com.alice.membership.logic

import br.com.alice.common.core.extensions.containsAny
import br.com.alice.data.layer.models.Cpt

object CPTLogic {
    fun pregnancyLogic(conditions: List<String>): List<String> {
        // We need to change the pregnancy CID Z34 to Z35 when there is any pregnancy risk CID present
        return conditions.map { if (it == "Z34" && conditions.containsAny(listOf("O24", "O00", "O42", "O14", "O86"))) "Z35" else it }
    }
}
