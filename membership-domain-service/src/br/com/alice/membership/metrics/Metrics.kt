package br.com.alice.membership.metrics

import br.com.alice.common.observability.metrics.Metric
import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistory
import br.com.alice.data.layer.models.LeadSource
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.TrackingInfo

object Metrics {
    private const val mvIntegrationTotal = "mv_integration_total"

    fun registerMetrics() {
        Metric.registerCounter(mvIntegrationTotal, "status" to "failure")
        Metric.registerCounter(mvIntegrationTotal, "status" to "success")
    }

    fun incrementLeadTotal(source: LeadSource?, trackingInfo: TrackingInfo?) =
        Metric.increment("lead_total",
            "source" to (source?.toString()?.lowercase() ?: ""),
            "utm_source" to (trackingInfo?.utmSource ?: "")
        )

    fun incrementMvIntegrationTotal(success: Boolean) =
        Metric.increment(mvIntegrationTotal, "status" to if (success) "success" else "failure")

    fun healthDeclarationFinished(healthDeclaration: HealthDeclaration) =
        Metric.increment("health_declaration_total",
            "cpts" to healthDeclaration.cpts.count().toString(),
            "surgeries" to healthDeclaration.surgeries.count().toString()
        )

    fun onboardingContractGenerated(contract: OnboardingContract) =
        Metric.increment("onboarding_contract_total",
            "product_id" to contract.productId.toString(),
        )

    fun incrementTaskStatusChangeHistoryRequested(taskChange: HealthPlanTaskStatusHistory) =
        Metric.increment("task_status_change_requested",
            "blame_user_type" to taskChange.blameUserType.toString()
        )
    fun incrementTaskStatusChangeHistoryRegistered(taskChange: HealthPlanTaskStatusHistory) =
        Metric.increment("task_status_change_registered",
            "blame_user_type" to taskChange.blameUserType.toString()
        )
    fun incrementTaskStatusChangeHistoryFailed(taskChange: HealthPlanTaskStatusHistory) =
        Metric.increment("task_status_change_failed",
            "blame_user_type" to taskChange.blameUserType.toString()
        )
}
