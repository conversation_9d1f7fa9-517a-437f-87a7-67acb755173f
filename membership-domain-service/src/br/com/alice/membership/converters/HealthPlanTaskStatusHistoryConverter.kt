package br.com.alice.membership.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistory
import br.com.alice.data.layer.models.HealthPlanTaskStatusHistoryModel

object HealthPlanTaskStatusHistoryConverter : Converter<HealthPlanTaskStatusHistoryModel, HealthPlanTaskStatusHistory>(
    HealthPlanTaskStatusHistoryModel::class,
    HealthPlanTaskStatusHistory::class,
)

fun HealthPlanTaskStatusHistory.toModel() = HealthPlanTaskStatusHistoryConverter.unconvert(this)
fun HealthPlanTaskStatusHistoryModel.toTransport() = HealthPlanTaskStatusHistoryConverter.convert(this)
