package br.com.alice.membership.route

import br.com.alice.bottini.events.PersonCreatedInLeadCentralEvent
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.BeneficiaryTermAcceptedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.ehr.event.ClinicalBackgroundUpdatedEvent
import br.com.alice.healthcondition.event.CaseRecordCreatedEvent
import br.com.alice.healthplan.events.HealthPlanTaskOverdueEvent
import br.com.alice.membership.consumers.*
import br.com.alice.membership.events.MemberProductChangeRequestApplyEvent
import br.com.alice.membership.model.events.AccessCodeCreatedEvent
import br.com.alice.membership.model.events.ContractPhaseStartedEvent
import br.com.alice.membership.model.events.InviteRequestedEvent
import br.com.alice.membership.model.events.InviteSentEvent
import br.com.alice.membership.model.events.MemberAccreditedNetworkTrackerEvent
import br.com.alice.membership.model.events.PersonAgeDefinedEvent
import br.com.alice.membership.model.events.PersonBenefitUpdatedEvent
import br.com.alice.membership.model.events.PersonRegistrationFinishedEvent
import br.com.alice.membership.model.events.ShoppingFinishedEvent
import br.com.alice.membership.model.events.SignedContractEvent
import br.com.alice.membership.model.events.UpdatedPersonContactInfoTempRequestAcceptedEvent
import br.com.alice.membership.model.events.UpdatedPersonContactInfoTempRequestApproveEvent
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import br.com.alice.moneyin.event.InvoicePaidEvent
import br.com.alice.moneyin.event.InvoiceSentEvent
import br.com.alice.onboarding.model.events.InsurancePortabilityRequestApprovedEvent
import br.com.alice.onboarding.model.events.PersonOnboardingArchivedEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCompletedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent
import java.time.Duration

fun ConsumerJob.Configuration.kafkaRoutes() {
    val clinicalBackgroundUpdatedConsumer by inject<ClinicalBackgroundUpdatedConsumer>()
    consume("apply-cpts", ClinicalBackgroundUpdatedEvent.name, clinicalBackgroundUpdatedConsumer::applyCpts)

    val caseRecordConsumer by inject<CaseRecordConsumer>()
    consume("apply-cpts-from-case", CaseRecordCreatedEvent.name, caseRecordConsumer::applyCpts)

    val healthDeclarationConsumer by inject<HealthDeclarationConsumer>()
    consume("remove-cpts", InsurancePortabilityRequestApprovedEvent.name, healthDeclarationConsumer::removeCpts)

    val contractConsumer by inject<ContractConsumer>()
    consume("send-push", ContractPhaseStartedEvent.name, contractConsumer::sendPush)
    consume("calculate-contract-sla", ContractPhaseStartedEvent.name, contractConsumer::calculateContractSla)
    consume("generate-contract", PersonUpdatedEvent.name, contractConsumer::generateContract)

    val acquisitionAnalyticsConsumer by inject<AcquisitionAnalyticsConsumer>()
    consume(
        "user-authenticated-send-to-crm",
        UserAuthenticatedEvent.name,
        acquisitionAnalyticsConsumer::sendUserAuthenticatedEvent
    )
    consume(
        "send-contract-received-event",
        ContractPhaseStartedEvent.name,
        acquisitionAnalyticsConsumer::sendContractReceivedEvent
    )
    consume(
        "contract-signed-send-to-crm",
        SignedContractEvent.name,
        acquisitionAnalyticsConsumer::sendContractSignedEvent
    )
    consume(
        "send-registration-finished-event",
        PersonRegistrationFinishedEvent.name,
        acquisitionAnalyticsConsumer::sendRegistrationFinishedEvent
    )
    consume(
        "send-person-age-notification",
        PersonAgeDefinedEvent.name,
        acquisitionAnalyticsConsumer::sendPersonAgeDefinedEvent
    )
    consume(
        "send-invite-requested-event",
        InviteRequestedEvent.name,
        acquisitionAnalyticsConsumer::sendInviteRequestedEvent
    )
    consume("send-invite-sent-event", InviteSentEvent.name, acquisitionAnalyticsConsumer::sendInviteSentEvent)
    consume("update-user-profile-event", PersonUpdatedEvent.name, acquisitionAnalyticsConsumer::updateUserProfile)
    consume(
        "send-shopping-finished-event",
        ShoppingFinishedEvent.name,
        acquisitionAnalyticsConsumer::sendShoppingFinishedEvent
    )
    consume("send-invoice-sent-event", InvoiceSentEvent.name, acquisitionAnalyticsConsumer::sendInvoiceSentEvent)
    consume("send-invoice-paid-event", InvoicePaidEvent.name, acquisitionAnalyticsConsumer::sendInvoicePaidEvent)
    consume(
        "send-member-created-event",
        MemberCreatedEvent.name,
        acquisitionAnalyticsConsumer::sendMemberCreatedEvent,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1))
    )
    consume(
        "send-member-activated-event",
        MemberActivatedEvent.name,
        acquisitionAnalyticsConsumer::sendMemberActivatedEvent
    )
    consume(
        "beneficiary-onboarding-phase-changed-send-event",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        acquisitionAnalyticsConsumer::sendBeneficiaryOnboardingPhaseChangedEvent
    )

    val appointmentScheduleEventsConsumer by inject<AppointmentScheduleEventsConsumer>()
    consume(
        "appointment-schedule-created-send-push",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleEventsConsumer::sendPush
    )
    consume(
        "appointment-schedule-created-update-person-onboarding",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleEventsConsumer::updatePersonOnboarding
    )
    consume(
        "appointment-schedule-completed-update-onboarding-phase-to-appointment-completed",
        AppointmentScheduleCompletedEvent.name,
        appointmentScheduleEventsConsumer::updateOnboardingPhaseToAppointmentCompleted
    )

    val appointmentScheduleAnalyticsConsumer by inject<AppointmentScheduleAnalyticsConsumer>()
    consume(
        "send-appointment-schedule-cancelled",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleAnalyticsConsumer::sendAppointmentScheduleCancelledEvent
    )
    consume(
        "send-appointment-schedule-no-show",
        AppointmentScheduleNoShowEvent.name,
        appointmentScheduleAnalyticsConsumer::sendAppointmentScheduleNoShowEvent
    )
    consume(
        "send-appointment-schedule-created",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleAnalyticsConsumer::sendAppointmentScheduleCreatedEvent
    )

    val contractSignedConsumer by inject<ContractSignedConsumer>()
    consume(
        "contract-signed-generate-certificate",
        SignedContractEvent.name,
        contractSignedConsumer::generateAndSendCertificate
    )
    consume(
        "contract-signed-create-member",
        SignedContractEvent.name,
        contractSignedConsumer::createMemberAndItsFirstPayment
    )

    val memberCancelledConsumer by inject<MemberCancelledConsumer>()
    consume("deactivate-mgm", MemberCancelledEvent.name, memberCancelledConsumer::deactivateMGM)
    consume("archive-logins", MemberCancelledEvent.name, memberCancelledConsumer::archiveLogins)
    consume("cancel-product-change", MemberCancelledEvent.name, memberCancelledConsumer::cancelMemberProductChange)

    val onboardingArchivedConsumer by inject<OnboardingArchivedConsumer>()
    consume("onboarding-archive-order", PersonOnboardingArchivedEvent.name, onboardingArchivedConsumer::archiveOrder)
    consume(
        "onboarding-archive-person-registration",
        PersonOnboardingArchivedEvent.name,
        onboardingArchivedConsumer::resetPersonRegistration
    )
    consume(
        "onboarding-archive-onboarding-contract",
        PersonOnboardingArchivedEvent.name,
        onboardingArchivedConsumer::archiveOnboardingContract
    )
    consume(
        "onboarding-archive-health-declaration",
        PersonOnboardingArchivedEvent.name,
        onboardingArchivedConsumer::archiveHealthDeclaration
    )

    val newRegistrationConsumer by inject<NewRegistrationConsumer>()
    consume(
        "start-background-check",
        PersonRegistrationFinishedEvent.name,
        newRegistrationConsumer::startBackgroundCheck
    )

    val invoiceConsumer by inject<InvoiceConsumer>()
    consume("finish-onboarding", InvoicePaidEvent.name, invoiceConsumer::finishOnboarding)

    val accessCodeCreatedConsumer by inject<AccessCodeCreatedConsumer>()
    consume("access-code-created-send-sms", AccessCodeCreatedEvent.name, accessCodeCreatedConsumer::sendSms)
    consume("access-code-created-send-email", AccessCodeCreatedEvent.name, accessCodeCreatedConsumer::sendEmail)

    val personCreatedInLeadCentralConsumer by inject<PersonCreatedInLeadCentralConsumer>()
    consume(
        "person-created-send-email",
        PersonCreatedInLeadCentralEvent.name,
        personCreatedInLeadCentralConsumer::sendDownloadEmail
    )

    val engagementAnalyticsConsumer by inject<EngagementAnalyticsConsumer>()
    consume(
        "send-health-plan-task-overdue",
        HealthPlanTaskOverdueEvent.name,
        engagementAnalyticsConsumer::sendHealthPlanTaskOverdueEvent
    )

    val personBenefitUpdatedConsumer by inject<PersonBenefitUpdatedConsumer>()
    consume(
        "change-person-benefit-opt-in-status",
        PersonBenefitUpdatedEvent.name,
        personBenefitUpdatedConsumer::changePersonBenefitOptInStatus
    )

    val beneficiaryTermAcceptedConsumer by inject<BeneficiaryTermAcceptedConsumer>()
    consume(
        "certify-data-processing-term",
        BeneficiaryTermAcceptedEvent.name,
        beneficiaryTermAcceptedConsumer::certifyDataProcessingTerm
    )

    val updatedPersonContactInfoTempConsumer by inject<UpdatedPersonContactInfoTempConsumer>()
    consume(
        "updated-person-contact-info-approve-request-submitted",
        UpdatedPersonContactInfoTempRequestApproveEvent.name,
        updatedPersonContactInfoTempConsumer::sendSmsAndEmail
    )

    consume(
        "updated-person-contact-info-approve-request-accepted",
        UpdatedPersonContactInfoTempRequestAcceptedEvent.name,
        updatedPersonContactInfoTempConsumer::sendAcceptedSmsAndEmail
    )

    val memberAccreditedNetworkTrackerConsumer by inject<MemberAccreditedNetworkTrackerConsumer>()
    consume(
        "register-member-accredited-network-tracker",
        MemberAccreditedNetworkTrackerEvent.name,
        memberAccreditedNetworkTrackerConsumer::registerMemberAccreditedNetwork
    )


    val memberProductChangeRequestConsumer by inject<MemberProductChangeRequestConsumer>()
    consume(
        "apply-member-product-change-request-consumer",
        MemberProductChangeRequestApplyEvent.name,
        memberProductChangeRequestConsumer::applyChangeRequest
    )
}
