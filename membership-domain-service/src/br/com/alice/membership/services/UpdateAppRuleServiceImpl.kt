package br.com.alice.membership.services

import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.UpdateAppRule
import br.com.alice.data.layer.services.UpdateAppRuleModelDataService
import br.com.alice.membership.client.UpdateAppRuleService
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.converters.toTransport
import com.github.kittinunf.result.map
import java.util.UUID

class UpdateAppRuleServiceImpl(
    private val data: UpdateAppRuleModelDataService
) : UpdateAppRuleService {

    override suspend fun getAll() =
        data.find {
            where { this.active.eq(true) }.orderBy { createdAt }.sortOrder { desc }
        }.mapEach { it.toTransport() }

    override suspend fun get(id: UUID) =
        data.get(id).map { it.toTransport() }

    override suspend fun add(request: UpdateAppRule) =
        data.add(request.toModel()).map { it.toTransport() }

    override suspend fun update(request: UpdateAppRule) =
        data.update(request.toModel()).map { it.toTransport() }

    override suspend fun getByDescription(description: String, range: IntRange) =
        data.find {
            where { this.description.like(description) and this.active.eq(true) }.offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun findByRange(range: IntRange) =
        data.find {
            where { this.active.eq(true) }.offset { range.first }.limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun countByDescription(description: String) =
        data.count {
            where { this.description.like(description) and this.active.eq(true) }
        }

    override suspend fun countAll() =
        data.count { all() }
}
