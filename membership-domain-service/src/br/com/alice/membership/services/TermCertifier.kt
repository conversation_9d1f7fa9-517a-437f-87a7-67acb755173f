package br.com.alice.membership.services

import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.document.pdf.PDFConverter
import br.com.alice.common.document.pdf.PdfUtil
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.MemberContractTerm
import br.com.alice.data.layer.models.MemberContractTermModel
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.TermType
import br.com.alice.data.layer.models.UserSignature
import br.com.alice.data.layer.models.UserSignatureModel
import br.com.alice.data.layer.services.MemberContractTermModelDataService
import br.com.alice.membership.converters.toTransport
import br.com.alice.membership.model.events.MemberContractTermSignedEvent
import br.com.alice.membership.model.term.CertificateTermData
import br.com.alice.membership.storage.MEMBER_TERM_SCOPED
import br.com.alice.membership.storage.MemberVaultDocuments
import br.com.alice.membership.storage.ResourceLoader
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class TermCertifier(
    private val dataService: MemberContractTermModelDataService,
    private val memberDocuments: MemberVaultDocuments,
    private val kafkaProducerService: KafkaProducerService,
) {

    private val certificateTerm = ResourceLoader
        .load("terms/certificate_term.html")
        .readText()

    private val termDocument = ResourceLoader
        .load("terms/Termo_de_Compartilhamento_B2B.pdf").readBytes()

    fun createCertificate(
        termId: UUID,
        person: Person,
        signature: UserSignatureModel,
        template: String = certificateTerm,
    ): Result<String, Throwable> =
        template
            .replace("{{TERM_ID}}", termId.toString())
            .replace("{{SIGNED_AT}}", signature.signedAt.toBrazilianDateTimeFormat())
            .replace("{{SIGNATURE_ID}}", signature.id.toString())
            .replace("{{SIGNATURE_TOKEN}}", signature.idToken)
            .replace("{{PERSON_FULL_NAME}}", person.fullSocialName)
            .replace("{{PERSON_NATIONAL_ID}}", person.nationalId)
            .replace("{{SIGNATURE_IP_ADDRESS}}", signature.ipAddress ?: "")
            .success()

    suspend fun certify(
        certificate: String,
        certificateData: CertificateTermData
    ): Result<MemberContractTermModel, Throwable> =
        buildTermWithCertificate(certificate).flatMap { documentWithCertificate ->
            memberDocuments.uploadPdf(certificateData.personId, MEMBER_TERM_SCOPED, documentWithCertificate)
        }.map { file ->
            MemberContractTermModel(
                id = certificateData.termId,
                memberId = certificateData.memberId,
                signature = certificateData.signature,
                termType = TermType.DATA_PROCESSING_TERMS,
                signedDocumentFileId = file.id
            )
        }.flatMap { memberTerm ->
            dataService.add(memberTerm)
        }.then {
            logger.info(
                "producing member contract term signed event",
                "member" to it.memberId,
                "term_type" to it.termType,
            )
            kafkaProducerService.produce(MemberContractTermSignedEvent(it.toTransport()))
        }

    private fun buildTermWithCertificate(certificate: String): Result<ByteArray, Throwable> {
        val certificateDocument = PDFConverter.htmlToPDF(certificate)
        return PdfUtil.merge(termDocument, certificateDocument).success()
    }
}
