package br.com.alice.membership.services.onboarding

import br.com.alice.bottini.client.LeadService
import br.com.alice.common.asyncLayer
import br.com.alice.common.core.PersonId
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ProductOrder
import br.com.alice.data.layer.models.PromoCode
import br.com.alice.data.layer.models.withPriceListing
import br.com.alice.data.layer.services.ProductOrderModelDataService
import br.com.alice.membership.client.PromoCodeService
import br.com.alice.membership.client.onboarding.ProductOrderService
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.converters.toTransport
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.product.client.ProductPriceListingService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class ProductOrderServiceImpl(
    private val productOrderModelDataService: ProductOrderModelDataService,
    private val leadService: LeadService,
    private val promoCodeService: PromoCodeService,
    private val productService: ProductService,
    private val productPriceListingService: ProductPriceListingService,
) : ProductOrderService {

    private suspend fun withFindOptions(
        productOrder: ProductOrder,
        findOptions: ProductOrderService.FindOptions
    ): ProductOrder =
        if (findOptions.withPriceListing)
            withPriceListing(productOrder).get()
        else productOrder

    override suspend fun findByIds(ids: List<UUID>): Result<List<ProductOrder>, Throwable> =
        productOrderModelDataService.findByIds(ids).mapEach { it.toTransport() }

    override suspend fun updateInBatch(productOrders: List<ProductOrder>) = asyncLayer {
        productOrders.chunked(50).pmap { productOrdersChunked ->
            logger.info(
                "ProductOrderServiceImpl:updateList Updating ProductOrder list in chunks",
                "chunk_size" to productOrdersChunked.size
            )
            productOrderModelDataService.updateList(productOrdersChunked.map { it.toModel() })
        }.lift().map { it.flatten().map { it.toTransport() } }
    }

    override suspend fun create(productOrder: ProductOrder): Result<ProductOrder, Throwable> =
        productOrderModelDataService.add(productOrder.toModel()).map { it.toTransport() }

    override suspend fun update(productOrder: ProductOrder): Result<ProductOrder, Throwable> =
        productOrderModelDataService.update(productOrder.toModel()).map { it.toTransport() }

    override suspend fun getByPersonId(personId: PersonId, findOptions: ProductOrderService.FindOptions) =
        productOrderModelDataService.findByPerson(personId)
            .map { withFindOptions(it.toTransport(), findOptions) }

    override suspend fun withPriceListing(productOrder: ProductOrder) =
        if (productOrder.productPriceListingId == null) {
            productOrder.success()
        } else {
            productPriceListingService.getPriceListing(productOrder.productPriceListingId!!)
                .map { productOrder.withPriceListing(it) }
        }

    override suspend fun getByPersonIdWithDiscount(personId: PersonId): Result<ProductOrder, Throwable> =
        coroutineScope {
            val productOrderDeferred = async { getByPersonId(personId).get() }
            val promoCodeDeferred = async { getPromoCode(personId) }

            val productOrder = productOrderDeferred.await()
            val promoCode = promoCodeDeferred.await()

            val productOrderWithPromoCode = productOrder.setPromoCode(promoCode)
            productOrderWithPromoCode.success()
        }

    override suspend fun findOrderWithProductAndProvidersByPersonId(
        personId: PersonId,
        findOptions: ProductOrderService.FindOptions
    ) =
        getByPersonId(personId, findOptions)
            .flatMapPair {
                productService.getProductWithProviders(it.selectedProduct.id)
            }
            .map { OrderWithProductWithProviders(it.second, it.first) }

    override suspend fun findByProductId(
        productId: String,
        dateRange: Pair<LocalDateTime, LocalDateTime>,
        range: IntRange
    ) =
        productOrderModelDataService.findByProductId(productId, dateRange, range).mapEach { it.toTransport() }

    override suspend fun unarchive(id: UUID): Result<ProductOrder, Throwable> =
        productOrderModelDataService.get(id)
            .flatMap { productOrderModelDataService.update(it.copy(archived = false)) }
            .map { it.toTransport() }

    private suspend fun getPromoCode(personId: PersonId): PromoCode? {
        val lead = leadService.findByNationalId(personId.toString()).getOrNullIfNotFound()
        return lead?.promoCodeId?.let { promoCodeService.get(it).get() }
    }
}
