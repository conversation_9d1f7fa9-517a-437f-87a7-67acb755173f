package br.com.alice.membership.consumers

import br.com.alice.common.logging.logger
import br.com.alice.membership.client.MemberProductChangeScheduleService
import br.com.alice.membership.events.MemberProductChangeRequestApplyEvent
import com.github.kittinunf.result.Result

class MemberProductChangeRequestConsumer(
    private val memberProductChangeScheduleService: MemberProductChangeScheduleService
): Consumer() {

    suspend fun applyChangeRequest(event: MemberProductChangeRequestApplyEvent): Result<Any, Throwable> {
        logger.info(
            "Applied member product change",
            "event_id" to event.messageId
        )

        val request = event.payload.memberProductChangeSchedule

        return withAuthenticatedSubscribersEnvironment(request.personId) {
            memberProductChangeScheduleService.apply(request.id)
        }
    }
}
