package br.com.alice.membership.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toQuarterHour
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PathToNavigate
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.ehr.event.AppointmentScheduleCompletedEvent
import br.com.alice.membership.client.DeviceService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class AppointmentScheduleEventsConsumer(
    private val deviceService: DeviceService,
    private val pushService: PushService,
    private val onboardingService: OnboardingService,
) : Consumer() {

    suspend fun sendPush(event: AppointmentScheduleCreatedEvent): Result<Any, Throwable> {
        logger.info(
            "Process appointment schedule created send push event on consumer",
            "event_id" to event.messageId
        )

        val personId = event.payload.appointmentSchedule.personId

        return withAuthenticatedSubscribersEnvironment(personId) {
            deviceService.getDeviceByPerson(personId.toString()).flatMap { device ->
                val (title, message) = getMessage(event.payload.appointmentSchedule.type)
                val data = mapOf(PathToNavigate.SCHEDULED_APPOINTMENTS.value)
                val push = FirebasePush(deviceToken = device.deviceId, title = title, body = message, data = data)

                pushService.send(push)
                    .flatMapError {
                        logger.info("Catch error", "ex" to it)
                        true.success()
                    }
            }.coFoldNotFound {
                logger.warn(
                    "AppointmentScheduleEventsConsumer::sendPush device not found, ignoring push",
                    "event_id" to event.messageId
                )
                false.success()
            }
        }
    }

    suspend fun updatePersonOnboarding(event: AppointmentScheduleCreatedEvent): Result<Any, Throwable> =
        withAuthenticatedSubscribersEnvironment(event.payload.appointmentSchedule.personId) {
            logger.info(
                "Process appointment schedule created update person onboarding event on consumer",
                "event_id" to event.messageId
            )

            logger.info("AppointmentScheduleCreatedEvent received", "event_id" to event.messageId)
            val appointmentSchedule = event.payload.appointmentSchedule

            if (appointmentSchedule.type == AppointmentScheduleType.HEALTH_DECLARATION) {
                logger.info(
                    "Updating person onboarding",
                    "appointment_schedule" to appointmentSchedule,
                    "person" to event.payload.person
                )
                onboardingService.findByPerson(appointmentSchedule.personId)
                    .map {
                        it.copy(
                            healthDeclarationAppointmentScheduledAt = appointmentSchedule.createdAt,
                            healthDeclarationAppointmentDate = appointmentSchedule.startTime.toQuarterHour()
                        )
                    }
                    .flatMap { onboardingService.update(it) }
            } else {
                return@withAuthenticatedSubscribersEnvironment true.success()
            }
        }

    private fun getMessage(type: AppointmentScheduleType) = if (type == AppointmentScheduleType.IMMERSION) {
        Pair(
            "Imersão agendada com sucesso.",
            "Parabéns, você deu um importante passo para conquistar sua melhor saúde. Clique para gerenciar seu agendamento."
        )
    } else {
        Pair(
            "Consulta agendada com sucesso",
            "Veja mais detalhes aqui."
        )
    }

    suspend fun updateOnboardingPhaseToAppointmentCompleted(event: AppointmentScheduleCompletedEvent) =
        withAuthenticatedSubscribersEnvironment(event.payload.appointmentSchedule.personId) {
            logger.info("AppointmentScheduleCompletedEvent received", "event_id" to event.messageId)
            val appointmentSchedule = event.payload.appointmentSchedule

            if (appointmentSchedule.type != AppointmentScheduleType.HEALTH_DECLARATION)
                return@withAuthenticatedSubscribersEnvironment true.success()

            onboardingService
                .findByPerson(appointmentSchedule.personId)
                .flatMap { personOnboarding -> isOnboardingValid(personOnboarding) }
                .map {
                    it.copy(
                        slaHealthDeclarationAppointmentCompleted = appointmentSchedule.completedInMinutes(event.eventDate),
                        healthDeclarationAppointmentCompletedAt = event.eventDate
                    )
                }
                .flatMap { onboardingService.update(it) }
                .flatMap { moveOnboardingToNewPhase(appointmentSchedule.personId) }
                .fold(
                    { true.success() },
                    { exception -> if (exception is NotFoundException) true.success() else exception.failure() }
                )
        }

    private suspend fun moveOnboardingToNewPhase(personId: PersonId) = onboardingService.changePhaseTo(
        personId = personId,
        newPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT.nextPhase()
    )

    private fun isOnboardingValid(personOnboarding: PersonOnboarding): Result<PersonOnboarding, Throwable> {
        return if (personOnboarding.currentPhase.isHealthDeclaration) personOnboarding.success()
        else NotFoundException("person_onboarding_not_found").failure()
    }
}
