package br.com.alice.common.sqs.internals

import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.sqs.builders.ConsumerBuilder
import br.com.alice.common.sqs.interfaces.Queue
import br.com.alice.common.sqs.interfaces.QueueConsumeConfig
import br.com.alice.common.sqs.interfaces.SQSNotificationEvent
import com.amazonaws.services.sqs.AmazonSQS
import com.amazonaws.services.sqs.model.Message
import com.amazonaws.services.sqs.model.ReceiveMessageRequest
import com.amazonaws.services.sqs.model.ReceiveMessageResult
import com.github.kittinunf.result.Result
import io.mockk.Ordering
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeAll
import kotlin.test.BeforeTest
import kotlin.test.Test

class ConsumerTest : MockedTestHelper() {

    companion object {
        @BeforeAll
        @JvmStatic
        fun classSetup() {
            mockkObject(ConsumerBuilder)
        }
    }

    private val queue = Queue(
        url = "url",
        handlerName = "handlerName",
        handler = { Result.success(Unit) },
        kClass = SQSNotificationEvent::class,
        bodyType = Any::class,
        queueConsumeConfig = QueueConsumeConfig()
    )

    private val message: Message = mockk()
    private val recordHandler: RecordHandler = mockk()
    private val awsConsumer: AmazonSQS = mockk()
    private val receiveMessageResult: ReceiveMessageResult = mockk()
    private val receiveMessageRequest = ConsumerBuilder.buildReceiveMessageRequest(
        queue.url, queue.queueConsumeConfig)

    private val priorityMessage: Message = mockk()
    private val receiveMessagePriorityResult: ReceiveMessageResult = mockk()
    private val receiveMessagePriorityRequest = ConsumerBuilder.buildReceiveMessageRequest(
        queue.url + "-priority", queue.queueConsumeConfig)


    @BeforeTest
    override fun setup() {
        super.setup()
        every { ConsumerBuilder.buildConsumer() } returns awsConsumer
    }

    @Test
    fun `should not call handleRecord if no messages`() = runBlocking {
        coEvery { recordHandler.handleRecord(message, queue) } returns Unit
        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns emptyList()

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerifyNone { recordHandler.handleRecord(any(), any()) }
    }

    @Test
    fun `should call handleRecord for returned messages`() = runBlocking {
        coEvery { recordHandler.handleRecord(message, queue) } returns Unit
        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message)

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(exactly = 2) { recordHandler.handleRecord(any(), any()) }
    }

    @Test
    fun `should call handleRecord for all returned messages even if handles fail`() = runBlocking {
        coEvery { recordHandler.handleRecord(message, queue) } throws RuntimeException("OH NOES")
        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message)

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(exactly = 2) { recordHandler.handleRecord(any(), any()) }
    }

    @Test
    fun `should stop polling if receiveMessage fails`() = runBlocking {
        every { awsConsumer.receiveMessage(receiveMessageRequest) } throws RuntimeException("OH NOES")

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerifyOnce { awsConsumer.receiveMessage(receiveMessageRequest) }
    }

    @Test
    fun `should poll for messages from priority queue first if configured`() = runBlocking {
        val queue = queue.copy(queueConsumeConfig = QueueConsumeConfig(exclusive = true, priorityAttempts = 1))
        coEvery { recordHandler.handleRecord(message, queue, any()) } returns Unit

        every { awsConsumer.receiveMessage(receiveMessagePriorityRequest) } returns receiveMessagePriorityResult
        every { receiveMessagePriorityResult.messages } returns listOf(priorityMessage, priorityMessage)

        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message)

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(ordering = Ordering.ORDERED) {
            recordHandler.handleRecord(priorityMessage, queue, true)
            recordHandler.handleRecord(priorityMessage, queue, true)
            recordHandler.handleRecord(message, queue)
            recordHandler.handleRecord(message, queue)
        }
        coVerifyOnce { awsConsumer.receiveMessage(receiveMessagePriorityRequest) }
        coVerifyOnce { awsConsumer.receiveMessage(receiveMessageRequest) }
    }

    @Test
    fun `should skip remaining priority queue polls if first poll brings no messages`() = runBlocking {
        val queue = queue.copy(queueConsumeConfig = QueueConsumeConfig(exclusive = true, priorityAttempts = 3))
        coEvery { recordHandler.handleRecord(message, queue) } returns Unit

        every { awsConsumer.receiveMessage(receiveMessagePriorityRequest) } returns receiveMessagePriorityResult
        every { receiveMessagePriorityResult.messages } returns listOf()

        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message)

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(ordering = Ordering.ORDERED) {
            recordHandler.handleRecord(message, queue)
            recordHandler.handleRecord(message, queue)
        }
        coVerifyOnce { awsConsumer.receiveMessage(receiveMessagePriorityRequest) }
        coVerifyOnce { awsConsumer.receiveMessage(receiveMessageRequest) }
    }

    @Test
    fun `should buffer messages for parallel execution`() = runBlocking {
        val queue = queue.copy(queueConsumeConfig = QueueConsumeConfig(exclusive = true, priorityAttempts = 3, maxItems = 30))
        coEvery { recordHandler.handleRecord(message, queue, any()) } returns Unit

        every { awsConsumer.receiveMessage(receiveMessagePriorityRequest) } returns receiveMessagePriorityResult
        every { receiveMessagePriorityResult.messages } returns listOf(priorityMessage, priorityMessage)

        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message)

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(ordering = Ordering.ORDERED) {
            repeat(18) {
                recordHandler.handleRecord(priorityMessage, queue, true)
            }
            repeat(6) {
                recordHandler.handleRecord(message, queue)
            }
        }
        coVerify(exactly = 9) { awsConsumer.receiveMessage(receiveMessagePriorityRequest) }
        coVerify(exactly = 3) { awsConsumer.receiveMessage(receiveMessageRequest) }
    }

    @Test
    fun `should process buffered messages so far if poll stops returning`() = runBlocking {
        val queue = queue.copy(queueConsumeConfig = QueueConsumeConfig(exclusive = true, priorityAttempts = 3, maxItems = 30))
        coEvery { recordHandler.handleRecord(message, queue, any()) } returns Unit

        every { awsConsumer.receiveMessage(receiveMessagePriorityRequest) } returns receiveMessagePriorityResult
        every { receiveMessagePriorityResult.messages } returns listOf(priorityMessage, priorityMessage) andThen listOf()

        every { awsConsumer.receiveMessage(receiveMessageRequest) } returns receiveMessageResult
        every { receiveMessageResult.messages } returns listOf(message, message) andThen listOf()

        val consumer = Consumer("serviceName", queue, recordHandler)

        consumer.start()
        delay(500)
        consumer.stop()

        coVerify(ordering = Ordering.ORDERED) {
            repeat(2) {
                recordHandler.handleRecord(priorityMessage, queue, true)
            }
            repeat(2) {
                recordHandler.handleRecord(message, queue)
            }
        }
        coVerify(exactly = 3) { awsConsumer.receiveMessage(receiveMessagePriorityRequest) }
        coVerify(exactly = 2) { awsConsumer.receiveMessage(receiveMessageRequest) }
    }
}
