package br.com.alice.sherlock.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.SHERLOCK_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.sherlock.controllers.JobsController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.jobsRoutes() {

    val jobsController by inject<JobsController>()

    route("/jobs") {
        get("/export_person_hashes") {
            asyncLayer {
                withRootServicePolicy(SHERLOCK_DOMAIN_ROOT_SERVICE_NAME) {
                    withUnauthenticatedTokenWithKey(SHERLOCK_DOMAIN_ROOT_SERVICE_NAME) {
                        co<PERSON><PERSON><PERSON>(jobsController::exportPersonHashes)
                    }
                }
            }
        }
    }
}
