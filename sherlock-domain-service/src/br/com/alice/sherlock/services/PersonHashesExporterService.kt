package br.com.alice.sherlock.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.EntityType
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.models.Person
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.GenericVaultUploadByteArray
import br.com.alice.person.client.PersonService
import br.com.alice.sherlock.client.PersonHashTokenService
import com.github.kittinunf.result.Result
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedWriter
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class PersonHashesExporterService(
    private val personService: PersonService,
    private val fileVaultActionService: FileVaultActionService,
    private val personHashTokenService: PersonHashTokenService
) {
    companion object {
        val staffId = RangeUUID.generate() //doesn't really matter, we're not accessing this through the system
    }

    suspend fun exportPersonHashes(): File {
        val personTotal = personService.countAll().get()
        logger.info("Starting export person hashes process for $personTotal people")

        val file = generateCSV(personTotal)
        logger.info("Export person hashes process complete!")

        val staffFileVault = uploadToFileVault(file).get()
        logger.info("File uploaded successfully!", "url" to staffFileVault.url)

        val securedStaffLink = fileVaultActionService.securedGenericLink(staffFileVault.id).get()
        logger.info("Secured link created!", "url" to securedStaffLink.url)

        return file
    }

    private suspend fun uploadToFileVault(file: File): Result<GenericFileVault, Throwable> {
        val content = file.readBytes()
        val staffVaultUploadByteArray = GenericVaultUploadByteArray(
            domain = "sherlock",
            namespace = "export_person_hashes",
            originalFileName = file.name,
            entityId = staffId,
            entityType = EntityType.STAFF,
            fileContent = content,
            fileType = FileType.CSV,
            fileSize = content.size.toLong()
        )
        return fileVaultActionService.uploadGenericFile(staffVaultUploadByteArray)
    }

    private suspend fun generateCSV(personTotal: Int): File {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        val file = File("export_person_hashes-$timestamp.csv")
        val writer = file.bufferedWriter()
        val stepSize = 100

        if (personTotal > 0) {
            for (offset in 0..personTotal step stepSize) {
                writeFileChunk(offset, stepSize, writer)
            }
        }

        withContext(Dispatchers.IO) {
            writer.close()
        }

        return file
    }

    private suspend fun writeFileChunk(offset: Int, stepSize: Int, writer: BufferedWriter) {
        var personList: List<Person>? = null
        for (i in 1..5) { //account for possible failed calls
            personService.findPaginated(offset, stepSize).fold({
                personList = it
            }, {
                logger.error("Failure in attempt $i reading from PersonService: ${it.message}")
            })
            if (personList != null) break
        }
        if (personList == null) throw RuntimeException("Failure reading from PersonService")

        calculateHashesFor(personList!!.map { it.id.toString() }).forEach {
            writer.write("${it.first}, ${it.second}, ${it.third}")
            writer.newLine()
        }
    }

    private suspend fun calculateHashesFor(personIds: List<String>): List<Triple<String?, String?, String?>> =
        personHashTokenService.generate(personIds).get().map { Triple(it["person_id01"], it["person_id02"], it["person_id03"]) }

}
