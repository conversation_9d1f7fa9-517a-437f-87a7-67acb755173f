package br.com.alice.sherlock.event

import br.com.alice.clinicalaccount.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import java.util.UUID

data class ChannelsFileDonePayload(
    val requestId: UUID,
    val staffId: UUID,
    val fileId: String,
    val reason: String,
)

data class ChannelsFileCreatedEvent(
    private val requestId: UUID,
    private val staffId: UUID,
    private val fileId: String,
    private val reason: String,
) : NotificationEvent<ChannelsFileDonePayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = ChannelsFileDonePayload(requestId = requestId, staffId = staffId, fileId = fileId, reason = reason)
) {
    companion object {
        const val name = "channels-file-created"
    }
}
