package br.com.alice.sherlock.repositories

import br.com.alice.data.layer.models.PIIField
import br.com.alice.sherlock.models.QueryResultFile
import com.github.kittinunf.result.Result

interface PIIRepository {
    /**
     * QueryResultFile will have one column for each pii field, with one value for each personId.
     * QueryResultFile preserves the order of the personIds list.
     */
    suspend fun getPIIData(
        personIds: List<String>,
        fields: List<PIIField>
    ): Result<QueryResultFile, Throwable>
}
