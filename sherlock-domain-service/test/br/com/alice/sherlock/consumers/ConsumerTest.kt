package br.com.alice.sherlock.consumers

import br.com.alice.authentication.Authenticator
import com.google.firebase.auth.FirebaseToken
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

abstract class ConsumerTest {
    @BeforeTest
    open fun before() {
        val firebaseToken: FirebaseToken = mockk()
        mockkObject(Authenticator)

        every { Authenticator.verifyIdToken(any()) } returns firebaseToken
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }
}
