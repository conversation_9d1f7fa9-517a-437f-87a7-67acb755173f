package br.com.alice.sherlock.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.QueryStatus
import br.com.alice.data.layer.models.SherlockFileResult
import br.com.alice.data.layer.models.VaultFileType
import br.com.alice.data.layer.services.SherlockFileResultDataService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class SherlockFileVaultServiceImplTest {

    private val fileVaultService: FileVaultActionService = mockk()
    private val sherlockFileResultDataService: SherlockFileResultDataService = mockk()
    private val sherlockFileVaultService = SherlockFileVaultServiceImpl(fileVaultService, sherlockFileResultDataService)

    @Test
    fun `#getStaffFileById retrieves secured link url`() = runBlocking {
        val fileId = RangeUUID.generate()
        coEvery { fileVaultService.securedGenericLink(fileId) } returns VaultResponse(
            url = "url.com.br",
            id = fileId,
            type = "type",
            vaultUrl = "vaultUrl"
        ).success()

        val result = sherlockFileVaultService.getStaffFileById(fileId)
        assertEquals("url.com.br", result.get())

        coVerifyOnce { fileVaultService.securedGenericLink(fileId) }
    }

    @Test
    fun `#auditFile persists successful file result on success`() = runBlocking {
        val fileId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val reason = "REASON"
        val url = "url.com.br"

        val expected = SherlockFileResult(
            staffId = staffId,
            fileId = fileId,
            fileType = VaultFileType.PERSON,
            reason = reason,
            status = QueryStatus.DONE,
            url = url
        )

        coEvery { fileVaultService.securedLink(fileId) } returns VaultResponse(
            url = url,
            id = fileId,
            type = "type",
            vaultUrl = "vaultUrl"
        ).success()

        coEvery { sherlockFileResultDataService.add(any()) } returns expected.success()

        val result = sherlockFileVaultService.auditFile(staffId, fileId, reason)
        assertEquals(expected, result.get())

        coVerifyOnce { fileVaultService.securedLink(fileId) }
        coVerifyOnce { sherlockFileResultDataService.add(any()) }
    }

    @Test
    fun `#auditFile persists error file result on failure`() = runBlocking {
        val fileId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val reason = "REASON"

        val expected = SherlockFileResult(
            staffId = staffId,
            fileId = fileId,
            fileType = VaultFileType.PERSON,
            reason = reason,
            status = QueryStatus.ERROR,
            errorMessage = "sheet happens"
        )

        coEvery { fileVaultService.securedLink(fileId) } returns RuntimeException("sheet happens").failure()

        coEvery { sherlockFileResultDataService.add(any()) } returns expected.success()

        val result = sherlockFileVaultService.auditFile(staffId, fileId, reason)
        assertEquals("sheet happens", result.failure().message)

        coVerifyOnce { fileVaultService.securedLink(fileId) }
        coVerifyOnce { sherlockFileResultDataService.add(any()) }
    }

    @Test
    fun `#auditFile returns error when file result persistence fails`() = runBlocking {
        val fileId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val reason = "REASON"
        val url = "url.com.br"

        coEvery { fileVaultService.securedLink(fileId) } returns VaultResponse(
            url = url,
            id = fileId,
            type = "type",
            vaultUrl = "vaultUrl"
        ).success()

        coEvery { sherlockFileResultDataService.add(any()) } returns RuntimeException("sheet happens").failure()

        val result = sherlockFileVaultService.auditFile(staffId, fileId, reason)
        ResultAssert.assertThat(result).isFailure().withFailMessage("sheet happens")

        coVerifyOnce { fileVaultService.securedLink(fileId) }
        coVerify(exactly = 2) { sherlockFileResultDataService.add(any()) } // one for success, one for error
    }
}
