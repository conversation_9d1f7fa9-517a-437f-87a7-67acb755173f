package br.com.alice.sherlock.repositories

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.sherlock.models.QueryResultFile
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class QueryPartialResultFileRepositoryMemoryImplTest {
    private lateinit var queryPartialResultFileRepository: QueryPartialResultFileRepository

    @Test
    fun `#get should retrieve the file from memory`() = runBlocking {
        val path = "path/to/partial/file"
        val file = QueryResultFile(
            columns = mapOf("foo" to listOf("bar"))
        )

        queryPartialResultFileRepository = QueryPartialResultFileRepositoryMemoryImpl(mapOf(path to file))

        val result = queryPartialResultFileRepository.get(path)

        ResultAssert.assertThat(result).isSuccessWithData(file)
    }
}
