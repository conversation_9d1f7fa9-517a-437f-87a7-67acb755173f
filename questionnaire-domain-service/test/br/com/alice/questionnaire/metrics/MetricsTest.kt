package br.com.alice.questionnaire.metrics

import br.com.alice.common.observability.metrics.Metric.meterRegistry
import br.com.alice.data.layer.models.FormType.HEALTH
import br.com.alice.data.layer.models.HealthFormAnswerSourceType.CHANNEL
import br.com.alice.questionnaire.metrics.Metrics.incrementStart
import br.com.alice.questionnaire.metrics.Result.SUCCESS
import io.micrometer.core.instrument.Counter
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class MetricsTest {

    @BeforeTest
    fun setup() {
        meterRegistry.meters.map { meter -> meterRegistry.remove(meter.id) }
    }

    private val form = HEALTH
    private val source = CHANNEL

    @Test
    fun `#incrementStart counts metric with expected labels`() {

        incrementStart(
            result = SUCCESS,
            formType = form,
            sourceType = source
        )

        val counter = meterRegistry.meters.find {
            it.id.name == "alice.questionnaire_started"
                    && it.id.getTag("form") == form.name
                    && it.id.getTag("source") == source.name
                    && it.id.getTag("result") == SUCCESS.toLabel()
        } as Counter

        assertThat(counter.count()).isEqualTo(1.0)
    }

    @Test
    fun `#incrementStart counts metric with empty label`() {

        incrementStart(
            result = SUCCESS,
            formType = form,
            sourceType = null
        )

        val counter = meterRegistry.meters.find {
            it.id.name == "alice.questionnaire_started"
                    && it.id.getTag("form") == form.name
                    && it.id.getTag("source") == "empty"
                    && it.id.getTag("result") == SUCCESS.toLabel()
        } as Counter

        assertThat(counter.count()).isEqualTo(1.0)
    }

    @Test
    fun `#incrementStart counts metric with expected labels multiple times`() {

        repeat(5) {
            incrementStart(
                result = SUCCESS,
                formType = form,
                sourceType = source
            )
        }

        val counter = meterRegistry.meters.find {
            it.id.name == "alice.questionnaire_started"
                    && it.id.getTag("form") == form.name
                    && it.id.getTag("source") == source.name
                    && it.id.getTag("result") == SUCCESS.toLabel()
        } as Counter

        assertThat(counter.count()).isEqualTo(5.0)
    }
}
