package br.com.alice.questionnaire.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppContentScreenDetail
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormAnswerGroup
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import br.com.alice.questionnaire.client.FormNavigationService
import br.com.alice.questionnaire.client.HealthFormAnswerGroupService
import br.com.alice.questionnaire.client.HealthFormQuestionAnswerService
import br.com.alice.questionnaire.client.HealthFormQuestionService
import br.com.alice.questionnaire.client.HealthFormSectionService
import br.com.alice.questionnaire.client.HealthFormService
import br.com.alice.questionnaire.client.NextQuestionService
import br.com.alice.questionnaire.client.PreviousQuestionService
import br.com.alice.questionnaire.client.StartFormService
import br.com.alice.questionnaire.converters.QuestionnaireQuestionResponseConverter
import br.com.alice.questionnaire.event.HealthFormCompletedEvent
import br.com.alice.questionnaire.exceptions.InvalidQuestionIndexException
import br.com.alice.questionnaire.metrics.Metrics
import br.com.alice.questionnaire.models.HealthFormActionsTransport
import br.com.alice.questionnaire.models.HealthFormAnswerTransport
import br.com.alice.questionnaire.models.HealthFormPreviousQuestionTransport
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

const val EMPTY_ANSWER_COUNT = 0

class FormNavigationServiceImpl(
    private val healthFormService: HealthFormService,
    private val previousQuestionService: PreviousQuestionService,
    private val nextQuestionService: NextQuestionService,
    private val startFormService: StartFormService,
    private val healthFormSectionService: HealthFormSectionService,
    private val healthFormAnswerGroupService: HealthFormAnswerGroupService,
    private val healthFormQuestionAnswerService: HealthFormQuestionAnswerService,
    private val healthFormQuestionService: HealthFormQuestionService,
    private val kafkaProducer: KafkaProducerService
): FormNavigationService {

    override suspend fun moveBackOneQuestion(
        questionTransport: HealthFormPreviousQuestionTransport,
        source: HealthFormAnswerSource?
    ) =
        healthFormAnswerGroupService.getCurrentAnswerGroup(
            personId = questionTransport.personId,
            healthFormId = questionTransport.healthFormId,
            source = source
        ).flatMap { answerGroup ->
            healthFormQuestionAnswerService.getFormAnswers(
                questionTransport.personId,
                questionTransport.healthFormId,
                answerGroup.id
            ).flatMap { answers ->
                previousQuestionService.moveBackOneQuestion(answers)
            }
        }


    override suspend fun moveForwardOneQuestion(
        answerTransport: HealthFormAnswerTransport,
        source: HealthFormAnswerSource?
    ): Result<HealthFormActionsTransport, Throwable> =
        healthFormAnswerGroupService.getCurrentAnswerGroup(
            personId = answerTransport.personId,
            healthFormId = answerTransport.healthFormId,
            source = source
        ).flatMap { answerGroup ->
            logger.info(
                "FormNavigationServiceImpl.moveForwardOneQuestion: receive answer",
                "data" to answerTransport
            )
            healthFormQuestionAnswerService.upsertAnswer(
                answerTransport = answerTransport,
                answerGroup = answerGroup
            ).flatMap { answer ->
                healthFormQuestionService.getQuestionById(id = answer.healthFormQuestionId).flatMap { currentQuestion ->
                    nextQuestionService.getNextQuestion(
                        personId = answer.personId,
                        answer = answer,
                        currentQuestion = currentQuestion
                    ).map { nextQuestion ->
                        HealthFormActionsTransport(
                            currentQuestion = nextQuestion,
                            previousQuestion = currentQuestion
                        )
                    }.mapError {
                        when (it) {
                            is InvalidQuestionIndexException -> finishHealthForm(answerGroup)
                            else -> it
                        }
                    }
                }
            }
        }

    override suspend fun startForm(
        personId: PersonId,
        formKey: String,
        source: HealthFormAnswerSource?
    ) =
        healthFormService.getFormByKey(formKey).flatMap { form ->
            val answerGroup = healthFormAnswerGroupService.getCurrentAnswerGroup(
                personId = personId,
                healthFormId = form.id,
                source = source,
                shouldGenerateAnswerGroup = false
            ).getOrNullIfNotFound()

            val answers = answerGroup?.let {
                getAnswersBasedOnAnswerGroup(answerGroup, personId)
            } ?: emptyList()

            startFormService.getPreviousAndCurrentQuestion(
                answers = answers,
                form = form,
                source = source,
                personId = personId
            ).map { currentQuestions ->
                HealthFormActionsTransport(
                    currentQuestion = currentQuestions.second,
                    previousQuestion = currentQuestions.first
                )
            }.then {
                logger.info(
                    "StartFormServiceImpl.getCurrentQuestionToAnswerByFormKey# Start form",
                    "next_question_id" to it.currentQuestion.id,
                    "next_question_index" to it.currentQuestion.index,
                    "person" to personId.id
                )
                Metrics.incrementStart(
                    result = br.com.alice.questionnaire.metrics.Result.SUCCESS,
                    formType = form.type,
                    sourceType = source?.type,
                    formKey = form.key,
                )
            }.mapError {
                when (it) {
                    is InvalidQuestionIndexException -> finishHealthForm(answerGroup!!, form)
                    else -> it
                }
            }.thenError {
                Metrics.incrementStart(
                    result = br.com.alice.questionnaire.metrics.Result.FAILURE,
                    formType = form.type,
                    sourceType = source?.type,
                    formKey = form.key,
                )
            }
        }

    override suspend fun getBaseQuestionResponse(
        personId: PersonId,
        question: HealthFormQuestion,
        source: HealthFormAnswerSource?,
        action: Any,
        backAction: Any?,
        selectedAnswer: HealthFormQuestionAnswer?,
        appContentScreenDetail: AppContentScreenDetail?,
        shouldGenerateAnswerGroup: Boolean
    ): Result<QuestionnaireQuestionResponse, Throwable> = coroutineScope {

        coResultOf {
            val answerGroup = healthFormAnswerGroupService.getCurrentAnswerGroup(personId, question.healthFormId, source, shouldGenerateAnswerGroup).getOrNullIfNotFound()
            val countAnswersDeferred = async { getAnswersCount(answerGroup, question, personId) }
            val countQuestionsDeferred = async { healthFormQuestionService.countActiveQuestionsFromForm(question.healthFormId) }
            val sectionDeferred = async { healthFormSectionService.getSectionById(question.healthFormSectionId) }

            QuestionnaireQuestionResponseConverter.convert(
                question,
                answerGroup,
                sectionDeferred.await().get(),
                countAnswersDeferred.await().get(),
                countQuestionsDeferred.await().get(),
                action,
                backAction,
                selectedAnswer,
                appContentScreenDetail,
            )
        }
    }

    private suspend fun getAnswersCount(
        answerGroup: HealthFormAnswerGroup?,
        question: HealthFormQuestion,
        personId: PersonId
    ) =
        if (answerGroup != null) {
            healthFormQuestionAnswerService.countAnswerFromForm(
                question.healthFormId,
                personId,
                answerGroup.id
            )
        } else EMPTY_ANSWER_COUNT.success()

    private suspend fun getAnswersBasedOnAnswerGroup(
        answerGroup: HealthFormAnswerGroup,
        personId: PersonId
    ) = healthFormQuestionAnswerService.getFormAnswers(
            personId = personId,
            formId = answerGroup.healthFormId,
            healthFormAnswerGroupId = answerGroup.id
        ).get()

    private suspend fun finishHealthForm(answerGroup: HealthFormAnswerGroup, form: HealthForm? = null): Throwable {
        finishHealthFormEvent(answerGroup, form)

        return InvalidQuestionIndexException(
            "Finishing health form, this question index does not exists ${answerGroup.healthFormId}, person_id: ${answerGroup.personId}"
        )
    }

    private suspend fun finishHealthFormEvent(answerGroup: HealthFormAnswerGroup, form: HealthForm?) {
        val form = form ?: healthFormService.getFormById(answerGroup.healthFormId).get()
        logger.info(
            "FormNavigationServiceImpl.finishHealthFormEvent: finish form",
            "person_id" to answerGroup.personId,
            "answer_group" to answerGroup.id,
            "form_name" to form.name,
            "form_id" to form.id
        )

        kafkaProducer.produce(HealthFormCompletedEvent(answerGroup, form))
    }
}
