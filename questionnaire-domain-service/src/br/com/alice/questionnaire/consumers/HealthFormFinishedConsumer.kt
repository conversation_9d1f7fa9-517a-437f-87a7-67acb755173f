package br.com.alice.questionnaire.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.client.HealthFormAnswerGroupService
import br.com.alice.questionnaire.client.HealthFormService
import br.com.alice.questionnaire.client.HomeCardFormService
import br.com.alice.questionnaire.event.HealthFormCompletedEvent
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime

class HealthFormFinishedConsumer(
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
    private val personService: PersonService,
    private val healthFormService: HealthFormService,
    private val healthFormAnswerGroupService: HealthFormAnswerGroupService,
    private val homeCardFormService: HomeCardFormService,
    private val kafkaProducerService: KafkaProducerService
) : Consumer() {
    private val crmContentCardAppState = "CRM_CONTENT_CARDS"

    suspend fun finishHealthFormBrazeCampaign(event: HealthFormCompletedEvent) = withSubscribersEnvironment {
        val answerGroup = event.payload.healthFormAnswerGroup
        personService.get(answerGroup.personId, false).flatMapPair {
            healthFormService.getFormById(answerGroup.healthFormId)
        }.map { (healthForm, person) ->
            val analyticsEvent = AnalyticsEvent(
                name = AnalyticsEventName.FINISHED_HEALTH_FORM,
                properties = mapOf("health_form_key" to healthForm.key)
            )
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = analyticsEvent
            ).success
        }
    }

    suspend fun finishHealthFormAnswerGroupEvent(event: HealthFormCompletedEvent) =
        withSubscribersEnvironment {
            logger.info(
                "HealthFormFinishedConsumer.finishHealthFormAnswerGroupEvent: finish health form answer group",
                "health_form_answer_group_id" to event.payload.healthFormAnswerGroup.id
            )

            healthFormAnswerGroupService.finishAnswerGroup(event.payload.healthFormAnswerGroup)
        }

    suspend fun sendRecurringNPS(event: HealthFormCompletedEvent) = withSubscribersEnvironment {
        val personId = event.payload.healthFormAnswerGroup.personId
        val healthForm = event.payload.healthForm

        if (isNPSForm(healthForm)) {

            logger.info("HealthFormFinishedConsumer.sendRecurringNPS: inactivating previous home cards and creating new one")

            homeCardFormService.inactivatePreviousHomeCardsByKey(
                personId = personId,
                questionnaireKey = healthForm.key
            )

            homeCardFormService.createHomeCardForm(
                personId = personId,
                questionnaireKey = healthForm.key,
                startAt = LocalDateTime.now().plusMonths(3),
            )

            sendNpsFinishedEvent(personId)
            updateAppState(personId)
        }

        true.success()
    }

    private suspend fun sendNpsFinishedEvent(personId: PersonId) {
        val analyticsEvent = AnalyticsEvent(name = AnalyticsEventName.FINISHED_NPS_FORM)
        personService.get(personId, false).get().let { person ->
            crmAnalyticsTracker.sendEvent(
                nationalId = person.nationalId,
                event = analyticsEvent
            ).success
        }
    }

    private suspend fun updateAppState(personId: PersonId) {
        val updateAppStateRequestedEvent = UpdateAppStateRequestedEvent(
            updateAppStateRequestedPayload = UpdateAppStateRequestedPayload(
                personId = personId,
                appState = crmContentCardAppState,
            )
        )

        kafkaProducerService.produce(updateAppStateRequestedEvent)
    }

    private fun isNPSForm(healthForm: HealthForm) =
        healthForm.key == "NPS"
}
