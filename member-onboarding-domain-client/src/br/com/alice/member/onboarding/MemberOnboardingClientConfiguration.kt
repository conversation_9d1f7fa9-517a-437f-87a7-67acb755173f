package br.com.alice.member.onboarding

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object MemberOnboardingClientConfiguration {
    private val config = HoconApplicationConfig(ConfigFactory.load("member_onboarding_client.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()

        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun baseUrl() = config.property("${environment().value.lowercase()}.baseUrl").getString()

    fun memberApiUrl(path: String): String {
        val memberApiUrl = config.property("${environment().value.lowercase()}.memberApiUrl").getString()
        return "$memberApiUrl$path"
    }
}
