package br.com.alice.member.onboarding.notifier

import br.com.alice.common.core.PersonId
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.member.onboarding.SERVICE_NAME

data class MemberOnboardingDroppedEvent(
    private val personId: PersonId
) : NotificationEvent<MemberOnboardingDroppedEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = MemberOnboardingDroppedEventPayload(
        personId = personId
    )
) {
    companion object {
        const val name = "member-onboarding-dropped"
    }
}

data class MemberOnboardingDroppedEventPayload(
    val personId: PersonId
)
