package br.com.alice.member.onboarding.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.MemberOnboardingAction
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.models.MemberOnboardingStep
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingProgressService: Service {
    override val namespace get() = "member_onboarding"
    override val serviceName get() = "member_onboarding_progress"

    suspend fun findStepFromPersonId(personId: PersonId): Result<Pair<MemberOnboardingCheckpoint, MemberOnboardingStep>, Throwable>
    suspend fun findStepFromStepId(personId: PersonId, stepId: UUID): Result<Pair<MemberOnboardingCheckpoint, MemberOnboardingStep>, Throwable>
    suspend fun dropCheckpoint(personId: PersonId): Result<MemberOnboardingCheckpoint, Throwable>
    suspend fun findActionsFromStep(step: MemberOnboardingStep): Result<List<MemberOnboardingAction>, Throwable>

}
