package br.com.alice.member.onboarding.model

import java.util.UUID

data class OnboardingConclusionTemplate(
    val title: String,
    val imageUrl: String,
    val description: String? = null,
    val healthPlanTasks: List<OnboardingHealthPlanTask>,
    val healthInfos: List<HealthInfo>
)

data class OnboardingHealthPlanTask(
    val id: UUID,
    val icon: String,
    val title: String,
    val description: String? = null
)

data class HealthInfo(
    val title: String,
    val description: String,
    val physicianStaff: PhysicianStaff? = null
)

data class PhysicianStaff(
    val name: String,
    val council: String,
    val profileImageUrl: String? = null
)
