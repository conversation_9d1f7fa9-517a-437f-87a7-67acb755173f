plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.sorting-hat-domain-service"
version = aliceSortingHatDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:sorting-hat-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:clinical-account-domain-service-data-package"))
	implementation(project(":data-packages:sorting-hat-domain-service-data-package"))
	implementation(project(":data-packages:marauders-map-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":sorting-hat-domain-client"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":marauders-map-domain-client"))

    ktor2Dependencies()

    implementation("com.google.cloud:google-cloud-firestore:$firestoreVersion")

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
