package br.com.alice.sortinghat

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.PersonTeamAssociationDataService
import br.com.alice.data.layer.services.PersonTeamAssociationDataServiceClient
import br.com.alice.data.layer.services.RoutingHistoryDataService
import br.com.alice.data.layer.services.RoutingHistoryDataServiceClient
import br.com.alice.data.layer.services.RoutingRuleDataService
import br.com.alice.data.layer.services.RoutingRuleDataServiceClient
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.client.RoutingHistoryService
import br.com.alice.sortinghat.client.RoutingRuleService
import br.com.alice.sortinghat.client.RoutingService
import br.com.alice.sortinghat.consumers.MemberRiskUpdatedConsumer
import br.com.alice.sortinghat.consumers.PersonHealthcareTeamAssociationConsumer
import br.com.alice.sortinghat.controllers.InternalFeaturesController
import br.com.alice.sortinghat.inputcreators.HealthcareTeamInputCreator
import br.com.alice.sortinghat.inputcreators.InputCreator
import br.com.alice.sortinghat.inputcreators.channel.ChannelAcuteInputCreator
import br.com.alice.sortinghat.inputcreators.channel.ChannelInputCreator
import br.com.alice.sortinghat.loadbalances.BalanceLoader
import br.com.alice.sortinghat.loadbalances.channel.ChannelAcuteBalanceLoader
import br.com.alice.sortinghat.loadbalances.channel.ChannelBalanceLoader
import br.com.alice.sortinghat.loadbalances.healthcareteam.HealthcareTeamBalanceLoader
import br.com.alice.sortinghat.models.balances.Balance
import br.com.alice.sortinghat.models.input.RoutingModel
import br.com.alice.sortinghat.models.inputs.Input
import br.com.alice.sortinghat.models.output.OutputModel
import br.com.alice.sortinghat.routes.apiRoutes
import br.com.alice.sortinghat.routes.kafkaRoutes
import br.com.alice.sortinghat.rules.Rule
import br.com.alice.sortinghat.rules.channel.ChannelAcuteRule
import br.com.alice.sortinghat.rules.channel.ChannelFirstTryRule
import br.com.alice.sortinghat.rules.channel.ChannelSecondTryRule
import br.com.alice.sortinghat.rules.channel.ChatAdministrativeRule
import br.com.alice.sortinghat.rules.healthcareteam.HealthcareTeamV3Rule
import br.com.alice.sortinghat.services.PersonTeamAssociationServiceImpl
import br.com.alice.sortinghat.services.RoutingHistoryServiceImpl
import br.com.alice.sortinghat.services.RoutingRuleServiceImpl
import br.com.alice.sortinghat.services.RoutingServiceImpl
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    @Suppress("UNCHECKED_CAST")
    fun dependencyInjectionModules() = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        PersonDomainClientModule,
        MaraudersMapDomainClientModule,
        StaffDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single {
                DefaultHttpClient({
                    install(ContentNegotiation) {
                        simpleGson()
                    }
                }, timeoutInMillis = 15_000)
            }

            // Health Controller
            single { HealthController(SERVICE_NAME) }

            // rules
            single(named("ChatAdministrative")) { ChatAdministrativeRule(get()) as Rule<Input, OutputModel> }
            single(named("ChannelFirstTryRule")) { ChannelFirstTryRule(get()) as Rule<Input, OutputModel> }
            single(named("ChannelSecondTryRule")) { ChannelSecondTryRule(get()) as Rule<Input, OutputModel> }
            single(named("ChannelAcuteRule")) { ChannelAcuteRule(get()) as Rule<Input, OutputModel> }
            single(named("HealthcareTeamV3Rule")) { HealthcareTeamV3Rule(get()) as Rule<Input, OutputModel> }

            // balance loaders
            single<BalanceLoader<Balance>>(named("ChannelBalanceLoader")) { ChannelBalanceLoader() }
            single<BalanceLoader<Balance>>(named("HealthcareTeamBalanceLoader")) {
                HealthcareTeamBalanceLoader(get(), get())
            }
            single<BalanceLoader<Balance>>(named("ChannelAcuteBalanceLoader")) { ChannelAcuteBalanceLoader(get()) }

            // input creators
            single(named("ChannelInputCreator")) {
                ChannelInputCreator(get()) as InputCreator<Input, RoutingModel, Balance>
            }
            single(named("ChannelAcuteInputCreator")) {
                ChannelAcuteInputCreator() as InputCreator<Input, RoutingModel, Balance>
            }
            single(named("HealthcareTeamInputCreator")) {
                HealthcareTeamInputCreator(get(), get()) as InputCreator<Input, RoutingModel, Balance>
            }

            // Service
            single<PersonTeamAssociationService> { PersonTeamAssociationServiceImpl(get(), get(), get()) }
            single<RoutingService> { RoutingServiceImpl(getAll(), getAll(), getAll()) }
            single<RoutingRuleService> { RoutingRuleServiceImpl(get()) }
            single<RoutingHistoryService> { RoutingHistoryServiceImpl(get()) }

            // Consumers
            single { MemberRiskUpdatedConsumer(get<PersonTeamAssociationService>() as PersonTeamAssociationServiceImpl) }
            single { PersonHealthcareTeamAssociationConsumer(get<PersonTeamAssociationService>() as PersonTeamAssociationServiceImpl) }

            // DataService
            val invoker = DataLayerClientConfiguration.build()
            single<PersonTeamAssociationDataService> { PersonTeamAssociationDataServiceClient(invoker) }
            single<RoutingHistoryDataService> { RoutingHistoryDataServiceClient(invoker) }
            single<RoutingRuleDataService> { RoutingRuleDataServiceClient(invoker) }

            // Internal
            single {
                InternalFeaturesController(
                    get<BalanceLoader<Balance>>(named("ChannelAcuteBalanceLoader")) as ChannelAcuteBalanceLoader,
                    get<PersonTeamAssociationService>() as PersonTeamAssociationServiceImpl
                )
            }

            loadServiceServers("br.com.alice.sortinghat.services")
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules(),
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        featureConfigBootstrap(FeatureNamespace.SORTING_HAT)

        routing {
            application.attributes.put(PolicyRootServiceKey, SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }
    }
}
