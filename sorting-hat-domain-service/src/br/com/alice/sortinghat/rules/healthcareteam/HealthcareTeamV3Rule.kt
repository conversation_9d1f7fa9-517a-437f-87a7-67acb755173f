package br.com.alice.sortinghat.rules.healthcareteam

import br.com.alice.data.layer.services.RoutingHistoryDataService
import br.com.alice.sortinghat.models.RoutingResult
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamType.LEAN_DEFAULT
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamType.LEAN_PEDIATRIC
import br.com.alice.sortinghat.models.inputs.healthCareTeam.HealthcareTeamInput
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel

class HealthcareTeamV3Rule(
    routingHistoryDataService: RoutingHistoryDataService
): HealthcareTeamRule(routingHistoryDataService) {

    override fun ruleVersion() = 0
    override fun ruleOrder() = 0

    override suspend fun logic(input: HealthcareTeamInput): RoutingResult<HealthcareTeamOutputModel> =
        memberAlreadyAssociated(input) ?: applyRoutingRule(
            input,
            if (input.person.isConsideredAdult()) LEAN_DEFAULT
            else LEAN_PEDIATRIC
        )

}
