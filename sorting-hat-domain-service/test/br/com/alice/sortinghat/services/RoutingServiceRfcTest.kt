package br.com.alice.sortinghat.services

import br.com.alice.authentication.TestFactory
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.rfc.ServiceServer
import br.com.alice.common.rfc.TestInvoker
import br.com.alice.common.withUnauthenticatedToken
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.featureconfig.core.FeaturePopulateService
import br.com.alice.sortinghat.RuleTestHelper
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.client.RoutingService
import br.com.alice.sortinghat.client.RoutingServiceClient
import br.com.alice.sortinghat.inputcreators.InputCreator
import br.com.alice.sortinghat.inputcreators.channel.ChannelInputCreator
import br.com.alice.sortinghat.loadbalances.BalanceLoader
import br.com.alice.sortinghat.loadbalances.channel.ChannelBalanceLoader
import br.com.alice.sortinghat.models.balances.Balance
import br.com.alice.sortinghat.models.balances.channel.ChannelBalance
import br.com.alice.sortinghat.models.balances.channel.ChannelBalance.Companion.AVAILABLE
import br.com.alice.sortinghat.models.input.ChannelModel
import br.com.alice.sortinghat.models.input.RoutingModel
import br.com.alice.sortinghat.models.inputs.Input
import br.com.alice.sortinghat.models.inputs.channel.ChannelInput
import br.com.alice.sortinghat.models.output.ChannelOutputModel
import br.com.alice.sortinghat.models.output.OutputModel
import br.com.alice.sortinghat.module
import br.com.alice.sortinghat.rules.Rule
import br.com.alice.sortinghat.rules.channel.ChannelFirstTryRule
import io.ktor.server.testing.TestApplicationEngine
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.koin.core.qualifier.named
import org.koin.dsl.module
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class RoutingServiceRfcTest : RuleTestHelper() {

    private val httpInvoker = TestInvoker(testEngine, "rfc")

    private val routingServiceClient = RoutingServiceClient(httpInvoker)

    companion object {
        private val featurePopulateService: FeaturePopulateService = mockk()
        var testEngine: TestApplicationEngine = TestApplicationEngine()
        val rule1: ChannelFirstTryRule = mockk()
        val rule2: ChannelFirstTryRule = mockk()
        val balanceLoader: ChannelBalanceLoader = mockk()
        val personTeamAssociationService: PersonTeamAssociationService = mockk()
        val inputCreator: ChannelInputCreator = mockk()

        @AfterAll
        @JvmStatic
        fun tearDown() {
            testEngine.stop(0, 0)
        }

        @Suppress("UNCHECKED_CAST")
        @BeforeAll
        @JvmStatic
        fun init() {
            testEngine = TestApplicationEngine()
            val applicationEngine = testEngine.start()

            val modules = listOf(
                module(createdAtStart = true) {
                    single { featurePopulateService }
                    single(named("Rule1")) { rule1 as Rule<Input, OutputModel> }
                    single(named("Rule2")) { rule2 as Rule<Input, OutputModel> }
                    single<BalanceLoader<Balance>> { balanceLoader }
                    single { inputCreator as InputCreator<Input, RoutingModel, Balance>}
                    single<RoutingService> { RoutingServiceImpl(getAll(), getAll(), getAll())  }
                    single<ServiceServer> { RoutingServiceServer(get())  }
                }
            )

            applicationEngine.application.module(modules, startRoutesSync = false)
        }
    }

    private val id = "channel_id"
    private val model = ChannelModel(
        id = id,
        personId = PersonId(),
        kind = "CHAT",
        createdAt = LocalDateTime.now()
    )

    private val balanceId = "staffId"
    private val balance = ChannelBalance(
        value = balanceId,
        count = 3,
        role = Role.SCREENING_NURSE,
        status = AVAILABLE
    )

    private val input = ChannelInput(
        personTeamAssociation = personTeamAssociation,
        balances = listOf(balance),
        source = model,
        person = person
    )

    private val output = "{\"id\":\"$balanceId\",\"modelClass\":\"br.com.alice.sortinghat.models.output.ChannelOutputModel\"}"

    @AfterTest
    fun after() = clearAllMocks()

    @Test
    fun `#execute returns success when call rfc`() = runBlocking {
        coEvery { personTeamAssociationService.path() } returns "sorting_hat/person_association_team"

        coEvery { rule1.modelType() } returns RoutingHistoryModel.CHANNEL
        coEvery { rule1.ruleOrder() } returns 0
        coEvery { rule1.apply(input) } returns listOf(ChannelOutputModel(balanceId))

        coEvery { rule2.modelType() } returns RoutingHistoryModel.CHANNEL
        coEvery { rule2.ruleOrder() } returns 1
        coEvery { rule2.apply(input) } returns listOf(ChannelOutputModel(balanceId))

        coEvery { balanceLoader.modelType() } returns RoutingHistoryModel.CHANNEL
        coEvery { balanceLoader.load() } returns listOf(balance)

        coEvery { inputCreator.modelType() } returns RoutingHistoryModel.CHANNEL
        coEvery { inputCreator.create(model, listOf(balance)) } returns input

        withUnauthenticatedToken(TestFactory.environmentToken) {
            val result = routingServiceClient.execute(model.serialize())
            assertThat(result).isSuccessWithData(listOf(output))
        }

        coVerifyOnce { rule1.modelType() }
        coVerifyOnce { rule1.apply(any()) }
        coVerifyOnce { rule2.modelType() }
        coVerifyOnce { rule2.apply(any()) }
        coVerifyOnce { balanceLoader.modelType() }
        coVerifyOnce { balanceLoader.load() }
        coVerifyOnce { inputCreator.modelType() }
        coVerifyOnce { inputCreator.create(any(), any()) }
    }

}
