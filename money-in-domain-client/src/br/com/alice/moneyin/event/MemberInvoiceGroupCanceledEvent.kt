package br.com.alice.moneyin.event

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.moneyin.SERVICE_NAME

class MemberInvoiceGroupCanceledEvent(memberInvoiceGroup: MemberInvoiceGroup): NotificationEvent<MemberInvoiceGroupCanceledEvent.Payload>(
    producer = SERVICE_NAME,
    name = name,
    payload = Payload(memberInvoiceGroup),
) {
    companion object {
        const val name = "member-invoice-group-canceled"
    }

    data class Payload(
        val memberInvoiceGroup: MemberInvoiceGroup,
    )
}
