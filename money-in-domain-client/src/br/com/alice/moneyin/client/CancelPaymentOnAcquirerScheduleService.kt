package br.com.alice.moneyin.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerSchedule
import br.com.alice.data.layer.models.InvoicePayment
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface CancelPaymentOnAcquirerScheduleService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "cancel_payment_on_acquirer_schedule"

    suspend fun findInvoicePaymentsRequestedToCancel(
        date: LocalDateTime,
        limit: Int
    ): Result<List<CancelPaymentOnAcquirerSchedule>, Throwable>

    suspend fun cancelPaymentOnAcquirer(
        cancelPaymentOnAcquirerSchedule: CancelPaymentOnAcquirerSchedule,
        invoicePayment: InvoicePayment
    ): Result<CancelPaymentOnAcquirerSchedule, Throwable>

    suspend fun scheduleCancelPaymentOnAcquirer(
        invoicePaymentId: UUID,
    ): Result<CancelPaymentOnAcquirerSchedule, Throwable>
}
