package br.com.alice.moneyin.helpers

import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.moneyin.models.BankSlipInfo
import br.com.alice.moneyin.models.PixInfo
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.common.BitMatrix
import java.awt.image.BufferedImage
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.Base64
import javax.imageio.ImageIO

object BoletoAndPixHelpers {
    fun getPixAndBoletoInfo(invoicePayment: InvoicePayment): Result<Pair<PixInfo?, BankSlipInfo?>, Throwable> {
        return when (invoicePayment.paymentDetail) {
            is BolepixPaymentDetail -> getBolepixInfo(invoicePayment)
            is PixPaymentDetail -> getPixInfo(invoicePayment).map { it to null }
            is BoletoPaymentDetail -> getBoletoInfo(invoicePayment).map { null to it }
            else -> IllegalArgumentException("Some error not found").failure()
        }
    }

    private fun getBolepixInfo(invoicePayment: InvoicePayment): Result<Pair<PixInfo, BankSlipInfo>, Throwable> {
        val paymentDetail = invoicePayment.paymentDetail as BolepixPaymentDetail

        return Pair(
            PixInfo(
                qrCodeImageUrl = paymentDetail.pixPaymentUrl,
                copyAndPaste = paymentDetail.pixCopyAndPaste,
                qrCodeBase64 = paymentDetail.pixCopyAndPaste?.let { generatePixQrCodeBase64(it) }
            ),
            BankSlipInfo(
                barcodeImageUrl = paymentDetail.boletoPaymentUrl,
                barcodeData = paymentDetail.bankSlipBarcodeData,
                barcodeBase64 = paymentDetail.bankSlipBarcodeData?.let { generateBoletoBarcodeBase64(it) },
                digitableLine = paymentDetail.bankSlipDigitableLine
            )
        ).success()
    }

    private fun getPixInfo(invoicePayment: InvoicePayment): Result<PixInfo?, Throwable> {
        val paymentDetail = invoicePayment.paymentDetail as PixPaymentDetail

        return PixInfo(
            qrCodeImageUrl = paymentDetail.paymentUrl,
            copyAndPaste = paymentDetail.copyAndPaste,
            qrCodeBase64 = paymentDetail.copyAndPaste?.let { generatePixQrCodeBase64(it) }
        ).success()
    }

    private fun getBoletoInfo(invoicePayment: InvoicePayment): Result<BankSlipInfo?, Throwable> {
        val paymentDetail = invoicePayment.paymentDetail as BoletoPaymentDetail
        return BankSlipInfo(
            barcodeImageUrl = paymentDetail.paymentUrl,
            barcodeBase64 = generateBoletoBarcodeBase64(paymentDetail.barcodeData!!),
            digitableLine = paymentDetail.digitableLine!!
        ).success()
    }

    private fun generateBarcodeBufferedImage(
        barcodeData: String,
        width: Int = 400,
        height: Int = 100
    ): BufferedImage {
        val hints = mapOf(
            EncodeHintType.MARGIN to 0
        )
        val bitMatrix: BitMatrix = MultiFormatWriter().encode(barcodeData, BarcodeFormat.ITF, width, height, hints)
        return MatrixToImageWriter.toBufferedImage(bitMatrix)
    }

    private fun generateQrCodeBufferedImage(
        pixCopyAndPaste: String,
        width: Int = 400,
        height: Int = 400
    ): BufferedImage {
        val hints = mapOf(
            EncodeHintType.MARGIN to 0
        )
        val bitMatrix: BitMatrix =
            MultiFormatWriter().encode(pixCopyAndPaste, BarcodeFormat.QR_CODE, width, height, hints)
        return MatrixToImageWriter.toBufferedImage(bitMatrix)
    }

    fun generateBoletoBarcodeBase64(barcodeData: String): String {
        val bufferedImage = generateBarcodeBufferedImage(barcodeData)

        val outputStream = ByteArrayOutputStream()
        ImageIO.write(bufferedImage, "png", outputStream)

        val bytes = outputStream.toByteArray()
        val base64 = Base64.getEncoder().encodeToString(bytes)

        return base64
    }

    fun generateBoletoBarcodeFile(barcodeData: String): File {
        val bufferedImage = generateBarcodeBufferedImage(barcodeData)

        val tempFile = File.createTempFile("barcode_", ".png")
        ImageIO.write(bufferedImage, "png", tempFile)

        return tempFile
    }

    fun generatePixQrCodeFile(pixCopyAndPaste: String): File {
        val bufferedImage = generateQrCodeBufferedImage(pixCopyAndPaste)

        val tempFile = File.createTempFile("qrcode_", ".png")
        ImageIO.write(bufferedImage, "png", tempFile)

        return tempFile
    }

    fun generateTempFileForBase64(base64: String): File {
        val bytes = Base64.getDecoder().decode(base64)
        val tempFile = File.createTempFile("temp_", ".png")
        tempFile.writeBytes(bytes)
        return tempFile
    }

    fun generatePixQrCodeBase64(pixCopyAndPaste: String): String {
        val bufferedImage = generateQrCodeBufferedImage(pixCopyAndPaste)

        val outputStream = ByteArrayOutputStream()
        ImageIO.write(bufferedImage, "png", outputStream)

        val bytes = outputStream.toByteArray()
        val base64 = Base64.getEncoder().encodeToString(bytes)

        return base64
    }

}
