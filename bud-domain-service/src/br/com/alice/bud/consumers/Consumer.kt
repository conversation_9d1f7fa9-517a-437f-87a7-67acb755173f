package br.com.alice.bud.consumers

import br.com.alice.common.asyncLayer
import br.com.alice.common.consumer.Consumer
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.BUD_DOMAIN_SERVICE_NAME
import br.com.alice.data.layer.SORTING_HAT_DOMAIN_ROOT_SERVICE_NAME
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success

abstract class Consumer : Consumer(BUD_DOMAIN_SERVICE_NAME) {
    override suspend fun withSubscribersEnvironment(key: String, func: suspend () -> Result<Any, Throwable>) =
        asyncLayer {
            withRootServicePolicy(BUD_DOMAIN_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(BUD_DOMAIN_SERVICE_NAME) {
                    try {
                        func.invoke()
                            .flatMapError { handleError(it) }
                    } catch (ex: Exception) {
                        handleError(ex)
                    }
                }
            }
        }

    private fun handleError(error: Throwable) =
        if (error is NotFoundException) true.success()
        else error.failure()
}

