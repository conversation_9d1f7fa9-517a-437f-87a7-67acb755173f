package br.com.alice.bud.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OptionType
import br.com.alice.data.layer.models.ServiceScriptNavigationStatus.ACTIVE
import br.com.alice.data.layer.services.ServiceScriptNavigationDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ServiceScriptNavigationServiceTest {

    private val serviceScriptNavigationDataService: ServiceScriptNavigationDataService = mockk()
    private val serviceScriptNavigationService = ServiceScriptNavigationService(serviceScriptNavigationDataService)

    private val serviceScriptNavigation = TestModelFactory.buildServiceScriptNavigation(currentNodeId = RangeUUID.generate())

    @Test
    fun `#create - should create a service script navigation`() = runBlocking {
        coEvery { serviceScriptNavigationDataService.add(serviceScriptNavigation) } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.create(serviceScriptNavigation)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.add(any()) }
    }

    @Test
    fun `#update - should update a service script navigation`() = runBlocking {
        coEvery { serviceScriptNavigationDataService.update(serviceScriptNavigation) } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.update(serviceScriptNavigation)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.update(any()) }
    }

    @Test
    fun `#getByGroupIdAndCurrent - should get a service script navigation by group id and current node id`() = runBlocking {
        val groupId = serviceScriptNavigation.groupId
        val currentNodeId = serviceScriptNavigation.currentNodeId!!

        coEvery { serviceScriptNavigationDataService.findOne(queryEq {
            where {
                this.groupId.eq(groupId) and
                this.currentNodeId.eq(currentNodeId) and
                this.status.eq(ACTIVE)
            }
                .orderBy { this.createdAt }
                .sortOrder { desc }
        }) } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.getByGroupIdAndCurrent(groupId, currentNodeId)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.findOne(any()) }
    }

    @Test
    fun `#getActualByGroupId - should return the actual service script navigation by group id`() = runBlocking {
        coEvery {
            serviceScriptNavigationDataService.findOne(
                queryEq {
                    where {
                        this.status.eq(ACTIVE) and
                                this.groupId.eq(serviceScriptNavigation.groupId)
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { desc }
                })
        } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.getActualByGroupId(serviceScriptNavigation.groupId)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.findOne(any()) }
    }

    @Test
    fun `#getByGroupIdAndAnswerType - should return the service script navigation by group id and answer type`() = runBlocking {
        coEvery {
            serviceScriptNavigationDataService.findOne(
                queryEq {
                    where {
                        this.groupId.eq(serviceScriptNavigation.groupId) and
                                this.questionAnswersType.eq(OptionType.INPUT_TEXT)
                    }
                        .orderBy { createdAt }
                        .sortOrder { desc }
                })
        } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.getByGroupIdAndAnswerType(serviceScriptNavigation.groupId, OptionType.INPUT_TEXT)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.findOne(any()) }
    }

    @Test
    fun `#getByGroupIdAndPrevious - should return the previous service script navigation by group id and previous node id`() = runBlocking {
        val groupId = serviceScriptNavigation.groupId
        val previousNodeId = serviceScriptNavigation.previousNodeId

        coEvery {
            serviceScriptNavigationDataService.findOne(
                queryEq {
                    where {
                        this.groupId.eq(groupId) and
                                this.previousNodeId.eq(previousNodeId) and
                                this.status.eq(ACTIVE)
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { desc }
                })
        } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.getByGroupIdAndPrevious(groupId, previousNodeId)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.findOne(any()) }
    }

    @Test
    fun `#get - should return a service script navigation by id`() = runBlocking {
        coEvery { serviceScriptNavigationDataService.get(serviceScriptNavigation.id) } returns serviceScriptNavigation.success()

        val result = serviceScriptNavigationService.get(serviceScriptNavigation.id)
        assertThat(result).isSuccessWithData(serviceScriptNavigation)

        coVerifyOnce { serviceScriptNavigationDataService.get(any()) }
    }

    @Test
    fun `#findByGroupId - should return a list of service script navigation by group id`() = runBlocking {
        coEvery {
            serviceScriptNavigationDataService.find(
                queryEq {
                    where {
                        this.groupId.eq(serviceScriptNavigation.groupId) and
                                this.status.eq(ACTIVE)
                    }.orderBy { this.createdAt }.sortOrder { asc }
                })
        } returns listOf(serviceScriptNavigation).success()

        val result = serviceScriptNavigationService.findByGroupId(serviceScriptNavigation.groupId)
        assertThat(result).isSuccessWithData(listOf(serviceScriptNavigation))

        coVerifyOnce { serviceScriptNavigationDataService.find(any()) }
    }

}
