package br.com.alice.bud.services.internal

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ServiceScriptActionDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ServiceScriptActionServiceTest {
   private val serviceScriptActionDataService: ServiceScriptActionDataService = mockk()

   private val service = ServiceScriptActionService(serviceScriptActionDataService)

   private val action = TestModelFactory.buildServiceScriptAction()

   @Test
   fun `#create - should create and return ServiceScriptAction`() = runBlocking {
      coEvery { serviceScriptActionDataService.add(action) } returns action.success()

      val result = service.create(action)

      assertThat(result).isSuccessWithData(action)

      coVerifyOnce { serviceScriptActionDataService.add(any()) }
   }

   @Test
   fun `#update - should update and return ServiceScriptAction`() = runBlocking {
      coEvery { serviceScriptActionDataService.update(action) } returns action.success()

      val result = service.update(action)

      assertThat(result).isSuccessWithData(action)

      coVerifyOnce { serviceScriptActionDataService.update(any()) }
   }

   @Test
   fun `#get - should return ServiceScriptAction by id`() = runBlocking {
      coEvery { serviceScriptActionDataService.get(action.id) } returns action.success()

      val result = service.get(action.id)

      assertThat(result).isSuccessWithData(action)

      coVerifyOnce { serviceScriptActionDataService.get(any()) }
   }

   @Test
   fun `#delete - should delete ServiceScriptAction and return true`() = runBlocking {
      coEvery { serviceScriptActionDataService.delete(action) } returns true.success()

      val result = service.delete(action)

      assertThat(result).isSuccessWithData(true)

      coVerifyOnce { serviceScriptActionDataService.delete(any()) }
   }

   @Test
   fun `#getByIds - should return a list of ServiceScriptAction by a list of ids`() = runBlocking {
      coEvery { serviceScriptActionDataService.find (
         queryEq {
            where { this.id.inList(listOf(action.id)) }
         }
      ) } returns listOf(action).success()

      val result = service.getByIds(listOf(action.id))

      assertThat(result).isSuccessWithData(listOf(action))

      coVerifyOnce { serviceScriptActionDataService.find(any()) }
   }

   @Test
   fun `#getByTypeAndExternalId - should return a ServiceScriptAction by type and external id`() = runBlocking {
      coEvery { serviceScriptActionDataService.findOne (
         queryEq {
            where { this.type.eq(action.type) and this.externalId.eq(action.externalId) }
         }
      ) } returns action.success()

      val result = service.getByTypeAndExternalId(action.type, action.externalId)

      assertThat(result).isSuccessWithData(action)

      coVerifyOnce { serviceScriptActionDataService.findOne(any()) }
   }
}
