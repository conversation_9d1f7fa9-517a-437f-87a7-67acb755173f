package br.com.alice.bud

import br.com.alice.authentication.Authenticator
import com.google.firebase.auth.FirebaseToken
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.routing.get
import io.ktor.server.testing.testApplication
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class HealthControllerTest {

    @BeforeTest
    fun setup() {
        val firebaseToken: FirebaseToken = mockk()

        mockkObject(Authenticator)

        every { Authenticator.verifyIdToken(any()) } returns firebaseToken
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
    }

    @AfterTest
    fun clear() {
        unmockkObject(Authenticator)
    }

    @Test
    fun `should return 200 on health check`() {
        testApplication {
            routing {
                get("/") {
                    assertEquals(HttpStatusCode.OK, call.response.status())
                }

                get("/ops/ready") {
                    assertEquals(HttpStatusCode.OK, call.response.status())
                }

                get("/ops/live") {
                    assertEquals(HttpStatusCode.OK, call.response.status())
                }
            }
        }
    }
}
