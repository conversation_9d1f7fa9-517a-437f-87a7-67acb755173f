package br.com.alice.membership.model.onboarding

import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProviderType
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID


data class ProductOption(
    val title: String,
    val price: BigDecimal?,
    val groups: List<ProductOptionProviderGroup>,
    val validity: SimulationValidity? = null,
    val ageRange: String? = null,
    val pricesByAge: List<PriceByAge>? = null
)

data class HospitalOption(
    val bundles: List<HospitalOptionBundle>,
)

data class HospitalOptionBundle(
    val id: UUID,
    val name: String,
    val price: BigDecimal? = null,
    val isRecommended: Boolean,
    val isSelected: Boolean,
    val providers: List<ProductOptionProvider>,
    val accommodationType: String,
    val pricesByAge: List<PriceByAge>? = null,
    val bundleIds: List<UUID> = emptyList(),
)

data class SimulationValidity(
    val expiresAt: LocalDateTime?,
    val expired: Boolean,
)

data class ProductOptionProviderGroup(
    val type: ProductBundleType,
    val bundles: List<ProductOptionBundle>,
)

data class ProductOptionBundle(
    val id: UUID,
    val name: String,
    val price: BigDecimal? = null,
    val priceDelta: BigDecimal?,
    val type: ProductBundleType,
    val isSelected: Boolean,
    val mutable: Boolean?,
    val providers: List<ProductOptionProvider>,
    val pricesByAge: List<PriceByAge>? = null,
    val isRecommended: Boolean = false,
    val bundleIds: List<UUID> = emptyList(),
    val accommodation: AccommodationType? = null
)

data class ProductOptionProvider(
    val id: String,
    val name: String,
    val description: String? = null,
    val imageUrl: String? = null,
    val type: ProviderType,
    val count: Int? = null
)

data class PriceByAge(
    val age: Int,
    val price: BigDecimal
)
