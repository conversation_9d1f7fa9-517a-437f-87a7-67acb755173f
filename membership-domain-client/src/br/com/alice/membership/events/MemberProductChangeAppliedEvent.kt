package br.com.alice.membership.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.MemberProductChangeSchedule
import br.com.alice.membership.SERVICE_NAME

class MemberProductChangeAppliedEvent (
    memberProductChangeSchedule: MemberProductChangeSchedule,
) : NotificationEvent<MemberProductChangeAppliedEvent.Payload>(
    name = name,
    producer = SERVICE_NAME,
    payload = Payload(
        memberProductChangeSchedule = memberProductChangeSchedule,
    ),
) {
    companion object {
        const val name = "MEMBER-PRODUCT-CHANGE-APPLIED"
    }

    data class Payload(
        val memberProductChangeSchedule: MemberProductChangeSchedule,
    )
}
