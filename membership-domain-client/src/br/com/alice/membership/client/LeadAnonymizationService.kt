package br.com.alice.membership.client

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Lead
import com.github.kittinunf.result.Result

@RemoteService
interface LeadAnonymizationService : Service {

    override val namespace get() = "membership"
    override val serviceName get() = "lead_anonymization"

    suspend fun anonymize(email: String): Result<Lead, Throwable>

}

class AnonymizationFailedException(
    message: String,
    code: String = "anonymization_failed",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(email: String): this(
        message = "Failure when trying to anonymize $email"
    )
}
