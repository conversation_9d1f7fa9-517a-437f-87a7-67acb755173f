package br.com.alice.membership.client

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Person
import com.github.kittinunf.result.Result

@RemoteService
interface MembershipPersonService : Service {

    override val namespace get() = "membership"
    override val serviceName get() = "person"

    suspend fun addGasInformationIfNecessary(person: Person): Result<Person, Throwable>
}

class RegistrationNotFinishedException(
    message: String,
    code: String = "registration_not_finished",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)
