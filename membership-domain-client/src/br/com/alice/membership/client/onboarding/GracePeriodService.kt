package br.com.alice.membership.client.onboarding

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.GracePeriod
import br.com.alice.data.layer.models.Member
import com.github.kittinunf.result.Result

@RemoteService
interface GracePeriodService: Service {

    override val namespace get() = "membership"
    override val serviceName get() = "grace_period"

    suspend fun getGracePeriod(personId: PersonId, member: Member? = null): Result<GracePeriod, Throwable>
}
