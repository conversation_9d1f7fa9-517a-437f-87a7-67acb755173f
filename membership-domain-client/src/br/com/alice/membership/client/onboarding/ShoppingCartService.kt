package br.com.alice.membership.client.onboarding

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ShoppingCart
import br.com.alice.membership.model.onboarding.ShoppingCartWithProduct
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ShoppingCartService : Service {

    override val namespace get() = "membership"
    override val serviceName get() = "shopping_cart"

    suspend fun findByLeadId(leadId: UUID): Result<ShoppingCart, Throwable>

    suspend fun findCartWithActiveProductByLead(leadId: UUID): Result<ShoppingCartWithProduct, Throwable>
}
