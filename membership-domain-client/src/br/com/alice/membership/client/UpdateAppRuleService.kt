package br.com.alice.membership.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.UpdateAppRule
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface UpdateAppRuleService : Service {
    override val namespace get() = "membership"
    override val serviceName get() = "update_app_rule"

    suspend fun getAll(): Result<List<UpdateAppRule>, Throwable>
    suspend fun getByDescription(description: String, range: IntRange): Result<List<UpdateAppRule>, Throwable>
    suspend fun findByRange(range: IntRange): Result<List<UpdateAppRule>, Throwable>
    suspend fun countAll(): Result<Int, Throwable>
    suspend fun countByDescription(description: String): Result<Int, Throwable>
    suspend fun get(id: UUID): Result<UpdateAppRule, Throwable>
    suspend fun add(request: UpdateAppRule): Result<UpdateAppRule, Throwable>
    suspend fun update(request: UpdateAppRule): Result<UpdateAppRule, Throwable>
}
