package br.com.alice.membership.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.PersonHealthcareTeamRecommendation
import com.github.kittinunf.result.Result
import java.util.UUID


@RemoteService
interface PersonHealthcareTeamRecommendationService: Service {

    override val namespace get() = "membership"
    override val serviceName get() = "person_healthcare_team_recommendation"

    suspend fun add(personHealthcareTeamRecommendation: PersonHealthcareTeamRecommendation): Result<PersonHealthcareTeamRecommendation, Throwable>

    suspend fun saveChosenHealthcareTeam(personId: PersonId, chosenHealthcareTeamId: UUID): Result<PersonHealthcareTeamRecommendation, Throwable>
}
