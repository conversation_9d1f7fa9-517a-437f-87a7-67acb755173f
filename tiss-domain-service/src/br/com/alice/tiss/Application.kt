package br.com.alice.tiss

import ServiceConfig
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.TISS_DOMAIN_SERVICE_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.tiss.client.CnesService
import br.com.alice.tiss.client.EligibilityService
import br.com.alice.tiss.clients.CnesClient
import br.com.alice.tiss.clients.ProxyLambdaClient
import br.com.alice.tiss.clients.v4_01_00.AttachmentClient
import br.com.alice.tiss.clients.v4_01_00.EligibilityClient
import br.com.alice.tiss.clients.v4_01_00.GuiaClient
import br.com.alice.tiss.clients.v4_01_00.configurations.createAnexoGuiaServiceConfig
import br.com.alice.tiss.clients.v4_01_00.configurations.createGuiaServiceConfig
import br.com.alice.tiss.consumers.AttachmentOpmeCreatedConsumer
import br.com.alice.tiss.consumers.GuiaCreatedConsumer
import br.com.alice.tiss.consumers.GuiaHospitalizationCreatedConsumer
import br.com.alice.tiss.consumers.GuiaHospitalizationExtensionCreatedConsumer
import br.com.alice.tiss.consumers.GuiaWithChemotherapyCreatedConsumer
import br.com.alice.tiss.consumers.GuiaWithRadiotherapyCreatedConsumer
import br.com.alice.tiss.consumers.converters.AttachmentOpmeRequestBuilder
import br.com.alice.tiss.consumers.converters.GuiaCreatedRequestBuilder
import br.com.alice.tiss.consumers.converters.GuiaHospitalizationCreatedRequestBuilder
import br.com.alice.tiss.consumers.converters.GuiaHospitalizationExtensionCreatedRequestBuilder
import br.com.alice.tiss.consumers.converters.GuiaWithChemotherapyCreatedRequestBuilder
import br.com.alice.tiss.consumers.converters.GuiaWithRadiotherapyCreatedRequestBuilder
import br.com.alice.tiss.routes.kafkaRoutes
import br.com.alice.tiss.services.CnesServiceImpl
import br.com.alice.tiss.services.EligibilityServiceImpl
import br.com.alice.tiss.services.internal.AttachmentService
import br.com.alice.tiss.services.internal.GuiaService
import br.com.alice.tiss.services.internal.GuiaWithAttachmentService
import com.amazonaws.services.lambda.AWSLambdaClientBuilder
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        KafkaProducerModule,
        FeatureConfigDomainClientModule,
        ExecIndicatorDomainClientModule,
        ProviderDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }

            // Clients
            single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }
            single { CnesClient(ServiceConfig.Cnes.cnesConfig(), get()) }
            single { EligibilityClient(get()) }
            single { GuiaClient(createGuiaServiceConfig()) }
            single { AttachmentClient(createAnexoGuiaServiceConfig()) }

            // Exposed Services
            single<CnesService> { CnesServiceImpl(get()) }
            single<EligibilityService> { EligibilityServiceImpl(get()) }

            // Services
            loadServiceServers("br.com.alice.tiss.services")

            // Data Services

            // Controllers
            single { HealthController(TISS_DOMAIN_SERVICE_ROOT_SERVICE_NAME) }

            // Internal Service
            single { AttachmentService(get()) }
            single { GuiaService(get()) }
            single { GuiaWithAttachmentService(get()) }
            single { ProxyLambdaClient(AWSLambdaClientBuilder.standard().withRegion("sa-east-1").build()) }
            single { EligibilityClient.config() }
            single { GuiaCreatedRequestBuilder(get(), get()) }
            single { GuiaHospitalizationCreatedRequestBuilder(get(), get(), get()) }
            single { GuiaHospitalizationExtensionCreatedRequestBuilder(get(), get(), get()) }
            single { AttachmentOpmeRequestBuilder(get(), get()) }
            single { GuiaWithChemotherapyCreatedRequestBuilder(get(), get()) }
            single { GuiaWithRadiotherapyCreatedRequestBuilder(get(), get()) }

            //Consumers
            single { GuiaCreatedConsumer(get(), get(), get(), get()) }
            single { GuiaHospitalizationCreatedConsumer(get(), get(), get()) }
            single { GuiaHospitalizationExtensionCreatedConsumer(get(), get(), get()) }
            single { GuiaWithChemotherapyCreatedConsumer(get(), get(), get()) }
            single { GuiaWithRadiotherapyCreatedConsumer(get(), get(), get()) }
            single { AttachmentOpmeCreatedConsumer(get(), get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, TISS_DOMAIN_SERVICE_ROOT_SERVICE_NAME)
        }

        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.TISS)
    }
}
