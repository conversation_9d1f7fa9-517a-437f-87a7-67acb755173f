package br.com.alice.tiss.consumers.converters

import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.data.layer.models.AnsRegisterCode
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.DEFAULT_CBO
import br.com.alice.exec.indicator.models.DEFAULT_COUNCIL_NUMBER
import br.com.alice.exec.indicator.models.DEFAULT_PROFESSIONAL_NAME
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.tiss.models.CreateGuiaHospitalizationExtensionRequest
import com.github.kittinunf.result.getOrNull
import java.time.LocalDate

class GuiaHospitalizationExtensionCreatedRequestBuilder(
    private val healthcareResourceService: HealthcareResourceService,
    private val hospitalizationInfoService: HospitalizationInfoService,
    private val providerUnitService: ProviderUnitService
) {
    companion object {
        private const val DEFAULT_CLINICAL_INDICATION = "Solicitacao internacao"
    }

    suspend fun build(
        procedures: List<MvAuthorizedProcedure>,
        totvsGuia: TotvsGuia,
        referenceTotvsGuia: TotvsGuia,
        beneficiary: Beneficiary,
        origin: GuiaOrigin
    ) = coResultOf<CreateGuiaHospitalizationExtensionRequest, Throwable> {
        val firstProcedure = procedures.first()
        val procedureCodes = procedures.mapNotNull { it.procedureId }
        val healthcareResources = healthcareResourceService.findByCodes(procedureCodes).get()
        val healthcareResourcesByCode = healthcareResources.associateBy { it.code }
        val hospitalizationInfo = hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id).getOrNull()
            ?: throw IllegalArgumentException("Hospitalization info not found")
        val providerUnit = providerUnitService.get(hospitalizationInfo.providerUnitId).get()
        val requesterData = RequesterData(name = providerUnit.name.unaccent(), providerCode = providerUnit.cnpj!!)

        CreateGuiaHospitalizationExtensionRequest(
            guiaNumber = totvsGuia.code,
            referenceGuiaNumber = referenceTotvsGuia.externalCode ?: referenceTotvsGuia.code,
            additionalDays = hospitalizationInfo.numberOfDays,
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(
                name = beneficiary.name?.unaccent(),
                nationalId = beneficiary.nationalId,
                newBornAttendance = firstProcedure.extraGuiaInfo.newBorn ?: false
            ),
            procedures = procedures.map { procedure ->
                GuiaProcedure(
                    code = procedure.procedureId!!,
                    description = healthcareResourcesByCode[procedure.procedureId!!]?.description ?: "NO DESCRIPTION",
                    quantity = procedure.extraGuiaInfo.quantity ?: 1,
                    status = procedure.status,
                    table = healthcareResourcesByCode[procedure.procedureId!!]?.tableType ?: "98"
                )
            },
            requester = requesterData,
            professional = Professional(
                name = firstProcedure.requestedByProfessional.fullName?.unaccent().nullIfBlank() ?: DEFAULT_PROFESSIONAL_NAME,
                councilNumber = firstProcedure.requestedByProfessional.councilNumber.nullIfBlank() ?: DEFAULT_COUNCIL_NUMBER,
                council = firstProcedure.requestedByProfessional.council,
                councilState = firstProcedure.requestedByProfessional.councilState,
                phoneNumber = firstProcedure.requestedByProfessional.phone?.onlyNumbers(),
                email = firstProcedure.requestedByProfessional.email,
                cbo = firstProcedure.requestedByProfessional.cboCode ?: DEFAULT_CBO,
            ),
            origin = origin,
            clinicalIndication = hospitalizationInfo.clinicalIndication?.sanitizeField()
                ?: DEFAULT_CLINICAL_INDICATION,
            clinicalAttachment = null,
            ansCode = AnsRegisterCode.ALICE.code
        )
    }

    private fun String?.sanitizeField() =
        this?.unaccent()?.onlyDigits()?.uppercase()?.trim()?.nullIfBlank()
}
