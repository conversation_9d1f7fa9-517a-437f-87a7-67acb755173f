package br.com.alice.tiss.consumers

import br.com.alice.common.logging.logger
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaHospitalizationCreatedEvent
import br.com.alice.tiss.consumers.converters.GuiaHospitalizationCreatedRequestBuilder
import br.com.alice.tiss.services.internal.GuiaService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import com.sun.xml.ws.client.ClientTransportException
import jakarta.xml.ws.WebServiceException

class GuiaHospitalizationCreatedConsumer(
    private val guiaService: GuiaService,
    private val guiaHospitalizationCreatedRequestBuilder: GuiaHospitalizationCreatedRequestBuilder,
    private val totvsGuiaService: TotvsGuiaService
) : AutoRetryableConsumer(ClientTransportException::class, WebServiceException::class) {

    suspend fun handle(event: GuiaHospitalizationCreatedEvent) = withSubscribersEnvironment {
        logger.info(
            "Processing GuiaHospitalizationCreatedEvent on tiss-domain-service",
            "payload" to event.payload,
        )

        val payload = event.payload

        val totvsGuiaId = payload.totvsGuia.id

        val totvsGuia = totvsGuiaService.get(totvsGuiaId).get()

        if (totvsGuia.externalCode != null) {
            logger.info(
                "GuiaHospitalizationCreatedEvent already processed",
                "totvs_guia_id" to event.payload.totvsGuia.id,
            )

            return@withSubscribersEnvironment true.success()
        }

        guiaHospitalizationCreatedRequestBuilder.build(
            totvsGuia = payload.totvsGuia,
            procedures = payload.procedures,
            beneficiary = payload.beneficiary,
            origin = payload.origin,
        ).flatMap { guiaService.createGuiaHospitalization(it) }
    }
}
