plugins {
    kotlin
    `kotlin-kapt`
    id("org.sonarqube")
}

group = "br.com.alice.alice-common-feature-access"
version = aliceCommonVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:common-feature-access")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

publishing {
    publications {
        create<MavenPublication>("nexus") {
            from(components["java"])
        }
    }

    repositories {
        maven {
            name = "nexus"
            url = uri("https://nexus.devtools.alice.tools/repository/releases/")
            credentials {
                username = System.getenv()["NEXUS_USER"] ?: ""
                password = System.getenv()["NEXUS_PASSWORD"] ?: ""
            }
        }
    }
}

dependencies {
    ktor2Dependencies()
    test2Dependencies()
    implementation(project(":common"))
    implementation(project(":common-core"))
    implementation(project(":common-service"))
    implementation(project(":common-logging"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:membership-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:schedule-domain-service-data-package"))
	implementation(project(":data-packages:product-domain-service-data-package"))

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
}
