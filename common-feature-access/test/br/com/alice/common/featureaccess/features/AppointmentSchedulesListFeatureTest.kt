package br.com.alice.common.featureaccess.features

import br.com.alice.authentication.UserType
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.FeatureAccessContext
import br.com.alice.common.featureaccess.FeatureAccessRequest
import br.com.alice.common.featureaccess.FeatureAccessResult
import br.com.alice.common.featureaccess.FeatureAccessSource
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.Staff
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class AppointmentSchedulesListFeatureTest {

    private val personId = PersonId()
    private val staff = Staff(
        email = "<EMAIL>",
        firstName = "Caio",
        lastName = "Salgadinho",
        gender = Gender.MALE,
        role = Role.MANAGER_PHYSICIAN,
        type = StaffType.PITAYA
    )
    private val appointmentSchedule = AppointmentSchedule(
        staffId = staff.id,
        type = AppointmentScheduleType.NUTRITIONIST,
        eventName = "event_name",
        eventId = null,
        eventUuid = null,
        location = null,
        startTime = LocalDateTime.now().plusDays(1),
        personId = personId,
        status = AppointmentScheduleStatus.COMPLETED
    )

    private val appointmentSchedulesListResource = AppointmentSchedulesListResource(
        appointmentSchedulesWithStaff = listOf(
            AppointmentScheduleWithStaff(
                appointmentSchedule,
                staff,
            )
        )
    )

    @Test
    fun `#AppointmentSchedulesListFeature filter should return the same input for members`() {

        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.MEMBER,
        )

        val expectedResult = FeatureAccessResult(
            feature = Feature.APPOINTMENT_SCHEDULES_LIST,
            response = appointmentSchedulesListResource,
        )

        val result = AppointmentSchedulesListFeature.filter(
            request = FeatureAccessRequest(
                context,
                resource = appointmentSchedulesListResource,
            )
        )

        assertThat(expectedResult).isEqualTo(result)
    }

    @Test
    fun `#AppointmentSchedulesListFeature filter should return the same input for non-members`() {
        val context = FeatureAccessContext(
            source = FeatureAccessSource.APP,
            serviceConfig = BaseConfig.instance,
            userType = UserType.NON_MEMBER,
        )

        val expectedResult = FeatureAccessResult(
            feature = Feature.APPOINTMENT_SCHEDULES_LIST,
            response = appointmentSchedulesListResource
        )

        val result = AppointmentSchedulesListFeature.filter(
            request = FeatureAccessRequest(
                context,
                resource = appointmentSchedulesListResource,
            )
        )

        assertThat(expectedResult).isEqualTo(result)
    }
}
