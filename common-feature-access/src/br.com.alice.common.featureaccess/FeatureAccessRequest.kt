package br.com.alice.common.featureaccess

import br.com.alice.authentication.UserType
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.featureaccess.features.FeatureResource

data class FeatureAccessRequest<T: FeatureResource>(
    val context: FeatureAccessContext,
    val resource: T?,
)

data class FeatureAccessContext(
    val source: FeatureAccessSource,
    val userType: UserType,
    val serviceConfig: BaseConfig,
)

enum class FeatureAccessSource {
    APP,
    EHR,
}
