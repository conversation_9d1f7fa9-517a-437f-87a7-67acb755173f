package br.com.alice.data.layer.strategies

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.UpdatedByReference
import br.com.alice.data.layer.services.UpdateRequest
import java.util.UUID

object ConflictResolutionStrategy {

    suspend fun hasConflict(
        request: UpdateRequest,
        updatedByReference: UpdatedByReference,
        loggedStaffIdFunc: suspend () -> UUID
    ): <PERSON><PERSON><PERSON> {
        val versionDiff = updatedByReference.version - request.version
        if (versionDiff == 0) return false

        logger.info(
            "Conflict on update",
            "updated_by_reference_class" to updatedByReference::class.qualifiedName,
            "updated_by_reference_id" to updatedByReference.id,
            "version_diff" to versionDiff,
            "reference" to updatedByReference.updatedBy
        )

        val updatedBy = updatedByReference.updatedBy ?: return false

        val userType = updatedBy.userType
        val userId = updatedBy.userId

        val loggedStaffId = loggedStaffIdFunc()
        val isSameStaff = userId == loggedStaffId.toString()

        val solved = versionDiff == 1 && userType == "Staff" && isSameStaff
        logger.info(
            "Conflict on update",
            "conflict_detected" to true,
            "updated_by_reference_id" to updatedByReference.id,
            "updated_by_reference_class" to updatedByReference::class.qualifiedName,
            "request_staff_id" to loggedStaffId,
            "request_user_type" to userType,
            "request_version" to request.version,
            "original_staff_id" to userId,
            "original_version" to updatedByReference.version,
            "version_diff" to versionDiff,
            "is_same_staff" to isSameStaff,
            "solved" to solved
        )
        return !solved
    }
}
