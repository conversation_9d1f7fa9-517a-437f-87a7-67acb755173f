package br.com.alice.data.layer.services


import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAllPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.SimilarToPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.Book
import br.com.alice.data.layer.models.Reference
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface BookDataService : Service,
    Finder<BookDataService.FieldOptions, BookDataService.OrderingOptions, Book>,
    Counter<BookDataService.FieldOptions, BookDataService.OrderingOptions, Book>,
    Updater<Book>,
    Getter<Book>,
    Adder<Book> {

    override val namespace: String
        get() = "test"
    override val serviceName: String
        get() = "book"

    class Name : Field.TextField(Book::name) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun diff(value: String) = Predicate.diff(this, value)
        @OptIn(LikePredicateUsage::class)
        fun iEq(value: String) = Predicate.iEq(this, value)

        @OptIn(LikePredicateUsage::class)
        fun startsWith(value: String) =
            Predicate.startsWith(this, value)

        fun search(value: String) =
            Predicate.search(this, value)

        @OptIn(LikePredicateUsage::class)
        fun like(value: String) =
            Predicate.like(this, value)

        @OptIn(LikePredicateUsage::class)
        fun likeAny(values: List<String>) =
            Predicate.likeAny(this, values)

        @OptIn(SimilarToPredicateUsage::class)
        fun similarTo(value: String) =
            Predicate.similarTo(this, value)
    }

    class Age : Field.IntegerField(Book::age) {
        fun eq(value: Int) = Predicate.eq(this, value)
        fun less(value: Int) = Predicate.less(this, value)
        fun greaterEq(value: Int) =
            Predicate.greaterEq(this, value)

        fun greater(value: Int) =
            Predicate.greater(this, value)

        fun lessEq(value: Int) = Predicate.lessEq(this, value)
        fun inList(value: List<Int>) = Predicate.inList(this, value)
        fun notInList(value: List<Int>) = Predicate.notInList(this, value)

        @OptIn(Predicate.Companion.InValuesListPredicateUsage::class)
        fun inValuesList(value: List<Int>) = Predicate.inValuesList(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class Isbn : Field.TextField(Book::isbn)

    class Available : Field.BooleanField(Book::available)

    @OptIn(LikePredicateUsage::class, ContainsPredicateUsage::class, ContainsAnyPredicateUsage::class)
    class Genres : Field.JsonbField(Book::genres) {
        fun eq(value: List<String>) = Predicate.eq(this, value)
        fun contains(value: String) = Predicate.contains(this, value)
        fun containsAny(list: List<String>) = Predicate.containsAny(this, list)
        fun like(value: String) = Predicate.like(this, value)
        fun likeAny(value: List<String>) = Predicate.likeAny(this, value)
        fun isNotEmptyList() = Predicate.isNotEmptyList(this)
        fun isEmptyList() = Predicate.isEmptyList(this)
    }

    class PersonIdField : Field.TableIdField(Book::personId)

    class NestedProp : Field.JsonbField(Book::nested) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"prop\":\"$value\"}")
    }

    class ReferencedProp : Field.JsonbField(Book::jsonObjectArray) {
        @OptIn(ContainsAllPredicateUsage::class)
        fun containsAll(value: List<Reference>) = Predicate.containsAll(this, value)
    }

    class Nested : Field.JsonbField(Book::nested) {
        fun eq(value: br.com.alice.data.layer.models.Nested) = Predicate.eq(this, value)
        fun inList(value: List<br.com.alice.data.layer.models.Nested>) = Predicate.inList(this, value)
        fun notInList(value: List<br.com.alice.data.layer.models.Nested>) = Predicate.notInList(this, value)
    }

    class CreatedAt : Field.DateTimeField(Book::createdAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class LaunchDate : Field.DateField(Book::launchDate) {
        fun eq(value: LocalDate) = Predicate.eq(this, value)
    }

    class SearchTokens : Field.TextField(Book::searchTokens) {
        fun search(value: String) = Predicate.rankedSearch(this, value)
    }

    class SomeUuidField : Field.UUIDField(Book::someUuid) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)

        fun withinRange(range: Int, latitude: String, longitude: String) =
            Predicate.withinRange(this, latitude, longitude, range)
    }

    class GeoLocation : Field.GeographyField(Book::geoLocation) {
        fun withinRange(range: Int, latitude: String, longitude: String) =
            Predicate.withinRange(this, latitude, longitude, range)
    }

    class FieldOptions {
        val name = Name()
        val age = Age()
        val available = Available()
        val genres = Genres()
        val personId = PersonIdField()
        val createdAt = CreatedAt()
        val launchDate = LaunchDate()
        val nested = NestedProp()
        val nested2 = Nested()
        val searchTokens = SearchTokens()
        val jsonObjectArray = ReferencedProp()
        val someUuid = SomeUuidField()
        val geoLocation = GeoLocation()
    }

    class OrderingOptions {
        val isbn = Isbn()
        val createdAt = CreatedAt()
        val geoLocation = GeoLocation()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<Book>, Throwable>
    override suspend fun update(model: Book): Result<Book, Throwable>
    override suspend fun get(id: UUID): Result<Book, Throwable>
    override suspend fun add(model: Book): Result<Book, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
