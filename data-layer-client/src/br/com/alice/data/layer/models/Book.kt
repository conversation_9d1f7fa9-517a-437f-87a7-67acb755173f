package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Geography
import br.com.alice.common.models.GeographyReference
import br.com.alice.common.serialization.JsonSerializable
import com.google.gson.annotations.Expose
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Book(
    val name: String,
    val author: String,
    val isbn: String,
    val age: Int?,
    val available: Boolean,
    val genres: List<String>,
    val nested: Nested? = null,
    val jsonObjectArray: List<Reference> = emptyList(),
    val listOfMap: List<Map<String, Any?>> = emptyList(),
    val launchDate: LocalDate = LocalDate.now(),
    val searchTokens: String? = name.unaccent(),
    val someUuid: UUID = RangeUUID.generate(),
    override val latitude: String? = null,
    override val longitude: String? = null,
    override val geoLocation: Geography? = null,
    val version: Int = 0,
    @Expose
    override val personId: PersonId = PersonId(),
    override val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference, GeographyReference {

    override fun sanitize(): Book = this.copy(
        geoLocation = GeographyReference.getGeoLocation(this.latitude, this.longitude),
    )
}

data class Nested(
    val prop: String
)

data class Reference(
    val id: UUID,
    val model: String
) : JsonSerializable
