package br.com.alice.data.layer

const val ACQUISITION_ROOT_SERVICE_NAME = "acquisition-domain-client"
const val ACQUISITION_ENVIRONMENT_SUBSCRIBERS = "acquisition-domain-service-subscribers"
const val ATLAS_DOMAIN_ROOT_SERVICE_NAME = "atlas-domain-service"
const val ATLAS_DOMAIN_SUBSCRIBERS_SERVICE_NAME = "atlas-domain-service-subscribers"
const val ATLAS_DOMAIN_BACKFILL_SERVICE_NAME = "atlas-domain-service-backfill"
const val AKINATOR_ROOT_SERVICE_NAME = "akinator-domain-service"
const val AMAS_ROOT_SERVICE_NAME = "amas-domain-service"
const val AMAS_API_ROOT_SERVICE_NAME = "amas-bff-api"
const val AMAS_RECURRING_SERVICE_NAME = "amas-domain-service-recurring"
const val AMAS_BACKFILL_SERVICE_NAME = "amas-domain-service-backfill"
const val APPOINTMENT_ROOT_SERVICE_NAME = "appointment-domain-service"
const val EVENTINDER_ROOT_SERVICE_NAME = "eventinder-domain-service"
const val BUSINESS_ENVIRONMENT_SUBSCRIBERS = "business-domain-service-subscribers"
const val BUSINESS_RISK_ENVIRONMENT_SUBSCRIBERS = "business-risk-domain-service-subscribers"
const val BUSINESS_ENVIRONMENT_RECURRENT = "business-domain-service-recurrent"
const val BUSINESS_ENVIRONMENT_BACKFILL = "business-domain-service-backfill"
const val BUSINESS_RISK_DOMAIN_ROOT_SERVICE_NAME = "business-risk-domain-service"
const val BUSINESS_RISK_BACKFILL_SERVICE_NAME = "business-risk-domain-service-backfill"
const val DB_INTEGRATION_SERVICE_ROOT_SERVICE_NAME = "db-integration-service"
const val CHANNEL_BFF_API_ROOT_SERVICE_NAME = "channel-bff-api"
const val CHANNEL_DOMAIN_ROOT_SERVICE = "channel-domain-service"
const val CHANNEL_EVENT_CONSUMER_ROOT_SERVICE = "channel-event-consumer"
const val EHR_API_ROOT_SERVICE_NAME = "ehr-api"
const val EHR_DOMAIN_ROOT_SERVICE_NAME = "ehr-domain-service"
const val EINSTEIN_BFF_API_ROOT_SERVICE_NAME = "einstein-bff-api"
const val EINSTEIN_INTEGRATION_SERVICE_ROOT_SERVICE_NAME = "einstein-integration-service"
const val DRAGON_RADAR_BFF_API_ROOT_SERVICE_NAME = "dragon-radar-bff-api"
const val DRAGON_RADAR_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "dragon-radar-domain-service"
const val EXEC_INDICATOR_API_ROOT_SERVICE_NAME = "exec-indicator-api"
const val EXAMPLE_API_ROOT_SERVICE_NAME = "example-api"
const val EXEC_INDICATOR_ROOT_SERVICE_NAME = "exec-indicator-domain-service"
const val EXEC_INDICATOR_RECURRING_SERVICE_NAME = "exec-indicator-domain-service-recurring"
const val EXEC_INDICATOR_BACKFILL_SERVICE_NAME = "exec-indicator-domain-service-backfill"
const val FILE_VAULT_ROOT_SERVICE_NAME = "file-vault-service"
const val FEATURE_CONFIG_ROOT_SERVICE_NAME = "feature-config-domain-service"
const val FLEURY_INTEGRATION_SERVICE_ROOT_SERVICE_NAME = "fleury-integration-service"
const val HAOC_INTEGRATION_SERVICE_ROOT_SERVICE_NAME = "haoc-integration-service"
const val HEALTH_CARE_OPS_ROOT_SERVICE_NAME = "healthcare-ops-api"
const val HEALTH_CARE_OPS_ENVIRONMENT_SUBSCRIBERS = "healthcare-ops-api-subscribers"
const val HEALTH_CARE_OPS_WEBHOOKS_ROOT_SERVICE_NAME = "healthcare-ops-webhooks"
const val HEALTH_LOGIC_DOMAIN_ROOT_SERVICE_NAME = "health-logic-domain-service"
const val LIMBO_API_ROOT_SERVICE_NAME = "limbo-api"
const val MEMBER_ROOT_SERVICE_NAME = "member-api"
const val MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME = "member-api-subscribers"
const val MEMBERSHIP_ROOT_SERVICE_NAME = "member-api"
const val MEMBERSHIP_SUBSCRIBERS_ROOT_SERVICE_NAME = "membership-domain-service-subscribers"
const val MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME = "membership-domain-service-webhooks"
const val MEMBERSHIP_ENVIRONMENT_BACKFILL = "membership-domain-service-backfill"
const val SCHEDULE_WEBHOOKS_ROOT_SERVICE_NAME = "schedule-domain-service-webhooks"
const val MONEY_IN_BFF_API_ROOT_SERVICE_NAME = "money-in-bff-api"
const val MONEY_IN_ROOT_SERVICE_NAME = "money-in-domain-service"
const val MONEY_IN_ENVIRONMENT_SUBSCRIBERS = "money-in-domain-service-subscribers"
const val RELOAD_TEST_PEOPLE_CACHE = "reload-test-people-cache"
const val SCHEDULE_DOMAIN_ROOT_SERVICE_NAME = "schedule-domain-service"
const val SCHEDULE_EVENT_CONSUMER = "schedule-event-consumer"
const val SYSTEM_OPS_ROOT_SERVICE_NAME = "system-ops-api"
const val TEST_RESULT_DOMAIN_ROOT_SERVICE_NAME = "test-result-domain-service"
const val WANDA_BFF_API_ROOT_SERVICE_NAME = "wanda-bff-api"
const val WANDA_DOMAIN_ROOT_SERVICE_NAME = "wanda-domain-service"
const val MEMBER_WANNABE_API_ROOT_SERVICE_NAME = "member-wannabe-api"
const val SCHEDULER_ROOT_SERVICE_NAME = "scheduler-bff-api"
const val PROVIDER_DOMAIN_ROOT_SERVICE_NAME = "provider-domain-service"
const val PROVIDER_ENVIRONMENT_BACKFILL = "provider-domain-service-backfill"
const val PROVIDER_RECURRING_SERVICE_NAME = "provider-domain-service-recurring"
const val PERSON_ENVIRONMENT_SUBSCRIBERS = "person-domain-service-subscribers"
const val FHIR_DOMAIN_ROOT_SERVICE_NAME = "fhir-domain-service"
const val FHIR_BACKFILL_SERVICE_NAME = "fhir-domain-service-backfill"
const val FHIR_RECURRING_SERVICE_NAME = "fhir-domain-service-recurring"
const val FHIR_BFF_API_ROOT_SERVICE_NAME = "fhir-bff-api"
const val HIPPOCRATES_DOMAIN_ROOT_SERVICE_NAME = "hippocrates-domain-service"
const val BUSINESS_DOMAIN_ROOT_SERVICE_NAME = "business-domain-service"
const val BUSINESS_PLATFORM_BFF_NAME = "business-platform-bff"
const val QUESTIONNAIRE_DOMAIN_ROOT_SERVICE_NAME = "questionnaire-domain-service"
const val BOTTINI_DOMAIN_ROOT_SERVICE_NAME = "bottini-domain-service"
const val BOTTINI_DOMAIN_SUBSCRIBERS_SERVICE_NAME = "bottini-domain-service-subscribers"
const val HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME = "health-condition-domain-service"
const val PRODUCT_DOMAIN_ROOT_SERVICE_NAME = "product-domain-service"
const val PRODUCT_DOMAIN_BACKFILL_SERVICE_NAME = "product-domain-service-backfill"
const val PRODUCT_DOMAIN_SUBSCRIBERS_SERVICE_NAME = "product-domain-service-subscribers"
const val ONBOARDING_DOMAIN_ROOT_SERVICE_NAME = "onboarding-domain-service"
const val ONBOARDING_RECURRING_SERVICE_NAME = "onboarding-domain-service-recurring"
const val SHERLOCK_API_ROOT_SERVICE_NAME = "sherlock-api"
const val SHERLOCK_DOMAIN_ROOT_SERVICE_NAME = "sherlock-domain-service"
const val SORTING_HAT_DOMAIN_ROOT_SERVICE_NAME = "sorting-hat-domain-service"
const val STAFF_DOMAIN_ROOT_SERVICE_NAME = "staff-domain-service"
const val ONBOARDING_WEBHOOK_ROOT_SERVICE_NAME = "onboarding-webhook-service"
const val PERSON_DOMAIN_ROOT_SERVICE_NAME = "person-domain-service"
const val COVERAGE_DOMAIN_ROOT_SERVICE_NAME = "coverage-domain-service"
const val MARAUDERS_MAP_DOMAIN_ROOT_SERVICE_NAME = "marauders-map-domain-service"
const val ACTION_PLAN_DOMAIN_ROOT_SERVICE_NAME = "action-plan-domain-service"
const val APP_CONTENT_DOMAIN_ROOT_SERVICE_NAME = "app-content-domain-service"
const val HEALTH_LOGICS_API_ROOT_SERVICE_NAME = "health-logics-api"
const val HEALTH_PLAN_DOMAIN_ROOT_SERVICE_NAME = "health-plan-domain-service"
const val SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME = "secondary-attention-domain-service"
const val CLINICAL_ACCOUNT_ROOT_SERVICE_NAME = "clinical-account-domain-service"
const val HEALTH_ANALYTICS_ROOT_SERVICE_NAME = "health-analytics-event-consumer"
const val MEMBER_ONBOARDING_ROOT_SERVICE_NAME = "member-onboarding-domain-service"
const val ITAU_INTEGRATION_ROOT_SERVICE_NAME = "itau-integration-service"
const val NULLVS_INTEGRATION_ROOT_SERVICE_NAME = "nullvs-integration-service"
const val NULLVS_INTEGRATION_ENVIRONMENT_BACKFILL = "nullvs-integration-domain-service-backfill"
const val NULLVS_INTEGRATION_ENVIRONMENT_RECURRENT = "nullvs-integration-service-recurrent"
const val NULLVS_INTEGRATION_ENVIRONMENT_ENTRY = "nullvs-integration-service-entry"
const val EITA_EXTERNAL_API_ROOT_SERVICE_NAME = "eita-external-api"
const val DUQUESA_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "duquesa-domain-service"
const val TISS_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "tiss-domain-service"
const val SCREENING_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "screening-domain-service"
const val REFUND_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "refund-domain-service"
const val SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME = "sales-channel-domain-service"
const val SALES_CHANNEL_API_ROOT_SERVICE_NAME = "sales-channel-api"
const val ZENDESK_BFF_API_ROOT_SERVICE_NAME = "zendesk-bff-api"
const val ZENDESK_INTEGRATION_SUBSCRIBERS_ROOT_SERVICE_NAME = "zendesk-integration-service-subscribers"
const val BACKOFFICE_BFF_API_ROOT_SERVICE_NAME = "backoffice-bff-api"
const val BUD_DOMAIN_SERVICE_NAME = "bud-domain-service"
const val ACQUISITION_DOMAIN_ROOT_SERVICE_NAME = "acquisition-domain-service"
const val HR_CORE_DOMAIN_ROOT_SERVICE_NAME = "hr-core-domain-service"
const val HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME = "hr-core-domain-service-subscribers"
const val HR_CORE_ENVIRONMENT_BACKFILL = "hr-core-domain-service-backfill"
const val EITA_NULLVS_INTEGRATION_ROOT_SERVICE_NAME = "eita-nullvs-integration-service"
