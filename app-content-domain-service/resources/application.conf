ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [br.com.alice.app.content.ApplicationKt.module]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

test {
    memberApiUrl = "https://localhost:8080/"

    appointmentSchedule {
        saraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=efce906a-79fc-49b3-b0db-6f66d989a400&token=true#/schedule"
    }

   gloriaMariaUrl = "https://gloria-maria-dev1.dev.alice.com.br"
}

development {
    memberApiUrl = "https://localhost:8080/"

    appointmentSchedule {
        saraCalendarUrl = "https://webview-dev1.dev.alice.com.br/scheduler/?appointmentScheduleEventTypeId=efce906a-79fc-49b3-b0db-6f66d989a400&token=true#/schedule"
    }

    gloriaMariaUrl = "https://gloria-maria-dev1.dev.alice.com.br"
}

production {
    memberApiUrl = "https://member-api.wonderland.engineering"
    memberApiUrl = "https://"${?MEMBER_API_HOST}"/"

    appointmentSchedule {
        saraCalendarUrl = "https://webview.alice.com.br/scheduler/?appointmentScheduleEventTypeId=efce906a-79fc-49b3-b0db-6f66d989a400&token=true#/schedule"
        saraCalendarUrl = ${?SARA_CALENDAR_URL}
    }

    gloriaMariaUrl = "https://gloria-maria.alice.com.br"
    gloriaMariaUrl = ${?GLORIA_MARIA_URL}
}
