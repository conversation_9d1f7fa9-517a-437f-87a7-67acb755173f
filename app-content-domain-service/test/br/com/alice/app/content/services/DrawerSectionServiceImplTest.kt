package br.com.alice.app.content.services

import br.com.alice.app.content.constants.*
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.services.drawer.CopayDrawerServiceImpl
import br.com.alice.app.content.services.drawer.DuquesaHomeDrawerServiceImpl
import br.com.alice.app.content.services.drawer.ExplorerDrawerServiceImpl
import br.com.alice.app.content.services.drawer.MainMenuDrawerServiceImpl
import br.com.alice.app.content.services.drawer.PlanDetailsDrawerServiceImpl
import br.com.alice.app.content.services.drawer.RedesignUnifiedHealthDrawerServiceImpl
import br.com.alice.app.content.services.drawer.UnifiedHealthDrawerServiceImpl
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class DrawerSectionServiceImplTest {

    private val person = TestModelFactory.buildPerson()
    private val appVersion = SemanticVersion.BASELINE_VERSION

    private val copayDrawerServiceImpl: CopayDrawerServiceImpl = mockk()
    private val mainMenuDrawerServiceImpl: MainMenuDrawerServiceImpl = mockk()
    private val duquesaHomeDrawerServiceImpl: DuquesaHomeDrawerServiceImpl = mockk()
    private val unifiedHealthDrawerServiceImpl: UnifiedHealthDrawerServiceImpl = mockk()
    private val redesignUnifiedHealthDrawerServiceImpl: RedesignUnifiedHealthDrawerServiceImpl = mockk()
    private val explorerDrawerServiceImpl: ExplorerDrawerServiceImpl = mockk()
    private val planDetailsDrawerServiceImpl: PlanDetailsDrawerServiceImpl = mockk()

    private val drawerSectionService = DrawerSectionServiceImpl(
        copayDrawerServiceImpl,
        mainMenuDrawerServiceImpl,
        duquesaHomeDrawerServiceImpl,
        unifiedHealthDrawerServiceImpl,
        redesignUnifiedHealthDrawerServiceImpl,
        explorerDrawerServiceImpl,
        planDetailsDrawerServiceImpl
    )

    companion object {
        @JvmStatic
        private fun provideExplorerDrawerScenarios() = listOf(
            ScreenType.EXPLORER_HELP_CENTER,
            ScreenType.EXPLORER_ABOUT_PLAN
        )
    }

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @AfterTest
    fun check() {
        confirmVerified()
    }

    @Test
    fun `#getByScreen should call MainMenuDrawerServiceImpl for MAIN_MENU screen`(): Unit = runBlocking {

        coEvery {
            mainMenuDrawerServiceImpl.getByScreen(
                ScreenType.MAIN_MENU,
                person.id,
                appVersion
            )
        } returns listOf(BENEFITS_DRAWER)

        val result = drawerSectionService.getByScreen(ScreenType.MAIN_MENU, person.id, appVersion).get()

        assertEquals(
            listOf(
                BENEFITS_DRAWER,
            ), result
        )

        coVerifyOnce {
            mainMenuDrawerServiceImpl.getByScreen(
                ScreenType.MAIN_MENU,
                person.id,
                appVersion
            )
        }

    }


    @Test
    fun `#getByScreen should return exception when don't have drawers to a selected screen`(): Unit = runBlocking {
        try {
            drawerSectionService.getByScreen(ScreenType.ALICE_AGORA, person.id, appVersion).get()
        } catch (error: Exception) {
            Assertions.assertThat(error).isInstanceOf(IllegalArgumentException::class.java)
        }
    }


    @Test
    fun `#getByScreen should call DuquesaHomeDrawerServiceImpl for DUQUESA_HOME screen`(): Unit = runBlocking {
        coEvery {
            duquesaHomeDrawerServiceImpl.getByScreen(
                ScreenType.DUQUESA_HOME,
                person.id,
                appVersion
            )
        } returns listOf(DUQUESA_SCHEDULE_MENU_DRAWER)

        val result = drawerSectionService.getByScreen(ScreenType.DUQUESA_HOME, person.id, appVersion).get()
        assertEquals(
            listOf(
                DUQUESA_SCHEDULE_MENU_DRAWER
            ), result
        )

        coVerifyOnce {
            duquesaHomeDrawerServiceImpl.getByScreen(
                ScreenType.DUQUESA_HOME,
                person.id,
                appVersion
            )
        }

    }


    @Test
    fun `#getByScreen should call UnifiedHealthDrawerServiceImpl for UNIFIED_HEALTH screen`(): Unit = runBlocking {

        coEvery {
            unifiedHealthDrawerServiceImpl.getByScreen(
                ScreenType.UNIFIED_HEALTH,
                person.id,
                appVersion
            )
        } returns listOf(DOCUMENTS_HISTORY_DRAWER)

        val result = drawerSectionService.getByScreen(ScreenType.UNIFIED_HEALTH, person.id, appVersion).get()
        assertEquals(
            listOf(
                DOCUMENTS_HISTORY_DRAWER
            ), result
        )

        coVerifyOnce {
            unifiedHealthDrawerServiceImpl.getByScreen(
                ScreenType.UNIFIED_HEALTH,
                person.id,
                appVersion
            )
        }
    }

    @Test
    fun `#getByScreen should call UnifiedHealthDrawerServiceImpl for UNIFIED_HEALTH screen out of AB`(): Unit =
        runBlocking {

            coEvery {
                unifiedHealthDrawerServiceImpl.getByScreen(
                    ScreenType.UNIFIED_HEALTH,
                    person.id,
                    appVersion
                )
            } returns listOf(DOCUMENTS_HISTORY_DRAWER)

            val result = drawerSectionService.getByScreen(ScreenType.UNIFIED_HEALTH, person.id, appVersion).get()
            assertEquals(
                listOf(
                    DOCUMENTS_HISTORY_DRAWER
                ), result
            )

            coVerifyOnce {
                unifiedHealthDrawerServiceImpl.getByScreen(
                    ScreenType.UNIFIED_HEALTH,
                    person.id,
                    appVersion
                )
            }
        }

    @Test
    fun `#getByScreen should return drawers for ACCREDITED_NETWORK_MENU screen`(): Unit = runBlocking {
        val result = drawerSectionService.getByScreen(ScreenType.ACCREDITED_NETWORK_MENU, person.id, appVersion).get()
        assertEquals(
            listOf(
                ACCREDITED_NETWORK_EMERGENCIES,
                ACCREDITED_NETWORK_SPECIALISTS,
                ACCREDITED_NETWORK_HOSPITALS,
                ACCREDITED_NETWORK_LABORATORIES,
                ACCREDITED_NETWORK_MATERNITIES,
            ), result
        )
    }

    @Test
    fun `#getByScreen should return drawers for ACCREDITED_NETWORK_MENU_V2 screen`(): Unit = runBlocking {
        val result = drawerSectionService.getByScreen(ScreenType.ACCREDITED_NETWORK_MENU_V2, person.id, appVersion).get()
        assertEquals(
            listOf(
                ACCREDITED_NETWORK_EMERGENCIES_V2,
                ACCREDITED_NETWORK_SPECIALISTS_V2,
                ACCREDITED_NETWORK_HOSPITALS_V2,
                ACCREDITED_NETWORK_LABORATORIES_V2,
                ACCREDITED_NETWORK_MATERNITIES_V2,
            ), result
        )
    }

    @Test
    fun `#getByScreen should return drawers for DUQUESA_MAIN_MENU screen`(): Unit = runBlocking {
        val result = drawerSectionService.getByScreen(ScreenType.DUQUESA_MAIN_MENU, person.id, appVersion).get()
        assertEquals(
            listOf(
                DUQUESA_BILLS_DRAWER,
                PERSONAL_INFO_DRAWER,
                DUQUESA_FAQ_DRAWER,
                OMBUDSMAN_DRAWER,
                USE_TERMS_DRAWER,
                LOGOUT_DRAWER
            ), result
        )
    }

    @Test
    fun `#getByScreen should return drawers for DUQUESA_PLAN_DETAILS_MENU screen`(): Unit = runBlocking {
        val result = drawerSectionService.getByScreen(ScreenType.DUQUESA_PLAN_DETAILS_MENU, person.id, appVersion).get()
        assertEquals(
            listOf(
                DUQUESA_CONTRACT_DRAWER,
            ), result
        )
    }

    @Test
    fun `#getByScreen should return drawers for DUQUESA_ACCREDITED_NETWORK_MENU screen`(): Unit = runBlocking {
        val result =
            drawerSectionService.getByScreen(ScreenType.DUQUESA_ACCREDITED_NETWORK_MENU, person.id, appVersion).get()
        assertEquals(
            listOf(
                DUQUESA_ACCREDITED_NETWORK_EMERGENCIES,
                DUQUESA_ACCREDITED_NETWORK_SPECIALISTS,
                DUQUESA_ACCREDITED_NETWORK_HOSPITALS,
                DUQUESA_ACCREDITED_NETWORK_LABORATORIES,
                DUQUESA_ACCREDITED_NETWORK_MATERNITIES,
            ), result
        )
    }

    @Test
    fun `#getByScreen should return drawers for DUQUESA_SCHEDULE_MENU screen`(): Unit = runBlocking {
        val result = drawerSectionService.getByScreen(ScreenType.DUQUESA_SCHEDULE_MENU, person.id, appVersion).get()
        assertEquals(
            listOf(
                DUQUESA_SCHEDULE_SEARCH,
            ), result
        )
    }

    @Test
    fun `#getByScreen should call CopayDrawerService for COPAY_MENU screen`(): Unit = runBlocking {

        coEvery {
            copayDrawerServiceImpl.getByScreen(
                ScreenType.COPAY_MENU,
                person.id,
                appVersion
            )
        } returns listOf(COPAY_DEFINITION)

        val result = drawerSectionService.getByScreen(ScreenType.COPAY_MENU, person.id, appVersion).get()

        assertEquals(
            listOf(
                COPAY_DEFINITION,
            ), result
        )

        coVerifyOnce {
            copayDrawerServiceImpl.getByScreen(
                ScreenType.COPAY_MENU,
                person.id,
                appVersion
            )
        }
    }

    @Test
    fun `#getByScreen should call RedesignUnifiedHealthDrawerServiceImpl for REDESIGN_UNIFIED_HEALTH screen`(): Unit = runBlocking {

        coEvery {
            redesignUnifiedHealthDrawerServiceImpl.getByScreen(
                ScreenType.REDESIGN_UNIFIED_HEALTH,
                person.id,
                appVersion
            )
        } returns listOf(REFUND_DRAWER)

        val result = drawerSectionService.getByScreen(ScreenType.REDESIGN_UNIFIED_HEALTH, person.id, appVersion).get()

        assertEquals(
            listOf(
                REFUND_DRAWER,
            ), result
        )

        coVerifyOnce {
            redesignUnifiedHealthDrawerServiceImpl.getByScreen(
                ScreenType.REDESIGN_UNIFIED_HEALTH,
                person.id,
                appVersion
            )
        }
    }

    @ParameterizedTest
    @MethodSource("provideExplorerDrawerScenarios")
    fun `#getByScreen should call ExplorerDrawerServiceImpl for explorer drawer screen types`(
        screenType: ScreenType
    ): Unit = runBlocking {
        coEvery {
            explorerDrawerServiceImpl.getByScreen(
                screenType,
                person.id,
                appVersion
            )
        } returns explorerHelpCenterDrawers

        val result = drawerSectionService.getByScreen(screenType, person.id, appVersion)

        ResultAssert.assertThat(result).isSuccessWithData(explorerHelpCenterDrawers)

        coVerifyOnce { explorerDrawerServiceImpl.getByScreen(any(), any(), any()) }
    }

    @Test
    fun `#getByScreen should return drawers for PLAN_DETAILS_MENU screen`(): Unit = runBlocking {
        val expectedDrawers = listOf(
            PLAN_DETAILS_ACCOMMODATION_SUBMENU_DRAWER,
            PLAN_DETAILS_NATIONAL_COVERAGE_SUBMENU_DRAWER,
            PLAN_DETAILS_COVERAGE_AND_LACK_SUBMENU_DRAWER,
            PLAN_DETAILS_HOSPITAL_SUBMENU_DRAWER,
            PLAN_DETAILS_LABORATORY_SUBMENU_DRAWER,
            PLAN_DETAILS_SPECIALIST_DETAILS_SUBMENU_DRAWER,
            PLAN_DETAILS_ALICE_AGORA_SUBMENU_DRAWER,
            PLAN_DETAILS_COPAY_SUBMENU_DRAWER,
            PLAN_DETAILS_REFUND_SUBMENU_DRAWER,
            PLAN_DETAILS_CONTRACT_SUBMENU_DRAWER
        )

        coEvery {
            planDetailsDrawerServiceImpl.getByScreen(ScreenType.PLAN_DETAILS_MENU, person.id, appVersion)
        } returns expectedDrawers

        val result = drawerSectionService.getByScreen(ScreenType.PLAN_DETAILS_MENU, person.id, appVersion)

        ResultAssert.assertThat(result).isSuccessWithData(expectedDrawers)

        coVerifyOnce { planDetailsDrawerServiceImpl.getByScreen(any(), any(), any()) }
    }
}
