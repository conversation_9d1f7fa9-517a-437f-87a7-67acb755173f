package br.com.alice.app.content.services.router

import br.com.alice.app.content.client.screens.AccreditedNetworkMenuService
import br.com.alice.app.content.client.screens.DuquesaHomeScreenService
import br.com.alice.app.content.client.screens.DuquesaMainMenuScreenService
import br.com.alice.app.content.client.screens.DuquesaServiceScreenService
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class DuquesaScreensServiceImplTest {

    private val duquesaMainMenuScreenService: DuquesaMainMenuScreenService = mockk()
    private val duquesaHomeScreenService: DuquesaHomeScreenService = mockk()
    private val duquesaServiceScreenService: DuquesaServiceScreenService = mockk()
    private val accreditedNetworkMenuService: AccreditedNetworkMenuService = mockk()
    private val service = DuquesaScreensServiceImpl(
        duquesaHomeScreenService,
        duquesaServiceScreenService,
        duquesaMainMenuScreenService,
        accreditedNetworkMenuService
    )

    private val appVersion = SemanticVersion("1.0.0")
    private val personId = PersonId()

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_HOME`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.DUQUESA_HOME.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { duquesaHomeScreenService.getByPersonId(personId) } returns home.success()

            val result = service.getHome(personId)

            assertThat(result).isSuccessWithData(home)
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_SCHEDULE_MENU`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.DUQUESA_SCHEDULE_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { duquesaHomeScreenService.getScheduleMenu(personId) } returns home.success()

            val result = service.getScheduleMenu(personId)

            assertThat(result).isSuccessWithData(home)
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_SCHEDULE_SHEET`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.DUQUESA_SCHEDULE_OTHER_SPECIALTY_MODAL.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { duquesaHomeScreenService.getScheduleOtherSpecialtyModal(personId) } returns home.success()

            val result = service.getScheduleOtherSpecialtyModal(personId)

            assertThat(result).isSuccessWithData(home)
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_ACCREDITED_NETWORK_MENU`(): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.DUQUESA, "use_alice_accredited_network_menu", false) {
                val home = ScreensTransport(
                    id = ScreenType.DUQUESA_ACCREDITED_NETWORK_MENU.value,
                    layout = ScreenLayout(type = "single_column", body = emptyList())
                )

                coEvery { duquesaHomeScreenService.getAccreditedNetworkMenu(personId) } returns home.success()
                val appVersion = SemanticVersion("1.0.0")
                val result = service.getAccreditedNetworkMenu(appVersion, personId)

                assertThat(result).isSuccessWithData(home)
            }
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_ACCREDITED_NETWORK_APS_SHEET`(): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.DUQUESA, "use_alice_accredited_network_menu", false) {
                val home = ScreensTransport(
                    id = ScreenType.DUQUESA_ACCREDITED_NETWORK_EMERGENCY_MODAL.value,
                    layout = ScreenLayout(type = "single_column", body = emptyList())
                )

                coEvery { duquesaHomeScreenService.getAccreditedNetworkEmergencyModal(personId) } returns home.success()

                val result = service.getAccreditedNetworkEmergencyModal(personId)

                assertThat(result).isSuccessWithData(home)
            }
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_SERVICE`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.DUQUESA_SERVICE.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { duquesaServiceScreenService.getByPersonId(personId) } returns home.success()

            val result = service.getService(personId)

            assertThat(result).isSuccessWithData(home)
        }

    @Test
    fun `#getByPersonIdAndScreenType returns for DUQUESA_MAIN_MENU`(): Unit =
        runBlocking {
            val home = ScreensTransport(
                id = ScreenType.DUQUESA_MAIN_MENU.value,
                layout = ScreenLayout(type = "single_column", body = emptyList())
            )

            coEvery { duquesaMainMenuScreenService.getMainMenu(personId) } returns home.success()

            val result = service.getMainMenu(personId)

            assertThat(result).isSuccessWithData(home)
        }

}


