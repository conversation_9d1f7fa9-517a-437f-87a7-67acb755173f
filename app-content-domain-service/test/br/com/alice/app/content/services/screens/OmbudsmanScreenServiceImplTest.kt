package br.com.alice.app.content.services.screens

import br.com.alice.app.content.constants.OmbudsmanScreenConstants
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenSafeArea
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.common.core.PersonId
import br.com.alice.common.mobile.Platform
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class OmbudsmanScreenServiceImplTest {
    private val service = OmbudsmanScreenServiceImpl()

    private val personId = PersonId()

    @BeforeTest
    fun before() {
        mockkObject(ScreenModule)
    }

    @AfterTest
    fun after() {
        unmockkObject(ScreenModule)
    }

    @Test
    fun `#getMenuScreen should return screen with correct sections`(): Unit =
        runBlocking {
            val result = service.getMenuScreen(personId).get()

            val expectedResult = ScreensTransport(
                id = ScreenType.OMBUDSMAN_MENU.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar("Ouvidoria"),
                    body = listOf(
                        ScreenModule.getImageSectionWithContent(
                            "image",
                            "https://alice-member-app-assets.s3.amazonaws.com/ombudsman/speak.svg",
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextSectionWithContent(
                            "title",
                            "Ajude a Alice a entender sobre seu motivo de contato com a Ouvidoria",
                            SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextCardSection(
                            "low_risk",
                            "Tenho uma dúvida ou elogio",
                            RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "action" to RemoteAction(
                                        method = RemoteActionMethod.GET,
                                        endpoint = "/app_content/screen/ombudsman_low_risk",
                                        params = mapOf(
                                            "screen_id" to "ombudsman_low_risk"
                                        )
                                    )
                                )
                            ),
                        ),
                        ScreenModule.getTextCardSection(
                            "medium_risk",
                            "Tenho um problema e é a primeira vez que estou tentando resolver",
                            RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "action" to RemoteAction(
                                        method = RemoteActionMethod.GET,
                                        endpoint = "/app_content/screen/ombudsman_medium_risk",
                                        params = mapOf(
                                            "screen_id" to "ombudsman_medium_risk"
                                        )
                                    )
                                )
                            ),
                        ),
                        ScreenModule.getTextCardSection(
                            "high_risk",
                            "Tentei resolver um problema de saúde ou administrativo em outros canais da Alice e não consegui solucionar",
                            RemoteAction(
                                mobileRoute = ActionRouting.EXTERNAL_APP,
                                params = mapOf(
                                    "link" to "https://ouvidoria.alice.com.br"
                                )
                            ),
                        )
                    )
                )
            )


            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

    @Test
    fun `#getLowRiskScreen should return screen with correct sections`(): Unit =
        runBlocking {
            val result = service.getLowRiskScreen(personId, Platform.ANDROID).get()

            val expectedResult =  ScreensTransport(
                id = ScreenType.OMBUDSMAN_LOW_RISK.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar("Ouvidoria"),
                    body = listOfNotNull(
                        ScreenModule.getImageSectionWithContent(
                            "image",
                            "https://alice-member-app-assets.s3.amazonaws.com/ombudsman/speak.svg",
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextSectionWithContent(
                            "title",
                            "Certo! Vamos lá, escolha uma opção:",
                            SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextCardSection(
                            "review_app",
                            "Quero deixar um elogio na loja do App",
                            RemoteAction(
                                mobileRoute = ActionRouting.EXTERNAL_APP,
                                params = mapOf(
                                    "link" to Platform.ANDROID.downloadUrl
                                )
                            ),
                        ),
                        ScreenModule.getTextCardSection(
                            "medium_risk",
                            "Tenho uma dúvida",
                            RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "action" to RemoteAction(
                                        method = RemoteActionMethod.GET,
                                        endpoint = "/app_content/screen/ombudsman_medium_risk",
                                        params = mapOf(
                                            "screen_id" to "ombudsman_medium_risk"
                                        )
                                    )
                                )
                            ),
                        ),
                    )
                )
            )
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

    @Test
    fun `#getMediumRiskScreen should return screen with correct sections`(): Unit =
        runBlocking {
            val result = service.getMediumRiskScreen(personId).get()

            val expectedResult = ScreensTransport(
                id = ScreenType.OMBUDSMAN_MEDIUM_RISK.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar(OmbudsmanScreenConstants.MediumRisk.APP_BAR_TITLE),
                    body = listOfNotNull(
                        ScreenModule.getImageSectionWithContent(
                            "image",
                            "https://alice-member-app-assets.s3.amazonaws.com/ombudsman/speak.svg",
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextSectionWithContent(
                            "title",
                            "Certo! Para dúvidas e problemas iniciais recomendamos a equipe do Alice Agora. Como prefere seguir?",
                            SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                            Alignment.LEFT
                        ),
                        ScreenModule.getTextCardSection(
                            "alice_agora",
                            "**{Recomendado}** Falar com o atendimento do Alice Agora (resposta em até 24h úteis)",
                            RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA),
                        ),
                        ScreenModule.getTextCardSection(
                            "faq",
                            "Ler as Perguntas mais frequentes",
                            RemoteAction(mobileRoute = ActionRouting.HELP_CENTER),
                        ),
                        ScreenModule.getTextCardSection(
                            "high_risk",
                            "Ainda prefiro falar com a Ouvidoria (resposta em até 7 dias úteis)",
                            RemoteAction(
                                mobileRoute = ActionRouting.EXTERNAL_APP,
                                params = mapOf(
                                    "link" to "https://ouvidoria.alice.com.br"
                                )
                            ),
                        ),
                    )
                )
            )
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }
}
