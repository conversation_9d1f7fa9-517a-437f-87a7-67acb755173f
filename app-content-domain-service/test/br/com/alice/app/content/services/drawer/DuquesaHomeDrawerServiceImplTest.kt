package br.com.alice.app.content.services.drawer

import br.com.alice.app.content.constants.DUQUESA_ACCREDITED_NETWORK_MENU_DRAWER
import br.com.alice.app.content.constants.DUQUESA_FAQ_DRAWER
import br.com.alice.app.content.constants.DUQUESA_SCHEDULE_MENU_DRAWER
import br.com.alice.app.content.constants.PROCEDURE_AUTHORIZATION_DRAWER
import br.com.alice.app.content.model.ScreenType
import br.com.alice.common.core.PersonId
import br.com.alice.common.mobile.SemanticVersion
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class DuquesaHomeDrawerServiceImplTest {

    private val duquesaHomeDrawerServiceImpl = DuquesaHomeDrawerServiceImpl()

    private val personId = PersonId()
    private val enabledAppVersion = SemanticVersion("10.0.1")

    @Test
    fun `#getByScreen should get menu with PROCEDURE_AUTHORIZATION_DRAWER`(): Unit =
        runBlocking {

            val result =
                duquesaHomeDrawerServiceImpl.getByScreen(ScreenType.DUQUESA_HOME, personId, enabledAppVersion)

            val expectedResult = listOfNotNull(
                DUQUESA_SCHEDULE_MENU_DRAWER,
                DUQUESA_ACCREDITED_NETWORK_MENU_DRAWER,
                PROCEDURE_AUTHORIZATION_DRAWER,
                DUQUESA_FAQ_DRAWER,
            )

            assertThat(result).isEqualTo(expectedResult)
        }
}
