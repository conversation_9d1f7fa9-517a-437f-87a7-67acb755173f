package br.com.alice.app.content.constants

import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.SectionTextLayout

const val EMPTY_STATE_SECTION_IMAGE_ID = "empty-state-image"
const val EMPTY_STATE_SECTION_TEXT_TITLE_ID = "empty-state-title"
const val EMPTY_STATE_SECTION_TEXT_DESCRIPTION_ID = "empty-state-description"

const val EMPTY_STATE_PDA_ONBOARDING_WARNING_TITLE = "Finalize seu 1º acesso e receba seu Plano de ação."
const val EMPTY_STATE_PDA_ONBOARDING_WARNING_DESCRIPTION = "Suas respostas vão ajudar a equipe de saúde da Alice entender mais sobre sua saúde e cria seu Plano de ação.."

const val EMPTY_STATE_PDA_EMPTY_DEMANDS_TITLE = "Seu primeiro Plano de ação está a caminho!"
const val EMPTY_STATE_PDA_EMPTY_DEMANDS_DESCRIPTION = "Plano de ação são tarefas criadas, pela equipe de saúde da Alice, a partir das suas respostas no formulário de imersão."

const val EMPTY_STATE_HISTORY_PDA_EMPTY_DEMANDS_TITLE = "Seu histórico está vazio!"
const val EMPTY_STATE_HISTORY_PDA_EMPTY_DEMANDS_DESCRIPTION = "Seu histórico será visível assim que alguma demanda for concluída!"

const val PDA_APP_BAR_TITLE = "Plano de ação"

val screenEmptyDemand = ScreensTransport(
    id = ScreenType.HEALTH_ALL_DEMANDS.value,
    layout = ScreenLayout(
        type = "single_column",
        appBar = ScreenModule.getDefaultAppBar(PDA_APP_BAR_TITLE),
        body = listOfNotNull(
            ScreenModule.getImageSectionWithContent(
                EMPTY_STATE_SECTION_IMAGE_ID,
                "https://alice-member-app-assets.s3.amazonaws.com/empty_pages/paper_empty_pda.png",

                ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_TITLE_ID,
                EMPTY_STATE_PDA_EMPTY_DEMANDS_TITLE,
                SectionTextLayout.TITLE_LARGE_HIGHLIGHT
            ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_DESCRIPTION_ID,
                EMPTY_STATE_PDA_EMPTY_DEMANDS_DESCRIPTION,
                SectionTextLayout.TEXT
            ),
        )
    ),
    properties = ScreenProperties(
        alignment = ScreenAlignment(
            vertical = ScreenAlignmentType.CENTER
        )
    )
)

val screenOnboardingInProgress = ScreensTransport(
    id = ScreenType.HEALTH_ALL_DEMANDS.value,
    layout = ScreenLayout(
        type = "single_column",
        appBar = ScreenModule.getDefaultAppBar(PDA_APP_BAR_TITLE),
        body = listOfNotNull(
            ScreenModule.getImageSectionWithContent(
                EMPTY_STATE_SECTION_IMAGE_ID,
                "https://alice-member-app-assets.s3.amazonaws.com/member_onboarding/onboarding.png",
            ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_TITLE_ID,
                EMPTY_STATE_PDA_ONBOARDING_WARNING_TITLE,
                SectionTextLayout.TITLE_LARGE_HIGHLIGHT
            ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_DESCRIPTION_ID,
                EMPTY_STATE_PDA_ONBOARDING_WARNING_DESCRIPTION,
                SectionTextLayout.TEXT
            ),
        )
    ),
    properties = ScreenProperties(
        alignment = ScreenAlignment(
            vertical = ScreenAlignmentType.CENTER
        )
    )
)

val screenHistoryEmptyDemand = ScreensTransport(
    id = ScreenType.HEALTH_HISTORY.value,
    layout = ScreenLayout(
        type = "single_column",
        appBar = ScreenModule.getDefaultAppBar(PDA_APP_BAR_TITLE),
        body = listOfNotNull(
            ScreenModule.getImageSectionWithContent(
                EMPTY_STATE_SECTION_IMAGE_ID,
                "https://alice-member-app-assets.s3.amazonaws.com/empty_pages/paper_empty_pda.png",

                ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_TITLE_ID,
                EMPTY_STATE_HISTORY_PDA_EMPTY_DEMANDS_TITLE,
                SectionTextLayout.TITLE_LARGE_HIGHLIGHT
            ),
            ScreenModule.getTextSectionWithContent(
                EMPTY_STATE_SECTION_TEXT_DESCRIPTION_ID,
                EMPTY_STATE_HISTORY_PDA_EMPTY_DEMANDS_DESCRIPTION,
                SectionTextLayout.TEXT
            ),
        )
    ),
    properties = ScreenProperties(
        alignment = ScreenAlignment(
            vertical = ScreenAlignmentType.CENTER
        )
    )
)
