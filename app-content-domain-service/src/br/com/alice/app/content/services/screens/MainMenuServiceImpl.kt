package br.com.alice.app.content.services.screens

import br.com.alice.app.content.client.screens.MainMenuService
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result

const val APP_VERSION_SECTION_ID = "app_version"
class MainMenuServiceImpl(): MainMenuService {

    private val listWithMGM = listOfNotNull(
        ScreenModule.getModuleSection(ScreenModule.HEALTH_SERVICE_CARDS_MODULE),
        ScreenModule.getDrawersByScreen(ScreenModule.MAIN_MENU_DRAWER_LIST_MODULE, ScreenType.MAIN_MENU),
        ScreenModule.getModuleSection(ScreenModule.MGM_MODULE),
        ScreenModule.getAppVersionTextSection(APP_VERSION_SECTION_ID)
    )

    private val listWithoutMGM = listOfNotNull(
        ScreenModule.getModuleSection(ScreenModule.HEALTH_SERVICE_CARDS_MODULE),
        ScreenModule.getDrawersByScreen(ScreenModule.MAIN_MENU_DRAWER_LIST_MODULE, ScreenType.MAIN_MENU),
        ScreenModule.getAppVersionTextSection(APP_VERSION_SECTION_ID)
    )
    override suspend fun getMainMenu(personId: PersonId): Result<ScreensTransport, Throwable> = Result.of {
        ScreensTransport(
            id = ScreenType.MAIN_MENU.value,
            layout = ScreenLayout(
                type = "single_column",
                body = parseMainMenuBody()
            )
        )
    }

    private fun parseMainMenuBody() = if (isMemberGetMemberDisabled()) {
        listWithoutMGM
    } else {
        listWithMGM
    }

    override suspend fun getPlanDetailsScreen(): Result<ScreensTransport, Throwable> = Result.of {
        ScreensTransport(
            id = ScreenType.PLAN_DETAILS_MENU.value,
            layout = ScreenLayout(
                type = "single_column",
                appBar = ScreenModule.getDefaultAppBar("Detalhes do Plano"),
                body = listOfNotNull(
                    ScreenModule.getDrawersByScreen(ScreenModule.MAIN_MENU_DRAWER_LIST_MODULE, ScreenType.PLAN_DETAILS_MENU),
                    getEmptySection()
                )
            )
        )
    }

    private fun isMemberGetMemberDisabled() =
        FeatureService.get(
            FeatureNamespace.ALICE_APP,
            "disable_member_get_member",
            false
        )

    private fun getEmptySection() =
        ScreenModule.getTextSectionWithContent(
            sectionId = "empty_space",
            text = "",
            layout = SectionTextLayout.TEXT,
            alignment = Alignment.CENTER,
            verticalPadding = SectionPadding.P0,
            horizontalPadding = SectionPadding.P0
        )
}
