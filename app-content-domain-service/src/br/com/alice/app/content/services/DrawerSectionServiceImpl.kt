package br.com.alice.app.content.services

import br.com.alice.app.content.client.sections.DrawerSectionService
import br.com.alice.app.content.constants.drawersByScreen
import br.com.alice.app.content.model.DrawerTransport
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.services.drawer.CopayDrawerServiceImpl
import br.com.alice.app.content.services.drawer.DuquesaHomeDrawerServiceImpl
import br.com.alice.app.content.services.drawer.ExplorerDrawerServiceImpl
import br.com.alice.app.content.services.drawer.MainMenuDrawerServiceImpl
import br.com.alice.app.content.services.drawer.PlanDetailsDrawerServiceImpl
import br.com.alice.app.content.services.drawer.RedesignUnifiedHealthDrawerServiceImpl
import br.com.alice.app.content.services.drawer.UnifiedHealthDrawerServiceImpl
import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import com.github.kittinunf.result.Result

class DrawerSectionServiceImpl(
    private val copayDrawerServiceImpl: CopayDrawerServiceImpl,
    private val mainMenuDrawerServiceImpl: MainMenuDrawerServiceImpl,
    private val duquesaHomeDrawerServiceImpl: DuquesaHomeDrawerServiceImpl,
    private val unifiedHealthDrawerServiceImpl: UnifiedHealthDrawerServiceImpl,
    private val redesignUnifiedHealthDrawerServiceImpl: RedesignUnifiedHealthDrawerServiceImpl,
    private val explorerDrawerServiceImpl: ExplorerDrawerServiceImpl,
    private val planDetailsDrawerServiceImpl: PlanDetailsDrawerServiceImpl
): DrawerSectionService {

    override suspend fun getByScreen(
        screenType: ScreenType,
        personId: PersonId,
        appVersion: SemanticVersion
    ): Result<List<DrawerTransport>, Throwable> {
        return Result.of {
            when {
                screenType == ScreenType.MAIN_MENU ->  mainMenuDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType == ScreenType.UNIFIED_HEALTH -> unifiedHealthDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType == ScreenType.DUQUESA_HOME -> duquesaHomeDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType == ScreenType.REDESIGN_UNIFIED_HEALTH -> redesignUnifiedHealthDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType == ScreenType.PLAN_DETAILS_MENU -> planDetailsDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType.isCopayContext() -> copayDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                screenType.isExplorerContext() -> explorerDrawerServiceImpl.getByScreen(screenType, personId, appVersion)
                drawersByScreen.contains(screenType) -> drawersByScreen[screenType]!!
                else -> {
                    logger.error(
                        "DrawerSectionServiceImpl - drawer not found for this screen",
                        "screen_type" to screenType.value
                    )
                    throw IllegalArgumentException("drawer not found for $screenType screen")
                }
            }
        }
    }
}
