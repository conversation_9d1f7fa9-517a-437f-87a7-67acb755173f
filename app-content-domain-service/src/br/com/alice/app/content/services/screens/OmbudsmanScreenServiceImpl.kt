package br.com.alice.app.content.services.screens

import br.com.alice.app.content.client.screens.OmbudsmanScreenService
import br.com.alice.app.content.constants.OmbudsmanScreenConstants
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenModule
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreenSafeArea
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.Platform
import com.github.kittinunf.result.Result


class OmbudsmanScreenServiceImpl: OmbudsmanScreenService {

    override suspend fun getMenuScreen(personId: PersonId): Result<ScreensTransport, Throwable> =
        coResultOf {
            ScreensTransport(
                id = ScreenType.OMBUDSMAN_MENU.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar(OmbudsmanScreenConstants.Menu.APP_BAR_TITLE),
                    body = listOfNotNull(
                        getImageSection(OmbudsmanScreenConstants.Assets.SPEAK),
                        getTitleSection(OmbudsmanScreenConstants.Menu.TITLE),
                        getOptionSection(
                            "low_risk",
                            OmbudsmanScreenConstants.Menu.OPTION_LOW_RISK,
                            getScreenRemoteAction(ScreenType.OMBUDSMAN_LOW_RISK)
                        ),
                        getOptionSection(
                            "medium_risk",
                            OmbudsmanScreenConstants.Menu.OPTION_MEDIUM_RISK,
                            getScreenRemoteAction(ScreenType.OMBUDSMAN_MEDIUM_RISK)
                        ),
                        getOptionSection(
                            "high_risk",
                            OmbudsmanScreenConstants.Menu.OPTION_HIGH_RISK,
                            getExternalRemoteAction(OmbudsmanScreenConstants.Assets.OMBUDSMAN_URL)
                        ),
                    )
                )
            )
        }

    override suspend fun getLowRiskScreen(personId: PersonId, platform: Platform): Result<ScreensTransport, Throwable> =
        coResultOf {
            ScreensTransport(
                id = ScreenType.OMBUDSMAN_LOW_RISK.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar(OmbudsmanScreenConstants.LowRisk.APP_BAR_TITLE),
                    body = listOfNotNull(
                        getImageSection(OmbudsmanScreenConstants.Assets.SPEAK),
                        getTitleSection(OmbudsmanScreenConstants.LowRisk.TITLE),
                        getOptionSection(
                            "review_app",
                            OmbudsmanScreenConstants.LowRisk.OPTION_REVIEW_APP,
                            getExternalRemoteAction(platform.downloadUrl)
                        ),
                        getOptionSection(
                            "medium_risk",
                            OmbudsmanScreenConstants.LowRisk.OPTION_MEDIUM_RISK,
                            getScreenRemoteAction(ScreenType.OMBUDSMAN_MEDIUM_RISK)
                        ),
                    )
                )
            )
        }

    override suspend fun getMediumRiskScreen(personId: PersonId): Result<ScreensTransport, Throwable> =
        coResultOf {
            ScreensTransport(
                id = ScreenType.OMBUDSMAN_MEDIUM_RISK.value,
                properties = ScreenProperties(
                    alignment = ScreenAlignment(
                        vertical = ScreenAlignmentType.END,
                        horizontal = ScreenAlignmentType.START,
                    ),
                    safeArea = ScreenSafeArea()
                ),
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = ScreenModule.getDefaultAppBar(OmbudsmanScreenConstants.MediumRisk.APP_BAR_TITLE),
                    body = listOfNotNull(
                        getImageSection(OmbudsmanScreenConstants.Assets.SPEAK),
                        getTitleSection(OmbudsmanScreenConstants.MediumRisk.TITLE),
                        getOptionSection(
                            "alice_agora",
                            OmbudsmanScreenConstants.MediumRisk.OPTION_ALICE_AGORA,
                            getNavigationRemoteAction(ActionRouting.ALICE_AGORA)
                        ),
                        getOptionSection(
                            "faq",
                            OmbudsmanScreenConstants.MediumRisk.OPTION_FAQ,
                            getNavigationRemoteAction(ActionRouting.HELP_CENTER)
                        ),
                        getOptionSection(
                            "high_risk",
                            OmbudsmanScreenConstants.MediumRisk.OPTION_HIGH_RISK,
                            getExternalRemoteAction(OmbudsmanScreenConstants.Assets.OMBUDSMAN_URL)
                        ),
                    )
                )
            )
        }

    private fun getImageSection(imageUrl: String) = ScreenModule.getImageSectionWithContent(
        "image",
        imageUrl,
        Alignment.LEFT
    )

    private fun getTitleSection(title: String) = ScreenModule.getTextSectionWithContent(
        "title",
        title,
        SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
        Alignment.LEFT
    )

    private fun getDescriptionSection(description: String) = ScreenModule.getTextSectionWithContent(
        "description",
        description,
        SectionTextLayout.TITLE_SMALL,
        Alignment.LEFT
    )

    private fun getOptionSection(optionId: String, title: String, onCardClick: RemoteAction) =
        ScreenModule.getTextCardSection(
            optionId,
            title,
            onCardClick,
        )

    private fun getScreenRemoteAction(screenType: ScreenType) = RemoteAction(
        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
        params = mapOf(
            "action" to RemoteAction(
                method = RemoteActionMethod.GET,
                endpoint = OmbudsmanScreenConstants.Endpoint.getScreenUrl(screenType),
                params = mapOf(
                    "screen_id" to screenType.lowercase()
                )
            )
        )
    )

    private fun getNavigationRemoteAction(route: ActionRouting) = RemoteAction(
        mobileRoute = route
    )

    private fun getExternalRemoteAction(url: String): RemoteAction {
        logger.info("OmbudsmanScreenServiceImpl: external remote act", "store" to url)
        return RemoteAction(
            mobileRoute = ActionRouting.EXTERNAL_APP,
            params = mapOf(
                "link" to url
            )
        )
    }
}
