package br.com.alice.app.content.services

import br.com.alice.app.content.client.PlanDetailsMenuService
import br.com.alice.app.content.model.MemberProductDetails
import br.com.alice.common.core.PersonId
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.RefundType
import br.com.alice.person.client.MemberService
import br.com.alice.product.client.CoPaymentCostInfoService
import br.com.alice.product.client.ProductService
import br.com.alice.product.client.RefundCostService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class PlanDetailsMenuServiceImpl(
    private val memberService: MemberService,
    private val productService: ProductService,
    private val coPaymentCostInfoService: CoPaymentCostInfoService,
    private val refundCostService: RefundCostService
): PlanDetailsMenuService, Spannable {

    override suspend fun getProductDetails(personId: PersonId) = span("getProductDetails") { span ->
        span.setAttribute("person_id", personId)
        memberService.getCurrent(personId).flatMap { member->
            productService.getProductWithProviders(member.productId)
                .map {
                    val (coPayment, refund) = getCostInfo(it.product, member.id)

                    MemberProductDetails(
                        product = it.product,
                        providers = it.providers,
                        coPayment = coPayment ?: emptyList(),
                        refund = emptyList(),
                        refundCost = refund
                    )
                }.recordResult(span)
        }
    }

    private suspend fun getCostInfo(product: Product, memberId: UUID) = coroutineScope {
        val coPayment = async { getCoPaymentInfo(product) }
        val refund = async { getRefundInfo(product, memberId) }

        (coPayment.await() to refund.await())
    }

    private suspend fun getCoPaymentInfo(product: Product) = span("getCoPaymentInfo") { span ->
        span.setAttribute("product_id", product.id)
        span.setAttribute("product_tier", product.tier)
        span.setAttribute("product_copayment", product.coPayment)

        product.takeIf { it.coPayment != CoPaymentType.NONE && it.tier != null }
            ?.let { coPaymentCostInfoService.getCoPaymentByProductTier(it.tier!!).getOrNull() }
    }

    private suspend fun getRefundInfo(product: Product, memberId: UUID) = span("getRefundInfo") { span ->
        span.setAttribute("product_id", product.id)
        span.setAttribute("product_tier", product.tier)
        span.setAttribute("product_refund", product.refund)

        product.takeIf { it.refund != RefundType.NONE && it.tier != null }
            ?.let { refundCostService.getRefundCostInfoByMemberId(memberId).getOrNull() }
    }
}
