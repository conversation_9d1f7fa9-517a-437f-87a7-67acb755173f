package br.com.alice.app.content.services.router

import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.client.screens.AccreditedNetworkMenuService
import br.com.alice.app.content.client.screens.AliceNowScreenService
import br.com.alice.app.content.client.screens.AppointmentHubScreenService
import br.com.alice.app.content.client.screens.CopayDetailsScreenService
import br.com.alice.app.content.client.screens.CopayScreenService
import br.com.alice.app.content.client.screens.DemandScreenService
import br.com.alice.app.content.client.screens.ExplorerScreenService
import br.com.alice.app.content.client.screens.HealthScreenService
import br.com.alice.app.content.client.screens.MainMenuService
import br.com.alice.app.content.client.screens.MemberProfileScreenService
import br.com.alice.app.content.client.screens.OmbudsmanScreenService
import br.com.alice.app.content.client.screens.RedesignHealthPlanScreenService
import br.com.alice.app.content.client.screens.RedesignUnifiedHealthScreenService
import br.com.alice.app.content.client.screens.TestResultScreenService
import br.com.alice.app.content.client.screens.UnifiedHomeScreenService
import br.com.alice.app.content.constants.CHANNELS_SCREEN
import br.com.alice.app.content.constants.SCHEDULE_SCREEN_WITH_APP_BAR
import br.com.alice.app.content.model.AliceScreensData
import br.com.alice.app.content.model.PaginationLayout
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.Platform
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.Result
import java.util.UUID

class AliceScreensServiceImpl(
    private val aliceNowScreenService: AliceNowScreenService,
    private val unifiedHomeScreenService: UnifiedHomeScreenService,
    private val healthScreenService: HealthScreenService,
    private val demandScreenService: DemandScreenService,
    private val explorerScreenService: ExplorerScreenService,
    private val mainMenuService: MainMenuService,
    private val memberProfileScreenService: MemberProfileScreenService,
    private val accreditedNetworkMenuService: AccreditedNetworkMenuService,
    private val examsScreenService: TestResultScreenService,
    private val copayScreenService: CopayScreenService,
    private val copayDetailsScreenService: CopayDetailsScreenService,
    private val ombudsmanScreenService: OmbudsmanScreenService,
    private val redesignUnifiedHealthScreenService: RedesignUnifiedHealthScreenService,
    private val redesignHealthPlanScreenService: RedesignHealthPlanScreenService,
    private val appointmentHubScreenService: AppointmentHubScreenService,

    ) : AliceScreensService, Spannable {

    override suspend fun getUnifiedHealthScreen(
        personId: PersonId,
        question: QuestionnaireQuestionResponse?
    ): Result<ScreensTransport, Throwable> = unifiedHomeScreenService.getUnifiedScreenByPersonId(personId, question)

    override suspend fun getHealthAllDemandsScreen(
        personId: PersonId,
        filterByTaskType: String?,
        appVersion: SemanticVersion?
    ): Result<ScreensTransport, Throwable> =
        healthScreenService.getFirstPageDemandsByPersonId(personId, filterByTaskType)

    override suspend fun getPaginatedHealthDemandsScreen(
        personId: PersonId,
        offset: Int,
        limit: Int,
        filterByTaskType: String?
    ): Result<PaginationLayout, Throwable> =
        healthScreenService.getPaginatedDemandsByPersonId(personId, offset, limit, filterByTaskType)

    override suspend fun getCopayScreens(
        personId: PersonId,
        screenType: ScreenType,
    ): Result<ScreensTransport, Throwable> = Result.of {
        when (screenType) {
            ScreenType.COPAY_MENU -> getCopayMenuScreenByPersonId(personId)
            ScreenType.COPAY_LIMITS -> getCopayLimitsScreenByPersonId(personId)
            ScreenType.COPAY_DETAILS_WHAT_IS_COPAY,
            ScreenType.COPAY_DETAILS_WHY_COPAY_EXISTS,
            ScreenType.COPAY_DETAILS_HOW_COPAY_WORKS,
            ScreenType.COPAY_DETAILS_WHICH_SERVICES_COPAY_APPLIES,
            ScreenType.COPAY_DETAILS_WHEN_HEALTH_TEAM_REFERS,
            ScreenType.COPAY_DETAILS_I_WAS_REFERRED,
            ScreenType.COPAY_DETAILS_REFERRAL_DEADLINE,
            ScreenType.COPAY_DETAILS_COPAY_IN_TESTS,
            ScreenType.COPAY_DETAILS_COPAY_IN_SPECIALIST_APPOINTMENT,
            ScreenType.COPAY_DETAILS_SPECIALIST_NEW_APPOINTMENT,
            ScreenType.COPAY_DETAILS_COPAY_IN_EMERGENCY,
            ScreenType.COPAY_DETAILS_COPAY_CALCULATION,
            ScreenType.COPAY_DETAILS_WHAT_IS_COPAY_FULL,
            ScreenType.COPAY_DETAILS_WHICH_CASES_COPAY_APPLIES,
            ScreenType.COPAY_DETAILS_HOW_LIMITS_WORKS,
            ScreenType.COPAY_DETAILS_WHEN_APPLIES_CHARGE,
            ScreenType.COPAY_DETAILS_WHY_PRIMARY_ATTENTION_FREE -> getCopayDetailsScreen(screenType, personId)

            else -> throw IllegalArgumentException("$screenType does not exists for copay sub screens")
        }
    }

    override suspend fun getOmbudsmanScreens(
        personId: PersonId,
        screenType: ScreenType,
        platform: Platform,
    ): Result<ScreensTransport, Throwable> = when (screenType) {
        ScreenType.OMBUDSMAN_MENU -> ombudsmanScreenService.getMenuScreen(personId)
        ScreenType.OMBUDSMAN_LOW_RISK -> ombudsmanScreenService.getLowRiskScreen(personId, platform)
        ScreenType.OMBUDSMAN_MEDIUM_RISK -> ombudsmanScreenService.getMediumRiskScreen(personId)
        else -> Result.failure(IllegalArgumentException("$screenType does not exists for ombudsman screens"))
    }

    override suspend fun getOtherAliceScreens(data: AliceScreensData): Result<ScreensTransport, Throwable> = span("getOtherAliceScreens") { span ->
        span.setAttribute("screen_type", data.screenType)
        span.setAttribute("person_id", data.personId)
        span.setAttribute("is_internal", data.isInternal)
        span.setAttribute("has_schedule", data.hasAppointmentSchedule().toString())

        coResultOf<ScreensTransport, Throwable> {
            when (data.screenType) {
                ScreenType.HEALTH -> getHealthScreenByPersonId(data.personId)
                ScreenType.REDESIGN_ALICE_AGORA -> getRedesignHomeAA(data.personId, data.appVersion)
                ScreenType.CHANNELS -> getChannelsScreenByPersonId(data.personId)
                ScreenType.EXPLORER -> getExplorerScreen(data.appVersion, data.personId)
                ScreenType.SCHEDULE -> getScheduleScreenByPersonId(data.personId)
                ScreenType.MAIN_MENU -> getMainMenu(data.personId)
                ScreenType.MEMBER_PROFILE -> getMemberProfileScreen(data.personId)
                ScreenType.ACCREDITED_NETWORK_MENU -> getAccreditedNetworkMenu(data.appVersion, data.personId)
                ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL -> getAccreditedNetworkEmergencyModal()
                ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL_V2 -> getAccreditedNetworkEmergencyModalV2()
                ScreenType.ACCREDITED_NETWORK_EMERGENCY_MENU -> getAccreditedNetworkEmergencyMenu(data.appVersion)
                ScreenType.ACCREDITED_NETWORK_HOSPITAL_MENU -> getAccreditedNetworkHospitalMenu()
                ScreenType.PLAN_DETAILS_MENU -> getPlanDetailsMenu()
                ScreenType.TEST_RESULT -> getExamsScreen(data.personId)
                ScreenType.REDESIGN_UNIFIED_HEALTH -> getRedesignUnifiedHealthScreen(data.appVersion, data.personId, data.question, data.nextAppointmentsSchedule?.firstOrNull())
                ScreenType.REDESIGN_HEALTH_PLAN_HOME -> getRedesignHealthPlanHomeScreen(data.personId, data.appVersion)
                ScreenType.REDESIGN_HEALTH_PLAN_HOME_DEMAND_LIST -> getRedesignHealthPlanHomeDemandListScreen(data.personId, data.demandStatuses, data.taskStatuses)
                ScreenType.REDESIGN_HEALTH_PLAN_HISTORY -> getRedesignHealthPlanHistoryScreen(data.personId)
                ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS -> getRedesignHealthPlanHomeTaskListFiltersScreen(
                    personId = data.personId,
                    demandId = data.demandId,
                    taskStatuses = data.taskStatuses,
                    demandStatuses = data.demandStatuses
                )
                ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST -> getRedesignHealthPlanHomeTaskListScreen(
                    personId = data.personId,
                    demandId = data.demandId,
                    filterByTaskType = data.filterByTaskType,
                    taskStatuses = data.taskStatuses,
                    demandStatuses = data.demandStatuses
                )
                ScreenType.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL -> getRedesignHealthPlanDemandDetailScreen(data.demandId)
                ScreenType.APPOINTMENT_HUB -> getAppointmentHubScreen(data.personId, data.nextAppointmentsSchedule)
                ScreenType.APPOINTMENT_HUB_ALICE_AGORA_SHEET -> getAppointmentHubAliceAgoraSheet()
                else -> resolveHealthScreens(data.personId, data.demandId, data.screenType)
            }
        }
            .recordResult(span)
    }

    private suspend fun getCopayMenuScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("CopayMenuScreen requested", "personId" to personId)
        return copayScreenService.getCopayMenuScreen(personId)
    }

    private suspend fun getCopayLimitsScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("CopayLimitsScreen requested", "personId" to personId)
        return copayScreenService.getCopayLimitsScreen(personId)
    }

    private fun getCopayDetailsScreen(screenType: ScreenType, personId: PersonId): ScreensTransport? {
        logger.info("CopayDetailsScreen requested", "personId" to personId)
        return copayDetailsScreenService.getCopayDetailsScreen(screenType)
    }

    private suspend fun resolveHealthScreens(
        personId: PersonId,
        demandId: UUID? = null,
        screenType: ScreenType,
    ): ScreensTransport = when (screenType) {
        ScreenType.HEALTH_HISTORY -> getHealthHistoryScreenByPersonId(personId)
        ScreenType.HEALTH_DEMAND_TASKS -> getHealthDemandTasksScreenByPersonId(personId, demandId!!)
        else -> throw IllegalArgumentException("$screenType does not exists for alice app")
    }

    private suspend fun getHealthScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("HealthScreen requested", "personId" to personId)
        return healthScreenService.getByPersonId(personId).get()
    }

    private suspend fun getMainMenu(personId: PersonId): ScreensTransport {
        logger.info("MainMenu requested")
        return mainMenuService.getMainMenu(personId).get()
    }

    private suspend fun getAccreditedNetworkMenu(appVersion: SemanticVersion, personId: PersonId): ScreensTransport {
        logger.info("AccreditedNetwork requested")
        return accreditedNetworkMenuService.getAccreditedNetworkMenu(appVersion, personId, Brand.ALICE).get()
    }

    private suspend fun getAccreditedNetworkEmergencyModal(): ScreensTransport =
        accreditedNetworkMenuService.getAccreditedNetworkEmergencyModal().get()

    private suspend fun getAccreditedNetworkEmergencyModalV2(): ScreensTransport =
        accreditedNetworkMenuService.getAccreditedNetworkEmergencyModalV2().get()

    private suspend fun getAccreditedNetworkEmergencyMenu(appVersion: SemanticVersion): ScreensTransport =
        accreditedNetworkMenuService.getAccreditedNetworkEmergencyMenu(Brand.ALICE, appVersion).get()

    private suspend fun getAccreditedNetworkHospitalMenu(): ScreensTransport =
        accreditedNetworkMenuService.getAccreditedNetworkHospitalMenu().get()

    private suspend fun getPlanDetailsMenu(): ScreensTransport {
        logger.info("PlanDetailsMenu requested")
        return mainMenuService.getPlanDetailsScreen().get()
    }

    private suspend fun getExamsScreen(personId: PersonId): ScreensTransport {
        logger.info("Exams requested")
        return examsScreenService.get(personId).get()
    }

    private suspend fun getRedesignHomeAA(personId: PersonId, appVersion: SemanticVersion): ScreensTransport {
        logger.info("AliceNowScreen requested", "personId" to personId)
        return aliceNowScreenService.getRedesignHomeAA(personId, appVersion).get()
    }

    private fun getChannelsScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("ChannelsScreen requested", "personId" to personId)
        return CHANNELS_SCREEN
    }

    private fun getScheduleScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("ScheduleScreen requested", "personId" to personId)
        return SCHEDULE_SCREEN_WITH_APP_BAR
    }

    private suspend fun getHealthHistoryScreenByPersonId(personId: PersonId): ScreensTransport {
        logger.info("HealthHistoryScreen requested", "personId" to personId)
        return healthScreenService.getHistoryByPersonId(personId).get()
    }

    private suspend fun getHealthDemandTasksScreenByPersonId(personId: PersonId, demandId: UUID): ScreensTransport {
        logger.info("HealthDemandTasksScreen requested", "demand_id" to demandId)
        return demandScreenService.getByDemandId(personId, demandId).get()
    }

    private suspend fun getExplorerScreen(appVersion: SemanticVersion, personId: PersonId) =
        explorerScreenService.getExplorer(appVersion, personId).get()

    private suspend fun getRedesignUnifiedHealthScreen(
        appVersion: SemanticVersion,
        personId: PersonId,
        questionnaireQuestionResponse: QuestionnaireQuestionResponse?,
        nextAppointment: AppointmentScheduleWithStaff?,
    ) =
        redesignUnifiedHealthScreenService.getRedesignUnifiedHealth(
            appVersion,
            personId,
            questionnaireQuestionResponse,
            nextAppointment,
        ).get()

    private suspend fun getRedesignHealthPlanHomeScreen(personId: PersonId, appVersion: SemanticVersion) =
        redesignHealthPlanScreenService.getHealthPlanHome(personId, appVersion).get()

    private suspend fun getRedesignHealthPlanHomeDemandListScreen(personId: PersonId, demandStatuses: List<DemandActionPlanStatus>, taskStatuses: List<ActionPlanTaskStatus>? = null) =
        redesignHealthPlanScreenService.getHealthPlanHomeDemandList(personId, demandStatuses, taskStatuses).get()

    private suspend fun getRedesignHealthPlanHistoryScreen(personId: PersonId) =
        redesignHealthPlanScreenService.getHealthPlanHistory(personId).get()

    private suspend fun getRedesignHealthPlanHomeTaskListFiltersScreen(
        personId: PersonId,
        demandId: UUID?,
        taskStatuses: List<ActionPlanTaskStatus>?,
        demandStatuses: List<DemandActionPlanStatus>
    ) =
        redesignHealthPlanScreenService.getHealthPlanTaskListFilters(personId, demandId, taskStatuses, demandStatuses).get()

    private suspend fun getRedesignHealthPlanHomeTaskListScreen(
        personId: PersonId,
        demandId: UUID?,
        filterByTaskType: String?,
        taskStatuses: List<ActionPlanTaskStatus>?,
        demandStatuses: List<DemandActionPlanStatus>
    ) =
        redesignHealthPlanScreenService.getHealthPlanTaskList(
            personId = personId,
            demandId = demandId,
            filterByTaskType = filterByTaskType,
            taskStatuses = taskStatuses,
            demandStatuses = demandStatuses
        ).get()

    private suspend fun getRedesignHealthPlanDemandDetailScreen(demandId: UUID?) =
        redesignHealthPlanScreenService.getHealthPlanDemandDetail(demandId!!).get()

    private suspend fun getMemberProfileScreen(personId: PersonId) =
        memberProfileScreenService.getMemberProfile(personId).get()

    private suspend fun getAppointmentHubScreen(personId: PersonId, appointmentSchedules: List<AppointmentScheduleWithStaff>?) =
        appointmentHubScreenService.getScreen(personId, appointmentSchedules).get()

    private suspend fun getAppointmentHubAliceAgoraSheet() = appointmentHubScreenService.getAliceAgoraSheet().get()
}
