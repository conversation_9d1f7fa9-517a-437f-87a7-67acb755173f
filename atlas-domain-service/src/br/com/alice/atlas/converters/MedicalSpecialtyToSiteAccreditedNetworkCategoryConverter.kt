package br.com.alice.atlas.converters

import br.com.alice.data.layer.models.ReferencedModelOrigin
import br.com.alice.data.layer.models.SiteAccreditedNetworkCategory

object MedicalSpecialtyToSiteAccreditedNetworkCategoryConverter {
    fun convert(
        title: String,
        active: <PERSON><PERSON>an,
        referencedModelValue: String,
        siteAccreditedNetworkCategory: SiteAccreditedNetworkCategory? = null,
    ): SiteAccreditedNetworkCategory =
        siteAccreditedNetworkCategory?.copy(
            title = title,
            active = active,
        ) ?: SiteAccreditedNetworkCategory(
            active = active,
            title = title,
            referencedModelValue = referencedModelValue,
            referencedModelOrigin = ReferencedModelOrigin.MEDICAL_SPECIALTY,
        )
}
