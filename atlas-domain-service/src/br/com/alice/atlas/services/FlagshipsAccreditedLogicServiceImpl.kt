package br.com.alice.atlas.services

import br.com.alice.atlas.model.Accredited
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success

class FlagshipsAccreditedLogicServiceImpl(
    private val accreditedNetworkFlagshipService: AccreditedNetworkFlagshipService,
    private val accreditedService: AccreditedService,
): FlagshipsAccreditedLogicService {
    override suspend fun getFlagships(
        accreditedList: List<Accredited>,
        placeId: String?,
        isDefaultSearch: Boolean,
    ): Result<List<Accredited>, Throwable> =
        when {
            placeId != null -> {
                logger.info(
                    "Flagship service enabled",
                    "placeId" to placeId,
                )

                val accreditedFlagship = getAccreditedFromFlagships(placeId)

                logger.info(
                    "Accredited with flagships",
                    "accredited_referenced_ids" to accreditedFlagship.map { it.referencedId },
                )

                (accreditedFlagship + accreditedList).distinctBy { it.referencedId }.success()
            }
            else -> {
                accreditedList.success()
            }
        }

    private suspend fun getAccreditedFromFlagships(
        placeId: String,
    ): List<Accredited> {
        val flagships = accreditedNetworkFlagshipService.getFlagship(placeId).getOrNullIfNotFound() ?: return emptyList()

        logger.info(
            "Got flagships",
            "placeId" to placeId,
            "flagships" to flagships,
        )

        val referencedIdsFlagship = flagships.data.map { it.providerUnitId }

        val accredited = accreditedService.getByReferencedIds(referencedIdsFlagship).getOrNull()

        logger.info(
            "Getting flagships from consolidated",
            "referenced_ids" to accredited?.map { it.referencedId },
        )

        return accredited ?: emptyList()
    }
}
