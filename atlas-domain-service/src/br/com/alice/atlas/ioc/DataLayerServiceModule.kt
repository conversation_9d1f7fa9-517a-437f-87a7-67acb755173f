package br.com.alice.atlas.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.services.SiteAccreditedNetworkAddressDataService
import br.com.alice.data.layer.services.SiteAccreditedNetworkAddressDataServiceClient
import br.com.alice.data.layer.services.SiteAccreditedNetworkCategoryDataService
import br.com.alice.data.layer.services.SiteAccreditedNetworkCategoryDataServiceClient
import br.com.alice.data.layer.services.SiteAccreditedNetworkDataService
import br.com.alice.data.layer.services.SiteAccreditedNetworkDataServiceClient
import br.com.alice.data.layer.services.SiteAccreditedNetworkFlagshipDataService
import br.com.alice.data.layer.services.SiteAccreditedNetworkFlagshipDataServiceClient
import br.com.alice.data.layer.services.SiteAccreditedNetworkProviderDataService
import br.com.alice.data.layer.services.SiteAccreditedNetworkProviderDataServiceClient
import org.koin.dsl.module

val DataLayerServiceModule = module(createdAtStart = true) {

    // Clients
    single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient()) }

    // Services
    single<SiteAccreditedNetworkDataService> { SiteAccreditedNetworkDataServiceClient(get()) }
    single<SiteAccreditedNetworkAddressDataService> { SiteAccreditedNetworkAddressDataServiceClient(get()) }
    single<SiteAccreditedNetworkProviderDataService> { SiteAccreditedNetworkProviderDataServiceClient(get()) }
    single<SiteAccreditedNetworkCategoryDataService> { SiteAccreditedNetworkCategoryDataServiceClient(get()) }
    single<SiteAccreditedNetworkFlagshipDataService> { SiteAccreditedNetworkFlagshipDataServiceClient(get()) }
}
