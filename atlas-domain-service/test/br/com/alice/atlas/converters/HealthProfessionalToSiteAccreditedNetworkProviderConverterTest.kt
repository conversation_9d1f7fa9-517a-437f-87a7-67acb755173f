package br.com.alice.atlas.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ReferencedModelClass
import br.com.alice.data.layer.models.SiteAccreditedNetworkProviderType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthProfessionalToSiteAccreditedNetworkProviderConverterTest {
    private val staff = TestModelFactory.buildStaff()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staff.id,
        specialtyId = RangeUUID.generate()
    )
    private val addresses = listOf(TestModelFactory.buildStructuredAddress())

    @Test
    fun `#convert should convert and return as expected`() {
        val result = HealthProfessionalToSiteAccreditedNetworkProviderConverter.convert(
            healthProfessional = healthProfessional,
            staff = staff,
            addresses = addresses
        )

        assertThat(result.name).isEqualTo(staff.fullName)
        assertThat(result.type).isEqualTo(SiteAccreditedNetworkProviderType.SPECIALIST)
        assertThat(result.categories).isEqualTo(listOf(healthProfessional.specialtyId.toString()))
        assertThat(result.referencedModelClass).isEqualTo(ReferencedModelClass.HEALTH_PROFESSIONAL)
        assertThat(result.referencedModelId).isEqualTo(healthProfessional.id)
        assertThat(result.active).isEqualTo(false)
        assertThat(result.icon).isNull()
        assertThat(result.isFlagship).isEqualTo(false)
        assertThat(result.productBundleId).isEqualTo(null)
        assertThat(result.addresses).isEqualTo(addresses.map { StructuredAddressToProviderAddressConverter.convert(it) })

    }
}
