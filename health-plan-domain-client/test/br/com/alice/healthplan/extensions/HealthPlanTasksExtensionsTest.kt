package br.com.alice.healthplan.extensions

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthPlanTaskType.EATING
import br.com.alice.data.layer.models.HealthPlanTaskType.MOOD
import br.com.alice.data.layer.models.HealthPlanTaskType.OTHERS
import br.com.alice.data.layer.models.HealthPlanTaskType.PHYSICAL_ACTIVITY
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.HealthPlanTaskType.SLEEP
import br.com.alice.data.layer.models.HealthPlanTaskType.values
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.models.StartType
import br.com.alice.data.layer.models.copy
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class HealthPlanTasksExtensionsTest {
    private val model = HealthPlanTask(
        personId = PersonId(),
        lastRequesterStaffId = RangeUUID.generate(),
        type = PRESCRIPTION,
        status = HealthPlanTaskStatus.ACTIVE,
        favorite = false,
    )

    @Test
    fun `#memberCanInit returns true for an active, not initiated task`() {
        listOf(
            EATING,
            PHYSICAL_ACTIVITY,
            SLEEP,
            MOOD,
            OTHERS,
        ).forEach {
            val task = model.copy(
                status = HealthPlanTaskStatus.ACTIVE,
                initiatedByMemberAt = null,
                start = Start(type = StartType.IMMEDIATE),
                type = it,
            )

            val result = task.onValidInitState()

            assertThat(result).isTrue
        }
    }

    @Test
    fun `#memberCanInit returns true for a task with CONDITIONAL start type`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.ACTIVE,
            initiatedByMemberAt = null,
            start = Start(type = StartType.CONDITIONAL),
        )

        val result = task.onValidInitState()

        assertThat(result).isTrue
    }

    @Test
    fun `#memberCanInit returns false for other task types`() {
        HealthPlanTaskType.values()
            .filter {
                !listOf(
                    PRESCRIPTION,
                    EATING,
                    PHYSICAL_ACTIVITY,
                    SLEEP,
                    MOOD,
                    OTHERS,
                ).contains(it)
            }
            .forEach {
            val task = model.copy(
                status = HealthPlanTaskStatus.ACTIVE,
                initiatedByMemberAt = null,
                start = Start(type = StartType.IMMEDIATE),
                type = it,
            )

            val result = task.onValidInitState()

            assertThat(result).isFalse
        }
    }

    @Test
    fun `#memberCanInit returns false for CREATE tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.CREATE,
            initiatedByMemberAt = null,
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for DRAFT tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.DRAFT,
            initiatedByMemberAt = null,
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for DONE tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.DONE,
            initiatedByMemberAt = null,
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for DELETED tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.DELETED,
            initiatedByMemberAt = null,
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for DELETED_BY_MEMBER tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.DELETED_BY_MEMBER,
            initiatedByMemberAt = null,
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for an initiated tasks`() {
        val task = model.copy(
            status = HealthPlanTaskStatus.DELETED,
            initiatedByMemberAt = LocalDateTime.now(),
            start = Start(type = StartType.IMMEDIATE),
        )

        val result = task.onValidInitState()

        assertThat(result).isFalse
    }

    @Test
    fun `#memberCanInit returns false for all start types but IMMEDIATE and CONDITIONAL`() {
        StartType.values()
            .filter { !listOf(StartType.IMMEDIATE, StartType.CONDITIONAL).contains(it) }
            .forEach {
                val task = model.copy(
                    status = HealthPlanTaskStatus.ACTIVE,
                    initiatedByMemberAt = null,
                    start = Start(type = it),
                )

                val result = task.onValidInitState()

                assertThat(result).isFalse
            }
    }

    @Test
    fun `#isHabit returns true for EATING type`(){
        val task = model.copy(type = EATING)

        val result = task.isHabit()

        assertThat(result).isTrue
    }

    @Test
    fun `#isHabit returns true for PHYSICAL_ACTIVITY type`(){
        val task = model.copy(type = PHYSICAL_ACTIVITY)

        val result = task.isHabit()

        assertThat(result).isTrue
    }

    @Test
    fun `#isHabit returns true for SLEEP type`(){
        val task = model.copy(type = SLEEP)

        val result = task.isHabit()

        assertThat(result).isTrue
    }

    @Test
    fun `#isHabit returns true for MOOD type`(){
        val task = model.copy(type = MOOD)

        val result = task.isHabit()

        assertThat(result).isTrue
    }

    @Test
    fun `#isHabit returns false for all types but habit ones`(){
        values()
            .filter { !listOf(EATING, PHYSICAL_ACTIVITY, SLEEP, MOOD, OTHERS).contains(it) }
            .forEach {
                val task = model.copy(type = it)

                val result = task.isHabit()

                assertThat(result).isFalse
            }
    }

    @Test
    fun `#isContinuous returns true for CONTINUOUS tasks`(){
        val task = model.copy(
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
        )

        val result = task.isContinuous()

        assertThat(result).isTrue
    }

    @Test
    fun `#isContinuous returns false for all Deadline units but CONTINUOUS`(){
        PeriodUnit.values().filter { it != PeriodUnit.CONTINUOUS }.forEach {
            val task = model.copy(
                deadline = Deadline(unit = it, quantity = 1),
            )

            val result = task.isContinuous()

            assertThat(result).isFalse
        }
    }

    @Test
    fun `#isContinuous returns true for tasks with UNDEFINED deadline`(){
        val task = model.copy(
            deadline = Deadline(unit = PeriodUnit.UNDEFINED, quantity = 0),
        )

        val result = task.withUndefinedDeadline()

        assertThat(result).isTrue
    }

    @Test
    fun `#withUndefinedDeadline returns false for all Deadline unit but UNDEFINED`() {
        PeriodUnit.values().filter { it != PeriodUnit.UNDEFINED }.forEach {
            val task = model.copy(
                deadline = Deadline(unit = it, quantity = 1),
            )

            val result = task.withUndefinedDeadline()

            assertThat(result).isFalse
        }
    }
}
