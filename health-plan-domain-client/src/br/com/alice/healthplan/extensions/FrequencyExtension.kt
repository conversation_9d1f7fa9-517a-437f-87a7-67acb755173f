package br.com.alice.healthplan.extensions

import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.FrequencyType
import br.com.alice.data.layer.models.PeriodUnit
import java.time.Duration
import java.time.temporal.TemporalAmount
import kotlin.math.ceil

fun Frequency.getInterval(): TemporalAmount? =
     when(type) {
         FrequencyType.EVERY -> getIntervalForEveryType()
         FrequencyType.QUANTITY_IN_PERIOD -> getIntervalForQuantityType()
         FrequencyType.TIMES -> getIntervalForTimesType()
         else -> null
    }

private fun Frequency.getIntervalForEveryType(): TemporalAmount? =
    when (unit) {
        PeriodUnit.HOUR -> Duration.ofHours(1)
        PeriodUnit.DAY -> Duration.ofDays(1)
        PeriodUnit.WEEK -> Duration.ofDays(7)
        PeriodUnit.MONTH -> Duration.ofDays(30)
        else -> null
    }

private fun Frequency.getIntervalForQuantityType(): TemporalAmount? {
    val amount = quantity.toLong()

    return when (unit) {
        PeriodUnit.HOUR -> Duration.ofHours(amount)
        PeriodUnit.DAY -> Duration.ofDays(amount)
        PeriodUnit.WEEK -> Duration.ofDays(7 * amount)
        PeriodUnit.MONTH -> Duration.ofDays(30 * amount)
        else -> null
    }
}

private fun Frequency.getIntervalForTimesType(): TemporalAmount? {
    val quantityOfMinutes = when (unit) {
        PeriodUnit.HOUR -> 60
        PeriodUnit.DAY -> 24 * 60
        PeriodUnit.WEEK -> 7 * 24 * 60
        PeriodUnit.MONTH -> 30 * 24 * 60
        else -> null
    } ?: return null

    val times = quantity.toLong()

    return Duration.ofMinutes(
        ceil(quantityOfMinutes.toFloat() / times.toFloat()).toLong()
    )
}
