package br.com.alice.healthplan.models

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.HealthPlanTaskGroupAttachment
import br.com.alice.data.layer.models.HealthPlanTaskGroupDescription
import java.util.UUID

data class HealthPlanTaskGroupTransport(
    val id: UUID? = null,
    val healthPlanId: UUID?,
    val name: String,
    val personId: PersonId,
    val descriptions: List<HealthPlanTaskGroupDescription> = emptyList(),
    val internalAttachments: List<HealthPlanTaskGroupAttachment> = emptyList(),
    val externalAttachments: List<HealthPlanTaskGroupAttachment> = emptyList()
)
