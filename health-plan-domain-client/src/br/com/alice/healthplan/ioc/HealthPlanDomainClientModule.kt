    package br.com.alice.healthplan.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.healthplan.HealthPlanDomainClientConfiguration
import br.com.alice.healthplan.client.HealthPlanService
import br.com.alice.healthplan.client.HealthPlanServiceClient
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.client.HealthPlanTaskGroupServiceClient
import br.com.alice.healthplan.client.HealthPlanTaskGroupTemplateService
import br.com.alice.healthplan.client.HealthPlanTaskGroupTemplateServiceClient
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.client.HealthPlanTaskServiceClient
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import br.com.alice.healthplan.client.HealthPlanTaskTemplateServiceClient
import org.koin.dsl.module

    val HealthPlanDomainClientModule = module(createdAtStart = true) {

    val baseUrl = HealthPlanDomainClientConfiguration.baseUrl()
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 10_000), "$baseUrl/rfc")

    single<HealthPlanService> { HealthPlanServiceClient(invoker) }
    single<HealthPlanTaskGroupService> { HealthPlanTaskGroupServiceClient(invoker) }
    single<HealthPlanTaskGroupTemplateService> { HealthPlanTaskGroupTemplateServiceClient(invoker) }
    single<HealthPlanTaskService> { HealthPlanTaskServiceClient(invoker) }
    single<HealthPlanTaskTemplateService> { HealthPlanTaskTemplateServiceClient(invoker) }
}
