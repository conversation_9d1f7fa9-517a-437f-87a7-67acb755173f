package br.com.alice.healthplan.converters.taskProgress

import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.SentenceReference
import br.com.alice.healthplan.models.TaskProgressInfo
import br.com.alice.healthplan.models.TaskProgressType
import java.time.Duration
import java.time.LocalDateTime
import kotlin.math.ceil
import kotlin.math.floor

class DeadlineProgressInfoHandler: ProgressInfoHandler() {
    override fun canBuild(task: HealthPlanTask): Boolean {
        val deadline = task.deadline ?: return false

        return deadline.unit != PeriodUnit.CONTINUOUS &&
                deadline.unit != PeriodUnit.UNDEFINED
    }

    override fun build(task: HealthPlanTask): TaskProgressInfo =
        Builder(task).build()

    private class Builder(private val task: HealthPlanTask) {
        fun build() =
            TaskProgressInfo(
                initiatedAt = task.initiatedByMemberAt!!,
                progressType = TaskProgressType.DEADLINE,
                endAt = calculateEndTime(),
                progress = calculateProgress(),
                newPrescription = false,
                alertMessage = false,
                message = getMessage(),
            )

        private fun calculateEndTime(): LocalDateTime? =
            task.deadline
                ?.fillDate(task.initiatedByMemberAt)
                ?.date
                ?.toLocalDate()
                ?.atEndOfTheDay()

        private fun calculateProgress(): Int {
            val daysToDeadline = daysFromMemberInitToDeadline()

            if (daysToDeadline == 0f) {
                return 0
            }

            return (daysSinceMemberInitiated() / daysToDeadline * 100).toInt()
        }

        private fun daysFromMemberInitToDeadline(): Float {
            val deadlineTime = task.deadline?.fillDate(task.initiatedByMemberAt)?.date
            val initTime = task.initiatedByMemberAt

            if (deadlineTime == null || initTime == null) {
                return 0f
            }

            return ceil(durationInDays(initTime, deadlineTime))
        }

        private fun daysSinceMemberInitiated(): Float {
            val initTime = task.initiatedByMemberAt ?: return 0f

            return floor(durationInDays(initTime, LocalDateTime.now()))
        }

        private fun durationInDays(start: LocalDateTime, end: LocalDateTime): Float =
            Duration.between(start, end).toHours() / 24f

        private fun getMessage(): String =
            when (task.type) {
                HealthPlanTaskType.PRESCRIPTION -> compactSentence()
                else -> SentenceReference.friendlyFrequency(task.frequency)
            }

        private fun compactSentence(): String =
            task.specialize<Prescription>().compactSentence()
    }
}
