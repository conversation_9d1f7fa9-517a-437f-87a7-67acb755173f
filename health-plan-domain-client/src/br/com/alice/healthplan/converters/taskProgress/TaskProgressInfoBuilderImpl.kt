package br.com.alice.healthplan.converters.taskProgress

import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.healthplan.models.TaskProgressInfo

class TaskProgressInfoBuilderImpl(
    private val handlers: ProgressInfoHandler? = null
): TaskProgressInfoBuilder {
    override fun canBuild(task: HealthPlanTask): Boolean =
        (handlers ?: defaultHandlersChain()).canHandle(task)

    override fun build(task: HealthPlanTask): TaskProgressInfo? {
        if (!wasInitiated(task)) {
            return null
        }

        return (handlers ?: defaultHandlersChain()).handle(task)
    }

    private fun wasInitiated(task: HealthPlanTask) = task.initiatedByMemberAt != null

    private fun defaultHandlersChain(): ProgressInfoHandler {
        val specialPrescription = SpecialPrescriptionProgressInfoHandler()
        val habitContinuous = HabitContinuousProgressInfoHandler()
        val deadline = DeadlineProgressInfoHandler()

        specialPrescription.next(habitContinuous).next(deadline)

        return specialPrescription
    }
}
