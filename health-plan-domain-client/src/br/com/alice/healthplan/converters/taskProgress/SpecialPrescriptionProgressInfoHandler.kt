package br.com.alice.healthplan.converters.taskProgress

import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.data.layer.models.Dose
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.FrequencyType
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.MedicineUnit
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.PrescriptionMedicineType
import br.com.alice.healthplan.extensions.getInterval
import br.com.alice.healthplan.extensions.isContinuous
import br.com.alice.healthplan.models.TaskProgressInfo
import br.com.alice.healthplan.models.TaskProgressType
import java.time.Duration
import java.time.LocalDateTime
import java.time.temporal.TemporalAmount
import kotlin.math.ceil
import kotlin.math.min
import kotlin.math.round

class SpecialPrescriptionProgressInfoHandler(): ProgressInfoHandler() {
    override fun canBuild(task: HealthPlanTask): Boolean {
        val prescription = toPrescriptionOrNull(task) ?: return false

        return Validator(prescription).canBuild()
    }

    override fun build(task: HealthPlanTask): TaskProgressInfo =
        Calculator(task.specialize()).build()

    private fun toPrescriptionOrNull(task: HealthPlanTask): Prescription? =
        if (task.type == HealthPlanTaskType.PRESCRIPTION) task.specialize() else null

    private class Validator(private val prescription: Prescription) {
        fun canBuild(): Boolean =
            prescription.isContinuous() &&
                    withValidMedicineFormat() &&
                    withValidMedicineType() &&
                    withValidFrequency()

        private fun withValidMedicineFormat() =
            listOf(MedicineUnit.PILLS, MedicineUnit.CAPSULE, MedicineUnit.TABLET)
                .contains(prescription.medicine?.unit)

        private fun withValidMedicineType() =
            prescription.medicine?.type == PrescriptionMedicineType.SPECIAL

        private fun withValidFrequency() =
            prescription.frequency?.type == FrequencyType.EVERY ||
                    prescription.frequency?.type == FrequencyType.QUANTITY_IN_PERIOD ||
                    prescription.frequency?.type == FrequencyType.TIMES
    }

    private class Calculator(private val prescription: Prescription) {
        private val prescriptionEndingThresholdInDays = 10

        private lateinit var dose: Dose
        private var prescriptionQuantity: Float = 0f
        private lateinit var dosesInterval: TemporalAmount

        private lateinit var endTime: LocalDateTime
        private var medicineTaken: Float = 0f

        fun build(): TaskProgressInfo {
            if (!loadInfo()) {
                return defaultProgressInfo()
            }

            endTime = calculateEndTime()
            medicineTaken = medicineTakenUntilNow()

            val medicineEnding = isMedicineEnding()
            val message = if (medicineEnding) medicineEndingMessage() else prescription.compactSentence()

            return TaskProgressInfo(
                initiatedAt = prescription.initiatedByMemberAt!!,
                progressType = TaskProgressType.SPECIAL_PRESCRIPTION_CONTINUOUS,
                endAt = endTime,
                progress = calculateProgress(),
                newPrescription = medicineEnding,
                alertMessage =  medicineEnding,
                message = message,
            )
        }

        private fun loadInfo(): Boolean {
            dose = prescription.dose ?: return false
            prescriptionQuantity = getPrescriptionQuantity() ?: return false

            val frequency = getFrequency() ?: return false
            dosesInterval = frequency.getInterval() ?: return false

            return true
        }

        private fun defaultProgressInfo(): TaskProgressInfo =
            TaskProgressInfo(
                initiatedAt = prescription.initiatedByMemberAt!!,
                progressType = TaskProgressType.SPECIAL_PRESCRIPTION_CONTINUOUS,
            )

        private fun calculateEndTime(): LocalDateTime {
            val totalMedicine = prescription.packing * prescriptionQuantity
            var medicineTaken = dose.quantity
            var endTime = prescription.initiatedByMemberAt!!

            while (medicineTaken < totalMedicine) {
                endTime = endTime.plus(dosesInterval)
                medicineTaken += dose.quantity
            }

            return endTime.toLocalDate().atEndOfTheDay()
        }

        private fun medicineTakenUntilNow(): Float {
            val now = LocalDateTime.now()

            var iterationTime = prescription.initiatedByMemberAt!!
            var medicineTaken = dose.quantity

            while (iterationTime <= now) {
                medicineTaken += dose.quantity

                iterationTime = iterationTime.plus(dosesInterval)
            }

            return medicineTaken - 1
        }

        private fun getPrescriptionQuantity(): Float? =
            prescription.medicine?.quantity?.toFloatOrNull()

        private fun getFrequency(): Frequency? =
            if (prescription.frequency?.unit == null) null else prescription.frequency

        private fun calculateProgress(): Int {
            val progress = (daysSinceMemberInitiated() / daysFromInitToMedicineEnd(endTime) * 100).toInt()

            return min(100, progress)
        }

        private fun daysSinceMemberInitiated(): Float =
            round(durationInDays(prescription.initiatedByMemberAt!!, LocalDateTime.now()))

        private fun daysFromInitToMedicineEnd(endTime: LocalDateTime): Float =
            ceil(durationInDays(prescription.initiatedByMemberAt!!, endTime))

        private fun durationInDays(start: LocalDateTime, end: LocalDateTime): Float =
            Duration.between(start, end).toHours() / 24f

        private fun isMedicineEnding() =
            durationInDays(LocalDateTime.now(), endTime) < prescriptionEndingThresholdInDays

        private fun medicineEndingMessage(): String {
            val remainingMedicine = prescription.packing * prescriptionQuantity - medicineTaken
            val remainingMedicineText = maxOf(0f, remainingMedicine).toInt()

            val medUnit = prescription.medicine!!.unit!!
            val medUnitText = if (remainingMedicine > 1) medUnit.plural else medUnit.singular

            val remainingText = if (remainingMedicine > 1) "restantes" else "restante"

            return "$remainingMedicineText $medUnitText $remainingText"
        }
    }
}
