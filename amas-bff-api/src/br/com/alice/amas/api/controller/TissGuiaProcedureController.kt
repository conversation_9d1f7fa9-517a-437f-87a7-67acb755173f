package br.com.alice.amas.api.controller

import br.com.alice.amas.api.exceptions.handlerAmasErrors
import br.com.alice.amas.client.GuiaProcedureService
import br.com.alice.amas.client.GuiaService
import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.client.TissBatchService
import br.com.alice.amas.utils.GuiaItemHash
import br.com.alice.amas.utils.HashBody
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.common.utils.RandomIdUtils
import br.com.alice.data.layer.models.Guia
import br.com.alice.data.layer.models.GuiaProcedure
import br.com.alice.data.layer.models.GuiaProcedureStatus
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.person.client.MemberService
import br.com.alice.person.model.MemberWithProduct
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class TissGuiaProcedureController(
    private val guiaProcedureService: GuiaProcedureService,
    private val guiaService: GuiaService,
    private val tissBatchService: TissBatchService,
    private val invoiceService: InvoiceService,
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService,
    private val memberService: MemberService
) : Controller() {

    private val TISS_BATCH_CODE_SIZE = 10
    private val GUIA_CODE_SIZE = 8
    private val codeChars = "**********".toCharArray()

    suspend fun criticize(procedureId: UUID, critique: GuiaProcedureCritique): Response {
        guiaProcedureService.get(procedureId).map {
            guiaProcedureService.update(it.copy(critique = if (critique.criticize.isNullOrBlank()) null else critique.criticize))
        }
        return true.toResponse()
    }

    suspend fun create(model: GuiaProcedureCreateRequest): Response {
        return coroutineScope {
            val tissBatchAndInvoiceDeferred = async {
                val tissBatch = tissBatchService.get(model.tissBatchId).get()

                val invoice = invoiceService.get(tissBatch.invoiceId).get()

                tissBatch to invoice
            }

            val healthSpecialistResourceBundleAndMemberWithProductDeferred = async {
                getHealthSpecialistResourceBundleAndMemberWithProduct(
                    model.aliceCode,
                    model.personId
                )
            }

            val (tissBatch, invoice) = tissBatchAndInvoiceDeferred.await()
            val (healthSpecialistResourceBundle, memberWithProduct) = healthSpecialistResourceBundleAndMemberWithProductDeferred.await()

            return@coroutineScope guiaService.add(
                Guia(
                    personId = model.personId?.let { PersonId(model.personId) },
                    providerExecutorCnpj = model.providerExecutorCnpj,
                    providerExecutorName = model.providerExecutorName,
                    providerRequesterCnpj = model.providerRequesterCnpj,
                    providerRequesterName = model.providerRequesterName,
                    valueProcedures = model.unityValue.multiply(model.quantity.toBigDecimal()),
                    valueTotal = model.unityValue.multiply(model.quantity.toBigDecimal()),
                    tissBatchId = model.tissBatchId,
                    number = generateNumber(GUIA_CODE_SIZE),
                    requestedAt = model.executedAt,
                    providerExecutorCnes = "99999",
                    tissBatchNumber = generateNumber(TISS_BATCH_CODE_SIZE),
                    memberTier = memberWithProduct?.product?.tier,
                    serviceTypes = listOfNotNull(healthSpecialistResourceBundle?.serviceType),
                    executionEnvironment = getExecutionEnvironment(
                        healthSpecialistResourceBundle?.executionEnvironment,
                        invoice.expenseType
                    ),
                )
            ).map { guia ->
                val hashBody = HashBody(
                    personId = model.personId?.let { PersonId(model.personId) },
                    providerExecutorCnpj = model.providerExecutorCnpj,
                    providerRequesterCnpj = model.providerRequesterCnpj,
                    procedureCode = model.procedureCode,
                    executedAt = model.executedAt,
                    procedureDescription = model.procedureDescription,
                    automaticGenerated = invoice.automaticGenerated,
                    staffId = invoice.staffId.toString(),
                )

                guiaProcedureService.add(
                    GuiaProcedure(
                        guiaId = guia.id,
                        quantity = model.quantity,
                        procedureCode = model.procedureCode,
                        procedureDescription = model.procedureDescription,
                        procedureTable = model.procedureTable,
                        unityValue = model.unityValue,
                        totalValue = model.unityValue.multiply(model.quantity.toBigDecimal()),
                        executedAt = model.executedAt,
                        hash = GuiaItemHash.generate(hashBody),
                        critique = model.critique,
                        addedOnCriticize = model.addedOnCriticize,
                        serviceType = healthSpecialistResourceBundle?.serviceType
                            ?: HealthSpecialistResourceBundleServiceType.UNDEFINED,
                    )
                ).fold({ guiaProcedure ->
                    tissBatchService.update(
                        tissBatch.copy(
                            totalValue = tissBatch.totalValue.plus(guia.valueTotal),
                            guiaQuantity = tissBatch.guiaQuantity + 1
                        )
                    )

                    guiaProcedure
                }, {
                    guiaService.delete(guia)
                    handlerAmasErrors(it)
                })
            }.foldResponse()
        }
    }

    suspend fun update(
        guiaProcedureId: UUID,
        request: GuiaProcedureUpdateRequest
    ): Response = coroutineScope {
        val resourceBundleDef = async {
            request.aliceCode?.let { healthSpecialistResourceBundleService.findByCodes(listOf(it)) }
        }

        val guiaProcedureDef = async { guiaProcedureService.get(guiaProcedureId) }

        coResultOf<GuiaProcedure, Throwable> {
            val resourceBundle = resourceBundleDef.await()?.getOrElse { emptyList() }?.firstOrNull()
            val guiaProcedure = guiaProcedureDef.await().get()

            guiaProcedure.copy(
                executedAt = request.executedAt,
                procedureCode = request.procedureCode,
                procedureDescription = request.procedureDescription,
                quantity = request.quantity,
                unityValue = request.unityValue,
                totalValue = request.unityValue.multiply(request.quantity.toBigDecimal()),
                status = request.status ?: guiaProcedure.status,
                serviceType = resourceBundle?.serviceType ?: guiaProcedure.serviceType
            )
        }
            .flatMap { guiaProcedureService.update(it) }
            .flatMapPair { updateGuiaAndTissBatchValue(it.guiaId) }
            .map { (_, guiaProcedure) -> guiaProcedure }
            .foldResponse()
    }

    private fun getExecutionEnvironment(
        executionEnvironment: HealthSpecialistProcedureExecutionEnvironment?,
        expenseType: InvoiceExpenseType
    ) = if (expenseType == InvoiceExpenseType.EXPENSES) {
        HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY
    } else {
        when (executionEnvironment) {
            HealthSpecialistProcedureExecutionEnvironment.SURGICAL -> HealthSpecialistProcedureExecutionEnvironment.SURGICAL
            HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT -> HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT
            else -> HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT
        }
    }

    private suspend fun updateGuiaAndTissBatchValue(guiaId: UUID) =
        guiaProcedureService.findByGuiaId(guiaId)
            .flatMap { guiaProcedures -> updateGuia(guiaId, guiaProcedures) }
            .flatMap { guia -> guiaService.findByTissBatchId(guia.tissBatchId) }
            .flatMap { guias -> updateTissBatch(guias) }

    private suspend fun updateGuia(guiaId: UUID, guiaProcedures: List<GuiaProcedure>) =
        guiaService.get(guiaId)
            .flatMap { guia ->
                val valueTotal = guiaProcedures.sumOf {
                    if (it.status == GuiaProcedureStatus.AUTHORIZED || it.status == null) it.totalValue!!
                    else BigDecimal.ZERO
                }

                guiaService.update(
                    guia.copy(
                        serviceTypes = guiaProcedures.map { it.serviceType }.distinct(),
                        valueTotal = valueTotal
                    )
                )
            }

    private suspend fun updateTissBatch(guias: List<Guia>) =
        tissBatchService.get(guias.first().tissBatchId)
            .flatMap { tissBatch ->
                tissBatchService.update(
                    tissBatch.copy(
                        guiaQuantity = guias.size,
                        totalValue = guias.sumOf { it.valueTotal }
                    )
                )
            }

    suspend fun delete(guiaProcedureId: UUID): Response =
        guiaProcedureService.get(guiaProcedureId)
            .flatMapPair { guiaProcedure -> guiaProcedureService.delete(guiaProcedure) }
            .flatMap { (_, guiaProcedure) -> deleteGuiaAndTissBatch(guiaProcedure.guiaId) }
            .foldResponse()

    private suspend fun deleteGuiaAndTissBatch(guiaId: UUID) = coResultOf<Boolean, Throwable> {
        val guiaProcedures = guiaProcedureService.findByGuiaId(guiaId).get()
        if (guiaProcedures.isNotEmpty()) {
            updateGuia(guiaId, guiaProcedures)
                .flatMap { guia -> guiaService.findByTissBatchId(guia.tissBatchId) }
                .flatMap { guias -> updateTissBatch(guias) }

            return@coResultOf false
        }

        val guia = guiaService.get(guiaId).get()
        guiaService.delete(guia).get()

        val guias = guiaService.findByTissBatchId(guia.tissBatchId).get()
        if (guias.isNotEmpty()) {
            updateTissBatch(guias)
            return@coResultOf false
        }

        val tissBatch = tissBatchService.get(guia.tissBatchId).get()
        tissBatchService.delete(tissBatch).get()
    }

    private fun generateNumber(size: Int): String =
        RandomIdUtils.randomId(alphabet = codeChars, size = size)

    private suspend fun getHealthSpecialistResourceBundleAndMemberWithProduct(
        aliceCode: String,
        personId: UUID?,
    ): Pair<HealthSpecialistResourceBundle?, MemberWithProduct?> {
        return coroutineScope {

            val healthSpecialistResourceBundleDeferred = async {
                healthSpecialistResourceBundleService.findByCodes(
                    listOf(aliceCode)
                ).get().firstOrNull()
            }

            val memberWithProductDeferred = async {
                personId?.let {
                    memberService.findActiveMembershipWithProduct(it.toPersonId()).get()
                }
            }

            healthSpecialistResourceBundleDeferred.await() to memberWithProductDeferred.await()
        }
    }

}

data class GuiaProcedureCritique(val criticize: String)


data class GuiaProcedureCreateRequest(
    val procedureCode: String,
    val aliceCode: String = "",
    val procedureDescription: String,
    val procedureTable: String,
    val quantity: Int,
    val unityValue: BigDecimal,
    val providerRequesterCnpj: String,
    val providerRequesterName: String,
    val providerExecutorCnpj: String,
    val providerExecutorName: String,
    val tissBatchId: UUID,
    val personId: UUID? = null,
    val executedAt: LocalDate,
    val critique: String? = null,
    val addedOnCriticize: Boolean
)

data class GuiaProcedureUpdateRequest(
    val id: UUID,
    val procedureCode: String,
    val aliceCode: String? = null,
    val procedureDescription: String,
    val quantity: Int,
    val unityValue: BigDecimal,
    val executedAt: LocalDate,
    val status: GuiaProcedureStatus? = null
)
