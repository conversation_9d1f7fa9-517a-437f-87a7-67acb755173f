package br.com.alice.amas.api.controller

import br.com.alice.amas.api.services.PreviewEarningSummaryUploadResponse
import br.com.alice.amas.api.services.UploadPreviewEarningSummaryService
import br.com.alice.amas.client.PreviewEarningSummaryService
import br.com.alice.amas.models.FileVaultResponse
import br.com.alice.amas.models.PreviewEarningSummaryResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.PreviewEarningSummary
import br.com.alice.data.layer.models.PreviewEarningSummaryStatus
import com.github.kittinunf.result.success
import io.ktor.http.HttpMethod
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

internal class PreviewEarningSummaryControllerTest : ControllerTestHelper() {
    private val uploadPreviewEarningSummaryService: UploadPreviewEarningSummaryService = mockk()
    private val previewEarningSummaryService: PreviewEarningSummaryService = mockk()

    private val controller = spyk(PreviewEarningSummaryController(uploadPreviewEarningSummaryService, previewEarningSummaryService))

    private val uploadPreviewEarningSummaryUploadResponse =
        PreviewEarningSummaryUploadResponse(
            id = RangeUUID.generate(),
            file = FileVaultResponse(fileName = "arquivo.csv", fileUrl = "https://www.petlove.com.br/static/pets/dog/28137/hd_1512128164-1500644060-photo.jpg"),
            createdAt = LocalDateTime.now(),
            status = PreviewEarningSummaryStatus.SUCCESS
        )

    private val previewEarningSummaryResponse = PreviewEarningSummaryResponse(
        id = RangeUUID.generate(),
        file = FileVaultResponse(fileName = "arquivo.csv", fileUrl = "https://www.petlove.com.br/static/pets/dog/28137/hd_1512128164-1500644060-photo.jpg"),
        createdAt = LocalDateTime.now(),
        status = PreviewEarningSummaryStatus.SUCCESS,
        errors = emptyList(),
        warnings = emptyList()
    )

    private val previewEarningSummary = PreviewEarningSummary(
        id = RangeUUID.generate(),
        fileId = RangeUUID.generate(),
        fileName = "sim.csv",
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun afterTest() = clearAllMocks()

    @Test
    fun `#uploadPreviewEarningSummary should call uploadPreviewEarningSummaryService#upload once`() = runBlocking {
        coEvery {
            uploadPreviewEarningSummaryService.upload(any(), any(), any())
        } returns uploadPreviewEarningSummaryUploadResponse.toResponse()

        coEvery { controller.currentUserIdKey() } returns RangeUUID.generate()

        authenticatedAs(token, staff) {
            multipart(HttpMethod.Post, "/medical-bills/earning-summary/upload", fileName = "csv-example-correct.csv", parameters = emptyMap()) { response ->
                assertEquals(response.bodyAsJson(),uploadPreviewEarningSummaryUploadResponse)
                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
            }
        }
    }
    @Test
    fun `#findPreviewEarningSummary should call previewEarningSummaryService#findAll once`() = runBlocking {
        coEvery {
            previewEarningSummaryService.findAllPaginated(5, 0)
        } returns Pair(listOf(previewEarningSummaryResponse), 10).success()

        authenticatedAs(token, staff) {
            get("/medical-bills/earning-summary?limit=5&offset=1") { response ->
                val paginatedResponse = response.bodyAsJson<FindPreviewEarningSummaryPaginatedResponse>()
                assertEquals(paginatedResponse.previewEarningSummaries, listOf(previewEarningSummaryResponse))
                assertEquals(paginatedResponse.currentPage, 1)
                assertEquals(paginatedResponse.totalItems, 10)
                assertEquals(paginatedResponse.totalPages, 2)
                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#deletePreviewEarningSummary should call previewEarningSummaryService#delete once`() = runBlocking {
        coEvery {
            previewEarningSummaryService.get(any())
        } returns previewEarningSummary.success()
        coEvery {
            previewEarningSummaryService.delete(any())
        } returns true.success()

        authenticatedAs(token, staff) {
            delete("/medical-bills/earning-summary/${previewEarningSummary.id}") { response ->
                assertEquals(response.bodyAsJson(),true)
                assertThat(response).isSuccessfulJson()
                assertThat(response).isOK()
            }
        }
    }
}
