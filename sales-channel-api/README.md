# Sales Channel API

This is the Sales Channel API from the Corretores Team. 
Initially, it is the BFF for the `vendas.alice.com.br` application.


### Responsible Team
Revenue, find us on ``#eng-revenue`` on Slack ;)
- [engineering notion](https://www.notion.so/alicehealth/engenharia-corretores-357a243ca9f74a298110785a07fef38d)

### Local development

Requirements
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``db_reset db_seed db_run`` - to start our database and data layer
* ``run service=sales-channel-api`` - to run it
