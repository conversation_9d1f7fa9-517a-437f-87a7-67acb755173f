package br.com.alice.sales_channel.api.models

interface BaseResponse<T : Any>

data class PaginatedResponse<T : Any>(
    val filterOptions: Map<String, List<FilterOption>> = emptyMap(),
    val filters: Map<String, Any> = emptyMap(),
    val total: Int,
    val limit: Int,
    val offset: Int,
    val results: List<T>
) : BaseResponse<T>

data class FilterOption(val name: String, val count: Int, val value: String)
