package br.com.alice.sales_channel.api.converters

import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.StageOwner
import br.com.alice.sales_channel.api.models.DealTimelineStep
import br.com.alice.sales_channel.api.models.DealTimelineStepStatus
import kotlin.test.Test
import kotlin.test.assertEquals

class DealTimelineConverterTest {
     @Test
     fun `should return to_be_done for steps in the future and completed in the past`() {
         val currentStep = DealStage.RISK_FLOW

         val expectedTimelineSteps = listOf(
             DealTimelineStep(
                 "Análise de documentação",
                 DealStage.BACKGROUND_CHECK,
                 10,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar análise médica Alice",
                 DealStage.RISK_FLOW,
                 30,
                 DealTimelineStepStatus.IN_PROGRESS,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Contrato em confecção",
                 DealStage.CONTRACT_PREPARATION,
                 40,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Assinar contrato",
                 DealStage.CONTRACT_SENT,
                 50,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Gerando pagamento",
                 DealStage.SEND_PAYMENT,
                 60,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar pagamento",
                 DealStage.PAYMENT_REQUESTED,
                 70,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Pagamento realizado",
                 DealStage.PAYMENT_CONFIRMED,
                 80,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Processo implantado",
                 DealStage.PROCESS_FINISHED,
                 90,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.CONCLUDED.name
             )
         )

         val actualTimelineSteps = DealTimelineConverter.convert(currentStep)

         assertEquals(expectedTimelineSteps, actualTimelineSteps)
     }

     @Test
     fun `should convert DealStage to DealTimelineSteps with all steps completed`() {
         val currentStep = DealStage.PROCESS_FINISHED

         val expectedTimelineSteps = listOf(
             DealTimelineStep(
                 "Análise de documentação",
                 DealStage.BACKGROUND_CHECK,
                 10,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar análise médica Alice",
                 DealStage.RISK_FLOW,
                 30,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Contrato em confecção",
                 DealStage.CONTRACT_PREPARATION,
                 40,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Assinar contrato",
                 DealStage.CONTRACT_SENT,
                 50,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Gerando pagamento",
                 DealStage.SEND_PAYMENT,
                 60,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar pagamento",
                 DealStage.PAYMENT_REQUESTED,
                 70,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Pagamento realizado",
                 DealStage.PAYMENT_CONFIRMED,
                 80,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Processo implantado",
                 DealStage.PROCESS_FINISHED,
                 90,
                 DealTimelineStepStatus.COMPLETED,
                 StageOwner.CONCLUDED.name
             )
         )

         val actualTimelineSteps = DealTimelineConverter.convert(currentStep)

         assertEquals(expectedTimelineSteps, actualTimelineSteps)
     }

     @Test
     fun `should return on background check step`() {
         val currentStep = DealStage.BACKGROUND_CHECK

         val expectedTimelineSteps = listOf(
             DealTimelineStep(
                 "Análise de documentação",
                 DealStage.BACKGROUND_CHECK,
                 10,
                 DealTimelineStepStatus.IN_PROGRESS,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar análise médica Alice",
                 DealStage.RISK_FLOW,
                 30,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Contrato em confecção",
                 DealStage.CONTRACT_PREPARATION,
                 40,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Assinar contrato",
                 DealStage.CONTRACT_SENT,
                 50,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Gerando pagamento",
                 DealStage.SEND_PAYMENT,
                 60,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar pagamento",
                 DealStage.PAYMENT_REQUESTED,
                 70,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Pagamento realizado",
                 DealStage.PAYMENT_CONFIRMED,
                 80,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Processo implantado",
                 DealStage.PROCESS_FINISHED,
                 90,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.CONCLUDED.name
             )
         )

         val actualTimelineSteps = DealTimelineConverter.convert(currentStep)

         assertEquals(expectedTimelineSteps, actualTimelineSteps)
     }

     @Test
     fun `should return on check corroborating documents and no background check`() {
         val currentStep = DealStage.CORROBORATING_DOCUMENTS_CHECK

         val expectedTimelineSteps = listOf(
             DealTimelineStep(
                 "Análise de documentação",
                 DealStage.CORROBORATING_DOCUMENTS_CHECK,
                 20,
                 DealTimelineStepStatus.IN_PROGRESS,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar análise médica Alice",
                 DealStage.RISK_FLOW,
                 30,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Contrato em confecção",
                 DealStage.CONTRACT_PREPARATION,
                 40,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Assinar contrato",
                 DealStage.CONTRACT_SENT,
                 50,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Gerando pagamento",
                 DealStage.SEND_PAYMENT,
                 60,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar pagamento",
                 DealStage.PAYMENT_REQUESTED,
                 70,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Pagamento realizado",
                 DealStage.PAYMENT_CONFIRMED,
                 80,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Processo implantado",
                 DealStage.PROCESS_FINISHED,
                 90,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.CONCLUDED.name
             )
         )

         val actualTimelineSteps = DealTimelineConverter.convert(currentStep)

         assertEquals(expectedTimelineSteps, actualTimelineSteps)
     }

     @Test
     fun `should return deal created step and no background check`() {
         val currentStep = DealStage.DEAL_CREATED

         val expectedTimelineSteps = listOf(
             DealTimelineStep(
                 "Análise de documentação",
                 DealStage.DEAL_CREATED,
                 0,
                 DealTimelineStepStatus.IN_PROGRESS,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar análise médica Alice",
                 DealStage.RISK_FLOW,
                 30,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Contrato em confecção",
                 DealStage.CONTRACT_PREPARATION,
                 40,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Assinar contrato",
                 DealStage.CONTRACT_SENT,
                 50,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Gerando pagamento",
                 DealStage.SEND_PAYMENT,
                 60,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.ALICE.name
             ),
             DealTimelineStep(
                 "Realizar pagamento",
                 DealStage.PAYMENT_REQUESTED,
                 70,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Pagamento realizado",
                 DealStage.PAYMENT_CONFIRMED,
                 80,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.COMPANY.name
             ),
             DealTimelineStep(
                 "Processo implantado",
                 DealStage.PROCESS_FINISHED,
                 90,
                 DealTimelineStepStatus.TO_BE_DONE,
                 StageOwner.CONCLUDED.name
             )
         )

         val actualTimelineSteps = DealTimelineConverter.convert(currentStep)

         assertEquals(expectedTimelineSteps, actualTimelineSteps)
     }

 }
