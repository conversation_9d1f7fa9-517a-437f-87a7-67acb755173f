package br.com.alice.dasaintegrationservice.br.com.alice.sales_channel.api.controllers

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.getTicketStage
import br.com.alice.sales_channel.api.controllers.CHECK_DOCUMENTOS_COMPROBATORIOS
import br.com.alice.sales_channel.api.controllers.ControllerTestHelper
import br.com.alice.sales_channel.api.controllers.TicketController
import br.com.alice.sales_channel.model.TicketRequest
import br.com.alice.sales_channel.service.HubspotTicketService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class TicketControllerTest : ControllerTestHelper() {
    private val kafkaService: KafkaProducerService = mockk()
    private val hubspotTicketService: HubspotTicketService = mockk()
    private val ticketController = TicketController(kafkaService, hubspotTicketService)

    private val deal = TestModelFactory.buildOngoingCompanyDeal()

    private val ticketRequest = TicketRequest(
        id = "1",
        stage = "*********",
        dealId = deal.sourceId,
        pendingTaskDescription = "pendingTaskDescription",
        subProcess = CHECK_DOCUMENTOS_COMPROBATORIOS
    )

    private val hubspotTicket = TestModelFactory.buildHubspotTicket(
        dealId = ticketRequest.dealId,
        ongoingCompanyDealId = deal.id,
        ticketId = ticketRequest.id,
        pendingTaskDescription = ticketRequest.pendingTaskDescription,
        stage = getTicketStage(ticketRequest.stage)
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { ticketController }
    }

    @Test
    fun `should return 200 when receiving a new ticket`() {
        coEvery { hubspotTicketService.getByTicketId(ticketRequest.id) } returns NotFoundException().failure()
        coEvery { kafkaService.produce(any()) } returns mockk()

        post(to = "/v1/external/hubspot/ticket", body = ticketRequest) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(ticketRequest.id)
            kafkaService.produce(any())
        }
    }

    @Test
    fun `should return 200 when receiving a existent ticket`() {
        val closedTicketRequest = ticketRequest.copy(stage = "*********")
        coEvery { hubspotTicketService.getByTicketId(closedTicketRequest.id) } returns hubspotTicket.success()
        coEvery { kafkaService.produce(any()) } returns mockk()

        post(to = "/v1/external/hubspot/ticket", body = closedTicketRequest) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(closedTicketRequest.id)
            kafkaService.produce(any())
        }
    }

    @Test
    fun `should sync when receiving an existent ticket with an invalid parameter and invalid status`() {
        val invalidTicketRequest = ticketRequest.copy(stage = "wrong_stage", pendingTaskDescription = null)
        coEvery { hubspotTicketService.getByTicketId(invalidTicketRequest.id) } returns hubspotTicket.success()
        coEvery { kafkaService.produce(any()) } returns mockk()

        post(to = "/v1/external/hubspot/ticket", body = invalidTicketRequest) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(invalidTicketRequest.id)
            kafkaService.produce(any())
        }
    }

    @Test
    fun `should not sync when receiving a new ticket with an invalid parameter `() {
        val invalidTicketRequest = ticketRequest.copy(pendingTaskDescription = null)
        coEvery { hubspotTicketService.getByTicketId(invalidTicketRequest.id) } returns NotFoundException().failure()

        post(to = "/v1/external/hubspot/ticket", body = invalidTicketRequest) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(invalidTicketRequest.id)
        }
        coVerifyNone { kafkaService.produce(any()) }
    }

    @Test
    fun `should not sync when receiving a new ticket with an invalid status `() {
        val closedTicketRequest = ticketRequest.copy(stage = "*********")
        coEvery { hubspotTicketService.getByTicketId(closedTicketRequest.id) } returns NotFoundException().failure()

        post(to = "/v1/external/hubspot/ticket", body = closedTicketRequest) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(closedTicketRequest.id)
        }
        coVerifyNone { kafkaService.produce(any()) }
    }

    @Test
    fun `should return Bad Request when could not search for ticket`() {
        coEvery { hubspotTicketService.getByTicketId(ticketRequest.id) } returns BadRequestException().failure()
        coEvery { kafkaService.produce(any()) } returns mockk()

        post(to = "/v1/external/hubspot/ticket", body = ticketRequest) { response ->
            ResponseAssert.assertThat(response).isBadRequest()
        }

        coVerifyOnce {
            hubspotTicketService.getByTicketId(ticketRequest.id)
            kafkaService.produce(any())
        }
    }
}
