package br.com.alice.healthplan.consumers

import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.services.HealthPlanTaskServiceImpl
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class DraftAppointmentDeletedConsumerTest : ConsumerTest() {

    private val healthPlantTaskService: HealthPlanTaskServiceImpl = mockk()
    private val consumer = DraftAppointmentDeletedConsumer(
        healthPlantTaskService
    )

    private val personId = PersonId()
    private val staffId = RangeUUID.generate()
    private val appointmentId = RangeUUID.generate()
    private val appointment = TestModelFactory.buildAppointment()
        .copy(
            id = appointmentId,
            personId = personId,
            staffId = staffId
        )

    private val event = DraftAppointmentDeletedEvent(
        appointmentId = appointmentId,
        discardedType = AppointmentDiscardedType.WRONG_APPOINTMENT_TYPE,
        description = "Descartado",
        personId = personId,
        type = appointment.type,
        staffId = staffId,
        createdAt = LocalDateTime.now()
    )

    private val referralTask = TestModelFactory.buildHealthPlanTask()
    private val testRequestTask = TestModelFactory.buildHealthPlanTaskTestRequest()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        healthPlantTaskService
    )

    @Test
    fun `#deleteDraftTasksByAppointment returns false when flag is disabled`() = runBlocking {
        withFeatureFlag(FeatureNamespace.HEALTH_PLAN, "enable_delete_draft_task_by_draft_appointment_deleted", false) {

            val result = consumer.deleteDraftTasksByAppointment(event)
            assertThat(result).isSuccessWithData(false)

            coVerify { healthPlantTaskService wasNot called }
        }
    }

    @Test
    fun `#deleteDraftTasksByAppointment returns false when staff flag is enabled but staff is not in enabled`() = runBlocking {
        withFeatureFlags(FeatureNamespace.EHR,
            mapOf(
                "enable_staff_automatic_process_task_by_appointment_events" to true,
                "should_display_new_healthplan" to listOf(RangeUUID.generate())
            )
        ) {

            val result = consumer.deleteDraftTasksByAppointment(event)
            assertThat(result).isSuccessWithData(false)

            coVerify { healthPlantTaskService wasNot called }
        }
    }

    @Test
    fun `#deleteDraftTasksByAppointment tasks after delete all`() = runBlocking {
        withFeatureFlag(FeatureNamespace.HEALTH_PLAN, "enable_delete_draft_task_by_draft_appointment_deleted", true) {

            val expectedReferralTask = referralTask.copy(status = HealthPlanTaskStatus.DELETED)
            val expectedTestRequestTask = testRequestTask.copy(status = HealthPlanTaskStatus.DELETED)

            coEvery {
                healthPlantTaskService.findByPersonAndFilters(
                    personId,
                    HealthPlanTaskFilters(
                        appointmentIds = listOf(appointmentId),
                        statuses = listOf(HealthPlanTaskStatus.DRAFT)
                    )
                )
            } returns listOf(referralTask, testRequestTask).success()

            coEvery {
                healthPlantTaskService.updateTask(
                    task =  referralTask.copy(
                        status = HealthPlanTaskStatus.DELETED
                    )
                )
            } returns expectedReferralTask.success()

            coEvery {
                healthPlantTaskService.updateTask(
                    task =  testRequestTask.copy(
                        status = HealthPlanTaskStatus.DELETED
                    )
                )
            } returns expectedTestRequestTask.success()

            val result = consumer.deleteDraftTasksByAppointment(event)
            assertThat(result).isSuccessWithData(
                listOf(expectedReferralTask, expectedTestRequestTask)
            )

            coVerifyOnce { healthPlantTaskService.findByPersonAndFilters(any(), any())}
            coVerify(exactly = 2) { healthPlantTaskService.updateTask(any(), any()) }
        }
    }

    @Test
    fun `#deleteDraftTasksByAppointment return tasks after delete all when staff is permitted`() = runBlocking {
        withFeatureFlags(FeatureNamespace.EHR,
            mapOf(
                "enable_staff_automatic_process_task_by_appointment_events" to true,
                "should_display_new_healthplan" to listOf(event.payload.staffId)
            )
        ) {

            val expectedReferralTask = referralTask.copy(status = HealthPlanTaskStatus.DELETED)
            val expectedTestRequestTask = testRequestTask.copy(status = HealthPlanTaskStatus.DELETED)

            coEvery {
                healthPlantTaskService.findByPersonAndFilters(
                    personId,
                    HealthPlanTaskFilters(
                        appointmentIds = listOf(appointmentId),
                        statuses = listOf(HealthPlanTaskStatus.DRAFT)
                    )
                )
            } returns listOf(referralTask, testRequestTask).success()

            coEvery {
                healthPlantTaskService.updateTask(
                    task =  referralTask.copy(
                        status = HealthPlanTaskStatus.DELETED
                    )
                )
            } returns expectedReferralTask.success()

            coEvery {
                healthPlantTaskService.updateTask(
                    task =  testRequestTask.copy(
                        status = HealthPlanTaskStatus.DELETED
                    )
                )
            } returns expectedTestRequestTask.success()

            val result = consumer.deleteDraftTasksByAppointment(event)
            assertThat(result).isSuccessWithData(
                listOf(expectedReferralTask, expectedTestRequestTask)
            )

            coVerifyOnce { healthPlantTaskService.findByPersonAndFilters(any(), any())}
            coVerify(exactly = 2) { healthPlantTaskService.updateTask(any(), any()) }
        }
    }
}
