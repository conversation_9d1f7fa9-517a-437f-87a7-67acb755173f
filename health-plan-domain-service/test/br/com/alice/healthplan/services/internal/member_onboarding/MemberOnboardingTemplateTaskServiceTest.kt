package br.com.alice.healthplan.services.internal.member_onboarding

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace.HEALTH_PLAN
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.healthlogic.client.HealthScoreResultService
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals

class MemberOnboardingTemplateTaskServiceTest {
    private val personService: PersonService = mockk()
    private val riskService: RiskService = mockk()
    private val healthScoreResultService: HealthScoreResultService = mockk()

    private val memberOnboardingTemplateTaskService = MemberOnboardingTemplateTaskService(
        personService,
        riskService,
        healthScoreResultService
    )

    private val person = TestModelFactory.buildPerson()
    private val risk = TestModelFactory.buildRisk(personId = person.id)
    private val templateIdAdultTarget = RangeUUID.generate()
    private val templateIdChildTarget = RangeUUID.generate()
    private val templateIdOthersChild = RangeUUID.generate()
    private val templateIdOthersAdolescent = RangeUUID.generate()
    private val templateIdOthersAdultNotLowOutcome = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomeFood = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomeQualityLife = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomeHabits = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomePhysicalActivity = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomeMental = RangeUUID.generate()
    private val templateIdOtherAdultLowOutcomeSleep = RangeUUID.generate()
    private val clinicalOutcomeRecord = TestModelFactory.buildClinicalOutcomeRecord(outcome = BigDecimal.valueOf(600))
    private val outcomeConfQualityLife = TestModelFactory.buildOutcomeConf(key = "EUROQOL_SCORE_MAGENTA")
    private val outcomeConfFood = TestModelFactory.buildOutcomeConf(key = "FOOD_SCORE_MAGENTA")
    private val outcomeConfHabits = TestModelFactory.buildOutcomeConf(key = "HABITS_SCORE_MAGENTA")
    private val outcomeConfPhysicalActivity = TestModelFactory.buildOutcomeConf(key = "IPAQ_SCORE_MAGENTA")
    private val outcomeConfMental = TestModelFactory.buildOutcomeConf(key = "MENTAL_SCORE_MAGENTA")
    private val outcomeConfSleep = TestModelFactory.buildOutcomeConf(key = "MSQ_SCORE_MAGENTA")
    private val outcomeRecords = listOf(
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfQualityLife
        ),
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfFood
        ),
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfHabits
        ),
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfPhysicalActivity
        ),
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfMental
        ),
        EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord,
            outcomeConfSleep
        )
    )


    @AfterTest
    fun after() {
        confirmVerified(personService, riskService, healthScoreResultService)
    }

    @Test
    fun `#getTemplate - should return templateIdAdultTarget`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_referral_to_adult_target_from_member_onboarding_completed",
            value = templateIdAdultTarget.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(12))
            val risk = risk.copy(riskDescription = RiskDescription.TARGET)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdAdultTarget), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateIdChildTarget`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_referral_to_child_target_from_member_onboarding_completed",
            value = templateIdChildTarget.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(11))
            val risk = risk.copy(riskDescription = RiskDescription.TARGET)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdChildTarget), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateIdOthersChild`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_child_risk_not_target_from_member_onboarding_completed",
            value = templateIdOthersChild.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(11))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersChild), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateIdOthersChild if risk is null`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_child_risk_not_target_from_member_onboarding_completed",
            value = templateIdOthersChild.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(11))

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns NotFoundException("").failure()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersChild), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }


    @Test
    fun `#getTemplate - should return templateIdOthersAdolescent`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adolescent_risk_not_target_from_member_onboarding_completed",
            value = templateIdOthersAdolescent.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(12))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersAdolescent), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateIdOthersAdolescent if risk is null`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adolescent_risk_not_target_from_member_onboarding_completed",
            value = templateIdOthersAdolescent.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(12))

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns NotFoundException("").failure()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersAdolescent), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }


    @Test
    fun `#getTemplate - should return templateIdChildTarget if baby not is TARGET`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_referral_to_child_target_from_member_onboarding_completed",
            value = templateIdChildTarget.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now())
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdChildTarget), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return empty if adult not have score magenta`() = runBlocking {
        val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
        val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()
        coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns emptyList<EnrichedClinicalOutcomeRecord>().success()

        val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

        assertEquals(emptyList(), result)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { riskService.getByPerson(any()) }
        coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }

    }

    @Test
    fun `#getTemplate - should return empty if adult not have score magenta minor of 500`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_not_low_from_member_onboarding_completed",
            value = templateIdOthersAdultNotLowOutcome.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersAdultNotLowOutcome), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return empty if adult not have score magenta minor of 500 if risk is null`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_not_low_from_member_onboarding_completed",
            value = templateIdOthersAdultNotLowOutcome.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns NotFoundException("").failure()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOthersAdultNotLowOutcome), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of food pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_food_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomeFood.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfFood
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeFood), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of quality life pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_quality_life_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomeQualityLife.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfQualityLife
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeQualityLife), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of habits pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_habits_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomeHabits.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfHabits
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeHabits), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of physical activity pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_physical_activity_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomePhysicalActivity.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfPhysicalActivity
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomePhysicalActivity), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of mental pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_mental_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomeMental.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfMental
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeMental), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return templateId of sleep pillar`() = runBlocking {
        withFeatureFlag(
            namespace = HEALTH_PLAN,
            key = "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_sleep_from_member_onboarding_completed",
            value = templateIdOtherAdultLowOutcomeSleep.toString()
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfSleep
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeSleep), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return the 2 pillars with the most defects`() = runBlocking {
        withFeatureFlags(
            HEALTH_PLAN,
            mapOf(
                "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_food_from_member_onboarding_completed" to templateIdOtherAdultLowOutcomeFood.toString(),
                "template_task_other_to_adult_risk_not_target_and_score_magenta_low_pillar_quality_life_from_member_onboarding_completed" to templateIdOtherAdultLowOutcomeQualityLife.toString()
            )
        ) {
            val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
            val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
            val outcomeRecords = listOf(
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                    outcomeConfFood
                ),
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(350)),
                    outcomeConfQualityLife
                ),
                EnrichedClinicalOutcomeRecord(
                    clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(450)),
                    outcomeConfHabits
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

            val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

            assertEquals(listOf(templateIdOtherAdultLowOutcomeQualityLife, templateIdOtherAdultLowOutcomeFood), result)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { riskService.getByPerson(any()) }
            coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
        }
    }

    @Test
    fun `#getTemplate - should return empty list if not match key of outcomeConf`() = runBlocking {
        val person = person.copy(dateOfBirth = LocalDateTime.now().minusYears(28))
        val risk = risk.copy(riskDescription = RiskDescription.HIGH_RISK)
        val outcomeRecords = listOf(
            EnrichedClinicalOutcomeRecord(
                clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(400)),
                outcomeConfFood.copy(key = "TEST")
            ),
            EnrichedClinicalOutcomeRecord(
                clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(350)),
                outcomeConfQualityLife.copy(key = "TEST_2")
            ),
            EnrichedClinicalOutcomeRecord(
                clinicalOutcomeRecord.copy(outcome = BigDecimal.valueOf(450)),
                outcomeConfHabits.copy(key = "TEST_3")
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()
        coEvery { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(person.id) } returns outcomeRecords.success()

        val result = memberOnboardingTemplateTaskService.getTemplate(person.id)

        assertEquals(emptyList(), result)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { riskService.getByPerson(any()) }
        coVerifyOnce { healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(any()) }
    }
}
