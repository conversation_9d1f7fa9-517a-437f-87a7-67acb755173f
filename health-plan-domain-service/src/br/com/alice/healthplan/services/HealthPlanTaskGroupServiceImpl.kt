package br.com.alice.healthplan.services

import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.services.HealthPlanTaskGroupDataService
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.converters.HealthPlanTaskGroupConverter
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import java.util.UUID

class HealthPlanTaskGroupServiceImpl(
    private val healthPlanTaskGroupDataService: HealthPlanTaskGroupDataService
) : HealthPlanTaskGroupService {

    override suspend fun create(
        personId: PersonId,
        group: HealthPlanTaskGroupTransport
    ): Result<HealthPlanTaskGroupTransport, Throwable> =
        HealthPlanTaskGroupConverter.convert(group, personId).let { groupModel ->
            healthPlanTaskGroupDataService.add(groupModel).map { it.convertTo(HealthPlanTaskGroupTransport::class) }
        }

    override suspend fun getOrCreate(
        personId: PersonId,
        group: HealthPlanTaskGroupTransport
    ): Result<HealthPlanTaskGroupTransport, Throwable> =
        healthPlanTaskGroupDataService.findOne { where { name.eq(group.name) and this.personId.eq(personId) } }
            .map { it.convertTo(HealthPlanTaskGroupTransport::class) }
            .mapError {
                when (it) {
                    is NotFoundException -> return create(personId, group)
                    else -> it
                }
            }

    override suspend fun get(id: UUID): Result<HealthPlanTaskGroupTransport, Throwable> =
        healthPlanTaskGroupDataService.get(id).map { it.convertTo(HealthPlanTaskGroupTransport::class) }

    override suspend fun findByIds(ids: List<UUID>): Result<List<HealthPlanTaskGroupTransport>, Throwable> =
        healthPlanTaskGroupDataService.find {
            where { id.inList(ids) }
        }.map { groups ->
            groups.map { it.convertTo(HealthPlanTaskGroupTransport::class) }
        }
}
