package br.com.alice.healthplan.services

import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.HealthPlan
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskType.TEST_REQUEST
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.data.layer.services.HealthPlanDataService
import br.com.alice.data.layer.services.UpdateRequest
import br.com.alice.data.layer.strategies.ConflictResolutionStrategy
import br.com.alice.documentsigner.services.DocumentPrinterService
import br.com.alice.healthplan.client.HealthPlanService
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.models.HealthPlanTaskGroupWithTasksTransport
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class HealthPlanServiceImpl(
    private val healthPlanDataService: HealthPlanDataService,
    private val staffService: StaffService,
    private val personService: PersonService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService,
    private val documentPrinterService: DocumentPrinterService,
) : HealthPlanService {

    override suspend fun get(id: UUID): Result<HealthPlanTransport, Throwable> =
        healthPlanDataService.get(id).map {
            it.convertTo(HealthPlanTransport::class)
        }

    override suspend fun getByPerson(personId: PersonId): Result<HealthPlanTransport, Throwable> =
        getRawByPerson(personId).map {
            it.convertTo(HealthPlanTransport::class)
        }

    override suspend fun getRawByPerson(personId: PersonId): Result<HealthPlan, Throwable> =
        healthPlanDataService.findOne {
            where { this.personId.eq(personId) }
        }.coFoldNotFound {
            healthPlanDataService.add(HealthPlan(personId = personId, description = ""))
        }

    override suspend fun update(
        staffId: UUID,
        healthPlan: HealthPlanTransport
    ): Result<HealthPlanTransport, Throwable> =
        getHealthPlanAndCheckConflicts(staffId, healthPlan.personId, healthPlan).map { model ->
            healthPlanDataService.update(
                model.copy(
                    editorStaffIds = model.editorStaffIds + staffId,
                    description = healthPlan.description,
                    healthGoal = healthPlan.healthGoal
                )
            ).map {
                it.convertTo(HealthPlanTransport::class)
            }.get()
        }

    override suspend fun getTestRequestPdfByIds(healthPlanTaskIds: List<UUID>): Result<ByteArray, Throwable> {
        val healthPlanTasks = healthPlanTaskService.getByIds(healthPlanTaskIds).get()
        val testRequestPdf = getTestRequestPdf(healthPlanTasks)

        return Result.of<ByteArray, Throwable> {
            testRequestPdf?.get() ?: throw InvalidArgumentException(
                code = "empty_test_requests",
                message = "Test Request is empty for health_plan_task_ids=$healthPlanTaskIds"
            )
        }.thenError { exception ->
            logger.error(
                "Error to generate test request PDF",
                "health_plan_task_ids" to healthPlanTaskIds,
                exception.message
            )
            exception.failure()
        }
    }

    private suspend fun getTestRequestPdf(healthPlanTasks: List<HealthPlanTask>): Result<ByteArray, Throwable>? =
     healthPlanTasks
        .filter { it.type == TEST_REQUEST && it.status == ACTIVE }
        .takeIf { it.isNotEmpty() }
        ?.let { tasks ->
            val testRequests = tasks.map { it.specialize<TestRequest>() }

            val person = personService.get(testRequests.first().personId).get()

            val requestersId = testRequests.map { it.requestersStaffIds }.flatten().distinct()
            val requesterId = testRequests.first().lastRequesterStaffId

            val requester = staffService.get(requesterId).get()
            val staff = requester.takeIf { it.isPhysician() || person.isTest }
                ?: staffService.findByList(requestersId).get().firstOrNull { it.isPhysician() || person.isTest }
                ?: requester

            documentPrinterService.printTestRequestPdf(testRequests, staff.id)
        }

    override suspend fun lastUpdatedBetweenDateRange(
        dateTimeFromOffset: LocalDateTime,
        dateTimeToOffset: LocalDateTime
    ): Result<List<HealthPlan>, Throwable> =
        healthPlanDataService.find {
            where {
                this.updatedAt.greater(dateTimeFromOffset) and
                    this.updatedAt.lessEq(dateTimeToOffset)
            }
        }

    override suspend fun getGroupWithTasks(groupId: UUID): Result<HealthPlanTaskGroupWithTasksTransport, Throwable> =
        HealthPlanTaskGroupWithTasksTransport(
            healthPlanTaskGroup = healthPlanTaskGroupService.get(groupId).get(),
            tasks = healthPlanTaskService.getByGroup(groupId).get()
        ).success()

    private suspend fun getHealthPlanAndCheckConflicts(
        loggedStaffId: UUID,
        personId: String,
        request: UpdateRequest
    ) =
        healthPlanDataService.findOneOrNull { where { this.personId.eq(personId) } }?.let {
            if (ConflictResolutionStrategy.hasConflict(request, it) { loggedStaffId }) {
                ConflictException("Conflict on update of health_plan_id=${it.id}").failure()
            } else it.success()
        } ?: NotFoundException("Not found health plan for personId: $personId").failure()
}
