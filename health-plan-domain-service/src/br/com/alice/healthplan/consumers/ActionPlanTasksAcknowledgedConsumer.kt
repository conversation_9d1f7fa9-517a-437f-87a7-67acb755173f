package br.com.alice.healthplan.consumers

import br.com.alice.action.plan.notifier.ActionPlanTasksAcknowledgedEvent
import br.com.alice.healthplan.client.HealthPlanTaskService

class ActionPlanTasksAcknowledgedConsumer(
    private val healthPlanTaskService: HealthPlanTaskService
) : Consumer() {

    suspend fun handleActionPlanTasksAcknowledged(event: ActionPlanTasksAcknowledgedEvent) = withSubscribersEnvironment {
        val taskIds = event.payload.tasks.map { it.id }
        val acknowledgedAt = event.payload.acknowledgedAt

        healthPlanTaskService.acknowledgeTasks(taskIds, acknowledgedAt)
    }
}
