package br.com.alice.healthplan.consumers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_GROUP_TEMPLATE
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_TEMPLATE
import br.com.alice.healthlogic.event.BUDActionEvent
import br.com.alice.healthlogic.event.HealthLogicResultEvent
import br.com.alice.healthplan.services.internal.health_logics.TaskTemplateActionService
import com.github.kittinunf.result.success

class HealthLogicResultEventConsumer(
    private val taskTemplateActionService: TaskTemplateActionService,
) : Consumer() {

    private val allowedActions = listOf(HEALTH_PLAN_TASK_GROUP_TEMPLATE, HEALTH_PLAN_TASK_TEMPLATE)

    suspend fun handleHealthLogicResultEvent(event: HealthLogicResultEvent) = withSubscribersEnvironment {
        val action = event.payload.action
        val healthLogicRecord = event.payload.healthLogicRecord
        val caseRecordId = event.payload.caseRecordId

        if (!allowedActions.contains(action.type)) return@withSubscribersEnvironment true.success()

        logger.info(
            "HealthLogicResultEventConsumer::handleHealthLogicResultEvent",
            "HealthLogicResultEvent" to event
        )

        taskTemplateActionService.createHealthPlanTaskFromTemplate(healthLogicRecord, action, caseRecordId)
    }

    suspend fun handleBUDActionEvent(event: BUDActionEvent) = withSubscribersEnvironment {
        val action = event.payload.action

        if (!allowedActions.contains(action.type) || event.payload.staffId == null)
            return@withSubscribersEnvironment true.success()
        val navigationId = event.payload.navigationId
        val personId = event.payload.personId
        val staffId = event.payload.staffId

        logger.info(
            "HealthLogicResultEventConsumer::handleBUDActionEvent",
            "HealthLogicResultEvent" to event
        )

        taskTemplateActionService.createTasksFromBUD(
            personId = personId,
            staffId = staffId!!,
            navigationId = navigationId,
            action = action
        )
    }
}
