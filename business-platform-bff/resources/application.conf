ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ br.com.alice.bff.business.ApplicationKt.module ]
    }
}

systemEnv = "test"
systemEnv = ${?ENVIRONMENT}
subEnv = ${?SUB_ENV}
AWS_DEFAULT_REGION = "us-east-1"

development {
    AWS_ACCESS_KEY_ID = "ACCESS_KEY_HERE"
    AWS_SECRET_ACCESS_KEY = "SECRET_ACCESS_KEY"
    AWS_SESSION_TOKEN = "AWS_SESSION_TOKEN"

    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
        noReplySenderName = "Alice"
        noReplySenderEmail = "<EMAIL>"
        bccRecipientName = "Alice"
        bccRecipientEmails = "<EMAIL>,<EMAIL>"
        templateName = "firebase_sign_in_portal_rh_magic_link"
    }

    externalApiAuth {
        user = "test"
        pass = "test"
    }

    cognito {
        secretKey = "test_secret_key"
    }
}

test {
    AWS_ACCESS_KEY_ID = "ACCESS_KEY_HERE"
    AWS_SECRET_ACCESS_KEY = "SECRET_ACCESS_KEY"
    AWS_SESSION_TOKEN = "AWS_SESSION_TOKEN"

     mailer {
            senderName = "Alice"
            senderEmail = "<EMAIL>"
            noReplySenderName = "Alice"
            noReplySenderEmail = "<EMAIL>"
            bccRecipientName = "Alice"
            bccRecipientEmails = "<EMAIL>,<EMAIL>"
            templateName = "firebase_sign_in_portal_rh_magic_link"
        }

    cognito {
        secretKey = "test_secret_key"
    }

    externalApiAuth {
        user = "test"
        pass = "test"
    }
}

production {
    AWS_ACCESS_KEY_ID = ${?AWS_ACCESS_KEY_ID}
    AWS_SECRET_ACCESS_KEY = ${?AWS_ACCESS_KEY_ID}
    AWS_SESSION_TOKEN = ${?AWS_SESSION_TOKEN}

    mailer {
        senderName = "Alice"
        senderName = ${?DEFAULT_EMAIL_SENDER_NAME}

        senderEmail = "<EMAIL>"
        senderEmail = ${?DEFAULT_EMAIL_SENDER_ADDRESS}

        noReplySenderName = "Alice"
        noReplySenderName = ${?NO_REPLY_EMAIL_SENDER_NAME}

        noReplySenderEmail = "<EMAIL>"
        noReplySenderEmail = ${?NO_REPLY_EMAIL_SENDER_ADDRESS}

        bccRecipientName = "Alice"
        bccRecipientName = ${?DEFAULT_BCC_RECIPIENT_NAME}

        bccRecipientEmails = "<EMAIL>"
        bccRecipientEmails = ${?DEFAULT_BCC_RECIPIENT_EMAILS}

        templateName = "firebase_sign_in_portal_rh_magic_link"
        templateName = ${?SES_TEMPLATE_NAME}
    }

    cognito {
        secretKey = "174EBBD88EF151B2513958623D2FC"
        secretKey = ${?COGNITO_SECRET_KEY}
    }

    externalApiAuth {
        user = "test"
        user = ${?EXTERNAL_USER}
        pass = "test"
        pass = ${?EXTERNAL_PASS}
    }
}
