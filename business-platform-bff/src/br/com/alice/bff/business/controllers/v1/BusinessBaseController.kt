package br.com.alice.bff.business.controllers.v1

import br.com.alice.authentication.currentEmail
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.AuthorizationException
import kotlinx.coroutines.coroutineScope
import java.util.UUID

open class BusinessBaseController(private val companyStaffService: CompanyStaffService) : Controller() {

    private suspend fun getCurrentCompanyStaff() = companyStaffService.getLatestByEmail(currentEmail()).get()

    protected suspend fun companyStaff() = getCurrentCompanyStaff()

    suspend fun withCompany(companyId: UUID, block: suspend () -> Response) = coroutineScope {
        val staff = companyStaff() // Call companyStaff as a suspend function
        if (staff.companyId == companyId) block()
        else throw AuthorizationException("This Staff doesn't have permission to access this company")
    }
}
