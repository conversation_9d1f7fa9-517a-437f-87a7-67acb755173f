package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.CompanyInfoUpdateRequestTransport
import br.com.alice.data.layer.helpers.TestModelFactory
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class CompanyConverterTest {
    private val company = TestModelFactory.buildCompany()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

    @Test
    fun `#toCompanyInfoResponseTransport should return correct response`() = runBlocking {
        val response = company.toCompanyInfoResponseTransport(billingAccountableParty)

        assert(response.name == company.name)
        assert(response.legalName == company.legalName)
        assert(response.legalResponsibleEmail == company.email)
        assert(response.financialManagerEmail == billingAccountableParty.email)
        assert(response.financialInvoiceEmail == billingAccountableParty.invoiceEmail)
        assert(response.phoneNumber == company.phoneNumber)
        assert(response.documentNumber == company.cnpj)
    }

    @Test
    fun `#toCompanyInfoUpdateResponseTransport should return correct response`() = runBlocking {
        val response = company.toCompanyInfoUpdateResponseTransport(billingAccountableParty)

        assert(response.id == company.id.toString())
        assert(response.name == company.name)
        assert(response.legalResponsibleEmail == company.email)
        assert(response.financialManagerEmail == billingAccountableParty.email)
        assert(response.financialInvoiceEmail == billingAccountableParty.invoiceEmail)
        assert(response.phoneNumber == company.phoneNumber)
    }

    @Test
    fun `#toCompanyInfoUpdate should return correct response`() = runBlocking {
        val companyInfoUpdateRequestTransport = CompanyInfoUpdateRequestTransport(
            name = "New Company Name",
            phoneNumber = "New Phone Number",
            legalResponsibleEmail = "<EMAIL>",
            financialManagerEmail = "<EMAIL>",
            financialInvoiceEmail = "<EMAIL>",
        )

        val response = companyInfoUpdateRequestTransport.toCompanyInfoUpdate()

        assert(response.name == companyInfoUpdateRequestTransport.name)
        assert(response.legalResponsibleEmail == companyInfoUpdateRequestTransport.legalResponsibleEmail)
        assert(response.financialManagerEmail == companyInfoUpdateRequestTransport.financialManagerEmail)
        assert(response.financialInvoiceEmail == companyInfoUpdateRequestTransport.financialInvoiceEmail)
        assert(response.phoneNumber == companyInfoUpdateRequestTransport.phoneNumber)
    }
}
