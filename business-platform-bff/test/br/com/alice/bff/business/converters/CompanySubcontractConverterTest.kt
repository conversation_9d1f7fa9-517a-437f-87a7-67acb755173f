package br.com.alice.bff.business.converters

import br.com.alice.bff.business.converters.v1.CompanySubcontractConverter.toResponse
import br.com.alice.bff.business.models.v1.CompanySubcontractResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions
import kotlin.test.Test

class CompanySubcontractConverterTest {
    @Test
    fun `should convert correctly`() {
        val subcontract = TestModelFactory.buildCompanySubContract()
        val result = subcontract.toResponse()

        val expected = CompanySubcontractResponse(
            id = subcontract.id,
            companyId = subcontract.companyId,
            title = subcontract.title,
        )

        Assertions.assertThat(result).isEqualTo(expected)
    }
}
