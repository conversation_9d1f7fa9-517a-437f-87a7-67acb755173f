package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.Chart
import br.com.alice.bff.business.models.v2.DistributionItem
import br.com.alice.bff.business.models.v2.ItemPeriod
import br.com.alice.bff.business.models.v2.ItemSplittedValue
import br.com.alice.bff.business.models.v2.ScoreMagentaResponse
import br.com.alice.bff.business.models.v2.ScoreMagentaTopics
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class CompanyScoreMagentaConverterTest {
    private val referenceDate = LocalDateTime.of(2024, 11, 13, 0, 0, 0).atEndOfTheDay()

    private val company = TestModelFactory.buildCompany(contractStartedAt = referenceDate.minusYears(1))
    private val range = IntRange(1, 11)

    private val companyScoreMagentaList = range.map {
        TestModelFactory.buildCompanyScoreMagenta(
            companyId = company.id,
            referenceStartDate = referenceDate.minusMonths(it.toLong()).withDayOfMonth(1).atBeginningOfTheDay(),
            referenceEndDate = referenceDate.minusMonths(it.toLong())
                .withDayOfMonth(referenceDate.minusMonths(it.toLong()).toLocalDate().lengthOfMonth()).atEndOfTheDay(),
            adhesion = 25f
        )
    }

    private val companyScoreMagentaWithEmptyGroupList = listOf(
        TestModelFactory.buildCompanyScoreMagenta(
            companyId = company.id,
            referenceStartDate = LocalDate.of(2024, 12, 1).atBeginningOfTheDay(),
            referenceEndDate = LocalDate.of(2024, 12, 31).atEndOfTheDay(),
            adhesion = 25f
        ),
        TestModelFactory.buildCompanyScoreMagenta(
            companyId = company.id,
            referenceStartDate = LocalDate.of(2024, 11, 1).atBeginningOfTheDay(),
            referenceEndDate = LocalDate.of(2024, 11, 30).atEndOfTheDay(),
            adhesion = 25f
        ),
        TestModelFactory.buildCompanyScoreMagenta(
            companyId = company.id,
            referenceStartDate = LocalDate.of(2024, 10, 1).atBeginningOfTheDay(),
            referenceEndDate = LocalDate.of(2024, 10, 31).atEndOfTheDay(),
            adhesion = 25f
        )
    )

    private val lgpdCompanyScoreMagentaList = range.map {
        TestModelFactory.buildCompanyScoreMagenta(
            companyId = company.id,
            referenceStartDate = referenceDate.minusMonths(it.toLong()).withDayOfMonth(1).atBeginningOfTheDay(),
            referenceEndDate = referenceDate.minusMonths(it.toLong())
                .withDayOfMonth(referenceDate.minusMonths(it.toLong()).toLocalDate().lengthOfMonth()).atEndOfTheDay(),
            adhesion = 50f,
            answersQuantity = 1
        )
    }

    private val expected = ScoreMagentaResponse(
        general = Chart(
            name = "Nota geral",
            explanation = "Faltam 300 pontos para alcançar o nível Excelente.",
            distribution = listOf(
                DistributionItem(
                    key = "general_score",
                    label = "Nota geral",
                    value = 700,
                    totalAnswers = 300,
                    adhesion = 75,
                    benchmark = 640f
                )
            ),
        ),
        generalByTopic = Chart(
            name = "Nota geral por pilar",
            explanation = "",
            distribution = listOf(
                DistributionItem(
                    key = ScoreMagentaTopics.HABITS.name,
                    label = "Hábitos",
                    value = 700,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 0f, good = 0f, bad = 0f),
                    benchmark = 856f
                ),
                DistributionItem(
                    key = ScoreMagentaTopics.LIFE_QUALITY.name,
                    label = "Qualidade de vida",
                    value = 750,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 0f, good = 0f, bad = 0f),
                    benchmark = 753f
                ),
                DistributionItem(
                    key = ScoreMagentaTopics.SLEEP.name,
                    label = "Sono",
                    value = 600,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 60f, good = 30f, bad = 10f),
                    benchmark = 583f
                ),
                DistributionItem(
                    key = ScoreMagentaTopics.PHYSICAL_ACTIVITY.name,
                    label = "Atividade física",
                    value = 750,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 0f, good = 0f, bad = 0f),
                    benchmark = 484f
                ),
                DistributionItem(
                    key = ScoreMagentaTopics.MENTAL_HEALTH.name,
                    label = "Saúde mental",
                    value = 800,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 0f, good = 0f, bad = 0f),
                    benchmark = 539f
                ),
                DistributionItem(
                    key = ScoreMagentaTopics.EATING.name,
                    label = "Alimentação",
                    value = 650,
                    totalAnswers = 300,
                    adhesion = 75,
                    splittedValue = ItemSplittedValue(excellent = 0f, good = 0f, bad = 0f),
                    benchmark = 631f
                )
            )
        ),
        generalHistoric = Chart(
            name = "Histórico da nota geral",
            explanation = "",
            distribution = listOf(
                DistributionItem(
                    key = "general_score_historic",
                    label = "Histórico da nota geral",
                    value = 700,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                    isPartial = false,
                    benchmark = 640f
                ),
                DistributionItem(
                    key = "general_score_historic",
                    label = "Histórico da nota geral",
                    value = 700,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                    isPartial = false,
                    benchmark = 640f
                ),
                DistributionItem(
                    key = "general_score_historic",
                    label = "Histórico da nota geral",
                    value = 700,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                    isPartial = false,
                    benchmark = 640f
                ),
                DistributionItem(
                    key = "general_score_historic",
                    label = "Histórico da nota geral",
                    value = 700,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                    isPartial = false,
                    benchmark = 640f
                )
            )
        ),
        historicByTopic = Chart(
            name = "Histórico dos pilares",
            explanation = "",
            distribution = listOf(
                DistributionItem(
                    key = "HABITS",
                    label = "Hábitos",
                    value = 700,
                    benchmark = 856f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "LIFE_QUALITY",
                    label = "Qualidade de vida",
                    value = 750,
                    benchmark = 753f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "SLEEP",
                    label = "Sono",
                    value = 600,
                    benchmark = 583f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "PHYSICAL_ACTIVITY",
                    label = "Atividade física",
                    value = 750,
                    benchmark = 484f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "MENTAL_HEALTH",
                    label = "Saúde mental",
                    value = 800,
                    benchmark = 539f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "EATING",
                    label = "Alimentação",
                    value = 650,
                    benchmark = 631f,
                    period = ItemPeriod(from = "01/11/2023", to = "31/01/2024"),
                    totalAnswers = 200,
                    adhesion = 50,
                ),
                DistributionItem(
                    key = "HABITS",
                    label = "Hábitos",
                    value = 700,
                    benchmark = 856f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "LIFE_QUALITY",
                    label = "Qualidade de vida",
                    value = 750,
                    benchmark = 753f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "SLEEP",
                    label = "Sono",
                    value = 600,
                    benchmark = 583f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "PHYSICAL_ACTIVITY",
                    label = "Atividade física",
                    value = 750,
                    benchmark = 484f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "MENTAL_HEALTH",
                    label = "Saúde mental",
                    value = 800,
                    benchmark = 539f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "EATING",
                    label = "Alimentação",
                    value = 650,
                    benchmark = 631f,
                    period = ItemPeriod(from = "01/02/2024", to = "30/04/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "HABITS",
                    label = "Hábitos",
                    value = 700,
                    benchmark = 856f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "LIFE_QUALITY",
                    label = "Qualidade de vida",
                    value = 750,
                    benchmark = 753f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "SLEEP",
                    label = "Sono",
                    value = 600,
                    benchmark = 583f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "PHYSICAL_ACTIVITY",
                    label = "Atividade física",
                    value = 750,
                    benchmark = 484f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "MENTAL_HEALTH",
                    label = "Saúde mental",
                    value = 800,
                    benchmark = 539f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "EATING",
                    label = "Alimentação",
                    value = 650,
                    benchmark = 631f,
                    period = ItemPeriod(from = "01/05/2024", to = "31/07/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "HABITS",
                    label = "Hábitos",
                    value = 700,
                    benchmark = 856f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "LIFE_QUALITY",
                    label = "Qualidade de vida",
                    value = 750,
                    benchmark = 753f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "SLEEP",
                    label = "Sono",
                    value = 600,
                    benchmark = 583f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "PHYSICAL_ACTIVITY",
                    label = "Atividade física",
                    value = 750,
                    benchmark = 484f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "MENTAL_HEALTH",
                    label = "Saúde mental",
                    value = 800,
                    benchmark = 539f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                ),
                DistributionItem(
                    key = "EATING",
                    label = "Alimentação",
                    value = 650,
                    benchmark = 631f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 300,
                    adhesion = 75,
                )
            )
        )
    )

    private val expectedWithEmptyGroupFiltered = ScoreMagentaResponse(
        general = expected.general,
        generalByTopic = expected.generalByTopic,
        generalHistoric = Chart(
            name = "Histórico da nota geral",
            explanation = "",
            distribution = listOf(
                DistributionItem(
                    key = "general_score_historic",
                    label = "Histórico da nota geral",
                    value = 700,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                    isPartial = false,
                    benchmark = 640f
                )
            )
        ),
        historicByTopic = Chart(
            name = "Histórico dos pilares",
            explanation = "",
            distribution = listOf(
                DistributionItem(
                    key = "HABITS",
                    label = "Hábitos",
                    value = 700,
                    benchmark = 856f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                ),
                DistributionItem(
                    key = "LIFE_QUALITY",
                    label = "Qualidade de vida",
                    value = 750,
                    benchmark = 753f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                ),
                DistributionItem(
                    key = "SLEEP",
                    label = "Sono",
                    value = 600,
                    benchmark = 583f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                ),
                DistributionItem(
                    key = "PHYSICAL_ACTIVITY",
                    label = "Atividade física",
                    value = 750,
                    benchmark = 484f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                ),
                DistributionItem(
                    key = "MENTAL_HEALTH",
                    label = "Saúde mental",
                    value = 800,
                    benchmark = 539f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                ),
                DistributionItem(
                    key = "EATING",
                    label = "Alimentação",
                    value = 650,
                    benchmark = 631f,
                    period = ItemPeriod(from = "01/08/2024", to = "31/10/2024"),
                    totalAnswers = 100,
                    adhesion = 25,
                )
            )
        )
    )

    @Test
    fun `#toCompanyScoreMagentaResponse should return correct response`() = mockLocalDateTime(referenceDate) {
        mockLocalDate(referenceDate.toLocalDate()) {
            val result =
                companyScoreMagentaList.toCompanyScoreMagentaResponse(company.contractStartedAt!!.toLocalDate())
            assertEquals(expected.general, result.general)
            assertEquals(expected.generalByTopic, result.generalByTopic)
            assertEquals(expected.generalHistoric, result.generalHistoric)
            assertEquals(expected.historicByTopic, result.historicByTopic)
        }
    }

    @Test
    fun `#toCompanyScoreMagentaResponse should return correct response filtering empty groups`() = mockLocalDateTime(referenceDate) {
        mockLocalDate(referenceDate.toLocalDate()) {
            val result = companyScoreMagentaWithEmptyGroupList.toCompanyScoreMagentaResponse(company.contractStartedAt!!.toLocalDate())
            assertEquals(expectedWithEmptyGroupFiltered.general, result.general)
            assertEquals(expectedWithEmptyGroupFiltered.generalByTopic, result.generalByTopic)
            assertEquals(expectedWithEmptyGroupFiltered.generalHistoric, result.generalHistoric)
            assertEquals(expectedWithEmptyGroupFiltered.historicByTopic, result.historicByTopic)
        }
    }

    @Test
    fun `#toCompanyScoreMagentaResponse should return null if there is not enough answers`() =
        mockLocalDateTime(referenceDate) {
            mockLocalDate(referenceDate.toLocalDate()) {
                val result =
                    lgpdCompanyScoreMagentaList.toCompanyScoreMagentaResponse(company.contractStartedAt!!.toLocalDate())
                assertEquals(null, result.general)
                assertEquals(null, result.generalByTopic)
                assertEquals(null, result.generalHistoric)
                assertEquals(null, result.historicByTopic)
            }
        }

    @Test
    fun `#generateGroups should return correct groups`() {
        val initialDate = LocalDate.of(2024, 1, 1)
        val now = LocalDate.of(2024, 11, 13)
        val result = generateGroups(initialDate, now, now)
        assertEquals(4, result.size)
        assertEquals(LocalDate.of(2024, 1, 1), result[0].initialDate)
        assertEquals(LocalDate.of(2024, 4, 1), result[1].initialDate)
        assertEquals(LocalDate.of(2024, 7, 1), result[2].initialDate)
        assertEquals(LocalDate.of(2024, 10, 1), result[3].initialDate)
        assertFalse(result[0].isPartial)
        assertFalse(result[1].isPartial)
        assertFalse(result[2].isPartial)
        assertTrue(result[3].isPartial)
    }

    @Test
    fun `#generateGroups should return correct groups 2`() {
        val initialDate = LocalDate.of(2024, 2, 1)
        val now = LocalDate.of(2024, 11, 13)
        val result = generateGroups(initialDate, now, now)
        assertEquals(3, result.size)
        assertEquals(LocalDate.of(2024, 2, 1), result[0].initialDate)
        assertEquals(LocalDate.of(2024, 5, 1), result[1].initialDate)
        assertEquals(LocalDate.of(2024, 8, 1), result[2].initialDate)
        assertFalse(result[0].isPartial)
        assertFalse(result[1].isPartial)
        assertFalse(result[2].isPartial)
    }

    @Test
    fun `#generateGroups should return correct groups 3`() {
        val initialDate = LocalDate.of(2024, 3, 1)
        val now = LocalDate.of(2024, 11, 13)

        val result = generateGroups(initialDate, now, now)
        assertEquals(3, result.size)
        assertEquals(LocalDate.of(2024, 3, 1), result[0].initialDate)
        assertEquals(LocalDate.of(2024, 6, 1), result[1].initialDate)
        assertEquals(LocalDate.of(2024, 9, 1), result[2].initialDate)
        assertFalse(result[0].isPartial)
        assertFalse(result[1].isPartial)
        assertTrue(result[2].isPartial)

    }

    @Test
    fun `#generateGroups should return correct groups 4`() {
        val initialDate = LocalDate.of(2024, 10, 1)
        val now = LocalDate.of(2024, 11, 13)

        val result = generateGroups(initialDate, now, now)
        assertEquals(1, result.size)
        assertEquals(LocalDate.of(2024, 10, 1), result[0].initialDate)
        assertTrue(result[0].isPartial)
    }

    @Test
    fun `#generateGroups should return correct groups 5`() {
        val initialDate = LocalDate.of(2024, 11, 1)
        val now = LocalDate.of(2024, 11, 13)
        val result = generateGroups(initialDate, now)
        assertEquals(0, result.size)
    }

    @Test
    fun `#generateGroups should return correct groups 6 `() {
        val initialDate = LocalDate.of(2023, 1, 1)
        val now = LocalDate.of(2024, 11, 13)
        val result = generateGroups(initialDate, now)
        assertEquals(8, result.size)
        assertEquals(LocalDate.of(2023, 1, 1), result[0].initialDate)
        assertEquals(LocalDate.of(2024, 10, 1), result[7].initialDate)
    }

    @Test
    fun `#generateGroups should return correct groups 7`() {
        val initialDate = LocalDate.of(2022, 5, 15)
        val now = LocalDate.of(2024, 11, 13)
        val result = generateGroups(initialDate, now)
        assertEquals(8, result.size)
        assertEquals(LocalDate.of(2022, 11, 1), result[0].initialDate)
        assertEquals(LocalDate.of(2024, 8, 1), result[7].initialDate)
    }
}
