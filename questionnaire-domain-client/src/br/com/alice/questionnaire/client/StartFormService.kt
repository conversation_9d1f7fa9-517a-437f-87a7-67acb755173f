package br.com.alice.questionnaire.client

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import com.github.kittinunf.result.Result

interface StartFormService {
    suspend fun getPreviousAndCurrentQuestion(
        answers: List<HealthFormQuestionAnswer>,
        form: HealthForm,
        source: HealthFormAnswerSource?,
        personId: PersonId
    ): Result<Pair<HealthFormQuestion?, HealthFormQuestion>, Throwable>

}
