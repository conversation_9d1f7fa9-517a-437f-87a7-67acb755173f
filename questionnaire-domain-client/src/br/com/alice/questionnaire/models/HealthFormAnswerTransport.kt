package br.com.alice.questionnaire.models

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import java.util.UUID

data class HealthFormAnswerTransport(
    val healthFormId: UUID,
    val personId: PersonId,
    val questionId: UUID,
    val next: Int?,
    val value: String
)

data class HealthFormPreviousQuestionTransport(
    val healthFormId: UUID,
    val personId: PersonId,
    val questionId: UUID
)

data class HealthFormActionsTransport(
    val currentQuestion: HealthFormQuestion,
    val previousQuestion: HealthFormQuestion? = null,
    val selectedAnswer: HealthFormQuestionAnswer? = null,
)

