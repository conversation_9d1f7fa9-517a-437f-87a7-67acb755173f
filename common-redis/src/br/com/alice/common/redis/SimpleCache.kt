package br.com.alice.common.redis

import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.serialization.gson
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.gson.reflect.TypeToken
import redis.clients.jedis.JedisPool
import redis.clients.jedis.params.SetParams

class SimpleCache(
    private val jedisPool: JedisPool,
    private val prefix: String
) : Cache {

    private val prefixPattern = "$prefix-"

    override suspend fun <T : Any> putIfAbsent(
        key: String,
        data: T,
        expirationTime: Long
    ): Result<T, Throwable> =
        span("putIfAbsent") { span ->
            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix(key))
            span.setAttribute("expiration_time", expirationTime)

            putSingleData(key, data, expirationTime, true).recordResult(span)
        }

    override suspend fun <T : Any> put(
        key: String,
        data: T,
        expirationTime: Long
    ): Result<T, Throwable> =
        span("put") { span ->
            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix(key))
            span.setAttribute("expiration_time", expirationTime)

            putSingleData(key, data, expirationTime).recordResult(span)
        }

    override suspend fun <T : Any> get(
        vararg key: String,
        typeToken: TypeToken<T>,
    ): Result<T, Throwable> =
        span("get") { span ->

            val keyWithPrefix = keyWithPrefix(key)

            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix)
            span.setAttribute("data_type", typeToken.type.typeName)

            resultOf<T, Throwable> {
                jedisPool.resource.use { jedis ->
                    if (key.size == 1) jedis.get(keyWithPrefix(key.first()))
                    else {
                        jedis.mget(*keyWithPrefix)
                            .filterNot { keyWithPrefix.contains(it) } // remove keys from response
                            .toString()
                    }?.let { gson.fromJson<T>(it, typeToken.type) }
                        ?: throw KeyDoesNotExistException(keyWithPrefix.toString())
                }
            }.recordResult(span)
        }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T : Any> getOrPut(
        vararg key: String,
        typeToken: TypeToken<T>,
        expirationTime: Long,
        useAbsent: Boolean,
        putCallback: suspend (missingKeys: List<String>?) -> T
    ): Result<T, Throwable> =
        span("getOrPut") { span ->
            catchResult {
                span.setAttribute("key", key)
                span.setAttribute("key_with_prefix", keyWithPrefix(key))
                span.setAttribute("expiration_time", expirationTime)
                span.setAttribute("data_type", typeToken.type.typeName)

                if (key.size == 1) {
                    get(*key, typeToken = typeToken).coFoldException(KeyDoesNotExistException::class) {
                        putSingleData(key.first(), putCallback(key.toList()), expirationTime, useAbsent)
                    }
                } else {
                    get(*key, typeToken = typeToken).flatMap { list ->
                        val dataList = list as Collection<*>

                        key.getMissing(dataList)
                            .takeIf { it.isNotEmpty() }
                            ?.let { missingKeys ->
                               putMultiData(
                                    *missingKeys.toTypedArray(),
                                    data = putCallback(missingKeys),
                                    expirationTime = expirationTime,
                                    useAbsent = useAbsent
                                ).map { ((it as Collection<*>) + dataList.filterNotNull()) as T }
                            } ?: list.success()
                    }
                }

            }.recordResult(span)
        }

    override suspend fun delete(key: String): Result<Boolean, Throwable> =
        span("delete") { span ->
            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix(key))

            resultOf<Boolean, Throwable> {
                jedisPool.resource.use { jedis ->
                    when (jedis.del(keyWithPrefix(key))) {
                        0L -> throw KeyDoesNotExistException(key)
                        else -> true
                    }
                }
            }.recordResult(span)
        }

    override suspend fun deleteByPattern(pattern: String): Result<List<String>, Throwable> =
        span("deleteByPattern") { span ->
            span.setAttribute("key", pattern)
            span.setAttribute("key_with_prefix", keyWithPrefix(pattern))

            resultOf<List<String>, Throwable> {
                jedisPool.resource.use { jedis ->
                    jedis.keys(keyWithPrefix(pattern)).let { keys ->
                        if (keys.isNotEmpty()) jedis.del(*keys.toTypedArray())
                        keys.map { it.replace(prefixPattern, "") }.toList()
                    }
                }
            }.recordResult(span)
        }

    private suspend fun <T : Any> putSingleData(
        key: String,
        data: T,
        expirationTime: Long,
        useAbsent: Boolean = false
    ): Result<T, Throwable> =
        span("put") { span ->
            val keyWithPrefix = keyWithPrefix(key)

            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix)
            span.setAttribute("expiration_time", expirationTime)

            val params = SetParams().ex(expirationTime).let {
                if (useAbsent) it.nx()
                else it
            }

            resultOf<T, Throwable> {
                jedisPool.resource.use { jedis ->
                    jedis.set(keyWithPrefix, gson.toJson(data), params)?.let { data }
                        ?: throw if (useAbsent) KeyAlreadyExistsException(key)
                        else ErrorToPersistDataException(key)
                }
            }.recordResult(span)
        }

    private suspend fun <T : Any> putMultiData(
        vararg key: String,
        data: T,
        expirationTime: Long,
        useAbsent: Boolean = false
    ): Result<T, Throwable> =
        span("put") { span ->
            val keyWithPrefix = keyWithPrefix(key)

            span.setAttribute("key", key)
            span.setAttribute("key_with_prefix", keyWithPrefix)
            span.setAttribute("expiration_time", expirationTime)

            val finalData = (data as Collection<*>).mapIndexed { index, indexData ->
                listOf(keyWithPrefix[index], gson.toJson(indexData))
            }.flatten()

            resultOf<T, Throwable> {
                jedisPool.resource.use { jedis ->
                    jedis.pipelined().let { pipeline ->
                        if (useAbsent) pipeline.msetnx(*finalData.toTypedArray())
                        else pipeline.mset(*finalData.toTypedArray())

                        keyWithPrefix.forEach { pipeline.expire(it, expirationTime) }
                        pipeline.sync()
                        data
                    }
                }
            }.recordResult(span)
        }

    private fun keyWithPrefix(key: String): String = "$prefixPattern$key"
    private fun keyWithPrefix(keys: Array<out String>): Array<String> = keys.map { keyWithPrefix(it) }.toTypedArray()

    private fun Array<out String>.getMissing(data: Collection<*>) =
        data.mapIndexed { index, element ->  if (element == null) this[index] else null }.filterNotNull()
}
