package br.com.alice.akinator.client.transfer

import br.com.alice.common.convertTo
import br.com.alice.data.layer.models.AIInferenceResult
import br.com.alice.data.layer.models.AITextInference
import br.com.alice.data.layer.models.AITextInferenceOutput
import br.com.alice.data.layer.models.AITextInferenceReferenceModel
import com.google.gson.JsonObject
import java.util.UUID

data class AIInferenceRequestDTO<T> (
    val context: T,
    val caller: String,
    val personId: UUID? = null,
    val referenceModel: AITextInferenceReferenceModel? = null
)

typealias AIInferenceRequestTextDTO = AIInferenceRequestDTO<String>
typealias AIInferenceRequestInternalDTO = AIInferenceRequestDTO<JsonObject>

data class AIInferenceResponseDTO (
    val id: UUID,
    val caller: String? = null,
    val result: AIInferenceResult,
    val outputs: List<AITextInferenceOutput>
)

fun AITextInference.toDTO() = this.convertTo(AIInferenceResponseDTO::class)
