package br.com.alice.eita.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.eita.nullvs.SERVICE_NAME
import br.com.alice.eita.nullvs.models.guia.TotvsGuiaResponse

class NullvsGuiaUpsertedResponseEvent(
    guiaResponse: TotvsGuiaResponse
) : NotificationEvent<NullvsGuiaUpsertedResponseEvent.Payload>(
    producer = SERVICE_NAME,
    name = name,
    payload = Payload(guiaResponse),
) {
    companion object {
        const val name = "nullvs-guia-upserted-response"
    }

    data class Payload(
        val guiaResponse: TotvsGuiaResponse,
    )
}
