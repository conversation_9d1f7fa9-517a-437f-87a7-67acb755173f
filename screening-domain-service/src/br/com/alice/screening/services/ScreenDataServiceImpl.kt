package br.com.alice.screening.services

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionType
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.MessageType
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelCreationParameters
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelScreeningNavigation
import br.com.alice.data.layer.models.ChannelScreeningNavigationStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ScreeningNavigation
import br.com.alice.data.layer.models.ScreeningNavigationStatus
import br.com.alice.healthlogic.client.ScreeningNavigationService
import br.com.alice.screening.client.ScreenDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class ScreenDataServiceImpl(
    private val screeningNavigationService: ScreeningNavigationService,
    private val channelService: ChannelService
) : ScreenDataService {

    override suspend fun finishScreening(
        screeningNavigationId: UUID,
        shouldOpenChat: Boolean,
        channelCreationParameters: ChannelCreationParameters?,
        userAgent: String?,
        appVersion: String,
        question: String?,
        answer: String?,
    ): Result<RemoteAction, Throwable> =
        screeningNavigationService.get(screeningNavigationId).flatMap {

            val status = if (shouldOpenChat) {
                if (channelCreationParameters == null) ScreeningNavigationStatus.DROPPED else ScreeningNavigationStatus.FINISHED
            } else ScreeningNavigationStatus.CLOSED

            screeningNavigationService.update(it.copy(status = status))
        }.flatMap { screeningNavigation ->
            if (shouldOpenChat) {
                createChat(
                    screeningNavigation,
                    channelCreationParameters = channelCreationParameters,
                    userAgent,
                    appVersion,
                ).map {
                    RemoteAction(
                        mobileRoute = ActionRouting.CHANNEL,
                        params = mapOf(
                            "channel_id" to it.id!!,
                            "pop_before_navigate" to true,
                        )
                    )
                }
            } else {
                RemoteAction(
                    type = RemoteActionType.CLOSE,
                    params = mapOf(
                        "root" to true,
                        "pop_to_root" to true,
                    )
                ).success()
            }
        }

    private suspend fun createChat(
        screeningNavigation: ScreeningNavigation,
        channelCreationParameters: ChannelCreationParameters? = null,
        userAgent: String?,
        appVersion: String,
    ): Result<ChannelDocument, Throwable> =
        getChatParams(parameters = channelCreationParameters).let { chatParamsWithValidations ->
            val createChatRequest = CreateChatRequest(
                personId = screeningNavigation.personId,
                allowOnCallFlow = true,
                origin = "newChannel",
                message = chatParamsWithValidations.content?.let { content ->
                    ChatMessageRequest(
                        content = content,
                        type = MessageType.TEXT,
                        appVersion = userAgent
                    )
                },
                category = chatParamsWithValidations.category,
                subCategory = chatParamsWithValidations.subCategory,
                subCategoryClassifier = chatParamsWithValidations.subCategoryClassifier,
                hideMemberInput = chatParamsWithValidations.hideMemberInput,
                tags = chatParamsWithValidations.tags,
                screeningNavigation = ChannelScreeningNavigation(
                    screeningNavigation.id,
                    hasProtocol = false,
                    status = getChannelStatus(screeningNavigation)
                ),
                appVersion = appVersion
            )
            channelService.addChatV2(request = createChatRequest)
        }

    private fun getChatParams(parameters: ChannelCreationParameters? = null): ChannelCreationParameters =
        parameters?.let {
            swapSubCategoryAnswerParameterToScreening(params = it)
        } ?: ChannelCreationParameters(
            content = "Preciso de ajuda nesse momento",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.SCREENING,
            tags = listOf("agudo_imediato")
        )

    private fun swapSubCategoryAnswerParameterToScreening(params: ChannelCreationParameters) =
        when {
            params.subCategory != null -> {
                params.copy(subCategory = ChannelSubCategory.SCREENING)
            }
            else -> params
        }

    private fun getChannelStatus(screeningNavigation: ScreeningNavigation) =
        when (screeningNavigation.status) {
            ScreeningNavigationStatus.FINISHED -> ChannelScreeningNavigationStatus.COMPLETED
            ScreeningNavigationStatus.DROPPED -> ChannelScreeningNavigationStatus.DROPPED
            ScreeningNavigationStatus.CLOSED -> ChannelScreeningNavigationStatus.CLOSED
            else -> ChannelScreeningNavigationStatus.INCOMPLETED
        }

}
