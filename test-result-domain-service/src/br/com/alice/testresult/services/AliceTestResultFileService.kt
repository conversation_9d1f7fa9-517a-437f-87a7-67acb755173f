package br.com.alice.testresult.services

import br.com.alice.data.layer.models.AliceTestResultFile
import com.github.kittinunf.result.Result
import java.util.UUID

interface AliceTestResultFileService {
    suspend fun get(id: UUID): Result<AliceTestResultFile, Throwable>
    suspend fun add(model: AliceTestResultFile): Result<AliceTestResultFile, Throwable>
    suspend fun update(model: AliceTestResultFile): Result<AliceTestResultFile, Throwable>
    suspend fun findByReference(referenceModelId: UUID): Result<AliceTestResultFile, Throwable>
}
