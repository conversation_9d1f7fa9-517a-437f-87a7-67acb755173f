package br.com.alice.testresult.consumers.exam.file.db

import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.dbintegrationclient.client.DbFileService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.testresult.consumers.Consumer
import br.com.alice.testresult.events.AliceTestResultUpsertedEvent
import br.com.alice.testresult.events.AliceTestResultWithUrlFileEvent
import br.com.alice.testresult.models.ProcessExamFile
import br.com.alice.testresult.services.AliceTestResultFileService
import br.com.alice.testresult.services.ExamFileService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class AliceTestResultDbConsumer(
    private val dbFileService: DbFileService,
    private val kafkaProducer: KafkaProducerService,
    private val examFileService: ExamFileService,
    private val aliceTestResultFileService: AliceTestResultFileService,
) : Consumer() {
    suspend fun handlerDbAliceResultGetUrlConsumer(event: AliceTestResultUpsertedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment(event.payload.personId.toString()) {
            if (!FeatureService.get(FeatureNamespace.INTEROP, "should_db_file_download", false))
                return@withSubscribersEnvironment false.success()
            loggerEvent(event)
            val payload = event.payload
            val exam = payload.exam

            if (payload.provider != ProviderIntegration.DB)
                return@withSubscribersEnvironment true.success()
            if (aliceTestResultFileService.findByReference(exam.id).getOrNullIfNotFound() != null)
                return@withSubscribersEnvironment true.success()

            dbFileService.getUrl(payload.externalId, listOf(exam.externalReference!!)).map { url ->
                kafkaProducer.produce(
                    AliceTestResultWithUrlFileEvent(
                        exam = exam,
                        provider = payload.provider,
                        externalId = payload.externalId,
                        urlFile = url,
                        personId = payload.personId
                    ),
                    exam.externalReference
                )
            }
        }


    suspend fun handlerDbAliceResultGetFileConsumer(event: AliceTestResultWithUrlFileEvent): Result<Any, Throwable> =
        withSubscribersEnvironment(event.payload.personId.toString()) {
            if (!FeatureService.get(FeatureNamespace.INTEROP, "should_db_file_download", false))
                return@withSubscribersEnvironment false.success()
            loggerEvent(event)
            val payload = event.payload
            val url = payload.urlFile
            val exam = payload.exam

            if (payload.provider != ProviderIntegration.DB)
                return@withSubscribersEnvironment true.success()
            if (aliceTestResultFileService.findByReference(exam.id).getOrNullIfNotFound() != null)
                return@withSubscribersEnvironment true.success()

            val byteArray = dbFileService.getFile(url).get()
            val processExamFile = ProcessExamFile.build(
                personId = payload.personId,
                referenceId = exam.id,
                externalReferenceId = exam.externalReference,
                referenceClassName = exam::class,
                fileContent = byteArray
            )

            logger.info(
                "Preparing to upsert ProcessExamFile",
                "person_id" to payload.personId,
                "reference_id" to exam.id,
                "class_name" to processExamFile.referenceClassName
            )
            examFileService.upsert(processExamFile)
        }

    private fun loggerEvent(event: AliceTestResultWithUrlFileEvent) {
        val payload = event.payload
        logger.info(
            "Consuming AliceTestResultWithUrlFileEvent",
            "message_id" to event.messageId,
            "external_id" to payload.externalId,
            "provider" to payload.provider,
            "external_reference" to payload.exam.externalReference,
            "person_id" to payload.personId
        )
    }

    private fun loggerEvent(event: AliceTestResultUpsertedEvent) {
        val payload = event.payload
        logger.info(
            "Consuming AliceTestResultUpsertedEvent",
            "message_id" to event.messageId,
            "external_id" to payload.externalId,
            "provider" to payload.provider,
            "external_reference" to payload.exam.externalReference,
            "person_id" to payload.personId
        )
    }

}
