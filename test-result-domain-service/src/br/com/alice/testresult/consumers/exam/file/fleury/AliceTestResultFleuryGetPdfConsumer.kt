package br.com.alice.testresult.consumers.exam.file.fleury

import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.fleuryintegrationclient.client.FleuryFileService
import br.com.alice.fleuryintegrationclient.exceptions.FleuryPdfItemIsEmptyException
import br.com.alice.testresult.consumers.Consumer
import br.com.alice.testresult.events.AliceTestResultUpsertedEvent
import br.com.alice.testresult.models.ProcessExamFile
import br.com.alice.testresult.services.AliceTestResultFileService
import br.com.alice.testresult.services.ExamFileService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class AliceTestResultFleuryGetPdfConsumer(
    private val fleuryFileService: FleuryFileService,
    private val examFileService: ExamFileService,
    private val aliceTestResultFileService: AliceTestResultFileService,
) : Consumer() {
    suspend fun handlerFleuryAliceResultGetPdfConsumer(event: AliceTestResultUpsertedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment(event.payload.personId.toString()) {
            if (!FeatureService.get(FeatureNamespace.INTEROP, "should_fleury_file_download", false))
                return@withSubscribersEnvironment false.success()
            loggerEvent(event)
            val payload = event.payload
            val exam = payload.exam

            if (payload.provider != ProviderIntegration.FLEURY)
                return@withSubscribersEnvironment true.success()
            if (aliceTestResultFileService.findByReference(exam.id).getOrNullIfNotFound() != null)
                return@withSubscribersEnvironment true.success()

            fleuryFileService.getToken()
                .flatMap { token ->
                    fleuryFileService.getFileByFichaItem(
                        token = token,
                        ficha = payload.externalId,
                        item = exam.externalReference!!
                    )
                }.map { file ->
                    ProcessExamFile.build(
                        personId = payload.personId,
                        referenceId = exam.id,
                        externalReferenceId = exam.externalReference,
                        referenceClassName = exam::class,
                        fileContent = file
                    )
                }.then {
                    logger.info(
                        "Preparing to upsert ProcessExamFile",
                        "person_id" to payload.personId,
                        "reference_id" to exam.id,
                        "class_name" to it.referenceClassName
                    )
                }.flatMap { processExamFile ->
                    examFileService.upsert(processExamFile)
                }.then {
                    logger.info(
                        "successfully for process file",
                        "message_id" to event.messageId,
                        "file_id" to it.id,
                        "person_id" to payload.personId,
                        "reference_id" to exam.id,
                        "provider" to payload.provider,
                    )
                }.foldError(FleuryPdfItemIsEmptyException::class to {
                    false.success()
                }).thenError { ex ->
                    logger.error(
                        "error for process file",
                        "message_id" to event.messageId,
                        "person_id" to payload.personId,
                        "reference_id" to exam.id,
                        "provider" to payload.provider,
                        ex
                    )
                }

        }

    private fun loggerEvent(event: AliceTestResultUpsertedEvent) {
        val payload = event.payload
        logger.info(
            "Consuming AliceTestResultUpsertedEvent",
            "message_id" to event.messageId,
            "external_id" to payload.externalId,
            "provider" to payload.provider,
            "external_reference" to payload.exam.externalReference,
            "person_id" to payload.personId
        )
    }

}
