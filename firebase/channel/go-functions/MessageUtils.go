package go_functions

import (
	"cloud.google.com/go/firestore"
	"regexp"
)

type MessageFirestoreValue struct {
	Name   string  `json:"name"`
	Fields Message `json:"fields"`
}

type StringValue struct {
	StringValue string `json:"stringValue"`
}

type NullableStringValue struct {
	NullableStringValue *string `json:"stringValue"`
}

type Message struct {
	UserId          StringValue         `json:"userId"`
	Type            StringValue         `json:"type"`
	ChannelCategory NullableStringValue `json:"channelCategory"`
}

type MessageFirestoreEvent struct {
	Value	   MessageFirestoreValue `json:"value"`
	UpdateMask struct {
		FieldPaths []string `json:"fieldPaths"`
	} `json:"updateMask"`
}

func (firestoreEvent MessageFirestoreEvent) getChannelId() string {
	for _, pattern := range firestoreChannelIdPatterns {
		r := regexp.MustCompile(pattern)
		results := r.FindStringSubmatch(firestoreEvent.Value.Name)
		if len(results) > 1 {
			return results[1]
		}
	}
	return ""
}

func (firestoreEvent MessageFirestoreEvent) getMessageId() string {
	r := regexp.MustCompile(firestoreMessageIdPattern)
	result := r.FindStringSubmatch(firestoreEvent.Value.Name)
	return result[1]
}

var client *firestore.Client
