package br.com.alice.secondary.attention.ioc

import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.secondary.attention.SERVICE_NAME
import br.com.alice.secondary.attention.client.CounterReferralRelevanceService
import br.com.alice.secondary.attention.client.HealthPlanTaskReferralsService
import br.com.alice.secondary.attention.client.SpecialistOpinionMessageService
import br.com.alice.secondary.attention.client.SpecialistOpinionService
import br.com.alice.secondary.attention.services.CounterReferralRelevanceServiceImpl
import br.com.alice.secondary.attention.services.HealthPlanTaskReferralsServiceImpl
import br.com.alice.secondary.attention.services.SpecialistOpinionMessageServiceImpl
import br.com.alice.secondary.attention.services.SpecialistOpinionServiceImpl
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    // Configuration
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }

    // Servers
    loadServiceServers("br.com.alice.secondary.attention.services")

    // Controllers
    single { HealthController(SERVICE_NAME) }

    // Services
    single<CounterReferralRelevanceService> { CounterReferralRelevanceServiceImpl(get()) }
    single<SpecialistOpinionMessageService> { SpecialistOpinionMessageServiceImpl(get(), get()) }
    single<SpecialistOpinionService> { SpecialistOpinionServiceImpl(get(), get(), get(), get()) }
    single<HealthPlanTaskReferralsService> { HealthPlanTaskReferralsServiceImpl(get(), get()) }
}
