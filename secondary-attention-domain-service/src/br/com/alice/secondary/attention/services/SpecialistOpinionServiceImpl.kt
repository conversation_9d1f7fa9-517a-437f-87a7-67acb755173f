package br.com.alice.secondary.attention.services

import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.SpecialistOpinion
import br.com.alice.data.layer.models.SpecialistOpinionMessage
import br.com.alice.data.layer.models.SpecialistOpinionStatus
import br.com.alice.data.layer.services.SpecialistOpinionDataService
import br.com.alice.data.layer.services.SpecialistOpinionMessageDataService
import br.com.alice.secondary.attention.client.SpecialistOpinionService
import br.com.alice.secondary.attention.events.SpecialistOpinionAssignedEvent
import br.com.alice.secondary.attention.events.SpecialistOpinionCreatedEvent
import br.com.alice.secondary.attention.events.SpecialistOpinionEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class SpecialistOpinionServiceImpl(
    private val data: SpecialistOpinionDataService,
    private val specialistOpinionMessageDataService: SpecialistOpinionMessageDataService,
    private val healthProfessionalService: HealthProfessionalService,
    private val kafkaProducerService: KafkaProducerService
) : SpecialistOpinionService {

    override suspend fun get(id: UUID): Result<SpecialistOpinion, Throwable> =
        data.get(id)

    override suspend fun getAll(): Result<List<SpecialistOpinion>, Throwable> =
        data.find { orderBy { createdAt }.sortOrder { desc } }

    override suspend fun create(model: SpecialistOpinion): Result<SpecialistOpinion, Throwable> =
        coroutineScope {
            data.add(model).then {
                logger.info(
                    "SpecialistOpinionService::create()",
                    "specialist_opinion_id" to it.id,
                    "staff_id" to it.staffId,
                )
                async { notifyCreated(it) }
            }
        }

    override suspend fun update(model: SpecialistOpinion): Result<SpecialistOpinion, Throwable> =
        coroutineScope {
            data.update(model).then {
                logger.info(
                    "SpecialistOpinionService::update()",
                    "specialist_opinion_id" to it.id,
                    "staff_id" to it.staffId,
                )

                async {
                    val messages = if (it.status == SpecialistOpinionStatus.RESPONDED)
                        getMessagesBySpecialistOpinionId(it.id).get()
                    else
                        emptyList()

                    notifyUpdated(it, messages)
                }
            }
        }

    override suspend fun updateList(
        models: List<SpecialistOpinion>,
        returnOnFailure: Boolean
    ): Result<List<SpecialistOpinion>, Throwable> =
        data.updateList(models)

    override suspend fun assignToSpecialist(id: UUID, staffId: UUID): Result<SpecialistOpinion, Throwable> =
        coroutineScope {
            data.get(id)
                .map { specialistOpinion ->
                    if (specialistOpinion.assignedStaffId != null || specialistOpinion.staffId == staffId) {
                        logger.error(
                            "Opinion has already assigned",
                            "opinion_request_staff_id" to specialistOpinion.staffId,
                            "assigned_staff_id" to specialistOpinion.assignedStaffId,
                            "staff_id" to staffId
                        )
                        throw UnsupportedOperationException()
                    } else specialistOpinion
                }.flatMap { specialistOpinion ->
                    healthProfessionalService.findByStaffId(staffId).map { healthProfessional ->
                        if (specialistOpinion.medicalSpecialtyId != healthProfessional.specialtyId) {
                            logger.error(
                                "Specialist has invalid specialtyId",
                                "required_specialty_id" to specialistOpinion.medicalSpecialtyId,
                                "specialist_specialty_id" to healthProfessional.specialtyId
                            )
                            throw IllegalAccessException()
                        } else specialistOpinion
                    }
                }.flatMap { specialistOpinion ->
                    update(specialistOpinion.assignToSpecialist(staffId))
                        .then { async { notifyAssigned(it) } }
                }
        }

    override suspend fun removeAssignee(specialistOpinionId: UUID): Result<SpecialistOpinion, Throwable> =
        data.get(specialistOpinionId)
            .flatMap {
                logger.info(
                    "Removing assignee from specialistOpinion",
                    "specialistOpinionId" to specialistOpinionId
                )
                data.update(it.removeAssignee())
            }

    override suspend fun findByIds(ids: List<UUID>): Result<List<SpecialistOpinion>, Throwable> =
        data.find {
            where {
                this.id.inList(ids)
            }
        }

    override suspend fun findBySpecialtyAndStatusWithRangeAndSort(
        specialty: UUID,
        status: List<SpecialistOpinionStatus>,
        range: IntRange,
        sort: Pair<String, String>?
    ): Result<List<SpecialistOpinion>, Throwable> =
        data.find {
            where { this.status.inList(status) and this.medicalSpecialtyId.eq(specialty) }
                .orderBy { createdAt }
                .sortOrder { sortOrder(sort) }
                .offset { range.first }
                .limit { range.count() }
        }

    override suspend fun countBySpecialtyAndStatus(
        specialty: UUID,
        status: List<SpecialistOpinionStatus>
    ): Result<Int, Throwable> =
        data.count {
            where { this.status.inList(status) and this.medicalSpecialtyId.eq(specialty) }
        }

    override suspend fun findByAssignedStaffAndStatusAndRange(
        staffId: UUID,
        status: List<SpecialistOpinionStatus>,
        range: IntRange,
        sort: Pair<String, String>?
    ): Result<List<SpecialistOpinion>, Throwable> =
        data.find {
            where { this.status.inList(status) and this.assignedStaffId.eq(staffId) }
                .orderBy { createdAt }
                .sortOrder { sortOrder(sort) }
                .offset { range.first }
                .limit { range.count() }
        }

    private fun sortOrder(sort: Pair<String, String>?) =
        if (sort?.second == "ASC") SortOrder.Ascending
        else SortOrder.Descending

    private suspend fun getMessagesBySpecialistOpinionId(opinionId: UUID) =
        specialistOpinionMessageDataService.find {
            where {
                this.specialistOpinionId.eq(opinionId)
            }
        }

    private suspend fun notifyCreated(
        specialistOpinion: SpecialistOpinion
    ) = kafkaProducerService.produce(
        SpecialistOpinionCreatedEvent(specialistOpinion)
    )

    private suspend fun notifyAssigned(
        specialistOpinion: SpecialistOpinion
    ) = kafkaProducerService.produce(
        SpecialistOpinionAssignedEvent(specialistOpinion)
    )

    private suspend fun notifyUpdated(
        specialistOpinion: SpecialistOpinion,
        specialistOpinionMessages: List<SpecialistOpinionMessage> = emptyList()
    ) = kafkaProducerService.produce(
        SpecialistOpinionEvent(
            specialistOpinion = specialistOpinion,
            specialistOpinionMessages = specialistOpinionMessages
        )
    )
}
