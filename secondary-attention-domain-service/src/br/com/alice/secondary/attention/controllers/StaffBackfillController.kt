package br.com.alice.secondary.attention.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.services.CounterReferralDataService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.secondary.attention.events.HealthPlanTaskReferralsHandleEvent
import br.com.alice.secondary.attention.events.TaskReferralsEventHandleType
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.map
import java.util.UUID

class HealthPlanTaskReferralsController(
    private val counterReferralService: CounterReferralDataService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val kafkaProducerService: KafkaProducerService
) : Controller() {
    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME) {
                func.invoke()
            }
        }

    suspend fun process(request: CRRequest) =
        withBackfillEnvironment {
            val result = counterReferralService.find {
                where {
                    id.inList(request.crIds)
                }
            }.flatMapPair { crs ->
                val taskIds = crs.mapNotNull { it.referralId }
                healthPlanTaskService.getByIds(taskIds)
            }.map { (tasks, crs) ->
                crs.mapNotNull {
                    val task = tasks.find { task -> task.id == it.referralId }
                    if (task != null) {
                        CrWithTask(it, task)
                    } else {
                        null
                    }
                }
            }.pmapEach {
                coResultOf<ProducerResult, Throwable> {
                    val payload = HealthPlanTaskReferralsHandleEvent(
                        TaskReferralsEventHandleType.INCREMENT_COUNTER_REFERRAL,
                        it.task,
                        it.cr
                    )
                    kafkaProducerService.produce(payload)
                }
            }.get()

            BackfillResponse(
                quantitySuccess = result.count { it.isSuccess() },
                quantityFailure = result.count { it.isFailure() }
            ).toResponse()
        }

    data class CRRequest(
        val crIds: List<UUID>
    )

    data class BackfillResponse(
        val quantitySuccess: Int,
        val quantityFailure: Int,
    )

    data class CrWithTask(
        val cr: CounterReferral,
        val task: HealthPlanTask
    )
}
