package br.com.alice.bud.client

import br.com.alice.bud.model.BudNode
import br.com.alice.bud.model.BudNodeFilterRequest
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType
import br.com.alice.data.layer.models.ServiceScriptRelationship
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface BudService : Service {
    override val namespace get() = "bud"
    override val serviceName get() = "bud"

    suspend fun getNodeById(id: UUID): Result<BudNode, Throwable>

    suspend fun getCategories(): Result<List<BudNode>, Throwable>

    suspend fun getNodeByTypeAndHealthCondition(
        nodeType: BudNode.BudNodeType,
        actionType: ScriptActionType,
        healthConditionId: UUID,
        loadFallback: Boolean = false
    ) : Result<BudNode, Throwable>

    suspend fun findNodesByFilter(filter: BudNodeFilterRequest): Result<List<BudNode>, Throwable>

    suspend fun createNode(node: BudNode): Result<BudNode, Throwable>

    suspend fun updateNode(node: BudNode): Result<BudNode, Throwable>

    suspend fun createRelationship(relationship: ServiceScriptRelationship): Result<ServiceScriptRelationship, Throwable>

    suspend fun updateRelationship(relationship: ServiceScriptRelationship): Result<ServiceScriptRelationship, Throwable>

    suspend fun countGroupedByRelationshipParentIds(nodeParentIds: List<UUID>): Result<List<CountByValues>, Throwable>

    suspend fun getRelationshipsByParentIds(nodeParentIds: List<UUID>): Result<List<ServiceScriptRelationship>, Throwable>

    suspend fun getRelationshipsByChildIds(nodeChildIds: List<UUID>): Result<List<ServiceScriptRelationship>, Throwable>

    suspend fun getRelationshipById(id: UUID): Result<ServiceScriptRelationship, Throwable>

    suspend fun createAction(model: ServiceScriptAction): Result<ServiceScriptAction, Throwable>

    suspend fun getOrCreateAction(type: ActionType, externalId: UUID): Result<ServiceScriptAction, Throwable>

    suspend fun updateAction(model: ServiceScriptAction): Result<ServiceScriptAction, Throwable>

    suspend fun getAction(id: UUID): Result<ServiceScriptAction, Throwable>

    suspend fun deleteAction(id: UUID): Result<Boolean, Throwable>

    suspend fun getActionsByIds(ids: List<UUID>): Result<List<ServiceScriptAction>, Throwable>
}
