package br.com.alice.featureconfig

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object FeatureConfigDomainClientConfiguration: BaseConfig(
    config = HoconApplicationConfig(ConfigFactory.load("feature_config_client.conf"))
) {

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun baseUrl() = config.property("${environment().value.lowercase()}.baseUrl").getString()

}
