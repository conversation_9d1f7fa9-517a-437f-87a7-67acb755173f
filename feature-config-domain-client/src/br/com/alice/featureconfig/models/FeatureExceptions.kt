package br.com.alice.featureconfig.models

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.data.layer.models.FeatureType

class InvalidFeatureTypeException(
    message: String,
    code: String = "invalid_feature_type",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(type: FeatureType, requiredType: String?) : this(
        message = "Feature config type ($type) is different from required class ($requiredType)"
    )
}

class InvalidDistributionBucketsException(
    message: String,
    code: String = "invalid_distribution_buckets",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(sum: String) : this(
        message = "Distribution buckets must sum to 1.0, not $sum"
    )
}
