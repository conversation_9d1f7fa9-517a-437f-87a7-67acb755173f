package br.com.alice.fleuryintegrationservice.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory.buildFleuryTestResult
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.FleuryTestResult
import br.com.alice.data.layer.services.AliceTestResultBundleDataService
import br.com.alice.data.layer.services.FleuryTestResultDataService
import br.com.alice.testresult.helpers.TestModelFactory.buildAliceTestResultBundle
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class FleuryFichaServiceTest {
    private val aliceTestResultBundleDataService: AliceTestResultBundleDataService = mockk()
    private val fleuryTestResultDataService: FleuryTestResultDataService = mockk()
    private val service = FleuryFichaService(
        fleuryTestResultDataService, aliceTestResultBundleDataService
    )
    private val idFicha = "123456"
    private val bundle = buildAliceTestResultBundle(externalId = idFicha)
    private val fleuryTestResult = buildFleuryTestResult(idFicha = idFicha)

    @Test
    fun `should return true when successful to delete ficha`() = runBlocking {
        coEvery { aliceTestResultBundleDataService.find(any()) } returns listOf(bundle).success()
        coEvery { fleuryTestResultDataService.find(any()) } returns listOf(fleuryTestResult).success()
        coEvery { aliceTestResultBundleDataService.delete(any()) } returns true.success()
        coEvery { fleuryTestResultDataService.delete(any()) } returns true.success()


        val response = service.removeFicha(idFicha)
        assertThat(response).isSuccessWithData(true)

        coVerify {
            aliceTestResultBundleDataService.find(queryEq {
                where { externalId.eq(idFicha) }
            })
        }
        coVerify {
            fleuryTestResultDataService.find(queryEq {
                where { ficha.eq(idFicha) }
            })
        }
        coVerify {
            fleuryTestResultDataService.delete(match { it.idFicha == idFicha })
        }
        coVerify {
            aliceTestResultBundleDataService.delete(match { it.externalId == idFicha })
        }
    }

    @Test
    fun `should return false when is not removed ficha`() = runBlocking {
        coEvery { aliceTestResultBundleDataService.find(any()) } returns listOf(bundle).success()
        coEvery { fleuryTestResultDataService.find(any()) } returns listOf(fleuryTestResult).success()
        coEvery { aliceTestResultBundleDataService.delete(any()) } returns true.success()
        coEvery { fleuryTestResultDataService.delete(any()) } returns false.success()

        val response = service.removeFicha(idFicha)
        assertThat(response).isSuccessWithData(false)

        coVerify {
            aliceTestResultBundleDataService.find(queryEq {
                where { externalId.eq(idFicha) }
            })
        }
        coVerify {
            fleuryTestResultDataService.find(queryEq {
                where { ficha.eq(idFicha) }
            })
        }
        coVerify {
            fleuryTestResultDataService.delete(match { it.idFicha == idFicha })
        }
        coVerify {
            aliceTestResultBundleDataService.delete(match { it.externalId == idFicha })
        }
    }

    @Test
    fun `should return false when is not contains items`() = runBlocking {
        coEvery { aliceTestResultBundleDataService.find(any()) } returns emptyList<AliceTestResultBundle>().success()
        coEvery { fleuryTestResultDataService.find(any()) } returns emptyList<FleuryTestResult>().success()

        val response = service.removeFicha(idFicha)
        assertThat(response).isSuccessWithData(false)

        coVerify {
            aliceTestResultBundleDataService.find(queryEq {
                where { externalId.eq(idFicha) }
            })
        }
        coVerify {
            fleuryTestResultDataService.find(queryEq {
                where { ficha.eq(idFicha) }
            })
        }
    }
}

