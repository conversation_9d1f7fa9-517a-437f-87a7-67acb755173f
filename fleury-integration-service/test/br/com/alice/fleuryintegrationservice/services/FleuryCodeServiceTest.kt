package br.com.alice.fleuryintegrationservice.services

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class FleuryCodeServiceTest {

    @Test
    fun `#nameOfCode returns corresponding code name`() {
        val result = FleuryCodeService.nameOfCode("GTT2")

        assertThat(result).isEqualTo("Curva Glicêmica, de 2 horas, plasma")
    }


    @Test
    fun `#nameOfCode returns code if code not found on file`() {
        val result = FleuryCodeService.nameOfCode("AnyCode")

        assertThat(result).isEqualTo("AnyCode")
    }
}
