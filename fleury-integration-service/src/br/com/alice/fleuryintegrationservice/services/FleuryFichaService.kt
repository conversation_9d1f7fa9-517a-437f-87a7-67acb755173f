package br.com.alice.fleuryintegrationservice.services

import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.services.AliceTestResultBundleDataService
import br.com.alice.data.layer.services.FleuryTestResultDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class FleuryFichaService(
    private val fleuryTestResultDataService: FleuryTestResultDataService,
    private val aliceTestResultBundle: AliceTestResultBundleDataService,
) {

    suspend fun removeFicha(ficha: String): Result<Boolean, Throwable> = coroutineScope {
        val deleteFleuryDeferred = async { deleteResultByFicha(ficha) }
        val deleteBundleDeferred = async { deleteBundleByFicha(ficha) }
        val results = (deleteFleuryDeferred.await().get() + deleteBundleDeferred.await().get())
        return@coroutineScope isAllItemsRemoved(results).success()
    }


    private suspend fun deleteResultByFicha(ficha: String) =
        getResultsByFicha(ficha).flatMapEach { result ->
            logger.info(
                "removing result by ficha",
                "id" to result.id,
                "ficha" to result.idFicha,
                "item" to result.idItem
            )

            fleuryTestResultDataService.delete(result).then {
                logger.info(
                    "success to execute the request", "response" to it
                )
            }.thenError {
                logger.error(
                    "error to run delete bundle", "id" to result.id, "ficha" to result.idFicha, it
                )
            }
        }

    private suspend fun deleteBundleByFicha(ficha: String) =
        getBundleByFicha(ficha).flatMapEach { bundle ->
            logger.info("removing result by ficha")
            aliceTestResultBundle.delete(bundle).then {
                logger.info(
                    "success to execute the delete", "response" to it
                )
            }.thenError {
                logger.error(
                    "error to run delete bundle",
                    "id" to bundle.id,
                    "ficha" to bundle.externalId,
                    it
                )
            }
        }

    private suspend fun getResultsByFicha(idFicha: String) =
        fleuryTestResultDataService.find { where { ficha.eq(idFicha) } }

    private suspend fun getBundleByFicha(ficha: String) =
        aliceTestResultBundle.find { where { this.externalId.eq(ficha) } }

    private fun isAllItemsRemoved(results: List<Boolean>): Boolean {
        return results.isNotEmpty() && !results.contains(false)
    }

}
