package br.com.alice.fleuryintegrationservice.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.fleuryintegrationservice.controller.FichaController
import br.com.alice.fleuryintegrationservice.controller.ReprocessController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    val reprocessController by inject<ReprocessController>()
    val fichaController by inject<FichaController>()
    route("/fleury/reprocess") {
        post("/") {
            coHand<PERSON>(reprocessController::reprocess)
        }
    }
    route("ficha") {
        delete("/{personId}/{ficha}") {
            coHandler("personId","ficha", fichaController::remove)
        }
    }
}
