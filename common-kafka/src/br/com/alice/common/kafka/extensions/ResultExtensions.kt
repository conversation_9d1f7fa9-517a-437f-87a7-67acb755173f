package br.com.alice.common.kafka.extensions

import br.com.alice.common.kafka.exceptions.AutoRetryableException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.mapError
import io.ktor.util.reflect.instanceOf
import kotlin.reflect.KClass

inline fun <V : Any, reified E : Throwable> Result<V, E>.mapAutoRetryable(kClass: KClass<out E>) =
    mapError {
        if (it.instanceOf(kClass)) AutoRetryableException(it) else it
    }
