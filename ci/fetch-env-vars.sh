#!/usr/bin/env bash
# This file will be eval'ed, so it is important to echo something like
# 'export VAR_NAME=VAR_VALUE' for every variable that you want to export.

set -euo pipefail

ENVIRONMENT=${1:-"prod"}
SERVICE=${2}

echo "export ENVIRONMENT=\"$ENVIRONMENT\""
echo "export SERVICE=\"$SERVICE\""

service_path="/common/"
aws ssm get-parameters-by-path --with-decryption \
    --path "$service_path" --output text --query 'Parameters[*].{Name:Name,Value:Value}' | \
    awk -v service_path="$service_path" -F $'\t' '{gsub(service_path,"",$1);print "export " $1 "=\"" $2 "\"; "}'

service_path="/$SERVICE/"
aws ssm get-parameters-by-path --with-decryption \
    --path "$service_path" --output text --query 'Parameters[*].{Name:Name,Value:Value}' | \
    awk -v service_path="$service_path" -F $'\t' '{gsub(service_path,"",$1);print "export " $1 "=\"" $2 "\"; "}'

service_path="/$ENVIRONMENT/services/$SERVICE/"
aws ssm get-parameters-by-path --with-decryption \
    --path "$service_path" --output text --query 'Parameters[*].{Name:Name,Value:Value}' | \
    awk -v service_path="$service_path" -F $'\t' '{gsub(service_path,"",$1);print "export " $1 "=\"" $2 "\"; " }'
