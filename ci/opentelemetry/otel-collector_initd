#!/bin/bash

# otel-collector -Startup script for the otelcontribcol router
#
# description: A lightweight and ultra-fast tool for building observability pipelines
# processname: otel-collector
# config:      /etc/otel-collector/config.yml
# pidfile:     /var/run/otel-collector.pid

### BEGIN INIT INFO
# Provides:          otel-collector
# Required-Start:    $network $local_fs
# Required-Stop:     $network $local_fs
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Short-Description: A lightweight and ultra-fast tool for building observability pipelines
# Description:       A lightweight and ultra-fast tool for building observability pipelines
### END INIT INFO

# This script is based off of https://bash.cyberciti.biz/guide//etc/init.d

# Source function library.
. /etc/rc.d/init.d/functions

# This will prevent initlog from swallowing up a pass-phrase prompt if
# mod_ssl needs a pass-phrase from the user.
INITLOG_ARGS=""

# Path to the apachectl script, server binary, and short-form for messages.
exec=/usr/bin/otelcontribcol
prog=otel-collector
pidfile=/var/run/$prog.pid
lockfile=/var/run/$prog.lock
config=/etc/$prog/config.yml
RETVAL=0
user=otel

# Start
start() {
  echo -n $"Starting $prog: "
  daemonize -p $pidfile -l $lockfile  -u $user $exec --config $config
  RETVAL=$?
  echo
  return $RETVAL
}

# Stop with a 10 second wait period
stop() {
  echo -n $"Stopping $prog: "
  # Handles removing the pidfile
  killproc -p ${pidfile} -d 10 $exec
  RETVAL=$?
  echo
  return $RETVAL
}

# Reload
reload() {
  echo -n $"Reloading $prog: "
  killproc -p ${pidfile} $otelcontribcol -HUP
  RETVAL=$?
  echo
  return $RETVAL
}

# See how we were called.
case "$1" in
  start)
    start
  ;;
  stop)
    stop
  ;;
  status)
    status -p ${pidfile} $otelcontribcol
    RETVAL=$?
  ;;
  restart)
    stop
    start
  ;;
  condrestart)
    if [ -f ${pidfile} ] ; then
      stop
      start
    fi
  ;;
  reload)
    reload
  ;;
  help)
    $otelcontribcol $@
    RETVAL=$?
  ;;
  *)
    echo $"Usage: $prog {start|stop|restart|condrestart|reload|status|help}"
    exit 1
esac

exit $RETVAL