package br.com.alice.nullvs.common

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeParseException
import kotlin.test.Test

class TotvsCommonsUtilityTest {

    @Test
    fun `fromTotvsFormatDateDMY should format correctly if format is ddMMyyyy`() {
        val date = " 11021997 "
        val result = date.fromTotvsFormatDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatDateDMY should format correctly if format is yyyy MM dd`() {
        val date = " 1997/02/11 "
        val result = date.fromTotvsFormatDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatDateDMY should format correctly if format is yyyyMMdd`() {
        val date = " 19970211 "
        val result = date.fromTotvsFormatDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatDateDMY should throw error if format is wrong`() {
        assertThatThrownBy {
            val date = "110-21-997"
            date.fromTotvsFormatDateDMY()
        }.isInstanceOf(DateTimeParseException::class.java)
    }

    @Test
    fun `fromTotvsFormatDateYMD should format correctly if format is yyyyMMdd`() {
        val date = " 19970211 "
        val result = date.fromTotvsFormatDateYMD()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatDateYMD should throw error if format is wrong`() {
        val date = "11021997"
        val result = date.fromTotvsFormatDateYMD()
        assertThat(result).isNull()
    }

    @Test
    fun `totvsFormatDateDMY should format correctly considering correct timezone`() {
        val localDateTime = LocalDateTime.of(1997,2,11,0,10)
        val x = localDateTime.totvsFormatDateDMY()
        assertThat(x).isEqualTo("10021997")
    }

    @Test
    fun `fromTotvsFormatOptionalDateDMY should format correctly if format is ddMMyyyy`() {
        val date = " 11021997 "
        val result = date.fromTotvsFormatOptionalDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatOptionalDateDMY should format correctly if format is yyyy MM dd`() {
        val date = " 1997/02/11 "
        val result = date.fromTotvsFormatOptionalDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatOptionalDateDMY should format correctly if format is yyyyMMdd`() {
        val date = " 19970211 "
        val result = date.fromTotvsFormatOptionalDateDMY()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatOptionalDateDMY should return null if format is wrong`() {
        val date = "110-21-997"
        val result = date.fromTotvsFormatOptionalDateDMY()
        assertThat(result).isNull()
    }

    @Test
    fun `fromTotvsFormatOptionalDateYMD should format correctly if format is yyyyMMdd`() {
        val date = " 19970211 "
        val result = date.fromTotvsFormatOptionalDateYMD()
        val expected = LocalDate.of(1997,2,11)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `fromTotvsFormatOptionalDateYMD should throw error if format is wrong`() {
        val date = "11021997"
        val result = date.fromTotvsFormatOptionalDateYMD()
        assertThat(result).isNull()
    }

}
