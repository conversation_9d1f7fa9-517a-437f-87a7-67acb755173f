plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.nullvs-integration-client"
version = aliceNullvsIntegrationClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:money-in-domain-service-data-package"))
    implementation(project(":data-packages:nullvs-integration-service-data-package"))
    implementation(project(":data-packages:amas-domain-service-data-package"))
    implementation(project(":data-packages:exec-indicator-domain-service-data-package"))

    kapt(project(":common"))
    ktor2Dependencies()
    

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
