package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME
import br.com.alice.nullvs.models.payment.NullvsPaymentWebhookReceived

class NullvsPaymentWebhookReceivedEvent(nullvsPaymentWebhookReceived: NullvsPaymentWebhookReceived):
    NotificationEvent<NullvsPaymentWebhookReceivedEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(nullvsPaymentWebhookReceived),
    ) {
    companion object {
        const val name = "nullvs-payment-webhook"
    }

    data class Payload(
        val webhook: NullvsPaymentWebhookReceived,
    )
}
