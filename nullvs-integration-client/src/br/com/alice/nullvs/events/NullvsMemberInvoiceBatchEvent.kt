package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.nullvs.SERVICE_NAME
import br.com.alice.nullvs.models.payment.NullvsMemberInvoiceDetailTransport

class NullvsMemberInvoiceBatchEvent(
    memberInvoiceGroup: MemberInvoiceGroup,
    memberInvoicesBatch: List<NullvsMemberInvoiceDetailTransport>,
) : NotificationEvent<NullvsMemberInvoiceBatchEvent.Payload>(
    producer = SERVICE_NAME,
    name = name,
    partitionKey = memberInvoiceGroup.id.toString(),
    payload = Payload(memberInvoiceGroup, memberInvoicesBatch),
) {
    companion object {
        const val name = "nullvs-member-invoice-batch"
    }

    data class Payload(
        val memberInvoiceGroup: MemberInvoiceGroup,
        val memberInvoicesBatch: List<NullvsMemberInvoiceDetailTransport>,
    )
}
