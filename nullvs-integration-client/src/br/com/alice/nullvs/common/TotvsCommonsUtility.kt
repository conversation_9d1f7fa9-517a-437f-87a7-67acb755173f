package br.com.alice.nullvs.common

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.company.ContractType
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Base64

private val totvsFormatterDMY = DateTimeFormatter.ofPattern("ddMMyyyy")

fun LocalDate.totvsFormatDateDMY(): String = this.format(totvsFormatterDMY)
fun LocalDateTime.totvsFormatDateDMY(): String = this.toSaoPauloTimeZone().format(totvsFormatterDMY)

fun Boolean.totvsFormatBoolean() = if (this) "1" else "0"

fun String.fromTotvsFormatBoolean() = this == "1"

fun String.fromTotvsFormatOptionalDateDMY() = try {
    fromTotvsFormatDateDMY()
} catch (err: DateTimeParseException) {
    null
}

fun String.fromTotvsFormatOptionalDateYMD() = try {
    fromTotvsFormatDateYMD()
} catch (err: DateTimeParseException) {
    null
}

fun String.fromTotvsFormatPaymentModel() = if (this == "1") PaymentModel.PRE_PAY else PaymentModel.POST_PAY

fun Month.totvsFormatMonth() = String.format("%02d", this.value)
fun String.fromTotvsToTotvsGroupCompany() = TotvsGroupCompany.values().firstOrNull { it.code == this }

fun ContractType.totvsFormat() = when (this) {
    ContractType.BUSINESS -> "3"
    ContractType.COLLECTIVE -> "2"
    ContractType.INDIVIDUAL -> "1"
}

const val TOTVS_DEFAULT_MOTHERS_NAME = "NOME NAO INFORMADO"
const val TOTVS_DEFAULT_PHONE_DDD = "11"
const val TOTVS_DEFAULT_PHONE_NUMBER = "11111111"

private val totvsFormatterYMD = DateTimeFormatter.ofPattern("yyyyMMdd")
private val totvsFormatterYMDWithSlashes = DateTimeFormatter.ofPattern("yyyy/MM/dd")
private val totvsFormatterYMDWithHyphen = DateTimeFormatter.ofPattern("yyyy-MM-dd")

fun String.fromTotvsFormatDateDMY() = try {
    this.trim().toLocalDate(totvsFormatterDMY)
} catch (ex: DateTimeParseException) {
    logger.warn("String.fromTotvsFormatDateDMY: handling DateTimeParseException", "date" to this, ex)
    this.trim().onlyDigits().toLocalDate(totvsFormatterYMD)
}

fun String?.fromTotvsFormatDateYMD(): LocalDate? {
    return if (!this.isNullOrBlank()) {
        try {
            this.trim().toLocalDate(totvsFormatterYMD)
        } catch (e: DateTimeParseException) {
            logger.warn("String.fromTotvsFormatDateYMD: handling DateTimeParseException", "date" to this, e)
            null
        }
    } else {
        null
    }
}

fun String?.fromTotvsFormatDateYMDWithSlashes() = try {
    this?.trim()?.toLocalDate(totvsFormatterYMDWithSlashes)
} catch (e: DateTimeParseException) {
    logger.warn(
        "String.fromTotvsFormatDateYMD: handling DateTimeParseException",
        "date" to this, "error" to e
    )
    null
}

fun LocalDate.toTotvsFormatDateYMDWithHyphen() = this.format(totvsFormatterYMDWithHyphen)

enum class NullvsActionType(val description: String) {
    CREATE("Criação"),
    UPDATE("Atualização"),
    CANCEL("Cancelamento"),
    UPDATE_PAYMENT("Baixa de Título"),
    REACTIVATION("Reativação");

    companion object {
        fun NullvsActionType.toTotvs() = when (this) {
            CREATE -> "insert"
            UPDATE -> "update"
            CANCEL -> "cancel"
            UPDATE_PAYMENT -> "update"
            REACTIVATION -> "reactivation"
        }

        fun String.toNullvsActionType() = when (this) {
            "insert" -> CREATE
            "update" -> UPDATE
            "cancel" -> CANCEL
            "reactivation" -> REACTIVATION
            else -> throw NotFoundException()
        }
    }
}

fun encodeBase64(data: String): String {
    return Base64.getEncoder().encodeToString(data.toByteArray())
}
