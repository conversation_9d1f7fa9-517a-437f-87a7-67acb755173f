package br.com.alice.nullvs.models.company

import br.com.alice.data.layer.models.BatchType
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsStatus
import java.util.UUID

data class NullvsCompanyContractWebhookReceived(
    val batchId: String,
    val idSoc: String,
    val status: TotvsStatus,
    val description: String? = null,
    val externalId: String? = null,
    val internalId: UUID? = null,
    val groupCompany: TotvsGroupCompany? = null,
    val errorCode: Int? = null,
    val errorMessage: String? = null,
    val type: BatchType,
)
