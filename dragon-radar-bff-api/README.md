# Dragon Radar BFF API

Este BFF é responsável pelo mapa da atenção terciária, onde é possível ter uma visão geral dos
membros internados ou em procedimentos cirúrgicos eletivos nos providers (hospitais).

**Mas por que dragon radar?**

No Dragon Ball Z existem as esferas do dragão, que ao juntar 7 voce pode realizar um desejo, e
para encontrá-las precisam construir um mapa.
Copiando a criação da grande Bulma, precisamos construir uma bússola/mapa de maneira que possamos encontrar nossas esferas
`"membros"` onde quer que eles estejam e assim conseguir realizar nosso desejo de tornar o mundo mais saudável. 🚀


![img.png](../dragon-radar-domain-service/resources/readme/dragon-radar.png)


### Responsible Team
Health Community, find us on ``#eng-atenção_secundária`` on Slack ;)

### Local development

Requirements
* Port 8197
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``db_reset db_seed db_run`` - to start our database and data layer
* ``run service=dragon-radar-bff-api`` - to run it
