package br.com.alice.api.dragonradar.converters

import br.com.alice.api.dragonradar.models.AttendanceEvolution
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import java.util.UUID

object AttendanceEvolutionResponseConverter {

    fun from(attendance: TertiaryIntentionTouchPoint, staffs: Map<UUID, Staff>) =
        attendance.evolutions.map {
            AttendanceEvolution(
                createdAt = it.createdAt,
                description = it.description,
                staffName = staffs[it.staffId]?.fullName,
                staffAvatarUrl = staffs[it.staffId]?.profileImageUrl,
            )
        }
}
