package br.com.alice.client.dalya.config

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object DalyaClientConfiguration {
    private val config = HoconApplicationConfig(ConfigFactory.load("dalya_client.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun dalyaUrl() = config.property("${environment().value.lowercase()}.dalyaUrl").getString()
}
