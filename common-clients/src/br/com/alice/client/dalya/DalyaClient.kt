package br.com.alice.client.dalya

import br.com.alice.client.dalya.config.DalyaClientConfiguration.dalyaUrl
import br.com.alice.client.dalya.models.DalyaTriageRecommendationResponse
import br.com.alice.client.dalya.models.TriageRecommendationRequest
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import com.github.kittinunf.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application.Json
import io.ktor.http.content.TextContent
import io.ktor.http.withCharset


class DalyaClient(
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 60_000)
) {

    suspend fun retrieveTriageOutputRecommendation(
        dalyaTriageRequest: TriageRecommendationRequest,
        url: String? = null
    ): Result<DalyaTriageRecommendationResponse, Throwable> =
        coResultOf {
            val response = post(
                url = url,
                payload = gson.toJson(dalyaTriageRequest)
            )

            DalyaTriageRecommendationResponse.fromJson(response)
        }

    suspend fun post(url: String?, payload: String): String {
        try {
            val url = url ?: (dalyaUrl() + "dalyatriage")
            val response: HttpResponse = httpClient.post(url) {
                setBody(TextContent(payload, Json.withCharset(Charsets.UTF_8)))
            }
            if (response.status.value == 200) {
                return response.bodyAsText()
            } else {
                logger.error(
                    "DalyaClient#retrieveTriageOutputRecommendation request failed",
                    "url" to url,
                    "payload" to payload,
                    "responseStatus" to response.status.value,
                    "responseBody" to response.bodyAsText()
                )
                throw BadRequestException(response.bodyAsText(), response.status.value.toString())
            }
        } catch (ex: Exception) {
            logger.error(
                "DalyaClient#retrieveTriageOutputRecommendation catch exception",
                "url" to url,
                "payload" to payload,
                "localizedMessage" to ex.localizedMessage,
                "stackTrace" to ex.stackTraceToString()
            )
            throw ex
        }
    }
}
