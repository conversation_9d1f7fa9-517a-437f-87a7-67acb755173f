package br.com.alice.fhir.clients

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.readFile
import br.com.alice.data.layer.helpers.TestModelFactory
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HaocClientTest {

    @Test
    fun `#authenticate - call client and get token`() = runBlocking<Unit> {

        val authResponse = readFile("testResources/haoc_authorization.json")

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://apimanager-interopteste.haoc.com.br:8243/token?grant_type=client_credentials")
            respond(
                content = ByteReadChannel(authResponse),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
        val client = HaocClient(getClient(engine))

        val token = client.authenticate()
        assertThat(token).isSuccessWithData("342f86fb-61cd-367c-9643-8d6ad38fcd25")

    }

    @Test
    fun `#getPersonTimeline - call client and get bundle`() = runBlocking<Unit> {
        val token = "token"
        val timelineResponse = readFile("testResources/haoc_bundle_timeline.json")
        val nationalId = ""

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://apimanager-interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter?subject%3APatient.identifier=urn%3Aoid%3A2.16.840.1.113883.13.237%7C${nationalId}&_count=5&_sort=-date")
            respond(
                content = ByteReadChannel(timelineResponse),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
        val client = HaocClient(getClient(engine))

        val bundleResponse = client.getPersonTimeline(token, nationalId).get()
        assertThat(bundleResponse.bundle.id).isEqualTo("cf79aa15-466e-46d6-885e-c2e63a21dc71")
        assertThat(bundleResponse.bundle.entry.size).isEqualTo(4)
    }

    @Test
    fun `#getPersonEncounter - call client and get bundle`() = runBlocking<Unit> {
        val encounterId = "1.42.20130403134532.123.1661367091610.2"
        val encounterResponse = readFile("testResources/haoc_bundle_encounter.json")
        val token = "token"

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://apimanager-interopteste.haoc.com.br:8243/ehrrunner/fhir/Encounter?_revinclude=Condition:encounter&_revinclude=Procedure:encounter&_revinclude=MedicationRequest:encounter&_revinclude=AllergyIntolerance:encounter&_revinclude=Observation:encounter&_revinclude=ServiceRequest:encounter&identifier=urn%3Aoid%3A${encounterId}")
            respond(
                content = ByteReadChannel(encounterResponse),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
        val client = HaocClient(getClient(engine))
        val bundleResponse = client.getPersonEncounter(token, encounterId).get()
        assertThat(bundleResponse.bundle.id).isEqualTo("276ab8aa-5878-438e-8a14-5611fb76b69d")
        assertThat(bundleResponse.bundle.entry.size).isEqualTo(8)
    }

    @Test
    fun `#createPatient - return true when creation is a success`() = runBlocking<Unit> {
        val mockkResponse = readFile("testResources/PixMessageResponse.xml")
        val person = TestModelFactory.buildPerson()
        val token = "token"

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://apimanager-interopteste.haoc.com.br:8243/res/ihe/pix/manager/v3")
            assertThat(it.method).isEqualTo(HttpMethod.Post)
            respond(
                content = ByteReadChannel(mockkResponse),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/soap+xml")
            )
        }
        val client = HaocClient(getClient(engine))

        val response = client.createPatient(token, person).get()
        assertThat(response).isEqualTo(true)
    }

    @Test
    fun `#createPatient - return false when creation is a failure`() = runBlocking<Unit> {
        val mockkResponse = readFile("testResources/PixMessageResponse.xml")
        val person = TestModelFactory.buildPerson()
        val token = "token"

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://apimanager-interopteste.haoc.com.br:8243/res/ihe/pix/manager/v3")
            assertThat(it.method).isEqualTo(HttpMethod.Post)
            respond(
                content = ByteReadChannel(mockkResponse),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/soap+xml")
            )
        }
        val client = HaocClient(getClient(engine))

        val response = client.createPatient(token, person).get()
        assertThat(response).isEqualTo(false)
    }

    private fun getClient(engine: HttpClientEngine) = HttpClient(engine = engine) { expectSuccess = true }

}
