package br.com.alice.fhir.clients

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.readFile
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Identifier
import br.com.alice.data.layer.models.ResourceCode
import br.com.alice.data.layer.models.ResourceCoding
import br.com.alice.data.layer.models.ResourceIdentifier
import br.com.alice.data.layer.models.ResourceMeta
import br.com.alice.data.layer.models.ResourcePeriod
import br.com.alice.data.layer.models.TextResource
import br.com.alice.fhir.ServiceConfig
import br.com.alice.fhir.model.ActorProvision
import br.com.alice.fhir.model.AttachmentResource
import br.com.alice.fhir.model.ConsentTransport
import br.com.alice.fhir.model.OrganizationTransport
import br.com.alice.fhir.model.PatientTransport
import br.com.alice.fhir.model.Provision
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.isSuccess
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import io.mockk.clearMocks
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.ZoneId
import java.time.ZonedDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFails

class DasaClientTest {
    private val httpClientMock: HttpClient = mockk(relaxUnitFun = true)
    private val gson = gsonCompleteSerializer
    private val credentials = ServiceConfig.Dasa

    @BeforeTest
    fun setup() {
        clearMocks(httpClientMock)
    }
    @Test
    fun `#authenticate should call authenticate successfully`() = runBlocking {
        val expected = AccessTokenResponse(accessToken = "abcd1234")
        val expectedJson = gson.toJson(expected)

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo(credentials.authUrl)
            respond(
                content = ByteReadChannel(expectedJson),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }

        val dasaClient = DasaClient(getClient(engine))

        val accessToken = dasaClient.getAccessToken().accessToken

        assertEquals(accessToken, expected.accessToken)
        assertEquals(engine.requestHistory.size, 1)
    }

    @Test
    fun `#authenticate should throw error if authentication failed`() = runBlocking {
        val dtoResponse = DasaAuthResponse(
            accessToken = RangeUUID.generate().toString(),
            expiresIn = 7000,
        )

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("https://id-hml.dasa.com.br/auth/realms/Parceiros/protocol/openid-connect/token")
            respond(
                content = ByteReadChannel(gson.toJson(dtoResponse)),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }

        val dasaClient = DasaClient(getClient(engine))

        assertFails {
            dasaClient.getAccessToken()
        }
        assertEquals(engine.requestHistory.size, 1)
    }

    @Test
    fun `#getImageTestResultAttachment should return pdf url successfully`(): Unit = runBlocking {
        val dtoResponse = DasaAuthResponse(
            accessToken = RangeUUID.generate().toString(),
            expiresIn = 7000,
        )
        val imageTestResultBundle = readFile("testResources/DasaImageTestResultBundle.json")

        val engine = MockEngine { request ->
            if (request.method === HttpMethod.Post) {
                assertThat(request.url.toString()).isEqualTo("https://id-hml.dasa.com.br/auth/realms/Parceiros/protocol/openid-connect/token")
                respond(
                    content = ByteReadChannel(gson.toJson(dtoResponse)),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            } else {
                assertThat(request.url.toString()).isEqualTo("https://interoperabilidade-hml.dasa.com.br/fhir/dw/DiagnosticReport?identifier=https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem%7Cx")
                respond(
                    content = imageTestResultBundle,
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        }

        val dasaClient = DasaClient(getClient(engine))

        val response = dasaClient.getAttachmentUrl("x")

        assertEquals(response.isSuccess(), true)
        val attachment = response.get()
        assertEquals(
            attachment.url,
            "https://bkt-sa-east-1-deepwater-prd-pdf.s3.sa-east-1.amazonaws.com/RDI-************-1646316276283.pdf?AWSAccessKeyId=AKIAQBHT2D72PUOHBEHO&Expires=1646322276&Signature=kuTTTl%2FDbnys75mtoPt5uC3bEG0%3D"
        )
    }

    @Test
    fun `#getImageTestResultAttachment should return pdf url successfully when contains multiple links`(): Unit = runBlocking {
        val dtoResponse = DasaAuthResponse(
            accessToken = RangeUUID.generate().toString(),
            expiresIn = 7000,
        )
        val imageTestResultBundle = readFile("testResources/DasaImageTestResultBundleMultipleLinks.json")

        val engine = MockEngine { request ->
            if (request.method === HttpMethod.Post) {
                assertThat(request.url.toString()).isEqualTo("https://id-hml.dasa.com.br/auth/realms/Parceiros/protocol/openid-connect/token")
                respond(
                    content = ByteReadChannel(gson.toJson(dtoResponse)),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            } else {
                assertThat(request.url.toString()).isEqualTo("https://interoperabilidade-hml.dasa.com.br/fhir/dw/DiagnosticReport?identifier=https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem%7Cx")
                respond(
                    content = imageTestResultBundle,
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        }

        val dasaClient = DasaClient(getClient(engine))

        val response = dasaClient.getAttachmentUrl("x")

        assertEquals(response.isSuccess(), true)
        val attachment = response.get()
        assertEquals(
            attachment.url,
            "https://bkt-sa-east-1-deepwater-prd-pdf.s3.sa-east-1.amazonaws.com/RDI-************-1646316276283.pdf?AWSAccessKeyId=AKIAQBHT2D72PUOHBEHO&Expires=1646322276&Signature=kuTTTl%2FDbnys75mtoPt5uC3bEG0%3D"
        )
    }

    @Test
    fun `#getImageTestResultAttachment should failure if DASA API return status not ok`(): Unit = runBlocking {
        val dtoResponse = DasaAuthResponse(
            accessToken = RangeUUID.generate().toString(),
            expiresIn = 7000,
        )
        val imageTestResultBundle = readFile("testResources/DasaImageTestResultBundleWithoutURL.json")

        val engine = MockEngine { request ->
            if (request.method === HttpMethod.Post) {
                assertThat(request.url.toString()).isEqualTo("https://id-hml.dasa.com.br/auth/realms/Parceiros/protocol/openid-connect/token")
                respond(
                    content = ByteReadChannel(gson.toJson(dtoResponse)),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            } else {
                assertThat(request.url.toString()).isEqualTo("https://interoperabilidade-hml.dasa.com.br/fhir/dw/DiagnosticReport?identifier=https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem%7Cx")
                respond(
                    content = imageTestResultBundle,
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        }

        val dasaClient = DasaClient(getClient(engine))

        val response = dasaClient.getAttachmentUrl("x")

        assertEquals(response.isFailure(), true)
    }

    @Test
    fun `#sendConsent should send Consent`() = runBlocking {
        val authorization = "authorization"
        val consent = consentRequest

        val engine = MockEngine {
            assertThat(it.url.toString()).isEqualTo("${credentials.baseUrl}/fhir/Consent")
            assertThat(it.headers["Authorization"]).isEqualTo("Bearer $authorization")
            assertThat(it.headers["Subscription"]).isEqualTo(credentials.subscriptionKey)
            assertThat(it.method).isEqualTo(HttpMethod.Post)

            respond("ok")
        }

        val clientMocked = DasaClient(getClient(engine))
        val response = clientMocked.sendConsent(consent, authorization, TestModelFactory.buildPerson())

        ResultAssert.assertThat(response).isSuccessWithData("ok")
    }

    private fun getClient(engine: HttpClientEngine) =
        HttpClient(engine = engine) { expectSuccess = true }
}

val organizationRequest = OrganizationTransport(
    id = RangeUUID.generate().toString(),
    resourceType = "Organization",
    meta = ResourceMeta(
        lastUpdated = "2020-03-10T10:00:00-03:00",
        profile = listOf(
            "https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Organization"
        )
    ),
    identifier = listOf(
        ResourceIdentifier(
            use = "official",
            system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoajuridica-id",
            value = "34266553000102"
        )
    ),
    status = "active",
    name = "Alice Operadora Ltda"
)

val patientRequest = PatientTransport(
    id = RangeUUID.generate().toString(),
    status = "active",
    resourceType = "Patient",
    meta = ResourceMeta(
        lastUpdated = "2020-03-10T10:00:00-03:00",
        profile = listOf(
            "https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Patient"
        )
    ),
    identifier = listOf(
        ResourceIdentifier(
            use = "usual",
            system = "https://www.alice.com.br/interoperabilidade/NamingSystem/name-system-patient",
            value = "1"
        ),
        ResourceIdentifier(
            use = "official",
            system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoafisica-id",
            value = "nationalId"
        )
    ),
    name = listOf(
        TextResource(
            use = "official",
            text = "person Name"
        )
    ),
    gender = "other",
    birthDate = "2020-03-10T10"
)

val consentRequest = ConsentTransport(
    id = "id",
    resourceType = "resourceType",
    meta = ResourceMeta(
        lastUpdated = ZonedDateTime.now(ZoneId.of("America/Sao_Paulo")).toOffsetDateTime().toString(),
        profile = listOf("https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Consent")
    ),
    identifier = listOf(
        ResourceIdentifier(
            use = "usual",
            system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-mdm-consentimento-id",
            value = "1"
        )
    ),
    sourceAttachment = AttachmentResource("content-type", "data"),
    status = "active",
    patient = Identifier(
        identifier = ResourceIdentifier(
            use = "official",
            system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoafisica-id",
            value = "07422345330"
        ),
        reference = "#${patientRequest.id}"
    ),
    policyRule = ResourceCode(
        coding = listOf(
            ResourceCoding(
                system = "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                code = "OPTIN"
            )
        )
    ),
    organization = listOf(
        Identifier(
            identifier = ResourceIdentifier(
                use = "official",
                system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoajuridica-id",
                value = "34266553000102"
            ),
            reference = "#${organizationRequest.id}"
        )
    ),
    scope = ResourceCode(
        coding = listOf(
            ResourceCoding(
                system = "http://terminology.hl7.org/CodeSystem/consentscope",
                code = "patient-privacy"
            )
        )
    ),
    category = listOf(
        ResourceCode(
            coding = listOf(
                ResourceCoding(
                    system = "http://loinc.org",
                    code = "59284-0"
                )
            )
        )
    ),
    provision = Provision(
        actor = listOf(
            ActorProvision(
                role = ResourceCode(
                    coding = listOf(
                        ResourceCoding(
                            system = "http://terminology.hl7.org/CodeSystem/v3-RoleClass",
                            code = "GRANTEE"
                        )
                    )
                ),
                reference = Identifier(
                    identifier = ResourceIdentifier(
                        use = "official",
                        system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoajuridica-id",
                        value = "34266553000102"
                    )
                )
            )
        ),
        purpose = listOf(
            ResourceCoding(
                system = "http://terminology.hl7.org/CodeSystem/v3-ActReason",
                code = "HOPERAT"
            )
        ),
        period = ResourcePeriod(
            start = "2020-03-10T10:00:00-03:00"
        )
    ),
    contained = listOf(
        patientRequest,
        organizationRequest
    )
)
