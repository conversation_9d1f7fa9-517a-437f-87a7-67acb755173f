package br.com.alice.fhir.services

import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.fhir.event.BundleNotificationEvent
import br.com.alice.fhir.event.SendPatientHistoryEvent
import br.com.alice.fhir.event.push.PushNotificationSantaJoanaEvent
import br.com.alice.fhir.model.FhirPatientNotification
import br.com.alice.fhir.model.PatientToken
import br.com.alice.fhir.model.PushNotificationSantaJoana
import br.com.alice.fhir.model.ReasonNotification
import io.mockk.clearAllMocks
import io.mockk.clearConstructorMockk
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class PatientNotificationServiceImplTest {
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = PatientNotificationServiceImpl(kafkaProducerService)

    @BeforeTest
    fun setup() {
        clearAllMocks()
        clearConstructorMockk()
    }

    @AfterTest
    fun cleanup() {
        unmockkAll()
    }

    @Test
    fun `#notification - should send notification when it is ADMITTED`() = runBlocking {
        val consentRegistration = FhirPatientNotification(
            patient = PatientToken(
                nationalId = "**********",
                publicToken = "PublicToken"
            ),
            encounterIdentifier = null,
            reason = ReasonNotification.ADMITTED,
            provider = ProviderIntegration.BP
        )

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        service.notification(consentRegistration)

        coVerify (exactly = 1){ kafkaProducerService.produce(
            match { it.name == SendPatientHistoryEvent.name }
        ) }
    }

    @Test
    fun `#notification - should send notification when it is DISCHARGE`() = runBlocking {
        val consentRegistration = FhirPatientNotification(
            patient = PatientToken(
                nationalId = "**********",
                publicToken = "PublicToken"
            ),
            encounterIdentifier = "encounterIdentifier",
            reason = ReasonNotification.DISCHARGE,
            provider = ProviderIntegration.BP
        )
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        service.notification(consentRegistration)

        coVerify (exactly = 1){ kafkaProducerService.produce(
            match { it.name == BundleNotificationEvent.name }
        ) }
    }

    @Test
    fun `#notification - should send notification when provider is Santa Joana`() = runBlocking {
        val push = PushNotificationSantaJoana(
            cdMpi = "cdMPI",
            nrCpf = "**********0",
            ieTipoMsg = "1",
            nmPaciente = "300001233"
        )
        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        service.notificationSantaJoana(push)

        coVerify (exactly = 1){ kafkaProducerService.produce(
            match { it.name == PushNotificationSantaJoanaEvent.name }
        ) }
    }

}
