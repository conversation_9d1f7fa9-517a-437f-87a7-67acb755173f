package br.com.alice.fhir.services.internal

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.copy
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class RelateHealthTaskWithAppointmentTest {

    private val personId = PersonId()

    @Test
    fun `#link should link HealthPlanTasks to Appointment`() {
        val healthPlan = TestModelFactory.buildHealthPlan(personId = personId)
        val appointments = listOf(
            TestModelFactory.buildAppointment(
                personId = personId,
                createdAt = LocalDateTime.of(2022, 7, 2, 14, 0, 0),
            ).copy(
                completedAt = LocalDateTime.of(2022, 7, 2, 14, 30, 0)
            ),
            TestModelFactory.buildAppointment(
                personId = personId,
                createdAt = LocalDateTime.of(2022, 7, 2, 15, 0, 0),
            ).copy(
                completedAt = LocalDateTime.of(2022, 7, 2, 15, 30, 0)
            )
        )
        val tasks = listOf(
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 2, 14, 10, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 2, 14, 59, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 2, 15, 59, 0, 0)),
        )

        val result = RelateHealthTaskWithAppointment.link(appointments, tasks)

        val firstAppointment = appointments[0]
        val secondAppointment = appointments[1]

        assertThat(result[firstAppointment.id]).isEqualTo(tasks.subList(0, 2))
        assertThat(result[secondAppointment.id]).isEqualTo(listOf(tasks[2]))
    }

    @Test
    fun `#link should link HealthPlanTasks to Appointment only ones that are in valid interval after complete it`() {
        val healthPlan = TestModelFactory.buildHealthPlan(personId = personId)
        val appointments = listOf(
            TestModelFactory.buildAppointment(
                personId = personId,
                createdAt = LocalDateTime.of(2022, 7, 2, 14, 0, 0),
            ).copy(
                completedAt = LocalDateTime.of(2022, 7, 2, 14, 30, 0)
            ),
            TestModelFactory.buildAppointment(
                personId = personId,
                createdAt = LocalDateTime.of(2022, 7, 4, 15, 0, 0),
            ).copy(
                completedAt = LocalDateTime.of(2022, 7, 4, 15, 30, 0)
            )
        )
        val tasks = listOf(
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 2, 14, 10, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 3, 14, 30, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 3, 14, 50, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 4, 15, 10, 0, 0)),
            TestModelFactory
                .buildHealthPlanTask(personId = personId, healthPlanId = healthPlan.id)
                .copy(releasedAt = LocalDateTime.of(2022, 7, 4, 15, 59, 0, 0)),
        )

        val result = RelateHealthTaskWithAppointment.link(appointments, tasks)

        val firstAppointment = appointments[0]
        val secondAppointment = appointments[1]

        assertThat(result[firstAppointment.id]).isEqualTo(tasks.subList(0, 2))
        assertThat(result[secondAppointment.id]).isEqualTo(tasks.subList(3, 5))
    }
}
