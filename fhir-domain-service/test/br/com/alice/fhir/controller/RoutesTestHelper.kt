package br.com.alice.fhir.controller

import br.com.alice.authentication.Authenticator
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.featureconfig.core.FeaturePopulateService
import br.com.alice.fhir.module
import io.ktor.server.application.Application
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import kotlin.test.BeforeTest

abstract class RoutesTestHelper : RoutesTestHelper() {

    private val featurePopulateService: FeaturePopulateService = mockk()

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { featurePopulateService } }
            )
        )
    }

    @BeforeTest
    override fun setup() {
        super.setup()

        mockkObject(Authenticator)
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "idToken"
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
    }

}
