package br.com.alice.fhir.consumers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.TermType
import br.com.alice.fhir.client.ConsentByTermService
import br.com.alice.membership.model.events.MemberContractTermSignedEvent
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class DasaConsumer(
    private val consentService: ConsentByTermService,
    private val memberService: MemberService
) : Consumer() {

    suspend fun consentByTerm(event: MemberContractTermSignedEvent): Result<Any, Throwable> {
        val term = event.payload.memberContractTermSigned
        logger.info(
            "Processing MemberContractTermSignedEvent event to create a consent",
            "term_type" to term.termType,
            "member" to term.memberId
        )

        if (term.termType != TermType.DATA_PROCESSING_TERMS) return false.success()

        return withSubscribersEnvironment {
            val member = memberService.get(term.memberId).get()

            if (consentService.shouldCreateConsent(member).get())
                consentService.createConsent(member, term)
            else {
                logger.info(
                    "Not processing memberContractTermSigned member for now",
                    "member_id" to term.memberId
                )
                true.success()
            }
        }
    }
}
