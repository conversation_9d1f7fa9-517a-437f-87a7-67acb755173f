package br.com.alice.fhir.consumers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HospitalSummaryHistory
import br.com.alice.fhir.event.HospitalSummaryHistoryEvent
import br.com.alice.fhir.services.internal.HospitalSummaryInternalService
import com.github.kittinunf.result.Result

class HospitalSummaryHistoryConsumer(
    private val hospitalSummaryInternalService: HospitalSummaryInternalService
) : Consumer() {

    suspend fun saveHospitalSummaryHistory(event: HospitalSummaryHistoryEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val payload = event.payload

            logger.info(
                "HospitalSummaryHistoryConsumer.saveHospitalSummaryHistory",
                "person_id" to payload.personId,
                "event_type" to payload.eventType,
                "provider_integration" to payload.provider,
                "external_id" to payload.externalId,
                "event_id" to payload.eventId,
                "unit" to payload.unit,
                "stack_trace" to payload.stackTrace,
                "event_value" to payload.eventValue
            )

            val model = HospitalSummaryHistory(
                personId = payload.personId,
                providerIntegration = payload.provider,
                eventType = payload.eventType,
                unit = payload.unit,
                eventValue = payload.eventValue,
                externalId = payload.externalId,
                eventId = payload.eventId
            )

            hospitalSummaryInternalService.save(model)
        }

}
