package br.com.alice.fhir.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toRangeSafeUUID
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.SummaryHistoryEventType
import br.com.alice.data.layer.models.SummaryHistoryEventType.ENCOUNTER_DISCHARGED_AT
import br.com.alice.data.layer.models.SummaryHistoryEventType.NOTIFICATION_ADMITTED
import br.com.alice.data.layer.models.SummaryHistoryEventType.NOTIFICATION_DISCHARGE
import br.com.alice.data.layer.models.SummaryHistoryEventType.SAVE_DISCHARGE_SUMMARY
import br.com.alice.data.layer.models.SummaryHistoryEventType.SAVE_FHIR_DOCUMENT
import br.com.alice.data.layer.models.SummaryHistoryEventType.SUMMARY_MATCH_WITHOUT_TERTIARY
import br.com.alice.data.layer.models.SummaryHistoryEventType.SUMMARY_MATCH_WITH_TERTIARY
import br.com.alice.einsteinintegrationclient.events.EinsteinDischargeSummaryReceivedEvent
import br.com.alice.fhir.converters.FhirNotificationConverter.getReason
import br.com.alice.fhir.event.FhirDocumentUpsertEvent
import br.com.alice.fhir.event.HospitalSummaryHistoryEvent
import br.com.alice.fhir.event.HospitalSummaryPayload
import br.com.alice.fhir.event.RegisterPublicTokenEvent
import br.com.alice.fhir.event.push.PushNotificationSantaJoanaEvent
import br.com.alice.fhir.model.ReasonNotification
import br.com.alice.person.client.PersonService
import br.com.alice.testresult.events.DischargeSummaryCardEvent
import br.com.alice.testresult.events.DischargeSummaryUpsertEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class NotifyHospitalSummaryHistoryConsumer(
    private val personService: PersonService,
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {
    suspend fun notifyPush(event: RegisterPublicTokenEvent): Result<Any, Throwable> {
        val payload = event.payload
        val nationalId = payload.patient.nationalId
        return withSubscribersEnvironmentWithKey(nationalId) {
            personService.findByNationalId(nationalId).map { person ->
                producerHospitalSummaryHistory(
                    personId = person.id,
                    provider = payload.provider,
                    eventId = event.messageId,
                    eventType = payload.reason.toEventType(),
                    externalId = payload.encounterIdentifier,
                )
            }
        }
    }

    suspend fun notifyMatchCard(event: DischargeSummaryCardEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val payload = event.payload
        val summary = event.payload.summary
        val eventType =
            if (payload.hasLink()) SUMMARY_MATCH_WITH_TERTIARY else SUMMARY_MATCH_WITHOUT_TERTIARY

        producerHospitalSummaryHistory(
            personId = summary.personId,
            provider = summary.provider,
            externalId = summary.externalId,
            eventId = summary.externalId.toRangeSafeUUID(),
            eventValue = payload.tertiaryId.toString(),
            eventType = eventType
        )

        true.success()
    }

    suspend fun saveFhirDocument(event: FhirDocumentUpsertEvent): Result<Any, Throwable> = withSubscribersEnvironment {
        val document = event.payload
        producerHospitalSummaryHistory(
            personId = document.personId,
            provider = document.provider,
            eventId = document.externalId.toRangeSafeUUID(),
            eventType = SAVE_FHIR_DOCUMENT,
            externalId = document.externalId,
        )
        true.success()
    }

    suspend fun registerEncounterDate(event: DischargeSummaryUpsertEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val summary = event.payload.summary

            producerHospitalSummaryHistory(
                personId = summary.personId,
                provider = summary.provider,
                eventId = summary.externalId.toRangeSafeUUID(),
                eventType = ENCOUNTER_DISCHARGED_AT,
                eventValue = summary.dischargeItem.dischargedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
                externalId = summary.externalId,
                unit = summary.dischargeItem.location,
            )
            true.success()
        }

    suspend fun saveDischargeSummary(event: DischargeSummaryUpsertEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val summary = event.payload.summary

            producerHospitalSummaryHistory(
                personId = summary.personId,
                provider = summary.provider,
                eventId = summary.externalId.toRangeSafeUUID(),
                eventType = SAVE_DISCHARGE_SUMMARY,
                externalId = summary.externalId,
                eventValue = summary.id.toString(),
                unit = summary.dischargeItem.location,
            )
            true.success()
        }


    private suspend fun producerHospitalSummaryHistory(
        personId: PersonId,
        provider: ProviderIntegration,
        eventId: UUID,
        eventType: SummaryHistoryEventType,
        externalId: String? = null,
        eventValue: String? = null,
        unit: String? = null,
    ) = kafkaProducerService.produce(
        HospitalSummaryHistoryEvent(
            HospitalSummaryPayload(
                personId = personId,
                provider = provider,
                eventId = eventId,
                eventType = eventType,
                unit = unit,
                eventValue = eventValue,
                externalId = externalId
            )
        )
    )

    private fun ReasonNotification.toEventType(): SummaryHistoryEventType = when (this) {
        ReasonNotification.ADMITTED -> NOTIFICATION_ADMITTED
        ReasonNotification.DISCHARGE -> NOTIFICATION_DISCHARGE
    }

    suspend fun notifyPushSantaJoana(event: PushNotificationSantaJoanaEvent): Result<Any, Throwable> {
        val payload = event.payload
        val reason = getReason(payload.ieTipoMsg)
        val nationalId = payload.nrCpf.onlyNumbers()

        return withSubscribersEnvironmentWithKey(nationalId) {
            personService.findByNationalId(nationalId).map { person ->
                producerHospitalSummaryHistory(
                    personId = person.id,
                    provider = ProviderIntegration.SANTA_JOANA,
                    eventId = event.messageId,
                    eventType = reason.toEventType(),
                    externalId = payload.nrAtendimento,
                )
            }
        }
    }

    suspend fun notifyPushEinstein(event: EinsteinDischargeSummaryReceivedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val payload = event.payload
            producerHospitalSummaryHistory(
                personId = payload.personId,
                provider = ProviderIntegration.EINSTEIN,
                eventId = event.messageId,
                eventType = NOTIFICATION_DISCHARGE,
                externalId = payload.passagem,
            )

            true.success()
        }
}
