package br.com.alice.fhir.builders

import br.com.alice.common.RangeUUID
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.Person
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

// Patient Identifier Cross-Referencing: https://projectcore.atlassian.net/wiki/spaces/IH/pages/264077407/PIX+PDQ
object PixMessageBuilder {
    private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm")
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
    private const val countryCode = "BR"
    // For now we are considering that the person is from São Paulo city
    private const val saoPauloIbgeCityCode = "3550308"

    fun build(person: Person, aliceAnsNumber: String): String {
        val messageUUID = RangeUUID.generate()

        val address = if (person.addresses.isNotEmpty()) {
            person.addresses[0]
        } else {
            null
        }

        val mothersName = person.mothersName ?: "N/A"
        val street = address?.street ?: ""
        val streetNumber = address?.number ?: ""
        val postalCode = address?.postalCode ?: ""
        val state = address?.state ?: ""
        val complement = address?.complement ?: ""
        val neighbourhood = address?.neighbourhood ?: ""

        val birthTimeElement = if (person.dateOfBirth != null)
            "<birthTime value=\"${person.dateOfBirth!!.format(dateFormatter)}\"/>"
        else ""

        val administrativeGenderCode = if (person.gender != null) {
            val code = when(person.gender) {
                Gender.MALE -> "M"
                Gender.FEMALE -> "F"
                Gender.NON_BINARY -> "UN"
                else -> "UN"
            }
            "<administrativeGenderCode code=\"$code\" codeSystem=\"2.16.840.1.113883.5.1\"/>"
        } else ""

        return """
<env:Envelope xmlns:env="http://www.w3.org/2003/05/soap-envelope">
   <env:Header/>
   <env:Body>
      <PRPA_IN201301UV02 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3">
         <!--Identificador da mensagem - gerado na aplicação de origem-->
         <id extension="$messageUUID"/>
         <!--Data e hora de criação da mensagem - gerado na aplicação de origem-->
         <!--Nota: formato HL7 i.e. YYYYMMDDHHMM-->
         <creationTime value="${LocalDateTime.now().format(dateTimeFormatter)}"/>
         <!--Identificador da transação:-->
         <!--PRPA_IN201301UV02 (Patient Added)-->
         <interactionId extension="PRPA_IN201301UV02" root="2.16.840.1.113883.1.6"/>
         <processingCode code="P"/>
         <processingModeCode code="T"/>
         <acceptAckCode code="AL"/>
         <!--Identificador do MPI (PIX Manager)-->
         <receiver typeCode="RCV">
            <device classCode="DEV" determinerCode="INSTANCE">
               <!--Identificador da instância do MPI/RES (OID)-->
               <id root="*******.4.1.54443" extension="*******.4.1.54443.1.1.4.1"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <!--Identificador da org detentora do MPI, i.e. CNES do HAOC-->
                     <id root="2.16.840.1.113883.13.36" extension="2078597"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </receiver>
         <!--Identificador da Aplicação Origem (Patient Identity Source)-->
         <sender typeCode="SND">
            <device classCode="DEV" determinerCode="INSTANCE">
               <!--Identificador do software que envia, p.e. ProntLife (OID) -->
               <!-- <id root="*******.4.1.54443.1.1.5" extension="*******.4.1.54443.1.1.5.1"/> -->
               <asAgent classCode="AGNT">
                  <!--Identificador da org que envia, i.e. Registro ANS da Alice -->
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id extension="*******.4.1.54443.2.1" root="$aliceAnsNumber"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </sender>
         <controlActProcess classCode="CACT" moodCode="EVN">
            <code code="PRPA_TE201301UV02" codeSystem="2.16.840.1.113883.1.6"/>
            <subject typeCode="SUBJ">
               <registrationEvent classCode="REG" moodCode="EVN">
                  <id nullFlavor="NA"/>
                  <statusCode code="active"/>
                  <subject1 typeCode="SBJ">
                     <!--Dados do Paciente-->
                     <patient classCode="PAT">
                        <!--Aqui será utilizado o CPF-->
                        <id extension="${person.nationalId}" root="2.16.840.1.113883.13.237"/>
                        <statusCode code="active"/>
                        <confidentialityCode code="N" codeSystem="2.16.840.1.113883.5.25"/>
                        <!--urn:veryImportantPersonCode code="VIP" codeSystem="2.16.840.1.113883.5.1075"/-->
                        <patientPerson classCode="PSN" determinerCode="INSTANCE">
                           <name use="L">
                              <given>${person.firstName}</given>
                              <family>${person.lastName}</family>
                           </name>
                           <name use="ASGN">
                              <given>${person.nickName ?: person.firstName}</given>
                           </name>
                           $administrativeGenderCode
                           <addr use="H">
                              <streetName>$street</streetName>
                              <houseNumber>$streetNumber</houseNumber>
                              <unitID>$complement</unitID>
                              <additionalLocator>$neighbourhood</additionalLocator>
                              <city>$saoPauloIbgeCityCode</city>
                              <state>$state</state>
                              <postalCode>$postalCode</postalCode>
                              <country>$countryCode</country>
                           </addr>
                           $birthTimeElement
                           <!-- 
                           <birthPlace classCode="BIRTHPL" determinerCode="INSTANCE">
                              <addr>
                                 <city>99999</city>
                              </addr>
                           </birthPlace> 
                            -->
                           <!--  <telecom use="PRN" value="+55-61-981435507"/> -->
                           <telecom use="NET" value="${person.email}"/>
                           <!--Nome da Mae-->
                           <personalRelationship classCode="PRS">
                              <code code="MTH" codeSystem="2.16.840.1.113883.1.11.19563" displayName="Nome da Mae"/>
                              <relationshipHolder1 classCode="PSN" determinerCode="INSTANCE">
                                 <name use="L">
                                    <given>$mothersName</given>
                                 </name>
                              </relationshipHolder1>
                           </personalRelationship>
                        </patientPerson>
                        <providerOrganization classCode="ORG" determinerCode="INSTANCE">
                            <!-- Identificador da org responsavel pelo registro do paciente, p.e. Registro ANS da Alice -->
                            <id extension="$aliceAnsNumber" root="*******.4.1.54443.2.1"/>
                        </providerOrganization>
                     </patient>
                  </subject1>
               </registrationEvent>
            </subject>
         </controlActProcess>
      </PRPA_IN201301UV02>
   </env:Body>
</env:Envelope>
"""
    }

}
