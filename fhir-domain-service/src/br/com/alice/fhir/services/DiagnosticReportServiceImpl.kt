package br.com.alice.fhir.services

import br.com.alice.data.layer.models.FhirDiagnosticReport
import br.com.alice.data.layer.services.FhirDiagnosticReportDataService
import br.com.alice.fhir.client.DiagnosticReportService
import com.github.kittinunf.result.Result
@Deprecated("use fhirDocument")
class DiagnosticReportServiceImpl(
    private val dataService: FhirDiagnosticReportDataService,
) : DiagnosticReportService {
    override suspend fun getByExternalId(externalId: String): Result<FhirDiagnosticReport, Throwable> =
        dataService.findOne { where { this.externalId.eq(externalId) } }

}
