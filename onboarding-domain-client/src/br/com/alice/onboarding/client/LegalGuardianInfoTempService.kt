package br.com.alice.onboarding.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.LegalGuardianInfoTemp
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface LegalGuardianInfoTempService: Service {
    override val namespace get() = "onboarding"
    override val serviceName get() = "legal_guardian_info_temp"

    suspend fun get(id: UUID): Result<LegalGuardianInfoTemp, Throwable>
    suspend fun getByChildId(childId: PersonId): Result<LegalGuardianInfoTemp, Throwable>
    suspend fun create(legalGuardianInfoTemp: LegalGuardianInfoTemp): Result<LegalGuardianInfoTemp, Throwable>
    suspend fun archive(legalGuardianInfoTemp: LegalGuardianInfoTemp): Result<LegalGuardianInfoTemp, Throwable>
}

class LegalGuardianInfoNotFoundException(
    message: String,
    code: String = "legal_guardian_info_not_found",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(
        message = "LegalGuardianInfoTemp with id = $id, was not found",
    )
}

class ChildAlreadyHasLegalGuardianException(
    message: String,
    code: String = "child_already_has_legal_guardian",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId) : this(
        message = "LegalGuardianInfoTemp with personId = $personId was found",
    )
}

class PersonWithChildIdNotFoundException(
    message: String,
    code: String = "person_with_child_id_not_found",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId) : this(
        message = "Person with personId = $personId was not found",
    )
}

class ChildLegalGuardianSameEmailException(
    message: String = "Child and legal guardian email cannot be equal",
    code: String = "child_legal_guardian_invalid",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)
