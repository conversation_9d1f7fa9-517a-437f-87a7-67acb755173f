package br.com.alice.amas.consumers

import br.com.alice.amas.events.TissUploadedEvent
import br.com.alice.amas.events.TissUploadedEventPayload
import br.com.alice.amas.services.internal.TissUploadedService
import br.com.alice.common.RangeUUID
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class TissUploadedConsumerTest : ConsumerTest() {

    private val service: TissUploadedService = mockk()
    private val consumer = TissUploadedConsumer(service)

    private val payload = TissUploadedEventPayload(
        tissBatchId = RangeUUID.generate()
    )
    private val event = TissUploadedEvent(payload)

    @Test
    fun `#validate once`() = runBlocking {
        coEvery { service.process(any()) } just Runs
        consumer.process(event)
        coVerify(exactly = 1) { service.process(any()) }
    }
}
