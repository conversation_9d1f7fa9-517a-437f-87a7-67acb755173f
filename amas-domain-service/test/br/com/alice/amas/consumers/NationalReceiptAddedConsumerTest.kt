package br.com.alice.amas.consumers

import br.com.alice.amas.events.NationalReceiptAddedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.data.layer.models.NationalReceipt
import br.com.alice.data.layer.models.NationalReceiptStatus
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class NationalReceiptAddedConsumerTest: ConsumerTest() {
    private val fileVaultActionService = mockk<FileVaultActionService>()
    private val emailSenderClient = mockk<EmailSenderClient>()

    private val consumer = NationalReceiptAddedConsumer(fileVaultActionService, emailSenderClient)
    private val nationalReceipt = NationalReceipt(
        id = UUID.randomUUID(),
        clientCnpj = "12345678000195",
        issuedAt = LocalDate.now().atStartOfDay(),
        issuerCnpj = "12345678000195",
        number = "123456789",
        status = NationalReceiptStatus.RECEIVED,
        totalValue = BigDecimal(100),
        externalId = "externalId",
        publicDocumentFileVaultId = UUID.randomUUID(),
    )
    private val event = NationalReceiptAddedEvent(nationalReceipt)
    private val invalidEvent = NationalReceiptAddedEvent(nationalReceipt.copy(publicDocumentFileVaultId = null))

    private val file = File("testResources/csv/csv-example-correct.csv")
    private val mockedVaultResponse = VaultResponse(url = "google.com/file", RangeUUID.generate(), type = "csv", vaultUrl = "google.com/file")

    @Test
    fun `#sendNFtoTransmite should send nf`() = runBlocking {
        coEvery { fileVaultActionService.genericFileContentById(event.payload.nationalReceipt.publicDocumentFileVaultId!!) } returns file.readBytes().success()
        coEvery { fileVaultActionService.securedGenericLink(event.payload.nationalReceipt.publicDocumentFileVaultId!!) } returns mockedVaultResponse.success()

        coEvery { emailSenderClient.send(any()) } returns EmailReceipt("123")
        val res = consumer.sendNFtoTransmite(event)

        ResultAssert.assertThat(res).isSuccess()

        coVerifyOnce { emailSenderClient.send(any()) }
    }

    @Test
    fun `#sendNFtoTransmite should ignore if vault id is null`() = runBlocking {
        val res = consumer.sendNFtoTransmite(invalidEvent)

        ResultAssert.assertThat(res).isSuccess()

        coVerifyNone { emailSenderClient.send(any()) }
    }
}

