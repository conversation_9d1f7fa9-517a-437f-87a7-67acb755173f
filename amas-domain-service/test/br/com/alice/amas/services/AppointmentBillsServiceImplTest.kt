package br.com.alice.amas.services

import br.com.alice.amas.client.AppointmentBillsSortingKey
import br.com.alice.amas.client.GuiaFilters
import br.com.alice.amas.client.GuiaProcedureService
import br.com.alice.amas.client.GuiaService
import br.com.alice.amas.client.GuiaWithGuiaProcedures
import br.com.alice.amas.client.GuiaWithGuiaProceduresListWithCount
import br.com.alice.amas.client.GuiasListWithCount
import br.com.alice.amas.client.SortingOrder
import br.com.alice.amas.client.TissBatchService
import br.com.alice.amas.models.TissGuiaMemberResponse
import br.com.alice.amas.models.TissGuiaProviderRequesterResponse
import br.com.alice.amas.models.TissGuiaResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAttendanceType
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AppointmentBillsServiceImplTest {

    private val guiaService: GuiaService = mockk()
    private val guiaProcedureService: GuiaProcedureService = mockk()
    private val personService: PersonService = mockk()
    private val person = TestModelFactory.buildPerson().copy(firstName = "José")
    private val tissBatchService: TissBatchService = mockk()

    private val service = AppointmentBillsServiceImpl(
        guiaService,
        personService,
        guiaProcedureService,
        tissBatchService
    )

    @AfterTest
    fun clear() = clearAllMocks()

    private val tissBatchID: UUID = RangeUUID.generate()
    private val guiasWithPerson = listOf(
        TestModelFactory.buildTissGuia(tissBatchId = tissBatchID, person = person.id, memberName = person.fullSocialName),
        TestModelFactory.buildTissGuia(tissBatchId = tissBatchID, person = person.id, memberName = person.fullSocialName)
    )

    private val guiasWithPersonResponse = guiasWithPerson.map { guia ->
        TissGuiaResponse(
            id = guia.id,
            number = guia.number,
            member = TissGuiaMemberResponse(
                id = guia.personId?.id,
                name = person.socialName ?: person.fullRegisterName,
                nationalId = person.nationalId
            ),
            attendanceType = guia.attendanceType?.let { AnsAttendanceType.fromCode(it)?.description } ?: "",
            providerRequester = TissGuiaProviderRequesterResponse(
                cnpj = guia.providerRequesterCnpj,
                name = guia.providerRequesterName,
                code = guia.providerRequesterCode
            )
        )
    }

    private val proceduresWithPerson = listOf(
        TestModelFactory.buildTissGuiaProcedure(guiaId = guiasWithPersonResponse[0].id),
        TestModelFactory.buildTissGuiaProcedure(guiaId = guiasWithPersonResponse[0].id),
        TestModelFactory.buildTissGuiaProcedure(guiaId = guiasWithPersonResponse[1].id),
        TestModelFactory.buildTissGuiaProcedure(guiaId = guiasWithPersonResponse[1].id)
    )

    @Test
    fun `#getGuiasByInvoiceId should get guias by invoice id paginated`() =
        runBlocking<Unit> {
            val invoiceId = RangeUUID.generate()
            val tissBatch = TestModelFactory.buildTissBatch(invoiceId = invoiceId)
            val filters = GuiaFilters(
                executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT
            )

            val expectedResponse = GuiaWithGuiaProceduresListWithCount(
                list = listOf(
                    GuiaWithGuiaProcedures(
                        guia = guiasWithPerson[0],
                        guiaProcedures = proceduresWithPerson.filter { it.guiaId == guiasWithPerson[0].id }
                    ),
                    GuiaWithGuiaProcedures(
                        guia = guiasWithPerson[1],
                        guiaProcedures = proceduresWithPerson.filter { it.guiaId == guiasWithPerson[1].id }
                    )
                ),
                count = 2
            )

            coEvery {
                tissBatchService.findByInvoiceId(invoiceId)
            } returns listOf(tissBatch).success()

            coEvery {
                guiaService.findByTissBatchIdsWithFiltersWithCount(
                    listOf(tissBatch.id),
                    filters,
                    IntRange(0, 9999)
                )
            } returns GuiasListWithCount(
                list = guiasWithPerson,
                count = 2
            )

            coEvery {
                guiaProcedureService.findByGuiaIds(guiasWithPerson.map { it.id })
            } returns proceduresWithPerson

            coEvery {
                personService.findByIds(
                    listOf(person.id.toString())
                )
            } returns listOf(person)

            val result = service.getGuiasByInvoiceId(invoiceId, filters, IntRange(0, 9))

            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce {
                tissBatchService.findByInvoiceId(invoiceId)
                guiaService.findByTissBatchIdsWithFiltersWithCount(
                    listOf(tissBatch.id),
                    filters,
                    IntRange(0, 9999)
                )
                guiaProcedureService.findByGuiaIds(any())
                personService.findByIds(any())
            }
        }

    @Test
    fun `#getGuiasByInvoiceId should get guias by invoice id paginated with search term filter`() =
        runBlocking<Unit> {
            val invoiceId = RangeUUID.generate()
            val tissBatch = TestModelFactory.buildTissBatch(invoiceId = invoiceId)
            val filters = GuiaFilters(
                executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT,
                searchTerm = "Jóse"
            )

            val expectedResponse = GuiaWithGuiaProceduresListWithCount(
                list = listOf(
                    GuiaWithGuiaProcedures(
                        guia = guiasWithPerson[0],
                        guiaProcedures = proceduresWithPerson.filter { it.guiaId == guiasWithPerson[0].id }
                    ),
                    GuiaWithGuiaProcedures(
                        guia = guiasWithPerson[1],
                        guiaProcedures = proceduresWithPerson.filter { it.guiaId == guiasWithPerson[1].id }
                    )
                ),
                count = 2
            )

            coEvery {
                tissBatchService.findByInvoiceId(invoiceId)
            } returns listOf(tissBatch)

            coEvery {
                guiaService.findByTissBatchIdsWithFiltersWithCount(
                    listOf(tissBatch.id),
                    filters,
                    IntRange(0, 9999)
                )
            } returns GuiasListWithCount(
                list = guiasWithPerson,
                count = 2
            )

            coEvery {
                guiaProcedureService.findByGuiaIds(guiasWithPerson.map { it.id })
            } returns proceduresWithPerson

            coEvery {
                personService.findByIds(
                    listOf(person.id.toString())
                )
            } returns listOf(person)

            val result = service.getGuiasByInvoiceId(invoiceId, filters, IntRange(0, 9))

            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce {
                tissBatchService.findByInvoiceId(invoiceId)
                guiaService.findByTissBatchIdsWithFiltersWithCount(
                    listOf(tissBatch.id),
                    filters,
                    IntRange(0, 9999)
                )
                guiaProcedureService.findByGuiaIds(any())
                personService.findByIds(any())
            }
        }

    @Test
    fun `guiaWithProceduresComparator should sort by MEMBER_NAME in ascending order`() {
        val guia1 = GuiaWithGuiaProcedures(
            guia = TestModelFactory.buildTissGuia().copy(
                memberName = "Alice",
                requestedAt = LocalDate.now(),
                createdAt = LocalDateTime.now()
            ),
            guiaProcedures = emptyList()
        )
        val guia2 = GuiaWithGuiaProcedures(
            guia = TestModelFactory.buildTissGuia().copy(
                memberName = "Bob",
                requestedAt = LocalDate.now(),
                createdAt = LocalDateTime.now()
            ),
            guiaProcedures = emptyList()
        )
        val guias = listOf(guia2, guia1)

        val comparator = service.guiaWithProceduresComparator(
            sortingKey = AppointmentBillsSortingKey.MEMBER_NAME,
            sortingOrder = SortingOrder.ASC
        )

        val sortedGuias = guias.sortedWith(comparator)

        assertEquals(listOf(guia1, guia2), sortedGuias)
    }

    @Test
    fun `guiaWithProceduresComparator should sort by DATE in descending order`() {
        val guia1 = GuiaWithGuiaProcedures(
            guia = TestModelFactory.buildTissGuia().copy(
                requestedAt = LocalDate.now().minusDays(1),
                createdAt = LocalDateTime.now()
            ),
            guiaProcedures = emptyList()
        )
        val guia2 = GuiaWithGuiaProcedures(
            guia = TestModelFactory.buildTissGuia().copy(
                requestedAt = LocalDate.now(),
                createdAt = LocalDateTime.now()
            ),
            guiaProcedures = emptyList()
        )
        val guias = listOf(guia1, guia2)

        val comparator = service.guiaWithProceduresComparator(
            sortingKey = AppointmentBillsSortingKey.DATE,
            sortingOrder = SortingOrder.DESC
        )

        val sortedGuias = guias.sortedWith(comparator)

        assertEquals(listOf(guia2, guia1), sortedGuias)
    }
}
