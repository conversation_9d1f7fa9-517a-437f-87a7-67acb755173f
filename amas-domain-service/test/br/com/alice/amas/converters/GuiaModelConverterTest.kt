package br.com.alice.amas.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.RangeUUID.PERSON_ID_RANGE
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.toPersonId
import br.com.alice.data.layer.models.Guia
import br.com.alice.data.layer.models.GuiaModel
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.TierType
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class GuiaModelConverterTest {
    private val guia = Guia(
        id = RangeUUID.generate(),
        version = 1,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        personId = PersonId(),
        memberName = "Member Name",
        number = "12345678",
        memberNewBorn = "N",
        memberCode = "Member Code",
        memberTier = TierType.TIER_1,
        professionalRequesterName = "Requester Name",
        professionalRequesterCouncilNumber = "12345",
        professionalRequesterCouncil = "Council",
        professionalRequesterCbos = "CBOS",
        professionalRequesterCouncilState = "State",
        professionalExecutorName = "Executor Name",
        professionalExecutorCouncil = "Council",
        professionalExecutorCouncilNumber = "67890",
        professionalExecutorCbos = "CBOS",
        professionalExecutorCouncilState = "State",
        providerRequesterCode = "Provider Code",
        providerRequesterName = "Provider Name",
        requestedAt = LocalDate.now(),
        attendanceType = "Type",
        providerExecutorCode = "Executor Code",
        providerExecutorName = "Executor Name",
        providerExecutorCnes = "CNES",
        attendanceCharacter = "Character",
        accidentIndication = "Indication",
        tissBatchNumber = "**********",
        valueProcedures = BigDecimal("100.00"),
        valueTaxRents = BigDecimal("10.00"),
        valueMaterials = BigDecimal("20.00"),
        valueMedicines = BigDecimal("30.00"),
        valueOpme = BigDecimal("40.00"),
        valueMedicinalGases = BigDecimal("50.00"),
        valueDaily = BigDecimal("60.00"),
        valueTotal = BigDecimal("310.00"),
        clinicIndication = "Indication",
        appointmentType = "Type",
        providerRequesterCnpj = "12345678000123",
        providerExecutorCnpj = "12345678000123",
        tissBatchId = UUID.randomUUID(),
        serviceTypes = listOf(),
        executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY,
        updatedBy = UpdatedBy(
            userType = "teste",
            userId = "teste",
            environmentName = "env"
        )
    )

    private val guiaModel = GuiaModel(
        id = guia.id,
        version = guia.version,
        createdAt = guia.createdAt,
        updatedAt = guia.updatedAt,
        personId = guia.personId,
        memberName = guia.memberName,
        number = guia.number,
        memberNewBorn = guia.memberNewBorn,
        memberCode = guia.memberCode,
        memberTier = guia.memberTier,
        professionalRequesterName = guia.professionalRequesterName,
        professionalRequesterCouncilNumber = guia.professionalRequesterCouncilNumber,
        professionalRequesterCouncil = guia.professionalRequesterCouncil,
        professionalRequesterCbos = guia.professionalRequesterCbos,
        professionalRequesterCouncilState = guia.professionalRequesterCouncilState,
        professionalExecutorName = guia.professionalExecutorName,
        professionalExecutorCouncil = guia.professionalExecutorCouncil,
        professionalExecutorCouncilNumber = guia.professionalExecutorCouncilNumber,
        professionalExecutorCbos = guia.professionalExecutorCbos,
        professionalExecutorCouncilState = guia.professionalExecutorCouncilState,
        providerRequesterCode = guia.providerRequesterCode,
        providerRequesterName = guia.providerRequesterName,
        requestedAt = guia.requestedAt,
        attendanceType = guia.attendanceType,
        providerExecutorCode = guia.providerExecutorCode,
        providerExecutorName = guia.providerExecutorName,
        providerExecutorCnes = guia.providerExecutorCnes,
        attendanceCharacter = guia.attendanceCharacter,
        accidentIndication = guia.accidentIndication,
        tissBatchNumber = guia.tissBatchNumber,
        valueProcedures = guia.valueProcedures,
        valueTaxRents = guia.valueTaxRents,
        valueMaterials = guia.valueMaterials,
        valueMedicines = guia.valueMedicines,
        valueOpme = guia.valueOpme,
        valueMedicinalGases = guia.valueMedicinalGases,
        valueDaily = guia.valueDaily,
        valueTotal = guia.valueTotal,
        clinicIndication = guia.clinicIndication,
        appointmentType = guia.appointmentType,
        providerRequesterCnpj = guia.providerRequesterCnpj,
        providerExecutorCnpj = guia.providerExecutorCnpj,
        tissBatchId = guia.tissBatchId,
        serviceTypes = guia.serviceTypes,
        executionEnvironment = guia.executionEnvironment,
        updatedBy = guia.updatedBy
    )

    @Test
    fun testToModel() {
        assertThat(guia.toModel())
            .isEqualTo(guiaModel)
    }

    @Test
    fun testToTransport() {
        assertThat(guiaModel.toTransport())
            .isEqualTo(guia)
    }
}
