package br.com.alice.amas.converters

import br.com.alice.data.layer.models.*
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class InvoiceModelConverterTest {
    private val invoice = Invoice(
        id = UUID.randomUUID(),
        version = 1,
        code = "INV123456",
        execIndicatorAuthorizerId = UUID.randomUUID(),
        providerUnitId = UUID.randomUUID(),
        userEmail = "<EMAIL>",
        status = TissInvoiceStatus.DRAFT,
        type = InvoiceType.HEALTH_INSTITUTION,
        expenseType = InvoiceExpenseType.PROCEDURES,
        automaticGenerated = false,
        referenceDate = LocalDate.now(),
        staffId = UUID.randomUUID(),
        searchTokens = null,
        bonusPercent = 10,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    private val invoiceModel = InvoiceModel(
        id = invoice.id,
        version = invoice.version,
        code = invoice.code,
        execIndicatorAuthorizerId = invoice.execIndicatorAuthorizerId,
        providerUnitId = invoice.providerUnitId,
        userEmail = invoice.userEmail,
        status = invoice.status,
        type = invoice.type,
        expenseType = invoice.expenseType,
        automaticGenerated = invoice.automaticGenerated,
        referenceDate = invoice.referenceDate,
        staffId = invoice.staffId,
        searchTokens = invoice.searchTokens,
        bonusPercent = invoice.bonusPercent,
        createdAt = invoice.createdAt,
        updatedAt = invoice.updatedAt
    )

    @Test
    fun testToModel() {
        assertThat(invoice.toModel())
            .isEqualTo(invoiceModel)
    }

    @Test
    fun testToTransport() {
        assertThat(invoiceModel.toTransport())
            .isEqualTo(invoice)
    }
}
