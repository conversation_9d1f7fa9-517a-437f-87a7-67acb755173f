package br.com.alice.amas.consumers

import br.com.alice.amas.services.internal.HealthProfessionalTierHistoryService
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import com.github.kittinunf.result.success

class HealthProfessionalChangedConsumer(
    private val healthProfessionalTierHistoryService: HealthProfessionalTierHistoryService
): Consumer() {

    suspend fun createTierHistory(event: HealthProfessionalChangedEvent) = withSubscribersEnvironment {
        logger.info(
            "Received event HealthProfessionalChangedEvent to process",
            "staffId" to event.payload.healthProfessional.staffId,
            "action" to event.eventAction
        )

        val healthProfessional = event.payload.healthProfessional

        if (healthProfessional.status == SpecialistStatus.INACTIVE) {
            logger.info(
                "HealthProfessional is inactive, skipping event processing",
                "staffId" to healthProfessional.staffId
            )
            return@withSubscribersEnvironment true.success()
        }

        healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(healthProfessional)
            .then {
                logger.info("HealthProfessionalChangedEvent processed successfully")
            }.thenError {
                logger.error(
                    "Error processing HealthProfessionalChangedEvent",
                    "staffId" to event.payload.healthProfessional.staffId,
                    "message" to it.message,
                    it
                )
            }
    }
}
