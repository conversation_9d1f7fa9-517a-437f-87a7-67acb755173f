package br.com.alice.amas.services

import br.com.alice.amas.client.GuiaProcedureService
import br.com.alice.amas.client.GuiaService
import br.com.alice.amas.client.InvoiceCritiqueService
import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.client.TissGuiaExpenseService
import br.com.alice.amas.converters.toModel
import br.com.alice.amas.converters.toTransport
import br.com.alice.amas.responses.TissGuiaExpenseResponse
import br.com.alice.amas.responses.TissGuiaProcedureResponse
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.InvoiceCritique
import br.com.alice.data.layer.models.InvoiceCritiqueProcedure
import br.com.alice.data.layer.models.InvoiceCritiqueReason
import br.com.alice.data.layer.models.InvoiceCritiqueStatus
import br.com.alice.data.layer.services.InvoiceCritiqueModelDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class InvoiceCritiqueServiceImpl(
    private val dataService: InvoiceCritiqueModelDataService,
    private val guiaService: GuiaService,
    private val invoiceService: InvoiceService,
    private val guiaProcedureService: GuiaProcedureService,
    private val guiaExpenseService: TissGuiaExpenseService
) : InvoiceCritiqueService {

    override suspend fun listByInvoiceId(invoiceId: UUID) = useReadDatabase {
        dataService.find { where { this.invoiceId.eq(invoiceId) } }.mapEach { it.toTransport() }
    }

    override suspend fun listByGuiaId(guiaId: UUID) = useReadDatabase {
        dataService.find { where { this.guiaId.eq(guiaId) } }.mapEach { it.toTransport() }
    }

    override suspend fun create(
        invoiceId: UUID,
        guiaId: UUID,
        reason: InvoiceCritiqueReason,
        description: String
    ) = coroutineScope {
        val invoiceDef = async { invoiceService.get(invoiceId) }
        val guiaDef = async { guiaService.get(guiaId) }
        val proceduresDef = async { guiaProcedureService.findGuiaProceduresByGuiaId(guiaId) }
        val expensesDef = async { guiaExpenseService.findTissGuiaExpensesByGuiaId(guiaId) }
        val hasCritiquesDef = async { isGuideAlreadyCritiqued(guiaId) }

        coResultOf<InvoiceCritique, Throwable> {

            val guia = guiaDef.await().get()
            val invoice = invoiceDef.await().get()
            val procedures = proceduresDef.await().get()
            val expenses = expensesDef.await().get()
            val hasCritiques = hasCritiquesDef.await().get()

            if (!invoice.canReceiveCritique()) throw IllegalStateException("Invalid invoice status")
            if (hasCritiques) throw IllegalStateException("This guia already has a critique")

            InvoiceCritique(
                invoiceId = invoice.id,
                guiaId = guia.id,
                personId = guia.personId,
                reason = reason,
                description = description,
                status = InvoiceCritiqueStatus.PENDING,
                procedures = critiqueProcedures(procedures, expenses)
            )
        }.flatMap { dataService.add(it.toModel()) }.map { it.toTransport() }
    }

    override suspend fun accept(id: UUID, answer: String) =
        dataService
            .get(id)
            .map { it.accept(answer) }
            .flatMap { dataService.update(it) }.map { it.toTransport() }

    override suspend fun reject(id: UUID, answer: String) =
        dataService
            .get(id)
            .map { it.reject(answer) }
            .flatMap { dataService.update(it) }.map { it.toTransport() }

    override suspend fun findByInvoiceIds(invoiceIds: List<UUID>): Result<List<InvoiceCritique>, Throwable> {
        if (invoiceIds.isEmpty()) return Result.success(emptyList())

        return useReadDatabase {
            dataService.find { where { this.invoiceId.inList(invoiceIds) } }.mapEach { it.toTransport() }
        }
    }

    override suspend fun deleteByInvoiceId(invoiceId: UUID) =
        dataService
            .find { where { this.invoiceId.eq(invoiceId) } }.mapEach { it.toTransport() }
            .flatMap { deleteInvoiceCritiques(it) }

    private suspend fun deleteInvoiceCritiques(critiques: List<InvoiceCritique>) =
        coResultOf<List<Boolean>, Throwable> { critiques.map { dataService.delete(it.toModel()).get() } }

    private suspend fun isGuideAlreadyCritiqued(guiaId: UUID) = useReadDatabase {
        dataService
            .count { where { this.guiaId.eq(guiaId) } }
            .map { it > 0 }
    }

    private fun critiqueProcedures(
        procedures: List<TissGuiaProcedureResponse>,
        expenses: List<TissGuiaExpenseResponse>
    ) = procedures.map { procedure ->
        InvoiceCritiqueProcedure(
            code = procedure.code,
            description = procedure.description
        )
    }.plus(
        expenses.map { expense ->
            InvoiceCritiqueProcedure(
                code = expense.code,
                description = expense.description
            )
        }
    )
}
