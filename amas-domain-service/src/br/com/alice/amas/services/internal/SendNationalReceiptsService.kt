package br.com.alice.amas.services.internal

import br.com.alice.amas.ServiceConfig
import br.com.alice.amas.client.ArquiveiService
import br.com.alice.amas.client.NationalReceiptService
import br.com.alice.amas.client.TotvsNfsService
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.NationalReceipt
import br.com.alice.data.layer.models.NationalReceiptStatus
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.success
import io.ktor.http.BadContentTypeFormatException
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime

class SendNationalReceiptsService(
    private val totvsNfsService: TotvsNfsService,
    private val arquiveiService: ArquiveiService,
    private val driveService: GoogleDriveService,
    private val nationalReceiptService: NationalReceiptService,
    private val fileVaultActionService: FileVaultActionService
) {

    private val googleDriveFolderId = ServiceConfig.GoogleDrive.folderId

    suspend fun sendToTotvs(nationalReceipts: List<NationalReceipt>): Result<Any, Throwable> =
        try {
            totvsNfsService.totvsApprove(
                nationalReceipts.map(NationalReceipt::number),
                nationalReceipts.first().issuerCnpj
            )
                .flatMap {
                    val authorized = nationalReceipts.map { it.copy(status = NationalReceiptStatus.AUTHORIZED) }
                    nationalReceiptService.updateList(authorized)
                }


        } catch (e: BadContentTypeFormatException) {
            logger.error(
                "Calling Totvs to approve national receipt",
                "national_receipt" to nationalReceipts,
                e
            )
            val notSentNfs = nationalReceipts.map { it.copy(status = NationalReceiptStatus.NOT_SENT) }

            nationalReceiptService.updateList(notSentNfs)
                .flatMap { e.failure() }
        }

    suspend fun sendToDrive(nationalReceipts: List<NationalReceipt>): Result<Any, Throwable> {
        val arquiveiUploads = nationalReceipts
            .filter { it.linkDownloadPdf != null }
            .pmap { uploadArquiveiNationalReceipt(it) }

        val manuallyUploads = nationalReceipts
            .filter { it.publicDocumentFileVaultId != null }
            .pmap { uploadFileVaultNationalReceipt(it) }

        val manuallyUploadWithError = manuallyUploads.find { it.isFailure() }
        val arquiveiUploadWithError = arquiveiUploads.find { it.isFailure() }

        return arquiveiUploadWithError ?: manuallyUploadWithError ?: true.success()
    }

    private suspend fun uploadArquiveiNationalReceipt(receipt: NationalReceipt) =
        arquiveiService.downloadDanfeByAccessKey(receipt.linkDownloadPdf!!)
            .flatMap { uploadToDrive(receipt = receipt, byteArray = it, fileType = FileType.PDF) }

    private suspend fun uploadFileVaultNationalReceipt(receipt: NationalReceipt) =
        coroutineScope {
            val byteArrayDeferred = async {
                fileVaultActionService.genericFileContentById(receipt.publicDocumentFileVaultId!!)
            }
            val fileDeferred = async {
                fileVaultActionService.securedGenericLink(receipt.publicDocumentFileVaultId!!)
            }

            val file = fileDeferred.await().get()
            val byteArray = byteArrayDeferred.await().get()

            FileType.fromExtension(file.type)
                ?.let { uploadToDrive(receipt = receipt, byteArray = byteArray, fileType = it) }
                ?: InvalidArgumentException("File type not supported in Google Drive", "file_type_not_supported").failure()
        }

    private suspend fun uploadToDrive(receipt: NationalReceipt, byteArray: ByteArray, fileType: FileType) =
        driveService.upload(
            fileName = "${receipt.issuerCnpj}_${receipt.number}_${LocalDateTime.now()}_${receipt.expenseType}.${fileType.extension}",
            mimeType = fileType.contentType,
            folderId = googleDriveFolderId,
            content = byteArray
        ).then {
            logger.info(
                "National receipt uploaded successfully",
                "national_receipt" to receipt.id,
                "file_id" to it
            )
        }.thenError {
            logger.error(
                "Error during download and upload national receipt to drive",
                "national_receipt" to receipt.id,
                it
            )
        }
}


