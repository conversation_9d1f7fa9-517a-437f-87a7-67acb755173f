package br.com.alice.amas.services.internal

import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.exceptions.ProviderUnitWithClinicalStaffNotFound
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isSameMonth
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Invoice
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.data.layer.models.InvoiceType
import br.com.alice.data.layer.models.TissInvoiceStatus
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID

class InvoiceSelectionService(
    private val invoiceService: InvoiceService,
    private val appointmentService: AppointmentService,
    private val healthProfessionalService: HealthProfessionalService,
    private val providerUnitService: ProviderUnitService
) {

    companion object {
        private const val MAX_DAY_TO_RETRIEVE_INVOICE = 1
    }

    suspend fun getByHealthProfessionalStaffIdOrCreate(
        healthProfessionalStaffId: UUID,
        expenseType: InvoiceExpenseType,
        automaticGenerated: Boolean,
    ): Result<Invoice, Throwable> {
        logger.info(
            "InvoiceSelectionService::selectInvoice - Processing with healthProfessionalStaffId",
            "healthProfessionalStaffId" to healthProfessionalStaffId
        )

        return invoiceService.findByExpenseTypeStaffIdAutomaticGeneratedAndStatus(
            staffId = healthProfessionalStaffId,
            expenseType = expenseType,
            automaticGenerated = automaticGenerated,
            status = TissInvoiceStatus.DRAFT
        ).flatMap { draftInvoices ->
            logger.info(
                "InvoiceSelectionService::handleInternalUserInvoiceSelection - Fetched invoices",
                "staffId" to healthProfessionalStaffId,
                "expenseType" to expenseType,
                "automaticGenerated" to automaticGenerated,
                "invoiceCount" to draftInvoices.size
            )

            draftInvoices.maxByOrNull { it.createdAt }?.success()?.then {
                logger.info(
                    "InvoiceSelectionService::handleInternalUserInvoiceSelection - Found draft invoice",
                    "invoiceId" to it.id
                )
            } ?: createInvoice(healthProfessionalStaffId, expenseType, automaticGenerated)
        }
    }


    suspend fun getByAppointmentIdOrCreate(
        appointmentId: UUID,
        expenseType: InvoiceExpenseType,
        automaticGenerated: Boolean
    ): Result<Invoice, Throwable> {
        logger.info(
            "InvoiceSelectionService::getByAppointmentIdOrCreate - Processing with appointmentId",
            "appointmentId" to appointmentId
        )

        return appointmentService.get(appointmentId)
            .flatMapPair { appointment ->
                logger.info(
                    "InvoiceSelectionService::handleAppointmentInvoiceSelection - Fetched appointment",
                    "appointmentId" to appointment.id
                )

                invoiceService.findLastThreeMonthsByExpenseTypeStaffIdAutomaticGenerated(
                    staffId = appointment.staffId,
                    expenseType = expenseType,
                    automaticGenerated = automaticGenerated
                )
            }.flatMap { (invoices, appointment) ->
                logger.info(
                    "InvoiceSelectionService::handleAppointmentInvoiceSelection - Fetched invoices",
                    "staffId" to appointment.staffId,
                    "expenseType" to expenseType,
                    "automaticGenerated" to automaticGenerated,
                    "invoiceCount" to invoices.size
                )

                val referenceDate = determineReferenceDate(appointment)

                val sameMonthInvoices = invoices.filter { it.referenceDate?.isSameMonth(referenceDate) == true }
                sameMonthInvoices.maxByOrNull { it.createdAt }?.success()?.then {
                    logger.info(
                        "InvoiceSelectionService::handleAppointmentInvoiceSelection - Found invoice for reference date",
                        "invoiceId" to it.id,
                        "referenceDate" to referenceDate
                    )
                } ?: createInvoice(appointment.staffId, expenseType, automaticGenerated, referenceDate)
            }
    }

    private fun determineReferenceDate(appointment: Appointment): LocalDate {
        val localDate = LocalDate.now()

        // If the event is processed in the first of the month, we check if the appointment was in the previous month
        if (localDate.dayOfMonth == MAX_DAY_TO_RETRIEVE_INVOICE) {
            val previousMonth = localDate.minusMonths(1)

            if (appointment.appointmentDate?.isSameMonth(previousMonth) == true) return previousMonth
        }
        return localDate
    }

    private suspend fun createInvoice(
        staffId: UUID,
        expenseType: InvoiceExpenseType,
        automaticGenerated: Boolean,
        referenceDate: LocalDate = LocalDate.now()
    ) = healthProfessionalService.findByStaffId(staffId)
        .flatMapPair { providerUnitService.getByClinicalStaffId(staffId) }
        .flatMap { (providerUnit, healthProfessional) ->
            if (providerUnit.isEmpty()) {
                return@flatMap ProviderUnitWithClinicalStaffNotFound().failure()
            }
            invoiceService.add(
                Invoice(
                    staffId = staffId,
                    userEmail = healthProfessional.email,
                    providerUnitId = providerUnit.first().id,
                    type = InvoiceType.HEALTH_SPECIALIST,
                    status = TissInvoiceStatus.DRAFT,
                    expenseType = expenseType,
                    automaticGenerated = automaticGenerated,
                    referenceDate = if (automaticGenerated) referenceDate else referenceDate.minusMonths(1),
                )
            )
        }.then {
            logger.info(
                "InvoiceSelectionService::createInvoice - Created invoice",
                "invoiceId" to it.id,
                "staffId" to staffId,
                "expenseType" to expenseType,
                "automaticGenerated" to automaticGenerated,
                "referenceDate" to referenceDate
            )
        }.thenError {
            if (it is ProviderUnitWithClinicalStaffNotFound) {
                logger.error(
                    "InvoiceSelectionService::createInvoice - Error creating invoice - cannot find provider unit",
                    "staffId" to staffId,
                    "expenseType" to expenseType,
                    "automaticGenerated" to automaticGenerated,
                    "referenceDate" to referenceDate
                )
            } else {
                logger.error(
                    "InvoiceSelectionService::createInvoice - Error creating invoice",
                    "staffId" to staffId,
                    "expenseType" to expenseType,
                    "automaticGenerated" to automaticGenerated,
                    "referenceDate" to referenceDate,
                    "exceptionMessage" to it.message
                )
            }
        }

}
