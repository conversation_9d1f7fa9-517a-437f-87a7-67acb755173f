
ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }

    application {
        modules = [br.com.alice.amas.ApplicationKt.module]
    }

    arquivei{
        apiId = "d87b26e5b77d616d9c1411153a3acbba97acc9b0"
        apiKey = "8132bbbddf0afd637e9c484aee9c6e4c28461fb1"
        baseUrl = "https://api.arquivei.com.br"
    }

}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    totvs {
        baseUrl = ${?TOTVS_BASE_URL}
        basicAuthToken = ${?TOTVS_AUTH_TOKEN}
    }

    ftpInvoiceTotvs{
        host = ${?TOTVS_INVOICE_HOST}
        port = ${?TOTVS_INVOICE_PORT}
        user = ${?TOTVS_INVOICE_USER}
        pass = ${?TOTVS_INVOICE_PASS}
    }

    googleDrive {
        folderId = ${?GOOGLE_DRIVE_NF_ARQUIVEI_EITA_FOLDER_ID}
        credentials = ${?GOOGLE_SA_CREDENTIALS}
    }

    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }

    transmite {
        email = "<EMAIL>"
    }
}

test {
    totvs {
        baseUrl = "https://totvs_url"
        basicAuthToken = "password"
    }

    ftpInvoiceTotvs{
        host = "alicetecnologia125516.protheus.cloudtotvs.com.br"
        port = "2323"
        user = "ftp_C68ICK_development"
        pass = "4wV7x0Nj"
    }

    googleDrive {
        folderId = "folderId"
        credentials = "{}"
    }

    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }

    transmite {
        email = "<EMAIL>"
    }
}

production {
    totvs {
        baseUrl = ${?TOTVS_BASE_URL}
        basicAuthToken = ${?TOTVS_AUTH_TOKEN}
    }

    ftpInvoiceTotvs{
        host = ${?TOTVS_INVOICE_HOST}
        port = ${?TOTVS_INVOICE_PORT}
        user = ${?TOTVS_INVOICE_USER}
        pass = ${?TOTVS_INVOICE_PASS}
    }

    googleDrive {
        folderId = ${?GOOGLE_DRIVE_NF_ARQUIVEI_EITA_FOLDER_ID}
        credentials = ${?GOOGLE_SA_CREDENTIALS}
    }
    mailer {
        senderName = "Alice"
        senderName = ${?DEFAULT_EMAIL_SENDER_NAME}

        senderEmail = "<EMAIL>"
        senderEmail = ${?DEFAULT_EMAIL_SENDER_ADDRESS}
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
        campaignId = ${?MAIL_PINPOINT_CAMPAIGN_ID}
    }

    transmite {
        email = ""
        email = ${?TRANSMITE_EMAIL}
    }
}
