package br.com.alice.hubspot.integration.lib.ioc

import br.com.alice.hubspot.integration.lib.clients.HubspotHttpClient
import io.ktor.client.HttpClient
import org.koin.dsl.module

val HubspotIntegrationLibModule = module(createdAtStart = true) {
    single { HttpClient() }
    single { HubspotHttpClient(get(), get()) }
//    single<HubspotClient> {
//        when (HubspotIntegrationLibConfig.environment()) {
//            RunningMode.PRODUCTION -> HubspotClientImpl(
//                HubspotIntegrationLibConfig.hubspotConfig(),
//                Apache.create {
//                    customizeClient {
//                        setMaxConnTotal(2000)
//                        setMaxConnPerRoute(200)
//                    }
//                })
//            RunningMode.DEVELOPMENT -> HubspotClientLocal()
//            RunningMode.TEST -> HubspotClientLocal()
//        }
//    }
}
