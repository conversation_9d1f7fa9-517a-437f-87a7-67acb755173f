plugins {
    kotlin
    id("org.sonarqube")
}

group = "br.com.alice.alice-hubspot-integration-lib"
version = aliceHubspotIntegrationLibVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:hubspot-integration-lib")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

dependencies {
    implementation("io.insert-koin:koin-ktor:$koin3Version")
    implementation(project(":common"))

    ktor2Dependencies()

    testImplementation(project(":common-tests"))
    test2Dependencies()
}
