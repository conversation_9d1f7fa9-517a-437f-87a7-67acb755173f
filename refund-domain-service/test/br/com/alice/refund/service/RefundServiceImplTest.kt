package br.com.alice.refund.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.RefundHealthEventType
import br.com.alice.data.layer.models.RefundStep
import br.com.alice.data.layer.services.RefundModelDataService
import br.com.alice.refund.BaseRefundTest
import br.com.alice.refund.converters.toModel
import br.com.alice.refund.models.RefundRequest
import br.com.alice.refund.notifier.RefundCreatedEvent
import br.com.alice.refund.notifier.RefundPayload
import br.com.alice.refund.notifier.RefundRequestedEvent
import br.com.alice.refund.service.step.RefundConfirmationStepServiceImpl
import br.com.alice.refund.service.step.RefundStepService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class RefundServiceImplTest: BaseRefundTest() {

    private val refundDataService: RefundModelDataService = mockk()
    private val refundConfirmationStepServiceImpl: RefundConfirmationStepServiceImpl = mockk()
    private val refundStepService: RefundStepService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = RefundServiceImpl(
        refundDataService,
        refundConfirmationStepServiceImpl,
        refundStepService,
        kafkaProducerService
    )

    private val healthEventType = RefundHealthEventType.APPOINTMENT

    private val person = TestModelFactory.buildPerson()

    private val refund = TestModelFactory.buildRefund(
        personId = person.id,
        healthEventType = healthEventType,
    )
    private val refundModel = refund.toModel()

    private val refunds = listOf(refund)
    private val refundModels = listOf(refundModel)

    private val refundRequest = RefundRequest(
        healthEventType = healthEventType,
        termsAccepted = true
    )

    private val financialDataId = RangeUUID.generate()

    private val refundResponse = this.getBasicRefundResponse(refund)

    private val offSet = 0
    private val limit = 2

    @Test
    fun `#get should get refund by id`() = runBlocking {
        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        val result = service.get(refund.id)

        ResultAssert.assertThat(result).isSuccessWithData(refund)

        coVerifyOnce { refundDataService.get(any()) }
        verify {
            refundConfirmationStepServiceImpl wasNot called
            refundStepService wasNot called
            kafkaProducerService wasNot called
        }
    }

    @Test
    fun `#get should fail when data service emits an error`() = runBlocking {

        coEvery {
            refundDataService.get(refund.id)
        } returns NotFoundException("not_found").failure()

        val result = service.get(refund.id)
        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { refundDataService.get(any()) }
        verify {
            refundConfirmationStepServiceImpl wasNot called
            refundStepService wasNot called
            kafkaProducerService wasNot called
        }
    }

    @Test
    fun `#findByPersonId should get refunds by person id paginated`() = runBlocking {
        coEvery {
            refundDataService.find(
                queryEq {
                    where {
                        this.personId.eq(person.id)
                    }
                        .orderBy { createdAt }
                        .sortOrder { SortOrder.Descending }
                        .offset { offSet }
                        .limit { limit }
                }
            )
        } returns refundModels.success()

        val result = service.findByPersonId(person.id, offSet, limit)

        ResultAssert.assertThat(result).isSuccessWithData(refunds)

        coVerifyOnce { refundDataService.find(any()) }
    }

    @Test
    fun `#getRefundRequested should get refund response for resume screen`() = runBlocking {
        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundConfirmationStepServiceImpl.get(refund, isResume = true)
        } returns refundResponse.success()

        val result = service.getRefundRequested(refund.id)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundConfirmationStepServiceImpl.get(any(), any()) }
    }

    @Test
    fun `#create should create refund`() = runBlocking {
        coEvery {
            refundDataService.add(
                match {
                    it == refundModel.copy(
                        id = it.id,
                        createdAt = it.createdAt,
                        updatedAt = it.updatedAt
                    )
                })
        } returns refundModel.success()

        coEvery {
            kafkaProducerService.produce(RefundCreatedEvent(RefundPayload(refund)))
        } returns mockk()

        coEvery {
            refundStepService.handle(refund)
        } returns refundResponse.success()

        val result = service.create(person.id, refundRequest)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.add(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#advanceStep should advance step to DOCUMENTS_ATTACHMENT`() = runBlocking {
        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundDataService.update(
                match {
                    it == refundModel.copy(
                        step = RefundStep.PAYMENT_INFO,
                        updatedAt = it.updatedAt
                    )
                })
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(refund)
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = refund.step
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#advanceStep should advance step to PAYMENT_INFO`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.DOCUMENTS_ATTACHMENT
        )
        val refundModel = refundModel.copy(
            step = RefundStep.DOCUMENTS_ATTACHMENT
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundDataService.update(
                match {
                    it == refundModel.copy(
                        step = RefundStep.PAYMENT_INFO,
                        updatedAt = it.updatedAt
                    )
                })
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(refund)
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = refund.step
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#advanceStep should advance step to CONFIRMATION`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.PAYMENT_INFO
        )
        val refundModel = refundModel.copy(
            step = RefundStep.PAYMENT_INFO
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundDataService.update(
                match {
                    it == refundModel.copy(
                        step = RefundStep.CONFIRMATION,
                        financialDataId = financialDataId,
                        updatedAt = it.updatedAt
                    )
                })
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(refund)
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = refund.step,
            financialDataId = financialDataId
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#advanceStep should advance step to REQUESTED`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.CONFIRMATION
        )
        val refundModel = refundModel.copy(
            step = RefundStep.CONFIRMATION
        )

        val updatedRefund = refund.copy(
            step = RefundStep.REQUESTED
        )
        val updatedRefundModel = refundModel.copy(
            step = RefundStep.REQUESTED
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundDataService.update(
                match {
                    it == refundModel.copy(
                        step = RefundStep.REQUESTED,
                        updatedAt = it.updatedAt
                    )
                })
        } returns updatedRefundModel.success()

        coEvery {
            kafkaProducerService.produce(RefundRequestedEvent(RefundPayload(updatedRefund)))
        } returns mockk()

        coEvery {
            refundStepService.handle(updatedRefund)
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = refund.step,
            financialDataId = financialDataId
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#advanceStep should not advance step because current step is before the current database step`() = runBlocking {
        val currentStep = RefundStep.DOCUMENTS_ATTACHMENT
        val refund = refund.copy(
            step = RefundStep.CONFIRMATION
        )
        val refundModel = refundModel.copy(
            step = RefundStep.CONFIRMATION
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(
                refund.copy(
                    step = RefundStep.PAYMENT_INFO
                )
            )
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = currentStep,
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyNone { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#advanceStep should not advance step but overwrite financial_data_id`() = runBlocking {
        val currentStep = RefundStep.PAYMENT_INFO
        val newFinancialDataId = RangeUUID.generate()
        val refund = refund.copy(
            step = RefundStep.CONFIRMATION,
            financialDataId = RangeUUID.generate()
        )
        val refundModel = refundModel.copy(
            step = RefundStep.CONFIRMATION,
            financialDataId = refund.financialDataId
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundDataService.update(
                match {
                    it == refundModel.copy(
                        financialDataId = newFinancialDataId,
                        updatedAt = it.updatedAt
                    )
                })
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(
                refund.copy(
                    step = RefundStep.CONFIRMATION
                )
            )
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = currentStep,
            financialDataId = newFinancialDataId
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#advanceStep should not advance step or overwrite financial_data_id`() = runBlocking {
        val currentStep = RefundStep.PAYMENT_INFO
        val newFinancialDataId = RangeUUID.generate()
        val refund = refund.copy(
            step = RefundStep.CONFIRMATION,
            financialDataId = newFinancialDataId
        )
        val refundModel = refundModel.copy(
            step = RefundStep.CONFIRMATION,
            financialDataId = newFinancialDataId
        )

        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(
                refund.copy(
                    step = RefundStep.CONFIRMATION
                )
            )
        } returns refundResponse.success()

        val result = service.advanceStep(
            refundId = refund.id,
            currentStep = currentStep,
            financialDataId = newFinancialDataId
        )

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyNone { refundDataService.update(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }

    @Test
    fun `#getIncompleteRefund should get refund`() = runBlocking {
        coEvery {
            refundDataService.get(refund.id)
        } returns refundModel.success()

        coEvery {
            refundStepService.handle(refund)
        } returns refundResponse.success()

        val result = service.getIncompleteRefund(refund.id)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { refundDataService.get(any()) }
        coVerifyOnce { refundStepService.handle(any()) }
    }
}
