package br.com.alice.refund.service.step

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FinancialData
import br.com.alice.data.layer.models.RefundHealthEventType
import br.com.alice.data.layer.models.RefundStep
import br.com.alice.moneyin.client.FinancialDataService
import br.com.alice.refund.BaseRefundTest
import br.com.alice.refund.models.RefundAccountHolders
import br.com.alice.refund.models.RefundResponse
import br.com.alice.refund.models.RefundScreenAction
import br.com.alice.refund.models.RefundScreenActionType
import br.com.alice.refund.models.RefundScreenInfo
import br.com.alice.refund.service.internal.AccountHolderService
import br.com.alice.refund.service.internal.PersonWithGuardian
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class RefundFinancialDataStepServiceImplTest : BaseRefundTest() {
    private val financialDataService: FinancialDataService = mockk()
    private val minorAccountHolderService: AccountHolderService = mockk()

    private val service = RefundFinancialDataStepServiceImpl(
        financialDataService,
        minorAccountHolderService
    )

    private val minorPerson = TestModelFactory.buildPerson(
        dateOfBirth = LocalDateTime.now().minusYears(3),
    )
    private val adultPerson = TestModelFactory.buildPerson(
        dateOfBirth = LocalDateTime.now().minusYears(35)
    )

    private val minorPersonFinancialData = TestModelFactory.buildFinancialData(
        personId = minorPerson.id,
    )

    private val adultPersonFinancialData = TestModelFactory.buildFinancialData(
        personId = minorPerson.id,
    )

    @Test
    fun `#get for an adult`() = runBlocking {
        val adultRefund = TestModelFactory.buildRefund(
            healthEventType = RefundHealthEventType.APPOINTMENT,
            personId = adultPerson.id,
        )

        coEvery {
            minorAccountHolderService.getAccountHolderPerson(adultPerson.id)
        } returns PersonWithGuardian(adultPerson).success()

        coEvery {
            financialDataService.findByPersonId(any())
        } returns listOf(adultPersonFinancialData).success()

        val result = service.get(refund = adultRefund)

        val expectedResult = RefundResponse(
            id = adultRefund.id,
            step = RefundStep.PAYMENT_INFO,
            screenInfo = RefundScreenInfo(
                title = "Cadastre uma conta bancária para receber o depósito",
                stepProgress = 60,
                primaryAction = RefundScreenAction(
                    label = "Avançar",
                    type = RefundScreenActionType.UPDATE_REFUND,
                    removePreviousSteps = false
                ),
            ),
            accountHolders = listOf(
                RefundAccountHolders(
                    id = adultPerson.id.toUUID(),
                    name = adultPerson.fullRegisterName,
                    details = "Titular",
                    nationalId = adultPerson.nationalId,
                    imageUrl = adultPerson.profilePicture?.url,
                    accounts = listOf(adultPersonFinancialData.toRefundAccount())
                )
            )
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce {
            minorAccountHolderService.getAccountHolderPerson(any())
        }
        coVerifyOnce {
            financialDataService.findByPersonId(adultPerson.id)
        }
    }

    @Test
    fun `#get for a minor should return legal guardian account holder too`() = runBlocking {
        val minorRefund = TestModelFactory.buildRefund(
            healthEventType = RefundHealthEventType.APPOINTMENT,
            personId = minorPerson.id,
        )

        coEvery {
            minorAccountHolderService.getAccountHolderPerson(minorPerson.id)
        } returns PersonWithGuardian(minorPerson, adultPerson).success()

        coEvery {
            financialDataService.findByPersonId(adultPerson.id)
        } returns listOf(adultPersonFinancialData).success()

        coEvery {
            financialDataService.findByPersonId(minorPerson.id)
        } returns listOf(minorPersonFinancialData).success()

        val result = service.get(refund = minorRefund)

        val expectedResult = RefundResponse(
            id = minorRefund.id,
            step = RefundStep.PAYMENT_INFO,
            screenInfo = RefundScreenInfo(
                title = "Cadastre uma conta bancária para receber o depósito",
                stepProgress = 60,
                primaryAction = RefundScreenAction(
                    label = "Avançar",
                    type = RefundScreenActionType.UPDATE_REFUND,
                    removePreviousSteps = false
                ),
            ),
            accountHolders = listOf(
                RefundAccountHolders(
                    id = minorPerson.id.toUUID(),
                    name = minorPerson.fullRegisterName,
                    details = "Titular",
                    nationalId = minorPerson.nationalId,
                    imageUrl = minorPerson.profilePicture?.url,
                    accounts = listOf(minorPersonFinancialData.toRefundAccount())
                ),
                RefundAccountHolders(
                    id = adultPerson.id.toUUID(),
                    name = adultPerson.fullRegisterName,
                    details = "Responsável Legal",
                    nationalId = adultPerson.nationalId,
                    imageUrl = adultPerson.profilePicture?.url,
                    accounts = listOf(adultPersonFinancialData.toRefundAccount())
                )
            )
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce {
            minorAccountHolderService.getAccountHolderPerson(any())
        }
        coVerify(exactly = 2) { financialDataService.findByPersonId(any()) }
        coVerifyOnce {
            financialDataService.findByPersonId(adultPerson.id)
        }
        coVerifyOnce {
            financialDataService.findByPersonId(minorPerson.id)
        }
    }

    @Test
    fun `#get without FinancialData`() = runBlocking {
        val adultRefund = TestModelFactory.buildRefund(
            healthEventType = RefundHealthEventType.APPOINTMENT,
            personId = adultPerson.id,
        )

        coEvery {
            minorAccountHolderService.getAccountHolderPerson(adultPerson.id)
        } returns PersonWithGuardian(adultPerson).success()

        coEvery {
            financialDataService.findByPersonId(any())
        } returns listOf<FinancialData>().success()

        val result = service.get(refund = adultRefund)

        val expectedResult = RefundResponse(
            id = adultRefund.id,
            step = RefundStep.PAYMENT_INFO,
            screenInfo = RefundScreenInfo(
                title = "Cadastre uma conta bancária para receber o depósito",
                stepProgress = 60,
                primaryAction = RefundScreenAction(
                    label = "Avançar",
                    type = RefundScreenActionType.UPDATE_REFUND,
                    removePreviousSteps = false
                ),
            ),
            accountHolders = listOf(
                RefundAccountHolders(
                    id = adultPerson.id.toUUID(),
                    name = adultPerson.fullRegisterName,
                    details = "Titular",
                    nationalId = adultPerson.nationalId,
                    imageUrl = adultPerson.profilePicture?.url,
                    accounts = listOf()
                )
            )
        )

        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce {
            minorAccountHolderService.getAccountHolderPerson(any())
        }
        coVerifyOnce {
            financialDataService.findByPersonId(adultPerson.id)
        }
    }

}
