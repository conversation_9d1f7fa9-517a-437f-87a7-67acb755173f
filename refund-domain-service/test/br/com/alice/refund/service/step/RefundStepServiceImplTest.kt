package br.com.alice.refund.service.step

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.RefundHealthEventType
import br.com.alice.data.layer.models.RefundStep
import br.com.alice.refund.BaseRefundTest
import br.com.alice.refund.service.step.RefundConfirmationStepServiceImpl
import br.com.alice.refund.service.step.RefundDocumentsStepServiceImpl
import br.com.alice.refund.service.step.RefundFinancialDataStepServiceImpl
import br.com.alice.refund.service.step.RefundRequestedStepServiceImpl
import br.com.alice.refund.service.step.RefundSelfieStepServiceImpl
import br.com.alice.refund.service.step.RefundStepServiceImpl
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class RefundStepServiceTest: BaseRefundTest() {

    private val selfieStepService: RefundSelfieStepServiceImpl = mockk()
    private val documentsStepService: RefundDocumentsStepServiceImpl = mockk()
    private val financialDataStepService: RefundFinancialDataStepServiceImpl = mockk()
    private val confirmationStepService: RefundConfirmationStepServiceImpl = mockk()
    private val refundRequestedStepService: RefundRequestedStepServiceImpl = mockk()
    private val service = RefundStepServiceImpl(
        selfieStepService,
        documentsStepService,
        financialDataStepService,
        confirmationStepService,
        refundRequestedStepService
    )

    private val person = TestModelFactory.buildPerson()

    private val refund = TestModelFactory.buildRefund(
        healthEventType = RefundHealthEventType.APPOINTMENT,
        personId = person.id,
    )

    private val refundResponse = getBasicRefundResponse(refund)

    @AfterTest
    fun confirmMocks() = confirmVerified(
        selfieStepService,
        documentsStepService,
    )

    @Test
    fun `#handle should handle refund in SELFIE_VERIFICATION step`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.SELFIE_VERIFICATION
        )

        coEvery {
            selfieStepService.get(refund)
        } returns refundResponse.success()

        val result = service.handle(refund)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerifyOnce { selfieStepService.get(any()) }
        coVerify { documentsStepService wasNot called }
        coVerify { financialDataStepService wasNot called }
        coVerify { confirmationStepService wasNot called }
        coVerify { refundRequestedStepService wasNot called }
    }

    @Test
    fun `#handle should handle refund in DOCUMENTS_ATTACHMENT step`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.DOCUMENTS_ATTACHMENT
        )

        coEvery {
            documentsStepService.get(refund)
        } returns refundResponse.success()

        val result = service.handle(refund)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerify { selfieStepService wasNot called }
        coVerifyOnce { documentsStepService.get(any()) }
        coVerify { financialDataStepService wasNot called }
        coVerify { confirmationStepService wasNot called }
        coVerify { refundRequestedStepService wasNot called }
    }

    @Test
    fun `#handle should handle refund in PAYMENT_INFO step`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.PAYMENT_INFO
        )

        coEvery {
            financialDataStepService.get(refund)
        } returns refundResponse.success()

        val result = service.handle(refund)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerify { selfieStepService wasNot called }
        coVerify { documentsStepService wasNot called }
        coVerifyOnce { financialDataStepService.get(any()) }
        coVerify { confirmationStepService wasNot called }
        coVerify { refundRequestedStepService wasNot called }
    }

    @Test
    fun `#handle should handle refund in CONFIRMATION step`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.CONFIRMATION
        )

        coEvery {
            confirmationStepService.get(refund)
        } returns refundResponse.success()

        val result = service.handle(refund)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerify { selfieStepService wasNot called }
        coVerify { documentsStepService wasNot called }
        coVerify { financialDataStepService wasNot called }
        coVerifyOnce { confirmationStepService.get(any()) }
        coVerify { refundRequestedStepService wasNot called }
    }

    @Test
    fun `#handle should handle refund in REQUESTED step`() = runBlocking {
        val refund = refund.copy(
            step = RefundStep.REQUESTED
        )

        coEvery {
            refundRequestedStepService.get(refund)
        } returns refundResponse.success()

        val result = service.handle(refund)

        ResultAssert.assertThat(result).isSuccessWithData(refundResponse)

        coVerify { selfieStepService wasNot called }
        coVerify { documentsStepService wasNot called }
        coVerify { financialDataStepService wasNot called }
        coVerify { confirmationStepService wasNot called }
        coVerifyOnce { refundRequestedStepService.get(any()) }
    }
}
