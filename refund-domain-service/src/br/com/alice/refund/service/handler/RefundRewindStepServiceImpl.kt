package br.com.alice.refund.service.handler

import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.RefundModel
import br.com.alice.data.layer.models.RefundStep
import br.com.alice.data.layer.models.RefundStep.DOCUMENTS_ATTACHMENT
import br.com.alice.data.layer.models.RefundStep.PAYMENT_INFO
import br.com.alice.data.layer.models.RefundStep.SELFIE_VERIFICATION
import br.com.alice.data.layer.services.RefundModelDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class RefundRewindStepServiceImpl(
    private val refundDataService: RefundModelDataService,
) {

    suspend fun rewindStep(
        refundId: UUID,
        deleteOperationType: DeleteOperationType
    ): Result<StepRewindResponse, Throwable> =
        refundDataService.get(refundId).flatMap { refund ->
            logger.info("RefundRewindStepServiceImpl trying to rewind step",
                "person_id" to refund.personId,
                "refund_id" to refund.id,
                "operation" to deleteOperationType
            )
            when (deleteOperationType) {
                DeleteOperationType.SELFIE -> rewindTo(refund, SELFIE_VERIFICATION)
                DeleteOperationType.REQUIRED_DOCUMENTS -> rewindTo(refund, DOCUMENTS_ATTACHMENT)
                DeleteOperationType.ACCOUNT -> rewindTo(refund, PAYMENT_INFO)
            }
        }

    private suspend fun rewindTo(
        refund: RefundModel,
        step: RefundStep
    ) =
        refundDataService.update(
            refund.copy(
                step = step
            )
        ).map {
            StepRewindResponse(rewind = true, step = step)
        }.then { response ->
            logger.info("RefundRewindStepServiceImpl rewind step",
                "person_id" to refund.personId,
                "refund_id" to refund.id,
                "step" to response.step
            )
        }
}

enum class DeleteOperationType{
    SELFIE,
    REQUIRED_DOCUMENTS,
    ACCOUNT
}

data class StepRewindResponse(
    val rewind: Boolean = false,
    val step: RefundStep? = null
)
