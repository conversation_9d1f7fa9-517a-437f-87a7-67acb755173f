package br.com.alice.refund

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.extensions.rfcRoutes
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.REFUND_DOMAIN_SERVICE_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.moneyin.ioc.MoneyInClientModule
import br.com.alice.onboarding.ioc.OnboardingClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.refund.ioc.DataLayerServiceModule
import br.com.alice.refund.ioc.RefundDomainClientModule
import br.com.alice.refund.ioc.ServiceModule
import br.com.alice.refund.routes.kafkaRoutes
import io.ktor.server.application.Application
import io.ktor.server.routing.routing
import org.koin.core.module.Module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        KafkaProducerModule,
        RefundDomainClientModule,
        ProductDomainClientModule,
        DataLayerServiceModule,
        OnboardingClientModule,
        PersonDomainClientModule,
        FeatureConfigDomainClientModule,
        BusinessDomainClientModule,
        MoneyInClientModule,
        ServiceModule
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, REFUND_DOMAIN_SERVICE_ROOT_SERVICE_NAME)
            rfcRoutes()
        }

        kafkaConsumer(startRoutesSync = true) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.EHR)
    }
}
