package br.com.alice.api.fhir.controllers

import br.com.alice.api.fhir.ControllerTestHelper
import br.com.alice.api.fhir.services.AuthService
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.FhirProviderAccess
import br.com.alice.data.layer.models.ProviderIntegration.EINSTEIN_CLINIC
import br.com.alice.fhir.client.FhirPatchResourceService
import br.com.alice.fhir.model.PatchOperation
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.hl7.fhir.r4.model.ResourceType
import java.net.URLDecoder
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class PatchControllerTest : ControllerTestHelper() {

    private val authService: AuthService = mockk()
    private val fhirPatchResourceService: FhirPatchResourceService = mockk()
    private val patchController = PatchController(
        authService,
        fhirPatchResourceService
    )

    private val operations = listOf(PatchOperation(op = "replace", path = "/status", value = "done"))
    private val identifierEncoded = "urn%3Ahttps%3A%2F%2Fwww.einstein.br%7CAPP444HIAE'"
    private val identifierDecoded = URLDecoder.decode(identifierEncoded, "UTF-8")

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { patchController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        authService,
        fhirPatchResourceService
    )

    @Test
    fun `#patchAppointment - should call service`() = runBlocking {
        coEvery { authService.getProviderName(token) } returns FhirProviderAccess(provider = EINSTEIN_CLINIC).success()
        coEvery {
            fhirPatchResourceService.notify(
                provider = EINSTEIN_CLINIC,
                resource = ResourceType.Appointment,
                operations = operations,
                resourceId = identifierDecoded
            )
        } returns true.success()

        authenticated(token) {
            patch("/Appointment?identifier=$identifierEncoded", operations) { response ->
                assertThat(response).isAccepted()
            }
        }

        coVerifyOnce { authService.getProviderName(any()) }
        coVerifyOnce { fhirPatchResourceService.notify(any(), any(), any(), any()) }
    }

    @Test
    fun `#patchAppointment returns error when any error occurs to notify`() = runBlocking {
        coEvery { authService.getProviderName(token) } returns FhirProviderAccess(provider = EINSTEIN_CLINIC).success()
        coEvery {
            fhirPatchResourceService.notify(
                provider = EINSTEIN_CLINIC,
                resource = ResourceType.Appointment,
                operations = operations,
                resourceId = identifierDecoded
            )
        } returns Exception("my exception").failure()

        authenticated(token) {
            patch("/Appointment?identifier=$identifierEncoded", operations) { response ->
                assertThat(response).isBadRequestWithErrorCode("invalid_request", "my exception")
            }
        }

        coVerifyOnce { authService.getProviderName(any()) }
        coVerifyOnce { fhirPatchResourceService.notify(any(), any(), any(), any()) }
    }
}
