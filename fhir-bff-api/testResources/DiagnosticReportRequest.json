{"identifier": [{"system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-laudoexame-codigoOrigem", "value": "795602354372", "use": "usual"}], "resourceType": "DiagnosticReport", "basedOn": [{"reference": "ServiceRequest/60c78c800120212002fe3409", "type": "ServiceRequest"}], "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0074", "code": "LAB", "display": "Laboratory"}], "text": "Laboratório"}], "code": {"coding": [{"system": "http://loinc.org", "code": "11502-2", "display": "Laboratory report"}], "text": "Resultado de Exame Laboratorial"}, "subject": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "effectivePeriod": {"start": "2021-06-10T13:28:43", "end": "2021-06-11T00:49:06"}, "performer": [{"type": "Practitioner", "reference": "Practitioner/60c6b321d81a2277b0783f5e", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}], "resultsInterpreter": [{"type": "Practitioner", "reference": "Practitioner/60c6b31f1d7633279b1d247c", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}], "result": [{"reference": "Observation/60c79450f752201def83719d", "type": "Observation"}], "presentedForm": [{"contentType": "application/fhir+json", "language": "pt-BR", "url": "https: //interoperabilidade.dasa.com.br/fhir/Media/60c7945f80daef0b789d1cfc"}], "updatedAt": "2021-06-18T19:30:57.941Z", "meta": {"lastUpdated": "2021-06-18T19:30:57.941Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/DiagnosticReport"]}, "_id": "60c7945fb4e9be676a713b17", "id": "60c7945fb4e9be676a713b17", "contained": [{"identifier": [{"value": "**********", "use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-paciente-codigoDestino"}, {"value": "01234567890", "use": "official", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoafisica-id"}], "name": [{"text": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "official"}], "telecom": [{"system": "phone", "value": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "mobile"}, {"system": "phone", "value": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "home"}, {"value": "XXXXXXXXXXXXXXXXXXXXXXX", "system": "phone", "use": "mobile"}, {"value": "XXXXXXXXXXXXXXXXXXXXXXX", "system": "phone", "use": "mobile"}], "gender": "female", "birthDate": "XXXXXXXXXXXXXXXXXXXXXXX8T00: 00: 00", "address": [{"text": "XXXXXXXXXXXXXXXXXXXXXXX,, SP - BR", "line": ["XXXXXXXXXXXXXXXXXXXXXXXros", "XXXXXXXXXXXXXXXXXXXXXXX"], "district": "XXXXXXXXXXXXXXXXXXXXXXX", "postalCode": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "home", "type": "physical"}], "resourceType": "Patient", "updatedAt": "2021-06-18T19: 30: 53.055Z", "meta": {"lastUpdated": "2021-06-18T19: 30: 53.055Z", "profile": ["https: //interoperabilidade.dasa.com.br/fhir/StructureDefinition/Patient"]}, "id": "60c78c7ead8e257b5ba321ce"}, {"identifier": [{"system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicoes-codigoOrigem", "value": "795602354372-4371-1547", "use": "usual"}], "resourceType": "Observation", "status": "final", "subject": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "performer": [{"type": "Practitioner", "reference": "Practitioner/60c6b31f1d7633283b1d247c", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}], "basedOn": [{"reference": "ServiceRequest/60c78c800120212002fe3409", "type": "ServiceRequest"}], "code": {"coding": [{"system": "https: //interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "1547", "display": "Série Vermelha"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "Série Vermelha"}, "component": [{"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "1554", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "valueQuantity": {"value": 5.1, "unit": "10^6/µ L"}, "referenceRange": [{"low": {"value": "4.0", "unit": "10^6/µ L"}, "high": {"value": "5.2", "unit": "10^6/µ L"}, "text": "de 4,00 até 5,20\t10^6/µ L"}]}, {"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "2046", "display": "Hemoglobina"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "Hemoglobina"}, "valueQuantity": {"value": 15.1, "unit": "g/dL"}, "referenceRange": [{"low": {"value": "12.0", "unit": "g/dL"}, "high": {"value": "16.0", "unit": "g/dL"}, "text": "de 12,0 até 16,0\tg/dL"}]}, {"code": {"coding": [{"system": "https: //interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "2021", "display": "Hematócrito"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "Hematócrito"}, "valueQuantity": {"value": 45.7, "unit": "%"}, "referenceRange": [{"low": {"value": "36.0", "unit": "%"}, "high": {"value": "46.0", "unit": "%"}, "text": "<PERSON> 36,0 at<PERSON> 46,0\t%"}]}, {"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "4151", "display": "VCM"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "VCM"}, "valueQuantity": {"value": 89.7, "unit": "fL"}, "referenceRange": [{"low": {"value": "80.0", "unit": "fL"}, "high": {"value": "100.0", "unit": "fL"}, "text": "de 80,0 até 100,0\tfL"}]}, {"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "1951", "display": "HCM"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "HCM"}, "valueQuantity": {"value": 29.7, "unit": "pg"}, "referenceRange": [{"low": {"value": "26.0", "unit": "pg"}, "high": {"value": "34.0", "unit": "pg"}, "text": "de 26,0 até 34,0\tpg"}]}, {"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "638", "display": "CHCM"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https: //interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "CHCM"}, "valueQuantity": {"value": 33.1, "unit": "g/dL"}, "referenceRange": [{"low": {"value": "31.0", "unit": "g/dL"}, "high": {"value": "36.0", "unit": "g/dL"}, "text": "de 31,0 até 36,0\tg/dL"}]}, {"code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "3134", "display": "RDW"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "RDW"}, "valueQuantity": {"value": 12.6, "unit": "%"}, "referenceRange": [{"low": {"value": "11.5", "unit": "%"}, "high": {"value": "14.5", "unit": "%"}, "text": "de 11,5 até 14,5\t%"}]}], "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "laboratory", "display": "Laboratory"}], "text": "Laboratory"}], "note": [], "effectiveDateTime": "2021-06-10T13:28:43.000Z", "updatedAt": "2021-06-18T19:30:54.446Z", "meta": {"lastUpdated": "2021-06-18T19:30:54.446Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Observation"]}, "id": "60c79450f752201def83719d"}, {"identifier": [{"use": "usual", "value": "795602354372-827688643707-1307", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicao-codigoOrigem"}], "resourceType": "ServiceRequest", "requisition": {"value": "84620206"}, "code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "5786", "display": "Contagem de Plaquetas"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40304361"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-dataprovider-produto-codigo", "code": "1307", "display": "HEMOGRAMA"}], "text": "Hemograma com Contagemde Plaquetas"}, "subject": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "requester": {"type": "Practitioner", "reference": "Practitioner/60c78c7ead8e257b5ba241cf", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "insurance": [{"reference": "Coverage/60c78c7ead8e257b5ba342d1", "type": "Coverage"}], "status": "completed", "intent": "order", "updatedAt": "2021-06-18T19: 30: 54.003Z", "meta": {"lastUpdated": "2021-06-18T19: 30: 54.003Z", "profile": ["https: //interoperabilidade.dasa.com.br/fhir/StructureDefinition/ServiceRequest"]}, "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "108252007", "display": "Laboratory procedure"}], "text": "Procedimento Laboratorial"}], "id": "60c78c800120212002fe3409"}, {"identifier": [{"value": "0250864", "use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-convenio-mPacCv_convenio"}], "resourceType": "Coverage", "status": "active", "subscriber": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "subscriberId": "0250864", "beneficiary": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "payor": {"reference": "Organization/60bfa7ccc3e86a5988dd700e", "type": "Organization", "display": "<PERSON>"}, "class": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/coverage-class", "code": "plan"}]}, "value": "<PERSON><PERSON><PERSON><PERSON>", "name": ""}], "updatedAt": "2021-06-16T21:35:37.482Z", "meta": {"lastUpdated": "2021-06-16T21:35:37.483Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/fhir/StructureDefinition/Coverage"]}, "id": "60c78c7ead8e257b5ba342d1"}, {"identifier": [{"system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/undefined-registro-profissional-ninscricao", "value": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "official"}], "resourceType": "Practitioner", "name": [{"text": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "official"}], "updatedAt": "2021-06-18T19:30:55.589Z", "meta": {"lastUpdated": "2021-06-18T19:30:55.589Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Practitioner"]}, "id": "60c6b321d81a2277b0783f5e"}, {"identifier": [{"system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/crmundefined-registro-profissional-ninscricao", "value": "112181", "use": "official"}], "name": [{"text": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "official"}], "resourceType": "Practitioner", "updatedAt": "2021-06-18T19:30:53.505Z", "meta": {"lastUpdated": "2021-06-18T19:30:53.505Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Practitioner"]}, "id": "60c6b31f1d7633279b1d247c"}, {"identifier": [{"value": "146741", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/crmsp-registro-profissional-ninscricao", "use": "official"}], "name": [{"text": "XXXXXXXXXXXXXXXXXXXXXXX", "use": "official"}], "resourceType": "Practitioner", "meta": {"lastUpdated": "2021-06-18T19:30:53.000Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Practitioner"]}, "id": "60c78c7ead8e257b5ba241cf"}, {"resourceType": "Media", "_id": "60c7945f80daef0b789d1cfc", "id": "60c7945f80daef0b789d1cfc", "identifier": [{"system": "https: //interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-laudoexame-codigoorigem", "value": "795602354372/11713603", "use": "usual"}], "status": "completed", "subject": {"reference": "Patient/60c78c7ead8e257b5ba321ce", "type": "Patient", "display": "XXXXXXXXXXXXXXXXXXXXXXX"}, "operator": {"identifier": {"value": "MOTION"}}, "content": {"contentType": "application/pdf", "url": "https://bkt-sa-east-1-deepwater-dev-pdf.s3.sa-east-1.amazonaws.com/795602354372-1624044657539.pdf?AWSAccessKeyId=AKIAQBHT2D72PUOHBEHO&Expires=**********&Signature=c6GBzUlb9Ds6uVUdR2VXaQHWE64%3D"}, "meta": {"lastUpdated": "2021-06-18T19:30:57.000Z", "profile": ["https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/Media"]}}]}