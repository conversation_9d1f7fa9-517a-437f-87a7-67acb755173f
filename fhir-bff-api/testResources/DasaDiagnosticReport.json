{"resourceType": "DiagnosticReport", "id": "d704a146aebc58dafc53e31a", "contained": [{"resourceType": "Patient", "id": "patient-01", "identifier": [{"use": "usual", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-paciente-codigoDestino", "value": "**********"}, {"use": "official", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/govbr-receitafederal-pessoafisica-id", "value": "01234567890"}], "name": [{"use": "official", "text": "<PERSON>"}], "telecom": [{"system": "phone", "value": "11987574597", "use": "mobile"}, {"system": "phone", "value": "**********", "use": "home"}, {"system": "email", "value": "<EMAIL>", "use": "home"}], "gender": "female", "birthDate": "1955-08-23", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON>, 52, apto 413 - <PERSON><PERSON> - São Paulo - SP - 21330300", "line": ["<PERSON><PERSON>", "52", "apto 413"], "city": "São Paulo", "district": "Vila <PERSON>", "state": "SP", "postalCode": "21330300", "country": "BR"}]}, {"resourceType": "Practitioner", "id": "practitioner-01", "identifier": [{"use": "official", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/crmsp-registro-profissional-ninscricao", "value": "122977"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON>"}]}, {"resourceType": "Practitioner", "id": "practitioner-02", "identifier": [{"use": "official", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/crbiosp-registro-profissional-ninscricao", "value": "106784/01"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON> Almeida"}]}, {"resourceType": "Practitioner", "id": "practitioner-03", "identifier": [{"use": "official", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/crmsp-registro-profissional-ninscricao", "value": "57537"}], "name": [{"use": "official", "text": "Francisco de Assis <PERSON>"}]}, {"resourceType": "Practitioner", "id": "practitioner-04", "identifier": [{"use": "official", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/crmsp-registro-profissional-ninscricao", "value": "112181"}], "name": [{"use": "official", "text": "<PERSON>"}]}, {"resourceType": "ServiceRequest", "id": "service-request-01", "identifier": [{"use": "usual", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-atendimento-codigoOrigem", "value": "796301368240-827347649903-40302504"}], "requisition": {"value": "81327292"}, "extension": [{"url": "https://interoperabilidade.dasa.com.br/fhir/StructureDefinition/coverage-password", "valueString": "108895870"}], "status": "completed", "intent": "order", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "108252007", "display": "Laboratory procedure"}], "text": "Procedimento Laboratorial"}], "code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "39951", "display": "COVID - Detecção qualitativa de Coronavírus (2019-nCov)"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40302512", "display": "Detecção qualitativa de Coronavírus (SARS-CoV-2)"}]}, "subject": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "requester": {"reference": "#practitioner-01", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON>"}, "insurance": [{"reference": "#coverage-01", "type": "Coverage"}]}, {"resourceType": "Coverage", "id": "coverage-01", "identifier": [{"use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-convenio-mPacCv_convenio", "value": "5886112"}], "status": "active", "subscriber": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "subscriberId": "5886112", "beneficiary": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "payor": {"reference": "#organization-01", "type": "Organization", "display": "<PERSON>"}, "class": [{"type": {"coding": [{"system": "http:terminology.hl7.org/CodeSystem/coverage-class", "code": "plan", "display": "Plan"}]}, "value": "alice-saude", "name": "<PERSON>"}]}, {"resourceType": "Organization", "id": "organization-01", "identifier": [{"use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-empresa-codigo", "value": "alice-saude"}], "name": "<PERSON>"}, {"resourceType": "Organization", "id": "organization-02", "identifier": [{"use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-empresa-codigo", "value": "31"}], "name": "Diagnosticos da America S.A -Delboni Auriemo"}, {"resourceType": "Encounter", "id": "encounter-01", "identifier": [{"use": "usual", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-gliese-visita-codigo", "value": "1873"}, {"use": "usual", "system": "http://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-atendimento-codigoOrigem", "value": "796301368240"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "basedOn": [{"reference": "#service-request-01", "type": "ServiceRequest"}], "location": [{"location": {"type": "Location", "identifier": {"value": "1234"}, "display": "Diagnosticos da America S.A -Delboni Auriemo"}}], "period": {"start": "2021-01-05T15:00:00-03:00", "end": "2021-01-05T15:00:00-03:00"}, "serviceProvider": {"reference": "#organization-02", "type": "Organization", "display": "Diagnosticos da America S.A -Delboni Auriemo"}}, {"resourceType": "Observation", "id": "observation-01", "identifier": [{"use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicoes-codigoOrigem", "value": "916100034874-39951-39951"}], "basedOn": [{"reference": "#service-request-01", "type": "ServiceRequest"}], "status": "final", "effectiveDateTime": "2021-02-10T20:09:38-03:00", "subject": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "performer": [{"reference": "#practitioner-03", "type": "Practitioner", "display": "Francisco de Assis <PERSON>"}, {"reference": "#practitioner-04", "type": "Practitioner", "display": "<PERSON>"}], "code": {"coding": [{"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem", "code": "39951", "display": "COVID - Detecção qualitativa de Coronavírus (2019-nCov)"}, {"system": "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo", "code": "40302512", "display": "Detecção qualitativa de Coronavírus (SARS-CoV-2)"}]}, "valueString": "<PERSON><PERSON>", "note": [{"text": "Observações:\n\n1. Este teste tem como alvos os genes N e RdRP do Sars-CoV-2.\n2. Limite de detecção = 100 cópias/mL (LOD 95%).\n3. O resultado Não Detectado não descarta a presença do vírus em concentração inferior ao limite de detecção do teste, o que é mais frequente nos dias iniciais e finais da infecção e em portadores assintomáticos.\n4. É importante correlacionar o resultado deste exame com o quadro clínico e outros achados radiológicos e laboratoriais do paciente.\n\nReferência Bibliográfica: \n<PERSON> et al. Validation and verification of the Abbott RealTime SARS-CoV-2 assay analytical and clinical performance. Journal of Clinical Virology 129 (2020) 104474.\n\nLaboratório habilitado pelo Instituto Adolfo Lutz (IAL-SP) conforme Comunicado DG/IAL de 27/03/20 publicado no DOSP."}], "referenceRange": [{"text": "<PERSON><PERSON>"}]}], "identifier": [{"use": "usual", "system": "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-resultadodeexame-codigoOrigem", "value": "533701801614"}], "basedOn": [{"reference": "#service-request-01", "type": "ServiceRequest"}], "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0074", "code": "LAB", "display": "Laboratory"}]}], "code": {"coding": [{"system": "http://loinc.org", "code": "11502-2", "display": "Laboratory report"}]}, "subject": {"reference": "#patient-01", "type": "Patient", "display": "<PERSON>"}, "encounter": {"reference": "#encounter-01", "type": "Encounter"}, "effectivePeriod": {"start": "2020-06-14T11:33:35-03:00", "end": "2020-06-14T11:33:35-03:00"}, "performer": [{"reference": "#practitioner-02", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON> Almeida"}], "resultsInterpreter": [{"reference": "#practitioner-03", "type": "Practitioner", "display": "Francisco de Assis <PERSON>"}], "result": [{"reference": "#observation-01", "type": "Observation"}], "presentedForm": [{"contentType": "application/fhir+json", "language": "pt-BR", "url": "https://interoperabilidade.dasa.com.br/fhir/Media/9dd45b0c8347df811341d864"}]}