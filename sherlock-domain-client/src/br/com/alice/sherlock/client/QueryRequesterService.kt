package br.com.alice.sherlock.client

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.PIIField
import br.com.alice.data.layer.models.Staff
import com.github.kittinunf.result.Result

@RemoteService
interface QueryRequesterService : Service {
    override val namespace get() = "sherlock"
    override val serviceName get() = "query_requester"

    suspend fun requestQuery(
        requester: Staff,
        customQuery: String,
        description: String,
        piiFields: List<PIIField>
    ): Result<QueryRequestDTO, Throwable>
}

data class QueryRequestDTO(
    val requestId: String
)

open class QueryRequesterException(
    message: String,
    code: String,
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class EmptyQueryException(
    message: String = "Query request must have a query",
    code: String = "query_request_with_empty_query",
    cause: Throwable? = null
) : QueryRequesterException(message, code, cause)

class InvalidPIIFIeldException(
    message: String = "Query request must all PII fields valid",
    code: String = "query_request_with_invalid_pii_field",
    cause: Throwable? = null
) : QueryRequesterException(message, code, cause)

class EmptyDescriptionException(
    message: String = "Query request must have a description",
    code: String = "query_request_with_empty_description",
    cause: Throwable? = null
) : QueryRequesterException(message, code, cause)

class CustomQueryValidationException(
    message: String,
    code: String = "custom_query_validation_error",
    cause: Throwable? = null
) : QueryRequesterException(message, code, cause) {
    constructor(error: String) : this(
        message = "Custom query validation error: $error"
    )
}

class QueryTooLongException(
    message: String,
    code: String = "query_too_long",
    cause: Throwable? = null
) : QueryRequesterException(message, code, cause) {
    constructor(querySize: Int) : this(
        message = "Query length is too long: $querySize characters"
    )
}
