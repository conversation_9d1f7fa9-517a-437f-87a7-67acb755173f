package br.com.alice.sherlock.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.SherlockFileResult
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface SherlockFileVaultService : Service {
    override val namespace get() = "sherlock"
    override val serviceName get() = "file_vault"
    suspend fun getStaffFileById(id: UUID): Result<String, Throwable>
    suspend fun auditFile(
        staffId: UUID,
        fileId: UUID,
        reason: String
    ): Result<SherlockFileResult, Throwable>
}
