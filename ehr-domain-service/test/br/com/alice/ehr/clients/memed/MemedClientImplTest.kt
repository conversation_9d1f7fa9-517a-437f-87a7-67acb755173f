package br.com.alice.ehr.clients.memed

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.exceptions.MemedApiCallException
import br.com.alice.ehr.exceptions.MemedNotActiveUserException
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestData
import io.ktor.content.TextContent
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MemedClientImplTest {
    private val defaultResponseHeaders = headersOf(
        "Content-Type" to listOf("application/vnd.api+json")
    )

    @Test
    fun `#createUser posts a user`() = runBlocking {
        val healthProfessional = TestModelFactory.buildHealthProfessional()
        val memedUserRequest = MemedUserRequest(
            data = MemedUserRequestData(
                attributes = MemedUserRequestAttributes(
                    externalId = "abc-123",
                    nome = "John",
                    sobrenome = "Cena",
                    dataNascimento = "23/04/1977",
                    cpf = "11122244488",
                    email = "<EMAIL>",
                    sexo = "M",
                    board = MemedUserBoardAttributes(
                        boardCode = "CRM",
                        boardNumber = "999123",
                        boardState = "AC"
                    )
                )
            )
        )

        mockkObject(MemedStaffConverter) {
            every {
                MemedStaffConverter.healthProfessionalToMemedUserRequest(healthProfessional)
            } returns memedUserRequest

            val httpClientMock = httpClientMock(memedActiveUser) { request ->
                request.method == HttpMethod.Post &&
                        request.url.encodedPath == "/v1/sinapse-prescricao/usuarios" &&
                        request.headers.contains(HttpHeaders.Accept, "application/vnd.api+json") &&
                        request.headers.contains(HttpHeaders.CacheControl, "no-cache") &&
                        (request.body as TextContent).text == gson.toJson(memedUserRequest)
            }

            val memedClient = MemedClientImpl(httpClientMock)

            val result = memedClient.createUser(healthProfessional)

            assertThat(result).isSuccess()
        }
    }

    @Test
    fun `#updateUser patches a user`() = runBlocking<Unit> {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            staff = TestModelFactory.buildStaff()
        )

        val path = "/v1/sinapse-prescricao/usuarios/${healthProfessional.staff?.nationalId}"
        val httpClientMock = httpClientMock(memedActiveUser) { request ->
            request.method == HttpMethod.Patch &&
                    request.url.encodedPath == path &&
                    request.headers.contains(HttpHeaders.Accept, "application/vnd.api+json") &&
                    request.headers.contains(HttpHeaders.CacheControl, "no-cache")
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.updateUser(healthProfessional)

        assertThat(result).isSuccess()
    }

    @Test
    fun `#getUser returns true if Memed API returns a user`() = runBlocking {
        val nationalId = "12300372861"
        val path = "/v1/sinapse-prescricao/usuarios/$nationalId"
        val expected = MemedUser(
            data = MemedUserData(
                attributes = MemedUserAttributes(
                    status = "Ativo",
                    token = memedActiveUserToken,
                    externalId = externalId
                )
            )
        )
        val httpClientMock = httpClientMock(memedActiveUser) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUser(nationalId)

        assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#getUser returns failure if Memed API answers with not found`() = runBlocking {
        val nationalId = "12300372861"
        val path = "/v1/sinapse-prescricao/usuarios/$nationalId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUser(nationalId)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getUser returns an error if Memed API returns an error`() = runBlocking {
        val physicianId = "500"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.InternalServerError) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUser(physicianId)

        assertThat(result).isFailureOfType(MemedApiCallException::class)
    }

    @Test
    fun `#getUserToken returns an error if physician is not found`() = runBlocking {
        val physicianId = "404"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserToken(physicianId)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getUserToken returns an error if API call fails`() = runBlocking {
        val physicianId = "500"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.InternalServerError) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserToken(physicianId)

        assertThat(result).isFailureOfType(MemedApiCallException::class)
    }

    @Test
    fun `#getUserToken returns an error if physician is not active`() = runBlocking {
        val physicianId = "400"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock(memedInactiveUser) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserToken(physicianId)

        assertThat(result).isFailureOfType(MemedNotActiveUserException::class)
    }

    @Test
    fun `#getUserToken returns the token if physician is active`() = runBlocking {
        val physicianId = "200"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock(memedActiveUser) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserToken(physicianId)

        assertThat(result).isSuccessWithData(memedActiveUserToken)
    }

    @Test
    fun `#getUserStatus returns the status of an active user`() = runBlocking {
        val nationalId = "12300372861"
        val path = "/v1/sinapse-prescricao/usuarios/$nationalId"

        val httpClientMock = httpClientMock(memedActiveUser) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserStatus(nationalId)

        assertThat(result).isSuccessWithData(MemedUserStatus.ACTIVE)
    }

    @Test
    fun `#getUserStatus returns active from users under analysis`() = runBlocking {
        val nationalId = "12300372861"
        val path = "/v1/sinapse-prescricao/usuarios/$nationalId"

        val httpClientMock = httpClientMock(memedUnderAnalysisUser) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserStatus(nationalId)

        assertThat(result).isSuccessWithData(MemedUserStatus.ACTIVE)
    }

    @Test
    fun `#getUserStatus returns not found for an unknown user`() = runBlocking {
        val nationalId = "12300372861"
        val path = "/v1/sinapse-prescricao/usuarios/$nationalId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserStatus(nationalId)

        assertThat(result).isSuccessWithData(MemedUserStatus.NOT_FOUND)
    }

    @Test
    fun `#getUserStatus returns an error if API call fails`() = runBlocking {
        val physicianId = "500zzz"
        val path = "/v1/sinapse-prescricao/usuarios/$physicianId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.InternalServerError) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getUserStatus(physicianId)

        assertThat(result).isFailureOfType(MemedApiCallException::class)
    }

    @Test
    fun `#getPrescription returns the prescription data`() = runBlocking {
        val prescriptionId = "prescrition_123_abc"
        val path = "/v1/prescricoes/$prescriptionId"

        val httpClientMock = httpClientMock(memedPrescription) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescription(prescriptionId, "abc123")

        assertThat(result).isSuccessWithData(
            MemedPrescription(
                data = MemedPrescriptionData(
                    attributes = MemedPrescriptionAttributes(
                        createdAt = "22/12/2021 16:57:56",
                        prescriptionDate = "2021-12-22 16:57:56",
                        medicines = listOf(
                            MemedPrescriptionMedicine(
                                name = "Ibuprofeno 200mg, Comprimido revestido (20un)",
                                description = "Ibuprofeno 200mg",
                                posology = "<p>Tomar 1 comprimido a cada 6 horas, conforme necessário.</p>",
                                sanitizedPosology = "Tomar 1 comprimido a cada 6 horas, conforme necessário.",
                                quantity = 0,
                                unit = "Uso Contínuo",
                                special = true,
                                receituarioId = "12",
                            )
                        )
                    )
                )
            )
        )
    }

    @Test
    fun `#getPrescription returns an error if prescription is not found`() = runBlocking {
        val prescriptionId = "prescription_xyz"
        val path = "/v1/prescricoes/$prescriptionId"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescription(prescriptionId, "abc123")

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getPrescriptionLink returns the prescription link info`() = runBlocking {
        val prescriptionId = "prescription_123"
        val path = "/v1/prescricoes/$prescriptionId/get-digital-prescription-link"

        val httpClientMock = httpClientMock(memedPrescriptionLink) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescriptionLink(prescriptionId, "abc123")

        assertThat(result).isSuccessWithData(
            MemedPrescriptionLink(
                data = listOf(
                    MemedPrescriptionLinkData(
                        attributes = MemedPrescriptionLinkAttributes(
                            link = "https://integrations.memed.com.br/p/bBH5Np",
                            digits = "6111",
                        )
                    )
                )
            )
        )
    }

    @Test
    fun `#getPrescriptionLink returns an error if prescription is not found`() = runBlocking {
        val prescriptionId = "prescription_123"
        val path = "/v1/prescricoes/$prescriptionId/get-digital-prescription-link"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescriptionLink(prescriptionId, "abc123")

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getPrescriptionDocument returns the prescription link info`() = runBlocking {
        val prescriptionId = "prescription_123"
        val path = "/v1/prescricoes/$prescriptionId/url-document/full"

        val httpClientMock = httpClientMock(memedPrescriptionDocument) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescriptionDocument(prescriptionId, "abc123")

        assertThat(result).isSuccessWithData(
            MemedPrescriptionDocument(
                data = listOf(
                    MemedPrescriptionDocumentData(
                        attributes = MemedPrescriptionDocumentAttributes(
                            link = "https://integrations.api.memed.com.br/v1/prescricoes/" +
                                    "bBH5Np/pdf?document=583ea70c-eef0-4cd2-809e-30c63dacc7a5",
                            signed = 0,
                        )
                    )
                )
            )
        )
    }

    @Test
    fun `#getPrescriptionDocument returns an error if prescription is not found`() = runBlocking {
        val prescriptionId = "prescription_123"
        val path = "/v1/prescricoes/$prescriptionId/url-document/full"

        val httpClientMock = httpClientMock("{}", HttpStatusCode.NotFound) { request ->
            request.method == HttpMethod.Get && request.url.encodedPath == path
        }
        val memedClient = MemedClientImpl(httpClientMock)

        val result = memedClient.getPrescriptionDocument(prescriptionId, "abc123")

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#setPrescriptionLayout posts a layout`() = runBlocking<Unit> {
        val httpClientMock = httpClientMock(memedLayoutConfiguration) { request ->
            request.method == HttpMethod.Post &&
                    request.url.encodedPath == "/v1/opcoes-receituario" &&
                    request.headers.contains(HttpHeaders.Accept, "application/vnd.api+json") &&
                    request.headers.contains(HttpHeaders.CacheControl, "no-cache")
        }

        val memedClient = MemedClientImpl(httpClientMock)
        val result = memedClient.setPrescriptionLayout("abc123")
        assertThat(result).isSuccess()
    }

    @Test
    fun `#setEmptyPrescriptionLayout posts an empty layout`() = runBlocking<Unit> {
        val httpClientMock = httpClientMock(memedLayoutConfiguration) { request ->
            request.method == HttpMethod.Post &&
                    request.url.encodedPath == "/v1/opcoes-receituario" &&
                    request.headers.contains(HttpHeaders.Accept, "application/vnd.api+json") &&
                    request.headers.contains(HttpHeaders.CacheControl, "no-cache")
        }

        val memedClient = MemedClientImpl(httpClientMock)
        val result = memedClient.setEmptyPrescriptionLayout("abc123")
        assertThat(result).isSuccess()
    }

    private fun httpClientMock(
        responseContent: String,
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        requestMatcher: (request: HttpRequestData) -> Boolean,
    ): HttpClient =
        HttpClient(MockEngine) {
            expectSuccess = true
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
            engine {
                addHandler { request ->
                    if (requestMatcher(request)) {
                        respond(
                            responseContent,
                            statusCode,
                            defaultResponseHeaders
                        )
                    } else {
                        error("unknown request")
                    }
                }
            }
        }
}
