package br.com.alice.ehr.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.HealthMeasurementType
import br.com.alice.data.layer.models.HealthMeasurementTypeFormat
import br.com.alice.data.layer.models.HealthMeasurementTypeModel
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDateTime
import kotlin.test.Test

class HealthMeasurementTypeConverterTest {

    private val healthMeasurementType = HealthMeasurementType(
        key = "WEIGHT",
        name = "Weight",
        healthMeasurementCategoryId = RangeUUID.generate(),
        format = HealthMeasurementTypeFormat.FLOAT,
        unit = "kg",
        readOnly = false,
        active = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        id = RangeUUID.generate(),
        version = 0,
        updatedBy = UpdatedBy("test-user", "Test User", "environment")
    )

    private val healthMeasurementTypeModel = HealthMeasurementTypeModel(
        key = healthMeasurementType.key,
        name = healthMeasurementType.name,
        healthMeasurementCategoryId = healthMeasurementType.healthMeasurementCategoryId,
        format = healthMeasurementType.format,
        unit = healthMeasurementType.unit,
        readOnly = healthMeasurementType.readOnly,
        active = healthMeasurementType.active,
        createdAt = healthMeasurementType.createdAt,
        updatedAt = healthMeasurementType.updatedAt,
        id = healthMeasurementType.id,
        version = healthMeasurementType.version,
        updatedBy = healthMeasurementType.updatedBy
    )

    @Test
    fun testToModel() {
        assertEquals(healthMeasurementTypeModel, healthMeasurementType.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(healthMeasurementType, healthMeasurementTypeModel.toTransport())
    }
}
