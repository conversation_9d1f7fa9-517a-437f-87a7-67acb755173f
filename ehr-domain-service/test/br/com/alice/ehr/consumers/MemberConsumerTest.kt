package br.com.alice.ehr.consumers

import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MemberConsumerTest : ConsumerTest() {

    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val consumer = MemberConsumer(personInternalReferenceService, personClinicalAccountService)

    @Test
    fun `#createPersonInternalReference should create person internal reference`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val event = MemberActivatedEvent(member)
        val personInternalReference = TestModelFactory.buildPersonInternalReference()

        coEvery { personInternalReferenceService.getForPerson(member.personId) } returns personInternalReference.success()

        consumer.createPersonInternalReference(event)

        coVerify(exactly = 1) {
            personInternalReferenceService.getForPerson(member.personId)
        }
    }

    @Test
    fun `#deletePersonClinicalAccount should delete person clinical account`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val event = MemberCancelledEvent(member)

        coEvery { personClinicalAccountService.deleteByPersonId(member.personId) } returns true.success()

        consumer.deletePersonClinicalAccount(event)

        coVerify(exactly = 1) { personClinicalAccountService.deleteByPersonId(any()) }
    }
}
