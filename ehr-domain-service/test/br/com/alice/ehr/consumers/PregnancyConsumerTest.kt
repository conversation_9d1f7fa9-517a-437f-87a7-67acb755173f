package br.com.alice.ehr.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.converters.toModel
import br.com.alice.ehr.event.PregnancyUpsertEvent
import br.com.alice.ehr.services.internal.assistance_summary.AssistanceSummaryPregnancyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class PregnancyConsumerTest : ConsumerTest() {

    private val assistanceSummaryPregnancyService: AssistanceSummaryPregnancyService = mockk()

    private val consumer = PregnancyConsumer(assistanceSummaryPregnancyService)

    private val pregnancy = TestModelFactory.buildPregnancy()
    private val assistanceSummary = TestModelFactory.buildAssistanceSummary()

    @AfterTest
    fun setup() =  clearAllMocks()

    @Test
    fun `#handleUpsert - should return true and upsert`() = runBlocking {
        val event = PregnancyUpsertEvent(pregnancy)

        coEvery { assistanceSummaryPregnancyService.upsert(pregnancy.toModel()) } returns assistanceSummary.toModel().success()

        val result = consumer.handleUpsert(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { assistanceSummaryPregnancyService.upsert(any()) }
    }

    @Test
    fun `#handleUpsert - should return false and upsert is return UnsupportedOperationException`() = runBlocking {
        val event = PregnancyUpsertEvent(pregnancy)

        coEvery { assistanceSummaryPregnancyService.upsert(pregnancy.toModel()) } returns UnsupportedOperationException().failure()

        val result = consumer.handleUpsert(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { assistanceSummaryPregnancyService.upsert(any()) }
    }

    @Test
    fun `#handleUpsert - should return failure because Exception`() = runBlocking {
        val event = PregnancyUpsertEvent(pregnancy)

        coEvery { assistanceSummaryPregnancyService.upsert(pregnancy.toModel()) } returns Exception().failure()

        val result = consumer.handleUpsert(event)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { assistanceSummaryPregnancyService.upsert(any()) }
    }


}
