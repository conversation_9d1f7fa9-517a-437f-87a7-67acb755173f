package br.com.alice.ehr.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthPlanTask
import br.com.alice.data.layer.helpers.TestModelFactory.buildPerson
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthEventOriginEnum
import br.com.alice.data.layer.models.HealthEventTypeEnum
import br.com.alice.data.layer.models.HealthEvents
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.ehr.event.EmergencyExecutedEvent
import br.com.alice.eventinder.events.HealthEventExecutedEvent
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime.now
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested

class EmergencyExecutedConsumerTest : ConsumerTest() {
    private val kafkaProducerService = mockk<KafkaProducerService>()
    private val healthPlanTaskService = mockk<HealthPlanTaskService>()

    val consumer = EmergencyExecutedConsumer(kafkaProducerService, healthPlanTaskService)

    private val person = buildPerson()
    private val taskId = RangeUUID.generate()
    private val task = buildHealthPlanTask(
        personId = person.id,
        id = taskId,
        type = HealthPlanTaskType.EMERGENCY,
    )
    private val producerResult = ProducerResult(now(), "1", 1)
    private val nowDate = now()
    private val healthEvents = HealthEvents(
        id = RangeUUID.generate(),
        personId = person.id,
        eventType = HealthEventTypeEnum.PS,
        procedureIds = listOf("40028922"),
        origin = HealthEventOriginEnum.EITA,
        requestedAt = nowDate,
        executedAt = nowDate,
        healthProfessionalId = RangeUUID.generate(),
        originReferences = listOf(
            EventReference(
                id = taskId.toString(),
                location = HealthEventLocationEnum.TOTVS_GUIA
            )
        )
    )

    @Test
    fun `#handlePsByEventinder should producer PsExecutedEvent`() = runBlocking {
        val event = HealthEventExecutedEvent(
            payload = healthEvents
        )
        coEvery {
            healthPlanTaskService.findByPersonAndFilters(
                person.id, HealthPlanTaskFilters(
                    statuses = listOf(HealthPlanTaskStatus.ACTIVE, HealthPlanTaskStatus.CREATE),
                    types = listOf(HealthPlanTaskType.EMERGENCY),
                    createdAtLess = nowDate
                )
            )
        } returns listOf(task).success()

        coEvery {
            kafkaProducerService.produce(
                match { it.name == EmergencyExecutedEvent.name },
                match { it == taskId.toString() })
        } returns producerResult

        val result = consumer.handlePsByEventinder(event)
        assertThat(result).isSuccess()

        coVerifyOnce { healthPlanTaskService.findByPersonAndFilters(any(), any()) }
        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

    @Nested
    inner class InvalidProducer {
        private val invalidExecute = healthEvents.copy(
            eventType = HealthEventTypeEnum.PS,
            executedAt = null
        )
        private val invalidType = healthEvents.copy(
            eventType = HealthEventTypeEnum.EXAM,
            executedAt = nowDate
        )

        @Test
        fun `#handlePsByEventinder should not producer PsExecutedEvent when type is not PS`() = runBlocking {
            val event = HealthEventExecutedEvent(
                payload = invalidType
            )

            val result = consumer.handlePsByEventinder(event)
            assertThat(result).isSuccess()

            coVerifyNone { healthPlanTaskService.findByPersonAndFilters(any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any(), any()) }

        }

        @Test
        fun `#handlePsByEventinder should not producer PsExecutedEvent when executed_at is null`() = runBlocking {
            val event = HealthEventExecutedEvent(
                payload = invalidExecute
            )

            val result = consumer.handlePsByEventinder(event)
            assertThat(result).isSuccess()

            coVerifyNone { healthPlanTaskService.findByPersonAndFilters(any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any(), any()) }
        }
    }


}
