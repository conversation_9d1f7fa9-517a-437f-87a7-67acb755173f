package br.com.alice.ehr.services

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.LaboratoryTestResult
import br.com.alice.data.layer.services.LaboratoryTestResultModelDataService
import br.com.alice.ehr.converters.toModel
import br.com.alice.ehr.model.UpdateLaboratoryTestResult
import com.github.kittinunf.result.Result
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class LaboratoryTestResultServiceImplTest {

    private val laboratoryTestResultDataService: LaboratoryTestResultModelDataService = mockk()
    private val laboratoryTestResultService = LaboratoryTestResultServiceImpl(laboratoryTestResultDataService)

    @Test
    fun `#update should partial update`() = runBlocking<Unit> {
        val laboratoryTestResult = LaboratoryTestResult(
            personId = PersonId(),
            performedAt = LocalDateTime.parse("2020-03-11T00:00"),
            key = LaboratoryTestResult.Key.ACIDO_URICO,
            otherName = null,
            value = "10"
        )

        val updatedLaboratoryTestResult = LaboratoryTestResult(
            personId = PersonId(),
            performedAt = LocalDateTime.parse("2020-03-11T00:00"),
            key = LaboratoryTestResult.Key.ACIDO_URICO,
            otherName = null,
            value = "12"
        )

        val update = UpdateLaboratoryTestResult(
            otherName = null,
            value = "12"
        )

        coEvery {
            laboratoryTestResultDataService.get(laboratoryTestResult.id)
        } returns Result.success(laboratoryTestResult.toModel())

        coEvery {
            laboratoryTestResultDataService.update(match { it.id == laboratoryTestResult.id && it.value == "12" })
        } returns Result.success(updatedLaboratoryTestResult.toModel())

        val result = laboratoryTestResultService.update(laboratoryTestResult.id.toString(), update)
        assertThat(result is Result.Success).isTrue
        assertThat(result.get()).isEqualTo(updatedLaboratoryTestResult)
    }
}
