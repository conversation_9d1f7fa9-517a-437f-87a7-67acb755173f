package br.com.alice.ehr.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.PersonAdditionalInfoModelDataService
import br.com.alice.ehr.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PersonAdditionalInfoServiceImplTest {

    private val personAdditionalInfoDataService: PersonAdditionalInfoModelDataService = mockk()
    private val personAdditionalInfoService =
        PersonAdditionalInfoServiceImpl(personAdditionalInfoDataService)

    private val personIdMock = PersonId()
    private val personAdditionalInfo = TestModelFactory.buildPersonAdditionalInfo(personIdMock)

    @Test
    fun `#getOrCreate should return the PersonAdditionalInfo if exists`() = runBlocking {
        coEvery {
            personAdditionalInfoDataService.findOneOrNull(queryEq { where { this.personId.eq(personIdMock) } })
        } returns personAdditionalInfo.toModel()

        val result = personAdditionalInfoService.getOrCreate(personIdMock)
        assertThat(result).isSuccessWithData(personAdditionalInfo)

        coVerify(exactly = 0) { personAdditionalInfoDataService.add(any()) }
    }

    @Test
    fun `#getOrCreate should return a new PersonAdditionalInfo if not exists`() = runBlocking {
        coEvery {
            personAdditionalInfoDataService.findOneOrNull(queryEq { where { this.personId.eq(personIdMock) } })
        } returns null

        coEvery {
            personAdditionalInfoDataService.add(match { it.personId == personIdMock })
        } returns personAdditionalInfo.toModel().success()

        val result = personAdditionalInfoService.getOrCreate(personIdMock)
        assertThat(result).isSuccessWithData(personAdditionalInfo)

        coVerify(exactly = 1) { personAdditionalInfoDataService.add(any()) }
    }

    @Test
    fun `#update should update the PersonAdditionalInfo`() = runBlocking {
        coEvery {
            personAdditionalInfoDataService.update(personAdditionalInfo.toModel())
        } returns personAdditionalInfo.toModel().success()

        val result = personAdditionalInfoService.update(personAdditionalInfo)
        assertThat(result).isSuccessWithData(personAdditionalInfo)

        coVerify(exactly = 1) { personAdditionalInfoDataService.update(any()) }
    }

    @Test
    fun `#findByIds should find info by ids and return the list`() = runBlocking {
        val ids = listOf(personIdMock)
        val expected = listOf(personAdditionalInfo)

        coEvery {
            personAdditionalInfoDataService.find(queryEq { where { this.personId.inList(ids) } })
        } returns listOf(personAdditionalInfo.toModel()).success()

        val result = personAdditionalInfoService.findByIds(ids)
        assertThat(result).isSuccessWithData(expected)
    }
}
