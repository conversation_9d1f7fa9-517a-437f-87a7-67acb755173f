package br.com.alice.ehr.services.internal.product_enrichment

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithBundles
import br.com.alice.product.model.ProductWithProviders
import br.com.alice.provider.client.ProviderService
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ProductWithProvidersServiceTest {
    private val productService: ProductService = mockk()
    private val providerService: ProviderService = mockk()

    private val productWithProvidersService = ProductWithProvidersService(
        productService,
        providerService
    )

    private val hospital1 = TestModelFactory.buildProvider(name = "Hospital1", type = ProviderType.HOSPITAL)
    private val hospital2 = TestModelFactory.buildProvider(name = "Hospital2", type = ProviderType.HOSPITAL)
    private val maternity = TestModelFactory.buildProvider(name = "Maternity", type = ProviderType.MATERNITY)
    private val laboratory1 = TestModelFactory.buildProvider(name = "Laboratory1", type = ProviderType.LABORATORY)
    private val laboratory2 = TestModelFactory.buildProvider(name = "Laboratory2", type = ProviderType.LABORATORY)
    private val specialtyTiers = SpecialtyTiers(tiers = listOf(SpecialistTier.TALENTED), RangeUUID.generate())
    private val hospitalBundle1 = TestModelFactory.buildProductBundle(
        type = ProductBundleType.HOSPITAL,
        providerIds = listOf(hospital1.id)
    )
    private val hospitalBundle2 = TestModelFactory.buildProductBundle(
        type = ProductBundleType.HOSPITAL,
        providerIds = listOf(hospital2.id)
    )
    private val maternityBundle = TestModelFactory.buildProductBundle(
        type = ProductBundleType.MATERNITY,
        providerIds = listOf(maternity.id)
    )
    private val laboratoryBundle1 = TestModelFactory.buildProductBundle(
        type = ProductBundleType.LABORATORY,
        providerIds = listOf(laboratory1.id)
    )
    private val laboratoryBundle2 = TestModelFactory.buildProductBundle(
        type = ProductBundleType.LABORATORY,
        providerIds = listOf(laboratory2.id)
    )
    private val specialtyTiersBundle = TestModelFactory.buildProductBundle(
        type = ProductBundleType.SPECIALITY_TIERS,
        specialtyTiers = listOf(specialtyTiers)
    )
    private val product1 = TestModelFactory.buildProduct(
        bundleIds = listOf(hospitalBundle1.id, maternityBundle.id, laboratoryBundle1.id, specialtyTiersBundle.id)
    )
    private val product2 = TestModelFactory.buildProduct(
        bundleIds = listOf(hospitalBundle2.id, maternityBundle.id, laboratoryBundle2.id, specialtyTiersBundle.id)
    )

    private val product1WithBundles = ProductWithBundles(
        product = product1,
        bundles = listOf(hospitalBundle1, maternityBundle, laboratoryBundle1, specialtyTiersBundle)
    )

    private val product2WithBundles = ProductWithBundles(
        product = product2,
        bundles = listOf(hospitalBundle2, maternityBundle, laboratoryBundle2, specialtyTiersBundle)
    )

    @Test
    fun `#findByIdsWithProviders should return product with providers`() = runBlocking {
        coEvery {
            productService.findByIdsWithBundles(listOf(product1.id, product2.id))
        } returns listOf(product1WithBundles, product2WithBundles).success()

        coEvery {
            providerService.getByIds(listOf(hospital1.id, maternity.id, laboratory1.id, hospital2.id, laboratory2.id))
        } returns listOf(hospital1, maternity, laboratory1, hospital2, laboratory2).success()

        val result = productWithProvidersService.findByIdsWithProviders(listOf(product1.id, product2.id))

        assertThat(result).isSuccessWithData(
            listOf(
                ProductWithProviders(product1, listOf(hospital1, maternity, laboratory1)),
                ProductWithProviders(product2, listOf(hospital2, maternity, laboratory2))
            )
        )
    }

    @Test
    fun `#get returns product by id`() = runBlocking {
        coEvery { productService.getProduct(product1.id) } returns product1.success()

        val result = productWithProvidersService.get(product1.id)
        assertThat(result).isSuccessWithData(product1)

        coVerifyOnce { productService.getProduct(any()) }
        coVerify { providerService wasNot Called }
    }
}
