package br.com.alice.ehr.clients.memed

import br.com.alice.common.serialization.gson
import com.google.gson.annotations.SerializedName
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText

private const val CONTINUOUS_USE_MEDICINE = "Uso Contínuo"

class MemedPrescriptionResponse(
    private val response: HttpResponse,
) : MemedResponse<MemedPrescription>() {
    override suspend fun deserialize(): MemedPrescription =
        gson.fromJson(
            response.bodyAsText(),
            MemedPrescription::class.java,
        )
}

data class MemedPrescription(
    val data: MemedPrescriptionData,
) {
    fun medicines() = data.attributes.medicines
}

data class MemedPrescriptionData(
    val attributes: MemedPrescriptionAttributes,
)

data class MemedPrescriptionAttributes(
    val createdAt: String,
    @SerializedName("prescriptionDateOriginal")
    val prescriptionDate: String,
    @SerializedName("medicamentos")
    val medicines: List<MemedPrescriptionMedicine>,
)

data class MemedPrescriptionMedicine(
    @SerializedName("nome")
    val name: String,
    @SerializedName("descricao")
    val description: String?,
    @SerializedName("posologia")
    val posology: String,
    val sanitizedPosology: String,
    @SerializedName("quantidade")
    val quantity: Int,
    val unit: String,
    @SerializedName("controle_especial")
    val special: Boolean,
    @SerializedName("receituario_id")
    val receituarioId: String?,
) {
    fun continuousUse() = unit.equals(CONTINUOUS_USE_MEDICINE, ignoreCase = true)
}
