package br.com.alice.ehr.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.map
import br.com.alice.ehr.model.ThirdPartyAppointment
import br.com.alice.ehr.model.ThirdPartyAppointmentKind
import br.com.alice.ehr.model.ThirdPartyAppointmentPlace
import br.com.alice.ehr.model.ThirdPartyAssistanceCareDetails
import br.com.alice.einsteinintegrationclient.models.EinsteinAppointment
import java.time.LocalDateTime
import java.time.LocalTime

object EinsteinAppointmentConverter : Converter<EinsteinAppointment,
    ThirdPartyAppointment>(EinsteinAppointment::class, ThirdPartyAppointment::class) {

    fun convert(source: EinsteinAppointment) =
        convert(
            source,
            map(ThirdPartyAppointment::id) from source.einsteinCode,
            map(ThirdPartyAppointment::place) from "${ThirdPartyAppointmentPlace.EINSTEIN} - ${source.local}",
            map(ThirdPartyAppointment::kind) from ThirdPartyAppointmentKind.EMERGENCY,
            map(ThirdPartyAppointment::createdAt) from source.addedAt,
            map(ThirdPartyAppointment::departureDate) from getDepartureData(source),
            map(ThirdPartyAppointment::entryDate) from source.appointmentDate.atTime(LocalTime.NOON),
            map(ThirdPartyAppointment::assistanceDetails) from buildAssistanceDetails(source)
        )

    private fun getDepartureData(source: EinsteinAppointment): LocalDateTime {
        val exitDateEmergencyRoom = source.exitDatePA ?: source.appointmentDate
        return exitDateEmergencyRoom.atBeginningOfTheDay()
    }

    private fun buildAssistanceDetails(source: EinsteinAppointment) =
        ThirdPartyAssistanceCareDetails(
            outcome = "${source.emergencyOutcome ?: source.emergencyOutcome}\nProfissional: ${source.professionals.joinToString("|") {
                "Nome: ${it.nomeProfissional} - Ocupação: ${it.ocupacao}"
            }}",
            personSubjectiveStatement = source.reasons.joinToString("\n") { it.fullDescription() },
            assistanceDateTime = source.appointmentDate.toBrazilianDateFormat(),
            diagnosticReports = source.diagnostics.map { it.fullDiagnostic() },
            procedures = source.procedures.map { it.procedimentoRealizado },
            plans = source.referrals.map { "Encaminhamento: ${it.encaminhamentoEspecialidade}" } +
                source.medicines.map { "Medicamento: ${it.fullDescription()}" },
            assistanceReason = source.origin
        )
}
