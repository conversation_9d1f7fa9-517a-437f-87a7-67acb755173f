package br.com.alice.ehr.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.DraftCommand
import br.com.alice.data.layer.models.DraftCommandModel
import br.com.alice.data.layer.models.HealthCommunityUnreferencedAccess
import br.com.alice.data.layer.models.HealthCommunityUnreferencedAccessModel
import br.com.alice.data.layer.models.Pregnancy
import br.com.alice.data.layer.models.PregnancyModel

object HealthCommunityUnreferencedAccessConverter : Converter<HealthCommunityUnreferencedAccessModel, HealthCommunityUnreferencedAccess>(
    HealthCommunityUnreferencedAccessModel::class,
    HealthCommunityUnreferencedAccess::class,
)

fun HealthCommunityUnreferencedAccess.toModel() = HealthCommunityUnreferencedAccessConverter.unconvert(this)
fun HealthCommunityUnreferencedAccessModel.toTransport() = HealthCommunityUnreferencedAccessConverter.convert(this)
