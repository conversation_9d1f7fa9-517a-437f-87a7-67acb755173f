package br.com.alice.ehr.services

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.ClinicalBackgroundStatus
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.data.layer.services.ClinicalBackgroundDataService
import br.com.alice.data.layer.services.DraftCommandModelDataService
import br.com.alice.ehr.client.ClinicalBackgroundFilters
import br.com.alice.ehr.client.ClinicalBackgroundService
import br.com.alice.ehr.event.ClinicalBackgroundUpdatedEvent
import br.com.alice.ehr.services.internal.appointments.draft.CurrentDraftAppointmentFetcher
import br.com.alice.ehr.services.internal.appointments.draft.CurrentDraftAppointmentFetcherImpl
import br.com.alice.ehr.services.internal.clinical_backgrounds.ClinicalBackgroundInMemoryStateValidatorImpl
import br.com.alice.ehr.services.internal.clinical_backgrounds.ClinicalBackgroundStateValidatorImpl
import br.com.alice.ehr.services.internal.clinical_backgrounds.DraftClinicalBackgroundCreator
import br.com.alice.ehr.services.internal.clinical_backgrounds.DraftClinicalBackgroundUpdater
import br.com.alice.ehr.services.internal.draft_commands.DraftCommandsCreatorImpl
import br.com.alice.ehr.services.internal.draft_commands.InMemoryClinicalBackgroundCommandsApplier
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDateTime
import java.util.UUID

class ClinicalBackgroundServiceImpl(
    private val clinicalBackgroundDataService: ClinicalBackgroundDataService,
    private val draftCommandDataService: DraftCommandModelDataService,
    private val appointmentService: AppointmentService,
    private val kafkaProducerService: KafkaProducerService
) : ClinicalBackgroundService {

    override suspend fun add(
        model: ClinicalBackground
    ): Result<ClinicalBackground, Throwable> {
        return validateState(model).map {
            clinicalBackgroundDataService.add(it).get()
        }.then {
            kafkaProducerService.produce(ClinicalBackgroundUpdatedEvent(it), it.personId.toString())
        }
    }

    override suspend fun addDraft(
        model: ClinicalBackground,
        staffId: UUID,
    ): Result<ClinicalBackground, Throwable> = Result.of {
        val draftFetcher = CurrentDraftAppointmentFetcherImpl(
            appointmentService, staffId, model.personId
        )

        val currentBackgrounds = getCurrentClinicalBackgrounds(
            model.personId, draftFetcher
        )

        val commandsCreator = DraftCommandsCreatorImpl(
            draftCommandDataService, draftFetcher
        )
        val validator = ClinicalBackgroundInMemoryStateValidatorImpl(
            model, currentBackgrounds
        )
        val creator = DraftClinicalBackgroundCreator(
            model, validator, commandsCreator,
        )

        creator.create()
    }

    private suspend fun getCurrentClinicalBackgrounds(
        personId: PersonId,
        draftFetcher: CurrentDraftAppointmentFetcher,
    ): List<ClinicalBackground> {
        val backgrounds = clinicalBackgroundDataService.find {
            where { this.personId.eq(personId) }
        }.get()

        return InMemoryClinicalBackgroundCommandsApplier(
            backgrounds,
            draftCommandDataService,
            draftFetcher,
        ).apply()
    }

    override suspend fun inactivate(
        personId: PersonId,
        id: UUID,
        staffId: UUID,
    ): Result<ClinicalBackground, Throwable> {
        return Result.of {
            val draftFetcher = CurrentDraftAppointmentFetcherImpl(
                appointmentService, staffId, personId
            )
            val commandsCreator = DraftCommandsCreatorImpl(
                draftCommandDataService, draftFetcher
            )

            val background = getCurrentClinicalBackgrounds(
                personId, draftFetcher
            ).find { it.id == id } ?: throw NotFoundException("background not found")

            DraftClinicalBackgroundUpdater(
                background,
                staffId,
                commandsCreator,
            ).inactive()
        }
    }

    override suspend fun getByPersonId(
        personId: PersonId,
        staffId: UUID?,
    ): Result<List<ClinicalBackground>, Throwable> = Result.of {
        var backgrounds = clinicalBackgroundDataService.find {
            where { this.personId.eq(personId) }
        }.get()

        if (staffId != null) {
            val draftFetcher = CurrentDraftAppointmentFetcherImpl(
                appointmentService, staffId, personId
            )

            backgrounds = InMemoryClinicalBackgroundCommandsApplier(
                backgrounds,
                draftCommandDataService,
                draftFetcher,
            ).apply()
        }

        backgrounds.filter { it.status == ClinicalBackgroundStatus.ACTIVE }
    }

    override suspend fun getByPersonIdWithFilters(
        personId: PersonId,
        filters: ClinicalBackgroundFilters
    ): Result<List<ClinicalBackground>, Throwable> =
        clinicalBackgroundDataService.find {
            where {
                this.personId.eq(personId) and
                        filters.types?.let { this.type.inList(it) } and
                        filters.status?.let { this.status.eq(it) } and
                        filters.denied?.let { this.denied.eq(it) }
            }
        }

    override suspend fun getActivesAtDate(
        personId: PersonId,
        date: LocalDateTime,
        staffId: UUID?,
    ): Result<List<ClinicalBackground>, Throwable> = Result.of {
        var backgrounds = clinicalBackgroundDataService.find {
            where {
                this.personId.eq(personId) and
                        this.type.inList(ClinicalBackgroundType.values().asList())
            }
        }.get()

        if (staffId != null) {
            val draftFetcher = CurrentDraftAppointmentFetcherImpl(
                appointmentService, staffId, personId
            )

            backgrounds = InMemoryClinicalBackgroundCommandsApplier(
                backgrounds,
                draftCommandDataService,
                draftFetcher,
            ).apply()
        }

        backgrounds.filter {
            val activeAtDate = it.deletedAt?.isAfter(date) ?: true

            it.addedAt.isBefore(date) && activeAtDate
        }
    }

    private suspend fun validateState(
        model: ClinicalBackground,
    ): Result<ClinicalBackground, Throwable> {
        return Result.of {
            ClinicalBackgroundStateValidatorImpl(
                model, clinicalBackgroundDataService
            ).validate()

            model
        }
    }
}
