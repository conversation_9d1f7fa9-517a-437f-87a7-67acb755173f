package br.com.alice.ehr.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.NO_RISK_FLOW
import br.com.alice.data.layer.models.Cpt
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.GracePeriod
import br.com.alice.data.layer.models.GracePeriodType.TOTAL_EXEMPTION
import br.com.alice.data.layer.models.HealthConditionCodeType.CID_10
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonGracePeriod
import br.com.alice.data.layer.models.Product
import br.com.alice.ehr.client.GracePeriods
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.logics.MemberCptsLogics
import br.com.alice.ehr.model.CptCondition
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.GracePeriodType
import br.com.alice.ehr.model.HealthInstitution
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.ehr.services.internal.product_enrichment.ProductWithProvidersService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.membership.client.onboarding.GracePeriodService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.logics.MemberServiceLogics.filterByPriority
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate

class MemberCptsServiceImpl(
    private val memberService: MemberService,
    private val gracePeriodService: GracePeriodService,
    private val personService: PersonService,
    private val healthConditionService: HealthConditionService,
    private val productService: ProductWithProvidersService,
    private val beneficiaryService: BeneficiaryService
) : MemberCptsService {

    private val baseGracePeriods = GracePeriods()

    override suspend fun buildPersonCpts(person: Person): Result<MemberCpt, Throwable> = coroutineScope {
        logger.info(
            "Start build cpt and gracePeriod for person",
            "person_id" to person.id
        )

        val (members, currentMember) = findMembershipsByPerson(person)

        val productDeferred = async { currentMember?.productId?.let { productService.get(it).getOrNullIfNotFound() } }

        gracePeriodService.getGracePeriod(person.id, currentMember).map { gracePeriod ->
            val activationDateForCpt = currentMember?.getActivationDateForCpt(members)

            val (validMembershipsForGracePeriod, activationDateForGracePeriod) = currentMember.getMembershipsAndActivationDateForGracePeriod(
                members
            )

            val (gracePeriods, baseDate) = getGraceBasePeriods(person, gracePeriod)

            MemberCpt(
                conditions = buildConditions(
                    gracePeriod,
                    baseDate ?: activationDateForCpt,
                    currentMember,
                    gracePeriods
                ),
                gracePeriod = buildGracePeriod(
                    currentMember,
                    productDeferred.await(),
                    person,
                    baseDate ?: activationDateForGracePeriod,
                    gracePeriods
                ),
                healthInstitutions = buildHealthInstitutions(
                    validMembershipsForGracePeriod,
                    currentMember
                ),
                baseGracePeriods = gracePeriods
            ).also {
                logger.info(
                    "Cpt and gracePeriod built for person",
                    "person_id" to person.id,
                    "cpt_and_grace_period" to it.toString()
                )
            }
        }.coFoldNotFound {
            logger.warn(
                "Error getting gracePeriod for person. Returning empty cpt_and_grace_period",
                "person_id" to person.id
            )
            MemberCpt().success()
        }
    }

    private suspend fun getGraceBasePeriods(person: Person, gracePeriod: GracePeriod) =
        when {
            person.productInfo?.isB2BOrAdesao() == true -> getBeneficiaryGracePeriodType(person.id)
            gracePeriod.portabilityRequest?.approved == true -> TOTAL_EXEMPTION to null
            else -> null to null
        }.let { (type, baseDate) -> baseGracePeriods.getCalculatedPeriodsDiscount(type) to baseDate }

    private suspend fun getBeneficiaryGracePeriodType(personId: PersonId) =
        beneficiaryService.findByPersonId(personId)
            .getOrNullIfNotFound()
            .let { beneficiary ->
                if (beneficiary?.onboarding?.flowType == NO_RISK_FLOW) TOTAL_EXEMPTION to beneficiary.gracePeriodBaseDate
                else beneficiary?.gracePeriodType to beneficiary?.gracePeriodBaseDate
            }

    private suspend fun buildHealthInstitutions(members: List<Member>?, currentMember: Member?) = members?.let {
        MemberCptsLogics.getEnabledMembershipToCheckHI(members, currentMember)?.let { enabledMembershipsToCheckHI ->
            findProductsWithProviderByMembership(enabledMembershipsToCheckHI).map {
                MemberCptsLogics.getHIsWithStartedAt(enabledMembershipsToCheckHI, it)
            }.map { (startedAt, cnpj) ->
                if (cnpj.isNotEmpty()) {
                    logger.info("health institution", "started_at" to startedAt, "cnpj" to cnpj)

                    listOf(HealthInstitution(startedAt = startedAt, cnpj = cnpj))
                } else emptyList()
            }.get()
        }
    } ?: emptyList()

    private suspend fun findMembershipsByPerson(person: Person) = memberService
        .findByPerson(person.id)
        .map { Pair(it, filterByPriority(it).firstOrNull()) }
        .then { (members, currentMember) ->
            logger.info(
                "List of memberships from person",
                "person_id" to person.id,
                "current_membership_id" to currentMember?.id,
                "membership_ids" to members.map { it.id })
        }
        .get()

    private suspend fun findProductsWithProviderByMembership(members: List<Member>) =
        members.map { it.productId }.let { personIds ->
            productService.findByIdsWithProviders(personIds)
                .then {
                    logger.info(
                        "Found products based in valid memberships for grace period",
                        "product_ids" to personIds,
                        "member_ids" to members.map { it.id },
                    )
                }
                .map { products -> products.associateBy { it.product.id } }
        }

    override suspend fun buildPersonCptsByPersonId(personId: PersonId): Result<MemberCpt, Throwable> =
        personService.get(personId).flatMap { buildPersonCpts(it) }

    private suspend fun buildConditions(
        gracePeriod: GracePeriod,
        activationDate: LocalDate?,
        member: Member?,
        gracePeriods: GracePeriods
    ): List<CptCondition> {
        val healthConditions = gracePeriod.cpts
            .mapNotNull { it.cids.firstOrNull() }
            .takeIf { it.isNotEmpty() }
            ?.let { healthConditionService.findByCodesAndType(it, CID_10).get() }
            ?: emptyList()

        return gracePeriod.cpts.pmap { cpt ->
            val cidCode = cpt.cids.firstOrNull()

            CptCondition(
                name = cpt.condition,
                cid = cpt.cids.joinToString(),
                healthCondition = healthConditions.find { it.code == cidCode },
                validUntil = buildValidUntilCpts(
                    member = member,
                    conditionValidUntil = activationDate?.plusDays(gracePeriods.cptInDays),
                    personGracePeriod = gracePeriod.personGracePeriods.find { it.value == cidCode },
                    cpt = cpt
                ),
                baseDate = activationDate ?: LocalDate.now(),
                periodInDays = gracePeriods.cptInDays
            )
        }
    }

    private fun buildValidUntilCpts(
        member: Member?,
        conditionValidUntil: LocalDate?,
        personGracePeriod: PersonGracePeriod?,
        cpt: Cpt,
    ): String =
        if (member?.isDuquesa == true)
            personGracePeriod?.endDate?.toBrazilianDateFormat()
                ?: throw Exception("Member is duquesa, but there is no person_grace_period with cid=${cpt.cids}, personId=${member.personId}")
        else conditionValidUntil
            ?.toBrazilianDateFormat()
            .orEmpty()

    private fun buildGracePeriod(
        member: Member?,
        product: Product?,
        person: Person,
        activationDate: LocalDate?,
        gracePeriods: GracePeriods,
    ): List<CptGracePeriod> {
        if (member?.isDuquesa == true) return emptyList()

        val now = LocalDate.now()

        return listOfNotNull(
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.ELECTIVE_SURGERY,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.surgeryInDays,
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.BIRTH,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.birthInDays,
                validateFemale = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.THERAPY,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.therapyInDays,
                validateNewPortfolio = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.SPECIAL_EXAMS,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.especialExamsInDays,
                validateNewPortfolio = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.PAC,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.pacInDays,
                validateNewPortfolio = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.EMERGENCY,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.emergencyInDays,
                validateNewPortfolio = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.HOSPITALIZATION,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.hospitalizationInDays,
                validateNewPortfolio = true
            ),
            buildCptGracePeriod(
                person = person,
                product = product,
                cpt = GracePeriodType.HOSPITALIZATION_DUE_TO_PERSONAL_ACCIDENT,
                now = now,
                activationDate = activationDate,
                periodInDays = gracePeriods.hospitalizationDueToPersonalAccidentInDays,
                validateNewPortfolio = true
            ),
            gracePeriods.others?.let { periodsInDays ->
                buildCptGracePeriod(
                    person = person,
                    product = product,
                    cpt = GracePeriodType.OTHERS,
                    now = now,
                    activationDate = activationDate,
                    periodInDays = periodsInDays,
                    validateNewPortfolio = true
                )
            }
        )
    }

    private fun buildCptGracePeriod(
        person: Person,
        product: Product?,
        cpt: GracePeriodType,
        now: LocalDate,
        activationDate: LocalDate?,
        periodInDays: Long,
        validateFemale: Boolean = false,
        validateNewPortfolio: Boolean = false
    ): CptGracePeriod? {
        val validSex = if (validateFemale) (person.sex == Sex.FEMALE || person.sex == null) else true
        val customPeriodInDays = if (!product.isNewPortfolio() && validateNewPortfolio) 0 else periodInDays

        val validUnit = activationDate?.plusDays(customPeriodInDays)

        return if (validSex) {
            CptGracePeriod(
                condition = cpt.description,
                validUntil = validUnit?.toBrazilianDateFormat().orEmpty(),
                baseDate = activationDate ?: now,
                type = cpt,
                periodInDays = customPeriodInDays
            )
        } else null
    }

    private fun Product?.isNewPortfolio() =
        !FeatureService.inList(
            namespace = FeatureNamespace.EHR,
            key = "old_portfolio_product_ans_numbers",
            testValue = (this?.ansNumber ?: ""),
            defaultReturn = false
        )

    private fun Member?.getActivationDateForCpt(members: List<Member>) =
        if (this?.active == true)
            MemberCptsLogics
                .getReferenceMemberForCpts(members, this)
                ?.activationDate
                ?.toLocalDate()
        else null

    private fun Member?.getMembershipsAndActivationDateForGracePeriod(members: List<Member>): Pair<List<Member>?, LocalDate?> {
        val validMembershipsForGracePeriod = MemberCptsLogics
            .getValidMembershipsForGracePeriod(members, this)

        return validMembershipsForGracePeriod to if (this?.active == true)
            validMembershipsForGracePeriod?.lastOrNull()
                ?.activationDate
                ?.toLocalDate()
        else null
    }


}
