package br.com.alice.ehr.services

import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.ehr.client.MailerService
import br.com.alice.ehr.communication.Mailer
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class MailerServiceImpl(
    private val mailer: Mailer
) : MailerService {

    override suspend fun sendEmail(
        recipientName: String,
        recipientEmail: String,
        replaceVariables: Map<String, String>,
        templateName: String
    ): Result<String, Throwable> {
        val emailAddress = listOf(
            EmailAddress(
                name = recipientName,
                email = recipientEmail
            )
        )

        val emailReceipt = mailer.sendEmail(
            emailAddresses = emailAddress,
            templateName = templateName,
            replaceVariables = replaceVariables
        )

        return emailReceipt.id.success()
    }
}
