package br.com.alice.ehr.services.internal.draft_commands

import br.com.alice.data.layer.models.DraftCommandReferencedModel
import br.com.alice.data.layer.models.Draftable
import java.util.UUID

interface DraftCommandsCreator {
    suspend fun createAddCommand(model: Draftable)

    suspend fun createUpdateCommand(model: Draftable)

    suspend fun createDeleteCommand(
        id: UUID,
        referencedModel: DraftCommandReferencedModel
    )
}
