package br.com.alice.ehr.services.internal.assistance_summary

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldBoolean
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.AssistanceSummary
import br.com.alice.data.layer.models.AssistanceSummaryPregnancy
import br.com.alice.data.layer.models.ComorbidityCondition
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.AssistanceSummaryModel
import br.com.alice.data.layer.models.AssistanceSummaryPregnancyModel
import br.com.alice.data.layer.models.ComorbidityConditionModel
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonCase
import br.com.alice.data.layer.models.Pregnancy
import br.com.alice.data.layer.models.PregnancyOutcomeType
import br.com.alice.data.layer.models.RiskCalculationConf
import br.com.alice.data.layer.services.AssistanceSummaryModelDataService
import br.com.alice.ehr.client.PregnancyService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthcondition.client.PersonCaseService
import br.com.alice.healthlogic.client.ClinicalOutcomeService
import br.com.alice.marauders.map.client.RiskCalculationConfService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class CreateAssistanceSummaryService(
    private val assistanceSummaryDataService: AssistanceSummaryModelDataService,
    private val riskCalculationConfService: RiskCalculationConfService,
    private val personService: PersonService,
    private val personCaseService: PersonCaseService,
    private val pregnancyService: PregnancyService,
    private val clinicalOutcomeService: ClinicalOutcomeService,
) {

    suspend fun create(personId: PersonId): Result<AssistanceSummaryModel, Throwable> =
        existsByPersonId(personId)
            .foldBoolean(
                { UnsupportedOperationException("Assistance Summary already exists").failure() },
                { save(personId) }
            )

    private suspend fun save(personId: PersonId): Result<AssistanceSummaryModel, Throwable> =
        personService.get(personId)
            .map { person ->
                getAssistanceSummaryData(person)
            }
            .flatMap {
                assistanceSummaryDataService.add(it)
            }

    private suspend fun getAssistanceSummaryData(person: Person): AssistanceSummaryModel = coroutineScope {
        val conditionsDef = async { getComorbidityConditions(person).get() }
        val pregnancyDef = async { getPregnancy(person).getOrNullIfNotFound() }
        val imcDef = async { getImc(person).getOrNullIfNotFound() }

        AssistanceSummaryModel(
            personId = person.id,
            comorbiditiesConditions = conditionsDef.await(),
            pregnancy = pregnancyDef.await(),
            imc = imcDef.await()
        )
    }

    private suspend fun getPregnancy(person: Person) =
        pregnancyService.getCurrent(person.id)
            .map { it.toAssistancePregnancy() }

    private suspend fun getImc(person: Person) =
        clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(person.id, outcomeConfIdIMC())
            .map { outcomeRecord ->
                outcomeRecord.outcome
            }

    private suspend fun getComorbidityConditions(person: Person): Result<List<ComorbidityConditionModel>, Throwable> =
        personCaseService.getByPersonId(person.id)
            .map { cases ->
                cases.filter { it.isCID() }
            }
            .flatMapPair { cases ->
                val conditionIds = cases.map { it.healthConditionId }.distinct()
                getRisksByPersonCases(conditionIds)
            }.map { (risks, cases) ->
                buildComorbidityConditions(cases, risks, person)
            }

    private suspend fun getRisksByPersonCases(conditionIds: List<UUID>) =
        riskCalculationConfService.getByHealthConditions(conditionIds)
            .map { risks -> risks.associateBy { it.healthConditionId } }

    private suspend fun existsByPersonId(personId: PersonId) =
        assistanceSummaryDataService.exists {
            where {
                this.personId.eq(personId)
            }
        }

    private fun buildComorbidityConditions(
        cases: List<PersonCase>,
        risks: Map<UUID, RiskCalculationConf>,
        person: Person
    ) = cases.filter { personCase ->
        risks[personCase.healthConditionId]?.let { risk ->
            !risk.noScore(isAdult = person.age > 12)
        } ?: false
    }.map { personCase ->
        personCase.toComorbidityCondition()
    }

    private fun Pregnancy.toAssistancePregnancy() =
        AssistanceSummaryPregnancyModel(
            id = id,
            createdAt = updatedAt,
            lastPeriodDate = lastPeriodDate,
            firstUsgDate = firstUsgDate,
            usgEstimatedGestationalAgeInDays = usgEstimatedGestationalAgeInDays,
            outcomeDate = outcomeDate,
            outcomeType = outcomeType
        )

    private fun PersonCase.toComorbidityCondition() = ComorbidityConditionModel(
        healthConditionId = healthConditionId,
        caseId = id,
        addedAt = addedAt,
        description = DiseaseDetails(
            id = healthConditionId.toString(),
            type = codeType,
            value = codeValue,
            description = codeDescription
        ),
        seriousness = seriousness,
        severity = severity,
        startedAt = startedAt,
        responsibleStaffId = responsibleStaffId ?: throw IllegalArgumentException("responsible_staff_id is null"),
    )

    private fun PersonCase.isCID() = codeType == Disease.Type.CID_10

    private fun outcomeConfIdIMC(): UUID =
        FeatureService.get(
            namespace = FeatureNamespace.EHR,
            key = "outcome_config_id_of_imc",
            defaultValue = "4faf5b2e-1b96-44a6-a8bb-31930c948400"
        ).toUUID()
}
