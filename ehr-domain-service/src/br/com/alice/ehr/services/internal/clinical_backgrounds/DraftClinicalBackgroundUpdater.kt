package br.com.alice.ehr.services.internal.clinical_backgrounds

import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.ClinicalBackgroundStatus
import br.com.alice.data.layer.models.copy
import br.com.alice.ehr.services.internal.draft_commands.DraftCommandsCreator
import java.time.LocalDateTime
import java.util.UUID

class DraftClinicalBackgroundUpdater(
    private val background: ClinicalBackground,
    private val staffId: UUID,
    private val commandsCreator: DraftCommandsCreator,
) {
    private val inactiveModel by lazy {
        background.copy(
            status = ClinicalBackgroundStatus.INACTIVE,
            deletedByStaffId = staffId,
            deletedAt = LocalDateTime.now(),
        )
    }

    suspend fun inactive(): ClinicalBackground {
        commandsCreator.createUpdateCommand(inactiveModel)

        return inactiveModel
    }
}
