package br.com.alice.ehr.services

import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.ehr.client.PersonClinicalAccountStaffService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class PersonClinicalAccountStaffServiceImpl(
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val staffService: StaffService,
) : PersonClinicalAccountStaffService {

    override suspend fun assignMultiStaff(referral: Referral): Result<PersonClinicalAccount, Throwable> =
        personClinicalAccountService.getByPersonId(referral.personId).flatMap { clinicalAccount ->
            val specialist = referral.suggestedSpecialist ?: return clinicalAccount.success()

            if (specialist.type != SpecialistType.STAFF) {
                return clinicalAccount.success()
            }

            val specialistId = specialist.id

            val staff = staffService.get(specialistId).get()
            val validStaff = staff.isFromMultiTeam() || staff.isCommunity()

            if (!validStaff || clinicalAccount.multiStaffIds.contains(specialistId)) {
                return clinicalAccount.success()
            }

            personClinicalAccountService.addStaffOnMultiTeam(referral.personId, staff)
        }
}
