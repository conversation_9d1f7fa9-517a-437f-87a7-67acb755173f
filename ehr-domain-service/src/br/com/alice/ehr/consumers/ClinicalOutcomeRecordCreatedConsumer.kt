package br.com.alice.ehr.consumers

import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ClinicalOutcomeRecord
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.ehr.client.HealthMeasurementService
import br.com.alice.ehr.converters.ClinicalOutcomeToHealthMeasurementConverter
import br.com.alice.ehr.services.internal.assistance_summary.AssistanceSummaryBMIService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.OutcomeConfService
import br.com.alice.healthlogic.event.ClinicalOutcomeRecordCreatedEvent
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class ClinicalOutcomeRecordCreatedConsumer(
    private val healthMeasurementService: HealthMeasurementService,
    private val outcomeConfService: OutcomeConfService,
    private val assistanceSummaryBMIService: AssistanceSummaryBMIService
) : Consumer() {
    suspend fun createHealthMeasurement(
        event: ClinicalOutcomeRecordCreatedEvent
    ) = withSubscribersEnvironment {
        val clinicalOutcomeRecord = event.payload.clinicalOutcomeRecord
        val outcomeConf = getOutcomeConf(clinicalOutcomeRecord.outcomeConfId)

        logger.info(
            "ClinicalOutcomeRecordCreatedConsumer.createHealthMeasurement - event received",
            "person_id" to clinicalOutcomeRecord.personId,
            "outcome" to clinicalOutcomeRecord.outcome,
            "outcome_conf_id" to clinicalOutcomeRecord.outcomeConfId,
        )

        if (outcomeConf.healthMeasurementTypeId == null)
            return@withSubscribersEnvironment true.success()

        clinicalOutcomeRecord.referencedLinks.firstOrNull {
            it.model == ClinicalOutcomeRecord.ReferenceLinkModel.HEALTH_MEASUREMENT
        }?.let {
            logger.info(
                "ClinicalOutcomeRecordCreatedConsumer.createHealthMeasurement" +
                    " - HealthMeasurement already created",
                "health_measurement_id" to it.id
            )
            return@withSubscribersEnvironment true.success()
        }

        val healthMeasurement = ClinicalOutcomeToHealthMeasurementConverter.convert(
            source = clinicalOutcomeRecord,
            typeId = outcomeConf.healthMeasurementTypeId!!
        )
        healthMeasurementService.addToHistory(healthMeasurement)
            .foldError(
                DuplicatedItemException::class to { healthMeasurement.success() }
            )
    }

    suspend fun upsertInAssistanceSummary(event: ClinicalOutcomeRecordCreatedEvent) = withSubscribersEnvironment {
        val outcomeRecord = event.payload.clinicalOutcomeRecord

        logger.info(
            "ClinicalOutcomeRecordCreatedConsumer received ClinicalOutcomeRecordCreatedEvent to upsert BMI data in AssistanceSummary",
            "person_id" to outcomeRecord.personId,
            "outcomeRecord_id" to outcomeRecord.id,
            "outcome_conf_id" to outcomeRecord.outcomeConfId
        )

        if (outcomeRecord.outcomeConfId != outcomeConfIdIMC()) return@withSubscribersEnvironment false.success()

        assistanceSummaryBMIService.upsert(outcomeRecord)
            .map { true }
            .thenError {
            logger.error(
                "ClinicalOutcomeRecordCreatedConsumer::upsertInAssistanceSummary - error to process event",
                "person_id" to outcomeRecord.personId,
                "outcomeRecord_id" to outcomeRecord.id,
                "outcome_conf_id" to outcomeRecord.outcomeConfId,
                it
            )
        }
    }

    private suspend fun getOutcomeConf(outcomeConfId: UUID) =
        outcomeConfService.get(outcomeConfId).get()

    private fun outcomeConfIdIMC(): UUID =
        FeatureService.get(
            namespace = FeatureNamespace.EHR,
            key = "outcome_config_id_of_imc",
            defaultValue = "4faf5b2e-1b96-44a6-a8bb-31930c948400"
        ).toUUID()
}
