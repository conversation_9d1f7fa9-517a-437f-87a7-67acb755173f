package br.com.alice.ehr.consumers

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.common.asyncLayer
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.services.ClinicalBackgroundDataService
import br.com.alice.data.layer.services.DraftCommandModelDataService
import br.com.alice.data.layer.services.HealthMeasurementModelDataService
import br.com.alice.data.layer.services.PregnancyModelDataService
import br.com.alice.ehr.services.internal.draft_commands.DraftCommandsExecutor
import com.github.kittinunf.result.Result

class DraftAppointmentFinishedConsumer(
    private val draftCommandDataService: DraftCommandModelDataService,
    private val pregnancyDataService: PregnancyModelDataService,
    private val healthMeasurementDataService: HealthMeasurementModelDataService,
    private val clinicalBackgroundDataService: ClinicalBackgroundDataService,
    private val eventProducer: KafkaProducerService,
) : Consumer() {

    suspend fun consume(
        event: AppointmentCompletedEvent
    ): Result<Any, Throwable> = asyncLayer {
        val appointmentId = event.payload.appointment.id

        return@asyncLayer withSubscribersEnvironment {
            Result.of {
                DraftCommandsExecutor(
                    appointmentId,
                    draftCommandDataService,
                    pregnancyDataService,
                    healthMeasurementDataService,
                    clinicalBackgroundDataService,
                    eventProducer,
                ).call()
            }
        }
    }
}
