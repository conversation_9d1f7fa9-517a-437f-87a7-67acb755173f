package br.com.alice.ehr.consumers

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.Referral
import br.com.alice.ehr.client.PersonClinicalAccountStaffService
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success

class ReferralUpsertedConsumer(
    private val accountStaffService: PersonClinicalAccountStaffService,
) : Consumer() {
    suspend fun assignMultiStaff(event: HealthPlanTaskUpsertedEvent) = withSubscribersEnvironment {
        val task = event.payload.task

        if (task.type != HealthPlanTaskType.REFERRAL) return@withSubscribersEnvironment true.success()

        val referral = task.specialize<Referral>()

        accountStaffService.assignMultiStaff(referral)
            .flatMapError {
                resultOf {
                    when (it) {
                        is NotFoundException,
                        is InvalidArgumentException -> true
                        else -> throw it
                    }
                }
            }.thenError {
                logger.error(
                    "ReferralUpsertedConsumer.assignMultiStaff: error to process event",
                    "task_id" to task.id,
                    it
                )
            }
    }
}
