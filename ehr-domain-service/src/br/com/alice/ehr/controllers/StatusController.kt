package br.com.alice.ehr.controllers

import br.com.alice.common.Response
import io.ktor.http.HttpStatusCode
import io.ktor.server.netty.NettyApplicationEngine
import br.com.alice.common.controllers.StatusController as BaseStatusController

//TODO: check if still used
class StatusController : BaseStatusController() {

    override suspend fun status(): Response {
//        val ehrDomainServiceStatus = dataLayerStatus()
//        val statusCode = if (ehrDomainServiceStatus.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError

        val message = """
parallelism - ${Runtime.getRuntime().availableProcessors()}
connectionGroupSize - ${NettyApplicationEngine.Configuration().connectionGroupSize}
workerGroupSize - ${NettyApplicationEngine.Configuration().workerGroupSize}
callGroupSize - ${NettyApplicationEngine.Configuration().callGroupSize}
        """

        return Response(HttpStatusCode.OK, message)
    }

//    private suspend fun dataLayerStatus(): Status {
//        return try {
//            staffDataService.findOneOrNull { where { email.eq(testStaffEmail) } }!!
//            Status(true)
//        } catch (e: Throwable) {
//            Status(false, "- ${e::class} - ${e.message}")
//        }
//    }
}
