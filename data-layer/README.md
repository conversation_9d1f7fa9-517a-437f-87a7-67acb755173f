# DataLayer API
Layer responsible for all data access. 

### Responsible Team
Platform
- Find us on ``#eng-platform`` on Slack ;)
- [Platform Notion](https://www.notion.so/alicehealth/Platform-Team-b789312d8f064ce0a599010450ea22d8)

### Local development

Requirements
* [docker](https://www.docker.com) 
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>`` (deprecated, make file tasks moved to root)

* ``clean`` - delete build files
* ``tests`` - run all tests
* ``run`` - run project on 8070 port
* ``db_create`` - creates test & development databases
* ``db_reset`` - drop all databases and re-create them
* ``db_clean`` - clean all tables, views and procedures
* ``db_seed`` - add seed data to development database

Run locally - (deprecated, make file tasks moved to root)
```
make db_create
make run
```
You can access http://localhost:8070
