package br.com.alice.haocintegrationservice.controllers

import br.com.alice.authentication.Authenticator
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HaocClaimProcess
import br.com.alice.data.layer.models.HaocClaimStatus
import br.com.alice.data.layer.models.HaocClaimType
import br.com.alice.data.layer.models.HaocUnit
import br.com.alice.featureconfig.core.FeaturePopulateService
import br.com.alice.haocintegrationservice.ControllerTestHelper
import br.com.alice.haocintegrationservice.module
import br.com.alice.haocintegrationservice.service.HaocClaimService
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class ClaimControllerTest : ControllerTestHelper() {

    private val featurePopulateService: FeaturePopulateService = mockk()

    private val haocClaimService: HaocClaimService = mockk()
    private val claimController = ClaimController(haocClaimService)

    private val claimProcess1 = HaocClaimProcess(
        personId = PersonId(),
        status = HaocClaimStatus.PENDING,
        type = HaocClaimType.HOSPITALIZATION,
        unit = HaocUnit.PAULISTA,
        claimId = 12345
    )
    private val claimProcess2 = HaocClaimProcess(
        personId = PersonId(),
        status = HaocClaimStatus.PENDING,
        type = HaocClaimType.HOSPITALIZATION_EXTENSION,
        unit = HaocUnit.VERGUEIRO,
        claimId = 12346
    )

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module(createdAtStart = true) { single { featurePopulateService } },
                module
            )
        )
    }

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { claimController }

        mockkObject(Authenticator)
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
    }

    @Test
    fun `#processPending should skip when feature flag is disabled`() = runBlocking {
        withFeatureFlag(FeatureNamespace.HAOC, "process_pending", false) {
            postAsPlainText(to = "/process_pending") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyNone { haocClaimService wasNot called }
    }

    @Test
    fun `#processPending should process pending claims`() = runBlocking {
        coEvery { haocClaimService.getPendingClaimProcess() } returns listOf(claimProcess1, claimProcess2).success()
        coEvery { haocClaimService.processAllPending(any()) } returns Unit

        withFeatureFlag(FeatureNamespace.HAOC, "process_pending_claim_chunk_size", 1) {
            postAsPlainText(to = "/process_pending") { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { haocClaimService.processAllPending(any()) }
    }

    @Test
    fun `#reprocessSingleClaim should process claim`() {
        val request =
            ClaimController.ReprocessSingleClaimRequest(UUID.fromString("2234bb17-d13c-4958-ba79-1de94e9b5300"))
        coEvery { haocClaimService.reprocessSingleClaim(request.idClaimProcess) } returns Unit

        post(to = "/reprocess_single_claim", body = request) { response ->
            assertThat(response).isOK()
        }

        coVerify(exactly = 1) { haocClaimService.reprocessSingleClaim(request.idClaimProcess) }
    }
}
