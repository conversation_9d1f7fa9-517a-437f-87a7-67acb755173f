package br.com.alice.haocintegrationservice.consumers

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildMvAuthorizedProcedure
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.HaocClaimProcess
import br.com.alice.data.layer.models.HaocClaimStatus
import br.com.alice.data.layer.models.HaocClaimType
import br.com.alice.data.layer.models.HaocUnit
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthEventOriginEnum
import br.com.alice.data.layer.models.HealthEventTypeEnum
import br.com.alice.data.layer.models.HealthEvents
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.eventinder.events.ProcessedHealthEvent
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.haocintegrationservice.service.HaocClaimService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class ClaimConsumerTest : ConsumerTest() {
    private val service: HaocClaimService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService = mockk()
    private val consumer: ClaimConsumer =
        ClaimConsumer(service, mvAuthorizedProcedureService, execIndicatorAuthorizerService)
    private val personId = PersonId()
    private val executionGroupId = RangeUUID.generate()
    private val payload = HealthEvents(
        id = RangeUUID.generate(),
        personId = personId,
        eventType = HealthEventTypeEnum.PS,
        procedureIds = listOf("40028922"),
        origin = HealthEventOriginEnum.EITA,
        requestedAt = LocalDateTime.now(),
        healthProfessionalId = RangeUUID.generate(),
        originReferences = listOf(
            EventReference(
                id = executionGroupId.toString(),
                location = HealthEventLocationEnum.EXECUTION_GROUP
            )
        )
    )
    private val procedure = buildMvAuthorizedProcedure(
        healthEventId = payload.id,
        executorId = RangeUUID.generate(),
        status = MvAuthorizedProcedureStatus.EXECUTED,
        executionGroupId = executionGroupId
    )
    private val executed = TestModelFactory.buildExecIndicatorAuthorizer(
        id = procedure.executorId!!,
        domain = "haoc.com.br",
        mvProviderId = MvUtil.Prestador.HAOC_PAULISTA.code,
    )

    @Test
    fun `#registerAuthorizedProcedureExec should handle request when it is hospitalization extension`() = runBlocking {
        val claim = HaocClaimProcess(
            personId,
            HaocClaimStatus.PENDING,
            HaocClaimType.HOSPITALIZATION_EXTENSION,
            HaocUnit.PAULISTA
        )

        coEvery { mvAuthorizedProcedureService.findByExecutionGroupId(executionGroupId) } returns listOf(procedure).success()
        coEvery { execIndicatorAuthorizerService.get(procedure.executorId!!) } returns executed.success()
        coEvery {
            service.registerClaim(
                personId,
                HaocClaimType.HOSPITALIZATION_EXTENSION,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        } returns claim.success()

        val event = ProcessedHealthEvent(
            payload.copy(eventType = HealthEventTypeEnum.EXTENSION)
        )

        consumer.registerAuthorizedProcedureExec(event)

        coVerify(exactly = 1) {
            service.registerClaim(
                personId,
                HaocClaimType.HOSPITALIZATION_EXTENSION,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        }
    }

    @Test
    fun `#registerAuthorizedProcedureExec should handle request when it is hospitalization`() = runBlocking {
        val claim = HaocClaimProcess(
            personId,
            HaocClaimStatus.PENDING,
            HaocClaimType.HOSPITALIZATION,
            HaocUnit.PAULISTA
        )

        coEvery { mvAuthorizedProcedureService.findByExecutionGroupId(executionGroupId) } returns listOf(procedure).success()
        coEvery { execIndicatorAuthorizerService.get(procedure.executorId!!) } returns executed.success()
        coEvery {
            service.registerClaim(
                personId,
                HaocClaimType.HOSPITALIZATION,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        } returns claim.success()

        val event = ProcessedHealthEvent(
            payload.copy(eventType = HealthEventTypeEnum.HOSPITALIZATION)
        )

        consumer.registerAuthorizedProcedureExec(event)

        coVerify(exactly = 1) {
            service.registerClaim(
                personId,
                HaocClaimType.HOSPITALIZATION,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        }
    }

    @Test
    fun `#registerAuthorizedProcedureExec should handle request when it is emergency care`() = runBlocking {
        val claim = HaocClaimProcess(
            personId,
            HaocClaimStatus.PENDING,
            HaocClaimType.EMERGENCY_CARE,
            HaocUnit.PAULISTA,
        )

        coEvery { mvAuthorizedProcedureService.findByExecutionGroupId(executionGroupId) } returns listOf(procedure).success()
        coEvery { execIndicatorAuthorizerService.get(procedure.executorId!!) } returns executed.success()
        coEvery {
            service.registerClaim(
                personId,
                HaocClaimType.EMERGENCY_CARE,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        } returns claim.success()

        val event = ProcessedHealthEvent(
            payload.copy(eventType = HealthEventTypeEnum.PS)
        )

        consumer.registerAuthorizedProcedureExec(event)

        coVerify(exactly = 1) {
            service.registerClaim(
                personId,
                HaocClaimType.EMERGENCY_CARE,
                HaocUnit.PAULISTA,
                procedure.healthEventId
            )
        }
    }

    @Test
    fun `#registerAuthorizedProcedureExec should not handle request when type is not PS or Hospitalization`() = runBlocking {
        val event = ProcessedHealthEvent(
            payload.copy(eventType = HealthEventTypeEnum.EXAM)
        )

        val result = consumer.registerAuthorizedProcedureExec(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyNone { service.registerClaim(any(), any(), any(), any()) }
        coVerifyNone { mvAuthorizedProcedureService.findByExecutionGroupId(any()) }
        coVerifyNone { execIndicatorAuthorizerService.get(any()) }
    }
    @Test
    fun `#registerAuthorizedProcedureExec should not handle request when it is not HAOC`() = runBlocking {
        val event = ProcessedHealthEvent(
            payload.copy(eventType = HealthEventTypeEnum.PS)
        )
        val executed = executed.copy(mvCdPrestador = MvUtil.Prestador.DB.code)

        coEvery { mvAuthorizedProcedureService.findByExecutionGroupId(executionGroupId) } returns listOf(procedure).success()
        coEvery { execIndicatorAuthorizerService.get(procedure.executorId!!) } returns executed.success()

        val result = consumer.registerAuthorizedProcedureExec(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { mvAuthorizedProcedureService.findByExecutionGroupId(any()) }
        coVerifyOnce { execIndicatorAuthorizerService.get(any()) }
        coVerifyNone { service.registerClaim(any(), any(), any(), any()) }
    }
}
