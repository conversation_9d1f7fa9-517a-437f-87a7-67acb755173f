package br.com.alice.haocintegrationservice.service

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.haocintegrationservice.clients.HaocClient
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class PixServiceTest {

    private var haocPixRegistrationService: HaocPixRegistrationService = mockk()
    private var haocClient: HaocClient = mockk()
    private var pixService = PixService(haocPixRegistrationService, haocClient)
    private val person = TestModelFactory.buildPerson()

    @BeforeTest
    fun setup() {
        clearMocks(haocPixRegistrationService, haocClient)
    }

    @Test
    fun `should return true when add Patient in HAOC and in data service`() = runBlocking {
        coEvery { haocClient.addPatient(person) } returns pixInsertedResponse.success()
        coEvery { haocPixRegistrationService.registerPerson(person.id) } returns true

        assertThat(pixService.addPatient(person)).isTrue()

        coVerify(exactly = 1) { haocPixRegistrationService.registerPerson(person.id) }
    }

    @Test
    fun `should return true when already existent Patient in HAOC and add in data service`() = runBlocking {
        coEvery { haocClient.addPatient(person) } returns pixAlreadyExistentResponse.success()
        coEvery { haocPixRegistrationService.registerPerson(person.id) } returns true

        assertThat(pixService.addPatient(person)).isTrue()

        coVerify(exactly = 1) { haocPixRegistrationService.registerPerson(person.id) }
    }

    @Test
    fun `should return false when add Patient in HAOC but not in data service`() = runBlocking<Unit> {
        coEvery { haocClient.addPatient(person) } returns pixInsertedResponse.success()
        coEvery { haocPixRegistrationService.registerPerson(person.id) } returns false

        assertThat(pixService.addPatient(person)).isFalse()
    }

    @Test
    fun `should return false when not add Patient in HAOC`() = runBlocking {
        coEvery { haocClient.addPatient(person) } returns pixFailedResponse.success()

        assertThat(pixService.addPatient(person)).isFalse()

        coVerify(exactly = 0) { haocPixRegistrationService.registerPerson(person.id) }
    }

    @Test
    fun `should return false when other response adding add Patient in HAOC`() = runBlocking {
        coEvery { haocClient.addPatient(person) } returns otherResponse.success()

        assertThat(pixService.addPatient(person)).isFalse()

        coVerify(exactly = 0) { haocPixRegistrationService.registerPerson(person.id) }
    }

}

private const val pixInsertedResponse = """
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
   <env:Header xmlns:env="http://www.w3.org/2003/05/soap-envelope"/>
   <soap:Body>
      <MCCI_IN000002UV01 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3">
         <id extension="8b3ff682-4043-463b-99a9-1a1d81927765" root="1.1"/>
         <creationTime value="2019829185151"/>
         <interactionId extension="MCCI_IN000002UV01" root="2.16.840.1.113883.1.6"/>
         <processingCode code="T"/>
         <processingModeCode code="T"/>
         <acceptAckCode code="NE"/>
         <receiver typeCode="RCV">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99998.8734"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99998"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </receiver>
         <sender typeCode="SND">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99999.4567"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99999.1234"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </sender>
         <acknowledgement>
            <typeCode code="CA"/>
            <targetMessage>
               <id extension="6e88df42-70ac-4db6-a4ba-449c7fbf2a4e" root=""/>
            </targetMessage>
            <acknowledgementDetail/>
         </acknowledgement>
      </MCCI_IN000002UV01>
   </soap:Body>
</soap:Envelope>
"""

private const val pixAlreadyExistentResponse = """
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
   <env:Header xmlns:env="http://www.w3.org/2003/05/soap-envelope"/>
   <soap:Body>
      <MCCI_IN000002UV01 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3">
         <id extension="8b3ff682-4043-463b-99a9-1a1d81927765" root="1.1"/>
         <creationTime value="2019829185151"/>
         <interactionId extension="MCCI_IN000002UV01" root="2.16.840.1.113883.1.6"/>
         <processingCode code="T"/>
         <processingModeCode code="T"/>
         <acceptAckCode code="NE"/>
         <receiver typeCode="RCV">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99998.8734"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99998"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </receiver>
         <sender typeCode="SND">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99999.4567"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99999.1234"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </sender>
         <acknowledgement>
            <typeCode code="CR"/>
            <targetMessage>
               <id extension="6e88df42-70ac-4db6-a4ba-449c7fbf2a4e" root=""/>
            </targetMessage>
            <acknowledgementDetail/>
         </acknowledgement>
      </MCCI_IN000002UV01>
   </soap:Body>
</soap:Envelope>
"""

private const val pixFailedResponse = """
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
   <env:Header xmlns:env="http://www.w3.org/2003/05/soap-envelope"/>
   <soap:Body>
      <MCCI_IN000002UV01 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3">
         <id extension="8b3ff682-4043-463b-99a9-1a1d81927765" root="1.1"/>
         <creationTime value="2019829185151"/>
         <interactionId extension="MCCI_IN000002UV01" root="2.16.840.1.113883.1.6"/>
         <processingCode code="T"/>
         <processingModeCode code="T"/>
         <acceptAckCode code="NE"/>
         <receiver typeCode="RCV">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99998.8734"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99998"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </receiver>
         <sender typeCode="SND">
            <device classCode="DEV" determinerCode="INSTANCE">
               <id root="1.2.840.114350.1.13.99999.4567"/>
               <asAgent classCode="AGNT">
                  <representedOrganization classCode="ORG" determinerCode="INSTANCE">
                     <id root="1.2.840.114350.1.13.99999.1234"/>
                  </representedOrganization>
               </asAgent>
            </device>
         </sender>
         <acknowledgement>
            <typeCode code="CE"/>
            <targetMessage>
               <id extension="6e88df42-70ac-4db6-a4ba-449c7fbf2a4e" root=""/>
            </targetMessage>
            <acknowledgementDetail/>
         </acknowledgement>
      </MCCI_IN000002UV01>
   </soap:Body>
</soap:Envelope>
"""

private const val otherResponse = """
<other>
   <content>
   </content>
</other>
"""
