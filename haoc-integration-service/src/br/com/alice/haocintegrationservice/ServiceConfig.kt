package br.com.alice.haocintegrationservice

import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig: BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {

    object Haoc {
        fun baseUrl() = config("haoc.baseUrl")
        fun secret() = config("haoc.secret")
        fun documentIdSalt() = config("haoc.documentIdSalt")
    }

}
