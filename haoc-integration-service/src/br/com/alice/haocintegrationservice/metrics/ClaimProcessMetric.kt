package br.com.alice.haocintegrationservice.metrics

import br.com.alice.common.observability.metrics.Metric

private const val HAOC_CLAIM_PROCESS_METRIC = "haoc_claim_process"

enum class ClaimProcessResult {
    DONE,
    PENDING,
    FAILURE;

    fun toLabel() = this.name.lowercase()
}

object ClaimProcessMetric {

    fun incrementClaimProcessMetric(result: ClaimProcessResult) =
        Metric.increment(
            HAOC_CLAIM_PROCESS_METRIC,
            "result" to result.toLabel()
        )
}

fun registerClaimProcessCounter() {
    ClaimProcessResult.values().forEach { result ->
        Metric.registerCounter(
            HAOC_CLAIM_PROCESS_METRIC,
            "result" to result.toLabel()
        )
    }
}
