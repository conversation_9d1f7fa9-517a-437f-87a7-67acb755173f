package br.com.alice.haocintegrationservice.builder.rac

import br.com.alice.data.layer.models.Staff

// https://specifications.openehr.org/releases/1.0.2/architecture/terminology.pdf
object OpenEhr {

    fun setting(staff: Staff) =
        when {
            staff.isPhysician() -> OpenEhrSetting("228", "primary medical care")
            staff.isNurse() -> OpenEhrSetting("229", "primary nursing care")
            staff.isNutritionist() || staff.isPhysicalEducator() -> OpenEhrSetting("230", "primary allied health care")
            else -> throw IllegalArgumentException("${staff.email} is not from a allowed role")
        }

}

data class OpenEhrSetting(
    val conceptId: String,
    val rubric: String
)
