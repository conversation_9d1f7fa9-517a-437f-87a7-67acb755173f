package br.com.alice.haocintegrationservice.models

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

data class PrefixedValueAndDefiningCode (
    @JacksonXmlProperty(namespace = "oe")
    val value: String,

    @JacksonXmlProperty(namespace = "oe", localName = "defining_code")
    val definingCode: PrefixedDefiningCode
)

data class PrefixedDefiningCode (
    @JacksonXmlProperty(namespace = "oe", localName = "terminology_id")
    val terminologyID: PrefixedStringValue,

    @JacksonXmlProperty(namespace = "oe", localName = "code_string")
    val codeString: String?,
)

data class NameAndDefiningCodeValue (
    val name: StringValue,
    val value: DefiningCode
)

data class NameAndPrefixedValueAndDefiningCode (
    val name: StringValue,
    val value: PrefixedValueAndDefiningCode
)

data class DefiningCode (
    val value: String?,

    @JsonProperty("defining_code")
    val definingCode: Terminology
)

data class Composer (
    @JacksonXmlProperty(namespace = "oe")
    val identifiers: Identifiers,
)

data class Identifiers (
    @JacksonXmlProperty(namespace = "oe")
    val issuer: String,
    @JacksonXmlProperty(namespace = "oe")
    val assigner: String,
    @JacksonXmlProperty(namespace = "oe")
    val id: String,
    @JacksonXmlProperty(namespace = "oe")
    val type: String
)

data class Terminology (
    @JsonProperty("terminology_id")
    val terminologyID: StringValue,

    @JsonProperty("code_string")
    val codeString: String
)

data class NameAndPrefixedValue (
    val name: StringValue,
    val value: PrefixedStringValue
)

data class NameAndValue (
    val name: StringValue,
    val value: StringValue
)

data class PrefixedStringValue (
    @JacksonXmlProperty(namespace = "oe")
    val value: String?
)

data class StringValue(
    val value: String
)

data class ProblemaDiagnostico (
    val name: StringValue,
    val language: Terminology,
    val encoding: Terminology,
    val data: ProblemaDiagnosticoData
)

data class ProblemaDiagnosticoData (
    @JsonProperty("Diagnóstico")
    val diagnostico: DataDiagnostico,

    @JsonProperty("Qualificador_do_problema_fslash_diagnóstico")
    val qualificadorDoProblemaFslashDiagnostico: QualificadorDoProblemaDiagnostico
)

data class DataDiagnostico (
    val name: StringValue,
    val value: PrefixedValueAndDefiningCode
)

data class QualificadorDoProblemaDiagnostico (
    val name: StringValue,

    @JsonProperty("Categoria_do_diagnóstico")
    val categoriaDoDiagnostico: NameAndDefiningCodeValue
)

data class Procedimento (
    val name: StringValue,

    @JsonProperty("Procedimento")
    val procedimentos: List<ProcedimentoData> = emptyList(),
)


data class ProcedimentoData(
    val name: StringValue,
    val language: Terminology,
    val encoding: Terminology,
    val time: PrefixedStringValue,
    val description: ProcedimentoDescription,

    @JsonProperty("ism_transition")
    val ismTransition: CurrentState
)

data class ProcedimentoDescription(
    @JsonProperty("Procedimento")
    val procedimento: NameAndPrefixedValueAndDefiningCode,

    @JsonProperty("Categoria_do_procedimento")
    val categoriaProcedimento: NameAndValue,

    @JsonProperty("Data_da_realização")
    val dataRealizacao: NameAndPrefixedValue,

    @JsonProperty("Estado_do_procedimento")
    val estadoDoProcedimento: NameAndDefiningCodeValue,
)


data class SubstanciaEspecifica(
    val name: StringValue,
    val value: PrefixedStringValue
)

data class Criticidade(
    val name: StringValue,
    val value: DefiningCode
)

data class CategoriaAgenteCausador(
    val name: StringValue,
    val value: StringValue
)

data class PlanoDeCuidadosInstrucoessERecomendacoes (
    val name: StringValue,

    @JsonProperty("Plano_de_cuidados")
    val planoDeCuidados: PlanoDeCuidados?,

    @JsonProperty("Solicitação_de_serviço")
    val solicitacaoDeServico: List<SolicitacaoDeServico> = emptyList(),

    @JsonProperty("Exames_e_procedimentos")
    val examesEProcedimento: List<ExamesEProcedimentos> = emptyList(),
)

data class PlanoDeCuidados (
    val name: StringValue,
    val language: Terminology,
    val encoding: Terminology,

    @JsonProperty("Atividade")
    val atividade: Atividade?
)

data class SolicitacaoDeServico (
    val name: StringValue,
    val language: Terminology,
    val encoding: Terminology,

    @JsonProperty("Atividade_Atual")
    val atividade: AtividadeAtual
)

data class Atividade (
    val name: StringValue,
    val timing: Timing,
    val description: Description,

    @JsonProperty("action_archetype_id")
    val actionArchetypeID: String
)

data class AtividadeAtual (
    val name: StringValue,
    val timing: Timing,
    val description: DescriptionAtividadeAtual,
)

data class ExamesEProcedimentos(
    val data: ExamesEProcedimentosData
)

data class ExamesEProcedimentosData(
    @JsonProperty("Texto_livre")
    val textoLivre: ExamesEProcedimentosTexto
)

data class ExamesEProcedimentosTexto(
    @JsonProperty("Encaminhamento_para_exames_e_procedimentos__openBrkt_não_estruturado_closeBrkt_")
    val encaminhamento: NameAndPrefixedValue
)

data class Timing (
    @JacksonXmlProperty(namespace = "oe")
    val value: String,

    @JacksonXmlProperty(namespace = "oe")
    val formalism: String
)

data class Description(
    @JsonProperty("Descrição_do_plano_de_cuidados_comma__instruções_e_recomendações")
    val descricaoDoPlanoDeCuidadosCommaInstrucoesERecomendacoes: NameAndPrefixedValue
)

data class DescriptionAtividadeAtual(
    @JsonProperty("Encaminhamento_para_especialidade")
    val encaminhamento: NameAndDefiningCodeValue,

    @JsonProperty("Justificativa")
    val justificativa: NameAndPrefixedValue
)

data class ConselhoDeClasse (
    val name: StringValue,

    @JsonProperty("Tipo_de_conselho")
    val tipoDeConselho: NameAndDefiningCodeValue,

    @JsonProperty("UF")
    val uf: NameAndPrefixedValue,

    @JsonProperty("Número_do_registro")
    val numeroDoRegistro: NameAndPrefixedValue
)
