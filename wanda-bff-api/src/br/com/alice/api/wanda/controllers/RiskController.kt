package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.converters.healthcaremap.SimpleStaffConverter
import br.com.alice.api.wanda.model.healthcaremap.AddedBy
import br.com.alice.api.wanda.model.healthcaremap.RiskCurrentResponse
import br.com.alice.api.wanda.model.healthcaremap.RiskUpdateRequest
import br.com.alice.api.wanda.model.healthcaremap.StaffSimpleResponse
import br.com.alice.api.wanda.model.risk.Calculated
import br.com.alice.api.wanda.model.risk.RiskCurrent
import br.com.alice.api.wanda.model.risk.RiskCurrentAndCalculatedResponse
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role.CARE_COORD_NURSE
import br.com.alice.common.core.Role.DIGITAL_CARE_PHYSICIAN
import br.com.alice.common.core.Role.HEALTHCARE_TEAM_NURSE
import br.com.alice.common.core.Role.MANAGER_PHYSICIAN
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.HealthcareAdditionalTeamType.ATTENDANCE_PHYSICIAN
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.models.Staff
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.marauders.map.client.TargetRiskReasonService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode.Companion.Unauthorized
import java.util.UUID

class RiskController(
    private val riskService: RiskService,
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val healthcareTeamService: HealthcareTeamService,
    private val healthcareAdditionalTeamService: HealthcareAdditionalTeamService,
    private val staffService: StaffService,
    private val personService: PersonService,
    private val targetRiskReasonService: TargetRiskReasonService
) : StaffController(staffService) {

    private val rolesThatCanChangeRisk = listOf(
        MANAGER_PHYSICIAN,
        CARE_COORD_NURSE,
        HEALTHCARE_TEAM_NURSE,
        DIGITAL_CARE_PHYSICIAN
    )

    private val publicTarget = "TARGET"
    private val publicDefault = "DEFAULT"

    suspend fun update(request: RiskUpdateRequest): Response {
        val newRisk = RiskDescription.valueOf(request.keyRisk)
        val currentStaff = currentStaff()
        if (!rolesThatCanChangeRisk.contains(currentStaff.role)) return Response(Unauthorized)
        val currentStaffId = currentStaff.id

        return personClinicalAccountService.getByPersonId(request.personId.toPersonId())
            .flatMap { canUpdateRisk(it, currentStaffId) }
            .flatMap {
                riskService.updateByEdit(
                    request.personId.toPersonId(),
                    newRisk,
                    it
                )
            }
            .foldResponse(
                { it },
                UnsupportedOperationException::class to {
                    ErrorResponse("unauthorized_risk_edition", it.message!!)
                }
            )
    }

    private suspend fun canUpdateRisk(
        pca: PersonClinicalAccount,
        currentStaffId: UUID
    ): Result<UUID, Throwable> {
        val team = healthcareTeamService.get(pca.healthcareTeamId).get()
        val additionalTeam = healthcareAdditionalTeamService.findByTypeAndHealthcareTeamId(
            ATTENDANCE_PHYSICIAN,
            team.id
        ).get()
        val staffIds = (team.staffIds + additionalTeam.flatMap { it.staffIds }).distinct()
        return if (!staffIds.contains(currentStaffId)) {
            UnsupportedOperationException(
                "Staff ID $currentStaffId not belongs in healthcare Team of person"
            ).failure()
        } else currentStaffId.success()
    }


    suspend fun get(personId: UUID): Response =
        riskService.getByPerson(personId.toPersonId()).flatMap { risk ->
            RiskCurrentResponse(
                id = risk.id,
                riskDescription = risk.riskDescription,
                addedAt = risk.addedAt,
                addedBy = if (risk.addedBy.type == Risk.AddedByType.STAFF && risk.addedBy.id != null) AddedBy(
                    staffService.get(risk.addedBy.id!!).get(),
                    risk.addedBy.type
                ) else AddedBy(null, risk.addedBy.type),
            ).success()
        }.foldResponse()

    suspend fun getCurrentAndCalculated(personId: String) =
        riskService.getByPerson(personId.toPersonId()).map { risk ->

            var staffSimple: StaffSimpleResponse? = null
            var calculated: Calculated? = null
            if (risk.addedBy.type == Risk.AddedByType.STAFF) {
                staffSimple = staffService.get(risk.addedBy.id!!).map { SimpleStaffConverter.convert(it) }.get()
                calculated = Calculated(
                    riskType = Risk.toDescription(risk.calculatedValue!!),
                    riskDescription = Risk.toDescription(risk.calculatedValue!!).description,
                    value = risk.calculatedValue!!
                )
            }

            val riskMinnesota = Risk.toDescription(risk.finalValue)
            RiskCurrentAndCalculatedResponse(
                id = risk.id,
                current = RiskCurrent(
                    riskType = riskMinnesota,
                    riskDescription = riskMinnesota.description,
                    value = risk.finalValue,
                    addedAt = risk.addedAt,
                    addedBy = risk.addedBy.type,
                    staff = staffSimple,
                    public = if (risk.isTarget()) publicTarget else publicDefault
                ),
                calculated = calculated,
                canEdit = rolesThatCanChangeRisk.contains(currentStaff().role)
            )
        }.foldResponse()

    suspend fun getDetailCalculationConf(personId: String, riskId: String) =
        personService.get(personId.toPersonId()).let {
            riskService.getDetailCalculationConf(it.get(), riskId.toUUID()).foldResponse()
        }

    suspend fun getTargetReason(personId: String) =
        riskService.getByPerson(personId.toPersonId()).flatMap {
            targetRiskReasonService.getTargetReason(it.id)
        }.foldResponse()

    suspend fun getHistory(personId: PersonId) =
        riskService.getAllByPerson(personId).flatMapPair { risks ->
            val healthProfessionalIds =
                risks.filter { it.addedBy.type == Risk.AddedByType.STAFF }.mapNotNull { it.addedBy.id }
            if (healthProfessionalIds.isNotEmpty()) staffService.findByList(healthProfessionalIds)
            else emptyList<Staff>().success()
        }.map { (staffs, risks) ->
            val staffsMap = staffs.associateBy { it.id }
            val riskSortedByAddedAt = risks.sortedBy { it.addedAt }

            riskSortedByAddedAt.mapIndexedNotNull { index, risk ->
                val lastRisk = riskSortedByAddedAt.getOrNull(index - 1)
                if (lastRisk?.riskDescription != risk.riskDescription)
                    buildCurrentRisk(
                        risk,
                        if (risk.addedBy.type == Risk.AddedByType.STAFF) staffsMap[risk.addedBy.id] else null
                    )
                else
                    null
            }.sortedByDescending { it.addedAt }
        }.foldResponse()

    private fun buildCurrentRisk(risk: Risk, staff: Staff? = null) = RiskCurrent(
        riskType = Risk.toDescription(risk.finalValue),
        riskDescription = Risk.toDescription(risk.finalValue).description,
        value = risk.finalValue,
        addedAt = risk.addedAt,
        addedBy = risk.addedBy.type,
        staff = staff?.let { SimpleStaffConverter.convert(it) },
        public  = risk.public
    )
}
