package br.com.alice.businessrisk.events

import br.com.alice.businessrisk.SERVICE_NAME
import br.com.alice.common.core.PersonId
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.PersonHealthConditionContractualRisk

data class PersonHealthConditionContractualRiskUpdatedEvent(
    private val personHealthConditionContractualRisks: List<PersonHealthConditionContractualRisk>,
    private val personId: PersonId,
): NotificationEvent<PersonHealthConditionContractualRiskUpdatedEvent.Payload>(
    name = NAME,
    producer = SERVICE_NAME,
    payload = Payload(personHealthConditionContractualRisks, personId),
) {
    companion object {
        const val NAME = "PERSON-HEALTH-CONDITION-CONTRACTUAL-RISK-UPDATED"
    }

    data class Payload(
        val personHealthConditionContractualRisks: List<PersonHealthConditionContractualRisk>,
        val personId: PersonId
    )
}
