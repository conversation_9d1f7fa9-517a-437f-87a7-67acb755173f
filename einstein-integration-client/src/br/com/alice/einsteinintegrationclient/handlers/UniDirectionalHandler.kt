package br.com.alice.einsteinintegrationclient.handlers

import br.com.alice.common.core.extensions.toDotDouble
import br.com.alice.data.layer.models.ReferenceRange

class UniDirectionalHandler(
    private val nextHandler: ReferenceRangeHandler? = null
) : ReferenceRangeHandler {

    override fun parseResult(
        referenceValue: String?, resultValue: String?
    ): ReferenceRange? {

        if (referenceValue.isNullOrEmpty() || resultValue.isNullOrEmpty())
            return nextHandler?.parseResult(referenceValue, resultValue)


        val resultWithoutUnit = resultValue.split(" ")[0]
        val comparatorRx = "(=|<[>=]?|>=?)".toRegex()
        val numberRx = "([0-9]*(?:,|)[0-9]*)".toRegex()

        val regex = "$comparatorRx$numberRx".toRegex()

        if (!regex.matches(referenceValue))
            return nextHandler?.parseResult(referenceValue, resultValue)

        return try {
            val matchResult = regex.find(referenceValue)
            val (comparator, value) = matchResult!!.destructured

            val correctValue = value.toDotDouble()
            val correctResultValue = resultWithoutUnit.toDotDouble()

            val operator = when (comparator) {
                ">" -> (correctResultValue > correctValue)
                ">=" -> (correctResultValue >= correctValue)
                "<" -> (correctResultValue < correctValue)
                else -> (correctResultValue <= correctValue)
            }

            if(operator) ReferenceRange.WITHIN_NORMAL_LIMIT else ReferenceRange.OUTSIDE_NORMAL_LIMIT
        }catch (ex: Exception){
            nextHandler?.parseResult(referenceValue, resultValue)
        }
    }
}
