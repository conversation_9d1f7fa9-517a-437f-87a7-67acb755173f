package br.com.alice.einsteinintegrationclient.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.EinsteinResultadoExame
import br.com.alice.einsteinintegrationclient.SERVICE_NAME

data class EinsteinTestResultCreatedEvent(
    private val results: List<EinsteinResultadoExame>
) : NotificationEvent<EinsteinTestResultCreatedPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = EinsteinTestResultCreatedPayload(results = results)
){
    companion object {
        const val name = "einstein-test-result-created"
    }
}

data class EinsteinTestResultCreatedPayload(
    val results: List<EinsteinResultadoExame>
)
