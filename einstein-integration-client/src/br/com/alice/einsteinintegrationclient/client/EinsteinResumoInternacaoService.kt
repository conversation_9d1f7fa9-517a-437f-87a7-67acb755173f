package br.com.alice.einsteinintegrationclient.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.EinsteinResumoInternacao
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface EinsteinResumoInternacaoService : Service,
    Adder<EinsteinResumoInternacao>,
    Getter<EinsteinResumoInternacao> {

    override val namespace get() = "einstein"
    override val serviceName get() = "resumo_internacao"

    override suspend fun add(model: EinsteinResumoInternacao): Result<EinsteinResumoInternacao, Throwable>
    override suspend fun get(id: UUID): Result<EinsteinResumoInternacao, Throwable>
}
