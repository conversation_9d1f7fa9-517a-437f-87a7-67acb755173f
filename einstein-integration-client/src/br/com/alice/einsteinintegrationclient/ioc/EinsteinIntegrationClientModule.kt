package br.com.alice.einsteinintegrationclient.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.einsteinintegrationclient.EinsteinsIntegrationClientConfiguration
import br.com.alice.einsteinintegrationclient.SERVICE_NAME
import br.com.alice.einsteinintegrationclient.client.*
import org.koin.core.qualifier.named
import org.koin.dsl.module

val EinsteinIntegrationClientModule = module(createdAtStart = true) {

    val baseUrl = EinsteinsIntegrationClientConfiguration.baseUrl()
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 10_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<EinsteinAlergiaService> { EinsteinAlergiaServiceClient(invoker) }
    single<EinsteinAtendimentoService> { EinsteinAtendimentoServiceClient(invoker) }
    single<EinsteinAvaliacaoInicialService> { EinsteinAvaliacaoInicialServiceClient(invoker) }
    single<EinsteinEncaminhamentoService> { EinsteinEncaminhamentoServiceClient(invoker) }
    single<EinsteinDadosDeAltaService> { EinsteinDadosDeAltaServiceClient(invoker) }
    single<EinsteinMedicamentoService> { EinsteinMedicamentoServiceClient(invoker) }
    single<EinsteinProcedimentoService> { EinsteinProcedimentoServiceClient(invoker) }
    single<EinsteinResultadoExameService> { EinsteinResultadoExameServiceClient(invoker) }
    single<EinsteinResumoInternacaoService> { EinsteinResumoInternacaoServiceClient(invoker) }
    single<EinsteinDiagnosticoService> { EinsteinDiagnosticoServiceClient(invoker) }
    single<EinsteinAppointmentService> { EinsteinAppointmentServiceClient(invoker) }
    single<EinsteinStructuredTestResultService> { EinsteinStructuredTestResultServiceClient(invoker) }
    single<EinsteinClientService> { EinsteinClientServiceClient(invoker) }
}
