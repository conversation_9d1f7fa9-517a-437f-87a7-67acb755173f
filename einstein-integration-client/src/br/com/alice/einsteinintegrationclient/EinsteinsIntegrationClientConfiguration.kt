package br.com.alice.einsteinintegrationclient

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object EinsteinsIntegrationClientConfiguration {
    private val config = HoconApplicationConfig(ConfigFactory.load("einstein_integration_client.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun baseUrl() = config.property("${environment().value.lowercase()}.baseUrl").getString()
}
