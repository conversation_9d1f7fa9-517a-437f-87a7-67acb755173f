package br.com.alice.api.ops.controllers.fhir

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.models.fhir.ProviderAccessRequest
import br.com.alice.api.ops.models.fhir.ProviderAccessResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.models.FhirProviderAccess
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.fhir.client.ProviderAccessService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProviderAccessControllerTest : ControllerTestHelper() {
    private val providerAccessService: ProviderAccessService = mockk()
    private val controller = ProviderAccessController(providerAccessService)

    private val providerAccessData = buildFhirProviderAccess()

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { controller }
    }

    @Test
    fun `#index - should list all provider access by range`() {
        coEvery { providerAccessService.findByRange(IntRange(0, 19)) } returns
                listOf(providerAccessData).success()
        coEvery { providerAccessService.countAll() } returns Result.of { 1 }

        authenticatedAs(idToken, staff) {
            get("/fhir/provider_access") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderAccessResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.map { it.id }).contains(providerAccessData.id)
            }
        }
    }

    @Test
    fun `#index - should list all provider access by provider name`() {
        coEvery { providerAccessService.getByProviderName("DASA") } returns
                listOf(providerAccessData).success()
        coEvery { providerAccessService.countAll() } returns Result.of { 1 }

        authenticatedAs(idToken, staff) {
            get("/fhir/provider_access?filter=%7B\"provider\"%3A\"DASA\"%7D&order=DESC&page=1&perPage=20&sort=active") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderAccessResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.map { it.id }).contains(providerAccessData.id)
            }
        }
    }

    @Test
    fun `#getById should get provider access`() {
        coEvery { providerAccessService.get(providerAccessData.id) } returns providerAccessData.success()

        authenticatedAs(idToken, staff) {
            get("/fhir/provider_access/${providerAccessData.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderAccessResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(providerAccessData.convertTo(ProviderAccessResponse::class))
            }
        }
    }

    @Test
    fun `#create should create ProviderAccess entity`() {
        val request = ProviderAccessRequest(
            provider = ProviderIntegration.BP,
            clientId = null,
            clientSecret = null,
            active = null
        )

        authenticatedAs(idToken, staff) {
            coEvery { providerAccessService.add(any()) } returns providerAccessData.success()

            post(to = "/fhir/provider_access", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderAccessResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(providerAccessData.convertTo(ProviderAccessResponse::class))
            }
        }
    }

    @Test
    fun `#update should update ProviderAccess entity`() {
        val request = ProviderAccessRequest(
            provider = ProviderIntegration.BP,
            clientId = RangeUUID.generate(),
            clientSecret = RangeUUID.generate(),
            active = false
        )

        coEvery { providerAccessService.get(providerAccessData.id) } returns providerAccessData.success()
        coEvery { providerAccessService.update(match {
            it.id == providerAccessData.id
        }) } returns providerAccessData.copy(provider = request.provider, active = request.active!!).success()

        authenticatedAs(idToken, staff) {
            put(to = "/fhir/provider_access/${providerAccessData.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderAccessResponse = response.bodyAsJson()
                val dataConverted = providerAccessData.convertTo(ProviderAccessResponse::class)
                assertThat(content).isEqualTo(dataConverted.copy(provider = request.provider, active = request.active!!))
            }
        }
    }


    private fun buildFhirProviderAccess(provider: ProviderIntegration = ProviderIntegration.DASA) =
        FhirProviderAccess(provider = provider)
}
