package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.utils.Logging
import br.com.alice.common.Converter
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.cleanSymbols
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.map
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Pronoun
import br.com.alice.person.client.PersonService
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class PersonController(
    private val personService: PersonService
) : Controller() {

    suspend fun create(request: PersonRequest): Response {
        logger.info("PersonController::create", "current_staff_id" to currentUserIdKey())
        val duplicatedMsg = i18n("person_duplicated")

        return personService.create(PersonRequestConverter.convert(request)).then {
            logger.info("Added Person", "current_staff_id" to currentUserIdKey(), "person_id" to it.id)
        }.foldResponse(
            { PersonResponseConverter.convert(it) },
            DuplicatedItemException::class to { ErrorResponse("person_duplicated", duplicatedMsg) }
        )
    }

    suspend fun index(queryParams: Parameters): Response {
        logger.info(
            "PersonController::index",
            "params" to queryParams.entries(),
            "current_staff_id" to currentUserIdKey()
        )
        val ids = parseIds(queryParams)

        if (ids != null) {
            Logging.logPIISearch(currentUserIdKey(), "personIds", ids)
            val people = if (ids.isNotEmpty()) {
                personService.findByIds(ids).get()
            } else {
                emptyList()
            }
            val responses = people.map { PersonResponseConverter.convert(it) }
            return Response(HttpStatusCode.OK, responses, mapOf(HttpHeaders.ContentRange to responses.size.toString()))
        }

        val filterQuery = parseQuery(queryParams)?.cleanSymbols()

        if (filterQuery != null) {
            Logging.logPIISearch(currentUserIdKey(), "filter_query", filterQuery)
            val responses =
                personService.findBySearchTokens(filterQuery).get().map { PersonResponseConverter.convert(it) }
            return Response(HttpStatusCode.OK, responses, mapOf(HttpHeaders.ContentRange to responses.size.toString()))
        }
        return Response(HttpStatusCode.OK, emptyList<PersonResponse>(), mapOf(HttpHeaders.ContentRange to "0"))
    }

    suspend fun get(personId: String): Response {
        Logging.logPIISearch(currentUserIdKey(), "personId", personId)
        return personService.get(personId.toPersonId()).foldResponse(
            { PersonResponseConverter.convert(it) },
            NotFoundException::class to { ErrorResponse("person_not_found") }
        )
    }

    suspend fun update(personId: String, request: PersonRequest): Response {
        logger.info("PersonController::update", "person_id" to personId, "current_staff_id" to currentUserIdKey())
        val person = personService.get(personId.toPersonId()).get()
        val updatedPerson = person.copy(
            email = request.email,
            phoneNumber = request.phoneNumber,
            nationalId = request.nationalId,
            firstName = request.firstName,
            lastName = request.lastName,
            socialFirstName = request.socialFirstName,
            socialLastName = request.socialLastName,
            socialName = request.socialName,
            nickName = request.nickName,
            dateOfBirth = request.dateOfBirth?.let { LocalDate.parse(it).atStartOfDay() },
            sex = request.sex,
            gender = request.gender,
            isTest = request.isTest,
            pronoun = request.pronoun,
            tags = request.tags?.split(",")?.map { it.trim().lowercase().replace(" ", "_") }
        )

        return personService.update(updatedPerson).then {
            logger.info("Updated Person", "current_staff_id" to currentUserIdKey(), "person_id" to it.id)
        }.foldResponse(
            { PersonResponseConverter.convert(it) },
            NotFoundException::class to { ErrorResponse("person_conflict") }
        )
    }
}

data class PersonRequest(
    val email: String,
    val nationalId: String,
    val phoneNumber: String?,
    val firstName: String,
    val lastName: String,
    val socialName: String? = null,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val nickName: String?,
    val dateOfBirth: String?,
    val sex: Sex,
    val gender: Gender,
    val isTest: Boolean,
    val pronoun: Pronoun?,
    val tags: String?
)

data class PersonResponse(
    val id: String,
    val email: String,
    val nationalId: String,
    val firstName: String,
    val lastName: String,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val socialName: String? = null,
    val fullName: String,
    val nickName: String?,
    val dateOfBirth: String?,
    val sex: Sex?,
    val gender: Gender?,
    val isTest: Boolean,
    val mothersName: String?,
    val pisNumber: String?,
    val phoneNumber: String?,
    val addresses: String?,
    val acceptedTermsAt: String?,
    val pronoun: Pronoun?,
    val tags: String?,
    val updatedAt: String,
    val createdAt: String
)

object PersonRequestConverter : Converter<PersonRequest, Person>(PersonRequest::class, Person::class) {
    fun convert(source: PersonRequest) =
        convert(
            source,
            map(Person::dateOfBirth) from source.dateOfBirth?.let { LocalDate.parse(it).atStartOfDay() },
            map(Person::tags) from source.tags?.split(",")?.map { it.trim().lowercase().replace(" ", "_") }
        )
}

object PersonResponseConverter : Converter<Person, PersonResponse>(Person::class, PersonResponse::class) {
    fun convert(source: Person) =
        convert(
            source,
            map(PersonResponse::fullName) from source.fullSocialName,
            map(PersonResponse::dateOfBirth) from source.dateOfBirth?.toLocalDate()?.format(DateTimeFormatter.ISO_DATE),
            map(PersonResponse::addresses) from source.addresses.joinToString(", ", transform = Address::toString),
            map(PersonResponse::tags) from source.tags?.joinToString(",")
        )
}
