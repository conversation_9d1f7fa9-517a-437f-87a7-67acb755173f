package br.com.alice.api.ops.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.schedule.client.LivanceService

class LivanceProviderUnitController(
    private val livanceService: LivanceService
) : Controller() {

    suspend fun getLocations(): Response =
        livanceService.getLocations().foldResponse()

}
