package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ProviderConverter
import br.com.alice.api.ops.converters.ProviderResponseConverter
import br.com.alice.api.ops.models.ProviderRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import com.github.kittinunf.result.map
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ProviderController(
    private val providerService: ProviderService
) : Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseQuery(queryParams)?.trim()
        val status = parseStatus(queryParams)?.split(",")?.map { Status.valueOf(it) } ?: emptyList()
        val ids = parseIds(queryParams)?.map {
            it.toUUID()
        } ?: emptyList()


        val response = async { getByFilterQuery(filterQuery, status, ids, range) }
        val totalCount = async { countByFilter(filterQuery, status, ids) }


        Response(HttpStatusCode.OK, response.await(), mapOf(HttpHeaders.ContentRange to totalCount.await().toString()))
    }

    suspend fun getById(id: String) =
        providerService.get(id.toUUID()).map { provider ->
            ProviderResponseConverter.convert(provider)
        }.foldResponse()

    suspend fun create(request: ProviderRequest) =
        providerService.add(ProviderConverter.convert(request)).then {
            logger.info("Added Provider", "current_staff_id" to currentUserIdKey(), "model" to it, "request" to request)
        }.map { provider ->
            ProviderResponseConverter.convert(provider)
        }.foldResponse()

    suspend fun update(id: String, request: ProviderRequest) =
        providerService.get(id.toUUID()).map {
            it.copy(
                name = request.name,
                site = request.site,
                cnpj = request.cnpj,
                phones = request.phones,
                imageUrl = request.imageUrl,
                description = request.description,
                icon = request.icon,
                logo = request.logo,
                thumbnail = request.thumbnail,
                about = request.about,
                type = request.type,
                flagship = request.flagship,
                daysForPayment = request.daysForPayment,
                brand = request.brand,
                status = request.status ?: Status.ACTIVE
            )
        }.map {
            providerService.update(it).get()
        }.then { updated ->
            logger.info(
                "Updated Provider", "current_staff_id" to currentUserIdKey(), "request" to request,
                "model" to updated
            )
        }.foldResponse({
            ProviderResponseConverter.convert(
                it
            )
        })


    private suspend fun getByFilterQuery(filterName: String?, status: List<Status>, ids: List<UUID>, range: IntRange) =
        providerService.getByFiltersWithRange(ProviderFilter(filterName, status, ids), range).mapEach { provider ->
            ProviderResponseConverter.convert(provider)
        }.get()

    private suspend fun countByFilter(filterName: String?, status: List<Status>, ids: List<UUID>) =
        providerService.countByFilters(ProviderFilter(filterName, status, ids)).get()
}
