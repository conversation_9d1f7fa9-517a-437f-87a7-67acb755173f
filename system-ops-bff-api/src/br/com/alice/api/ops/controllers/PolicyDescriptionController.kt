package br.com.alice.api.ops.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.data.layer.services.PolicyDescriptionDataService
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode

class PolicyDescriptionController(private val policyDescriptionDataService: PolicyDescriptionDataService) : Controller() {

    suspend fun index(): Response {
        val list = policyDescriptionDataService.getAllPoliciesDescriptions().get().sorted().map {
            PolicyDescriptionResponse(id = RangeUUID.generate().toString(), description = it)
        }

        return Response(
            HttpStatusCode.OK,
            list,
            mapOf(HttpHeaders.ContentRange to list.size.toString())
        )
    }

}

data class PolicyDescriptionResponse(
    val id: String,
    val description: String,
)
