package br.com.alice.api.ops.controllers.healthcampaign

import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.gson.reflect.TypeToken
import io.ktor.http.HttpStatusCode.Companion.OK
import java.util.UUID

class HealthCampaignController(
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val personService: PersonService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
) {
    suspend fun upload(campaignId: String, multipartRequest: MultipartRequest): Response {
        val content = multipartRequest.fileContent!!.bufferedReader().use { it.readText() }
        val typeToken = object : TypeToken<List<InternalCode>>() {}.type
        val value: List<InternalCode> = gson.fromJson(content, typeToken)

        personInternalReferenceService.getByInternalCodes(value.map { it.internalCode }).flatMapPair {
            val personIds = it.map { person -> person.personId.toString() }
            personService.findByIds(personIds)
        }.map { (person, _) ->
            val nationalIds = person.map { it.nationalId }

            logger.info("campaignId $campaignId" , "NationalIds" to nationalIds)

            crmAnalyticsTracker.triggerCampaignToMembers(campaignId, nationalIds)
        }
        return Response(OK)
    }

    suspend fun getCampaigns(): Response {
        val campaigns = crmAnalyticsTracker.getCampaigns().flatMap { campaignList ->
            campaignList.campaigns.map {
                CampaignsResponse(
                    name = it.name ?: "",
                    campaignId = it.id
                )
            }.success()
        }
        return campaigns.foldResponse()
    }
}

data class HealthCampaignRequest(
    val internalCodes: List<InternalCode>
)
data class InternalCode(
    val internalCode: String
)

data class CampaignsResponse(
    val name: String?,
    val campaignId: UUID
)
