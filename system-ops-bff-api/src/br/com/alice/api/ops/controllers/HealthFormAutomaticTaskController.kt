package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.models.HealthFormAction
import br.com.alice.api.ops.models.HealthFormAutomaticTaskRequest
import br.com.alice.api.ops.models.HealthFormCondition
import br.com.alice.api.ops.models.OptionResponse
import br.com.alice.api.ops.models.toTimeUnit
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.ServiceScriptOperator
import br.com.alice.data.layer.models.TestRequestScriptAction
import br.com.alice.provider.client.TestCodeService
import br.com.alice.questionnaire.client.HealthFormManagementService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import com.google.api.client.util.ArrayMap
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters

class HealthFormAutomaticTaskController(
    private val healthFormManagementService: HealthFormManagementService,
    private val testCodeService: TestCodeService,
) : Controller() {

    suspend fun getAllHealthForms() : Response =
        healthFormManagementService.getAllForms().foldResponseWithContentRange()

    suspend fun getSectionsByHealthFormId(queryParams: Parameters) : Response =
        ignoreId(queryParams) ?: run {
            parseKey(queryParams, "health_form_id")?.let { healthFormId ->
                healthFormManagementService.getSectionByFormId(healthFormId.toUUID()).foldResponseWithContentRange()
            } ?: Response(HttpStatusCode.BadRequest)
        }

    suspend fun getQuestionsBySectionId(queryParams: Parameters) : Response =
        ignoreId(queryParams) ?: run {
            parseKey(queryParams, "section_id")?.let { sectionId ->
                healthFormManagementService.getQuestionBySectionId(sectionId.toUUID()).foldResponseWithContentRange()
            } ?: Response(HttpStatusCode.BadRequest)
        }

    suspend fun getResponseOptions(queryParams: Parameters) : Response =
        ignoreId(queryParams) ?: run {
            parseKey(queryParams, "question_id")?.let { questionId ->
                healthFormManagementService
                    .getQuestionById(questionId.toUUID())
                    .map { it.options.map { OptionResponse(it.value.toString(), it.label.toString()) } }
                    .foldResponseWithContentRange()
            } ?: Response(HttpStatusCode.BadRequest)
        }

    suspend fun getTestCodePackages() : Response =
        testCodeService.findAllPackages().foldResponseWithContentRange()

    private suspend fun conditionWithShortQuestion(conditions: List<Condition>): List<HealthFormCondition> =
        conditions.mapNotNull { condition ->
            if (condition.key.contains("question_")) {
                val id = condition.key.removePrefix("question_").toUUID()
                val options = if(condition.value is List<*>) {
                    condition.value as List<String>
                } else {
                    listOf(condition.value as String)
                }
                healthFormManagementService.getQuestionById(id).map {
                    HealthFormCondition(
                        healthFormQuestionId = id.toString(),
                        healthFormQuestionOption = options,
                        healthFormSection = it.healthFormSectionId.toString(),
                        healthFormQuestionSummary = it.summaryQuestion,
                    )
                }.getOrNullIfNotFound()
            } else {
                null
            }
        }

    private fun conditionsFromRequest(request: HealthFormAutomaticTaskRequest) : List<Condition> =
        request.conditions.map { condition ->
            Condition(
                key = "${ConditionOptions.QUESTION.key}_${condition.healthFormQuestionId}",
                operator = ServiceScriptOperator.IN_LIST,
                value = condition.healthFormQuestionOption
            )
        }.plus(listOf(
            Condition(
                key = ConditionOptions.AGE.key,
                operator = ServiceScriptOperator.RANGE,
                value = "${request.ageGreaterThan}..${request.ageLessThan}"
            ),
            Condition(
                key = ConditionOptions.HEALTH_FORM_ID.key,
                operator = ServiceScriptOperator.EQUALITY,
                value = request.healthFormId
            ),
            Condition(
                key = ConditionOptions.SEX.key,
                operator = ServiceScriptOperator.EQUALITY,
                value = request.sex.toString()
            ),
        ))

    private suspend fun actionWithPackageName(action: TestRequestScriptAction) : HealthFormAction? =
        action.testCodePackageId?.let { uuid ->
            testCodeService.getPackage(uuid).fold({
                HealthFormAction(
                    action.deadlinePeriodUnit.toTimeUnit()!!,
                    action.deadlinePeriodQuantity,
                    action.groupName,
                    action.startPeriodUnit.toTimeUnit()!!,
                    action.startPeriodQuantity,
                    action.testCodePackageId!!.toString(),
                    it.name
                )
            }, {null})
        }

    private fun ignoreId(queryParams: Parameters): Response? =
        parseId(queryParams)?.let {
            Response(HttpStatusCode.OK, emptyList<String>(), mapOf(ContentRange to "0"))
        }

    private fun parseKey(queryParams: Parameters, key: String): String? {
        val map: ArrayMap<String, Any>? =
            queryParams["filter"]?.let { br.com.alice.common.serialization.gson.fromJson(it) }
        return map?.get(key) as String?
    }

    private fun parseId(queryParams: Parameters): List<String>? {
        val map: ArrayMap<String, Any>? = queryParams["filter"]?.let { br.com.alice.common.serialization.gson.fromJson(it) }
        return map?.get("id") as? List<String>?
    }
}

fun <V : Any, E : Throwable> Result<V, E>.foldResponseWithContentRange() : Response = this.fold(
    {
        val total = when(it) {
            is Collection<*> -> it.size
            else -> 0
        }
        Response(HttpStatusCode.OK, it, mapOf(ContentRange to total.toString()))
    },
    { it.failure().foldResponse() }
)


