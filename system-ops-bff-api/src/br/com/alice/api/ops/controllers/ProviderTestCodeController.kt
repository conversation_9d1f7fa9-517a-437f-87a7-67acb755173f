package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ProviderTestCodeResponseConverter
import br.com.alice.api.ops.models.ProviderTestCodeResponse
import br.com.alice.api.ops.models.UpsertProviderTestCode
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.service.serialization.isoDateWithoutFieldPolicyGson
import br.com.alice.data.layer.models.ProviderTestCode
import br.com.alice.provider.client.ProviderTestCodeDataIntegrationService
import br.com.alice.provider.client.ProviderTestCodeService
import br.com.alice.provider.client.TestCodeService
import com.github.kittinunf.result.flatMap
import com.google.api.client.util.ArrayMap
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters

class ProviderTestCodeController(
    private val providerTestCodeService: ProviderTestCodeService,
    private val testCodeService: TestCodeService,
    private val dataIntegrationService: ProviderTestCodeDataIntegrationService,
) : Controller() {

    private val gson = isoDateWithoutFieldPolicyGson

    suspend fun index(queryParams: Parameters): Response {
        val filterQuery = parseQuery(queryParams)
        val brand = parseBrand(queryParams)

        if (filterQuery != null || brand != null) {

            val response = getProviderTestCodeResponse(filterQuery, brand)

            return Response(HttpStatusCode.OK, response, mapOf(HttpHeaders.ContentRange to response.size.toString()))
        }

        val range = parseRange(queryParams)

        val providerTestCodes = providerTestCodeService.findByRange(range).get()

        val response = getResponseForProviderTestCodes(providerTestCodes)

        val totalCount = providerTestCodeService.countAll().get()

        return Response(HttpStatusCode.OK, response, mapOf(HttpHeaders.ContentRange to totalCount.toString()))
    }

    private suspend fun getProviderTestCodeResponse(
        filterQuery: String?,
        brand: ProviderTestCode.Brand?
    ): List<ProviderTestCodeResponse> {

        val providerTestCodes = if (filterQuery == null) {
            providerTestCodeService.findByBrand(brand!!).get()
        } else if (brand == null) {
            providerTestCodeService.findByProviderCodes(listOf(filterQuery)).get()
        } else {
            providerTestCodeService.findByProviderCodesAndBrand(listOf(filterQuery), brand).get()
        }

        val responsesByProviderTestCode = getResponseForProviderTestCodes(providerTestCodes)

        val responsesByAliceTestCode = filterQuery?.let { getResponseForAliceTestCodes(filterQuery, brand) } ?: emptyList()

        return (responsesByProviderTestCode + responsesByAliceTestCode).distinct()
    }

    suspend fun getById(providerTestCodeId: String): Response =
        providerTestCodeService.get(providerTestCodeId.toUUID())
            .foldResponse()

    suspend fun create(request: UpsertProviderTestCode): Response =
        providerTestCodeService.add(
            ProviderTestCode(
                testCodeId = request.testCodeId,
                providerId = request.providerId,
                providerCode = request.providerCode,
                providerBrand = request.providerBrand,
                dataIntegration = request.dataIntegration ?: emptyList()
            )
        ).then {
            logger.info("Added ProviderTestCode", "current_staff_id" to currentUserIdKey(), "model" to it.asMap())
        }.foldResponse()

    suspend fun update(providerTestCodeId: String, request: UpsertProviderTestCode): Response =
        providerTestCodeService.get(providerTestCodeId.toUUID()).then {
            logger.info(
                "Update ProviderTestCode",
                "current_staff_id" to currentUserIdKey(),
                "new_model" to request.asMap(),
                "old_model" to it.asMap()
            )
        }.get().copy(
                testCodeId = request.testCodeId,
                providerCode = request.providerCode,
                providerBrand = request.providerBrand,
                dataIntegration = request.dataIntegration?.distinct() ?: emptyList()
            ).let { providerTestCode ->
                providerTestCodeService.update(providerTestCode).then {
                    logger.info(
                        "Updated ProviderTestCode",
                        "current_staff_id" to currentUserIdKey(),
                        "model" to it.asMap()
                    )
                }.foldResponse()
            }

    suspend fun delete(providerTestCodeId: String) =
        providerTestCodeService.get(providerTestCodeId.toUUID())
            .flatMap { providerTestCode ->
                providerTestCodeService.delete(providerTestCode).then {
                    logger.info(
                        "Deleted ProviderTestCode",
                        "current_staff_id" to currentUserIdKey(),
                        "model" to providerTestCode.asMap()
                    )
                }
            }.foldResponse()

    suspend fun getDataIntegration(): Response {
        val dataIntegration = dataIntegrationService.getAll().get()

        return Response(
            status = HttpStatusCode.OK,
            message = dataIntegration,
            headers = mapOf(HttpHeaders.ContentRange to dataIntegration.size.toString())
        )
    }

    private suspend fun getResponseForProviderTestCodes(providerTestCodes: List<ProviderTestCode>): List<ProviderTestCodeResponse> {
        if (providerTestCodes.isEmpty()) return emptyList()
        val testCodes = testCodeService.findByIds(providerTestCodes.map { it.testCodeId }).get()
        if (testCodes.isEmpty()) return emptyList()
        val idsToCodes = testCodes.map { it.id to it.code }.toMap()
        return providerTestCodes.map {
            ProviderTestCodeResponseConverter.convert(it, idsToCodes[it.testCodeId]!!)
        }
    }

    private suspend fun getResponseForAliceTestCodes(
        filterQuery: String,
        brand: ProviderTestCode.Brand?
    ): List<ProviderTestCodeResponse> {
        val testCodes = testCodeService.findBySearchTokens(filterQuery).get()
        if (testCodes.isEmpty()) return emptyList()
        val idsToCodes = testCodes.associate { it.id to it.code }
        val aliceTestCodes = testCodes.map { it.code }
        val providerTestCodes =
            providerTestCodeService.findByTestCodes(aliceTestCodes).get()
        val codes = providerTestCodes.map {
            ProviderTestCodeResponseConverter.convert(
                it.convertTo(ProviderTestCode::class),
                idsToCodes[it.testCodeId]!!
            )
        }
        return brand?.let {
            codes.filter { it.providerBrand == brand }
        } ?: codes
    }

    private fun parseBrand(queryParams: Parameters): ProviderTestCode.Brand? {
        val map: ArrayMap<String, Any>? = queryParams["filter"]?.let { gson.fromJson(it) }
        val brand = map?.get("brand") as String?
        return brand?.let { ProviderTestCode.Brand.valueOf(it) }
    }
}
