package br.com.alice.api.ops.controllers.healthcondition

import br.com.alice.api.ops.controllers.BaseController
import br.com.alice.api.ops.models.HealthConditionAxisRequest
import br.com.alice.api.ops.models.HealthConditionAxisResponse
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.models.HealthConditionAxis
import br.com.alice.healthcondition.client.HealthConditionAxisService
import com.github.kittinunf.result.Result

class HealthConditionAxisController(
    private val healthConditionAxisService: HealthConditionAxisService,
) : BaseController<HealthConditionAxisRequest, HealthConditionAxis, HealthConditionAxisResponse>() {

    override suspend fun get(id: String): Result<HealthConditionAxis, Throwable> =
        healthConditionAxisService.get(id.toUUID())

    override suspend fun getByRange(range: IntRange): Result<List<HealthConditionAxis>, Throwable> =
        healthConditionAxisService.getByRange(range)

    override suspend fun getAll(ids: List<String>): Result<List<HealthConditionAxis>, Throwable> =
        healthConditionAxisService.getAll(ids.map { it.toUUID() })

    override suspend fun add(model: HealthConditionAxis): Result<HealthConditionAxis, Throwable> =
        healthConditionAxisService.add(model)

    override suspend fun update(model: HealthConditionAxis): Result<HealthConditionAxis, Throwable> =
        healthConditionAxisService.update(model)

    override suspend fun getByQueryAndRange(
        query: String,
        range: IntRange
    ): Result<List<HealthConditionAxis>, Throwable> =
        throw BadRequestException("Function not implemented", "not_implemented")

    override suspend fun count(): Result<Int, Throwable> =
        healthConditionAxisService.count()

    override suspend fun formatRequest(request: HealthConditionAxisRequest): HealthConditionAxis =
        request.convertTo(HealthConditionAxis::class)

    override suspend fun formatRequestToUpdate(
        model: HealthConditionAxis,
        request: HealthConditionAxisRequest
    ): HealthConditionAxis =
        request.convertTo(HealthConditionAxis::class).copy(id = model.id, version = model.version, createdAt = model.createdAt)

    override suspend fun formatResponse(item: HealthConditionAxis): HealthConditionAxisResponse =
        item.convertTo(HealthConditionAxisResponse::class)

}
