package br.com.alice.api.ops.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.secondary.attention.client.SpecialistOpinionService
import io.ktor.http.HttpStatusCode
import java.util.UUID

class SpecialistOpinionController(
    private val specialistOpinionService: SpecialistOpinionService,
) : Controller() {

    suspend fun removeAssignee(specialistOpinionId: UUID): Response {
        specialistOpinionService.removeAssignee(specialistOpinionId)

        return Response(HttpStatusCode.NoContent)
    }
}
