package br.com.alice.api.ops.controllers.staff

import br.com.alice.api.ops.ServiceConfig
import br.com.alice.api.ops.controllers.CouncilTypeResponse
import br.com.alice.api.ops.controllers.ValueItemResponse
import br.com.alice.api.ops.controllers.staff.models.CreateUserRequest
import br.com.alice.api.ops.controllers.staff.models.UserRequest
import br.com.alice.api.ops.services.Filters
import br.com.alice.api.ops.services.StaffInternalService
import br.com.alice.api.ops.utils.ImageRequestValidator
import br.com.alice.api.ops.utils.QueryParamsParser
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileStoreRequest
import br.com.alice.common.storage.FileType
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Qualification
import br.com.alice.data.layer.models.SpecialistAppointmentType
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class StaffController(
    private val staffInternalService: StaffInternalService,
    private val fileStorage: FileStorage,
) : Controller() {

    suspend fun create(request: CreateUserRequest): Response {
        return staffInternalService.createUser(request).foldResponse()
    }

    suspend fun list(queryParams: Parameters): Response = coroutineScope {
        val range = parseRange(queryParams)
        val filters = queryParams.buildFilters()


        val usersDeferred = async { staffInternalService.list(filters, range).get() }
        val quantityDeferred = async { staffInternalService.count(filters).get() }
        Response(
            OK, usersDeferred.await(), mapOf(ContentRange to quantityDeferred.await().toString())
        )
    }

    suspend fun getById(id: UUID): Response = staffInternalService.get(id).foldResponse()

    suspend fun update(id: UUID, request: UserRequest): Response {
        return staffInternalService.update(id, request).foldResponse()
    }

    fun image(multipartRequest: MultipartRequest): Response {
        val validator = ImageRequestValidator(multipartRequest)

        if (!validator.isValid()) {
            return ValidationErrorResponse("invalid_file")
        }

        return storeImage(
            multipartRequest, validator.getFileType()!!
        ).toResponse()
    }

    private fun storeImage(multipartRequest: MultipartRequest, extension: FileType): String =
        fileStorage.store(
            FileStoreRequest(
                bucketName = ServiceConfig.bucket("publicAssetsBucket"),
                fileContent = multipartRequest.fileContent!!,
                filePath = "healthcare-team-assets/${multipartRequest.file!!.name}",
                fileType = extension,
                pii = false,
                phi = false
            )
        )

    suspend fun getRoles(queryParams: Parameters): Response = span("getRoles") { span ->
        span.setAttribute("query_params", queryParams.entries().joinToString(","))
        val searchTerm = parseQuery(queryParams).orEmpty().trim()
        val type = queryParams["type"]?.let { StaffType.valueOf(it) }
        span.setAttribute("search_term", searchTerm)

        val roles = Role.values().filter { type == null || it.types.contains(type) }
        val filteredRoles =
            if (searchTerm.isEmpty()) roles else roles.filter { it.description.contains(searchTerm, ignoreCase = true) }
        val sortedRoles = filteredRoles.sortedBy { it.description }
        val responseItems = sortedRoles.map { ValueItemResponse(it.name, it.description, it.name) }

        Response(OK, responseItems, mapOf(ContentRange to responseItems.size.toString()))
    }

    fun getTypes() = StaffType
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    fun getPhoneTypes() = PhoneType
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    fun getQualification() = Qualification
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    fun getCouncilTypes() = CouncilType
        .values()
        .map { type ->
            CouncilTypeResponse(
                id = type.code,
                name = type.name
            )
        }.toResponse()

    fun getHealthCommunitySpecialistAppointmentType() = SpecialistAppointmentType
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    fun getHealthCommunitySpecialistTiers() = SpecialistTier
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    fun getStaffScore() = HealthSpecialistScoreEnum
        .values()
        .map { type ->
            ValueItemResponse(
                id = type.name,
                name = type.description,
                value = type.name
            )
        }.toResponse()

    private fun parseStaffType(types: String): List<StaffType> =
        types.split(",").map { type -> StaffType.valueOf(type) }


    private fun parseRole(roles: String): List<Role> =
        roles.split(",").map { role -> Role.valueOf(role) }

    private fun Parameters.buildFilters(): Filters {
        val queryFilters = parseFilter(this)

        val q = queryFilters?.get("q") as String?
        val active = queryFilters?.get("active") as Boolean?
        val type = queryFilters?.get("type") as String?
        val role = queryFilters?.get("role") as String?
        val ids = QueryParamsParser().parseIds(this)

        logger.info(
            "Searching users with filter",
            "q" to q,
            "active" to active,
            "type" to type,
            "role" to role
        )
        return Filters(
            namePrefix = q,
            active = active,
            types = type?.let { parseStaffType(it) },
            roles = role?.let { parseRole(it) },
            ids = ids?.map { it.toSafeUUID() }
        )
    }
}
