package br.com.alice.api.ops.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters

class AppointmentScheduleEventTypeController(
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
) : Controller()  {

    suspend fun index(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val filterQuery = parseQuery(queryParams)


        filterQuery?.let {
            return appointmentScheduleEventTypeService.getActiveByTitle(filterQuery).foldResponse()
        }

        val byRange = appointmentScheduleEventTypeService.findByRange(range)
        val response = byRange.get()
        val totalCount = appointmentScheduleEventTypeService.countAll().get()

        return Response(HttpStatusCode.OK, response, mapOf(HttpHeaders.ContentRange to totalCount.toString()))
    }
}
