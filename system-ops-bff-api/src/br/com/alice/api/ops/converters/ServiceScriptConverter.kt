package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.ScriptRelationshipWithConditionsRequest
import br.com.alice.api.ops.models.ScriptRelationshipWithConditionsResponse
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.models.CaseSeriousness
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.JoinConditionType
import br.com.alice.data.layer.models.ServiceScriptOperator
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.healthlogic.models.healthLogics.HLAgeConditionRequest
import br.com.alice.healthlogic.models.healthLogics.HLDemandWithSeriousnessConditionRequest
import br.com.alice.healthlogic.models.healthLogics.HLOutcomeConditionRequest
import com.google.gson.internal.LinkedTreeMap
import java.util.UUID

object ServiceScriptConverter {
    fun toInternal(
        request: ScriptRelationshipWithConditionsRequest,
    ) =
        ServiceScriptRelationship(
            nodeParentId = request.nodeParentId.toUUID(),
            nodeChildId = request.nodeChildId.toUUID(),
            name = request.name,
            status = request.status,
            conditions = buildConditions(request),
            priority = request.priority
        )

    fun toResponse(
        relationship: ServiceScriptRelationship
    ) = ScriptRelationshipWithConditionsResponse(
        id = relationship.id,
        name = relationship.name,
        nodeParentId = relationship.nodeParentId.toString(),
        nodeChildId = relationship.nodeChildId.toString(),
        status = relationship.status,
        priority=relationship.priority,
        demandsWithSeriousness = buildDemandsWithSeriousnessCondition(relationship.conditions),
        outcomes = buildOutcomeCondition(relationship.conditions),
        outcomesOperator = buildOutcomeConditionOperator(relationship.conditions),
        age = buildAgeCondition(relationship.conditions),
        symptoms = buildSymptomsCondition(relationship.conditions),
        freeText = buildFreeTextCondition(relationship.conditions)
    )

    fun buildConditions(request: ScriptRelationshipWithConditionsRequest): List<Condition> {
        return listOfNotNull(
            buildDemandsWithSeriousness(request.demandsWithSeriousness ?: emptyList()),
            buildOutcome(request.outcomes, request.outcomesOperator),
            buildAge(request.age),
            buildSymptoms(request.symptoms),
            buildFreeText(request.freeText)
        )
    }

    private fun buildOutcome(
        outcomes: List<HLOutcomeConditionRequest>,
        operator: ServiceScriptOperator?
    ): Condition? {
        val conditions = outcomes.map {
            listOf(
                Condition(
                    key = ConditionOptions.OUTCOME_CONF_ID.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = it.outcomeConfId
                ),
                Condition(
                    key = ConditionOptions.CLINICAL_OUTCOME_REFERENCE.key,
                    operator = ServiceScriptOperator.RANGE,
                    value = "${it.minValue}..${it.maxValue}"
                )
            )
        }

        if (conditions.isEmpty()) return null

        return Condition(
            key = ConditionOptions.JOIN_CONDITIONS.key,
            operator = operator!!,
            joinConditionType = JoinConditionType.OUTCOME,
            value = conditions
        )
    }
    private fun buildOutcomeCondition(conditions: List<Condition>): List<HLOutcomeConditionRequest> {
        if (conditions.isEmpty()) return emptyList()
        val outcomesConditions = filterOutcomeConditions(conditions) ?: return emptyList()
        val outcomesConditionsList = (outcomesConditions.value as List<*>).map {
                when (it) {
                    is LinkedTreeMap<*, *> -> Condition(
                        key = it["key"].toString(),
                        value = it["value"].toString(),
                        operator = ServiceScriptOperator.valueOf(it["operator"].toString())
                    )
                    else -> it as Condition
                }
            }

        val outcomes = getOutcomeRangeAndType(outcomesConditionsList)

        return if (outcomes == null) emptyList() else listOf(outcomes)
    }

    private fun buildSymptoms(
        symptoms: List<String>?
    ): Condition? {
        val conditions = symptoms?.map { it.toUUID() }

        if (conditions?.isEmpty() == true) return null

        return conditions?.let {
            Condition(
                key = ConditionOptions.SYMPTOM_ID.key,
                operator = ServiceScriptOperator.IN_LIST,
                value = it
            )
        }
    }

    private fun buildFreeText(
        texts: List<String>?
    ): Condition? {
        val conditions = texts?.map { it.toUUID() }

        if (conditions?.isEmpty() == true) return null

        return conditions?.let {
            Condition(
                key = ConditionOptions.TEXT.key,
                operator = ServiceScriptOperator.IN_LIST,
                value = it
            )
        }
    }

    private fun buildDemandsWithSeriousness(
        demandsWithSeriousness: List<HLDemandWithSeriousnessConditionRequest>
    ): Condition? {
        val conditions = demandsWithSeriousness.map { demandWithSeriousness ->
            demandWithSeriousness.seriousness?.let {
                listOf(
                    Condition(
                        key = ConditionOptions.HEALTH_DEMAND_ID.key,
                        operator = ServiceScriptOperator.EQUALITY,
                        value = demandWithSeriousness.hDemandId
                    ),
                    Condition(
                        key = ConditionOptions.HEALTH_DEMAND_SERIOUSNESS.key,
                        operator = ServiceScriptOperator.EQUALITY,
                        value = it
                    )
                )
            } ?: listOf(
                Condition(
                    key = ConditionOptions.HEALTH_DEMAND_ID.key,
                    operator = ServiceScriptOperator.EQUALITY,
                    value = demandWithSeriousness.hDemandId
                )
            )
        }

        if (conditions.isEmpty()) return null

        return Condition(
            key = ConditionOptions.JOIN_CONDITIONS.key,
            operator = ServiceScriptOperator.OR,
            joinConditionType = JoinConditionType.DEMAND,
            value = conditions
        )
    }

    private fun buildSymptomsCondition(conditions: List<Condition>): List<UUID> {
        if(conditions.isEmpty()) return emptyList()
        val symptoms = conditions
            .firstOrNull {
                it.key == ConditionOptions.SYMPTOM_ID.key
            } ?: return emptyList()

        return (symptoms.value as List<*>).map {
            if (it is String) {
                it.toUUID()
            } else {
                it as UUID
            }
        }

    }

    private fun buildFreeTextCondition(conditions: List<Condition>): List<UUID> {
        if(conditions.isEmpty()) return emptyList()
        val texts = conditions
            .firstOrNull {
                it.key == ConditionOptions.TEXT.key
            } ?: return emptyList()

        return (texts.value as List<*>).map {
            if (it is String) {
                it.toUUID()
            } else {
                it as UUID
            }
        }

    }

    private fun buildDemandsWithSeriousnessCondition(conditions: List<Condition>): List<HLDemandWithSeriousnessConditionRequest> {
        if (conditions.isEmpty()) return emptyList()
        val hDemandWithSeriousness = conditions
            .firstOrNull {
                it.key == ConditionOptions.JOIN_CONDITIONS.key
                        && it.joinConditionType == JoinConditionType.DEMAND
            } ?: return emptyList()

        val hDemandWithSeriousnessList = (hDemandWithSeriousness.value as List<*>).map { insideList ->
            (insideList as List<*>).map {
                when (it) {
                    is LinkedTreeMap<*, *> -> Condition(
                        key = it["key"].toString(),
                        value = it["value"].toString(),
                        operator = ServiceScriptOperator.valueOf(it["operator"].toString())
                    )
                    else -> it as Condition
                }
            }
        }

        return hDemandWithSeriousnessList.mapNotNull {
            getHealthDemandAndSeriousness(it)
        }
    }

    private fun buildAge(age: HLAgeConditionRequest?): Condition? =
        age?.let {
            Condition(
                key = ConditionOptions.AGE.key,
                operator = ServiceScriptOperator.RANGE,
                value = "${it.minAge}..${it.maxAge}"
            )
        }

    private fun buildAgeCondition(conditions: List<Condition>): HLAgeConditionRequest? {
        if (conditions.isEmpty()) return null
        return conditions.firstOrNull { it.key == ConditionOptions.AGE.key }?.let { age ->
            val ageRange = age.value.toString().split("..")
            val minAge = ageRange[0].toInt()
            val maxAge = ageRange[1].toInt()

            HLAgeConditionRequest(
                minAge = minAge,
                maxAge = maxAge
            )
        }
    }

    private fun buildOutcomeConditionOperator(conditions: List<Condition>): ServiceScriptOperator? {
        if (conditions.isEmpty()) return null
        val outcomesConditions = filterOutcomeConditions(conditions) ?: return null

        return outcomesConditions.operator
    }

    private fun filterOutcomeConditions(conditions: List<Condition>) =
        conditions.firstOrNull {
            (it.key == ConditionOptions.JOIN_CONDITIONS.key && it.joinConditionType == JoinConditionType.OUTCOME)
                    || (it.key == ConditionOptions.JOIN_CONDITIONS.key && it.joinConditionType == null)
        }

    private fun getHealthDemandAndSeriousness(conditions: List<Condition>): HLDemandWithSeriousnessConditionRequest? {
        val seriousness = conditions.firstOrNull { it.key == ConditionOptions.HEALTH_DEMAND_SERIOUSNESS.key }?.value
        val hDemandId = conditions.firstOrNull { it.key == ConditionOptions.HEALTH_DEMAND_ID.key }?.value?.toString()?.toUUID()

        return hDemandId?.let { hDemandId ->
            HLDemandWithSeriousnessConditionRequest(
                hDemandId = hDemandId,
                seriousness = seriousness?.let { CaseSeriousness.valueOf(it.toString())}
            )
        }
    }

    private fun getOutcomeRangeAndType(conditions: List<Condition>): HLOutcomeConditionRequest? {
        val range = conditions.firstOrNull { it.key == ConditionOptions.CLINICAL_OUTCOME_REFERENCE.key }?.value
        val outcomeConfId = conditions.firstOrNull { it.key == ConditionOptions.OUTCOME_CONF_ID.key }?.value?.toString()?.toUUID()

        if (range == null || outcomeConfId == null) {
            return null
        }
        val values = range.toString().split("..")
        val minValue = values[0].toInt()
        val maxValue = values[1].toInt()

        return HLOutcomeConditionRequest(
            outcomeConfId = outcomeConfId,
            minValue = minValue,
            maxValue = maxValue
        )
    }
}
