package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.BeneficiaryOnboardingFlowTypeResponse
import br.com.alice.api.ops.models.BeneficiaryOnboardingPhaseTypeResponse
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType

object BeneficiaryOnboardingConverter {

    fun List<BeneficiaryOnboardingPhaseType>.toBeneficiaryOnboardingPhaseTypesResponse() = this.map {
        BeneficiaryOnboardingPhaseTypeResponse(
            id = it.name,
            name = it.description,
            value = it.name
        )
    }

    fun List<BeneficiaryOnboardingFlowType>.toBeneficiaryOnboardingFlowTypesResponse() = this.map {
        BeneficiaryOnboardingFlowTypeResponse(
            id = it.name,
            name = it.description,
            value = it.name
        )
    }
}
