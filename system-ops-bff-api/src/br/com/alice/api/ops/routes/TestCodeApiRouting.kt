package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.TestCodeController
import br.com.alice.api.ops.controllers.TestCodePackageController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.testCodeRoutes() {

    authenticate {
        val testCodeController by inject<TestCodeController>()
        route("test_codes") {
            get("/") {
                coHandler(testCodeController::index)
            }
            get("/{testCodeId}") {
                coHandler("testCodeId", testCodeController::getById)
            }
            post("/") {
                coHandler(testCodeController::create)
            }
            put("/{testCodeId}") {
                co<PERSON>and<PERSON>("testCodeId", testCodeController::update)
            }
        }

        val testCodePackageController by inject<TestCodePackageController>()
        route("test_code_package") {
            get("/") {
                coHandler(testCodePackageController::index)
            }
            get("/{testCodeId}") {
                coHandler("testCodeId", testCodePackageController::getById)
            }
            post("/") {
                coHandler(testCodePackageController::create)
            }
            put("/{testCodePackageId}") {
                coHandler("testCodePackageId", testCodePackageController::update)
            }
        }

    }
}
