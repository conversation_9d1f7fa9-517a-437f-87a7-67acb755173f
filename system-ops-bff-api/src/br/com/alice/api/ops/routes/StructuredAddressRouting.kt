package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.StructuredAddressController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.structuredAddressRouting() {
    authenticate {
        val structuredAddressController by inject<StructuredAddressController>()
        route("structured_address") {
            post { coHandler(structuredAddressController::create) }
            get("/{id}") { coHandler("id", structuredAddressController::getById) }
            put("/{id}") { coHandler("id", structuredAddressController::update) }
            delete("/{id}") { coHandler("id", structuredAddressController::delete) }
        }
    }
}
