package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.CassiSpecialistControllerV2
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.cassiSpecialistRoutes() {

    authenticate {
        val cassiSpecialistControllerV2 by inject<CassiSpecialistControllerV2>()
        route("v2/cassi_specialist") {
            post("/") {
                coHandler(cassiSpecialistControllerV2::create)
            }
            get("/") {
                coHandler(cassiSpecialistControllerV2::index)
            }
            get("/{id}") {
                coHandler("id", cassiSpecialistControllerV2::get)
            }
            put("/{id}") {
                coHandler("id", cassiSpecialistControllerV2::update)
            }
        }

    }
}
