package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.BillingAccountablePartyController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.billingAccountablePartyRoutes() {

    authenticate {
        val billingAccountablePartyController by inject<BillingAccountablePartyController>()

        route("/billing_accountable_parties") {
            get("/") {
                coHandler(billingAccountablePartyController::index)
            }
            get("/{id}") {
                coHandler("id", billingAccountablePartyController::getById)
            }
            post("/") {
                coHandler(billingAccountablePartyController::create)
            }
            put("/{id}") {
                coHandler("id", billingAccountablePartyController::update)
            }
        }
    }
}
