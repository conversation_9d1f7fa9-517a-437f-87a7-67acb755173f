package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.AppointmentScheduleOptionsController
import br.com.alice.common.coHandler
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.appointmentScheduleOptionsRoutes() {

    authenticate {
        val appointmentScheduleOptionsController by inject<AppointmentScheduleOptionsController>()
        route("/appointmentScheduleOptions") {
            post("/") {
                coHandler(appointmentScheduleOptionsController::create)
            }
            post("/image") {
                multipartHandler(appointmentScheduleOptionsController::image)
            }
            get("/") {
                coHandler(appointmentScheduleOptionsController::index)
            }
            get("/{scheduleOptionId}") {
                coHandler("scheduleOptionId", appointmentScheduleOptionsController::getById)
            }
            put("/{scheduleOptionId}") {
                coHandler("scheduleOptionId", appointmentScheduleOptionsController::update)
            }
        }

    }
}
