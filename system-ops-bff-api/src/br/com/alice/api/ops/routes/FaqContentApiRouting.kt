package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.FaqContentController
import br.com.alice.common.coHandler
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.faqContentRoutes() {

    authenticate {
        val faqContentController by inject<FaqContentController>()
        route("faq_contents") {
            get("/") {
                coHandler(faqContentController::index)
            }
            post("/image") {
                multipartHandler(faqContentController::image)
            }
            get("/{id}") {
                coHandler("id", faqContentController::getById)
            }
            post("/") {
                coHandler(faqContentController::create)
            }
            put("/{id}") {
                coHandler("id", faqContentController::update)
            }
        }

    }
}
