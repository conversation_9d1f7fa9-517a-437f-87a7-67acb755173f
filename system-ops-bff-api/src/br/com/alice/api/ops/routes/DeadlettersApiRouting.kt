package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.DeadletterController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.deadlettersRoutes() {

    authenticate {
        val deadletterController by inject<DeadletterController>()
        route("/deadletters") {
            get { coHandler(deadletterController::listDeadletters) }
            get("/{id}") { coHandler("id", deadletterController::get) }
            put("{id}/reprocess") { coHandler("id", deadletterController::reprocess) }
            put("{id}/drop") { coHandler("id", deadletterController::drop) }
        }
    }
}
