package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.CoveredGeoRegionController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject


fun Routing.coveredGeoRegionRoutes() {

    authenticate {
        val coveredGeoRegion by inject<CoveredGeoRegionController>()
        route("/covered_geo_region") {
            get { coHandler(coveredGeoRegion::listCoveredGeoRegions) }
            post { coHandler(coveredGeoRegion::create) }
            get("/{id}") { coHandler("id", coveredGeoRegion::get) }
            put("/{id}") { coHandler("id", coveredGeoRegion::update) }
        }
    }
}
