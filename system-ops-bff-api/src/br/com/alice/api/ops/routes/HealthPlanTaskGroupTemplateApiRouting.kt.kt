package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.HealthPlanTaskGroupTemplateController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.healthPlanTaskGroupTemplateRoutes() {
    authenticate {
        val taskTemplateGroupController by inject<HealthPlanTaskGroupTemplateController>()
        route("/health_plan_task_group_templates") {
            get("/") { coHandler(taskTemplateGroupController::index) }
            get("/search") { coHandler(taskTemplateGroupController::search) }
            post("/") { coHandler(taskTemplateGroupController::create) }
            get("/{templateId}") { coHandler("templateId", taskTemplateGroupController::get) }
            put("/{templateId}") { coHandler("templateId", taskTemplateGroupController::update) }
        }
    }
}
