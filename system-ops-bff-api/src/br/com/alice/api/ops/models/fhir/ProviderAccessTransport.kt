package br.com.alice.api.ops.models.fhir

import br.com.alice.data.layer.models.ProviderIntegration
import java.util.UUID

data class ProviderAccessRequest(
    val provider: ProviderIntegration,
    val clientId: UUID?,
    val clientSecret: UUID?,
    val active: Boolean?,
)

data class ProviderAccessResponse(
    val id: UUID,
    val provider: ProviderIntegration,
    val clientId: UUID,
    val clientSecret: UUID,
    val active: Boolean,
    val createdAt: String,
    val updatedAt: String,
)
