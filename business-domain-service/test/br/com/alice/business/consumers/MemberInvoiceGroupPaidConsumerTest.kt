package br.com.alice.business.consumers

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.services.internal.BeneficiaryActivationService
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class MemberInvoiceGroupPaidConsumerTest : ConsumerTest() {
    private val companyContractService: CompanyContractService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val beneficiaryActivationService: BeneficiaryActivationService = mockk()

    val consumer = MemberInvoiceGroupPaidConsumer(
        companyContractService,
        companySubcontractService,
        beneficiaryActivationService
    )

    val personId = PersonId()
    val member = TestModelFactory.buildMember(personId = personId)

    val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)

    private val memberInvoice = TestModelFactory.buildMemberInvoice(
        memberId = member.id,
        dueDate = LocalDateTime.of(2023, 4, 19, 12, 0),
        totalAmount = BigDecimal("100.0")
    )
    private val specificLocalDateTime = LocalDateTime.of(2023, 9, 15, 10, 0)
    private val firstPaymentInvoicePayment =
        TestModelFactory.buildInvoicePayment(
            memberInvoiceIds = listOf(memberInvoice.id),
            reason = PaymentReason.B2B_FIRST_PAYMENT,
            status = InvoicePaymentStatus.APPROVED,
            approvedAt = specificLocalDateTime,
            amount = BigDecimal("100.0"),
        )

    @Test
    fun `#activateMemberWhenInvoiceGroupIsPaid - should not activate member when type is not B2B_FIRST_PAYMENT`() =
        runBlocking {
            val memberInvoiceGroupRegularType = TestModelFactory.buildMemberInvoiceGroup(
                dueDate = LocalDate.of(2023, 9, 15),
                totalAmount = BigDecimal("100.0"),
                type = MemberInvoiceType.REGULAR_PAYMENT,
            )

            val event = MemberInvoiceGroupPaidEvent(memberInvoiceGroupRegularType, firstPaymentInvoicePayment)

            val result = consumer.activateMemberWhenInvoiceGroupIsPaid(event)

            assertThat(result).isSuccess()

            coVerify { companyContractService wasNot Called }
            coVerify { companySubcontractService wasNot Called }
            coVerify { beneficiaryActivationService wasNot Called }
        }

    @Test
    fun `#activateMemberWhenInvoiceGroupIsPaid - should activate member`() =
        runBlocking {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                dueDate = LocalDate.of(2023, 9, 15),
                totalAmount = BigDecimal("100.0"),
                type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            )
            val subContract = TestModelFactory.buildCompanySubContract(
                id = memberInvoiceGroup.companySubcontractId!!
            )

            val contract = TestModelFactory.buildCompanyContract(
                id = subContract.contractId
            )

            val event = MemberInvoiceGroupPaidEvent(memberInvoiceGroup, firstPaymentInvoicePayment)

            coEvery { companySubcontractService.get(memberInvoiceGroup.companySubcontractId!!) } returns subContract.success()
            coEvery { companyContractService.get(subContract.contractId) } returns contract.success()
            coEvery {
                beneficiaryActivationService.handleBeneficiaryActivation(
                    contract,
                    subContract,
                    firstPaymentInvoicePayment.approvedAt!!.toLocalDate()
                )
            } returns Unit.success()

            val result = consumer.activateMemberWhenInvoiceGroupIsPaid(event)

            assertThat(result).isSuccess()

            coVerifyOnce { companyContractService.get(any()) }
            coVerifyOnce { companySubcontractService.get(any()) }
            coVerifyOnce { beneficiaryActivationService.handleBeneficiaryActivation(any(), any(), any()) }
        }
}
