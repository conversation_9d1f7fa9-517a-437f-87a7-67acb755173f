package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class SubContractConsumerTest : ConsumerTest() {

    private val beneficiaryService: BeneficiaryService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()

    private val consumer = SubContractConsumer(companySubContractService, beneficiaryService)

    private val subContract = TestModelFactory.buildCompanySubContract()
    private val beneficiary1 = TestModelFactory.buildBeneficiary(companyId = subContract.companyId)
    private val beneficiary2 = TestModelFactory.buildBeneficiary(companyId = subContract.companyId)
    private val beneficiary3 = TestModelFactory.buildBeneficiary(companyId = subContract.companyId)

    @Test
    fun `#handleCompanySubContractCreatedEvent should associate all beneficiaries of a company to the first subcontract`(): Unit =
        runBlocking {
            coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(subContract)
            coEvery { beneficiaryService.findByCompanyId(any()) } returns listOf(
                beneficiary1,
                beneficiary2,
                beneficiary3
            )
            coEvery { beneficiaryService.update(match { it.id == beneficiary1.id }) } returns beneficiary1
            coEvery { beneficiaryService.update(match { it.id == beneficiary2.id }) } returns beneficiary2
            coEvery { beneficiaryService.update(match { it.id == beneficiary3.id }) } returns beneficiary3

            val event = CompanySubContractCreatedEvent(subContract)
            val result = consumer.handleCompanySubContractCreatedEvent(event)

            assertThat(result).isSuccess()

            coVerifyOnce { companySubContractService.findByCompanyId(subContract.companyId) }
            coVerifyOnce { beneficiaryService.findByCompanyId(subContract.companyId) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary1.id }) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary2.id }) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary3.id }) }
        }

    @Test
    fun `#handleCompanySubContractCreatedEvent should not associate any beneficiaries of a company if it is not the first subcontract`(): Unit =
        runBlocking {
            val subContract2 = TestModelFactory.buildCompanySubContract()

            coEvery { companySubContractService.findByCompanyId(any()) } returns listOf(subContract, subContract2)

            val event = CompanySubContractCreatedEvent(subContract)
            val result = consumer.handleCompanySubContractCreatedEvent(event)

            assertThat(result).isSuccess()

            coVerifyOnce { companySubContractService.findByCompanyId(subContract.companyId) }
        }

    @Test
    fun `#handleCompanySubContractCreatedEvent should associate all beneficiaries of a company when not found any subcontract`(): Unit =
        runBlocking {
            coEvery { companySubContractService.findByCompanyId(any()) } returns emptyList()
            coEvery { beneficiaryService.findByCompanyId(any()) } returns listOf(
                beneficiary1,
                beneficiary2,
                beneficiary3
            )
            coEvery { beneficiaryService.update(match { it.id == beneficiary1.id }) } returns beneficiary1
            coEvery { beneficiaryService.update(match { it.id == beneficiary2.id }) } returns beneficiary2
            coEvery { beneficiaryService.update(match { it.id == beneficiary3.id }) } returns beneficiary3

            val event = CompanySubContractCreatedEvent(subContract)
            val result = consumer.handleCompanySubContractCreatedEvent(event)

            assertThat(result).isSuccess()

            coVerifyOnce { companySubContractService.findByCompanyId(subContract.companyId) }
            coVerifyOnce { beneficiaryService.findByCompanyId(subContract.companyId) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary1.id }) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary2.id }) }
            coVerifyOnce { beneficiaryService.update(match { it.id == beneficiary3.id }) }
        }

}
