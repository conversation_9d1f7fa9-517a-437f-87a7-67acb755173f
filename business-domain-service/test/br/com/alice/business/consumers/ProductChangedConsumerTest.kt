package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.product.client.ProductService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ProductChangedConsumerTest : ConsumerTest() {

    private val beneficiaryService: BeneficiaryService = mockk()
    private val productService: ProductService = mockk()

    private val consumer = ProductChangedConsumer(beneficiaryService, productService)

    @Test
    fun `#createBeneficiary should exit when previous product is not B2B`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()
        val b2cProduct = TestModelFactory.buildProduct(type = ProductType.B2C)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.B2B)

        coEvery { productService.getProduct(previousMember.productId) } returns b2cProduct
        coEvery { productService.getProduct(newMember.productId) } returns newProduct
        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException()

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(any(), any()) }
        coVerifyNone { beneficiaryService.addBeneficiaryForExistentMember(any(), any(), any(), any()) }
    }

    @Test
    fun `#createBeneficiary should exit when new product is not B2B`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()
        val b2cProduct = TestModelFactory.buildProduct(type = ProductType.B2C)
        val product = TestModelFactory.buildProduct(type = ProductType.B2B)

        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns b2cProduct
        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException()

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(any(), any()) }
        coVerifyNone { beneficiaryService.addBeneficiaryForExistentMember(any(), any(), any(), any()) }
    }

    @Test
    fun `#createBeneficiary should exit when products are ADESAO and B2B`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()
        val adesaoProduct = TestModelFactory.buildProduct(type = ProductType.ADESAO)
        val b2bProduct = TestModelFactory.buildProduct(type = ProductType.B2B)

        coEvery { productService.getProduct(previousMember.productId) } returns b2bProduct
        coEvery { productService.getProduct(newMember.productId) } returns adesaoProduct
        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException()

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(any(), any()) }
        coVerifyNone { beneficiaryService.addBeneficiaryForExistentMember(any(), any(), any(), any()) }
    }

    @Test
    fun `#createBeneficiary should exit when has already a Beneficiary for new memberId B2B`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()

        val product = TestModelFactory.buildProduct(type = ProductType.B2B)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.B2B)
        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns newProduct

        val newBeneficiary = TestModelFactory.buildBeneficiary()
        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns newBeneficiary

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(newMember.id) }
        coVerifyNone { beneficiaryService.addBeneficiaryForExistentMember(any(), any(), any(), any()) }
    }

    @Test
    fun `#createBeneficiary should exit when has already a Beneficiary for new memberId ADESAO`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()

        val product = TestModelFactory.buildProduct(type = ProductType.ADESAO)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.ADESAO)
        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns newProduct

        val newBeneficiary = TestModelFactory.buildBeneficiary()
        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns newBeneficiary

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(newMember.id) }
        coVerifyNone { beneficiaryService.addBeneficiaryForExistentMember(any(), any(), any(), any()) }
    }

    @Test
    fun `#createBeneficiary should add Beneficiary B2B`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()

        val product = TestModelFactory.buildProduct(type = ProductType.B2B)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.B2B)
        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns newProduct

        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException("Beneficiary not found")

        val previousBeneficiary = TestModelFactory.buildBeneficiary(
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED))
            )
        )
        coEvery { beneficiaryService.findByMemberId(previousMember.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns previousBeneficiary

        val expectedBeneficiary = TestModelFactory.buildBeneficiary()

        coEvery {
            beneficiaryService.addBeneficiaryForExistentMember(
                match {
                      it.memberId === newMember.id
                },
                newMember.productId,
                previousBeneficiary.onboarding!!.flowType,
                previousBeneficiary.onboarding!!.currentPhase!!.phase
            )
        } returns expectedBeneficiary

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(expectedBeneficiary)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { productService.getProduct(newMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(newMember.id) }
        coVerifyOnce { beneficiaryService.findByMemberId(previousMember.id, BeneficiaryService.FindOptions(withOnboarding = true)) }
        coVerifyOnce {
            beneficiaryService.addBeneficiaryForExistentMember(
                match {
                    it.memberId === newMember.id && it.memberStatus === newMember.status
                },
                newMember.productId,
                previousBeneficiary.onboarding!!.flowType,
                previousBeneficiary.onboarding!!.currentPhase!!.phase
            )
        }
    }

    @Test
    fun `#createBeneficiary should add Beneficiary B2B without hiredAt`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()

        val product = TestModelFactory.buildProduct(type = ProductType.B2B)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.B2B)
        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns newProduct

        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException("Beneficiary not found")

        val previousBeneficiary = TestModelFactory.buildBeneficiary(
            hiredAt = null,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED))
            )
        )
        coEvery {
            beneficiaryService.findByMemberId(
                previousMember.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns previousBeneficiary

        val expectedBeneficiary = TestModelFactory.buildBeneficiary()

        coEvery {
            beneficiaryService.addBeneficiaryForExistentMember(
                match {
                    it.memberId === newMember.id && it.memberStatus === newMember.status
                },
                newMember.productId,
                previousBeneficiary.onboarding!!.flowType,
                previousBeneficiary.onboarding!!.currentPhase!!.phase,
                ignoreHiredAtValidation = true
            )
        } returns expectedBeneficiary

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(expectedBeneficiary)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { productService.getProduct(newMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(newMember.id) }
        coVerifyOnce { beneficiaryService.findByMemberId(previousMember.id, BeneficiaryService.FindOptions(withOnboarding = true)) }
        coVerifyOnce {
            beneficiaryService.addBeneficiaryForExistentMember(
                any(), any(), any(), any(), any()
            )
        }
    }

    @Test
    fun `#createBeneficiary should add Beneficiary ADESAO`() = runBlocking {
        val previousMember = TestModelFactory.buildMember()
        val newMember = TestModelFactory.buildMember()

        val product = TestModelFactory.buildProduct(type = ProductType.ADESAO)
        val newProduct = TestModelFactory.buildProduct(type = ProductType.ADESAO)
        coEvery { productService.getProduct(previousMember.productId) } returns product
        coEvery { productService.getProduct(newMember.productId) } returns newProduct

        coEvery { beneficiaryService.findByMemberId(newMember.id) } returns NotFoundException("Beneficiary not found")

        val previousBeneficiary = TestModelFactory.buildBeneficiary(
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
                phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.FINISHED))
            )
        )
        coEvery { beneficiaryService.findByMemberId(previousMember.id, BeneficiaryService.FindOptions(withOnboarding = true)) } returns previousBeneficiary

        val expectedBeneficiary = TestModelFactory.buildBeneficiary()

        coEvery {
            beneficiaryService.addBeneficiaryForExistentMember(
                match {
                    it.memberId === newMember.id && it.memberStatus === newMember.status
                },
                newMember.productId,
                previousBeneficiary.onboarding!!.flowType,
                previousBeneficiary.onboarding!!.currentPhase!!.phase
            )
        } returns expectedBeneficiary

        val event = ProductChangedEvent(previousMember, newMember)
        val result = consumer.createBeneficiary(event)

        assertThat(result).isSuccessWithData(expectedBeneficiary)

        coVerifyOnce { productService.getProduct(previousMember.productId) }
        coVerifyOnce { productService.getProduct(newMember.productId) }
        coVerifyOnce { beneficiaryService.findByMemberId(newMember.id) }
        coVerifyOnce { beneficiaryService.findByMemberId(previousMember.id, BeneficiaryService.FindOptions(withOnboarding = true)) }
        coVerifyOnce {
            beneficiaryService.addBeneficiaryForExistentMember(
                match {
                    it.memberId === newMember.id && it.memberStatus === newMember.status
                },
                newMember.productId,
                previousBeneficiary.onboarding!!.flowType,
                previousBeneficiary.onboarding!!.currentPhase!!.phase
            )
        }
    }
}
