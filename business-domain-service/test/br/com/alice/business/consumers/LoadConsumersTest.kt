package br.com.alice.business.consumers

import br.com.alice.business.ApplicationModule
import br.com.alice.business.SERVICE_NAME
import br.com.alice.business.module
import br.com.alice.business.routes.kafkaRoutes
import br.com.alice.common.ApplicationTest
import br.com.alice.common.kafka.internals.ConsumerJob
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.application.uninstall
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import kotlin.test.Test

class LoadConsumersTest: ApplicationTest() {

    override val serviceName = SERVICE_NAME

    lateinit var application: Application

    override val moduleFunction = { application: Application, module: Module ->
        val modules = ApplicationModule.dependencyInjectionModules + module
        this.application = application
        application.module(modules)
    }

    @Test
    fun `#Application can load dependencies correctly`() = runBlocking<Unit> {
        val service = serviceName
        application.uninstall(ConsumerJob.plugin)
        application.install(ConsumerJob.plugin) {
            serviceName = service
            kafkaRoutes()
        }
    }
}
