package br.com.alice.business.converters

import br.com.alice.business.converters.BeneficiaryConverter.toNewPerson
import br.com.alice.business.converters.BeneficiaryConverter.toNewPristineBeneficiary
import br.com.alice.business.converters.model.toModel
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.MemberStatus
import com.github.kittinunf.result.success
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.Test

class BeneficiaryConverterTest {
    @Test
    fun `#Beneficiary_toNewPristineBeneficiary - converts correctly with null cancelation fields`() {
        val beneficiaryCanceled = TestModelFactory.buildBeneficiary(
            canceledReason = BeneficiaryCancelationReason.RESIGNATION_WITHOUT_CAUSE,
            canceledAt = LocalDateTime.now(),
            canceledDescription = "description",
            hasContributed = false,
            memberStatus = null,
        ).toModel()
        val memberId = RangeUUID.generate()
        val expectedBeneficiary = beneficiaryCanceled.copy(
            memberId = memberId,
            canceledAt = null,
            canceledDescription = null,
            canceledReason = null,
            hasContributed = null,
        )
        val result = beneficiaryCanceled.toNewPristineBeneficiary(memberId).success()

        ResultAssert.assertThat(result).isSuccessWithDataIgnoringGivenFields(
            expectedBeneficiary, "id", "onboarding", "dependents"
        )
    }

    @Test
    fun `#Beneficiary_toNewPristineBeneficiary - converts correctly with null cancelation fields using different Member Status`() {
        val beneficiaryCanceled = TestModelFactory.buildBeneficiary(
            canceledReason = BeneficiaryCancelationReason.RESIGNATION_WITHOUT_CAUSE,
            canceledAt = LocalDateTime.now(),
            canceledDescription = "description",
            hasContributed = false,
            memberStatus = null,
        ).toModel()
        val memberId = RangeUUID.generate()
        val expectedBeneficiary = beneficiaryCanceled.copy(
            memberId = memberId,
            canceledAt = null,
            canceledDescription = null,
            canceledReason = null,
            hasContributed = null,
        )
        val result = beneficiaryCanceled.toNewPristineBeneficiary(memberId, memberStatus = MemberStatus.ACTIVE).success()

        Assertions.assertThat(result.get().memberStatus).isEqualTo(MemberStatus.ACTIVE)
        ResultAssert.assertThat(result).isSuccessWithDataIgnoringGivenFields(
            expectedBeneficiary, "id", "onboarding", "dependents", "memberStatus"
        )
    }

    @Test
    fun `#toNewPerson - converts correctly to Person`() {
        val oldPerson = TestModelFactory.buildPerson(
            email = "<EMAIL>",
            sex = Sex.FEMALE,
            tags = listOf("tag1", "tag2"),
        )
        val newPerson = oldPerson.copy(
            email = "<EMAIL>",
            sex = Sex.INTERSEX,
            tags = listOf("tag2", "tag3"),
        )

        val expectedPerson = newPerson.copy(tags = listOf("tag1", "tag2", "tag3"))

        val result = oldPerson.toNewPerson(expectedPerson).success()

        ResultAssert.assertThat(result).isSuccessWithData(expectedPerson)
    }

    @Test
    fun `#toNewPerson - works converting tags with new null`() {
        val oldPerson = TestModelFactory.buildPerson(
            email = "<EMAIL>",
            sex = Sex.FEMALE,
            tags = null,
        )
        val newPerson = oldPerson.copy(
            email = "<EMAIL>",
            sex = Sex.INTERSEX,
            tags = listOf("tag2", "tag3"),
        )

        val expectedPerson = newPerson.copy(tags = listOf("tag2", "tag3"))

        val result = oldPerson.toNewPerson(expectedPerson).success()

        ResultAssert.assertThat(result).isSuccessWithData(expectedPerson)
    }

    @Test
    fun `#toNewPerson - works converting tags with old null`() {
        val oldPerson = TestModelFactory.buildPerson(
            email = "<EMAIL>",
            sex = Sex.FEMALE,
            tags = listOf("tag1", "tag2"),
        )
        val newPerson = oldPerson.copy(
            email = "<EMAIL>",
            sex = Sex.INTERSEX,
            tags = null,
        )

        val expectedPerson = newPerson.copy(tags = listOf("tag1", "tag2"))

        val result = oldPerson.toNewPerson(expectedPerson).success()

        ResultAssert.assertThat(result).isSuccessWithData(expectedPerson)
    }
}
