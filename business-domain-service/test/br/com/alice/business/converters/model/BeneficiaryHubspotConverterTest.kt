package br.com.alice.business.converters.model

import br.com.alice.common.RangeUUID
import br.com.alice.common.extensions.toPersonId
import br.com.alice.data.layer.models.BeneficiaryHubspot
import br.com.alice.data.layer.models.BeneficiaryHubspotModel
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class BeneficiaryHubspotConverterTest {
    private val beneficiaryHubspot = BeneficiaryHubspot(
        id = RangeUUID.generate(),
        personId = RangeUUID.generate(RangeUUID.PERSON_ID_RANGE).toPersonId(),
        beneficiaryId = RangeUUID.generate(),
        companyId = RangeUUID.generate(),
        externalContactId = RangeUUID.generate().toString(),
        externalDealId = RangeUUID.generate().toString(),
        version = 1
    )

    private val beneficiaryHubspotModel = BeneficiaryHubspotModel(
        id = beneficiaryHubspot.id,
        personId = beneficiaryHubspot.personId,
        beneficiaryId = beneficiaryHubspot.beneficiaryId,
        companyId = beneficiaryHubspot.companyId,
        externalContactId = beneficiaryHubspot.externalContactId,
        externalDealId = beneficiaryHubspot.externalDealId,
        version = beneficiaryHubspot.version
    )

    @Test
    fun testToTransport() {
        assertThat(beneficiaryHubspotModel.toTransport()).isEqualTo(beneficiaryHubspot)
    }

    @Test
    fun testToModel() {
        assertThat(beneficiaryHubspot.toModel()).isEqualTo(beneficiaryHubspotModel)
    }
}
