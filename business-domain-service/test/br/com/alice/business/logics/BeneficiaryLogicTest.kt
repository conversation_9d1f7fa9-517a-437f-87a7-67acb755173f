package br.com.alice.business.logics

import br.com.alice.business.converters.model.toModel
import br.com.alice.business.exceptions.BeneficiaryAlreadyCanceledException
import br.com.alice.business.exceptions.BeneficiaryWithCnpjMustBePjPartnerOrAffiliateContractType
import br.com.alice.business.exceptions.BeneficiaryWithPjContractTypeMustHaveCnpj
import br.com.alice.business.exceptions.CnpjMustBeInCorrectFormat
import br.com.alice.business.exceptions.EmployeeMustHaveHasContributedOnCancelationException
import br.com.alice.business.exceptions.InvalidCancelationDescription
import br.com.alice.business.exceptions.InvalidDependentFieldException
import br.com.alice.business.exceptions.InvalidEmployeeFieldException
import br.com.alice.business.exceptions.InvalidHiredAtFieldException
import br.com.alice.business.exceptions.InvalidRelationTypeFieldException
import br.com.alice.business.exceptions.SelfDependentException
import br.com.alice.business.logics.BeneficiaryLogic.filterBeforeActivatedAt
import br.com.alice.business.logics.BeneficiaryLogic.inferOnboardingFlow
import br.com.alice.business.logics.BeneficiaryLogic.isValidCnpj
import br.com.alice.business.logics.BeneficiaryLogic.validateAndUpdateCancelFields
import br.com.alice.business.logics.BeneficiaryLogic.validateBeneficiary
import br.com.alice.business.logics.BeneficiaryLogic.withDependents
import br.com.alice.business.logics.BeneficiaryLogic.withOnboarding
import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.business.services.BeneficiaryServiceImpl
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.models.Sex
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.services.BeneficiaryModelDataService.FieldOptions
import br.com.alice.data.layer.services.BeneficiaryModelDataService.OrderingOptions
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class BeneficiaryLogicTest {

    private val baseBeneficiary = TestModelFactory.buildBeneficiary()
    private val basePerson = TestModelFactory.buildPerson(sex = Sex.FEMALE, phoneNumber = "31999999999")
    private val beneficiaryTransport = BeneficiaryTransport(
        companyId = baseBeneficiary.companyId,
        parentBeneficiary = baseBeneficiary.parentBeneficiary,
        type = baseBeneficiary.type,
        contractType = baseBeneficiary.contractType,
        parentBeneficiaryRelationType = baseBeneficiary.parentBeneficiaryRelationType,
        activatedAt = baseBeneficiary.activatedAt,
        hiredAt = baseBeneficiary.hiredAt,
        parentBeneficiaryRelatedAt = baseBeneficiary.parentBeneficiaryRelatedAt,
        cnpj = baseBeneficiary.cnpj,
        birthDate = LocalDateTime.of(1997, 6, 2, 0, 0, 0),
        address = basePerson.addresses[0],
        email = basePerson.email,
        firstName = basePerson.firstName,
        lastName = basePerson.lastName,
        mothersName = basePerson.mothersName!!,
        nationalId = basePerson.nationalId,
        phoneNumber = basePerson.phoneNumber!!,
        sex = basePerson.sex!!,
        initialProductId = null
    )

    private fun defaultQueryBuilder(
        init: FieldOptions.() -> Predicate
    ): QueryBuilder<FieldOptions, OrderingOptions> {
        val queryBuilder = QueryBuilder(FieldOptions(), OrderingOptions())

        return queryBuilder.where {
            init()
        }
    }

    companion object {
        @JvmStatic
        fun beneficiarySetupForInference(): List<InferOnboardingFlowSourceData> =
            listOf(
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = null,
                    subcontractDefaultFlowType = null,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = null,
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.UNDEFINED
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = null,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = null,
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.UNDEFINED
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = null,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = null,
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.UNDEFINED
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = null,
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusDays(15),
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusDays(31),
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now().plusDays(30),
                    hiredAt = LocalDateTime.now().minusDays(15),
                    parentHiredAt = null,
                    relatedAt = null,
                    parentActivatedAt = null,
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(31),
                    parentActivatedAt = LocalDateTime.now().minusYears(11),
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(10),
                    parentActivatedAt = LocalDateTime.now(),
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now().plusDays(10),
                    hiredAt = LocalDateTime.now().minusDays(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(10),
                    parentActivatedAt = LocalDateTime.now().minusYears(10),
                    companyStartedAt = null,
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(31),
                    parentActivatedAt = LocalDateTime.now().minusYears(11),
                    companyStartedAt = LocalDate.now().plusDays(40),
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(31),
                    parentActivatedAt = LocalDateTime.now().minusYears(11),
                    companyStartedAt = LocalDate.now(),
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = null,
                    relatedAt = LocalDateTime.now().minusDays(31),
                    parentActivatedAt = LocalDateTime.now().minusYears(11),
                    companyStartedAt = LocalDate.now().minusDays(31),
                    expectedFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
                ),
                InferOnboardingFlowSourceData(
                    companyDefaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    subcontractDefaultFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    currentFlowType = BeneficiaryOnboardingFlowType.UNDEFINED,
                    activatedAt = LocalDateTime.now(),
                    hiredAt = LocalDateTime.now().minusYears(10),
                    parentHiredAt = LocalDateTime.now().minusDays(11),
                    relatedAt = LocalDateTime.now().minusYears(5),
                    parentActivatedAt = LocalDateTime.now().minusYears(11),
                    companyStartedAt = LocalDate.now(),
                    expectedFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                ),
            )

    }

    @Test
    fun `#buildFilterPredicates - if all null`() {
        val result = BeneficiaryLogic.buildFilterQuery(null, null)
        val expected =
            QueryBuilder(FieldOptions(), OrderingOptions()).all()
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterPredicates - if companyId is passed`() {
        val companyId = RangeUUID.generate()
        val result = BeneficiaryLogic.buildFilterQuery(companyId, null)
        val expected = defaultQueryBuilder {
            this.companyId.eq(companyId) and this.archived.eq(false)
        }
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterPredicates - if parentId is passed`() {
        val parentId = RangeUUID.generate()
        val result = BeneficiaryLogic.buildFilterQuery(null, parentId)
        val expected = defaultQueryBuilder {
            this.parentId.eq(parentId) and this.archived.eq(false)
        }

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterPredicates - if all parameters are passed`() {
        val parentId = RangeUUID.generate()
        val companyId = RangeUUID.generate()

        val result = BeneficiaryLogic.buildFilterQuery(companyId, parentId)
        val expected =
            defaultQueryBuilder {
                this.companyId.eq(companyId) and this.parentId.eq(parentId) and this.archived.eq(false)
            }

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildFilterPredicates - if range is passed`() {
        val parentId = RangeUUID.generate()
        val companyId = RangeUUID.generate()
        val range = IntRange(1, 19)

        val result = BeneficiaryLogic.buildFilterQuery(companyId, parentId, range)
        val expected = defaultQueryBuilder {
            this.companyId.eq(companyId) and this.parentId.eq(parentId) and this.archived.eq(false)
        }.orderBy { createdAt }.sortOrder { desc }.offset { range.first }.limit { range.count() }

        assertThat(result).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#buildGetBeneficiaryQueryFilter - if is with dependents`() {
        val beneficiaryId = RangeUUID.generate()

        val result = BeneficiaryLogic.buildGetBeneficiaryQueryFilter(beneficiaryId, true)

        val expected =
            defaultQueryBuilder {
                scope(
                    this.id.eq(beneficiaryId) or this.parentId.eq(beneficiaryId)
                ) and this.archived.eq(false)
            }

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildGetBeneficiaryQueryFilter - if is without dependents`() {
        val beneficiaryId = RangeUUID.generate()

        val result = BeneficiaryLogic.buildGetBeneficiaryQueryFilter(beneficiaryId, false)

        val expected =
            defaultQueryBuilder {
                scope(this.id.eq(beneficiaryId)) and this.archived.eq(false)
            }

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiaryRelationType and parentBeneficiary fields are null for employee beneficiary`() {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiary)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiaryRelationType field is passed for employee beneficiary`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidEmployeeFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiary field is passed for employee beneficiary`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(
            parentBeneficiary = parentBeneficiaryId
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidEmployeeFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiary field is passed for dependent beneficiary should fail without parent type`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiary = parentBeneficiaryId,
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidRelationTypeFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiary field is passed for dependent beneficiary`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiary = parentBeneficiaryId,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiary)
    }


    @Test
    fun `#validateBeneficiary - if parentBeneficiary field is null for dependent beneficiary`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidDependentFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if parentBeneficiary field is it's own id, should return expected exception`() {
        val id = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(
            id = id,
            parentBeneficiary = id,
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        )

        val result = beneficiary.validateBeneficiary()
        ResultAssert.assertThat(result).isFailureOfType(SelfDependentException::class)
    }

    @Test
    fun `#validateBeneficiary - if hiredAt field is null for the employee beneficiary, should return an expected exception`() {
        val beneficiary = TestModelFactory.buildBeneficiary(hiredAt = null)
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidHiredAtFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if hiredAt field is passed for the employee beneficiary, should be successful`() {
        val beneficiary = TestModelFactory.buildBeneficiary(hiredAt = null)
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidHiredAtFieldException::class)
    }

    @Test
    fun `#validateBeneficiary - if hiredAt field is null for the dependent beneficiary, should be successful`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiary = parentBeneficiaryId,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            hiredAt = null
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiary)
    }

    @Test
    fun `#withOnboarding - copy beneficiary with onboarding`() {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val onboarding = BeneficiaryOnboarding(
            beneficiaryId = beneficiary.id,
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            initialProductId = RangeUUID.generate(),
            phases = emptyList(),
        ).toModel()
        val result = beneficiary.withOnboarding(onboarding)
        val expected = beneficiary.copy(onboarding = onboarding)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#withDependents - copy beneficiary with dependents`() {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val onboarding = BeneficiaryOnboarding(
            beneficiaryId = beneficiary.id,
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            initialProductId = RangeUUID.generate(),
            phases = emptyList(),
        ).toModel()
        val result = beneficiary.withOnboarding(onboarding)
        val expected = beneficiary.copy(onboarding = onboarding)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#filterBeforeActivatedAt - filter only with equal or before given activatedAt day, removing with null`() {
        val activatedAt = LocalDate.now()
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(activatedAt = activatedAt.atEndOfTheDay()),
            TestModelFactory.buildBeneficiary(activatedAt = activatedAt.plusDays(1).atEndOfTheDay()),
        )
        val result = beneficiaries.filterBeforeActivatedAt(activatedAt)
        assertThat(result).isEqualTo(listOf(beneficiaries[0]))
    }

    @Test
    fun `#validateBeneficiary - if has cnpj, must be in correct form`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            cnpj = "123456789101112",
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(CnpjMustBeInCorrectFormat::class)
    }

    @Test
    fun `#validateBeneficiary - if has cnpj, must be PJ`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            cnpj = "77.275.634/0001-81",
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiary)
    }

    @Test
    fun `#validateBeneficiary - if is PJ and hasnt cnpj, must given error`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(BeneficiaryWithPjContractTypeMustHaveCnpj::class)
    }

    @Test
    fun `#validateBeneficiary - if has cnpj and is a CLT, must given error`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            cnpj = "77.275.634/0001-81",
            contractType = BeneficiaryContractType.CLT
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result)
            .isFailureOfType(BeneficiaryWithCnpjMustBePjPartnerOrAffiliateContractType::class)
    }

    @Test
    fun `#validateBeneficiary - if has cnpj and doesnt have contractType, must given error`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            cnpj = "77.275.634/0001-81",
        )
        val result = beneficiary.validateBeneficiary()

        ResultAssert.assertThat(result)
            .isFailureOfType(BeneficiaryWithCnpjMustBePjPartnerOrAffiliateContractType::class)
    }

    @Test
    fun `Beneficiary_validateCnpj - just numbers`() {
        val cnpj = "73727057000160"
        val result = cnpj.isValidCnpj()

        assertTrue { result }
    }

    @Test
    fun `Beneficiary_validateCnpj - with characters`() {
        val cnpj = "73.727.057/0001-60"
        val result = cnpj.isValidCnpj()

        assertTrue { result }
    }

    @Test
    fun `Beneficiary_validateCnpj - error empty`() {
        val cnpj = ""
        val result = cnpj.isValidCnpj()

        assertFalse { result }
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - beneficiary already with canceledAt`() {
        val beneficiary = TestModelFactory.buildBeneficiary(canceledAt = LocalDateTime.now()).toModel()
        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = LocalDate.now(),
            canceledReason = BeneficiaryCancelationReason.ANOTHER,
            "",
            true
        )

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)

        ResultAssert.assertThat(result).isFailureOfType(BeneficiaryAlreadyCanceledException::class)
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - must have canceledDescription`() {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = LocalDate.now(),
            canceledReason = BeneficiaryCancelationReason.ANOTHER,
            "",
            true
        )
        val otherBeneficiaryCancelation = beneficiaryCancelation.copy(canceledDescription = null)

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)
        val otherResult = beneficiary.validateAndUpdateCancelFields(otherBeneficiaryCancelation)

        ResultAssert.assertThat(result).isFailureOfType(InvalidCancelationDescription::class)
        ResultAssert.assertThat(otherResult).isFailureOfType(InvalidCancelationDescription::class)
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - Employee must have contributed`() {
        val beneficiary = TestModelFactory.buildBeneficiary(type = BeneficiaryType.EMPLOYEE).toModel()
        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = LocalDate.now(),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            null
        )

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)

        ResultAssert.assertThat(result).isFailureOfType(EmployeeMustHaveHasContributedOnCancelationException::class)
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - Dependent doesnt need to have contributed`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.DEPENDENT, canceledAt = null, canceledReason = null,
            canceledDescription = null, hasContributed = null
        ).toModel()
        val canceledAt = LocalDate.now()

        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            null
        )

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)
        val expected = beneficiary.copy(
            canceledAt = canceledAt.atEndOfTheDay(),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            hasContributed = null,
        )
        ResultAssert.assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - success`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.EMPLOYEE, canceledAt = null, canceledReason = null,
            canceledDescription = null, hasContributed = null
        ).toModel()
        val canceledAt = LocalDate.now()

        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            false
        )

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)
        val expected = beneficiary.copy(
            canceledAt = canceledAt.atEndOfTheDay(),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            hasContributed = false,
        )
        ResultAssert.assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `BeneficiaryCancelation_validateBeneficiaryAndMemberShip - should be canceled instantly if member is pending`() {
        val beneficiary = TestModelFactory.buildBeneficiary(
            type = BeneficiaryType.EMPLOYEE, canceledAt = null, canceledReason = null,
            canceledDescription = null, hasContributed = null
        ).toModel()
        val canceledAt = LocalDateTime.now().atEndOfTheDay()

        val beneficiaryCancelation = BeneficiaryCancelation(
            canceledAt = canceledAt.toLocalDate(),
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            false
        )

        val result = beneficiary.validateAndUpdateCancelFields(beneficiaryCancelation)
        val expected = beneficiary.copy(
            canceledAt = canceledAt,
            canceledReason = BeneficiaryCancelationReason.EXCLUSION_REQUEST,
            canceledDescription = null,
            hasContributed = false,
        )
        ResultAssert.assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#validateBeneficiaryTransport - if parentBeneficiaryRelationType and parentBeneficiary fields are null for employee beneficiary`() {
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiaryTransport)
    }

    @Test
    fun `#validateBeneficiaryTransport - if parentBeneficiaryRelationType field is passed for employee beneficiary`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidEmployeeFieldException::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if parentBeneficiary field is passed for employee beneficiary`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiaryTransport = beneficiaryTransport.copy(
            parentBeneficiary = parentBeneficiaryId
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidEmployeeFieldException::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if parentBeneficiary field is passed for dependent beneficiary`() {
        val parentBeneficiaryId = RangeUUID.generate()
        val beneficiaryTransport = beneficiaryTransport.copy(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiary = parentBeneficiaryId,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiaryTransport)
    }

    @Test
    fun `#validateBeneficiaryTransport - if parentBeneficiary field is null for dependent beneficiary`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            type = BeneficiaryType.DEPENDENT,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(InvalidDependentFieldException::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if has cnpj, must be in correct form`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            cnpj = "123456789101112",
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(CnpjMustBeInCorrectFormat::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if has cnpj, must be PJ`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            cnpj = "77.275.634/0001-81",
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isSuccessWithData(beneficiaryTransport)
    }

    @Test
    fun `#validateBeneficiaryTransport - if is PJ and hasnt cnpj, must given error`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            contractType = BeneficiaryContractType.PJ
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result).isFailureOfType(BeneficiaryWithPjContractTypeMustHaveCnpj::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if has cnpj and is a CLT, must given error`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            cnpj = "77.275.634/0001-81",
            contractType = BeneficiaryContractType.CLT
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result)
            .isFailureOfType(BeneficiaryWithCnpjMustBePjPartnerOrAffiliateContractType::class)
    }

    @Test
    fun `#validateBeneficiaryTransport - if has cnpj and doesnt have contractType, must given error`() {
        val beneficiaryTransport = beneficiaryTransport.copy(
            cnpj = "77.275.634/0001-81",
        )
        val result = beneficiaryTransport.validateBeneficiary()

        ResultAssert.assertThat(result)
            .isFailureOfType(BeneficiaryWithCnpjMustBePjPartnerOrAffiliateContractType::class)
    }

    @Test
    fun `#extractHolderFromDependents - success`() {
        val beneficiary = TestModelFactory.buildBeneficiary().toModel()
        val beneficiaryDependent = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id).toModel()

        val expected = beneficiary.withDependents(listOf(beneficiaryDependent))

        val result =
            BeneficiaryLogic.extractHolderFromDependents(beneficiary.id, listOf(beneficiary, beneficiaryDependent))

        assertThat(result).isEqualTo(expected)
    }

    @Nested
    inner class InferOnboardingFlow {
        @ParameterizedTest()
        @MethodSource("br.com.alice.business.logics.BeneficiaryLogicTest#beneficiarySetupForInference")
        fun `should infer as expected`(
            data: InferOnboardingFlowSourceData
        ) {
            val companyDefaultFlowType = data.companyDefaultFlowType
            val subcontractDefaultFlowType = data.subcontractDefaultFlowType
            val currentFlowType = data.currentFlowType
            val activatedAt = data.activatedAt
            val parentHiredAt = data.parentHiredAt
            val hiredAt = data.hiredAt
            val relatedAt = data.relatedAt
            val parentActivatedAt = data.parentActivatedAt
            val companyStartedAt = data.companyStartedAt
            val expectedFlowType = data.expectedFlowType

            val contract = TestModelFactory.buildCompanyContract(startedAt = companyStartedAt).toModel()
            val company = TestModelFactory.buildCompany().toModel()
                .copy(contractIds = listOf(contract.id), defaultFlowType = companyDefaultFlowType)
            val subcontract =
                TestModelFactory.buildCompanySubContract(contractId = contract.id).toModel()
                    .copy(defaultFlowType = subcontractDefaultFlowType)

            val companyInfo = BeneficiaryServiceImpl.BeneficiaryCompanyInfo(
                contract = contract,
                subContract = subcontract,
                company = company,
            )

            val beneficiary = TestModelFactory.buildBeneficiary(
                activatedAt = activatedAt,
                type = parentActivatedAt?.let { BeneficiaryType.DEPENDENT } ?: BeneficiaryType.EMPLOYEE,
                hiredAt = parentActivatedAt?.let { null } ?: hiredAt,
                parentBeneficiaryRelatedAt = relatedAt,
            ).toModel()
            val parentBeneficiary = parentActivatedAt?.let {
                TestModelFactory.buildBeneficiary(
                    activatedAt = it,
                    hiredAt = parentHiredAt,
                    type = BeneficiaryType.EMPLOYEE,
                ).toModel()
            }

            val result = beneficiary.inferOnboardingFlow(currentFlowType, companyInfo, parentBeneficiary).get()
            assertThat(result).isEqualTo(expectedFlowType)
        }
    }
}

data class InferOnboardingFlowSourceData(
    val companyDefaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val subcontractDefaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val currentFlowType: BeneficiaryOnboardingFlowType,
    val activatedAt: LocalDateTime,
    val hiredAt: LocalDateTime? = null,
    val parentHiredAt: LocalDateTime? = null,
    val relatedAt: LocalDateTime? = null,
    val parentActivatedAt: LocalDateTime? = null,
    val companyStartedAt: LocalDate? = null,
    val expectedFlowType: BeneficiaryOnboardingFlowType,
)
