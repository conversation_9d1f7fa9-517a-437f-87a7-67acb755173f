package br.com.alice.business.services

import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.BeneficiaryHubspotModelDataService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryHubspotServiceImplTest {
    private val beneficiaryHubspotDataService: BeneficiaryHubspotModelDataService = mockk()

    private val beneficiaryHubspotService = BeneficiaryHubspotServiceImpl(
        beneficiaryHubspotDataService,
    )

    @BeforeTest
    fun setup() {
        mockkObject(
            logger,
            BeneficiaryMetric
        )
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

//    @Test
//    fun `#get by id returns beneficiaryHubspot`() = runBlocking {
//        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot().toModel()
//
//        coEvery { beneficiaryHubspotDataService.get(any()) } returns beneficiaryHubspot
//
//        val result = beneficiaryHubspotService.get(beneficiaryHubspot.id)
//        assertThat(result).isSuccessWithData(beneficiaryHubspot)
//
//        coVerifyOnce {
//            beneficiaryHubspotDataService.get(beneficiaryHubspot.id)
//        }
//    }

    @Test
    fun `#findByBeneficiaryIds returns beneficiaryHubspots`() = runBlocking {
        val beneficiaryId1 = RangeUUID.generate()
        val beneficiaryId2 = RangeUUID.generate()
        val beneficiaryHubspot1 = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId1).toModel()
        val beneficiaryHubspot2 = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId2).toModel()

        coEvery { beneficiaryHubspotDataService.find(any()) } returns listOf(beneficiaryHubspot1, beneficiaryHubspot2)

        val result = beneficiaryHubspotService.findByBeneficiaryIds(listOf(beneficiaryId1, beneficiaryId2))
        assertThat(result).isSuccessWithData(
            listOf(beneficiaryHubspot1.toTransport(), beneficiaryHubspot2.toTransport())
        )

        coVerifyOnce {
            beneficiaryHubspotDataService.find(queryEq { where {
                this.beneficiaryId.inList(listOf(beneficiaryId1, beneficiaryId2))
            }})
        }
    }

}
