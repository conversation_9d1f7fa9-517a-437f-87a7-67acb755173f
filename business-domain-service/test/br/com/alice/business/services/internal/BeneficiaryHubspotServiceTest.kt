package br.com.alice.business.services.internal

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CompanyService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.exceptions.BeneficiaryDoesntHaveOnboarding
import br.com.alice.business.exceptions.BeneficiaryHubspotAlreadyExistsException
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotContactConflictException
import br.com.alice.communication.crm.sales.b2b.AssociationResult
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipelineException
import br.com.alice.communication.crm.sales.b2b.ContactResult
import br.com.alice.communication.crm.sales.b2b.DealResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.services.BeneficiaryHubspotModelDataService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryHubspotServiceTest {
    private val beneficiaryHubspotDataService: BeneficiaryHubspotModelDataService = mockk()
    private val personService: PersonService = mockk()
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()
    private val productService: ProductService = mockk()

    private val beneficiaryHubspotService = BeneficiaryHubspotService(
        beneficiaryHubspotDataService,
        businessSalesCrmPipeline,
        beneficiaryService,
        companyService,
        personService,
        productService
    )

    @BeforeTest
    fun setup() {
        mockkObject(
            logger,
            BeneficiaryMetric
        )
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `#create should add BeneficiaryHubspot as expected`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val initialProductId = RangeUUID.generate()
        val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

        val company = TestModelFactory.buildCompany(
            id = beneficiary.companyId,
            availableProducts = listOf(initialProductId),
            defaultProductId = initialProductId
        )
        val product = TestModelFactory.buildProduct(
            id = initialProductId,
        )
        val person = TestModelFactory.buildPerson(
            personId = beneficiary.personId,
        )
        val contactResult = ContactResult(id = "12341")
        val dealResult = DealResult(id = "123")
        val associationResult = AssociationResult(true)
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
            beneficiaryId = beneficiary.id,
            externalDealId = "123",
            externalContactId = "12341",
        ).toModel()
        val metadata = mapOf("empresaVidas" to "2 a 5")

        coEvery {
            beneficiaryHubspotDataService.getByPersonAndCompanyId(
                any(),
                any()
            )
        } returns NotFoundException()
        coEvery { companyService.get(any()) } returns company
        coEvery { personService.get(any()) } returns person
        coEvery { productService.getProduct(any()) } returns product
        coEvery { businessSalesCrmPipeline.createNewContact(any()) } returns contactResult
        coEvery { businessSalesCrmPipeline.createDeal(any()) } returns dealResult
        coEvery { businessSalesCrmPipeline.associateContactToDeal(any(), any()) } returns associationResult
        coEvery { beneficiaryHubspotDataService.add(any()) } returns beneficiaryHubspot

        val result = beneficiaryHubspotService.create(beneficiary, initialProductId, flowType, metadata)
        assertThat(result).isSuccessWithData(beneficiaryHubspot.toTransport())

        coVerifyOnce { companyService.get(beneficiary.companyId) }
        coVerifyOnce { personService.get(beneficiary.personId) }
        coVerifyOnce { productService.getProduct(initialProductId) }
        coVerifyOnce { businessSalesCrmPipeline.createNewContact(any()) }
        coVerifyOnce { businessSalesCrmPipeline.createDeal(any()) }
        coVerifyOnce { businessSalesCrmPipeline.associateContactToDeal("12341", "123") }
        coVerifyOnce { beneficiaryHubspotDataService.add(any()) }
    }


    @Test
    fun `#create should add BeneficiaryHubspot getting info from Exception`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

            val company = TestModelFactory.buildCompany(
                id = beneficiary.companyId,
                availableProducts = listOf(initialProductId),
                defaultProductId = initialProductId
            )
            val product = TestModelFactory.buildProduct(
                id = initialProductId,
            )
            val person = TestModelFactory.buildPerson(
                personId = beneficiary.personId,
            )
            val dealResult = DealResult(id = "123")
            val associationResult = AssociationResult(true)
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
                beneficiaryId = beneficiary.id,
                externalDealId = "123",
                externalContactId = "12341",
            ).toModel()

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns NotFoundException()
            coEvery { companyService.get(any()) } returns company
            coEvery { personService.get(any()) } returns person
            coEvery { productService.getProduct(any()) } returns product
            coEvery { businessSalesCrmPipeline.createNewContact(any()) } throws HubspotContactConflictException(
                message = "error",
                contactId = "12341"
            )
            coEvery { businessSalesCrmPipeline.createDeal(any()) } returns dealResult
            coEvery { businessSalesCrmPipeline.associateContactToDeal(any(), any()) } returns associationResult
            coEvery { beneficiaryHubspotDataService.add(any()) } returns beneficiaryHubspot

            val result = beneficiaryHubspotService.create(beneficiary, initialProductId, flowType)
            assertThat(result).isSuccessWithData(beneficiaryHubspot.toTransport())

            coVerifyOnce { companyService.get(beneficiary.companyId) }
            coVerifyOnce { personService.get(beneficiary.personId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { businessSalesCrmPipeline.createNewContact(any()) }
            coVerifyOnce { businessSalesCrmPipeline.createDeal(any()) }
            coVerifyOnce { businessSalesCrmPipeline.associateContactToDeal("12341", "123") }
            coVerifyOnce { beneficiaryHubspotDataService.add(any()) }
        }

    @Test
    fun `#create should add BeneficiaryHubspot getting product from Company`() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

            val company = TestModelFactory.buildCompany(
                id = beneficiary.companyId,
                availableProducts = listOf(initialProductId),
                defaultProductId = initialProductId
            )
            val product = TestModelFactory.buildProduct(
                id = initialProductId,
            )
            val person = TestModelFactory.buildPerson(
                personId = beneficiary.personId,
            )
            val dealResult = DealResult(id = "123")
            val associationResult = AssociationResult(true)
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
                beneficiaryId = beneficiary.id,
                externalDealId = "123",
                externalContactId = "12341",
            ).toModel()

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns NotFoundException()
            coEvery { companyService.get(any()) } returns company
            coEvery { personService.get(any()) } returns person
            coEvery { productService.getProduct(any()) } returns product
            coEvery { businessSalesCrmPipeline.createNewContact(any()) } throws HubspotContactConflictException(
                message = "error",
                contactId = "12341"
            )
            coEvery { businessSalesCrmPipeline.createDeal(any()) } returns dealResult
            coEvery { businessSalesCrmPipeline.associateContactToDeal(any(), any()) } returns associationResult
            coEvery { beneficiaryHubspotDataService.add(any()) } returns beneficiaryHubspot

            val result = beneficiaryHubspotService.create(beneficiary, initialProductId, flowType)
            assertThat(result).isSuccessWithData(beneficiaryHubspot.toTransport())

            coVerifyOnce { companyService.get(beneficiary.companyId) }
            coVerifyOnce { personService.get(beneficiary.personId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { businessSalesCrmPipeline.createNewContact(any()) }
            coVerifyOnce { businessSalesCrmPipeline.createDeal(any()) }
            coVerifyOnce { businessSalesCrmPipeline.associateContactToDeal("12341", "123") }
            coVerifyOnce { beneficiaryHubspotDataService.add(any()) }
        }

    @Test
    fun `#create should not add a BeneficiaryHubspot when it gets some error `() =
        runBlocking {
            val beneficiary = TestModelFactory.buildBeneficiary()
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

            val company = TestModelFactory.buildCompany(
                id = beneficiary.companyId,
                availableProducts = listOf(initialProductId),
                defaultProductId = initialProductId
            )
            val product = TestModelFactory.buildProduct(
                id = initialProductId,
            )
            val person = TestModelFactory.buildPerson(
                personId = beneficiary.personId,
            )
            val dealResult = DealResult(id = "123")
            val associationResult = AssociationResult(true)
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
                beneficiaryId = beneficiary.id,
                externalDealId = "123",
                externalContactId = "12341",
            ).toModel()

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns NotFoundException()
            coEvery { companyService.get(any()) } returns company
            coEvery { personService.get(any()) } returns person
            coEvery { productService.getProduct(any()) } returns product
            coEvery { businessSalesCrmPipeline.createNewContact(any()) } throws BusinessSalesCrmPipelineException("")
            coEvery { businessSalesCrmPipeline.createDeal(any()) } returns dealResult
            coEvery { businessSalesCrmPipeline.associateContactToDeal(any(), any()) } returns associationResult
            coEvery { beneficiaryHubspotDataService.add(any()) } returns beneficiaryHubspot

            val result = beneficiaryHubspotService.create(beneficiary, initialProductId, flowType)
            assertThat(result).isFailureOfType(BusinessSalesCrmPipelineException::class)

            coVerifyOnce { companyService.get(beneficiary.companyId) }
            coVerifyOnce { personService.get(beneficiary.personId) }
            coVerifyOnce { productService.getProduct(initialProductId) }
            coVerifyOnce { businessSalesCrmPipeline.createNewContact(any()) }
            coVerifyNone { businessSalesCrmPipeline.createDeal(any()) }
            coVerifyNone { businessSalesCrmPipeline.associateContactToDeal(any(),any()) }
            coVerifyNone { beneficiaryHubspotDataService.add(any()) }
        }

    @Test
    fun `#create should not add BeneficiaryHubspot when it already exists`() =
        runBlocking {
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot().toModel()
            val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryHubspot.beneficiaryId)
            val initialProductId = RangeUUID.generate()
            val flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns beneficiaryHubspot

            val result = beneficiaryHubspotService.create(beneficiary, initialProductId, flowType)

            assertThat(result).isFailureOfType(BeneficiaryHubspotAlreadyExistsException::class)

            coVerifyOnce {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    beneficiary.personId,
                    beneficiary.companyId
                )
            }
            coVerifyNone { beneficiaryHubspotDataService.add(any()) }
        }

    @Test
    fun `#getOrCreateByPersonAndCompanyId should get BeneficiaryHubspot`() = runBlocking {
        val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot().toModel()
        val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryHubspot.beneficiaryId)

        coEvery {
            beneficiaryHubspotDataService.getByPersonAndCompanyId(
                any(),
                any()
            )
        } returns beneficiaryHubspot

        val result = beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
            beneficiary.id,
            beneficiary.personId,
            beneficiary.companyId
        )
        assertThat(result).isSuccessWithData(beneficiaryHubspot.toTransport())

        coVerifyOnce {
            beneficiaryHubspotDataService.getByPersonAndCompanyId(beneficiary.personId, beneficiary.companyId)
        }
    }

    @Test
    fun `#getOrCreateByPersonAndCompanyId should throw Error when there is no beneficiaryHubspot and beneficiary hasn't onboarding`() =
        runBlocking {
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot().toModel()
            val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryHubspot.beneficiaryId)

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns NotFoundException()
            coEvery { beneficiaryService.get(any(), any()) } returns beneficiary

            val result = beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                beneficiary.id,
                beneficiary.personId,
                beneficiary.companyId
            )
            assertThat(result).isFailureOfType(BeneficiaryDoesntHaveOnboarding::class)

            coVerifyOnce {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(beneficiary.personId, beneficiary.companyId)
            }
            coVerifyOnce { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
        }

    @Test
    fun `#getOrCreateByPersonAndCompanyId should create beneficiaryHubspot when not exists and log`() =
        runBlocking {
            val beneficiaryOnboarding =
                TestModelFactory.buildBeneficiaryOnboarding()
            val beneficiary = TestModelFactory.buildBeneficiary(
                onboarding = beneficiaryOnboarding
            )
            val company = TestModelFactory.buildCompany(
                id = beneficiary.companyId,
                availableProducts = listOf(beneficiaryOnboarding.initialProductId),
                defaultProductId = beneficiaryOnboarding.initialProductId
            )
            val product = TestModelFactory.buildProduct(
                id = beneficiaryOnboarding.initialProductId,
            )
            val person = TestModelFactory.buildPerson(
                personId = beneficiary.personId,
            )
            val contactResult = ContactResult(id = "12341")
            val dealResult = DealResult(id = "123")
            val associationResult = AssociationResult(true)
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(
                beneficiaryId = beneficiary.id,
                externalDealId = "123",
                externalContactId = "12341",
            ).toModel()

            coEvery {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    any(),
                    any()
                )
            } returns NotFoundException()
            coEvery { beneficiaryService.get(any(), any()) } returns beneficiary
            coEvery { companyService.get(any()) } returns company
            coEvery { personService.get(any()) } returns person
            coEvery { productService.getProduct(any()) } returns product
            coEvery { businessSalesCrmPipeline.createNewContact(any()) } returns contactResult
            coEvery { businessSalesCrmPipeline.createDeal(any()) } returns dealResult
            coEvery { businessSalesCrmPipeline.associateContactToDeal(any(), any()) } returns associationResult
            coEvery { beneficiaryHubspotDataService.add(any()) } returns beneficiaryHubspot

            val result = beneficiaryHubspotService.getOrCreateByPersonAndCompanyId(
                beneficiary.id,
                beneficiary.personId,
                beneficiary.companyId
            )
            assertThat(result).isSuccessWithData(beneficiaryHubspot.toTransport())

            coVerifyOnce {
                beneficiaryHubspotDataService.getByPersonAndCompanyId(
                    beneficiary.personId,
                    beneficiary.companyId
                )
            }
            coVerifyOnce { BeneficiaryMetric.metrifyBeneficiaryHubspotNotFound(beneficiary.id) }
            coVerifyOnce {
                logger.info(
                    "BeneficiaryHubspot not found",
                    "beneficiary_id" to beneficiaryHubspot.beneficiaryId,
                )
            }
            coVerifyOnce { beneficiaryService.get(beneficiary.id, FindOptions(withOnboarding = true)) }
            coVerifyOnce { companyService.get(beneficiary.companyId) }
            coVerifyOnce { personService.get(beneficiary.personId) }
            coVerifyOnce { productService.getProduct(beneficiaryOnboarding.initialProductId) }
            coVerifyOnce { businessSalesCrmPipeline.createNewContact(any()) }
            coVerifyOnce { businessSalesCrmPipeline.createDeal(any()) }
            coVerifyOnce { businessSalesCrmPipeline.associateContactToDeal("12341", "123") }
            coVerifyOnce { beneficiaryHubspotDataService.add(any()) }

        }
}
