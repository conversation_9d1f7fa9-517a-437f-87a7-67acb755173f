package br.com.alice.business.controllers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.client.StandardCostService
import br.com.alice.business.clients.CompanyExternalInformationResponse
import br.com.alice.business.clients.ReceitaFederalClient
import br.com.alice.business.converters.CompanyProductPriceListingConverter.toPriceListItems
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.BeneficiaryChangedEvent
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.business.events.RequestUpdateDependentEmployeeBind
import br.com.alice.business.models.*
import br.com.alice.business.services.internal.BeneficiaryHubspotService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.and
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.services.BeneficiaryModelDataService
import br.com.alice.data.layer.services.BeneficiaryOnboardingModelDataService
import br.com.alice.data.layer.services.CompanyContractModelDataService
import br.com.alice.data.layer.services.CompanyModelDataService
import br.com.alice.data.layer.services.CompanySubContractModelDataService
import br.com.alice.ehr.model.BackfillResponse
import br.com.alice.membership.client.ContractGenerator
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.nullvs.common.NullvsActionType.UPDATE
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.PriceListingService
import br.com.alice.product.client.ProductPriceListingService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackFillControllerTest : RecurrentControllerTestHelper() {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()
    private val memberService: MemberService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val memberProductPriceService: MemberProductPriceService = mockk()
    private val productService: ProductService = mockk()
    private val contractGenerator: ContractGenerator = mockk()
    private val cassiMemberService: CassiMemberService = mockk()
    private val beneficiaryHubspotService: BeneficiaryHubspotService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val productPriceListingService: ProductPriceListingService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val beneficiaryDataService: BeneficiaryModelDataService = mockk()
    private val beneficiaryOnboardingDataService: BeneficiaryOnboardingModelDataService = mockk()
    private val companyContractDataService: CompanyContractModelDataService = mockk()
    private val companySubcontractDataService: CompanySubContractModelDataService = mockk()
    private val companyDataService: CompanyModelDataService = mockk()
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline = mockk()
    private val priceListingService: PriceListingService = mockk()
    private val personService: PersonService = mockk()
    private val standardCostService: StandardCostService = mockk()
    private val receitaFederalClient: ReceitaFederalClient = mockk()

    private val controller = BackFillController(
        beneficiaryService,
        companyService,
        memberService,
        kafkaProducerService,
        memberProductPriceService,
        productService,
        contractGenerator,
        cassiMemberService,
        beneficiaryHubspotService,
        companyContractService,
        billingAccountablePartyService,
        beneficiaryOnboardingService,
        companySubContractService,
        productPriceListingService,
        companyProductPriceListingService,
        beneficiaryDataService,
        beneficiaryOnboardingDataService,
        companyContractDataService,
        companySubcontractDataService,
        companyDataService,
        businessSalesCrmPipeline,
        priceListingService,
        personService,
        standardCostService,
        receitaFederalClient,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `#archiveBeneficiaryById - call service for ids`() = runBlocking {
        val beneficiaryId1 = RangeUUID.generate()
        val beneficiaryId2 = RangeUUID.generate()
        val beneficiary1 = TestModelFactory.buildBeneficiary(id = beneficiaryId1)
        val beneficiary2 = TestModelFactory.buildBeneficiary(id = beneficiaryId2)
        val request = ArchiveBeneficiaryRequest(
            ids = listOf(beneficiaryId1, beneficiaryId2),
        )

        coEvery { beneficiaryService.archiveBeneficiaryById(beneficiaryId1) } returns beneficiary1.success()
        coEvery { beneficiaryService.archiveBeneficiaryById(beneficiaryId2) } returns Exception("").failure()

        internalAuthentication {
            post("/backfill/archive_beneficiaries", request) { response ->
                assertThat(response).isOK()
                val content = gson.fromJson(response.bodyAsText(), ArchiveBeneficiaryResponse::class.java)
                assertThat(content.response[0].success).isEqualTo(true)
                assertThat(content.response[1].success).isEqualTo(false)
            }
        }

        coVerify(exactly = 1) { beneficiaryService.archiveBeneficiaryById(beneficiary1.id) }
        coVerify(exactly = 1) { beneficiaryService.archiveBeneficiaryById(beneficiary2.id) }
    }

    @Test
    fun `#backFillOnboardingPhase - should filter and produce expected events`() = runBlocking {
        val cnpj = "cnpj-1"
        val company = TestModelFactory.buildCompany(cnpj = cnpj)

        val beneficiary1 = TestModelFactory.buildBeneficiary(
            companyId = company.id,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(
                    TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
                )
            )
        )

        val beneficiary2 = TestModelFactory.buildBeneficiary(
            companyId = company.id,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(
                    TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)
                )
            )
        )

        val beneficiary3 = TestModelFactory.buildBeneficiary(
            companyId = company.id,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                phases = listOf(
                    TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)
                )
            )
        )

        val beneficiaries = listOf(beneficiary1, beneficiary2, beneficiary3)

        val member1 = TestModelFactory.buildMember(
            personId = beneficiary1.personId,
            activationDate = LocalDateTime.of(2022, 1, 10, 10, 10),
            status = MemberStatus.ACTIVE,
        )
        val member2 = TestModelFactory.buildMember(
            personId = beneficiary2.personId,
            activationDate = LocalDateTime.of(2022, 1, 22, 10, 10),
            status = MemberStatus.PENDING,
        )

        val members = listOf(member1, member2)

        coEvery { companyService.findByCnpjs(listOf(cnpj)) } returns listOf(company).success()
        coEvery { beneficiaryService.findByCompanyId(company.id, any()) } returns beneficiaries.success()
        coEvery {
            memberService.findByPersonIds(
                listOf(
                    beneficiary1,
                    beneficiary2
                ).map { it.personId })
        } returns members.success()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()

        val status = MemberStatus.ACTIVE

        val request = BackFillOnboardingRequest(
            cnpjs = listOf(cnpj),
            memberStatus = status,
            currentPhaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
            toPhaseType = BeneficiaryOnboardingPhaseType.FINISHED,
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        )

        internalAuthentication {
            post("/backfill/onboarding_phases", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: MoveToPhaseEvent ->
                it.payload.beneficiaryId == beneficiary1.id &&
                        it.payload.requiredPhaseType == request.toPhaseType
            }, beneficiary1.id.toString())
        }
        coVerify(exactly = 0) {
            kafkaProducerService.produce(match { it: MoveToPhaseEvent ->
                it.payload.beneficiaryId == beneficiary2.id
            }, any())
        }
        coVerify(exactly = 0) {
            kafkaProducerService.produce(match { it: MoveToPhaseEvent ->
                it.payload.beneficiaryId == beneficiary3.id
            }, any())
        }
    }

    @Test
    fun `#retryBeneficiaryCreated - should produce BeneficiaryCreated event again`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.success()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()

        val request = BackFillBeneficiaryCreated(
            beneficiaryId = beneficiary.id,
            initialProductId = RangeUUID.generate(),
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            cassiMemberInfo = CassiMemberInfo("123", "02/06/1997", "02/06/2010")
        )

        internalAuthentication {
            post("/backfill/retry_beneficiary_created", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: BeneficiaryCreatedEvent ->
                it.payload.beneficiary.id == beneficiary.id &&
                        it.payload.flowType == request.flowType &&
                        it.payload.initialProductId == request.initialProductId
            }, beneficiary.id.toString())
        }
    }

    @Test
    fun `#backFillMemberProductPrice - should call expected operations`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember(personId = personId)
        val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = member.id)

        coEvery { memberService.findActiveMembership(any()) } returns member.success()
        coEvery { memberProductPriceService.setCurrent(any(), any(), any()) } returns memberProductPrice.success()

        val request = BackFillMemberProductPrice(personId = personId)

        internalAuthentication {
            post("/backfill/member_product_price", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) { memberService.findActiveMembership(personId) }
        coVerify(exactly = 1) { memberProductPriceService.setCurrent(member.id, member.productId, any()) }
    }

    @Test
    fun `#backFillBeneficiaryMember with beneficiaries without memberId should call expected operations`() =
        runBlocking {
            val personId = PersonId()
            val member = TestModelFactory.buildMember(personId = personId)
            val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)
            val updatedBeneficiary = beneficiary.copy(memberId = member.id)
            val beneficiaries = listOf(
                BackFillBeneficiaryMember(
                    personId = beneficiary.personId,
                )
            )

            coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary.success()
            coEvery { memberService.getCurrent(beneficiary.personId) } returns member.success()
            coEvery { beneficiaryService.update(updatedBeneficiary) } returns updatedBeneficiary.success()

            val request = BackFillBeneficiaryMemberRequest(beneficiaries = beneficiaries)

            internalAuthentication {
                post("/backfill/beneficiary_member", request) { response ->
                    val expectedResponse = listOf(updatedBeneficiary)
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerify(exactly = 1) { beneficiaryService.findByPersonId(beneficiary.personId) }
            coVerify(exactly = 1) { memberService.getCurrent(beneficiary.personId) }
            coVerify(exactly = 1) { beneficiaryService.update(updatedBeneficiary) }
        }

    @Test
    fun `#backFillBeneficiaryMember with beneficiaries and memberId should call expected operations`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember(personId = personId)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)
        val updatedBeneficiary = beneficiary.copy(memberId = member.id)
        val beneficiaries = listOf(
            BackFillBeneficiaryMember(
                personId = beneficiary.personId,
                memberId = member.id,
            )
        )

        coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary.success()
        coEvery { beneficiaryService.update(updatedBeneficiary) } returns updatedBeneficiary.success()

        val request = BackFillBeneficiaryMemberRequest(beneficiaries = beneficiaries)

        internalAuthentication {
            post("/backfill/beneficiary_member", request) { response ->
                val expectedResponse = listOf(updatedBeneficiary)
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerify(exactly = 1) { beneficiaryService.findByPersonId(beneficiary.personId) }
        coVerify(exactly = 0) { memberService.getCurrent(any()) }
        coVerify(exactly = 1) { beneficiaryService.update(updatedBeneficiary) }
    }

    @Test
    fun `#backFillBeneficiaryMember with cnpjs should call expected operations`() = runBlocking {
        val personId = PersonId()
        val company = TestModelFactory.buildCompany()
        val member = TestModelFactory.buildMember(personId = personId)
        val beneficiary = TestModelFactory.buildBeneficiary(personId = personId, companyId = company.id)
        val updatedBeneficiary = beneficiary.copy(memberId = member.id)

        coEvery { companyService.findByCnpj(company.cnpj) } returns company.success()
        coEvery { beneficiaryService.findByCompanyId(company.id) } returns listOf(beneficiary).success()
        coEvery { beneficiaryService.findByPersonId(beneficiary.personId) } returns beneficiary.success()
        coEvery { memberService.getCurrent(beneficiary.personId) } returns member.success()
        coEvery { beneficiaryService.update(updatedBeneficiary) } returns updatedBeneficiary.success()

        val request = BackFillBeneficiaryMemberRequest(cnpjs = listOf(company.cnpj))

        internalAuthentication {
            post("/backfill/beneficiary_member", request) { response ->
                val expectedResponse = listOf(updatedBeneficiary)
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerify(exactly = 1) { companyService.findByCnpj(company.cnpj) }
        coVerify(exactly = 1) { beneficiaryService.findByCompanyId(company.id) }
        coVerify(exactly = 1) { beneficiaryService.findByPersonId(beneficiary.personId) }
        coVerify(exactly = 1) { memberService.getCurrent(beneficiary.personId) }
        coVerify(exactly = 1) { beneficiaryService.update(updatedBeneficiary) }
    }

    @Test
    fun `#createBeneficiaryForActiveMember - backFill for beneficiary linking to member`() = runBlocking {
        val request = BackfillBeneficiaryWithMemberIdRequest(
            oldBeneficiaryId = RangeUUID.generate(),
            memberId = RangeUUID.generate(),
            productId = RangeUUID.generate(),
        )
        val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase()
        val beneficiaryOnboarding =
            TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(beneficiaryOnboardingPhase))
        val beneficiary = TestModelFactory.buildBeneficiary(onboarding = beneficiaryOnboarding)
        val newBeneficiary = beneficiary.copy(id = RangeUUID.generate(), memberId = request.memberId)

        coEvery { beneficiaryService.get(any(), any()) } returns beneficiary.success()
        coEvery {
            beneficiaryService.addBeneficiaryForExistentMember(
                any(),
                any(),
                any(),
                any()
            )
        } returns newBeneficiary.success()

        internalAuthentication {
            post("/backfill/beneficiary_with_existent_member", request) { response ->
                assertThat(response).isOKWithData(newBeneficiary)
            }
        }

        coVerify(exactly = 1) { beneficiaryService.get(request.oldBeneficiaryId, FindOptions(withOnboarding = true)) }
        coVerify(exactly = 1) {
            beneficiaryService.addBeneficiaryForExistentMember(
                any(),
                initialProductId = request.productId,
                flowType = newBeneficiary.onboarding!!.flowType,
                phase = newBeneficiary.onboarding!!.currentPhase!!.phase
            )
        }
    }

    @Test
    fun `#populateHiredAtForBeneficiary - backfill to populate hired at on beneficiaries`() = runBlocking {
        val firstMember = BackFillHiredAtBeneficiaries(
            memberId = RangeUUID.generate(),
            hiredAt = "2000-08-22"
        )
        val secondMember = BackFillHiredAtBeneficiaries(
            memberId = RangeUUID.generate(),
            hiredAt = "2022-07-25"
        )

        val request = BackFillHiredAtBeneficiaryRequest(
            beneficiaries = listOf(firstMember, secondMember)
        )

        val beneficiary = TestModelFactory.buildBeneficiary()

        coEvery { beneficiaryService.findByMemberId(any(), any()) } returns beneficiary.success()
        coEvery { beneficiaryService.update(any()) } returns beneficiary.success()

        internalAuthentication {
            post("/backfill/beneficiary_hired_at", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) { beneficiaryService.findByMemberId(firstMember.memberId) }
        coVerify(exactly = 1) { beneficiaryService.findByMemberId(secondMember.memberId) }
        coVerify(exactly = 1) {
            beneficiaryService.update(
                beneficiary.copy(
                    hiredAt = LocalDate.parse(firstMember.hiredAt).atStartOfDay()
                )
            )
        }
        coVerify(exactly = 1) {
            beneficiaryService.update(
                beneficiary.copy(
                    hiredAt = LocalDate.parse(secondMember.hiredAt).atStartOfDay()
                )
            )
        }
    }

    @Test
    fun `#populateCanceledReasonForBeneficiary - backfill to populate canceled reason on beneficiaries`() =
        runBlocking {
            val firstMember = BackFillCanceledReasonBeneficiaries(
                memberId = RangeUUID.generate(),
                canceledReason = BeneficiaryCancelationReason.ALICE_DECISION
            )
            val secondMember = BackFillCanceledReasonBeneficiaries(
                memberId = RangeUUID.generate(),
                canceledReason = BeneficiaryCancelationReason.RETIREMENT
            )

            val request = BackFillCanceledReasonBeneficiaryRequest(
                beneficiaries = listOf(firstMember, secondMember)
            )

            val beneficiary = TestModelFactory.buildBeneficiary()

            coEvery { beneficiaryService.findByMemberId(any(), any()) } returns beneficiary.success()
            coEvery { beneficiaryService.update(any()) } returns beneficiary.success()

            internalAuthentication {
                post("/backfill/beneficiary_churn", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerify(exactly = 1) { beneficiaryService.findByMemberId(firstMember.memberId) }
            coVerify(exactly = 1) { beneficiaryService.findByMemberId(secondMember.memberId) }
            coVerify(exactly = 1) { beneficiaryService.update(beneficiary.copy(canceledReason = BeneficiaryCancelationReason.ALICE_DECISION)) }
            coVerify(exactly = 1) { beneficiaryService.update(beneficiary.copy(canceledReason = BeneficiaryCancelationReason.RETIREMENT)) }
        }

    @Test
    fun `#beneficiaryCreatedBatch - should produce BeneficiaryCreated event for all entities in list`() = runBlocking {
        val beneficiary1 = TestModelFactory.buildBeneficiary()
        val beneficiary2 = TestModelFactory.buildBeneficiary()

        val beneficiary1Model = BackFillBeneficiaryCreatedWithPhase(
            beneficiaryId = beneficiary1.id,
            initialProductId = RangeUUID.generate(),
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            currentPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS
        )

        val beneficiary2Model = BackFillBeneficiaryCreatedWithPhase(
            beneficiaryId = beneficiary2.id,
            initialProductId = RangeUUID.generate(),
            flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        )

        coEvery {
            beneficiaryService.findByIds(listOf(beneficiary1.id, beneficiary2.id))
        } returns listOf(beneficiary1, beneficiary2).success()
        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()


        val request = BackFillBeneficiaryCreatedBatch(
            beneficiaries = listOf(beneficiary1Model, beneficiary2Model)
        )

        internalAuthentication {
            post("/backfill/beneficiary_created_batch", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: BeneficiaryCreatedEvent ->
                it.payload.beneficiary.id == beneficiary1.id &&
                        it.payload.flowType == beneficiary1Model.flowType &&
                        it.payload.initialProductId == beneficiary1Model.initialProductId
            }, beneficiary1.id.toString())
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: BeneficiaryCreatedEvent ->
                it.payload.beneficiary.id == beneficiary2.id &&
                        it.payload.flowType == beneficiary2Model.flowType &&
                        it.payload.initialProductId == beneficiary2Model.initialProductId
            }, beneficiary2.id.toString())
        }
    }

    @Test
    fun `#createMemberContractToMemberWithoutContract - backFill for member without contract member`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val beneficiary = TestModelFactory.buildBeneficiary()
        val request = BackFillCreateMemberContractToMemberWithoutContract(
            memberId = member.id,
        )
        val memberContractId = RangeUUID.generate()
        val memberContractTerm =
            TestModelFactory.buildMemberContractTerm(memberId = member.id, memberContractId = memberContractId)
        val memberContract = TestModelFactory.buildMemberContract(terms = listOf(memberContractTerm))

        coEvery { memberService.get(request.memberId) } returns member
        coEvery { beneficiaryService.findByMemberId(request.memberId) } returns beneficiary
        coEvery { contractGenerator.generateContractB2B(member, beneficiary) } returns memberContract

        internalAuthentication {
            post("/backfill/member_without_member_contract", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { memberService.get(request.memberId) }
        coVerifyOnce { memberService.get(request.memberId) }
        coVerifyOnce { contractGenerator.generateContractB2B(member, beneficiary) }
    }

    @Test
    fun `#createCassiMemberFromExistentOne - backFill for cassi member creating from an existent one`() = runBlocking {
        val personId = PersonId()
        val newProduct = TestModelFactory.buildProduct(hasNationalCoverage = true)
        val cassiMember = TestModelFactory.buildCassiMember()
        val oldMember = TestModelFactory.buildMember(personId = personId, cassiMember = cassiMember)
        val newMember = TestModelFactory.buildMember(personId = personId, product = newProduct)

        val request = BackfillCassiMemberRequest(
            ids = listOf(oldMember.id),
        )

        coEvery { memberService.findByIds(listOf(oldMember.id)) } returns listOf(oldMember)
        coEvery { memberService.findByPersonIds(listOf(oldMember.personId)) } returns listOf(newMember)
        coEvery { productService.findByIds(listOf(newProduct.id)) } returns listOf(newProduct)
        coEvery { cassiMemberService.copyOrCreateCassiMember(oldMember.id, newMember.id, newProduct) } returns
                cassiMember.copy(memberId = newMember.id)

        internalAuthentication {
            post("/backfill/create_cassi_member_from_existent_one", request) { response ->
                assertThat(response).isOKWithData(listOf(cassiMember.copy(memberId = newMember.id)))
            }
        }

        coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(oldMember.id, newMember.id, newProduct) }
    }

    @Test
    fun `#createBeneficiaryHubspotDealAndContact - backFill to create hubspot deal and contact for beneficiary`() =
        runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val beneficiaryOnboardingId = RangeUUID.generate()
            val newPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
            val beneficiaryHubspot = TestModelFactory.buildBeneficiaryHubspot(beneficiaryId = beneficiaryId)
            val beneficiaryOnboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
                beneficiaryOnboardingId = beneficiaryOnboardingId,
                phase = newPhase,
            )
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(
                id = beneficiaryOnboardingId,
                beneficiaryId = beneficiaryId,
                phases = listOf(beneficiaryOnboardingPhase),
            )
            val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryId, onboarding = beneficiaryOnboarding)

            coEvery {
                beneficiaryHubspotService.create(
                    beneficiary,
                    beneficiaryOnboarding.initialProductId,
                    beneficiaryOnboarding.flowType
                )
            } returns beneficiaryHubspot
            coEvery { beneficiaryService.get(any(), any()) } returns beneficiary

            val request = BackfillBeneficiaryHubspotDealAndContact(
                beneficiaryIds = listOf(beneficiary.id)
            )

            internalAuthentication {
                post("/backfill/create_beneficiary_hubspot_deal_and_contact", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce { beneficiaryHubspotService.create(beneficiary, any(), any()) }
        }


    @Test
    fun `#executeUpdateCassiExpirationDateRoutine - backFill to execute case member expiration date routine successful`() =
        runBlocking {

            coEvery {
                beneficiaryService.updateCassiMembershipByExpirationDate(any(), any())
            } returns true.success()

            internalAuthentication {
                post(
                    "/backfill/update_cassi_older_expiration_date", body = DefaultPaginatedRequest(
                        limit = 10,
                        offset = 0
                    )
                ) { response ->
                    assertThat(response).isOK()
                    assertThat(response).isOKWithData(true)
                }
            }

            coVerifyOnce { beneficiaryService.updateCassiMembershipByExpirationDate(limit = 10, offset = 0) }

        }

    @Test
    fun `#createCassiMemberFromMemberWithoutOne - should create cassi member`() = runBlocking {
        val personId = PersonId()
        val product = TestModelFactory.buildProduct(hasNationalCoverage = true)
        val member = TestModelFactory.buildMember(personId = personId, product = product)
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
        val request = BackfillCassiMemberRequest(listOf(member.id))

        coEvery { memberService.findByIds(listOf(member.id)) } returns listOf(member)
        coEvery { cassiMemberService.createCassiMember(member) } returns cassiMember

        internalAuthentication {
            post("/backfill/create_cassi_member_from_member_without_one", request) { response ->
                assertThat(response).isOKWithData(listOf(cassiMember))
            }
        }

        coVerifyOnce { memberService.findByIds(listOf(member.id)) }
        coVerifyOnce { cassiMemberService.createCassiMember(member) }
    }

    @Test
    fun `#cancelCancellationRequest - should fill with null all canceled fields`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary(
            canceledAt = LocalDateTime.now(),
            canceledDescription = "teste",
            canceledReason = BeneficiaryCancelationReason.RESIGNATION_REQUEST
        )
        val updated = beneficiary.copy(canceledAt = null, canceledReason = null, canceledDescription = null)
        val request = CancelCancellationRequestRequest(beneficiaryId = beneficiary.id)

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryService.update(updated) } returns updated

        internalAuthentication {
            post("/backfill/cancel_cancellation_request", request) { response ->
                assertThat(response).isOKWithData(updated)
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id) }
        coVerifyOnce { beneficiaryService.update(updated) }
    }

    @Test
    fun `#updateCompanyBillingAccountableParty - should update billingAccountablePartyId of companies`() = runBlocking {
        val billingAccountableParty1 = TestModelFactory.buildBillingAccountableParty(nationalId = "*********")
        val billingAccountableParty2 = TestModelFactory.buildBillingAccountableParty(nationalId = "**********")
        val company1 = TestModelFactory.buildCompany(cnpj = billingAccountableParty1.nationalId)
        val company2 = TestModelFactory.buildCompany(cnpj = billingAccountableParty2.nationalId)
        val cnpjs = listOf(company1.cnpj, company2.cnpj)
        val updatedCompany1 = company1.copy(billingAccountablePartyId = billingAccountableParty1.id)
        val updatedCompany2 = company2.copy(billingAccountablePartyId = billingAccountableParty2.id)

        val request = UpdateCompanyBillingAccountablePartyRequest(
            companyCnpjs = cnpjs,
        )

        coEvery { companyService.findByCnpjs(cnpjs) } returns listOf(company1, company2)
        coEvery { billingAccountablePartyService.findByNationalIds(cnpjs) } returns listOf(
            billingAccountableParty1,
            billingAccountableParty2
        )
        coEvery { companyService.update(any()) } returns updatedCompany1
        coEvery { companyService.update(any()) } returns updatedCompany2

        internalAuthentication {
            post("/backfill/update_company_billing_accountable_party", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { companyService.findByCnpjs(cnpjs) }
        coVerifyOnce { billingAccountablePartyService.findByNationalIds(cnpjs) }
        coVerifyOnce { companyService.update(updatedCompany1) }
        coVerifyOnce { companyService.update(updatedCompany2) }
    }

    @Test
    fun `#force_member_activation - should activate member of company`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val member = TestModelFactory.buildMember()
        val activatedMember = member.copy(status = MemberStatus.ACTIVE)

        val request = ForceMemberActivationRequest(beneficiary.id)

        coEvery { beneficiaryService.get(any()) } returns beneficiary
        coEvery { memberService.get(any()) } returns member
        coEvery { memberService.activateMember(any()) } returns activatedMember

        internalAuthentication {
            post("/backfill/force_member_activation", request) { response ->
                assertThat(response).isOKWithData(activatedMember)
            }
        }

        coVerifyOnce { beneficiaryService.get(request.beneficiaryId) }
        coVerifyOnce { memberService.get(beneficiary.memberId) }
        coVerifyOnce { memberService.activateMember(member) }
    }

    @Test
    fun `#request_update_beneficiary_dependent - should request update by national id`() = runBlocking {
        val request = RequestUpdateDependent(ids = listOf("0001"))

        coEvery { kafkaProducerService.produce(any(), any()) } returns mockk()

        internalAuthentication {
            post("/backfill/request_update_beneficiary_dependent", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) {
            kafkaProducerService.produce(match { it: RequestUpdateDependentEmployeeBind ->
                it.payload.nationalId == "0001"
            })
        }
    }

    @Test
    fun `#updateBeneficiaryParentAndStatusFields - should update member and parent fields`() = runBlocking {
        val request = RequestBeneficiaryParentAndStatusUpdate(1, 5, null)
        val members = listOf(
            TestModelFactory.buildMember(status = MemberStatus.ACTIVE),
            TestModelFactory.buildMember(status = MemberStatus.PENDING),
            TestModelFactory.buildMember(status = MemberStatus.CANCELED),
        )
        val parents = listOf(
            TestModelFactory.buildBeneficiary(),
            TestModelFactory.buildBeneficiary(),
        )
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(memberId = members[0].id, parentBeneficiary = parents[0].id),
            TestModelFactory.buildBeneficiary(memberId = members[1].id, parentBeneficiary = parents[1].id),
            TestModelFactory.buildBeneficiary(memberId = members[2].id),
        )

        val expectedBeneficiaries = listOf(
            beneficiaries[0].copy(parentPerson = parents[0].personId, memberStatus = members[0].status),
            beneficiaries[1].copy(parentPerson = parents[1].personId, memberStatus = members[1].status),
            beneficiaries[2].copy(parentPerson = null, memberStatus = members[2].status),
        )

        coEvery { beneficiaryService.findByFilters(any(), any(), any(), any(), any()) } returns beneficiaries
        coEvery { memberService.findByIds(any()) } returns members
        coEvery { beneficiaryService.findByIds(any()) } returns parents
        coEvery { beneficiaryService.updateInBatch(any()) } returns expectedBeneficiaries

        internalAuthentication {
            post("/backfill/update_parent_and_status_field", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 1) {
            beneficiaryService.findByFilters(null, null, null, null, IntRange(1, 5))
        }
        coVerify(exactly = 1) { memberService.findByIds(members.map { it.id }) }
        coVerify(exactly = 1) { beneficiaryService.findByIds(parents.map { it.id }) }
        coVerify(exactly = 1) { beneficiaryService.updateInBatch(expectedBeneficiaries) }
    }

    @Test
    fun `#resetBeneficiaryRiskFlow - should update risk flow in member onboarding`() = runBlocking {
        val beneficiaryId = UUID.randomUUID()
        val request = BackFillBeneficiaryRiskFlowRequest(beneficiaryId)

        val expectedOnboard = TestModelFactory.buildBeneficiaryOnboarding(
            beneficiaryId = beneficiaryId,
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED
        )

        coEvery {
            beneficiaryOnboardingService.resetBeneficiaryOnboardingWithFlowType(
                any(),
                any()
            )
        } returns expectedOnboard

        internalAuthentication {
            post("/backfill/reset_beneficiary_risk_flow", request) { response ->
                assertThat(response).isOK()
                assertThat(response).isOKWithData(expectedOnboard)
            }
        }

        coVerifyOnce {
            beneficiaryOnboardingService.resetBeneficiaryOnboardingWithFlowType(
                beneficiaryId,
                BeneficiaryOnboardingFlowType.UNDEFINED
            )
        }
    }

    @Test
    fun `#moveBeneficiaryCompany - should move beneficiaries from one company to another one`() = runBlocking {
        val fromCompany = TestModelFactory.buildCompany(cnpj = "1")
        val toCompany = TestModelFactory.buildCompany(cnpj = "2")
        val beneficiary1 = TestModelFactory.buildBeneficiary(companyId = fromCompany.id)
        val beneficiary2 = TestModelFactory.buildBeneficiary(companyId = fromCompany.id)

        val beneficiaryUpdated1 = TestModelFactory.buildBeneficiary(companyId = toCompany.id)
        val beneficiaryUpdated2 = TestModelFactory.buildBeneficiary(companyId = toCompany.id)

        val request = MoveBeneficiaryCompanyRequest(from = fromCompany.cnpj, to = toCompany.cnpj)

        coEvery { companyService.findByCnpjs(listOf(fromCompany.cnpj, toCompany.cnpj)) } returns listOf(
            fromCompany,
            toCompany
        )
        coEvery { beneficiaryService.findByCompanyId(fromCompany.id) } returns listOf(beneficiary1, beneficiary2)
        coEvery { beneficiaryService.update(match { it.id == beneficiary1.id && it.companyId == toCompany.id }) } returns beneficiaryUpdated1
        coEvery { beneficiaryService.update(match { it.id == beneficiary2.id && it.companyId == toCompany.id }) } returns beneficiaryUpdated2

        internalAuthentication {
            post("/backfill/move_beneficiary_company", request) { response ->
                assertThat(response).isOK()
                assertThat(response).isOKWithData(listOf(beneficiaryUpdated1, beneficiaryUpdated2))
            }
        }

        coVerifyOnce {
            companyService.findByCnpjs(any())
            beneficiaryService.findByCompanyId(any())
        }
        coVerify(exactly = 2) { beneficiaryService.update(any()) }
    }

    @Test
    fun `#moveBeneficiaryCompany - should not move beneficiaries when some company does not exist`() = runBlocking {
        val fromCompany = TestModelFactory.buildCompany(cnpj = "1")
        val toCompany = TestModelFactory.buildCompany(cnpj = "2")


        val request = MoveBeneficiaryCompanyRequest(from = fromCompany.cnpj, to = toCompany.cnpj)

        coEvery { companyService.findByCnpjs(listOf(fromCompany.cnpj, toCompany.cnpj)) } returns listOf(
            fromCompany
        )

        internalAuthentication {
            post("/backfill/move_beneficiary_company", request) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { companyService.findByCnpjs(listOf(fromCompany.cnpj, toCompany.cnpj)) }
        coVerifyNone {
            beneficiaryService.findByCompanyId(any())
            beneficiaryService.update(any())
        }
    }

    @Test
    fun `#backfillBeneficiariesParentMember - `() = runBlocking {
        val parentMembers =
            listOf(TestModelFactory.buildMember(), TestModelFactory.buildMember(), TestModelFactory.buildMember())
        val parentBeneficiaries = listOf(
            TestModelFactory.buildBeneficiary(memberId = parentMembers[0].id),
            TestModelFactory.buildBeneficiary(memberId = parentMembers[1].id),
            TestModelFactory.buildBeneficiary(memberId = parentMembers[2].id)
        )

        val childMembers =
            listOf(
                TestModelFactory.buildMember(
                    parentMember = parentMembers[0].id,
                ),
                TestModelFactory.buildMember(parentMember = parentMembers[1].id),
                TestModelFactory.buildMember(parentMember = parentMembers[2].id)
            )
        val childBeneficiaries = listOf(
            TestModelFactory.buildBeneficiary(
                memberId = childMembers[0].id,
                parentBeneficiary = parentBeneficiaries[0].id
            ),
            TestModelFactory.buildBeneficiary(
                memberId = childMembers[1].id,
                parentBeneficiary = parentBeneficiaries[1].id
            ),
            TestModelFactory.buildBeneficiary(
                memberId = childMembers[2].id,
                parentBeneficiary = parentBeneficiaries[2].id
            )
        )

        val childMembersUpdated = listOf(
            childMembers[0].copy(parentMember = parentMembers[0].id, parentPerson = parentMembers[0].personId),
            childMembers[1].copy(parentMember = parentMembers[1].id, parentPerson = parentMembers[1].personId),
            childMembers[2].copy(parentMember = parentMembers[2].id, parentPerson = parentMembers[2].personId),
        )

        coEvery {
            beneficiaryService.findByFilters(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns childBeneficiaries
        coEvery { beneficiaryService.findByIds(any()) } returns parentBeneficiaries
        coEvery { memberService.findByIds(childMembers.map { it.id }) } returns childMembers
        coEvery { memberService.findByIds(parentMembers.map { it.id }) } returns parentMembers
        coEvery { memberService.update(childMembersUpdated[0]) } returns childMembersUpdated[0]
        coEvery { memberService.update(childMembersUpdated[1]) } returns childMembersUpdated[1]
        coEvery { memberService.update(childMembersUpdated[2]) } returns childMembersUpdated[2]

        internalAuthentication {
            post("/backfill/update_parent_member_beneficiary", RangeRequest(0, 10)) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#migrateCompanyProductsToCompanyProductPriceListing - should migrate the products from company to CPPL`() =
        runBlocking {
            val product = TestModelFactory.buildProduct(ansNumber = "121")
            val productId = product.id
            val productIds = listOf(productId)

            val priceListing = TestModelFactory.buildPriceListing()
            val priceListingId = priceListing.id
            val productPricelisting =
                TestModelFactory.buildProductPriceListing(productId = productId, priceListingId = priceListingId)

            val company = TestModelFactory.buildCompany(availableProducts = productIds)
            val companyId = company.id
            val companyIds = listOf(companyId)
            val subcontract = TestModelFactory.buildCompanySubContract(companyId = companyId)
            val subcontractId = subcontract.id

            val cppl = TestModelFactory.buildCompanyProductPriceListing(
                productId = productId,
                companyId = companyId,
                companySubContractId = subcontractId,
            )

            val request = MigrateCompanyProductsToCompanyProductPriceListRequest(companyIds)

            coEvery { companyService.findByIds(companyIds) } returns listOf(company)
            coEvery { productPriceListingService.getCurrents(productIds) } returns listOf(productPricelisting)
            coEvery {
                productService.findByIds(
                    productIds,
                    ProductService.FindOptions(withPriceListing = false)
                )
            } returns listOf(product)
            coEvery { companySubContractService.findByCompanyIds(companyIds) } returns listOf(subcontract)
            coEvery { priceListingService.getList(listOf(priceListingId)) } returns listOf(priceListing)
            coEvery {
                companyProductPriceListingService.addListToSubContract(
                    match {
                        it[0].productId == productId &&
                                it[0].companyId == companyId &&
                                it[0].companySubContractId == subcontractId
                    },
                    subcontract,
                    request.sendEvent
                )
            } returns listOf(cppl)


            internalAuthentication {
                post("/backfill/migrate_company_products_to_cppl", request) { response ->
                    assertThat(response).isOK()

                    println(response.bodyAsText())
                }
            }
        }

    @Test
    fun `#deleteContract - shoud delete company contract and update associate ids to null`() = runBlocking {

        val contractId = UUID.randomUUID()

        val contract = TestModelFactory.buildCompanyContract(id = contractId)
        val subContract = TestModelFactory.buildCompanySubContract(contractId = contract.id)
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(companySubContractId = subContract.id),
            TestModelFactory.buildBeneficiary(companySubContractId = subContract.id)
        )
        val beneficiariesWithoutSubContract = beneficiaries.map { it.copy(companySubContractId = null) }
        val company = TestModelFactory.buildCompany()

        coEvery { companyContractService.get(contractId) } returns contract
        coEvery { companySubContractService.findByContractId(contract.id) } returns listOf(subContract)
        coEvery { beneficiaryService.findByCompanySubContractId(any()) } returns beneficiaries
        coEvery { beneficiaryService.updateInBatch(beneficiariesWithoutSubContract) } returns beneficiariesWithoutSubContract
        coEvery { companySubContractService.delete(subContract) } returns true.success()
        coEvery { companyService.findByContractId(contract.id) } returns listOf(company)
        coEvery { companyService.update(match { it.contractIds.isEmpty() }) } returns company
        coEvery { companyContractService.delete(contract) } returns true.success()


        internalAuthentication {
            post("/backfill/contract/delete_contract", DeleteContractRequest(contractId)) { response ->
                assertThat(response).isOK()
            }
        }
        coVerifyOnce { companyContractService.get(contractId) }
        coVerifyOnce { companySubContractService.findByContractId(contract.id) }
        coVerifyOnce { beneficiaryService.findByCompanySubContractId(any()) }
        coVerifyOnce { beneficiaryService.updateInBatch(any()) }
        coVerifyOnce { companySubContractService.delete(subContract) }
        coVerifyOnce { companyService.update(any()) }
        coVerifyOnce { companyContractService.delete(contract) }

    }

    @Test
    fun `#deleteSubcontract - should delete company subcontract and update associate ids to null`() = runBlocking {

        val subcontractId = UUID.randomUUID()

        val subcontract = TestModelFactory.buildCompanySubContract(id = subcontractId)
        val beneficiaries = listOf(
            TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id),
            TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id)
        )
        val beneficiariesWithoutSubContract = beneficiaries.map { it.copy(companySubContractId = null) }

        coEvery { companySubContractService.get(subcontract.id) } returns subcontract
        coEvery { beneficiaryService.findByCompanySubContractId(any()) } returns beneficiaries
        coEvery { beneficiaryService.updateInBatch(beneficiariesWithoutSubContract) } returns beneficiariesWithoutSubContract
        coEvery { companySubContractService.delete(subcontract) } returns true.success()


        internalAuthentication {
            post("/backfill/subcontract/delete", DeleteSubcontractRequest(subcontractId)) { response ->
                assertThat(response).isOK()
            }
        }
        coVerifyOnce { companySubContractService.get(subcontract.id) }
        coVerifyOnce { beneficiaryService.findByCompanySubContractId(any()) }
        coVerifyOnce { beneficiaryService.updateInBatch(any()) }
        coVerifyOnce { companySubContractService.delete(subcontract) }
    }

    @Test
    fun `#deleteSubcontract - should delete company subcontract and update associate ids to optional subcontract id`() =
        runBlocking {

            val subcontractId = UUID.randomUUID()
            val optionalSubcontractId = UUID.randomUUID()

            val subcontract = TestModelFactory.buildCompanySubContract(id = subcontractId)
            val beneficiaries = listOf(
                TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id),
                TestModelFactory.buildBeneficiary(companySubContractId = subcontract.id)
            )
            val beneficiariesWithSubContract =
                beneficiaries.map { it.copy(companySubContractId = optionalSubcontractId) }

            coEvery { companySubContractService.get(subcontract.id) } returns subcontract
            coEvery { beneficiaryService.findByCompanySubContractId(any()) } returns beneficiaries
            coEvery { beneficiaryService.updateInBatch(beneficiariesWithSubContract) } returns beneficiariesWithSubContract
            coEvery { companySubContractService.delete(subcontract) } returns true.success()


            internalAuthentication {
                post(
                    "/backfill/subcontract/delete",
                    DeleteSubcontractRequest(subcontractId, optionalSubcontractId)
                ) { response ->
                    assertThat(response).isOK()
                }
            }
            coVerifyOnce { companySubContractService.get(subcontract.id) }
            coVerifyOnce { beneficiaryService.findByCompanySubContractId(any()) }
            coVerifyOnce { beneficiaryService.updateInBatch(any()) }
            coVerifyOnce { companySubContractService.delete(subcontract) }

        }

    @Test
    fun `#deleteB2bDataFromB2cMember - should delete beneficiaryOnboardPhase and beneficiaryOnboard successfully`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val members = listOf(
                TestModelFactory.buildMember(
                    selectedProduct = MemberProduct(product.id, product.prices, type = ProductType.B2C)
                ),
                TestModelFactory.buildMember(
                    selectedProduct = MemberProduct(
                        product.id,
                        product.prices,
                        type = ProductType.B2B
                    )
                )
            )
            val membersIds = members.map { it.id }

            val beneficiary = TestModelFactory.buildBeneficiary(memberId = members[0].id)
            val beneficiaryOnboarding = TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiary.id)
            val phases = listOf(
                TestModelFactory.buildBeneficiaryOnboardingPhase(
                    phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                    beneficiaryOnboardingId = beneficiaryOnboarding.id
                ),
                TestModelFactory.buildBeneficiaryOnboardingPhase(
                    phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
                    beneficiaryOnboardingId = beneficiaryOnboarding.id
                )
            )

            coEvery { memberService.findByIds(any()) } returns members.success()
            coEvery { beneficiaryService.findByMemberId(any()) } returns beneficiary
            coEvery { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) } returns beneficiaryOnboarding
            coEvery { beneficiaryOnboardingService.findAllPhasesFromBeneficiaryOnboardingId(beneficiaryOnboarding.id) } returns phases
            coEvery { beneficiaryOnboardingService.deleteBeneficiaryOnboardingPhase(any()) } returns true
            coEvery { beneficiaryOnboardingService.deleteBeneficiaryOnboarding(any()) } returns true

            internalAuthentication {
                post(
                    "/backfill/member/delete_b2c_data",
                    DeleteB2bDataFromB2cMemberRequest(membersIds)
                ) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce { memberService.findByIds(any()) }
            coVerifyOnce { beneficiaryService.findByMemberId(any()) }
            coVerifyOnce { beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id) }
            coVerifyOnce { beneficiaryOnboardingService.findAllPhasesFromBeneficiaryOnboardingId(beneficiaryOnboarding.id) }
            coVerify(exactly = 2) { beneficiaryOnboardingService.deleteBeneficiaryOnboardingPhase(any()) }
            coVerifyOnce { beneficiaryOnboardingService.deleteBeneficiaryOnboarding(any()) }

        }

    @Test
    fun `#backfillGracePeriodType - should update gracePeriodType for beneficiaries with null value`() = runBlocking {
        val request = GracePeriodRequest()

        val member1 = TestModelFactory.buildMember()
        val member2 = TestModelFactory.buildMember(status = MemberStatus.ACTIVE)
        val member3 = TestModelFactory.buildMember()

        val contract1 = TestModelFactory.buildCompanyContract(groupCompany = "0004")
        val contract2 = TestModelFactory.buildCompanyContract(groupCompany = "0001")

        val company1 = TestModelFactory.buildCompany(
            defaultFlowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
            contractIds = listOf(contract1.id),
        )
        val company2 = TestModelFactory.buildCompany(
            defaultFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
            contractIds = listOf(contract2.id),
        )
        val company3 = TestModelFactory.buildCompany(
            defaultFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
            contractIds = listOf(contract2.id),
        )

        val subcontract1 = TestModelFactory.buildCompanySubContract(contractId = contract1.id, companyId = company1.id)
        val subcontract2 = TestModelFactory.buildCompanySubContract(contractId = contract2.id, companyId = company2.id)

        val beneficiaryId1 = RangeUUID.generate()
        val beneficiaryId2 = RangeUUID.generate()
        val beneficiaryId3 = RangeUUID.generate()

        val beneficiaryOnboarding1 =
            TestModelFactory.buildBeneficiaryOnboarding(
                beneficiaryId = beneficiaryId1,
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
            )
        val beneficiaryOnboarding2 =
            TestModelFactory.buildBeneficiaryOnboarding(
                beneficiaryId = beneficiaryId2,
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
            )
        val beneficiaryOnboarding3 =
            TestModelFactory.buildBeneficiaryOnboarding(
                beneficiaryId = beneficiaryId3,
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW
            )


        val beneficiary1 = TestModelFactory.buildBeneficiary(
            id = beneficiaryId1,
            onboarding = beneficiaryOnboarding1,
            gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
            companyId = company1.id,
            companySubContractId = subcontract1.id,
            memberId = member1.id,
        ).toModel()
        val beneficiary2 = TestModelFactory.buildBeneficiary(
            id = beneficiaryId2,
            onboarding = beneficiaryOnboarding2,
            gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
            companyId = company2.id,
            companySubContractId = subcontract2.id,
            memberId = member2.id,
        ).toModel()
        val beneficiary3 = TestModelFactory.buildBeneficiary(
            id = beneficiaryId3,
            onboarding = beneficiaryOnboarding3,
            gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
            companyId = company3.id,
            companySubContractId = subcontract2.id,
            memberId = member3.id,
        ).toModel()

        val beneficiary1ToUpdate = beneficiary1.copy(
            gracePeriodType = GracePeriodType.TOTAL_EXEMPTION,
            gracePeriodTypeReason = GracePeriodTypeReason.MLA,
        )
        val beneficiary2ToUpdate = beneficiary2.copy(
            gracePeriodType = GracePeriodType.TOTAL_EXEMPTION,
            gracePeriodTypeReason = GracePeriodTypeReason.PORTABILITY,
        )
        val beneficiary3ToUpdate = beneficiary3.copy(
            gracePeriodType = GracePeriodType.TOTAL_EXEMPTION,
            gracePeriodTypeReason = GracePeriodTypeReason.PORTABILITY,
        )

        val expected = mapOf(
            "successCount" to 2.0,
            "errorsCount" to 1.0,
            "errors" to mapOf(beneficiary3.id.toString() to "Error Test")
        )

        coEvery {
            beneficiaryOnboardingDataService.find(
                queryEq {
                    where { this.flowType.eq(BeneficiaryOnboardingFlowType.NO_RISK_FLOW) }
                        .offset { request.page * request.perPage }
                        .limit { request.perPage }
                }
            )
        } returns listOf(beneficiary1.onboarding!!, beneficiary2.onboarding!!, beneficiary3.onboarding!!)

        coEvery { kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member2, UPDATE)) } returns mockk()

        coEvery {
            beneficiaryDataService.find(
                queryEq {
                    where {
                        this.id.inList(
                            listOf(
                                beneficiaryId1,
                                beneficiaryId2,
                                beneficiaryId3
                            )
                        ) and this.gracePeriodType.eq(
                            GracePeriodType.TOTAL_GRACE_PERIOD
                        )
                    }
                }
            )
        } returns listOf(beneficiary1, beneficiary2, beneficiary3)

        coEvery {
            memberService.findByIds(listOf(member1.id, member2.id, member3.id))
        } returns listOf(member1, member2, member3)

        coEvery {
            companyService.findByIds(
                listOf(
                    beneficiary1.companyId,
                    beneficiary2.companyId,
                    beneficiary3.companyId,
                )
            )
        } returns listOf(company1, company2, company3)

        coEvery {
            companySubContractService.findByIds(
                listOf(
                    subcontract1.id,
                    subcontract2.id,
                )
            )
        } returns listOf(subcontract1, subcontract2)

        coEvery {
            companyContractService.findByIds(listOf(contract1.id, contract2.id))
        } returns listOf(contract1, contract2)

        coEvery { beneficiaryService.update(beneficiary1ToUpdate.toTransport()) } returns
                beneficiary1ToUpdate.toTransport()
        coEvery { beneficiaryService.update(beneficiary2ToUpdate.toTransport()) } returns
                beneficiary2ToUpdate.toTransport()
        coEvery { beneficiaryService.update(beneficiary3ToUpdate.toTransport()) } returns Exception("Error Test")

        internalAuthentication {
            post(
                "/backfill/grace_period_type",
                request
            ) { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce {
            beneficiaryDataService.find(any())
            beneficiaryOnboardingDataService.find(any())
            companyService.findByIds(any())
            kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member2, UPDATE))
        }
        coVerify(exactly = 3) { beneficiaryService.update(any()) }
    }

    @Test
    fun `#updateGracePeriodTypeAndSync - should update gracePeriodType for beneficiaries and send sync event`() =
        runBlocking {

            val beneficiary1 = TestModelFactory.buildBeneficiary()
            val beneficiary2 = TestModelFactory.buildBeneficiary()
            val beneficiary3 = TestModelFactory.buildBeneficiary(
                gracePeriodTypeReason = GracePeriodTypeReason.PARTIAL_PORTABILITY
            )

            val member1 = TestModelFactory.buildMember(id = beneficiary1.memberId)
            val member2 = TestModelFactory.buildMember(id = beneficiary2.memberId)
            val member3 = TestModelFactory.buildMember(id = beneficiary3.memberId)

            val beneficiary1ToUpdate = beneficiary1.copy(
                gracePeriodType = GracePeriodType.TOTAL_EXEMPTION,
                gracePeriodTypeReason = GracePeriodTypeReason.PORTABILITY
            )
            val beneficiary2ToUpdate = beneficiary2.copy(
                gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
                gracePeriodTypeReason = GracePeriodTypeReason.MLA
            )
            val beneficiary3ToUpdate = beneficiary3.copy(
                gracePeriodType = GracePeriodType.PARTIAL_GRACE_PERIOD
            )

            val request = UpdateGracePeriodTypeRequest(
                items = listOf(
                    UpdateGracePeriodTypeItemRequest(
                        beneficiary1.id,
                        GracePeriodType.TOTAL_EXEMPTION,
                        GracePeriodTypeReason.PORTABILITY
                    ),
                    UpdateGracePeriodTypeItemRequest(
                        beneficiary2.id,
                        GracePeriodType.TOTAL_GRACE_PERIOD,
                        GracePeriodTypeReason.MLA
                    ),
                    UpdateGracePeriodTypeItemRequest(beneficiary3.id, GracePeriodType.PARTIAL_GRACE_PERIOD, null)
                )
            )

            val beneficiaryIds = listOf(beneficiary1.id, beneficiary2.id, beneficiary3.id)
            val memberIds = listOf(beneficiary1.memberId, beneficiary2.memberId, beneficiary3.memberId)

            val expected = mapOf(
                "successCount" to 2.0,
                "errorsCount" to 1.0,
                "errors" to mapOf(beneficiary3.id.toString() to "Error Test")
            )

            coEvery { beneficiaryService.findByIds(beneficiaryIds) } returns listOf(
                beneficiary1,
                beneficiary2,
                beneficiary3
            )
            coEvery { memberService.findByIds(memberIds) } returns listOf(member1, member2, member3)

            coEvery { beneficiaryService.update(beneficiary1ToUpdate) } returns beneficiary1ToUpdate
            coEvery { beneficiaryService.update(beneficiary2ToUpdate) } returns beneficiary2ToUpdate
            coEvery { beneficiaryService.update(beneficiary3ToUpdate) } returns Exception("Error Test")

            coEvery { kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member1, UPDATE)) } returns mockk()
            coEvery { kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member2, UPDATE)) } returns mockk()

            internalAuthentication {
                post("/backfill/update_grace_period_type", request) { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerifyOnce { beneficiaryService.findByIds(any()) }
            coVerifyOnce { memberService.findByIds(any()) }
            coVerify(exactly = 3) { beneficiaryService.update(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#updateCpplOnSubcontracts - should update cppl on subcontracts within the range expected`() = runBlocking {
        val request = UpdateCpplOnSubcontractsRequest(1, 2, null)

        val subcontract1 = TestModelFactory.buildCompanySubContract()
        val subcontract2 = TestModelFactory.buildCompanySubContract()

        val cppl1 = TestModelFactory.buildCompanyProductPriceListing(companySubContractId = subcontract1.id)
        val cppl2 = TestModelFactory.buildCompanyProductPriceListing(companySubContractId = subcontract2.id)


        coEvery {
            companySubContractService.findByRange(IntRange(1, 2))
        } returns listOf(subcontract1, subcontract2)

        val expectedSubcontract1 = subcontract1.copy(avaliableCompanyProductPriceListing = listOf(cppl1.id))
        val expectedSubcontract2 = subcontract2.copy(avaliableCompanyProductPriceListing = listOf(cppl2.id))

        coEvery { companyProductPriceListingService.findCurrentBySubContractId(expectedSubcontract1.id) } returns listOf(
            cppl1
        )
        coEvery { companyProductPriceListingService.findCurrentBySubContractId(expectedSubcontract2.id) } returns listOf(
            cppl2
        )

        coEvery { companySubContractService.update(expectedSubcontract1) } returns expectedSubcontract1
        coEvery { companySubContractService.update(expectedSubcontract2) } returns expectedSubcontract2

        internalAuthentication {
            post("/backfill/update_subcontracts_cppl", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { companySubContractService.findByRange(any()) }
        coVerify(exactly = 2) { companySubContractService.update(any()) }
    }

    @Test
    fun `#deleteCompanyProductPriceListingById - should delete cppl with the id passed`() = runBlocking {
        val uuid = UUID.randomUUID()
        val request = DeleteCompanyProductPriceListingByIdRequest(listOf(uuid))

        val cppl = TestModelFactory.buildCompanyProductPriceListing()

        coEvery {
            companyProductPriceListingService.findByIds(listOf(uuid))
        } returns listOf(cppl).success()

        coEvery { companyProductPriceListingService.delete(cppl) } returns true.success()

        internalAuthentication {
            post("/backfill/company_product_price_listing/delete", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { companyProductPriceListingService.delete(any()) }
        coVerifyOnce { companyProductPriceListingService.findByIds(any()) }
    }

    @Test
    fun `#deleteCompanyProductPriceListingByCompanyId - should delete cppl with the id passed`() = runBlocking {
        val uuid = UUID.randomUUID()
        val request = DeleteCompanyProductPriceListingByCompanyIdRequest(listOf(uuid))

        val company = TestModelFactory.buildCompany(uuid)
        val companySubcontract = TestModelFactory.buildCompanySubContract(companyId = uuid)

        val cppls = listOf(
            TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubcontract.id),
            TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubcontract.id)
        )

        val map = mapOf(companySubcontract.id.toString() to cppls)


        coEvery {
            companySubContractService.findByCompanyIds(listOf(company.id))
        } returns listOf(companySubcontract)


        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIds(listOf(companySubcontract.id))
        } returns map.success()


        coEvery { companyProductPriceListingService.delete(any()) } returns true.success()

        internalAuthentication {
            post("/backfill/company_product_price_listing/delete_from_company", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { companyProductPriceListingService.delete(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIds(any()) }
    }

    @Test
    fun `#moveBeneficiaryToPhase - should move the beneficiary to target phase`() = runBlocking {
        val beneficiaryId = UUID.randomUUID()
        val request = MoveBeneficiaryToPhaseRequest(
            beneficiaryIds = listOf(beneficiaryId),
            phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        )

        val onboardingPhase = TestModelFactory.buildBeneficiaryOnboardingPhase()

        val onboarding =
            TestModelFactory.buildBeneficiaryOnboarding(beneficiaryId = beneficiaryId, phases = listOf(onboardingPhase))
        val beneficiary = TestModelFactory.buildBeneficiary(beneficiaryId, onboarding = onboarding)
        val targetOnboardingPhase =
            TestModelFactory.buildBeneficiaryOnboardingPhase(phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION)



        coEvery {
            beneficiaryService.findByIds(listOf(beneficiaryId), findOptions = FindOptions(withOnboarding = true))
        } returns listOf(beneficiary)


        coEvery {
            beneficiaryOnboardingService.moveToPhase(
                beneficiaryId,
                BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION,
                any(),
                1
            )
        } returns targetOnboardingPhase


        coEvery { companyProductPriceListingService.delete(any()) } returns true.success()

        internalAuthentication {
            post("/backfill/beneficiary/move_to_phase", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { beneficiaryService.findByIds(any(), findOptions = FindOptions(withOnboarding = true)) }
        coVerifyOnce {
            beneficiaryOnboardingService.moveToPhase(
                any(),
                any(),
                any(),
                any()
            )
        }
    }

    @Test
    fun `updateCompanySizeInCompany - should update company size and return success response`() = runBlocking {
        val request = CompanySizeBackfillRequest(
            companies = listOf(
                CompanySizeBackfill(UUID.randomUUID(), CompanySize.MLA),
                CompanySizeBackfill(UUID.randomUUID(), CompanySize.MLA)
            )
        )
        val company = TestModelFactory.buildCompany(companySize = null).toModel()

        coEvery { companyDataService.get(any()) } returns company.success()
        coEvery { companyService.update(any()) } returns company.toTransport().success()

        internalAuthentication {
            post("/backfill/update_company_size_in_company", request) { response ->
                assertThat(response).isOK()

                assertThat(response).isOKWithData(
                    mapOf(
                        "successCount" to 2.0,
                        "errorsCount" to 0.0,
                        "errors" to emptyMap<String, String>()
                    )
                )
            }
        }

        coVerify(exactly = 2) { companyDataService.get(any()) }
        coVerify(exactly = 2) { companyService.update(any()) }
    }

    @Test
    fun `cancellationRequestInBatch should works`() = runBlocking {
        val member0 = TestModelFactory.buildMember()
        val beneficiary0 = TestModelFactory.buildBeneficiary(memberId = member0.id)
        val member1 = TestModelFactory.buildMember()
        val beneficiary1 = TestModelFactory.buildBeneficiary(memberId = member1.id)
        val beneficiaryIds = listOf(beneficiary0.id, beneficiary1.id)

        val cancelDate = LocalDateTime.now().minusDays(10).atEndOfTheDay()
        val canceledMember0 = member0.cancel(cancelDate)
        val canceledMember1 = member1.cancel(cancelDate)
        val request = BackFillCancellationRequestInBatch(
            beneficiaryIds,
            cancelDate.toLocalDate(),
            BeneficiaryCancelationReason.ANOTHER
        )

        val canceledBeneficiary0 = beneficiary0.copy(
            canceledAt = request.canceledAt.atEndOfTheDay(),
            canceledReason = request.canceledReason,
            canceledDescription = request.canceledDescription,
            hasContributed = request.hasContributed,
        )

        val canceledBeneficiary1 = beneficiary1.copy(
            canceledAt = request.canceledAt.atEndOfTheDay(),
            canceledReason = request.canceledReason,
            canceledDescription = request.canceledDescription,
            hasContributed = request.hasContributed,
        )

        coEvery { beneficiaryService.get(beneficiary0.id, any()) } returns beneficiary0
        coEvery { memberService.get(member0.id, any()) } returns member0
        coEvery { memberService.update(canceledMember0) } returns canceledMember0
        coEvery { beneficiaryService.update(canceledBeneficiary0) } returns canceledBeneficiary0

        coEvery { beneficiaryService.get(beneficiary1.id, any()) } returns beneficiary1
        coEvery { memberService.get(member1.id, any()) } returns member1
        coEvery { memberService.update(canceledMember1) } returns canceledMember1
        coEvery { beneficiaryService.update(canceledBeneficiary1) } returns canceledBeneficiary1

        coEvery { kafkaProducerService.produce(any()) } returns mockk()


        internalAuthentication {
            post("/backfill/beneficiary_cancellation", request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `cancellationRequestInBatch should works without sending cancellated event`() = runBlocking {
        val member0 = TestModelFactory.buildMember()
        val beneficiary0 = TestModelFactory.buildBeneficiary(memberId = member0.id)
        val member1 = TestModelFactory.buildMember()
        val beneficiary1 = TestModelFactory.buildBeneficiary(memberId = member1.id)
        val beneficiaryIds = listOf(beneficiary0.id, beneficiary1.id)

        val cancelDate = LocalDateTime.now().minusDays(10).atEndOfTheDay()
        val canceledMember0 = member0.cancel(cancelDate)
        val canceledMember1 = member1.cancel(cancelDate)
        val request = BackFillCancellationRequestInBatch(
            beneficiaryIds,
            cancelDate.toLocalDate(),
            BeneficiaryCancelationReason.ANOTHER,
            sendEvent = false,
        )

        val canceledBeneficiary0 = beneficiary0.copy(
            canceledAt = request.canceledAt.atEndOfTheDay(),
            canceledReason = request.canceledReason,
            canceledDescription = request.canceledDescription,
            hasContributed = request.hasContributed,
        )

        val canceledBeneficiary1 = beneficiary1.copy(
            canceledAt = request.canceledAt.atEndOfTheDay(),
            canceledReason = request.canceledReason,
            canceledDescription = request.canceledDescription,
            hasContributed = request.hasContributed,
        )

        coEvery { beneficiaryService.get(beneficiary0.id, any()) } returns beneficiary0
        coEvery { memberService.get(member0.id, any()) } returns member0
        coEvery { memberService.update(canceledMember0, false) } returns canceledMember0
        coEvery { beneficiaryService.update(canceledBeneficiary0) } returns canceledBeneficiary0

        coEvery { beneficiaryService.get(beneficiary1.id, any()) } returns beneficiary1
        coEvery { memberService.get(member1.id, any()) } returns member1
        coEvery { memberService.update(canceledMember1, false) } returns canceledMember1
        coEvery { beneficiaryService.update(canceledBeneficiary1) } returns canceledBeneficiary1

        internalAuthentication {
            post("/backfill/beneficiary_cancellation", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyNone {
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `updateOrCreateCppl should works for upsert`() = runBlocking {
        val subContract = TestModelFactory.buildCompanySubContract()
        val product = TestModelFactory.buildProduct(ansNumber = "123")
        val request = UpdateOrCreateCpplRequest(
            items = listOf(
                UpdateOrCreateCpplItemRequest(
                    subContractId = subContract.id,
                    productId = product.id,
                    range0to18 = BigDecimal(100.1),
                    range19to23 = BigDecimal(100.2),
                    range24to28 = BigDecimal(100.3),
                    range29to33 = BigDecimal(100.4),
                    range34to38 = BigDecimal(100.5),
                    range39to43 = BigDecimal(100.6),
                    range44to48 = BigDecimal(100.7),
                    range49to53 = BigDecimal(100.8),
                    range54to58 = BigDecimal(100.9),
                    range59plus = BigDecimal(101.0),
                )
            )
        )
        val cppl = TestModelFactory.buildCompanyProductPriceListing(
            companyId = subContract.companyId,
            companySubContractId = subContract.id,
            productId = product.id,
            ansNumber = product.ansNumber!!,
            startDate = LocalDate.now(),
            priceListItems = request.items[0].toPriceListItems()
        )

        coEvery { companySubContractService.findByIds(any()) } returns listOf(subContract)
        coEvery { productService.findByIds(any()) } returns listOf(product)
        coEvery {
            companyProductPriceListingService.upsertCompanyProductPriceListing(
                any(),
                any(),
                true,
                true
            )
        } returns cppl

        internalAuthentication {
            post("/backfill/update_or_create_cppl", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { companySubContractService.findByIds(listOf(subContract.id)) }
        coVerifyOnce { productService.findByIds(listOf(product.id)) }
    }

    @Test
    fun `createNewDependents`() = runBlocking {
        val oldBeneficiaryParentId = RangeUUID.generate()
        val parentBeneficiaryPersonId = PersonId()
        val newBeneficiaryParent = TestModelFactory.buildBeneficiary(personId = parentBeneficiaryPersonId)
        val oldBeneficiaryDependent = TestModelFactory.buildBeneficiary(
            parentBeneficiary = oldBeneficiaryParentId,
            parentPerson = parentBeneficiaryPersonId
        )
        val newBeneficiaryDependent = TestModelFactory.buildBeneficiary(parentPerson = parentBeneficiaryPersonId)

        val request = ParentToCreateNewDependentsRequest(listOf(newBeneficiaryParent.id))

        coEvery { beneficiaryService.findByIds(listOf(newBeneficiaryParent.id)) } returns listOf(newBeneficiaryParent)

        coEvery {
            beneficiaryService.findByParentPerson(
                newBeneficiaryParent.personId,
                listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                findOptions = FindOptions(withOnboarding = true)
            )
        } returns listOf(oldBeneficiaryDependent)

        coEvery {
            beneficiaryService.updateDependentWithNewParent(oldBeneficiaryDependent, newBeneficiaryParent)
        } returns newBeneficiaryDependent

        internalAuthentication {
            post("/backfill/beneficiary/create_new_dependents", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            beneficiaryService.findByIds(listOf(newBeneficiaryParent.id))
            beneficiaryService.findByParentPerson(
                newBeneficiaryParent.personId,
                listOf(MemberStatus.ACTIVE, MemberStatus.PENDING),
                findOptions = FindOptions(withOnboarding = true)
            )
            beneficiaryService.updateDependentWithNewParent(oldBeneficiaryDependent, newBeneficiaryParent)
        }
    }

    @Test
    fun `updateMemberParentInfo - should update member parent info and return success response`() = runBlocking {

        val member = TestModelFactory.buildMember()

        val request = UpdateMemberParentInfoRequest(
            memberId = member.id,
            parentMemberId = null,
            parentPersonId = null
        )

        coEvery { memberService.get(any()) } returns member.success()
        coEvery { memberService.update(any()) } returns member.success()

        internalAuthentication {
            post("/backfill/member/update_parent_info", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            memberService.get(any())
            memberService.update(any())
        }
    }

    @Test
    fun `sanitizeCpplAnsNumber - should update member parent info and return success response`() = runBlocking {
        val cppl = TestModelFactory.buildCompanyProductPriceListing(ansNumber = "123-123")
        val cpplUpdated = cppl.copy(ansNumber = "123123")
        val ids = listOf(cppl.id)

        coEvery { companyProductPriceListingService.findByIds(ids) } returns listOf(cppl)
        coEvery {
            companyProductPriceListingService.update(
                cpplUpdated,
                sendEvent = false,
                shouldUpdateTotvsMemberPriceListing = false
            )
        } returns cpplUpdated

        internalAuthentication {
            post("/backfill/sanitize_cppl_ans_number", IdsRequest(ids = ids)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            companyProductPriceListingService.findByIds(any())
            companyProductPriceListingService.update(any(), any(), any())
        }
    }

    @Test
    fun `sanitizeAnsNumber - should update member parent info and return success response`() = runBlocking {
        val product = TestModelFactory.buildProduct(ansNumber = "123-123")
        val productUpdated = product.copy(ansNumber = "123123")
        val ids = listOf(product.id)

        coEvery { productService.findByIds(ids) } returns listOf(product)
        coEvery { productService.update(productUpdated) } returns productUpdated

        internalAuthentication {
            post("/backfill/sanitize_product_ans_number", IdsRequest(ids = ids)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            productService.findByIds(any())
            productService.update(any())
        }
    }

    @Test
    fun `remove_duplicated_current_cppl - should remove all duplicated ansNumber by current cppl`() = runBlocking {
        val subContractId = RangeUUID.generate()
        val ansNumber = "ansnumber"
        val request = BackfillRemovingDuplicatedCurrentCppl(
            items = listOf(
                BackfillRemovingDuplicatedCurrentCpplItem(
                    subContractId,
                    ansNumber
                )
            )
        )

        val cppl1 =
            TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = ansNumber,
                companySubContractId = subContractId,
                startDate = LocalDate.now().minusDays(10), endDate = LocalDate.now().minusDays(1)
            )
        val cppl2 =
            TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = ansNumber,
                companySubContractId = subContractId, startDate = LocalDate.now(), endDate = null
            )
        val cppl3 = TestModelFactory.buildCompanyProductPriceListing(
            ansNumber = ansNumber,
            companySubContractId = subContractId,
            startDate = LocalDate.now().minusDays(10),
            endDate = LocalDate.now().minusDays(1),
        )

        coEvery {
            companyProductPriceListingService.findBySubContractIdAndAnsNumber(
                subContractId,
                ansNumber
            )
        } returns listOf(cppl1, cppl2, cppl3)
        coEvery {
            companyProductPriceListingService.delete(cppl1)
        } returns true
        coEvery {
            companyProductPriceListingService.update(cppl3, false)
        } returns cppl3

        internalAuthentication {
            post("/backfill/remove_duplicated_current_cppl", request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `remove_duplicated_current_cppl - should use fallback`() = runBlocking {
        val subContractId = RangeUUID.generate()
        val ansNumber = "ansnumber"
        val request = BackfillRemovingDuplicatedCurrentCppl(
            items = listOf(
                BackfillRemovingDuplicatedCurrentCpplItem(
                    subContractId,
                    ansNumber
                )
            )
        )

        val cppl1 =
            TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = "prod1",
                companySubContractId = subContractId,
                startDate = LocalDate.now().minusDays(10),
                endDate = LocalDate.now()
            )
        val cppl2 =
            TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = "prod2",
                companySubContractId = subContractId,
                startDate = LocalDate.now().minusDays(30),
                endDate = LocalDate.now().minusDays(11)
            )
        val cppl3 = TestModelFactory.buildCompanyProductPriceListing(
            ansNumber = "prod3",
            companySubContractId = subContractId,
            startDate = LocalDate.now().minusDays(30),
            endDate = null
        )
        val cppl3Updated = cppl3.copy(endDate = cppl1.startDate.minusDays(1))

        coEvery {
            companyProductPriceListingService.findBySubContractIdAndAnsNumber(
                subContractId,
                ansNumber
            )
        } returns listOf(cppl1, cppl2, cppl3)
        coEvery {
            companyProductPriceListingService.delete(cppl2)
        } returns true
        coEvery {
            companyProductPriceListingService.update(cppl3Updated, false)
        } returns cppl3Updated
        coEvery {
            companyProductPriceListingService.update(cppl1.copy(endDate = null), false)
        } returns cppl1

        internalAuthentication {
            post("/backfill/remove_duplicated_current_cppl", request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `updateCompanyContractId - should update company contractId and contractIds`() = runBlocking {
        val existingContractId = RangeUUID.generate()
        val newContractId = RangeUUID.generate()

        val company = TestModelFactory.buildCompany(
            contractIds = listOf(existingContractId)
        )

        val request = UpdateCompanyContractIdRequest(
            companyId = company.id,
            newContractId = newContractId
        )

        val expectedCompany = company.copy(
            contractIds = listOf(existingContractId, request.newContractId)
        )

        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companyService.update(expectedCompany) } returns expectedCompany.success()

        internalAuthentication {
            post("/backfill/company/update_contract_id", request) { response ->
                assertThat(response).isOKWithData(expectedCompany)
            }
        }

        coVerifyOnce {
            companyService.get(any())
            companyService.update(any())
        }

    }

    @Test
    fun `updateCompanyContractId - should not update contractIds when newContractId is already there`() = runBlocking {
        val existingContractId = RangeUUID.generate()
        val newContractId = RangeUUID.generate()

        val company = TestModelFactory.buildCompany(
            contractIds = listOf(existingContractId, newContractId)
        )

        val request = UpdateCompanyContractIdRequest(
            companyId = company.id,
            newContractId = newContractId
        )

        val expectedCompany = company.copy(
            contractIds = listOf(existingContractId, request.newContractId)
        )

        coEvery { companyService.get(any()) } returns company.success()
        coEvery { companyService.update(expectedCompany) } returns expectedCompany.success()

        internalAuthentication {
            post("/backfill/company/update_contract_id", request) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            companyService.get(any())
            companyService.update(expectedCompany)
        }

    }

    @Test
    fun `#updateBlockedAt - should update the blockedAt subcontract field`() = runBlocking {

        val contractId = UUID.randomUUID()

        val groupCompany = "0001"
        val contractNumber = "0001"
        val subcontractNumber = "0001"

        val contract = TestModelFactory.buildCompanyContract(
            id = contractId,
            externalId = contractNumber,
            groupCompany = groupCompany,
        )
        val subcontract =
            TestModelFactory.buildCompanySubContract(contractId = contract.id, externalId = subcontractNumber)

        val updatedSubcontract =
            subcontract.copy(blockedAt = LocalDate.of(2024, 9, 4), status = CompanySubContractStatus.BLOCKED)
        val updatedContract = contract.copy(status = CompanyContractStatus.BLOCKED)

        coEvery { companyContractService.getByExternalIdAndGroupCompany(contractNumber, groupCompany) } returns contract
        coEvery { companySubContractService.findByContractId(contractId) } returns listOf(subcontract)
        coEvery {
            companySubContractService.update(
                match {
                    it.status == updatedSubcontract.status && it.blockedAt == updatedSubcontract.blockedAt && it.id == updatedSubcontract.id
                },
                sendEvent = false,
            )
        } returns updatedSubcontract
        coEvery {
            companyContractService.update(
                match { it.id == updatedContract.id && it.status == updatedContract.status },
                sendEvent = false,
            )
        } returns updatedContract

        internalAuthentication {
            post(
                "/backfill/subcontract/update_blocked_at", UpdateSubcontractBlockedAtRequest(
                    groupCompany = groupCompany,
                    contractNumber = contractNumber,
                    subcontractNumber = subcontractNumber,
                    blockedAt = "20240904"
                )
            ) { response ->
                assertThat(response).isOK()
            }
        }
        coVerifyOnce { companyContractService.getByExternalIdAndGroupCompany(contractNumber, groupCompany) }
        coVerifyOnce { companySubContractService.findByContractId(contract.id) }
        coVerifyOnce {
            companySubContractService.update(match {
                it.status == updatedSubcontract.status && it.blockedAt == updatedSubcontract.blockedAt && it.id == updatedSubcontract.id
            }, sendEvent = false)
        }
        coVerifyOnce {
            companyContractService.update(
                match { it.id == updatedContract.id && it.status == updatedContract.status },
                sendEvent = false
            )
        }

    }

    @Test
    fun `#updateBlockedAt - should update the blockedAt subcontract field but dont update the contract`() =
        runBlocking {
            val contractId = UUID.randomUUID()

            val groupCompany = "0001"
            val contractNumber = "0001"
            val subcontractNumber = "0001"

            val contract = TestModelFactory.buildCompanyContract(
                id = contractId,
                externalId = contractNumber,
                groupCompany = groupCompany,
            )

            val subcontract1 =
                TestModelFactory.buildCompanySubContract(contractId = contract.id, externalId = subcontractNumber)
            val subcontract2 =
                TestModelFactory.buildCompanySubContract(
                    contractId = contract.id,
                ).copy(status = CompanySubContractStatus.ACTIVE)

            val updatedSubcontract =
                subcontract1.copy(blockedAt = LocalDate.of(2024, 9, 4), status = CompanySubContractStatus.BLOCKED)

            coEvery {
                companyContractService.getByExternalIdAndGroupCompany(
                    contractNumber,
                    groupCompany
                )
            } returns contract
            coEvery { companySubContractService.findByContractId(contractId) } returns listOf(
                subcontract1,
                subcontract2
            )
            coEvery {
                companySubContractService.update(
                    match {
                        it.status == updatedSubcontract.status && it.blockedAt == updatedSubcontract.blockedAt && it.id == updatedSubcontract.id
                    },
                    sendEvent = false,
                )
            } returns updatedSubcontract

            internalAuthentication {
                post(
                    "/backfill/subcontract/update_blocked_at", UpdateSubcontractBlockedAtRequest(
                        groupCompany = groupCompany,
                        contractNumber = contractNumber,
                        subcontractNumber = subcontractNumber,
                        blockedAt = "20240904"
                    )
                ) { response ->
                    assertThat(response).isOK()
                }
            }
            coVerifyOnce { companyContractService.getByExternalIdAndGroupCompany(contractNumber, groupCompany) }
            coVerifyOnce { companySubContractService.findByContractId(contract.id) }
            coVerifyOnce {
                companySubContractService.update(match {
                    it.status == updatedSubcontract.status && it.blockedAt == updatedSubcontract.blockedAt && it.id == updatedSubcontract.id
                }, sendEvent = false)
            }
            coVerifyNone {
                companyContractService.update(
                    any(),
                    sendEvent = false
                )
            }
        }

    @Test
    fun `updateBeneficiaryRelationType - should update beneficiary relation type and return success response`() =
        runBlocking {

            val person = TestModelFactory.buildPerson()
            val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id)

            val request = UpdateBeneficiaryRelationTypeRequest(
                beneficiaries = listOf(
                    UpdateBeneficiaryRelationTypeRequest.DependentBeneficiary(
                        nationalId = person.nationalId,
                        relationType = "Spouse"
                    )
                )
            )

            coEvery { personService.findByNationalId(person.nationalId) } returns person.success()

            coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()

            coEvery { beneficiaryService.update(beneficiary.copy(parentBeneficiaryRelationType = ParentBeneficiaryRelationType.SPOUSE)) } returns beneficiary.success()

            internalAuthentication {
                post("/backfill/update_beneficiary_relation_type", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                personService.findByNationalId(any())
                beneficiaryService.findByPersonId(any())
                beneficiaryService.update(any())
            }
        }

    @Test
    fun `updateContractStatus - should update the contract status and return success response`() =
        runBlocking {
            val contract =
                TestModelFactory.buildCompanyContract(externalId = "00001", status = CompanyContractStatus.PROCESSING)

            val contractUpdated = contract.copy(
                status = CompanyContractStatus.ACTIVE,
                integrationStatus = CompanyContractIntegrationStatus.PROCESSED
            )

            coEvery {
                companyContractService.findByIds(listOf(contract.id))
            } returns listOf(contract)

            coEvery {
                companyContractService.update(contractUpdated, sendEvent = false)
            } returns contractUpdated

            val request = UpdateContractStatusRequest(
                contractIds = listOf(contract.id),
                status = CompanyContractStatus.ACTIVE,
                integrationStatus = CompanyContractIntegrationStatus.PROCESSED
            )

            internalAuthentication {
                post("/backfill/contract/update_status", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                companyContractService.findByIds(any())
                companyContractService.update(any(), any())
            }
        }

    @Nested
    inner class UpdateBeneficiary {

        @Test
        fun `should not update the beneficiary when nothing is requested`() = runBlocking {
            val requestWithoutData = UpdateBeneficiariesRequest(data = emptyList())

            internalAuthentication {
                post("/backfill/beneficiary/update", requestWithoutData) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyNone {
                beneficiaryService.findByIds(any())
                beneficiaryService.update(any())
            }
        }

        @Test
        fun `should not update the beneficiary when no beneficiary is found`() = runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val request = UpdateBeneficiariesRequest(
                data = listOf(
                    UpdateBeneficiariesRequest.UpdateBeneficiary(
                        id = beneficiaryId,
                    )
                )
            )

            coEvery { beneficiaryService.findByIds(listOf(beneficiaryId)) } returns emptyList()

            internalAuthentication {
                post("/backfill/beneficiary/update", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                beneficiaryService.findByIds(listOf(beneficiaryId))
            }

            coVerifyNone {
                beneficiaryService.update(any())
            }
        }

        @Test
        fun `should update the beneficiary`() = runBlocking {
            val beneficiaryId = RangeUUID.generate()
            val subcontractId = RangeUUID.generate()

            val beneficiary = TestModelFactory.buildBeneficiary(id = beneficiaryId)
            val beneficiaryUpdated = beneficiary.copy(
                companySubContractId = subcontractId
            )

            val subcontractId2 = RangeUUID.generate()
            val beneficiary2 = TestModelFactory.buildBeneficiary()
            val beneficiary2Updated = beneficiary2.copy(
                companySubContractId = subcontractId2
            )

            val expected = mapOf(
                "successCount" to 1.0,
                "errorsCount" to 1.0,
                "errors" to mapOf(beneficiary2.id.toString() to "Some error")
            )

            val request = UpdateBeneficiariesRequest(
                data = listOf(
                    UpdateBeneficiariesRequest.UpdateBeneficiary(
                        id = beneficiaryId,
                        companySubContractId = subcontractId,
                    ),
                    UpdateBeneficiariesRequest.UpdateBeneficiary(
                        id = beneficiary2.id,
                        companySubContractId = subcontractId2,
                    )
                )
            )

            coEvery { beneficiaryService.findByIds(listOf(beneficiaryId, beneficiary2.id)) } returns listOf(
                beneficiary,
                beneficiary2,
            )

            coEvery { beneficiaryService.update(beneficiaryUpdated) } returns beneficiaryUpdated
            coEvery { beneficiaryService.update(beneficiary2Updated) } returns Exception("Some error")

            internalAuthentication {
                post("/backfill/beneficiary/update", request) { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerifyOnce {
                beneficiaryService.findByIds(listOf(beneficiaryId, beneficiary2.id))
                beneficiaryService.update(beneficiaryUpdated)
                beneficiaryService.update(beneficiary2Updated)
            }
        }
    }

    @Nested
    inner class UpdateCompanyWithSubcontracts {
        @Test
        fun `should not update the company or subcontracts when nothing is requested`() =
            runBlocking {
                val requestWithoutData = UpdateCompanyWithSubcontractsRequest(
                    data = emptyList()
                )

                val requestWithoutUpdateRequested = UpdateCompanyWithSubcontractsRequest(
                    data = listOf(
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123456",
                        )
                    )
                )

                internalAuthentication {
                    post("/backfill/company/update_with_subcontracts", requestWithoutData) { response ->
                        assertThat(response).isOK()
                    }
                }

                internalAuthentication {
                    post("/backfill/company/update_with_subcontracts", requestWithoutUpdateRequested) { response ->
                        assertThat(response).isOK()
                    }
                }

                coVerifyNone {
                    companyService.findByCnpjs(any())
                    companyService.update(any())
                    companySubcontractDataService.find(any())
                    companySubContractService.update(any())
                }
            }

        @Test
        fun `should not update the company or subcontracts when no company is found`() =
            runBlocking {

                val request = UpdateCompanyWithSubcontractsRequest(
                    data = listOf(
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123456",
                            company = UpdateCompanyWithSubcontract.Company(legalName = "Alice"),
                        )
                    )
                )

                coEvery { companyService.findByCnpjs(listOf("123456")) } returns emptyList()

                internalAuthentication {
                    post("/backfill/company/update_with_subcontracts", request) { response ->
                        assertThat(response).isOK()
                    }
                }

                coVerifyOnce { companyService.findByCnpjs(any()) }
                coVerifyNone {
                    companyService.update(any())
                    companySubcontractDataService.find(any())
                    companySubContractService.update(any())
                }
            }

        @Test
        fun `should update the company and subcontracts as expected`() =
            runBlocking {

                val contract = TestModelFactory.buildCompanyContract()
                val contractUpdated = contract.copy(title = "Alice")
                val company = TestModelFactory.buildCompany(cnpj = "123456", contractIds = listOf(contract.id))
                val companyUpdated = company.copy(legalName = "Alice")
                val subcontract = TestModelFactory.buildCompanySubContract(companyId = company.id).toModel()
                val subcontractUpdated = subcontract.copy(title = "Alice")

                val company2 = TestModelFactory.buildCompany(cnpj = "123457")
                val company2Updated = company2.copy(legalName = "Alice")

                val company3 = TestModelFactory.buildCompany(cnpj = "123460")
                val subcontract3 = TestModelFactory.buildCompanySubContract(companyId = company3.id).toModel()
                val subcontract3Updated = subcontract3.copy(title = "Alice")

                val contract4 = TestModelFactory.buildCompanyContract()
                val contract4Updated = contract4.copy(title = "Alice")
                val company4 = TestModelFactory.buildCompany(cnpj = "123461", contractIds = listOf(contract4.id))

                val request = UpdateCompanyWithSubcontractsRequest(
                    data = listOf(
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123456",
                            contract = UpdateCompanyWithSubcontract.Contract(title = "Alice"),
                            company = UpdateCompanyWithSubcontract.Company(legalName = "Alice"),
                            subcontract = UpdateCompanyWithSubcontract.Subcontract(title = "Alice"),
                        ),
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123457",
                            company = UpdateCompanyWithSubcontract.Company(legalName = "Alice"),
                        ),
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123458",
                            company = UpdateCompanyWithSubcontract.Company(legalName = "Alice"),
                        ),
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123459",
                        ),
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123460",
                            subcontract = UpdateCompanyWithSubcontract.Subcontract(title = "Alice"),
                        ),
                        UpdateCompanyWithSubcontract(
                            companyCnpj = "123461",
                            contract = UpdateCompanyWithSubcontract.Contract(title = "Alice"),
                        ),
                    )
                )

                coEvery {
                    companyService.findByCnpjs(
                        listOf(
                            "123456",
                            "123457",
                            "123458",
                            "123460",
                            "123461"
                        )
                    )
                } returns listOf(
                    company,
                    company2,
                    company3,
                    company4,
                )

                coEvery { companyContractService.findByIds(listOf(contract.id, contract4.id)) } returns listOf(
                    contract,
                    contract4
                )

                coEvery {
                    companySubcontractDataService.find(queryEq {
                        where {
                            companyId.inList(listOf(company.id, company3.id))
                        }
                    })
                } returns listOf(subcontract, subcontract3)

                coEvery {
                    companyService.update(companyUpdated, sendEvent = false)
                } returns companyUpdated

                coEvery {
                    companyService.update(company2Updated, sendEvent = false)
                } returns Exception("Something is wrong")

                coEvery {
                    companySubContractService.update(subcontractUpdated.toTransport(), sendEvent = false)
                } returns subcontractUpdated.toTransport()

                coEvery {
                    companySubContractService.update(subcontract3Updated.toTransport(), sendEvent = false)
                } returns subcontract3Updated.toTransport()

                coEvery {
                    companyContractService.update(contractUpdated, sendEvent = false)
                } returns contractUpdated

                coEvery {
                    companyContractService.update(contract4Updated, sendEvent = false)
                } returns contract4Updated

                internalAuthentication {
                    post("/backfill/company/update_with_subcontracts", request) { response ->
                        assertThat(response).isOK()
                    }
                }

                coVerifyOnce {
                    companyService.findByCnpjs(
                        listOf(
                            "123456",
                            "123457",
                            "123458",
                            "123460",
                            "123461"
                        )
                    )
                    companySubcontractDataService.find(queryEq {
                        where {
                            companyId.inList(listOf(company.id, company3.id))
                        }
                    })
                    companyService.update(companyUpdated, sendEvent = false)
                    companyService.update(company2Updated, sendEvent = false)
                    companySubContractService.update(subcontractUpdated.toTransport(), sendEvent = false)
                    companySubContractService.update(subcontract3Updated.toTransport(), sendEvent = false)
                    companyContractService.update(contractUpdated, sendEvent = false)
                    companyContractService.update(contract4Updated, sendEvent = false)
                }

                coVerifyNone {
                    companyService.update(match { it.id == company3.id }, sendEvent = false)
                    companyService.update(match { it.id == company4.id }, sendEvent = false)
                }
            }
    }

    @Test
    fun `updateSubcontractStatus - should update the subcontract status and return success response`() =
        runBlocking {
            val subcontract =
                TestModelFactory.buildCompanySubContract(externalId = "00001")
                    .copy(status = CompanySubContractStatus.PROCESSING)

            val subcontractUpdated = subcontract.copy(
                status = CompanySubContractStatus.ACTIVE,
                integrationStatus = CompanySubContractIntegrationStatus.PROCESSED,
            )

            coEvery {
                companySubContractService.findByIds(listOf(subcontract.id))
            } returns listOf(subcontract)

            coEvery {
                companySubContractService.update(subcontractUpdated, sendEvent = false)
            } returns subcontractUpdated

            val request = UpdateSubContractStatusRequest(
                subcontractIds = listOf(subcontract.id),
                status = CompanySubContractStatus.ACTIVE,
                integrationStatus = CompanySubContractIntegrationStatus.PROCESSED
            )

            internalAuthentication {
                post("/backfill/subcontract/update_status", request) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                companySubContractService.findByIds(any())
                companySubContractService.update(any(), sendEvent = false)
            }
        }

    @Test
    fun `#insertStandardCost should insert standard cost and return response with success count 1`() =
        mockRangeUuidAndDateTime { uuid, _ ->
            runBlocking {
                val standardCostItem = StandardCostBackfillItem(
                    companyBusinessUnit = CompanyBusinessUnit.CORRETORES,
                    companySize = CompanySize.MICRO,
                    adhesion = Adhesion.MANDATORY,
                    ansNumber = "123",
                    costByPersonDetail = CostByPersonDetail(
                        holder = GenderDetail(
                            male = listOf(
                                CostByAgeRange(
                                    minAge = 0,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(150.0)
                                )
                            ),
                            female = listOf(
                                CostByAgeRange(
                                    minAge = 8,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(285.0)
                                )
                            )
                        ),
                        dependent = GenderDetail(
                            male = listOf(
                                CostByAgeRange(
                                    minAge = 0,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(150.0)
                                )
                            ),
                            female = listOf(
                                CostByAgeRange(
                                    minAge = 8,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(285.0)
                                )
                            )
                        )
                    )
                )

                val request = StandardCostBackfillRequest(
                    listOf(
                        standardCostItem
                    )
                )

                val expectedStandardCost = TestModelFactory.buildStandardCost(
                    id = uuid,
                    companyBusinessUnit = standardCostItem.companyBusinessUnit,
                    companySize = standardCostItem.companySize,
                    adhesion = standardCostItem.adhesion,
                    ansNumber = standardCostItem.ansNumber,
                    costByPersonDetail = standardCostItem.costByPersonDetail
                )

                val toBeInserted = listOf(expectedStandardCost)
                val expectedResponse = BackfillResponse(1, 0)

                coEvery {
                    standardCostService.addList(toBeInserted)
                } returns toBeInserted.success()

                internalAuthentication {
                    post("/backfill/standard_cost/insert_standard_cost", body = request) { response ->
                        assertThat(response).isOKWithData(expectedResponse)
                    }
                }

                coVerifyOnce { standardCostService.addList(any()) }
            }
        }

    @Test
    fun `#insertStandardCost should not insert standard cost and return error when service fails`() =
        mockRangeUuidAndDateTime { uuid, _ ->
            runBlocking {
                val standardCostItem = StandardCostBackfillItem(
                    companyBusinessUnit = CompanyBusinessUnit.CORRETORES,
                    companySize = CompanySize.MICRO,
                    adhesion = Adhesion.MANDATORY,
                    ansNumber = "123",
                    costByPersonDetail = CostByPersonDetail(
                        holder = GenderDetail(
                            male = listOf(
                                CostByAgeRange(
                                    minAge = 0,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(150.0)
                                )
                            ),
                            female = listOf(
                                CostByAgeRange(
                                    minAge = 8,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(285.0)
                                )
                            )
                        ),
                        dependent = GenderDetail(
                            male = listOf(
                                CostByAgeRange(
                                    minAge = 0,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(150.0)
                                )
                            ),
                            female = listOf(
                                CostByAgeRange(
                                    minAge = 8,
                                    maxAge = 18,
                                    cost = BigDecimal.valueOf(285.0)
                                )
                            )
                        )
                    )
                )

                val request = StandardCostBackfillRequest(
                    listOf(
                        standardCostItem
                    )
                )

                val expectedStandardCost = TestModelFactory.buildStandardCost(
                    id = uuid,
                    companyBusinessUnit = standardCostItem.companyBusinessUnit,
                    companySize = standardCostItem.companySize,
                    adhesion = standardCostItem.adhesion,
                    ansNumber = standardCostItem.ansNumber,
                    costByPersonDetail = standardCostItem.costByPersonDetail
                )

                val toBeInserted = listOf(expectedStandardCost)
                val expectedResponse = BackfillResponse(0, 1)

                coEvery {
                    standardCostService.addList(toBeInserted)
                } returns Exception().failure()

                internalAuthentication {
                    post("/backfill/standard_cost/insert_standard_cost", body = request) { response ->
                        assertThat(response).isOKWithData(expectedResponse)
                    }
                }

                coVerifyOnce { standardCostService.addList(any()) }
            }
        }

    @Test
    fun `#changeMembersProduct should change members product in batch and return response successfully`() =
        runBlocking {
            val member1 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 1"
                )
            )
            val member2 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 2"
                )
            )
            val members = listOf(member1, member2)
            val memberIds = members.map { it.id }
            val expectedProduct = TestModelFactory.buildProduct(
                title = "Alice 3"
            )

            val productPriceListing = TestModelFactory.buildProductPriceListing(
                productId = expectedProduct.id,
            )
            val request = BackfillChangeMembersProductRequest(
                memberIds = memberIds,
                newProductId = expectedProduct.id
            )

            val updatedMember1 = member1.copy(
                selectedProduct = MemberProduct(
                    id = expectedProduct.id,
                    prices = expectedProduct.prices,
                    type = expectedProduct.type,
                    priceListing = expectedProduct.priceListing,
                    productPriceListingId = productPriceListing.id
                )
            )
            val updatedMember2 = member2.copy(
                selectedProduct = MemberProduct(
                    id = expectedProduct.id,
                    prices = expectedProduct.prices,
                    type = expectedProduct.type,
                    priceListing = expectedProduct.priceListing,
                    productPriceListingId = productPriceListing.id
                )
            )
            val expectedResponse = BackfillResponse(2, 0)

            coEvery { productService.getProduct(expectedProduct.id) } returns expectedProduct
            coEvery { productPriceListingService.getCurrent(expectedProduct.id) } returns productPriceListing
            coEvery { memberService.findByIds(memberIds) } returns members
            coEvery { memberService.update(updatedMember1) } returns updatedMember1
            coEvery { memberService.update(updatedMember2) } returns updatedMember2

            internalAuthentication {
                post(
                    "/backfill/member/change_members_product",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { productService.getProduct(any()) }
            coVerifyOnce { productPriceListingService.getCurrent(any()) }
            coVerifyOnce { memberService.findByIds(any()) }
            coVerify(exactly = 2) { memberService.update(any()) }
        }

    @Test
    fun `#changeMembersProduct should change members product in batch and return response successfully with one error`() =
        runBlocking {
            val member1 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 1"
                )
            )
            val member2 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 2"
                )
            )
            val members = listOf(member1, member2)
            val memberIds = members.map { it.id }
            val expectedProduct = TestModelFactory.buildProduct(
                title = "Alice 3"
            )
            val request = BackfillChangeMembersProductRequest(
                memberIds = memberIds,
                newProductId = expectedProduct.id
            )

            val productPriceListing = TestModelFactory.buildProductPriceListing(
                productId = expectedProduct.id,
            )
            val updatedMember1 = member1.copy(
                selectedProduct = MemberProduct(
                    id = expectedProduct.id,
                    prices = expectedProduct.prices,
                    type = expectedProduct.type,
                    priceListing = expectedProduct.priceListing,
                    productPriceListingId = productPriceListing.id
                )
            )

            val updatedMember2 = member2.copy(
                selectedProduct = MemberProduct(
                    id = expectedProduct.id,
                    prices = expectedProduct.prices,
                    type = expectedProduct.type,
                    priceListing = expectedProduct.priceListing,
                    productPriceListingId = productPriceListing.id
                )
            )

            val expectedResponse = BackfillResponse(1, 1)

            coEvery { productService.getProduct(expectedProduct.id) } returns expectedProduct
            coEvery { productPriceListingService.getCurrent(expectedProduct.id) } returns productPriceListing
            coEvery { memberService.findByIds(memberIds) } returns members
            coEvery { memberService.update(updatedMember1) } returns updatedMember1
            coEvery { memberService.update(updatedMember2) } returns Exception()

            internalAuthentication {
                post(
                    "/backfill/member/change_members_product",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { productService.getProduct(any()) }
            coVerifyOnce { productPriceListingService.getCurrent(any()) }
            coVerifyOnce { memberService.findByIds(any()) }
            coVerify(exactly = 2) { memberService.update(any()) }
        }

    @Test
    fun `#changeMembersProduct should not change members product in batch and return BadRequest when product cannot be retrieved`() =
        runBlocking {
            val member1 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 1"
                )
            )
            val member2 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 2"
                )
            )
            val members = listOf(member1, member2)
            val memberIds = members.map { it.id }
            val expectedProduct = TestModelFactory.buildProduct(
                title = "Alice 3"
            )
            val request = BackfillChangeMembersProductRequest(
                memberIds = memberIds,
                newProductId = expectedProduct.id
            )

            val expectedResponse = BackfillResponse(0, 1, message = "Product not found")

            coEvery { productService.getProduct(expectedProduct.id) } returns NotFoundException()

            internalAuthentication {
                post(
                    "/backfill/member/change_members_product",
                    body = request,
                ) { response ->
                    assertThat(response).isBadRequestWithData(expectedResponse)
                }
            }

            coVerifyOnce { productService.getProduct(any()) }
        }

    @Test
    fun `#changeMembersProduct should not change members product in batch and return BadRequest when product price listing cannot be retrieved`() =
        runBlocking {
            val member1 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 1"
                )
            )
            val member2 = TestModelFactory.buildMember(
                status = MemberStatus.ACTIVE,
                product = TestModelFactory.buildProduct(
                    title = "Alice 2"
                )
            )
            val members = listOf(member1, member2)
            val memberIds = members.map { it.id }
            val expectedProduct = TestModelFactory.buildProduct(
                title = "Alice 3"
            )
            val request = BackfillChangeMembersProductRequest(
                memberIds = memberIds,
                newProductId = expectedProduct.id
            )

            val expectedResponse = BackfillResponse(0, 1, message = "Product price listing not found")

            coEvery { productService.getProduct(expectedProduct.id) } returns expectedProduct
            coEvery { productPriceListingService.getCurrent(expectedProduct.id) } returns NotFoundException()

            internalAuthentication {
                post(
                    "/backfill/member/change_members_product",
                    body = request,
                ) { response ->
                    assertThat(response).isBadRequestWithData(expectedResponse)
                }
            }

            coVerifyOnce { productService.getProduct(any()) }
            coVerifyOnce { productPriceListingService.getCurrent(any()) }
        }

    @Test
    fun `#createNewCpplAndBlockOrRemoveOldCppl should create a new cppl and BLOCK the old one`() =
        mockRangeUUID { uuid ->
            val oldProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 01"
            )
            val newProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 02"
            )

            val companySubcontract = TestModelFactory.buildCompanySubContract()

            val oldCppl = TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = oldProduct.ansNumber!!,
                product = oldProduct,
                companySubContractId = companySubcontract.id,
            )
            val newCppl = oldCppl.copy(
                id = uuid,
                ansNumber = newProduct.ansNumber!!,
                product = newProduct,
                productId = newProduct.id
            )
            val oldCpplBlocked = oldCppl.copy(isBlockedForSale = true)

            val request = CreateNewCpplAndBlockOrRemoveOldCpplRequest(
                subContractId = companySubcontract.id,
                oldAnsNumber = oldProduct.ansNumber!!,
                productId = newProduct.id,
                action = CpplBackfillAction.BLOCK
            )

            val expectedResponse = BackfillResponse(
                successCount = 1, errorsCount = 0
            )

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                    companySubcontract.id,
                    oldProduct.ansNumber!!
                )
            } returns oldCppl
            coEvery { productService.getProduct(newProduct.id) } returns newProduct
            coEvery { companySubContractService.get(companySubcontract.id) } returns companySubcontract
            coEvery { companyProductPriceListingService.add(newCppl, companySubcontract) } returns newCppl
            coEvery { companyProductPriceListingService.updateBlockForSale(oldCppl, true) } returns oldCpplBlocked

            internalAuthentication {
                post(
                    "/backfill/company_product_price_listing/create_new_and_block_or_remove_old",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(any(), any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
            coVerifyOnce { companySubContractService.get(any()) }
            coVerifyOnce { companyProductPriceListingService.add(any(), any()) }
            coVerifyOnce { companyProductPriceListingService.updateBlockForSale(any(), any()) }
        }

    @Test
    fun `#createNewCpplAndBlockOrRemoveOldCppl should create a new cppl and REMOVE the old one`() =
        mockRangeUUID { uuid ->
            val oldProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 01"
            )
            val newProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 02"
            )

            val companySubcontract = TestModelFactory.buildCompanySubContract()

            val oldCppl = TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = oldProduct.ansNumber!!,
                product = oldProduct,
                companySubContractId = companySubcontract.id,
            )
            val newCppl = oldCppl.copy(
                id = uuid,
                ansNumber = newProduct.ansNumber!!,
                product = newProduct,
                productId = newProduct.id
            )

            val request = CreateNewCpplAndBlockOrRemoveOldCpplRequest(
                subContractId = companySubcontract.id,
                oldAnsNumber = oldProduct.ansNumber!!,
                productId = newProduct.id,
                action = CpplBackfillAction.REMOVE
            )

            val expectedResponse = BackfillResponse(
                successCount = 1, errorsCount = 0
            )

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                    companySubcontract.id,
                    oldProduct.ansNumber!!
                )
            } returns oldCppl
            coEvery { productService.getProduct(newProduct.id) } returns newProduct
            coEvery { companySubContractService.get(companySubcontract.id) } returns companySubcontract
            coEvery { companyProductPriceListingService.add(newCppl, companySubcontract) } returns newCppl
            coEvery { companyProductPriceListingService.delete(oldCppl) } returns true

            internalAuthentication {
                post(
                    "/backfill/company_product_price_listing/create_new_and_block_or_remove_old",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(any(), any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
            coVerifyOnce { companySubContractService.get(any()) }
            coVerifyOnce { companyProductPriceListingService.add(any(), any()) }
            coVerifyOnce { companyProductPriceListingService.delete(any()) }
        }

    @Test
    fun `#createNewCpplAndBlockOrRemoveOldCppl should return bad request when new product has no ans number`() =
        runBlocking {
            val oldProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 01"
            )
            val newProduct = TestModelFactory.buildProduct(
                ansNumber = null
            )

            val companySubcontract = TestModelFactory.buildCompanySubContract()

            val oldCppl = TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = oldProduct.ansNumber!!,
                product = oldProduct,
                companySubContractId = companySubcontract.id,
            )

            val request = CreateNewCpplAndBlockOrRemoveOldCpplRequest(
                subContractId = companySubcontract.id,
                oldAnsNumber = oldProduct.ansNumber!!,
                productId = newProduct.id,
                action = CpplBackfillAction.REMOVE
            )

            val expectedResponse = BackfillResponse(
                successCount = 0,
                errorsCount = 1,
                message = "Product has null ans number"
            )

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                    companySubcontract.id,
                    oldProduct.ansNumber!!
                )
            } returns oldCppl
            coEvery { productService.getProduct(newProduct.id) } returns newProduct

            internalAuthentication {
                post(
                    "/backfill/company_product_price_listing/create_new_and_block_or_remove_old",
                    body = request,
                ) { response ->
                    assertThat(response).isBadRequestWithData(expectedResponse)
                }
            }

            coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(any(), any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
        }

    @Test
    fun `#createNewCpplAndBlockOrRemoveOldCppl should not create a new cppl and REMOVE the old one when add fail`() =
        mockRangeUUID { uuid ->
            val oldProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 01"
            )
            val newProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 02"
            )

            val companySubcontract = TestModelFactory.buildCompanySubContract()

            val oldCppl = TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = oldProduct.ansNumber!!,
                product = oldProduct,
                companySubContractId = companySubcontract.id,
            )

            val newCppl = oldCppl.copy(
                id = uuid,
                ansNumber = newProduct.ansNumber!!,
                product = newProduct,
                productId = newProduct.id
            )

            val request = CreateNewCpplAndBlockOrRemoveOldCpplRequest(
                subContractId = companySubcontract.id,
                oldAnsNumber = oldProduct.ansNumber!!,
                productId = newProduct.id,
                action = CpplBackfillAction.REMOVE
            )

            val expectedResponse = BackfillResponse(
                successCount = 0, errorsCount = 1
            )

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                    companySubcontract.id,
                    oldProduct.ansNumber!!
                )
            } returns oldCppl
            coEvery { productService.getProduct(newProduct.id) } returns newProduct
            coEvery { companySubContractService.get(companySubcontract.id) } returns companySubcontract
            coEvery { companyProductPriceListingService.add(newCppl, companySubcontract) } returns Exception()

            internalAuthentication {
                post(
                    "/backfill/company_product_price_listing/create_new_and_block_or_remove_old",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(any(), any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
            coVerifyOnce { companySubContractService.get(any()) }
            coVerifyOnce { companyProductPriceListingService.add(any(), any()) }
        }

    @Test
    fun `#createNewCpplAndBlockOrRemoveOldCppl should not create a new cppl and REMOVE the old one when remove fail`() =
        mockRangeUUID { uuid ->
            val oldProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 01"
            )
            val newProduct = TestModelFactory.buildProduct(
                ansNumber = "Alice 02"
            )

            val companySubcontract = TestModelFactory.buildCompanySubContract()

            val oldCppl = TestModelFactory.buildCompanyProductPriceListing(
                ansNumber = oldProduct.ansNumber!!,
                product = oldProduct,
                companySubContractId = companySubcontract.id,
            )
            val newCppl = oldCppl.copy(
                id = uuid,
                ansNumber = newProduct.ansNumber!!,
                product = newProduct,
                productId = newProduct.id
            )

            val request = CreateNewCpplAndBlockOrRemoveOldCpplRequest(
                subContractId = companySubcontract.id,
                oldAnsNumber = oldProduct.ansNumber!!,
                productId = newProduct.id,
                action = CpplBackfillAction.REMOVE
            )

            val expectedResponse = BackfillResponse(
                successCount = 0, errorsCount = 1
            )

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                    companySubcontract.id,
                    oldProduct.ansNumber!!
                )
            } returns oldCppl
            coEvery { productService.getProduct(newProduct.id) } returns newProduct
            coEvery { companySubContractService.get(companySubcontract.id) } returns companySubcontract
            coEvery { companyProductPriceListingService.add(newCppl, companySubcontract) } returns newCppl
            coEvery { companyProductPriceListingService.delete(oldCppl) } returns Exception()

            internalAuthentication {
                post(
                    "/backfill/company_product_price_listing/create_new_and_block_or_remove_old",
                    body = request,
                ) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerifyOnce { companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(any(), any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
            coVerifyOnce { companySubContractService.get(any()) }
            coVerifyOnce { companyProductPriceListingService.add(any(), any()) }
            coVerifyOnce { companyProductPriceListingService.delete(any()) }
        }

    @Nested
    inner class BackfillUpdateBillingAccountablePartyAddress {
        @Test
        fun `#update billing from national id list`() = runBlocking {
            val billingAccountableParty1 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "01", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty2 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "02", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty3 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "03", email = "<EMAIL>")

            val companyExternalInformationResponse = CompanyExternalInformationResponse(
                createdAt = "13/10/2016",
                status = "ATIVA",
                type = "MATRIZ",
                legalName = "ACME LTDA",
                name = "ACME",
                sizeCategory = "MICRO EMPRESA",
                postalCode = "05.018-000",
                state = "SP",
                city = "SAO PAULO",
                neighborhood = "PERDIZES",
                street = "RUA CAYOWAA",
                number = "519",
                complement = "APT 123"
            )

            val updatedBillingAccountableParty1 =
                billingAccountableParty1.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty2 =
                billingAccountableParty2.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty3 =
                billingAccountableParty3.copy(address = companyExternalInformationResponse.toAddress())

            coEvery { billingAccountablePartyService.findByNationalIds(listOf("01", "02", "03")) } returns listOf(
                billingAccountableParty1,
                billingAccountableParty2,
                billingAccountableParty3,
            )

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "01",
                        "03"
                    )
                })
            } returns companyExternalInformationResponse

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "02"
                    )
                })
            } throws Exception("Something is wrong with request")

            val request = BackfillBillingAccountablePartyUpdateAddressRequest(
                cnpjs = listOf("01", "02", "03"),
            )

            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty1) } returns updatedBillingAccountableParty1
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty2) } returns updatedBillingAccountableParty2
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty3) } returns Exception("Some error")

            internalAuthentication {
                post(
                    "/backfill/billing_accountable_party/update_address",
                    body = request,
                ) { response ->
                    assertThat(response).isOK()
                    val content = gson.fromJson<InternalFeatureResponse>(response.bodyAsText())
                    assertThat(content.successCount).isEqualTo(1)
                    assertThat(content.errorsCount).isEqualTo(2)
                    assertThat(content.additionalInfo)
                        .isEqualTo(
                            mapOf(
                                billingAccountableParty3.id.toString() to "Some error",
                                billingAccountableParty2.id.toString() to "Something is wrong with request",
                            )
                        )
                }
            }
        }

        @Test
        fun `#update billing from ids list`() = runBlocking {
            val billingAccountableParty1 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "01", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty2 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "02", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty3 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "03", email = "<EMAIL>")

            val companyExternalInformationResponse = CompanyExternalInformationResponse(
                createdAt = "13/10/2016",
                status = "ATIVA",
                type = "MATRIZ",
                legalName = "ACME LTDA",
                name = "ACME",
                sizeCategory = "MICRO EMPRESA",
                postalCode = "05.018-000",
                state = "SP",
                city = "SAO PAULO",
                neighborhood = "PERDIZES",
                street = "RUA CAYOWAA",
                number = "519",
                complement = "APT 123"
            )

            val updatedBillingAccountableParty1 =
                billingAccountableParty1.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty2 =
                billingAccountableParty2.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty3 =
                billingAccountableParty3.copy(address = companyExternalInformationResponse.toAddress())

            coEvery {
                billingAccountablePartyService.findById(
                    listOf(
                        billingAccountableParty1.id,
                        billingAccountableParty2.id,
                        billingAccountableParty3.id
                    )
                )
            } returns listOf(
                billingAccountableParty1,
                billingAccountableParty2,
                billingAccountableParty3,
            )

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "01",
                        "03"
                    )
                })
            } returns companyExternalInformationResponse

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "02"
                    )
                })
            } throws Exception("Something is wrong with request")

            val request = BackfillBillingAccountablePartyUpdateAddressRequest(
                ids = listOf(billingAccountableParty1.id, billingAccountableParty2.id, billingAccountableParty3.id),
            )

            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty1) } returns updatedBillingAccountableParty1
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty2) } returns updatedBillingAccountableParty2
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty3) } returns Exception("Some error")

            internalAuthentication {
                post(
                    "/backfill/billing_accountable_party/update_address",
                    body = request,
                ) { response ->
                    assertThat(response).isOK()
                    val content = gson.fromJson<InternalFeatureResponse>(response.bodyAsText())
                    assertThat(content.successCount).isEqualTo(1)
                    assertThat(content.errorsCount).isEqualTo(2)
                    assertThat(content.additionalInfo)
                        .isEqualTo(
                            mapOf(
                                billingAccountableParty3.id.toString() to "Some error",
                                billingAccountableParty2.id.toString() to "Something is wrong with request",
                            )
                        )
                }
            }
        }

        @Test
        fun `#update billing by null or empty address`() = runBlocking {
            val billingAccountableParty1 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "01", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty2 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "02", email = "<EMAIL>")
                    .copy(address = null)
            val billingAccountableParty3 =
                TestModelFactory.buildBillingAccountableParty(nationalId = "03", email = "<EMAIL>")

            val companyExternalInformationResponse = CompanyExternalInformationResponse(
                createdAt = "13/10/2016",
                status = "ATIVA",
                type = "MATRIZ",
                legalName = "ACME LTDA",
                name = "ACME",
                sizeCategory = "MICRO EMPRESA",
                postalCode = "05.018-000",
                state = "SP",
                city = "SAO PAULO",
                neighborhood = "PERDIZES",
                street = "RUA CAYOWAA",
                number = "519",
                complement = "APT 123"
            )

            val updatedBillingAccountableParty1 =
                billingAccountableParty1.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty2 =
                billingAccountableParty2.copy(address = companyExternalInformationResponse.toAddress())
            val updatedBillingAccountableParty3 =
                billingAccountableParty3.copy(address = companyExternalInformationResponse.toAddress())

            coEvery {
                billingAccountablePartyService.findByEmptyAddressPaginated(
                    BillingAccountablePartyType.LEGAL_PERSON, 0, 1
                )
            } returns listOf(
                billingAccountableParty1,
            )

            coEvery {
                billingAccountablePartyService.findByEmptyAddressPaginated(
                    BillingAccountablePartyType.LEGAL_PERSON, 1, 1
                )
            } returns listOf(
                billingAccountableParty2,
            )

            coEvery {
                billingAccountablePartyService.findByEmptyAddressPaginated(
                    BillingAccountablePartyType.LEGAL_PERSON, 2, 1
                )
            } returns listOf(
                billingAccountableParty3,
            )

            coEvery {
                billingAccountablePartyService.findByEmptyAddressPaginated(
                    BillingAccountablePartyType.LEGAL_PERSON, 3, 1
                )
            } returns emptyList()

            coEvery {
                billingAccountablePartyService.findById(
                    listOf(
                        billingAccountableParty1.id,
                        billingAccountableParty2.id,
                        billingAccountableParty3.id
                    )
                )
            } returns listOf(
                billingAccountableParty1,
                billingAccountableParty2,
                billingAccountableParty3,
            )

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "01",
                        "03"
                    )
                })
            } returns companyExternalInformationResponse

            coEvery {
                receitaFederalClient.getCompanyInfoByCNPJ(match {
                    it in listOf(
                        "02"
                    )
                })
            } throws Exception("Something is wrong with request")

            val request = BackfillBillingAccountablePartyUpdateAddressRequest(
                limit = 1
            )

            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty1) } returns updatedBillingAccountableParty1
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty2) } returns updatedBillingAccountableParty2
            coEvery { billingAccountablePartyService.update(updatedBillingAccountableParty3) } returns Exception("Some error")

            internalAuthentication {
                post(
                    "/backfill/billing_accountable_party/update_address",
                    body = request,
                ) { response ->
                    assertThat(response).isOK()
                    val content = gson.fromJson<InternalFeatureResponse>(response.bodyAsText())
                    assertThat(content.successCount).isEqualTo(1)
                    assertThat(content.errorsCount).isEqualTo(2)
                    assertThat(content.additionalInfo)
                        .isEqualTo(
                            mapOf(
                                billingAccountableParty3.id.toString() to "Some error",
                                billingAccountableParty2.id.toString() to "Something is wrong with request",
                            )
                        )
                }
            }
        }

        @Test
        fun `#emitBeneficiaryUpdatedEvent produce 2 events`() = runBlocking {
            val beneficiary1 = TestModelFactory.buildBeneficiary()
            val beneficiary2 = TestModelFactory.buildBeneficiary()

            coEvery { beneficiaryService.findByFilters(
                personId = null,
                companyId = null,
                parentId = null,
                currentPhase = null,
                range = IntRange(0, 0)
            ) } returns listOf(beneficiary1).success()

            coEvery { beneficiaryService.findByFilters(
                personId = null,
                companyId = null,
                parentId = null,
                currentPhase = null,
                range = IntRange(1, 1)
            ) } returns listOf(beneficiary2).success()

            coEvery { beneficiaryService.findByFilters(
                personId = null,
                companyId = null,
                parentId = null,
                currentPhase = null,
                range = IntRange(2, 2)
            ) } returns emptyList<Beneficiary>().success()

            coEvery { kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiary = beneficiary1,
                    eventAction = NotificationEventAction.UPDATED
                ), beneficiary1.id.toString()
            ) } returns mockk()

            coEvery { kafkaProducerService.produce(
                BeneficiaryChangedEvent(
                    beneficiary = beneficiary2,
                    eventAction = NotificationEventAction.UPDATED
                ), beneficiary2.id.toString()
            ) } returns mockk()

            internalAuthentication {
                post(
                    "/backfill/beneficiary/emit_beneficiary_updated_event",
                    body = LimitedBackfillRequest(
                        limit = 1,
                        offset = 0
                    ),
                ) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerify(exactly = 3) { beneficiaryService.findByFilters(
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            ) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }
    }
}
