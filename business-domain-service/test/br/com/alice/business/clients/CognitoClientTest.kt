import br.com.alice.business.clients.CognitoClient
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpMethod
import io.ktor.http.headersOf
import io.mockk.clearAllMocks
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.TestInstance
import java.io.File
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CognitoClientTest {

    @AfterTest
    fun setup() = clearAllMocks()

    private var cognitoClient: CognitoClient
    private val localFile = File("testResources/cognito/example-file.pdf")

    init {
        val mockHttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Get)
                    val responseHeaders = headersOf(
                        "Content-Type" to listOf(ContentType.Application.Pdf.toString()),
                        "Content-Disposition" to listOf("attachment; filename=\"example-file.doc\"")
                    )
                    respond(localFile.readBytes(), headers = responseHeaders)
                }
            }
        }

        cognitoClient = CognitoClient(mockHttpClient)
    }

    @Test
    fun `#downloadFile testDownloadFileSuccess`() = runBlocking<Unit> {
        val response =
            cognitoClient.downloadFile("https://www.internet.com")
        Assertions.assertThat(response.get().second).isEqualTo("example-file.doc")
    }

    @Test
    fun `#downloadFile testDownloadFileWithoutContentDisposition`() = runBlocking<Unit> {
        val mockHttpClient = HttpClient(MockEngine) {
            engine {
                addHandler {
                    respond(ByteArray(0), headers = Headers.build { /* No Content-Disposition header */ })
                }
            }
        }

        val clientWithoutContentDisposition = CognitoClient(mockHttpClient)

        val response =
            clientWithoutContentDisposition.downloadFile("https://www.internet.com")

        Assertions.assertThat(response.get().second).isEqualTo("downloaded_file")
    }
}
