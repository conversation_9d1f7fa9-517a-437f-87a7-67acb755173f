package br.com.alice.business.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.observability.Spannable
import br.com.alice.common.service.serialization.simpleGson
import com.github.kittinunf.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpHeaders


class ENotasGWClient(
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) { simpleGson() }
    }, timeoutInMillis = 15_000)
) : Spannable {

    suspend fun downloadFile(url: String): Result<Pair<ByteArray, String>, Throwable> =
        coResultOf {
            span("downloadFile") {
                val httpResponse: HttpResponse = httpClient.get(url)
                val contentDisposition = httpResponse.headers[HttpHeaders.ContentDisposition]
                val fileName = extractFileNameFromContentDisposition(contentDisposition)
                val fileBytes = httpResponse.body<ByteArray>()
                Pair(fileBytes, fileName)
            }
        }

    private fun extractFileNameFromContentDisposition(contentDisposition: String?): String {
        if (!contentDisposition.isNullOrBlank()) {
            val parts = contentDisposition.split(";")
            for (part in parts) {
                if (part.trim().startsWith("filename")) {
                    val fileName = part.substring(part.indexOf('=') + 1).trim()
                    return fileName.removeSurrounding("\"")
                }
            }
        }
        return "downloaded_file"
    }
}

