package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.core.suspend
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BeneficiaryAlreadyOnCorrectPhaseException
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvalidBeneficiaryOnboardingNextPhaseException
import br.com.alice.data.layer.models.InvalidBeneficiaryOnboardingToPhaseException
import br.com.alice.data.layer.models.InvalidBusinessOnboardingFlowInitialStateException
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class UserAuthenticatedConsumer(
    private val beneficiaryService: BeneficiaryService,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
) : Consumer() {

    suspend fun startBeneficiaryOnboarding(event: UserAuthenticatedEvent) = withSubscribersEnvironment {
        logger.info(
            "UserAuthenticatedConsumer::startBeneficiaryOnboarding - checking for ready_to_onbarding phase",
            "event.payload.personId"
        )

        val beneficiary = beneficiaryService.findByPersonId(
            event.payload.personId,
            BeneficiaryService.FindOptions(withOnboarding = true)
        ).getOrNullIfNotFound()
            ?: return@withSubscribersEnvironment true.success()

        if (beneficiary.onboarding?.flowType == BeneficiaryOnboardingFlowType.UNDEFINED)
            return@withSubscribersEnvironment true.success()


        if (beneficiary.onboarding?.currentPhase?.phase == BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD) {
            val expectedNextPhase = getPhaseTypeBasedOnRiskFlow(beneficiary.onboarding!!.flowType)

            moveBeneficiaryToPhase(beneficiary.id, expectedNextPhase)
                .then {
                    logger.info(
                        "Moving beneficiary to next onboarding phase",
                        "person_id" to beneficiary.personId,
                        "beneficiary_id" to beneficiary.id,
                    )
                }
        } else {
            logger.info(
                "UserAuthenticatedConsumer::startBeneficiaryOnboarding - dependent already into right phase",
                "person_id" to beneficiary.personId,
                "beneficiary_id" to beneficiary.id,
                "phase" to beneficiary.onboarding?.currentPhase?.phase,
            )
        }

        val familyOnboardingEnabled = FeatureService.get(
            FeatureNamespace.BUSINESS,
            "enable_family_onboarding",
            false
        )

        if (familyOnboardingEnabled) {
            beneficiaryService.findByParentId(beneficiary.id, BeneficiaryService.FindOptions(withOnboarding = true), true)
                .mapEach { dependentBeneficiary ->
                    val flowType = dependentBeneficiary.onboarding!!.flowType

                    if (flowType == BeneficiaryOnboardingFlowType.UNDEFINED) {
                        logger.info(
                            "UserAuthenticatedConsumer::startBeneficiaryOnboarding - dependent with undefined flow",
                            "dependet_beneficiary_id" to dependentBeneficiary.id,
                            "beneficiary_id" to beneficiary.id,
                        )

                        return@mapEach
                    }

                    if (dependentBeneficiary.onboarding?.currentPhase?.phase == BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD) {
                        val expectedNextPhase = getPhaseTypeBasedOnRiskFlow(flowType)

                        moveBeneficiaryToPhase(dependentBeneficiary.id, expectedNextPhase)
                            .then {
                                logger.info(
                                    "UserAuthenticatedConsumer::startBeneficiaryOnboarding - moving dependent to next onboarding phase",
                                    "dependent_beneficiary_id" to dependentBeneficiary.id,
                                    "phase" to expectedNextPhase,
                                    "flow" to dependentBeneficiary.onboarding!!.flowType
                                )
                            }
                    } else {
                        logger.info(
                            "UserAuthenticatedConsumer::startBeneficiaryOnboarding - dependent already into right phase",
                            "dependent_beneficiary_id" to dependentBeneficiary.id,
                        )
                    }
                }
        } else {
            logger.info("UserAuthenticatedConsumer::startBeneficiaryOnboarding - skip the family onboarding once FF.enable_family_onboarding is false")
        }

        true.success()
    }

    private fun getPhaseTypeBasedOnRiskFlow(flowType: BeneficiaryOnboardingFlowType) =
        if (flowType == BeneficiaryOnboardingFlowType.NO_RISK_FLOW) BeneficiaryOnboardingPhaseType.REGISTRATION else BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION

    private suspend fun moveBeneficiaryToPhase(beneficiaryId: UUID, phaseType: BeneficiaryOnboardingPhaseType) =
        beneficiaryOnboardingService.moveToPhase(beneficiaryId, phaseType, LocalDateTime.now())
            .coFoldError(
                BeneficiaryAlreadyOnCorrectPhaseException::class to suspend() { _: Throwable -> true.success() },
                InvalidBeneficiaryOnboardingToPhaseException::class to suspend() { _: Throwable -> true.success() },
                InvalidBeneficiaryOnboardingNextPhaseException::class to suspend() { _: Throwable -> true.success() },
                InvalidBusinessOnboardingFlowInitialStateException::class to suspend() { _: Throwable -> true.success() },
            ).thenError {
                logger.error(
                    "UserAuthenticatedConsumer::startBeneficiaryOnboarding - something is wrong",
                    "beneficiary_id" to beneficiaryId,
                    "phase" to phaseType
                )
            }
}
