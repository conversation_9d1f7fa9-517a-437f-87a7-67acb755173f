package br.com.alice.business.controllers

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.client.StandardCostService
import br.com.alice.business.clients.ReceitaFederalClient
import br.com.alice.business.converters.CompanyProductPriceListingConverter.toPriceListItems
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.BeneficiaryCanceledEvent
import br.com.alice.business.events.BeneficiaryChangedEvent
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.business.events.RequestCreateSubcontractFromCompanyEvent
import br.com.alice.business.events.RequestUpdateDependentEmployeeBind
import br.com.alice.business.exceptions.BeneficiaryDoesntHaveOnboarding
import br.com.alice.business.models.*
import br.com.alice.business.services.internal.BeneficiaryHubspotService
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType.NO_RISK_FLOW
import br.com.alice.data.layer.models.GracePeriodType.TOTAL_EXEMPTION
import br.com.alice.data.layer.models.GracePeriodType.TOTAL_GRACE_PERIOD
import br.com.alice.data.layer.services.BeneficiaryModelDataService
import br.com.alice.data.layer.services.BeneficiaryOnboardingModelDataService
import br.com.alice.data.layer.services.CompanyContractModelDataService
import br.com.alice.data.layer.services.CompanyModelDataService
import br.com.alice.data.layer.services.CompanySubContractModelDataService
import br.com.alice.ehr.model.BackfillResponse
import br.com.alice.membership.client.ContractGenerator
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.fromTotvsFormatDateYMD
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.product.client.PriceListingService
import br.com.alice.product.client.ProductPriceListingService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.runCatching
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.server.plugins.BadRequestException
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackFillController(
    private val beneficiaryService: BeneficiaryService,
    private val companyService: CompanyService,
    private val memberService: MemberService,
    private val kafkaProducerService: KafkaProducerService,
    private val memberProductPriceService: MemberProductPriceService,
    private val productService: ProductService,
    private val contractGenerator: ContractGenerator,
    private val cassiMemberService: CassiMemberService,
    private val beneficiaryHubspotService: BeneficiaryHubspotService,
    private val companyContractService: CompanyContractService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
    private val companySubContractService: CompanySubContractService,
    private val productPriceListingService: ProductPriceListingService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val beneficiaryDataService: BeneficiaryModelDataService,
    private val beneficiaryOnboardingDataService: BeneficiaryOnboardingModelDataService,
    private val companyContractDataService: CompanyContractModelDataService,
    private val companySubcontractDataService: CompanySubContractModelDataService,
    private val companyDataService: CompanyModelDataService,
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline,
    private val priceListingService: PriceListingService,
    private val personService: PersonService,
    private val standardCostService: StandardCostService,
    private val receitaFederalClient: ReceitaFederalClient,
) : BackfillBaseController() {

    suspend fun requestUpdateDependent(request: RequestUpdateDependent): Response = withBackfillEnvironment {
        logger.info(
            "Start backFill to request update to beneficiary dependent"
        )

        request.ids.pmap {
            kafkaProducerService.produce(RequestUpdateDependentEmployeeBind(it))
        }.toResponse()
    }

    suspend fun archiveBeneficiaries(request: ArchiveBeneficiaryRequest): Response = withBackfillEnvironment {
        logger.info(
            "Start backFill to archive Beneficiary",
            "beneficiary_ids" to request.ids,
        )

        val results = request.ids.pmap { beneficiaryId ->
            beneficiaryService.archiveBeneficiaryById(beneficiaryId)
                .fold(
                    {
                        logger.info("Beneficiary archived successfully")
                        SingleArchiveBeneficiaryResponse(beneficiaryId, true)
                    },
                    {
                        logger.error("Beneficiary not archived due error", it)
                        SingleArchiveBeneficiaryResponse(beneficiaryId, false)
                    }
                )
        }
        ArchiveBeneficiaryResponse(results).toResponse()
    }

    suspend fun createBeneficiaryForActiveMember(
        request: BackfillBeneficiaryWithMemberIdRequest
    ) = withBackfillEnvironment {
        logger.info(
            "Start backFill to create beneficiary based on oldBeneficiary and link to existing member",
            "old_beneficiary_id" to request.oldBeneficiaryId,
            "member_id" to request.memberId
        )

        beneficiaryService.get(request.oldBeneficiaryId, FindOptions(withOnboarding = true))
            .map {
                it.copy(
                    id = RangeUUID.generate(),
                    memberId = request.oldBeneficiaryId,
                    canceledDescription = null,
                    canceledReason = null,
                    hasContributed = null,
                    canceledAt = null,
                    memberStatus = request.memberStatus ?: it.memberStatus,
                )
            }
            .flatMap {
                beneficiaryService.addBeneficiaryForExistentMember(
                    beneficiary = it,
                    initialProductId = request.productId,
                    flowType = it.onboarding!!.flowType,
                    phase = it.onboarding!!.currentPhase!!.phase,
                )
            }
            .foldResponse()
    }

    private suspend fun getBeneficiaryAndCreateHubspotDeal(beneficiaryId: UUID): Result<BeneficiaryHubspot, Throwable> {
        return beneficiaryService.get(beneficiaryId, FindOptions(withOnboarding = true))
            .flatMap { beneficiary ->
                beneficiary.onboarding?.let {
                    beneficiaryHubspotService.create(beneficiary, it.initialProductId, it.flowType)
                } ?: BeneficiaryDoesntHaveOnboarding(beneficiary.id.toString()).failure()
            }
    }

    suspend fun createBeneficiaryHubspotDealAndContact(
        request: BackfillBeneficiaryHubspotDealAndContact
    ) = withBackfillEnvironment {
        logger.info(
            "Start backFill to create beneficiary hubspot deal and contact",
            "beneficiariesIds" to request.beneficiaryIds
        )

        request.beneficiaryIds.pmap { beneficiaryId ->
            getBeneficiaryAndCreateHubspotDeal(beneficiaryId)
        }.lift().thenError {
            logger.info(
                "It was not possible to create hubspot deal",
                "exception" to it
            )
        }.foldResponse()
    }


    suspend fun populateHiredAtForBeneficiary(request: BackFillHiredAtBeneficiaryRequest): Response =
        withBackfillEnvironment {
            request.beneficiaries.map { getBeneficiaryAndUpdateHiredAt(it.memberId, it.hiredAt) }.success()
                .foldResponse()
        }

    private suspend fun getBeneficiaryAndUpdateHiredAt(memberId: UUID, hiredAt: String) =
        beneficiaryService.findByMemberId(memberId)
            .flatMap { beneficiaryService.update(it.copy(hiredAt = LocalDate.parse(hiredAt).atStartOfDay())) }

    suspend fun populateCanceledReasonForBeneficiary(request: BackFillCanceledReasonBeneficiaryRequest): Response =
        withBackfillEnvironment {
            request.beneficiaries.map { getBeneficiaryAndUpdateCanceledReason(it.memberId, it.canceledReason) }
                .success()
                .foldResponse()
        }

    suspend fun cancellationRequestByBeneficiaryIds(request: BackFillCancellationRequestInBatch): Response =
        withBackfillEnvironment {
            request.beneficiaryIds.map { beneficiaryId ->
                val beneficiary = beneficiaryService.get(beneficiaryId).get()
                val member = memberService.get(beneficiary.memberId).get()

                val updatedBeneficiary = beneficiary.copy(
                    canceledAt = request.canceledAt.atEndOfTheDay(),
                    canceledReason = request.canceledReason,
                    canceledDescription = request.canceledDescription,
                    hasContributed = request.hasContributed,
                )

                memberService.update(member.cancel(request.canceledAt.atEndOfTheDay()), sendEvent = request.sendEvent)
                    .then { if (request.sendEvent) kafkaProducerService.produce(MemberCancelledEvent(it)) }
                    .then { beneficiaryService.update(updatedBeneficiary) }
                    .then {
                        if (request.sendEvent) kafkaProducerService.produce(
                            BeneficiaryCanceledEvent(
                                updatedBeneficiary
                            )
                        )
                    }
            }
                .toResponse()
        }


    private suspend fun getBeneficiaryAndUpdateCanceledReason(
        memberId: UUID,
        canceledReason: BeneficiaryCancelationReason
    ) =
        beneficiaryService.findByMemberId(memberId)
            .flatMap { beneficiaryService.update(it.copy(canceledReason = canceledReason)) }

    suspend fun backFillMemberProductPrice(request: BackFillMemberProductPrice): Response = withBackfillEnvironment {
        logger.info("Start MemberProductPrice backfill", "person_id" to request.personId)

        memberService.findActiveMembership(request.personId)
            .map { memberProductPriceService.setCurrent(it.id, it.productId, LocalDateTime.now()) }
            .foldResponse()
    }

    suspend fun backFillOnboardingPhase(request: BackFillOnboardingRequest): Response = withBackfillEnvironment {
        logger.info("Start OnboardigPhaseBackfill", "cnpjs" to request.cnpjs).success()
            .flatMap { getBeneficiaryAndMember(request) }
            .flatMapEach { it.toMoveToPhaseEvent().produceToKafka() }
            .then { logger.info("OnboardigPhaseBackfill finished successfully", "total_success" to it.size) }
            .then { logger.error("OnboardigPhaseBackfill finished with errors", it) }
            .foldResponse()
    }

    suspend fun retryBeneficiaryCreated(request: BackFillBeneficiaryCreated): Response = withBackfillEnvironment {
        logger.info("Start RetryBeneficiaryCreated", "BeneficiaryId" to request.beneficiaryId).success()
            .flatMap { beneficiaryService.get(request.beneficiaryId) }
            .flatMap { it.toBeneficiaryCreatedEvent(request).produceToKafka() }
            .then { logger.info("Backfill finished successfully") }
            .then { logger.error("Backfill finished with errors", it) }
            .foldResponse()
    }

    suspend fun backfillRemovingDuplicatedCurrentCppl(request: BackfillRemovingDuplicatedCurrentCppl): Response =
        withBackfillEnvironment {
            request.items.map { requestItem ->
                logger.info(
                    "Start backfill to remove duplicated current cppl",
                    "sub_contract_id" to requestItem.subContractId,
                    "ans_number" to requestItem.ansNumber
                )
                companyProductPriceListingService.findBySubContractIdAndAnsNumber(
                    requestItem.subContractId,
                    requestItem.ansNumber
                )
                    .map { cppls -> cppls.groupBy { it.startDate } }
                    .map { cppls ->
                        val removedCppls = mutableListOf<UUID>()
                        cppls.filter { it.value.size > 1 }
                            .forEach { entry ->
                                entry.value.sortedByDescending { it.createdAt }.drop(1).forEach {
                                    companyProductPriceListingService.delete(it).get()
                                    removedCppls.add(it.id)
                                }
                            }
                        cppls.values.flatten().filterNot { removedCppls.contains(it.id) }
                    }
                    .map { cppls -> cppls.sortedByDescending { it.startDate } }
                    .map { cppls ->
                        cppls.foldIndexed(emptyList<CompanyProductPriceListing>()) { index, acc, currentCppl ->
                            if (index == 0) {
                                return@foldIndexed acc + currentCppl
                            }
                            val previousCppl = acc.last()
                            return@foldIndexed companyProductPriceListingService.update(
                                currentCppl.copy(endDate = previousCppl.startDate.minusDays(1)),
                                sendEvent = false
                            )
                                .map { acc + it }.get()

                        }
                    }.map { cppls ->
                        if (!cppls.any { it.endDate == null }) {
                            cppls.maxBy { it.startDate }.let {
                                companyProductPriceListingService.update(it.copy(endDate = null), sendEvent = false)
                            }
                        }
                    }
            }.toResponse()
        }

    suspend fun backfillBeneficiaryCreatedBatch(request: BackFillBeneficiaryCreatedBatch): Response =
        withBackfillEnvironment {
            logger.info("backfillBeneficiaryCreatedBatch",
                "BeneficiaryId" to request.beneficiaries.map { it.beneficiaryId }).success()
                .map { request.beneficiaries.map { it.beneficiaryId } }
                .flatMap { beneficiaryService.findByIds(it) }
                .flatMapEach { beneficiary ->
                    beneficiary
                        .toBeneficiaryCreatedEvent(request.beneficiaries.first { it.beneficiaryId == beneficiary.id })
                        .produceToKafka()
                }
                .then { logger.info("Backfill finished successfully") }
                .thenError { logger.error("Backfill finished with errors", it) }
                .foldResponse()
        }

    suspend fun backFillBeneficiaryMember(request: BackFillBeneficiaryMemberRequest): Response =
        withBackfillEnvironment {
            logger.info("Start BenficiaryMember backfill", "cnpjs" to request.cnpjs).success()
                .flatMap { updateBeneficiaryAndMember(request) }
                .then { logger.info("BenficiaryMember backfill finished successfully", "total_success" to it.size) }
                .thenError { logger.error("BenficiaryMember backfill finished with errors", it) }
                .foldResponse()
        }

    suspend fun executeUpdateCassiExpirationDateRoutine(defaultPaginatedRequest: DefaultPaginatedRequest): Response =
        withBackfillEnvironment {

            logger.info("Start executeUpdateCassiExpirationDateRoutine backfill").success()
                .flatMap {
                    beneficiaryService.updateCassiMembershipByExpirationDate(
                        defaultPaginatedRequest.limit,
                        defaultPaginatedRequest.offset
                    )
                }
                .then { logger.info("executeUpdateCassiExpirationDateRoutine backfill finished successfully") }
                .thenError { logger.error("executeUpdateCassiExpirationDateRoutine backfill finished with errors", it) }
                .foldResponse()

        }

    private suspend fun updateBeneficiaryAndMember(request: BackFillBeneficiaryMemberRequest): Result<List<Beneficiary>, Throwable> =
        when {
            request.beneficiaries.isNotEmpty() -> request.beneficiaries.map { updateBeneficiaryMember(it).get() }
                .success()

            request.cnpjs.isNotEmpty() -> request.cnpjs.flatMap { updateBeneficiaryMember(it) }.success()
            else -> InvalidArgumentException(
                code = "invalid_request",
                message = "Beneficiaries or cnpjs must be provided"
            ).failure()
        }

    private suspend fun updateBeneficiaryMember(cnpj: String) =
        logger.info("Updating Beneficiaries", "cnpj" to cnpj).success()
            .flatMap { companyService.findByCnpj(cnpj) }
            .flatMap { beneficiaryService.findByCompanyId(it.id) }
            .flatMapEach { updateBeneficiaryMember(BackFillBeneficiaryMember(personId = it.personId)) }
            .get()

    private suspend fun updateBeneficiaryMember(beneficiaryMember: BackFillBeneficiaryMember) =
        logger.info(
            "Updating Beneficiary",
            "person_id" to beneficiaryMember.personId,
            "member_id" to beneficiaryMember.memberId
        ).success()
            .flatMap { beneficiaryService.findByPersonId(beneficiaryMember.personId) }
            .mapPair { beneficiaryMember.memberId ?: memberService.getCurrent(it.personId).get().id }
            .flatMap { (memberId, beneficiary) -> beneficiaryService.update(beneficiary.copy(memberId = memberId)) }
            .then {
                logger.info(
                    "Success updating Beneficiary",
                    "person_id" to beneficiaryMember.personId,
                    "member_id" to beneficiaryMember.memberId
                ).success()
            }
            .thenError {
                logger.info(
                    "Error updating Beneficiary",
                    "person_id" to beneficiaryMember.personId,
                    "member_id" to beneficiaryMember.memberId,
                    it
                ); it
            }

    private suspend fun getBeneficiaryAndMember(request: BackFillOnboardingRequest) =
        companyService.findByCnpjs(request.cnpjs)
            .flatMapEach { beneficiaryService.findByCompanyId(it.id, FindOptions(withOnboarding = true)) }
            .map { it.flatten() }
            .flatMap { getBeneficiaryAndMember(it, request) }

    private suspend fun getBeneficiaryAndMember(beneficiaries: List<Beneficiary>, request: BackFillOnboardingRequest) =
        beneficiaries
            .filter { it.onboarding?.currentPhase?.phase == request.currentPhaseType }
            .let { filteredBeneficiaries ->
                getMembersMap(
                    filteredBeneficiaries.map { it.personId },
                    request.memberStatus
                )
            }
            .map { membersMap ->
                beneficiaries.mapNotNull {
                    it.toBeneficiaryAndMember(membersMap[it.personId], request.toPhaseType, request.flowType)
                }
            }

    private suspend fun getMembersMap(personIds: List<PersonId>, status: MemberStatus) =
        memberService.findByPersonIds(personIds)
            .map { members -> members.groupBy { it.personId }.getMaxOfPerson() }
            .map { membersMap -> membersMap.filter { it.value.status == status } }

    private fun Map<PersonId, List<Member>>.getMaxOfPerson() = this.mapNotNull { entry ->
        entry.value.maxByOrNull { it.activationDate ?: LocalDateTime.MIN }?.let { entry.key to it }
    }.toMap()

    private suspend fun MoveToPhaseEvent.produceToKafka() =
        coResultOf<MoveToPhaseEvent, Throwable> {
            kafkaProducerService.produce(this, this.payload.beneficiaryId.toString())
            this
        }

    private suspend fun BeneficiaryCreatedEvent.produceToKafka() =
        coResultOf<BeneficiaryCreatedEvent, Throwable> {
            kafkaProducerService.produce(this, this.payload.beneficiary.id.toString())
            this
        }

    private fun Beneficiary.toBeneficiaryAndMember(
        member: Member?,
        phaseType: BeneficiaryOnboardingPhaseType,
        flowType: BeneficiaryOnboardingFlowType
    ) = member?.let { BeneficiaryAndMember(this, it, phaseType, flowType) }


    private fun Beneficiary.toBeneficiaryCreatedEvent(request: BackFillBeneficiaryCreated) =
        BeneficiaryCreatedEvent(
            beneficiary = this,
            initialProductId = request.initialProductId!!,
            flowType = request.flowType,
        )

    private fun BeneficiaryAndMember.toMoveToPhaseEvent() =
        MoveToPhaseEvent(
            beneficiaryId = this.beneficiary.id,
            requiredPhaseType = this.phaseType,
        )

    private fun Beneficiary.toBeneficiaryCreatedEvent(request: BackFillBeneficiaryCreatedWithPhase) =
        BeneficiaryCreatedEvent(
            beneficiary = this,
            initialProductId = request.initialProductId!!,
            flowType = request.flowType,
            phase = request.currentPhase
        )

    suspend fun resetBeneficiaryRiskFlow(request: BackFillBeneficiaryRiskFlowRequest): Response =
        withBackfillEnvironment {
            logger.info("Starting backfill to reset beneficiary risk flow")

            beneficiaryOnboardingService.resetBeneficiaryOnboardingWithFlowType(request.beneficiaryId, request.riskFlow)
                .then { logger.info("Backfill finished successfully - reset beneficiary risk flow", request) }
                .thenError { logger.error("Backfill finished with errors", it) }
                .foldResponse()
        }

    suspend fun createMemberContractToMemberWithoutContract(request: BackFillCreateMemberContractToMemberWithoutContract): Response =
        withBackfillEnvironment {
            memberService.get(request.memberId)
                .flatMapPair { beneficiaryService.findByMemberId(it.id) }
                .map { (beneficiary, member) -> contractGenerator.generateContractB2B(member, beneficiary) }
                .foldResponse()
        }

    suspend fun createCassiMemberFromExistentOne(request: BackfillCassiMemberRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Start backfill to copy existent cassi member from existent one",
                "member_ids" to request.ids,
                "total" to request.ids.size
            )

            memberService.findByIds(request.ids)
                .mapPair { fetchMembershipFromOldMembership(it) }
                .flatMap { (newMemberships, oldMemberships) ->
                    fetchProductsFromMembership(newMemberships)
                        .mapPair { collectMembershipToUpdate(newMemberships, it) }
                        .flatMap { (membershipToUpdate, products) ->
                            copyCassiMember(oldMemberships, membershipToUpdate, products)
                        }
                }.foldResponse()

        }

    private suspend fun fetchMembershipFromOldMembership(oldMemberships: List<Member>) = oldMemberships
        .success()
        .map { members -> members.map { it.personId } }
        .flatMap { memberService.findByPersonIds(it) }
        .then { members ->
            logger.info(
                "Memberships found by older one",
                "member_ids" to members.map { it.id },
                "total" to members.size,
            )
        }
        .get()

    private suspend fun fetchProductsFromMembership(memberships: List<Member>) = memberships
        .success()
        .map { members -> members.map { it.productId }.distinct() }
        .flatMap { productService.findByIds(it) }
        .then { products ->
            logger.info(
                "Products found by member",
                "products_id" to products.map { it.id },
                "total" to products.size,
                "has_national_coverage_total" to products.filter { it.hasNationalCoverage }.size,
                "does_not_has_national_coverage_total" to products.filterNot { it.hasNationalCoverage }.size,
            )
        }
        .map { products -> products.associateBy { it.id } }

    private fun collectMembershipToUpdate(memberships: List<Member>, products: Map<UUID, Product>) = memberships
        .success()
        .map { members ->
            members.fold(listOf<Member>()) { acc, item ->
                if (products.getValue(item.productId).hasNationalCoverage) acc + item
                else acc
            }
        }.then { members ->
            logger.info(
                "Memberships to update",
                "members_id" to members.map { it.id },
                "total" to members.size
            )
        }.get()

    private suspend fun copyCassiMember(
        oldMemberships: List<Member>,
        newMemberships: List<Member>,
        products: Map<UUID, Product>
    ) = newMemberships
        .success()
        .mapPair { oldMemberships.associateBy { it.personId } }
        .flatMap { (associatedOldMembership, members) ->
            members.pmap {
                cassiMemberService.copyOrCreateCassiMember(
                    associatedOldMembership.getValue(it.personId).id,
                    it.id,
                    products.getValue(it.productId),
                )
            }.lift()
        }

    suspend fun createCassiMemberFromMemberWithoutOne(request: BackfillCassiMemberRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::createCassiMemberFromMemberWithoutOne",
                "member_ids" to request.ids,
            )

            memberService.findByIds(request.ids)
                .flatMapEach { cassiMemberService.createCassiMember(it) }
                .foldResponse()
        }

    suspend fun cancelCancellationRequest(request: CancelCancellationRequestRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::cancelCancelationRequest",
                "beneficiary_id" to request.beneficiaryId,
            )

            beneficiaryService.get(request.beneficiaryId)
                .flatMap {
                    beneficiaryService.update(
                        it.copy(
                            canceledAt = null,
                            canceledReason = null,
                            canceledDescription = null
                        )
                    )
                }
                .foldResponse()
        }

    suspend fun backfillBeneficiariesParentMember(request: RangeRequest): Response =
        withBackfillEnvironment {
            coroutineScope {
                val childBeneficiaries = beneficiaryService.findByFilters(
                    null,
                    null,
                    null,
                    null,
                    range = IntRange(request.start, request.endInclusive)
                )
                    .map { beneficiaries -> beneficiaries.filter { it.parentBeneficiary != null } }.get()
                if (childBeneficiaries.isEmpty())
                    return@coroutineScope true.toResponse()

                val parentMembersMapDeferred = async {
                    val parentBeneficiaries =
                        beneficiaryService.findByIds(childBeneficiaries.map { it.parentBeneficiary!! }).get()

                    val parentMembers = memberService.findByIds(parentBeneficiaries.map { it.memberId }).get()

                    parentBeneficiaries.associateBy({ it.id }, { beneficiary ->
                        parentMembers.first { it.id == beneficiary.memberId }
                    })
                }
                val childMembersDeferred =
                    async {
                        memberService.findByIds(childBeneficiaries.map { it.memberId }).get().associateBy { it.id }
                    }

                val parentMembersMap = parentMembersMapDeferred.await()
                val childMembers = childMembersDeferred.await()

                childBeneficiaries.pmap {
                    memberService.update(
                        childMembers.getValue(it.memberId).copy(
                            parentMember = parentMembersMap.getValue(it.parentBeneficiary!!).id,
                            parentPerson = parentMembersMap.getValue(it.parentBeneficiary!!).personId,
                        )
                    )
                }
                true.toResponse()
            }
        }

    suspend fun createSubcontractForCompany(request: IdsRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::createSubcontractForCompany",
                "company_ids" to request.ids,
            )

            companyService.findByIds(request.ids)
                .pmapEach { company ->
                    logger.info("company", "id" to company.id)
                    kafkaProducerService.produce(RequestCreateSubcontractFromCompanyEvent(company))
                }
                .foldResponse()

        }

    suspend fun updateCompanyBillingAccountableParty(request: UpdateCompanyBillingAccountablePartyRequest): Response =
        withBackfillEnvironment {
            coroutineScope {

                val companiesDeferred = async {
                    companyService.findByCnpjs(request.companyCnpjs)
                }
                val billingsDeferred = async {
                    billingAccountablePartyService.findByNationalIds(request.companyCnpjs)
                }

                val companies = companiesDeferred.await().get()
                val billings = billingsDeferred.await().get().associateBy { it.nationalId }

                companies.pmap { companyService.update(it.copy(billingAccountablePartyId = billings.getValue(it.cnpj).id)) }
            }.toResponse()
        }

    suspend fun forceMemberActivation(request: ForceMemberActivationRequest): Response =
        withBackfillEnvironment {
            beneficiaryService.get(request.beneficiaryId)
                .flatMap { memberService.get(it.memberId) }
                .flatMap { memberService.activateMember(it) }
                .foldResponse()
        }

    suspend fun deleteContract(request: DeleteContractRequest): Response =
        withBackfillEnvironment {

            companyContractService.get(request.id).then { contract ->

                companySubContractService.findByContractId(contract.id).then { companySubContracts ->
                    companySubContracts.forEach { companySubContract ->
                        beneficiaryService.findByCompanySubContractId(companySubContract.id).then { beneficiaries ->
                            val beneficiariesToUpdate = beneficiaries.map { beneficiary ->

                                logger.info(
                                    "backfill - deleteContract::update company subcontract id in beneficiary to null",
                                    "beneficiary_id" to beneficiary.id,
                                    "member_id" to beneficiary.memberId
                                )

                                beneficiary.copy(companySubContractId = null)
                            }

                            beneficiaryService.updateInBatch(beneficiariesToUpdate)

                        }

                        logger.info(
                            "backfill - deleteContract::delete companySubContract",
                            "company_sub_contract_id" to companySubContract.id,
                        )

                        companySubContractService.delete(companySubContract)
                    }
                }

                companyService.findByContractId(contract.id).pmapEach { company ->

                    logger.info(
                        "backfill - deleteContract::update contractId in company to null",
                        "company_id" to company.id,
                    )

                    val contractIds = company.contractIds.filterNot { it == contract.id }
                    companyService.update(company.copy(contractIds = contractIds))

                }

                logger.info(
                    "backfill - deleteContract::delete companyContract",
                    "company_contract_id" to contract.id,
                )

                companyContractService.delete(contract)
            }

            true.toResponse()
        }

    suspend fun deleteSubcontract(request: DeleteSubcontractRequest): Response =
        withBackfillEnvironment {
            companySubContractService.get(request.id).map { companySubContract ->
                beneficiaryService.findByCompanySubContractId(companySubContract.id).then { beneficiaries ->
                    val beneficiariesToUpdate = beneficiaries.map { beneficiary ->

                        logger.info(
                            "backfill - deleteSubcontract::update company subcontract id in beneficiary to null",
                            "beneficiary_id" to beneficiary.id,
                            "member_id" to beneficiary.memberId
                        )

                        beneficiary.copy(companySubContractId = request.optionalSubcontractId)
                    }

                    beneficiaryService.updateInBatch(beneficiariesToUpdate)

                }

                logger.info(
                    "backfill - deleteSubcontract::delete companySubContract",
                    "company_sub_contract_id" to companySubContract.id,
                )

                companySubContractService.delete(companySubContract)

                true
            }.foldResponse()

        }

    suspend fun deleteCompanyProductPriceListingById(
        request: DeleteCompanyProductPriceListingByIdRequest
    ): Response = withBackfillEnvironment {
        logger.info(
            "Start BackFill to delete company product price listing by id",
            "cppl_ids" to request.ids,
        )

        val cppls = companyProductPriceListingService.findByIds(request.ids).get()

        cppls.forEach { cppl -> companyProductPriceListingService.delete(cppl) }

        logger.info(
            "end BackFill to delete company product price listing by id",
            "cppl_ids" to request.ids,
        )

        true.toResponse()
    }

    suspend fun deleteCompanyProductPriceListingByCompanyId(
        request: DeleteCompanyProductPriceListingByCompanyIdRequest
    ): Response = withBackfillEnvironment {
        logger.info(
            "Start BackFill to delete company product price listing by company dd",
            "company_ids" to request.ids,
        )

        companySubContractService.findByCompanyIds(request.ids).flatMap { companySubContracts ->
            companyProductPriceListingService.findCurrentBySubContractIds(companySubContracts.map { it.id })
                .flatMap { cppls ->
                    cppls.forEach { (key, values) ->
                        values.forEach {
                            companyProductPriceListingService.delete(it)
                        }
                    }
                    true.success()
                }
        }

        logger.info(
            "end BackFill to delete company product price listing by company dd",
            "company_ids" to request.ids,
        )

        true.toResponse()
    }

    suspend fun deleteB2bDataFromB2cMember(request: DeleteB2bDataFromB2cMemberRequest): Response =
        withBackfillEnvironment {

            val response = mutableListOf<DeleteB2bDataFromB2cMemberResponse>()

            memberService.findByIds(request.ids).then { members ->
                members.forEach { member ->
                    if (member.selectedProduct.type == ProductType.B2C) {
                        beneficiaryService.findByMemberId(memberId = member.id).then { beneficiary ->
                            beneficiaryOnboardingService.findByBeneficiaryId(beneficiary.id)
                                .then { beneficiaryOnboarding ->
                                    beneficiaryOnboardingService.findAllPhasesFromBeneficiaryOnboardingId(
                                        beneficiaryOnboarding.id
                                    ).flatMapEach {

                                        logger.info(
                                            "backfill - deleteB2bDataFromB2cMember::delete beneficiaryOnboardingPhase",
                                            "beneficiary_onboarding_phase_id" to it.id,
                                        )

                                        beneficiaryOnboardingService.deleteBeneficiaryOnboardingPhase(it)
                                    }

                                    logger.info(
                                        "backfill - deleteB2bDataFromB2cMember::delete beneficiaryOnboarding",
                                        "beneficiary_onboarding_id" to beneficiaryOnboarding.id,
                                    )

                                    beneficiaryOnboardingService.deleteBeneficiaryOnboarding(beneficiaryOnboarding)
                                    beneficiaryService.delete(beneficiary)

                                    response.add(
                                        DeleteB2bDataFromB2cMemberResponse(
                                            id = member.id,
                                            status = "SUCCESS",
                                            observation = "delete beneficiary with success"
                                        )
                                    )
                                }
                        }
                    } else {
                        response.add(
                            DeleteB2bDataFromB2cMemberResponse(
                                id = member.id,
                                status = "CANCELED",
                                observation = "member is not B2C"
                            )
                        )
                    }
                }
            }

            response.toResponse()
        }

    private suspend fun findBeneficiariesByRequest(request: RequestBeneficiaryParentAndStatusUpdate) =
        request.let {
            if (it.beneficiaryIds != null)
                beneficiaryService.findByIds(it.beneficiaryIds)
            else if (it.start != null && it.endInclusive != null)
                beneficiaryService.findByFilters(null, null, null, null, IntRange(it.start, it.endInclusive))
            else BadRequestException("Required params missing").failure()
        }

    private suspend fun List<Beneficiary>.updateMemberStatus() =
        memberService.findByIds(this.map { it.memberId })
            .map { members -> members.associateBy { it.id } }
            .map { mapMember -> this.map { it.copy(memberStatus = mapMember[it.memberId]?.status) } }

    private suspend fun List<Beneficiary>.updateParentPerson() =
        beneficiaryService.findByIds(this.mapNotNull { it.parentBeneficiary })
            .map { parentBeneficiaries -> parentBeneficiaries.associateBy { it.id } }
            .map { mapParent ->
                this.map {
                    it.copy(
                        parentPerson = mapParent.getOrDefault(it.parentBeneficiary, null)?.personId
                    )
                }
            }

    suspend fun updateBeneficiaryParentAndStatusFields(request: RequestBeneficiaryParentAndStatusUpdate): Response =
        withBackfillEnvironment {
            logger.info(
                "Start backFill to update parent and status field of beneficiaries"
            )
            findBeneficiariesByRequest(request)
                .flatMap { it.updateMemberStatus() }
                .flatMap { it.updateParentPerson() }
                .flatMap { beneficiaryService.updateInBatch(it) }
                .toResponse()
        }

    suspend fun updateCpplOnSubcontracts(request: UpdateCpplOnSubcontractsRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Start backFill to update cppl on subcontracts", "request" to request
            )
            findSubcontractsByRequest(request)
                .pmapEach { companySubContract ->
                    companyProductPriceListingService.findCurrentBySubContractId(companySubContract.id)
                        .flatMapPair { currentCppl ->
                            val subcontractToUpdate = companySubContract.copy(
                                avaliableCompanyProductPriceListing = currentCppl.map { it.id }
                            )
                            companySubContractService.update(subcontractToUpdate)
                        }.then { (companySubContract, currentCppl) ->
                            logger.info(
                                "CompanySubContract updated successfully",
                                "company_sub_contract_id" to companySubContract.id,
                                "company_price_listings" to currentCppl.map { it.id })
                        }
                }
                .toResponse()
        }

    private suspend fun findSubcontractsByRequest(request: UpdateCpplOnSubcontractsRequest) =
        request.let {
            if (it.subcontractIds != null)
                companySubContractService.findByIds(it.subcontractIds)
            else if (it.start != null && it.endInclusive != null)
                companySubContractService.findByRange(IntRange(it.start, it.endInclusive))
            else BadRequestException("Required params missing").failure()
        }

    suspend fun moveBeneficiaryCompany(request: MoveBeneficiaryCompanyRequest): Response = withBackfillEnvironment {
        companyService.findByCnpjs(listOf(request.from, request.to))
            .flatMap { companies ->
                val fromCompany = companies.getByCnpj(request.from)
                val toCompany = companies.getByCnpj(request.to)

                beneficiaryService.findByCompanyId(fromCompany.id)
                    .pmapEach { beneficiary ->
                        beneficiaryService.update(
                            beneficiary.copy(companyId = toCompany.id)
                        ).get()
                    }
            }.foldResponse()
    }

    suspend fun migrateCompanyProductsToCompanyProductPriceListing(request: MigrateCompanyProductsToCompanyProductPriceListRequest): Response =
        withBackfillEnvironment {
            coroutineScope {
                val companies = companyService.findByIds(request.companyIds).get()
                val productIds = companies.flatMap { it.availableProducts ?: emptyList() }.distinct()
                val productPriceListingsDeferred = async {
                    productPriceListingService.getCurrents(productIds).get()
                        .associateBy { it.productId }
                }
                val productsDeferred =
                    async {
                        productService.findByIds(productIds, ProductService.FindOptions(withPriceListing = false)).get()
                            .associateBy { it.id }
                    }
                val subcontractsDeferred = async {
                    companySubContractService.findByCompanyIds(companies.map { it.id }).get()
                        .groupBy { it.companyId }
                }
                val subcontracts = subcontractsDeferred.await()
                val products = productsDeferred.await()
                val productPriceListing = productPriceListingsDeferred.await()
                val priceListingMap =
                    priceListingService.getList(productPriceListing.values.map { it.priceListingId }.distinct()).get()
                        .associateBy { it.id }
                companies.pmap { company ->
                    runCatching {
                        val companySubcontracts = subcontracts.getValue(company.id)
                        companySubcontracts.map { subcontract ->

                            val cppl = company.toCompanyProductPriceListing(
                                subcontract,
                                products,
                                productPriceListing,
                                priceListingMap
                            )
                            val currentCompanyProductPriceListings =
                                if (subcontract.avaliableCompanyProductPriceListing.isNotNullOrEmpty()) {
                                    companyProductPriceListingService.findCurrentBySubContractId(subcontract.id).get()
                                } else {
                                    emptyList()
                                }
                            val currentAnsNumbers = currentCompanyProductPriceListings.map { it.ansNumber }
                            val cpplToCreate = cppl.filterNot { currentAnsNumbers.contains(it.ansNumber) }

                            companyProductPriceListingService.addListToSubContract(
                                cpplToCreate,
                                subcontract,
                                request.sendEvent
                            )
                                .map {
                                    logger.info(
                                        "CompanyProductPriceListing created successfully",
                                        "company_id" to company.id
                                    )
                                    MigrateCompanyProductsToCompanyProductPriceListResponse(
                                        companyId = company.id,
                                        success = true
                                    )
                                }.get()
                        }
                    }.getOrElse { ex ->
                        logger.error("Error migrating CompanyProductPriceListing on companyId: ${company.id}", ex)
                        listOf(
                            MigrateCompanyProductsToCompanyProductPriceListResponse(
                                companyId = company.id,
                                success = false,
                                reason = ex.message
                            )
                        )
                    }!!
                }
                    .flatten()
                    .toResponse()
            }
        }

    private fun getGracePeriod(
        contract: CompanyContract? = null,
        company: Company
    ): Pair<GracePeriodType, GracePeriodTypeReason> {
        val type = TOTAL_EXEMPTION

        val reason =
            if (contract?.groupCompany in listOf("0004", "0005", "0006") || company.defaultFlowType == NO_RISK_FLOW) {
                GracePeriodTypeReason.MLA
            } else {
                GracePeriodTypeReason.PORTABILITY
            }
        return type to reason
    }

    suspend fun backfillGracePeriodType(request: GracePeriodRequest) = withBackfillEnvironment {
        coroutineScope {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            val beneficiaryIds = beneficiaryOnboardingDataService.find {
                where { this.flowType.eq(NO_RISK_FLOW) }
                    .offset { request.page * request.perPage }
                    .limit { request.perPage }
            }.then { bo ->
                logger.info(
                    "Beneficiaries onboarding",
                    "total" to bo.map { mapOf("id" to it.id, "beneficiary_id" to it.beneficiaryId) })
            }.mapEach { it.beneficiaryId }.get()

            val beneficiaries = beneficiaryDataService.find {
                where { this.id.inList(beneficiaryIds) and this.gracePeriodType.eq(TOTAL_GRACE_PERIOD) }
            }
                .map { b ->
                    b.ifEmpty {
                        return@coroutineScope Response(
                            HttpStatusCode.OK,
                            "Nenhum registro de beneficiário encontrado"
                        )
                    }
                }
                .then { b ->
                    logger.info("Beneficiaries", "total" to b.map { it.id })
                }

            val companiesDeferred = async {
                beneficiaries.flatMap {
                    val companyIds = it.map { beneficiary -> beneficiary.companyId }.distinct()

                    companyService.findByIds(companyIds)
                }.map { companies -> companies.associateBy { it.id } }.get()
            }

            val subcontractsDeferred = async {
                beneficiaries.flatMap {
                    val subcontractIds = it.mapNotNull { beneficiary -> beneficiary.companySubContractId }.distinct()

                    companySubContractService.findByIds(subcontractIds)
                }.map { subcontracts -> subcontracts.associateBy { it.id } }.get()
            }

            val companies = companiesDeferred.await()
            val subcontracts = subcontractsDeferred.await()

            val contracts = companies.values
                .map { company ->
                    company.contractIds
                }.flatten().distinct().let {
                    companyContractService.findByIds(it)
                }.map { contracts -> contracts.associateBy { it.id } }.get()

            val memberMap =
                memberService.findByIds(beneficiaries.get().map { it.memberId }).get().associateBy { it.id }

            beneficiaries.pmapEach { beneficiary ->


                val company = companies.getValue(beneficiary.companyId)
                val contract = beneficiary.companySubContractId?.let { subcontractId ->
                    subcontracts[subcontractId]?.let {
                        contracts[it.contractId]
                    }
                }

                val (gracePeriodType, gracePeriodTypeReason) = getGracePeriod(contract, company)
                logger.info(
                    "Beneficiary", "id" to beneficiary.id, "gracePeriodType" to gracePeriodType,
                    "gracePeriodTypeReason" to gracePeriodTypeReason
                )

                beneficiaryService.update(
                    beneficiary.copy(
                        gracePeriodType = gracePeriodType,
                        gracePeriodTypeReason = gracePeriodTypeReason
                    ).toTransport()
                )
                    .then {
                        logger.info("SUCESSSS", "id" to it.id)
                        successCount.incrementAndGet()
                    }.thenError {
                        logger.info("ERROR", "id" to beneficiary.id)
                        errors[beneficiary.id.toString()] = it.message ?: "ERROR"
                        errorsCount.incrementAndGet()
                    }
                    .map {
                        val member = memberMap[beneficiary.memberId]

                        if (member?.active == true) {
                            kafkaProducerService.produce(
                                NullvsSyncMemberRequestEvent(
                                    memberMap.getValue(beneficiary.memberId),
                                    NullvsActionType.UPDATE
                                )
                            )
                        }
                    }
            }.thenError {
                logger.error("Error", it)

                errors["main_error"] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }

            Response(
                HttpStatusCode.OK,
                mapOf(
                    "successCount" to successCount.get(),
                    "errorsCount" to errorsCount.get(),
                    "errors" to errors
                )
            )
        }
    }

    suspend fun updateGracePeriodTypeAndSync(request: UpdateGracePeriodTypeRequest) = withBackfillEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        val beneficiaryGracePeriodTypeMap = request.items.associateBy { it.beneficiaryId }

        val beneficiaries = beneficiaryService.findByIds(request.items.map { it.beneficiaryId }).get()

        val memberMap = memberService.findByIds(beneficiaries.map { it.memberId }).get().associateBy { it.id }

        beneficiaries.pmap { beneficiary ->
            val item = beneficiaryGracePeriodTypeMap.getValue(beneficiary.id)

            beneficiaryService.update(
                beneficiary.copy(
                    gracePeriodType = item.gracePeriodType,
                    gracePeriodTypeReason = item.gracePeriodTypeReason ?: beneficiary.gracePeriodTypeReason,
                    gracePeriodBaseDate = item.gracePeriodBaseDate?.let { it.toLocalDate() }
                        ?: beneficiary.gracePeriodBaseDate,
                )
            ).map {
                kafkaProducerService.produce(
                    NullvsSyncMemberRequestEvent(memberMap.getValue(beneficiary.memberId), NullvsActionType.UPDATE)
                )
            }.then {
                successCount.incrementAndGet()
            }.thenError {
                errors[beneficiary.id.toString()] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }
        }

        Response(
            HttpStatusCode.OK,
            mapOf(
                "successCount" to successCount.get(),
                "errorsCount" to errorsCount.get(),
                "errors" to errors
            )
        )
    }

    suspend fun updateCompanyAndContractStartedAt(request: UpdateCompanyAndContractStartedAtRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()
            val cnpjs = request.items.map { it.cnpj }

            companyService.findByCnpjs(cnpjs).flatMapPair { companies ->
                val contractIds = companies.mapNotNull { it.contractIds }.flatten()
                companyContractService.findByIds(contractIds)
            }.map { (contracts, companies) ->
                val contractById = contracts.associateBy { it.id }
                val dateByCnpj = request.items.associateBy({ it.cnpj }, { it.date })
                companies.pmap { company ->
                    company.contractIds.pmap { contractId ->
                        val contract = contractById.getValue(contractId)
                        val dateToUpdate = dateByCnpj.getValue(company.cnpj)
                        companyService.update(company.copy(contractStartedAt = dateToUpdate.atStartOfDay())).flatMap {
                            companyContractService.update(
                                contract.copy(startedAt = dateToUpdate),
                                sendEvent = request.sendEvent
                            )
                        }.then {
                            successCount.incrementAndGet()
                        }.thenError {
                            logger.error("Error updating company and contract startedAt", it)
                            errors[company.cnpj] = it.message ?: "ERROR"
                            errorsCount.incrementAndGet()
                        }
                    }.lift()
                }.lift()
            }

            Response(
                HttpStatusCode.OK,
                mapOf(
                    "successCount" to successCount.get(),
                    "errorsCount" to errorsCount.get(),
                    "errors" to errors
                )
            )
        }

    suspend fun updateCompanySizeInCompany(request: CompanySizeBackfillRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to update the company size field")

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        request.companies.forEach {
            companyDataService
                .get(it.companyId)
                .map { company -> company.copy(companySize = it.companySize) }
                .flatMap { company -> companyService.update(company.toTransport()) }
                .fold(
                    { successCount.incrementAndGet() },
                    { error ->
                        errors[it.companyId.toString()] = error.message ?: "ERROR"
                        errorsCount.incrementAndGet()
                    }
                )
        }

        Response(
            HttpStatusCode.OK,
            mapOf(
                "successCount" to successCount.get(),
                "errorsCount" to errorsCount.get(),
                "errors" to errors
            )
        )
    }

    suspend fun updateCompanySubcontractsNatureAndBillingGroup(request: UpdateCompanySubcontractNatureRequest): Response =
        withBackfillEnvironment {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            val itemsBySubcontractId = request.items.associateBy { it.subcontractId }

            val subcontractIds = itemsBySubcontractId.keys.toList()

            val companySubcontracts = companySubContractService.findByIds(subcontractIds).get()

            companySubcontracts.pmap { companySubcontract ->
                val item = itemsBySubcontractId.getValue(companySubcontract.id)
                companySubContractService.update(
                    companySubcontract.copy(nature = item.nature, billingGroup = item.billingGroup),
                    sendEvent = request.sendEvent
                ).then {
                    successCount.incrementAndGet()
                }.thenError {
                    logger.error("Error updating subcontract nature and billing group", it)
                    errors[companySubcontract.id.toString()] = it.message ?: "ERROR"
                    errorsCount.incrementAndGet()
                }
            }

            Response(
                HttpStatusCode.OK,
                mapOf(
                    "successCount" to successCount.get(),
                    "errorsCount" to errorsCount.get(),
                    "errors" to errors
                )
            )
        }

    suspend fun moveBeneficiaryToPhase(request: MoveBeneficiaryToPhaseRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to move beneficiary to some phase")

        beneficiaryService.findByIds(request.beneficiaryIds, findOptions = FindOptions(withOnboarding = true))
            .mapEach { beneficiary ->
                val maxStep = request.maxStep?.let {
                    beneficiary.onboarding?.let {
                        it.currentPhase?.stepsForPhase(
                            it.flowType,
                            request.phase,
                        )
                    }
                } ?: 1

                logger.info(
                    "Move beneficiary phase",
                    "beneficiary_id" to beneficiary.id,
                    "onboarding_type" to beneficiary.onboarding?.flowType,
                    "current_phase" to beneficiary.onboarding?.currentPhase,
                    "target_phase" to request.phase,
                    "max_step" to maxStep,
                )

                beneficiaryOnboardingService.moveToPhase(beneficiary.id, request.phase, LocalDateTime.now(), maxStep)
            }.toResponse()
    }

    suspend fun backfillItemsCppl(request: UpdatePriceListItemsRequest) = withBackfillEnvironment {
        val cppls = companyProductPriceListingService.findByIds(request.items.map { it.cpplId }).get()
        val priceListings =
            priceListingService.getList(request.items.map { it.priceListingId }).get().associateBy { it.id }
        val cpplPriceListMap = request.items.associateBy { it.cpplId }
        cppls.pmap {
            companyProductPriceListingService.update(
                it.copy(
                    priceListItems = priceListings.getValue(cpplPriceListMap.getValue(it.id).priceListingId).items
                ),
            )
        }.toResponse()
    }

    suspend fun updateBeneficiaryParentPerson(request: UpdateBeneficiaryParentPersonRequest) = withBackfillEnvironment {
        beneficiaryService.get(request.beneficiaryId).flatMap { beneficiary ->
            beneficiaryService.update(
                beneficiary.copy(parentPerson = request.parentPersonId)
            )
        }.foldResponse()
    }

    suspend fun updateMemberParentInfo(request: UpdateMemberParentInfoRequest) = withBackfillEnvironment {
        memberService.get(request.memberId).flatMap { member ->
            memberService.update(
                member.copy(parentPerson = request.parentPersonId, parentMember = request.parentMemberId)
            )
        }.foldResponse()
    }

    suspend fun updateContractIdOfSubcontract(request: UpdateContractIdOfSubcontractRequest) = withBackfillEnvironment {
        companySubContractService.get(request.subcontractId).flatMap {
            require(it.externalId == null) { "Subcontract already has synced with totvs" }
            companySubContractService.update(it.copy(contractId = request.contractId))
        }.foldResponse()
    }


    private fun Company.toCompanyProductPriceListing(
        subcontract: CompanySubContract,
        products: Map<UUID, Product>,
        productPriceListings: Map<UUID, ProductPriceListing>,
        priceListingMap: Map<UUID, PriceListing>
    ): List<CompanyProductPriceListing> {
        require(this.availableProducts != null) { "Company availableProducts is null" }
        val companyProducts = this.availableProducts!!.distinct().map { products.getValue(it) }
        require(
            companyProducts.map { it.ansNumber!! }.hasDuplicates().not()
        ) { "Company availableProducts has products with duplicated ansNumber" }
        return companyProducts
            .filter { it.active && productPriceListings[it.id] != null }
            .map { product ->
                val productPriceListing = productPriceListings.getValue(product.id)
                CompanyProductPriceListing(
                    companyId = this.id,
                    companySubContractId = subcontract.id,
                    productId = product.id,
                    priceListingId = null,
                    ansNumber = product.ansNumber!!,
                    startDate = productPriceListing.startDate.toLocalDate(),
                    priceListItems = priceListingMap.getValue(productPriceListing.priceListingId).items
                )
            }
    }

    private fun <T> List<T>.hasDuplicates(): Boolean {
        val originalLength = this.size
        return this.distinct().size != originalLength
    }

    private fun List<Company>.getByCnpj(cnpj: String) =
        find { it.cnpj == cnpj } ?: throw NotFoundException("The company $cnpj is not found")

    suspend fun getTotalEmployeesFromHubspot(company: Company): Int? {
        logger.info(
            "BackfillController::updateCompanySize - searching for company in hubspot",
            "cnpj" to company.cnpj,
            "company_id" to company.id
        )
        val hubspotDeals = businessSalesCrmPipeline.searchDealByCnpj(company.cnpj).results

        logger.info(
            "BackfillController::updateCompanySize - deals found",
            "size" to hubspotDeals.size,
            "results" to hubspotDeals
        )
        val deal = hubspotDeals.firstOrNull()

        return deal?.properties?.b2b_total_de_funcionarios?.toIntOrNull()
    }


    suspend fun sanitizeAnsNumber(productIds: IdsRequest) = withBackfillEnvironment {
        productService.findByIds(productIds.ids)
            .pmapEach { productService.update(it.copy(ansNumber = it.ansNumber?.onlyNumbers())) }
            .foldResponse()
    }

    suspend fun sanitizeCpplAnsNumber(cpplIds: IdsRequest) = withBackfillEnvironment {
        companyProductPriceListingService.findByIds(cpplIds.ids)
            .pmapEach {
                companyProductPriceListingService.update(
                    it.copy(ansNumber = it.ansNumber.onlyNumbers()),
                    sendEvent = false,
                    shouldUpdateTotvsMemberPriceListing = false
                )
            }
            .foldResponse()
    }

    suspend fun updateOrCreateCppl(request: UpdateOrCreateCpplRequest) = withBackfillEnvironment {
        val subContractIds = request.items.map { it.subContractId }.distinct()
        val productIds = request.items.map { it.productId }.distinct()

        val subContractsMap = companySubContractService.findByIds(subContractIds).get().associateBy { it.id }

        val productsMap = productService.findByIds(productIds).get().associateBy { it.id }

        request.items.pmap { item ->
            val priceListItems = item.toPriceListItems()
            companyProductPriceListingService.upsertCompanyProductPriceListing(
                CompanyProductPriceListing(
                    companyId = subContractsMap.getValue(item.subContractId).companyId,
                    companySubContractId = item.subContractId,
                    productId = item.productId,
                    priceListingId = null,
                    ansNumber = productsMap.getValue(item.productId).ansNumber!!,
                    startDate = LocalDate.now(),
                    priceListItems = priceListItems
                ),
                subContractsMap.getValue(item.subContractId),
                shouldUpdateTotvsMemberPriceListing = true
            )
        }.toResponse()
    }

    suspend fun createNewDependents(request: ParentToCreateNewDependentsRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to create new dependents")

        beneficiaryService.findByIds(request.beneficiaryIds)
            .pmapEach { parentBeneficiary ->
                val expectedMemberStatuses = listOf(MemberStatus.ACTIVE, MemberStatus.PENDING)

                beneficiaryService.findByParentPerson(
                    parentBeneficiary.personId,
                    expectedMemberStatuses,
                    findOptions = FindOptions(withOnboarding = true)
                ).then {
                    logger.info(
                        "Dependents found",
                        "size" to it.size,
                        "map" to it.map { dependent -> dependent.personId to dependent.id },
                    )
                }
                    .pmapEach { beneficiaryService.updateDependentWithNewParent(it, parentBeneficiary).get() }
            }.map { beneficiaries ->
                beneficiaries.map { it.get() }
            }.toResponse()
    }

    suspend fun updateCompanyContractId(request: UpdateCompanyContractIdRequest) = withBackfillEnvironment {
        companyService.get(request.companyId).flatMap { company ->
            val contractIds = if (company.contractIds.contains(request.newContractId)) {
                company.contractIds
            } else {
                company.contractIds.plus(request.newContractId)
            }
            company.copy(contractIds = contractIds)
                .let { companyService.update(it) }
        }.foldResponse()
    }

    suspend fun updateSubcontractBlockedAt(request: UpdateSubcontractBlockedAtRequest) = withBackfillEnvironment {
        logger.info(
            "Start backfill to update the blockedAt date",
            "group_company" to request.groupCompany,
            "contract_number" to request.contractNumber,
            "subcontract_number" to request.subcontractNumber,
            "blocked_at" to request.blockedAt,
        )

        val contract =
            companyContractService.getByExternalIdAndGroupCompany(request.contractNumber, request.groupCompany).get()
        val subcontracts = companySubContractService.findByContractId(contract.id).get()

        val subcontractToUpdate = subcontracts.firstOrNull { it.externalId == request.subcontractNumber }

        if (subcontractToUpdate != null) {
            companySubContractService.update(
                subcontractToUpdate.copy(
                    blockedAt = request.blockedAt.fromTotvsFormatDateYMD(),
                    status = CompanySubContractStatus.BLOCKED,
                ),
                sendEvent = false,
            ).then {
                logger.info("Subcontract updated", "subcontract_id" to it.id)
            }

            if (subcontracts.count { it.status == CompanySubContractStatus.BLOCKED } + 1 == subcontracts.size) {
                companyContractService.update(contract.copy(status = CompanyContractStatus.BLOCKED), sendEvent = false)
                    .then {
                        logger.info("Contract updated", "contract_id" to it.id)
                    }
            }
        }

        true.toResponse()
    }

    suspend fun updateBeneficiaryRelationType(request: UpdateBeneficiaryRelationTypeRequest) = withBackfillEnvironment {
        request.beneficiaries.map {
            personService.findByNationalId(it.nationalId)
                .flatMap { person ->
                    beneficiaryService.findByPersonId(person.id)
                        .flatMap { beneficiary ->
                            beneficiaryService.update(
                                beneficiary.copy(
                                    parentBeneficiaryRelationType = ParentBeneficiaryRelationType.valueOf(
                                        it.relationType.uppercase()
                                    )
                                )
                            )
                        }
                }
        }.toResponse()
    }

    suspend fun updateWithSubcontracts(request: UpdateCompanyWithSubcontractsRequest) = withBackfillEnvironment {
        coroutineScope {

            logger.info("Starting backfill to update the company and subcontracts")

            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            val cnpjs =
                request.data.mapNotNull { if (it.company == null && it.subcontract == null && it.contract == null) null else it.companyCnpj }

            cnpjs.ifEmpty {
                return@coroutineScope Response(
                    HttpStatusCode.OK,
                    "No company was found",
                )
            }

            val companies = companyService.findByCnpjs(cnpjs).get()

            companies.ifEmpty {
                return@coroutineScope Response(
                    HttpStatusCode.OK,
                    "No company was found",
                )
            }

            logger.info(
                "Companies found:",
                "size" to companies.size,
                "companies_ids" to companies.map { mapOf("id" to it.id) },
            )

            val contractIds =
                request.data.mapNotNull { item ->
                    item.contract?.let { companies.find { company -> company.cnpj == item.companyCnpj }?.contractIds }
                }.flatten().distinct()

            val companyIdsThatNeedToBeFinding =
                request.data.mapNotNull { item ->
                    item.subcontract?.let { companies.find { company -> company.cnpj == item.companyCnpj }?.id }
                }

            logger.info("Companies ids that need to be finding:", "size" to companyIdsThatNeedToBeFinding)

            val subcontractsDef = async {
                companySubcontractDataService.find {
                    where {
                        companyId.inList(companyIdsThatNeedToBeFinding)
                    }
                }.get()
            }

            val contractsDef = async { companyContractService.findByIds(contractIds).get() }
            val subcontracts = subcontractsDef.await()
            val contracts = contractsDef.await()

            companies.pmap { company ->
                val contractsDef = async {
                    val contractsForCompany = contracts.filter { it.id in company.contractIds }

                    contractsForCompany.pmap { contract ->
                        val contractUpdateData = request.data.first { it.companyCnpj == company.cnpj }.contract!!

                        companyContractService.update(
                            contract.copy(
                                title = contractUpdateData.title ?: contract.title,
                                billingAccountablePartyId = contractUpdateData.billingAccountablePartyId
                                    ?: contract.billingAccountablePartyId,
                                startedAt = contractUpdateData.startedAt ?: contract.startedAt,
                                accountableEmail = contractUpdateData.accountableEmail
                                    ?: contract.accountableEmail,
                                contractFileIds = contractUpdateData.contractFileIds
                                    ?: contract.contractFileIds,
                                isProRata = contractUpdateData.isProRata ?: contract.isProRata,
                                defaultProductId = contractUpdateData.defaultProductId
                                    ?: contract.defaultProductId,
                                availableProducts = contractUpdateData.availableProducts
                                    ?: contract.availableProducts,
                                dueDate = contractUpdateData.dueDate ?: contract.dueDate,
                                isBillingLevel = contractUpdateData.isBillingLevel ?: contract.isBillingLevel,
                                paymentType = contractUpdateData.paymentType ?: contract.paymentType,
                                nature = contractUpdateData.nature ?: contract.nature,
                                status = contractUpdateData.status ?: contract.status,
                                integrationStatus = contractUpdateData.integrationStatus
                                    ?: contract.integrationStatus,
                                hasRetroactiveCharge = contractUpdateData.hasRetroactiveCharge
                                    ?: contract.hasRetroactiveCharge,
                                beneficiaryCountAtDayZero = contractUpdateData.beneficiaryCountAtDayZero
                                    ?: contract.beneficiaryCountAtDayZero,
                                companySize = contractUpdateData.companySize ?: contract.companySize,
                                companyBusinessUnit = contractUpdateData.companyBusinessUnit
                                    ?: contract.companyBusinessUnit
                            ),
                            sendEvent = false,
                        ).then {
                            logger.info("Contract was update successfully", "id" to it.id)
                            successCount.incrementAndGet()
                        }.flatMapError { ex ->
                            logger.info("Contract was not updated", "id" to company.id)
                            errors[contract.id.toString()] = ex.message ?: "UNKNOWN ERROR"
                            errorsCount.incrementAndGet()

                            true.success()
                        }
                    }
                }

                val companyDef = async {
                    val companyUpdateData =
                        request.data.first { it.companyCnpj == company.cnpj }.company

                    if (companyUpdateData != null) {

                        companyService.update(
                            company.copy(
                                cnpj = companyUpdateData.cnpj ?: company.cnpj,
                                name = companyUpdateData.name ?: company.name,
                                legalName = companyUpdateData.legalName ?: company.legalName,
                                email = companyUpdateData.email ?: company.email,
                                phoneNumber = companyUpdateData.phoneNumber ?: company.phoneNumber,
                                address = companyUpdateData.address ?: company.address,
                                priceAdjustmentType = companyUpdateData.priceAdjustmentType
                                    ?: company.priceAdjustmentType,
                                contractType = companyUpdateData.contractType ?: company.contractType,
                                contractStartedAt = companyUpdateData.contractStartedAt ?: company.contractStartedAt,
                                beneficiariesCountAtDayZero = companyUpdateData.beneficiariesCountAtDayZero
                                    ?: company.beneficiariesCountAtDayZero,
                                bankingInfo = companyUpdateData.bankingInfo ?: company.bankingInfo,
                                availableProducts = companyUpdateData.availableProducts ?: company.availableProducts,
                                totalEmployees = companyUpdateData.totalEmployees ?: company.totalEmployees,
                                billingAccountablePartyId = companyUpdateData.billingAccountablePartyId
                                    ?: company.billingAccountablePartyId,
                                defaultProductId = companyUpdateData.defaultProductId ?: company.defaultProductId,
                                flexBenefit = companyUpdateData.flexBenefit ?: company.flexBenefit,
                                hasEmployeesAbroad = companyUpdateData.hasEmployeesAbroad ?: company.hasEmployeesAbroad,
                                contractsUrls = companyUpdateData.contractsUrls.takeIf { it.isNotNullOrEmpty() }
                                    ?: company.contractsUrls,
                                defaultFlowType = companyUpdateData.defaultFlowType ?: company.defaultFlowType,
                                totvsContract = companyUpdateData.totvsContract ?: company.totvsContract,
                                totvsContractVersion = companyUpdateData.totvsContractVersion
                                    ?: company.totvsContractVersion,
                                totvsSubContract = companyUpdateData.totvsSubContract ?: company.totvsSubContract,
                                totvsSubContractVersion = companyUpdateData.totvsSubContractVersion
                                    ?: company.totvsSubContractVersion,
                                externalBrandId = companyUpdateData.externalBrandId ?: company.externalBrandId,
                                contractIds = companyUpdateData.contractIds.takeIf { it.isNotNullOrEmpty() }
                                    ?: company.contractIds,
                                status = companyUpdateData.status ?: company.status,
                                companySize = companyUpdateData.companySize ?: company.companySize,
                                companyBusinessUnit = companyUpdateData.companyBusinessUnit
                                    ?: company.companyBusinessUnit
                            ),
                            sendEvent = false,
                        ).then {
                            logger.info("Company was updated successfully", "id" to it.id)
                            successCount.incrementAndGet()
                        }.flatMapError { ex ->
                            logger.info("Company was not updated", "id" to company.id)
                            errors[company.id.toString()] = ex.message ?: "UNKNOWN ERROR"
                            errorsCount.incrementAndGet()

                            true.success()
                        }
                    }
                }

                val subcontractsDef = async {
                    val subcontractsForCompany = subcontracts.filter { it.companyId == company.id }

                    logger.info(
                        "Subcontracts found:",
                        "size" to subcontractsForCompany.size,
                        "subcontract_ids" to subcontractsForCompany.map { mapOf("id" to it.id) },
                    )

                    subcontractsForCompany.pmap { subcontract ->

                        val subcontractUpdateData =
                            request.data.first { it.companyCnpj == company.cnpj }.subcontract!!

                        companySubContractService.update(
                            subcontract.copy(
                                externalId = subcontractUpdateData.externalId ?: subcontract.externalId,
                                title = subcontractUpdateData.title ?: subcontract.title,
                                billingAccountablePartyId = subcontractUpdateData.billingAccountablePartyId
                                    ?: subcontract.billingAccountablePartyId,
                                isProRata = subcontractUpdateData.isProRata ?: subcontract.isProRata,
                                flexBenefit = subcontractUpdateData.flexBenefit ?: subcontract.flexBenefit,
                                hasEmployeesAbroad = subcontractUpdateData.hasEmployeesAbroad
                                    ?: subcontract.hasEmployeesAbroad,
                                defaultProductId = subcontractUpdateData.defaultProductId
                                    ?: subcontract.defaultProductId,
                                availableProducts = subcontractUpdateData.availableProducts
                                    ?: subcontract.availableProducts,
                                defaultFlowType = subcontractUpdateData.defaultFlowType
                                    ?: subcontract.defaultFlowType,
                                dueDate = subcontractUpdateData.dueDate ?: subcontract.dueDate,
                                isBillingLevel = subcontractUpdateData.isBillingLevel ?: subcontract.isBillingLevel,
                                paymentType = subcontractUpdateData.paymentType ?: subcontract.paymentType,
                                nature = subcontractUpdateData.nature ?: subcontract.nature,
                                billingGroup = subcontractUpdateData.billingGroup ?: subcontract.billingGroup,
                                avaliableCompanyProductPriceListing = subcontractUpdateData.avaliableCompanyProductPriceListing
                                    ?: subcontract.avaliableCompanyProductPriceListing,
                                status = subcontractUpdateData.status ?: subcontract.status,
                                integrationStatus = subcontractUpdateData.integrationStatus
                                    ?: subcontract.integrationStatus,
                                hasRetroactiveCharge = subcontractUpdateData.hasRetroactiveCharge
                                    ?: subcontract.hasRetroactiveCharge,
                                blockedAt = subcontractUpdateData.blockedAt ?: subcontract.blockedAt
                            ).toTransport(),
                            sendEvent = false,
                        )
                            .then {
                                logger.info("Subcontract was updated successfully", "id" to it.id)
                                successCount.incrementAndGet()
                            }.flatMapError { ex ->
                                logger.info("Subcontract was not updated", "id" to subcontract.id)
                                errors[subcontract.id.toString()] = ex.message ?: "UNKNOWN ERROR"
                                errorsCount.incrementAndGet()

                                true.success()
                            }
                    }.lift()
                }

                awaitAll(contractsDef, companyDef, subcontractsDef)
            }

            Response(
                HttpStatusCode.OK,
                mapOf(
                    "successCount" to successCount.get(),
                    "errorsCount" to errorsCount.get(),
                    "errors" to errors
                )
            )
        }
    }

    suspend fun insertStandardCost(backfillRequest: StandardCostBackfillRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to insert standard cost")

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)

        backfillRequest.items.pmap { item ->
            StandardCost(
                companyBusinessUnit = item.companyBusinessUnit,
                companySize = item.companySize,
                adhesion = item.adhesion,
                ansNumber = item.ansNumber,
                costByPersonDetail = item.costByPersonDetail
            )
        }.let {
            standardCostService.addList(it).then {
                successCount.getAndIncrement()
            }.thenError { err ->
                logger.error(
                    "Error inserting Standard Cost",
                    "error_message" to err.message
                )
                errorsCount.getAndIncrement()
            }
        }

        Response(
            HttpStatusCode.OK, BackfillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get()
            )
        )
    }

    suspend fun updateBeneficiaries(request: UpdateBeneficiariesRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to update the beneficiaries")

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        val ids =
            request.data.map { it.id }

        ids.ifEmpty {
            return@withBackfillEnvironment Response(
                HttpStatusCode.OK,
                "No beneficiaries was found",
            )
        }

        val beneficiaries = beneficiaryService.findByIds(ids).get()

        beneficiaries.ifEmpty {
            return@withBackfillEnvironment Response(
                HttpStatusCode.OK,
                "No beneficiaries was found",
            )
        }

        logger.info(
            "Beneficiary found:",
            "size" to beneficiaries.size,
            "beneficiary_ids" to beneficiaries.map { mapOf("id" to it.id) },
        )

        beneficiaries.pmap { beneficiary ->
            val beneficiaryUpdateData = request.data.first { it.id == beneficiary.id }

            beneficiaryService.update(
                beneficiary.copy(
                    parentBeneficiary = beneficiaryUpdateData.parentBeneficiary ?: beneficiary.parentBeneficiary,
                    memberId = beneficiaryUpdateData.memberId ?: beneficiary.memberId,
                    companyId = beneficiaryUpdateData.companyId ?: beneficiary.companyId,
                    type = beneficiaryUpdateData.type ?: beneficiary.type,
                    contractType = beneficiaryUpdateData.contractType ?: beneficiary.contractType,
                    parentBeneficiaryRelationType = beneficiaryUpdateData.parentBeneficiaryRelationType
                        ?: beneficiary.parentBeneficiaryRelationType,
                    activatedAt = beneficiaryUpdateData.activatedAt ?: beneficiary.activatedAt,
                    canceledAt = beneficiaryUpdateData.canceledAt ?: beneficiary.canceledAt,
                    hiredAt = beneficiaryUpdateData.hiredAt ?: beneficiary.hiredAt,
                    parentBeneficiaryRelatedAt = beneficiaryUpdateData.parentBeneficiaryRelatedAt
                        ?: beneficiary.parentBeneficiaryRelatedAt,
                    canceledReason = beneficiaryUpdateData.canceledReason ?: beneficiary.canceledReason,
                    canceledDescription = beneficiaryUpdateData.canceledDescription ?: beneficiary.canceledDescription,
                    cnpj = beneficiaryUpdateData.cnpj ?: beneficiary.cnpj,
                    hasContributed = beneficiaryUpdateData.hasContributed ?: beneficiary.hasContributed,
                    archived = beneficiaryUpdateData.archived ?: beneficiary.archived,
                    onboarding = beneficiaryUpdateData.onboarding ?: beneficiary.onboarding,
                    dependents = beneficiaryUpdateData.dependents ?: beneficiary.dependents,
                    brand = beneficiaryUpdateData.brand ?: beneficiary.brand,
                    companySubContractId = beneficiaryUpdateData.companySubContractId
                        ?: beneficiary.companySubContractId,
                    memberStatus = beneficiaryUpdateData.memberStatus ?: beneficiary.memberStatus,
                    parentPerson = beneficiaryUpdateData.parentPerson ?: beneficiary.parentPerson,
                    gracePeriodType = beneficiaryUpdateData.gracePeriodType ?: beneficiary.gracePeriodType,
                    gracePeriodTypeReason = beneficiaryUpdateData.gracePeriodTypeReason
                        ?: beneficiary.gracePeriodTypeReason,
                    gracePeriodBaseDate = beneficiaryUpdateData.gracePeriodBaseDate ?: beneficiary.gracePeriodBaseDate
                )
            ).then {
                logger.info("Beneficiary was updated successfully", "id" to it.id)
                successCount.incrementAndGet()
            }.flatMapError { ex ->
                logger.info("Beneficiary was not updated", "id" to beneficiary.id)
                errors[beneficiary.id.toString()] = ex.message ?: "UNKNOWN ERROR"
                errorsCount.incrementAndGet()

                true.success()
            }
        }

        Response(
            HttpStatusCode.OK,
            mapOf(
                "successCount" to successCount.get(),
                "errorsCount" to errorsCount.get(),
                "errors" to errors
            )
        )
    }

    suspend fun updateContractStatus(request: UpdateContractStatusRequest) =
        withBackfillEnvironment {
            logger.info("Starting backfill to update contract status", "contract_ids" to request.contractIds)
            companyContractService.findByIds(request.contractIds).flatMapEach {
                companyContractService.update(
                    it.copy(
                        status = request.status ?: it.status,
                        integrationStatus = request.integrationStatus ?: it.integrationStatus
                    ),
                    sendEvent = false
                )
            }.foldResponse()
        }

    suspend fun updateSubcontractStatus(request: UpdateSubContractStatusRequest) =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill to update the subcontract status",
                "subcontract_ids" to request.subcontractIds
            )
            companySubContractService.findByIds(request.subcontractIds).flatMapEach {
                companySubContractService.update(
                    it.copy(
                        status = request.status ?: it.status,
                        integrationStatus = request.integrationStatus ?: it.integrationStatus
                    ),
                    sendEvent = false
                )
            }.foldResponse()
        }

    suspend fun updateCassiMemberByMemberIds(request: UpdateCassiMemberByMemberIdRequest) = withBackfillEnvironment {
        memberService.findByIds(request.memberIds).pmapEach { member ->
            personService.get(member.personId).flatMap { person ->
                cassiMemberService.updateCassiMember(person, member)
            }
        }.foldResponse()
    }

    suspend fun backfillUpdateBillingAccountablePartyAddress(request: BackfillBillingAccountablePartyUpdateAddressRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill to update the subcontract status",
                "cnpjs" to request.cnpjs,
                "ids" to request.ids,
            )

            getBillingAccountableParty(request).flatMap { updateAddress(it) }.foldResponse()
        }

    suspend fun changeMembersProduct(request: BackfillChangeMembersProductRequest) = withBackfillEnvironment {
        logger.info(
            "Starting backfill to change members product",
            "member_ids" to request.memberIds,
            "product_id" to request.newProductId,
        )

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)

        val newProduct =
            productService.getProduct(request.newProductId).getOrNullIfNotFound()
                ?: return@withBackfillEnvironment Response(
                    HttpStatusCode.BadRequest,
                    BackfillResponse(
                        successCount = successCount.get(),
                        errorsCount = errorsCount.incrementAndGet(),
                        message = "Product not found"
                    )
                )

        val productPriceListing = productPriceListingService.getCurrent(newProduct.id).getOrNullIfNotFound()
            ?: return@withBackfillEnvironment Response(
                HttpStatusCode.BadRequest,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.incrementAndGet(),
                    message = "Product price listing not found"
                )
            )

        memberService.findByIds(request.memberIds).pmapEach { member ->
            val updatedMember = member.copy(
                selectedProduct = MemberProduct(
                    id = newProduct.id,
                    prices = newProduct.prices,
                    type = newProduct.type,
                    priceListing = newProduct.priceListing,
                    productPriceListingId = productPriceListing.id
                )
            )

            memberService.update(updatedMember).then {
                logger.info("Member product updated successfully", "member_id" to updatedMember.id)
                successCount.incrementAndGet()
            }.thenError {
                logger.error("Error updating member product", "member_id" to member.id, "error_message" to it)
                errorsCount.incrementAndGet()
            }
        }.thenError {
            logger.error("Error getting members", "member_ids" to request.memberIds, "error_message" to it)
            errorsCount.incrementAndGet()
        }

        Response(
            HttpStatusCode.OK,
            BackfillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get()
            )
        )
    }

    suspend fun createNewCpplAndBlockOrRemoveOldCppl(request: CreateNewCpplAndBlockOrRemoveOldCpplRequest) =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill to create new cppl and block or remove old cppl",
                "subcontract_id" to request.subContractId,
                "old_ans_number" to request.oldAnsNumber,
                "product_id" to request.productId,
                "action" to request.action
            )

            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)

            companyProductPriceListingService.findCurrentBySubContractIdAndAnsNumber(
                subContractId = request.subContractId,
                ansNumber = request.oldAnsNumber
            ).flatMapPair {
                productService.getProduct(request.productId)
            }.andThen { (product, oldCppl) ->
                val newCppl = oldCppl.copy(
                    id = RangeUUID.generate(),
                    ansNumber = product.ansNumber ?: return@withBackfillEnvironment Response(
                        HttpStatusCode.BadRequest,
                        BackfillResponse(
                            successCount = successCount.get(),
                            errorsCount = errorsCount.incrementAndGet(),
                            message = "Product has null ans number"
                        )
                    ),
                    product = product,
                    productId = product.id
                )

                companySubContractService.get(request.subContractId).flatMap { subcontract ->
                    companyProductPriceListingService.add(newCppl, subcontract)
                        .then {
                            logger.info("New cppl created successfully", "cppl_id" to it.id)
                        }.thenError {
                            logger.error("Error creating new cppl", "error_message" to it)
                            errorsCount.incrementAndGet()
                        }
                }
            }.flatMap { (_, oldCppl) ->
                when (request.action) {
                    CpplBackfillAction.BLOCK -> blockOldCppl(oldCppl)
                    CpplBackfillAction.REMOVE -> removeOldCppl(oldCppl)
                }.then {
                    logger.info("Old cppl ${request.action} successfully", "cppl_id" to oldCppl.id)
                    successCount.incrementAndGet()
                }.thenError {
                    logger.error(
                        "Error ${request.action} old cppl",
                        "error_message" to it, "cppl_id" to oldCppl
                    )
                    errorsCount.incrementAndGet()
                }
            }

            Response(
                HttpStatusCode.OK,
                BackfillResponse(
                    successCount = successCount.get(),
                    errorsCount = errorsCount.get()
                )
            )
        }

    suspend fun emitBeneficiaryUpdatedEvent(backfillRequest: LimitedBackfillRequest) = withBackfillEnvironment {
        logger.info("Starting backfill to produce BeneficiaryChangedEvents")

        var offset = backfillRequest.offset
        var limit = backfillRequest.limit
        do {
            val range = IntRange(offset, limit-1)
            val beneficiaries = beneficiaryService.findByFilters(
                personId = null,
                companyId = null,
                parentId = null,
                currentPhase = null,
                range = range
            ).get()

            beneficiaries.pmap {
                kafkaProducerService.produce(BeneficiaryChangedEvent(
                    beneficiary = it,
                    eventAction = NotificationEventAction.UPDATED
                ), it.id.toString())
            }

            logger.info(
                "BeneficiaryChangedEvents produced",
                "offset" to range.start,
                "limit" to range.count(),
                "size" to beneficiaries.size
            )
            offset += range.count()
            limit += range.count()

        } while (beneficiaries.size == range.count())

        Response(HttpStatusCode.OK)
    }

    private suspend fun updateAddress(billingAccountableParties: List<BillingAccountableParty>): Result<InternalFeatureResponse, Throwable> =
        coResultOf {
            val successCount = AtomicInteger(0)
            val errorsCount = AtomicInteger(0)
            val errors = mutableMapOf<String, String>()

            billingAccountableParties.pmap { billingAccountableParty ->
                coResultOf<BillingAccountableParty, Throwable> {
                    try {
                        val companyInfo = receitaFederalClient.getCompanyInfoByCNPJ(billingAccountableParty.nationalId)

                        logger.info(
                            "BillingAccountablePartynationalId",
                            "data" to billingAccountableParty.nationalId,
                            "data" to companyInfo,
                            "id" to billingAccountableParty.id,
                        )

                        billingAccountablePartyService.update(
                            billingAccountableParty.copy(
                                address = companyInfo.toAddress()
                            )
                        ).get()
                    } catch (e: Exception) {
                        logger.error(
                            "Error getting company info",
                            "error_message" to e.message,
                            "id" to billingAccountableParty.id,
                        )
                        errors[billingAccountableParty.id.toString()] = e.message ?: "ERROR"
                        errorsCount.incrementAndGet()

                        throw e
                    }
                }.then {
                    successCount.incrementAndGet()
                }
            }

            InternalFeatureResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = errors
            )
        }


    private suspend fun getBillingAccountableParty(request: BackfillBillingAccountablePartyUpdateAddressRequest): Result<List<BillingAccountableParty>, Throwable> =
        when {
            request.cnpjs.isNotEmpty() -> request.cnpjs.let {
                getBillingAccountablePartyByCnpj(it)
            }

            request.ids.isNotEmpty() -> request.ids.let { getBillingAccountablePartyByIds(it) }
            else -> getBillingAccountablePartyByEmptyAddress(request.limit)
        }

    private suspend fun getBillingAccountablePartyByCnpj(cnpjs: List<String>): Result<List<BillingAccountableParty>, Throwable> =
        cnpjs.chunked(200).pmap { chunkedCnpjs ->
            billingAccountablePartyService.findByNationalIds(chunkedCnpjs)
        }.lift().map { it.flatten() }
            .then { logger.info("BillingAccountableParty", "ids" to it.map { mapOf("id" to it.id) }) }

    private suspend fun getBillingAccountablePartyByIds(ids: List<UUID>): Result<List<BillingAccountableParty>, Throwable> =
        ids.chunked(200).pmap { chunkedIds ->
            billingAccountablePartyService.findById(chunkedIds)
        }.lift().map { it.flatten() }

    private suspend fun getBillingAccountablePartyByEmptyAddress(limit: Int = 200): Result<List<BillingAccountableParty>, Throwable> {
        var offset = 0
        var page = 0

        val billingAccountableParties: MutableList<BillingAccountableParty> = mutableListOf()
        while (true) {

            val data = billingAccountablePartyService.findByEmptyAddressPaginated(
                BillingAccountablePartyType.LEGAL_PERSON, offset, limit
            ).get()

            billingAccountableParties.addAll(data)

            logger.info("Find for billing", "offset" to offset, "limit" to limit, "size" to data.size)

            if (data.size < limit) {
                logger.info("RecurrentJob::inactivateMembership finished")

                break
            }

            offset += limit
            page += page
        }

        return billingAccountableParties.success()
    }

    private suspend fun blockOldCppl(oldCppl: CompanyProductPriceListing): Result<Boolean, Throwable> =
        companyProductPriceListingService.updateBlockForSale(oldCppl, true).map {
            true
        }

    private suspend fun removeOldCppl(oldCppl: CompanyProductPriceListing): Result<Boolean, Throwable> =
        companyProductPriceListingService.delete(oldCppl).map {
            true
        }
}

data class UpdateCassiMemberByMemberIdRequest(
    val memberIds: List<UUID>
)

data class UpdateContractStatusRequest(
    val contractIds: List<UUID>,
    val status: CompanyContractStatus? = null,
    val integrationStatus: CompanyContractIntegrationStatus? = null,
)

data class UpdateSubContractStatusRequest(
    val subcontractIds: List<UUID>,
    val status: CompanySubContractStatus? = null,
    val integrationStatus: CompanySubContractIntegrationStatus? = null,
)

data class UpdateBeneficiariesRequest(
    val data: List<UpdateBeneficiary>
) {
    data class UpdateBeneficiary(
        val id: UUID,
        val parentBeneficiary: UUID? = null,
        val memberId: UUID? = null,
        val companyId: UUID? = null,
        val type: BeneficiaryType? = null,
        val contractType: BeneficiaryContractType? = null,
        val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
        val activatedAt: LocalDateTime? = null,
        val canceledAt: LocalDateTime? = null,
        val hiredAt: LocalDateTime? = null,
        val parentBeneficiaryRelatedAt: LocalDateTime? = null,
        val canceledReason: BeneficiaryCancelationReason? = null,
        val canceledDescription: String? = null,
        val cnpj: String? = null,
        val hasContributed: Boolean? = null,
        val archived: Boolean? = null,
        val onboarding: BeneficiaryOnboarding? = null,
        val dependents: List<Beneficiary>? = null,
        val brand: Brand? = null,
        val companySubContractId: UUID? = null,
        val memberStatus: MemberStatus? = null,
        val parentPerson: PersonId? = null,
        val gracePeriodType: GracePeriodType? = null,
        val gracePeriodTypeReason: GracePeriodTypeReason? = null,
        val gracePeriodBaseDate: LocalDate? = null,
    )
}

data class UpdateCompanyWithSubcontractsRequest(
    val data: List<UpdateCompanyWithSubcontract>
)

data class UpdateCompanyWithSubcontract(
    val companyCnpj: String,
    val contract: Contract? = null,
    val company: Company? = null,
    val subcontract: Subcontract? = null,
) {
    data class Contract(
        val title: String? = null,
        val billingAccountablePartyId: UUID? = null,
        val startedAt: LocalDate? = null,
        val accountableEmail: String? = null,
        val contractFileIds: List<ContractFile>? = null,
        val isProRata: Boolean? = null,
        val defaultProductId: UUID? = null,
        val availableProducts: List<UUID>? = null,
        val dueDate: Int? = null,
        val isBillingLevel: Boolean? = null,
        val paymentType: PaymentModel? = null,
        val nature: String? = null,
        val status: CompanyContractStatus? = null,
        val integrationStatus: CompanyContractIntegrationStatus? = null,
        val hasRetroactiveCharge: Boolean? = null,
        val beneficiaryCountAtDayZero: Int? = null,
        val companySize: CompanySize? = null,
        val companyBusinessUnit: CompanyBusinessUnit? = null
    )

    data class Company(
        val parentId: UUID? = null,
        val externalCode: String? = null,
        val name: String? = null,
        val legalName: String? = null,
        val cnpj: String? = null,
        val email: String? = null,
        val phoneNumber: String? = null,
        val address: CompanyAddress? = null,
        val priceAdjustmentType: PriceAdjustmentType? = null,
        val contractType: CompanyContractType? = null,
        val contractStartedAt: LocalDateTime? = null,
        val beneficiariesCountAtDayZero: Int? = null,
        val bankingInfo: CompanyBankingInfo? = null,
        val availableProducts: List<UUID>? = null,
        val totalEmployees: Int? = null,
        val billingAccountablePartyId: UUID? = null,
        val defaultProductId: UUID? = null,
        val flexBenefit: Boolean? = null,
        val hasEmployeesAbroad: Boolean? = null,
        val contractsUrls: List<String> = emptyList(),
        val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
        val totvsContract: String? = null,
        val totvsContractVersion: String? = null,
        val totvsSubContract: String? = null,
        val totvsSubContractVersion: String? = null,
        val externalBrandId: String? = null,
        val contractIds: List<UUID> = emptyList(),
        val status: CompanyStatus? = null,
        val companySize: CompanySize? = null,
        val companyBusinessUnit: CompanyBusinessUnit? = null,
    )

    data class Subcontract(
        val externalId: String? = null,
        val title: String? = null,
        val billingAccountablePartyId: UUID? = null,
        val isProRata: Boolean? = null,
        val flexBenefit: Boolean? = null,
        val hasEmployeesAbroad: Boolean? = null,
        val defaultProductId: UUID? = null,
        val availableProducts: List<UUID>? = null,
        val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
        val dueDate: Int? = null,
        val isBillingLevel: Boolean? = null,
        val paymentType: PaymentModel? = null,
        val nature: String? = null,
        val billingGroup: String? = null,
        val avaliableCompanyProductPriceListing: List<UUID>? = null,
        val status: CompanySubContractStatus? = null,
        val integrationStatus: CompanySubContractIntegrationStatus? = null,
        val hasRetroactiveCharge: Boolean? = null,
        val blockedAt: LocalDate? = null,
    )


}

data class ParentToCreateNewDependentsRequest(
    val beneficiaryIds: List<UUID>
)

data class UpdateCompanyContractIdRequest(
    val companyId: UUID,
    val newContractId: UUID
)

data class UpdateOrCreateCpplRequest(
    val items: List<UpdateOrCreateCpplItemRequest>,
)

data class UpdateOrCreateCpplItemRequest(
    val subContractId: UUID,
    val productId: UUID,
    val range0to18: BigDecimal,
    val range19to23: BigDecimal,
    val range24to28: BigDecimal,
    val range29to33: BigDecimal,
    val range34to38: BigDecimal,
    val range39to43: BigDecimal,
    val range44to48: BigDecimal,
    val range49to53: BigDecimal,
    val range54to58: BigDecimal,
    val range59plus: BigDecimal,
)

data class GracePeriodRequest(
    val page: Int = 0,
    val perPage: Int = 100
)

data class UpdateGracePeriodTypeRequest(
    val items: List<UpdateGracePeriodTypeItemRequest>
)

data class GracePeriodAndStatusRequest(
    val start: Int,
    val endInclusive: Int,
    val memberStatus: MemberStatus,
    val currentGracePeriodType: GracePeriodType,
    val newGracePeriodType: GracePeriodType,
)

data class UpdateGracePeriodTypeItemRequest(
    val beneficiaryId: UUID,
    val gracePeriodType: GracePeriodType,
    val gracePeriodTypeReason: GracePeriodTypeReason? = null,
    val gracePeriodBaseDate: String? = null,
)

data class LimitedBackfillRequest(
    val offset: Int,
    val limit: Int
)

data class CompanySizeBackfillRequest(
    val companies: List<CompanySizeBackfill>
)

data class CompanySizeBackfill(
    val companyId: UUID,
    val companySize: CompanySize
)

data class StandardCostBackfillRequest(
    val items: List<StandardCostBackfillItem>
)

data class StandardCostBackfillItem(
    val companyBusinessUnit: CompanyBusinessUnit,
    val companySize: CompanySize,
    val adhesion: Adhesion,
    val ansNumber: String,
    val costByPersonDetail: CostByPersonDetail
)

data class BackfillBillingAccountablePartyUpdateAddressRequest(
    val cnpjs: List<String> = emptyList(),
    val ids: List<UUID> = emptyList(),
    val limit: Int = 200,
)

data class InternalFeatureResponse(
    val successCount: Int = 0,
    val errorsCount: Int = 0,
    val message: String? = null,
    val additionalInfo: Map<String, Any?>? = null
)

data class BackfillChangeMembersProductRequest(
    val memberIds: List<UUID>,
    val newProductId: UUID
)

data class CreateNewCpplAndBlockOrRemoveOldCpplRequest(
    val subContractId: UUID,
    val oldAnsNumber: String,
    val productId: UUID,
    val action: CpplBackfillAction
)

enum class CpplBackfillAction {
    BLOCK, REMOVE
}
