package br.com.alice.business.services

import br.com.alice.business.client.MemberTelegramTrackingService
import br.com.alice.business.converters.MemberTelegramTrackingStatusEnum
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.models.MemberTelegramTracking
import br.com.alice.data.layer.services.MemberTelegramTrackingModelDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class MemberTelegramTrackingServiceImpl(
    private val dataService: MemberTelegramTrackingModelDataService
) : MemberTelegramTrackingService {

    companion object {
        const val MAX_SEARCH_SIZE = 10
    }

    override suspend fun get(id: UUID) = dataService.get(id).map { it.toTransport() }
    override suspend fun add(memberTelegramTracking: MemberTelegramTracking) =
        dataService.add(memberTelegramTracking.toModel()).map { it.toTransport() }
    override suspend fun upsert(memberTelegramTracking: MemberTelegramTracking) =
        this.findByExternalId(memberTelegramTracking.externalId)
            .flatMap { model ->
                update(
                    memberTelegramTracking.copy(
                        id = model.id,
                        version = model.version,
                        createdAt = model.createdAt,
                        updatedAt = model.updatedAt
                    )
                )
            }.coFoldNotFound { add(memberTelegramTracking) }

    override suspend fun update(memberTelegramTracking: MemberTelegramTracking) =
        dataService.update(memberTelegramTracking.toModel()).map { it.toTransport() }

    override suspend fun findByTrackingCode(trackingCode: String) = dataService.findOne {
        where { this.tracking.eq(trackingCode) }
    }.map { it.toTransport() }

    override suspend fun findByExternalId(externalIdParam: String) =
        dataService.findOne {
            where { this.externalId.eq(externalIdParam) }
        }.map { it.toTransport() }

    override suspend fun findBySearchTokens(query: String) =
        dataService.find {
            where { this.searchTokens.search(query) }.limit { MAX_SEARCH_SIZE }.orderBy { this.dateLastChange }
                .sortOrder { desc }
        }.pmapEach { it.toTransport() }

    @OrPredicateUsage
    override suspend fun findNotDeliveredItems(): Result<List<MemberTelegramTracking>, Throwable> =
        dataService.find {
            where {
                this.statusDelivery.isNull() and
                        scope(
                            this.status.eq(MemberTelegramTrackingStatusEnum.POSTADO.description)
                                    or this.status.eq(MemberTelegramTrackingStatusEnum.PROCESSANDO.description)
                                    or this.status.isNull()
                        )

            }
        }.pmapEach { it.toTransport() }

}
