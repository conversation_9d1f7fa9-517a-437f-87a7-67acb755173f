package br.com.alice.business.services

import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.CompanyProductPriceListing
import br.com.alice.data.layer.models.CompanyProductPriceListingModel
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.CompanyProductPriceListingModelDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.product.client.PriceListingService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class CompanyProductPriceListingServiceImpl(
    private val dataService: CompanyProductPriceListingModelDataService,
    private val productService: ProductService,
    private val priceListingService: PriceListingService,
    private val companySubContractService: CompanySubContractService,
) : CompanyProductPriceListingService {

    override suspend fun get(id: UUID): Result<CompanyProductPriceListing, Throwable> = useReadDatabase {
        doAndLog("get", id) {
            dataService.get(id).map { it.toTransport() }
        }
    }

    override suspend fun findByIds(ids: List<UUID>): Result<List<CompanyProductPriceListing>, Throwable> =
        useReadDatabase {
            doAndLog("findByIds", ids) {
                dataService.find { where { this.id.inList(ids) } }.pmapEach { it.toTransport() }
            }
        }

    override suspend fun findCurrentBySubContractIdAvailableForSale(
        subContractId: UUID, findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<List<CompanyProductPriceListing>, Throwable> = useReadDatabase {
        doAndLog("findCurrentBySubContractId", subContractId) {
            dataService.find {
                where {
                    this.companySubContractId.eq(subContractId) and
                            this.isBlockedForSale.diff(true) and
                            buildCurrentFilterPredicate()
                }
            }
        }
    }.flatMap { withOptions(it, findOptions) }.pmapEach { it.toTransport() }

    override suspend fun findCurrentBySubContractId(
        subContractId: UUID,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<List<CompanyProductPriceListing>, Throwable> =
        useReadDatabase {
            doAndLog("findCurrentBySubContractId", subContractId) {
                dataService.find {
                    where { this.companySubContractId.eq(subContractId) and buildCurrentFilterPredicate() }
                }
            }
        }.flatMap { withOptions(it, findOptions) }.pmapEach { it.toTransport() }

    override suspend fun findCurrentBySubContractIdAndAnsNumber(
        subContractId: UUID,
        ansNumber: String,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<CompanyProductPriceListing, Throwable> =
        useReadDatabase {
            doAndLog("findCurrentBySubContractId", subContractId) {
                dataService.findOne {
                    where {
                        this.companySubContractId.eq(subContractId) and this.ansNumber.eq(
                            ansNumber
                        ) and buildCurrentFilterPredicate()
                    }
                }
            }
        }.flatMap { withOptions(listOf(it), findOptions).mapFirst() }.map { it.toTransport() }

    override suspend fun findCurrentBySubContractIdAndProductId(
        subContractId: UUID,
        productId: UUID,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<CompanyProductPriceListing, Throwable> =
        useReadDatabase {
            doAndLog("findCurrentBySubContractId", subContractId) {
                dataService.findOne {
                    where {
                        this.companySubContractId.eq(subContractId) and this.productId.eq(
                            productId
                        ) and buildCurrentFilterPredicate()
                    }
                }
            }
        }.flatMap { withOptions(listOf(it), findOptions).mapFirst() }.map { it.toTransport() }

    override suspend fun findBySubContractIdAndProductId(
        subContractId: UUID,
        productId: UUID,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<List<CompanyProductPriceListing>, Throwable> =
        useReadDatabase {
            doAndLog("findBySubContractIdAndProductId", subContractId) {
                dataService.find {
                    where {
                        this.companySubContractId.eq(subContractId) and this.productId.eq(
                            productId
                        )
                    }
                }
            }
        }.flatMap { withOptions(it, findOptions) }.pmapEach { it.toTransport() }

    override suspend fun findBySubContractIdAndAnsNumber(
        subContractId: UUID,
        ansNumber: String,
    ): Result<List<CompanyProductPriceListing>, Throwable> =
        useReadDatabase {
            doAndLog("findBySubContractIdAndProductId", subContractId) {
                dataService.find {
                    where {
                        this.companySubContractId.eq(subContractId) and this.ansNumber.eq(
                            ansNumber
                        )
                    }
                }
            }
        }.pmapEach { it.toTransport() }

    override suspend fun findBySubContractIdAndAnsNumberAndDateRange(
        subContractId: UUID,
        ansNumber: String,
        startDate: LocalDate,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<CompanyProductPriceListing, Throwable> =
        useReadDatabase {
            doAndLog("findBySubContractIdAndAnsNumber", subContractId) {
                dataService.findOne {
                    where {
                        this.companySubContractId.eq(subContractId) and
                                this.ansNumber.eq(ansNumber) and this.startDate.eq(startDate)
                    }
                }
            }
        }.flatMap { withOptions(listOf(it), findOptions).mapFirst() }.map { it.toTransport() }

    override suspend fun findCurrentBySubContractIds(
        subContractIds: List<UUID>,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<Map<String, List<CompanyProductPriceListing>>, Throwable> = useReadDatabase {
        doAndLog("findCurrentBySubContractIds", subContractIds) {
            dataService.find { where { this.companySubContractId.inList(subContractIds) and buildCurrentFilterPredicate() } }
        }
    }.flatMap {
        withOptions(it, findOptions)
    }.map { cppl ->
        cppl.map { it.toTransport() }.groupBy { it.companySubContractId.toString() }
    }

    override suspend fun findCurrentByCompanyIds(
        companyIds: List<UUID>,
        findOptions: CompanyProductPriceListingService.FindOptions,
    ): Result<List<CompanyProductPriceListing>, Throwable> = useReadDatabase {
        doAndLog("findCurrentByCompanyId", companyIds) {
            dataService.find { where { this.companyId.inList(companyIds) and buildCurrentFilterPredicate() } }
        }
    }.flatMap { withOptions(it, findOptions) }.pmapEach { it.toTransport() }


    private suspend fun withOptions(
        companyProductPriceListing: List<CompanyProductPriceListingModel>,
        findOptions: CompanyProductPriceListingService.FindOptions
    ): Result<List<CompanyProductPriceListingModel>, Throwable> = coroutineScope {
        val productIds = companyProductPriceListing.map { it.productId }.distinct()

        val productsDeferred = async {
            if (findOptions.withProduct) productService.findByIds(
                productIds,
                findOptions = ProductService.FindOptions(withPriceListing = false, withBundles = false)
            ).get().associateBy { it.id }
            else null
        }

        val products = productsDeferred.await()

        companyProductPriceListing.map {
            it.copy(
                product = products?.get(it.productId),
            )
        }.success()
    }

    override suspend fun add(
        companyProductPriceListing: CompanyProductPriceListing,
        subcontract: CompanySubContract,
        sendEvent: Boolean,
        shouldUpdateTotvsMemberPriceListing: Boolean?
    ): Result<CompanyProductPriceListing, Throwable> =
        doAndLog("add", companyProductPriceListing) {
            dataService.add(companyProductPriceListing.toModel())
                .andThen {
                    if (companyProductPriceListing.companySubContractId != subcontract.id) throw IllegalArgumentException("Subcontract id does not match")
                    companySubContractService.update(
                        subcontract.copy(
                            avaliableCompanyProductPriceListing = subcontract.toNewProductPriceListing(listOf(companyProductPriceListing))
                        ),
                        sendEvent = sendEvent,
                        shouldUpdateTotvsMemberPriceListing = shouldUpdateTotvsMemberPriceListing
                    )
                }
        }.map { it.toTransport() }

    private suspend fun addNewValidity(newModel: CompanyProductPriceListingModel) =
        dataService.findOne {
            where {
                this.companySubContractId.eq(newModel.companySubContractId) and
                        this.productId.eq(newModel.productId) and
                        buildCurrentFilterPredicate()
            }
        }
            .flatMap { current -> dataService.update(current.copy(endDate = LocalDate.now().minusDays(1))) }
            .flatMap { dataService.add(newModel.copy(startDate = LocalDate.now(), endDate = null)) }

    override suspend fun upsertCompanyProductPriceListing(
        companyProductPriceListing: CompanyProductPriceListing,
        subcontract: CompanySubContract,
        sendEvent: Boolean,
        shouldUpdateTotvsMemberPriceListing: Boolean?
    ): Result<CompanyProductPriceListing, Throwable> =
        doAndLog("add", companyProductPriceListing) {
            dataService.add(companyProductPriceListing.toModel())
                .coFoldDuplicated { addNewValidity(companyProductPriceListing.toModel()) }
                .andThen {
                    if (companyProductPriceListing.companySubContractId != subcontract.id) throw IllegalArgumentException("Subcontract id does not match")
                    companySubContractService.update(
                        subcontract.copy(
                            avaliableCompanyProductPriceListing = subcontract.toNewProductPriceListing(listOf(companyProductPriceListing))
                        ),
                        sendEvent = sendEvent,
                        shouldUpdateTotvsMemberPriceListing = shouldUpdateTotvsMemberPriceListing
                    )
                }
        }.map { it.toTransport() }

    override suspend fun addListToSubContract(
        companyProductPriceListingList: List<CompanyProductPriceListing>,
        subcontract: CompanySubContract,
        sendEvent: Boolean
    ): Result<List<CompanyProductPriceListing>, Throwable> =
        doAndLog("addList", companyProductPriceListingList) {
            dataService.addList(companyProductPriceListingList.pmap { it.toModel() })
                .andThen {
                    companyProductPriceListingList.forEach { if (it.companySubContractId != subcontract.id) throw IllegalArgumentException("Subcontract id does not match") }
                    companySubContractService.update(
                        subcontract.copy(
                            avaliableCompanyProductPriceListing = subcontract.toNewProductPriceListing(companyProductPriceListingList)
                        ),
                        sendEvent = sendEvent
                    )
                }
        }.pmapEach { it.toTransport() }

    private fun CompanySubContract.toNewProductPriceListing(companyProductPriceListing: List<CompanyProductPriceListing>? = null) =
        if (companyProductPriceListing != null) ((this.avaliableCompanyProductPriceListing
            ?: emptyList()) + companyProductPriceListing.map { it.id }).distinct()
        else this.avaliableCompanyProductPriceListing

    override suspend fun update(
        companyProductPriceListing: CompanyProductPriceListing,
        sendEvent: Boolean,
        shouldUpdateTotvsMemberPriceListing: Boolean
    ): Result<CompanyProductPriceListing, Throwable> =
        doAndLog("update", companyProductPriceListing) {
            dataService.update(companyProductPriceListing.toModel())
                .andThen {
                    if (sendEvent) {
                        companySubContractService.get(companyProductPriceListing.companySubContractId)
                            .flatMap { csc ->
                                companySubContractService.update(
                                    csc,
                                    sendEvent = sendEvent,
                                    shouldUpdateTotvsMemberPriceListing = shouldUpdateTotvsMemberPriceListing
                                )
                            }
                    }
                    it.success()
                }
        }.map { it.toTransport() }

    override suspend fun updateList(
        companyProductPriceListingList: List<CompanyProductPriceListing>,
        companySubContract: CompanySubContract,
        sendEvent: Boolean,
        shouldUpdateTotvsMemberPriceListing: Boolean
    ): Result<List<CompanyProductPriceListing>, Throwable> =
        doAndLog("updateList", companyProductPriceListingList) {
            dataService.updateList(companyProductPriceListingList.pmap { it.toModel() })
                .andThen {
                    if (sendEvent) {
                        companySubContractService.update(
                            companySubContract,
                            sendEvent = sendEvent,
                            shouldUpdateTotvsMemberPriceListing = shouldUpdateTotvsMemberPriceListing
                        )
                    }
                    it.success()
                }
        }.pmapEach { it.toTransport() }

    override suspend fun delete(companyProductPriceListing: CompanyProductPriceListing) =
        doAndLog("delete", companyProductPriceListing) {
            dataService.delete(companyProductPriceListing.toModel())
                .andThen {
                    companySubContractService.get(companyProductPriceListing.companySubContractId)
                        .flatMap { csc ->
                            companySubContractService.update(
                                csc.copy(
                                    avaliableCompanyProductPriceListing = csc.avaliableCompanyProductPriceListing?.filter {
                                        it != companyProductPriceListing.id
                                    })
                            )
                        }
                }

        }

    override suspend fun updateBlockForSale(
        companyProductPriceListing: CompanyProductPriceListing,
        blockForSale: Boolean
    ): Result<CompanyProductPriceListing, Throwable> =
        doAndLog("blockForSale", companyProductPriceListing) {
            dataService.update(companyProductPriceListing.copy(isBlockedForSale = blockForSale).toModel())
        }.map { it.toTransport() }

    override suspend fun clearFromSubContract(companySubContractId: UUID) =
        doAndLog("deleteInBatch", companySubContractId) {
            findCurrentBySubContractId(companySubContractId)
                .flatMap {
                    dataService.deleteList(it.pmap { it.toModel() })
                }
                .andThen {
                    companySubContractService.get(companySubContractId)
                        .flatMap {
                            companySubContractService.update(
                                it.copy(
                                    avaliableCompanyProductPriceListing = null
                                )
                            )
                        }
                }

        }

    override suspend fun findForBackfill(range: IntRange): Result<List<CompanyProductPriceListing>, Throwable> =
        useReadDatabase {
            dataService.find {
                where { this.priceListItems.isEmptyList() }.offset { range.first }.limit { range.last }
            }
        }.pmapEach { it.toTransport() }

    override suspend fun findCpplProductIdsForSubContract(subContractId: UUID) =
        doAndLog("findCpplProductIdsForSubContract", subContractId) {
            dataService.find { where { this.companySubContractId.eq(subContractId) and this.endDate.isNull() } }
        }.mapEach { it.productId }

    override suspend fun countCurrentBySubContractId(subContractId: UUID): Result<Int, Throwable> = useReadDatabase {
        doAndLog("countCurrentBySubContractId", subContractId) {
            dataService.count {
                where {
                    this.companySubContractId.eq(subContractId) and
                            buildCurrentFilterPredicate()
                }
            }
        }
    }

    override suspend fun countCurrentBySubContractIdAvailableForSale(subContractId: UUID): Result<Int, Throwable> =
        useReadDatabase {
            doAndLog("countCurrentBySubContractId", subContractId) {
                dataService.count {
                    where {
                        this.companySubContractId.eq(subContractId) and
                                this.isBlockedForSale.diff(true) and
                                buildCurrentFilterPredicate()
                    }
                }
            }
        }

    private suspend fun <T : Any> doAndLog(
        action: String, inputField: Any, block: suspend () -> Result<T, Throwable>
    ): Result<T, Throwable> {
        logger.info("CompanyProductPriceListingService::$action started", "input" to inputField)
        return block().then { logger.info("CompanyProductPriceListingService::$action finished") }
            .thenError { logger.error("CompanyProductPriceListingService::$action exception thrown", "ex" to it) }
    }

    private fun useNewCurrentLogic() =
        FeatureService.get(FeatureNamespace.BUSINESS, "new_current_logic_for_price_listing", false)

    @OptIn(OrPredicateUsage::class)
    private fun CompanyProductPriceListingModelDataService.FieldOptions.buildCurrentFilterPredicate() =
        if (useNewCurrentLogic()) {
            LocalDate.now().let { now ->
                scope(this.startDate.lessEq(now) and scope(this.endDate.isNull() or this.endDate.greaterEq(now)))
            }
        } else {
            this.endDate.isNull()
        }
}
