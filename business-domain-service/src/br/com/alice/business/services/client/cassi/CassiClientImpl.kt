package br.com.alice.business.services.client.cassi

import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.suspend
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializer
import com.google.gson.JsonSyntaxException
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpResponseValidator
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.forms.FormDataContent
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.client.statement.request
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.contentType
import redis.clients.jedis.exceptions.JedisException
import java.time.LocalDate
import kotlin.reflect.KClass

class CassiClientImpl(
    private val config: CassiConfiguration,
    private val httpEngine: HttpClientEngine,
    private val cache: GenericCache,
) : CassiClient {

    private val client = DefaultHttpClient(httpEngine, {
        install(ContentNegotiation) {
            gsonSnakeCase()
        }

        HttpResponseValidator {
            handleResponseException { cause: Throwable ->
                val responseException = cause as? ResponseException ?: return@handleResponseException
                val response = responseException.response

                val path = response.request.let { "[${it.method.value}] ${it.url}" }

                when (response.status) {
                    HttpStatusCode.Forbidden -> throw ForbiddenException(path)
                    HttpStatusCode.Unauthorized -> throw UnauthorizedException(path)

                    HttpStatusCode.NotFound,
                    HttpStatusCode.BadRequest -> {
                        val body = response.bodyAsText()

                        logger.error(
                            "Cassi Handle Response Exception",
                            "status" to response.status,
                            "body" to body,
                        )

                        try {
                            val errorResponse = gson.fromJson<ReportErrorResponse>(body)

                            when (errorResponse.error) {
                                "invalid_request", "unauthorized_client" -> throw UnauthorizedException(path)
                                else -> throw CassiException(errorResponse.error_description)
                            }
                        } catch (ex: NullPointerException) {
                            val errorResponse = gson.fromJson<V1BadErrorResponse>(body)
                            val error = errorResponse.data.first()

                            if(error == "Não foram encontrados registros para os dados informados.") {
                                throw NotFoundException("CASSI Card details not found")
                            }

                            throw CassiException(error)
                        }
                    }

                    HttpStatusCode.InternalServerError -> throw ServerResponseException(response, response.bodyAsText())
                }
            }
        }
    }, timeoutInMillis = 15_000)

    private suspend fun fetchAuthenticationTokenFromCassi() = client.post(config.authUrl) {
        setBody(FormDataContent(Parameters.build {
            append("client_id", config.clientId)
            append("client_secret", config.secret)
            append("grant_type", "client_credentials")
        }))
    }.body<AuthenticationResponse>().access_token

    override suspend fun getAuthenticationToken() = try {
        cache.get(
            "auth-token",
            String::class,
            expirationTime = 36000,
        ) {
            fetchAuthenticationTokenFromCassi()
        }.success()
    } catch (err: JedisException) {
        logger.error(
            "It's not possible use the cache now",
            "reason" to err
        )
        fetchAuthenticationTokenFromCassi().success()
    } catch (err: Throwable) {
        err.failure()
    }

    private suspend fun getCardDetailByOldApi(nationalId: String, token: String) =
        "${config.baseUrl}/CartaoParticipante/listarCartaoReciprocidade".let { url ->
            client.post(url) {
                header("User_Key", config.userKey)
                header("Authorization", "Bearer $token")
                contentType(ContentType.Application.Json)
                setBody(CardDetailRequest(nationalId))
            }.bodyAsText()
                .also {
                    logger.info("CassiClientImpl::getCardDetailByOldApi - Use old api url", "url" to url)
                }
        }

    private suspend fun getCardByDetailByV1Api(nationalId: String, token: String) = config.baseUrlV1.let { url ->
        client.get(url) {
            header("User_Key", config.userKeyV1)
            header("Authorization", "Bearer $token")
            url {
                parameters.append("cpfs", nationalId)
            }
        }.bodyAsText()
            .also {
            logger.info("CassiClientImpl::getCardByDetailByV1Api - Use new api url", "url" to url)
        }
    }

    private fun shouldUseNewApi() = FeatureService.get(FeatureNamespace.BUSINESS, "should_use_new_cassi_api", false)

    override suspend fun getCardDetailsByNationalId(
        nationalId: String
    ): Result<CassiMemberInfo, Throwable> = getAuthenticationToken()
        .map {
            if (shouldUseNewApi())
                getCardByDetailByV1Api(nationalId, it)
            else
                getCardDetailByOldApi(nationalId, it)
        }.flatMap {
            if (shouldUseNewApi())
                parseErrorByV1Api(nationalId, it)
            else
                parseErrorByOldApi(it)

        }

    private suspend fun parseErrorByOldApi(response: String) =
        trySerialize<SuccessResponse>(response)
            .coFoldError(notFoundResponseCatch(response))
            .flatMap { successResponse ->
                if (!successResponse.success) {
                    trySerialize<BadRequestResponse>(response)
                        .flatMap {
                            logger.info(
                                "Serialized CASSI response",
                                "body" to it,
                                "response" to "BadRequestResponse"
                            )

                            CassiException("Something's wrong with CASSI integration").failure()
                        }
                } else
                    successResponse.data.first().let { cardDetail ->
                        cardDetail.toCassiMemberInfo().success()
                    }
            }


    private suspend fun parseErrorByV1Api(nationalId: String, response: String) =
        trySerialize<SuccessResponse>(response)
            .coFoldError(notFoundResponseCatchV1Api(nationalId, response))
            .flatMap { successResponse ->
                if (!successResponse.success) {
                    logger.error("Serialized CASSI response", "data" to successResponse.data)
                    CassiException("Something's wrong with CASSI integration").failure()
                } else
                    successResponse.data.first().let { cardDetail ->
                        cardDetail.toCassiMemberInfo().success()
                    }

            }


    private fun CassiBeneficiaryDetail.toCassiMemberInfo() = CassiMemberInfo(
        accountNumber = numeroCartao,
        expirationDate =
        competenciaFinalValidade
            .atEndOfTheMonth()
            .toString(),
        startDate =
        dataAdesao
            .toString()
    )

    private inline fun <reified T> trySerialize(json: String) = Result.of<T, Throwable> {
        Gson()
            .newBuilder()
            .registerLocalDateCassi()
            .create()
            .fromJson(json, T::class.java)
    }

    private fun notFoundResponseCatch(body: String)
            : Pair<KClass<JsonSyntaxException>, suspend (Throwable) -> Result<Nothing, Throwable>> =
        Pair(JsonSyntaxException::class,
            suspend() { ex ->
                trySerialize<NotFoundResponse>(body)
                    .flatMap {
                        logger.info(
                            "Serialized CASSI response",
                            "body" to it,
                            "response" to "NotFound"
                        )

                        if (it.data == "Dados não localizados") {
                            NotFoundException("CASSI Card details not found").failure()
                        } else {
                            CassiException("Something's wrong with CASSI integration").failure()
                        }
                    }
            })

    private fun notFoundResponseCatchV1Api(nationalId: String, body: String)
            : Pair<KClass<JsonSyntaxException>, suspend (Throwable) -> Result<Nothing, Throwable>> =
        Pair(JsonSyntaxException::class,
            suspend() { ex ->
                trySerialize<V1BadErrorResponse>(body)
                    .flatMap {
                        logger.info(
                            "Serialized CASSI response",
                            "body" to it,
                            "response" to "NotFound"
                        )

                        logger.info(
                            "Serialized CASSI response",
                            "body" to it,
                            "response" to "BadRequestResponse"
                        )

                        val error = it.data.first()

                        if (error == "CPF '$nationalId' é inválido.")
                            NotFoundException("CASSI Card details not found").failure()
                        else
                            CassiException("Something's wrong with CASSI integration").failure()
                    }
            })

    private fun GsonBuilder.registerLocalDateCassi(): GsonBuilder =
        this.registerTypeAdapter(LocalDate::class.java, JsonDeserializer { json, _, _ ->
            val jsonDate = json.asJsonPrimitive.asString.substring(0, 10)
            LocalDate.parse(jsonDate)
        } as JsonDeserializer<LocalDate?>?)
}
