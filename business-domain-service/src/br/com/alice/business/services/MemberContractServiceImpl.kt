package br.com.alice.business.services

import br.com.alice.business.client.MemberContractService
import br.com.alice.business.client.MemberContractService.FindOptions
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.MemberContractCreatedEvent
import br.com.alice.business.events.MemberContractSignedEvent
import br.com.alice.business.exceptions.MemberContractAlreadyCreatedException
import br.com.alice.business.logics.MemberContractLogic.checkIfIsValidContract
import br.com.alice.business.model.CreateMemberContractRequest
import br.com.alice.business.services.factory.MemberContractFactory
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.models.MemberContract
import br.com.alice.data.layer.models.MemberContractModel
import br.com.alice.data.layer.models.UserSignature
import br.com.alice.data.layer.models.withTerms
import br.com.alice.data.layer.services.MemberContractModelDataService
import br.com.alice.membership.client.MemberContractTermService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class MemberContractServiceImpl(
    private val dataService: MemberContractModelDataService,
    private val memberContractTermService: MemberContractTermService,
    private val kafkaProducerService: KafkaProducerService,
    private val memberContractFactory: MemberContractFactory,
) : MemberContractService {

    override suspend fun create(request: CreateMemberContractRequest): Result<MemberContract, Throwable> {
        val memberId = request.memberId

        val hasContract = findByMember(memberId).getOrNullIfNotFound()
        if (hasContract != null) return MemberContractAlreadyCreatedException(memberId).failure()

        val contract = memberContractFactory.create(request)
        contract.terms?.forEach { memberContractTermService.create(it).get() }

        return dataService.add(contract.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(MemberContractCreatedEvent(it)) }
    }

    override suspend fun softDeleteByMemberId(memberId: UUID): Result<Boolean, Throwable> =
        dataService.findOne { where { this.memberId.eq(memberId) } }
            .flatMapPair { dataService.softDelete(it) }
            .flatMap { (_, memberContract) -> memberContractTermService.softDeleteByMemberContractId(memberContract.id) }

    override suspend fun findByMember(memberId: UUID, findOptions: FindOptions): Result<MemberContract, Throwable> =
        dataService.findOne { where { this.memberId.eq(memberId) } }
            .flatMap { withOptions(it, findOptions) }
            .map { it.toTransport() }

    override suspend fun get(id: UUID, findOptions: FindOptions): Result<MemberContract, Throwable> =
        dataService.get(id).flatMap { withOptions(it, findOptions) }.map { it.toTransport() }

    private suspend fun withOptions(
        memberContract: MemberContractModel,
        findOptions: FindOptions,
    ): Result<MemberContractModel, Throwable> = when {
        findOptions.withTerms -> withTerms(memberContract)
        else -> memberContract.success()
    }

    private suspend fun withTerms(memberContract: MemberContractModel): Result<MemberContractModel, Throwable> =
        memberContractTermService.findByMemberContractId(memberContract.id)
            .map { memberContract.withTerms(it) }

    override suspend fun sign(contractId: UUID, userSignature: UserSignature): Result<MemberContract, Throwable> =
        get(contractId, FindOptions(true))
            .map { it.toModel() }
            .flatMap { it.checkIfIsValidContract() }
            .flatMap { it.sign(userSignature).success() }
            .flatMap { dataService.update(it) }
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(MemberContractSignedEvent(it)) }
}
