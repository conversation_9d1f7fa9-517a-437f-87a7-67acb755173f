package br.com.alice.business.metrics

import br.com.alice.common.observability.metrics.Metric

object CompanyDataExportMetrics {
    const val MEMBER_DATA_EXPORT = "company_data_export_member_data_count"

    fun registerCompanyDataMetrics() {
        listOf(
            MEMBER_DATA_EXPORT,
        ).forEach { metric ->
            Metric.registerCounter(metric, "success" to true.toString())
            Metric.registerCounter(metric, "success" to false.toString())
        }
    }

    fun increaseMemberRequestedData(success: Boolean) = Metric.increment(MEMBER_DATA_EXPORT, "success" to success.toString())
}
