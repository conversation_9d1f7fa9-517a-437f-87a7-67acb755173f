package br.com.alice.business.converters.model

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.data.layer.models.CompanyAddressModel
import br.com.alice.data.layer.models.CompanyBankingInfo
import br.com.alice.data.layer.models.CompanyBankingInfoModel
import br.com.alice.data.layer.models.CompanyModel

object CompanyConverter : Converter<CompanyModel, Company>(
    CompanyModel::class,
    Company::class,
)

fun Company.toModel() = CompanyConverter.unconvert(this)
fun CompanyModel.toTransport() = CompanyConverter.convert(this)

object CompanyBankingInfoConverter : Converter<CompanyBankingInfoModel, CompanyBankingInfo>(
    CompanyBankingInfoModel::class,
    CompanyBankingInfo::class,
)

fun CompanyBankingInfo.toModel() = CompanyBankingInfoConverter.unconvert(this)
fun CompanyBankingInfoModel.toTransport() = CompanyBankingInfoConverter.convert(this)

object CompanyAddressConverter : Converter<CompanyAddressModel, CompanyAddress>(
    CompanyAddressModel::class,
    CompanyAddress::class,
)

fun CompanyAddress.toModel() = CompanyAddressConverter.unconvert(this)
fun CompanyAddressModel.toTransport() = CompanyAddressConverter.convert(this)
