package br.com.alice.ehr.model.diseaseSearchEngine

import br.com.alice.common.Disease

data class DiseaseResponse(
    val code: String,
    val name: String,
    val description: String,
    val score: Int,
    val classification: Classification
){
    fun typeValue() = "${this.classification.diseaseType.name}_${this.code}"
}

data class DiseasesResponse(val results: List<DiseaseResponse>)

enum class Classification(val diseaseType: Disease.Type) {
    CID(Disease.Type.CID_10),
    CIAP(Disease.Type.CIAP_2)
}
