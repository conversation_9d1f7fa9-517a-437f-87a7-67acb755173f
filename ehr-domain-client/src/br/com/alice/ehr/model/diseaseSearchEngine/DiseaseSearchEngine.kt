package br.com.alice.ehr.model.diseaseSearchEngine

import br.com.alice.common.logging.logger
import br.com.alice.ehr.model.ciapSearchEngine.CiapResult
import br.com.alice.ehr.model.ciapSearchEngine.CiapSearchEngine
import br.com.alice.ehr.model.cidSearchEngine.CidResult
import br.com.alice.ehr.model.cidSearchEngine.CidSearchEngine

object DiseaseSearchEngine {

    private fun searchCID(query: String): List<CidResult> {
        val cidStartTime = System.nanoTime()
        val cidResults = CidSearchEngine.search(query, 5, 10)
        val cidEndTime = System.nanoTime()
        logger.info("cid search: {} {}", "query" to query, "elapsedTime" to cidEndTime - cidStartTime)
        return cidResults
    }

    private fun searchCIAP(query: String): List<CiapResult> {
        val ciapStartTime = System.nanoTime()
        val ciapResults = CiapSearchEngine.search(query, 5)
        val ciapEndTime = System.nanoTime()
        logger.info("ciap search: {} {}", "query" to query, "elapsedTime" to ciapEndTime - ciapStartTime)
        return ciapResults
    }

    fun searchAll(query: String) = search(query, false)

    fun search(query: String, removeCIDs: Boolean): List<DiseaseResponse> {
        val cidResults = if (removeCIDs) emptyList() else searchCID(query)
        val ciapResults = searchCIAP(query)

        val responses = mutableListOf<DiseaseResponse>()
        cidResults.forEach {
            responses.add(DiseaseResponse(it.code, it.description, "${it.description} - CID ${it.code}", it.score, Classification.CID))
        }
        ciapResults.forEach {
            responses.add(DiseaseResponse(it.code, it.description, "${it.description} - CIAP ${it.code}", it.score, Classification.CIAP))
        }

        return responses
    }
}
