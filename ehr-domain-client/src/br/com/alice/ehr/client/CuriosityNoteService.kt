package br.com.alice.ehr.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.CuriosityNote
import com.github.kittinunf.result.Result

@RemoteService
interface CuriosityNoteService: Service {
    override val namespace get() = "ehr"
    override val serviceName get() = "curiosity_note"

    suspend fun update(model: CuriosityNote): Result<CuriosityNote, Throwable>

    suspend fun getOrCreate(personId: PersonId): Result<CuriosityNote, Throwable>
}
