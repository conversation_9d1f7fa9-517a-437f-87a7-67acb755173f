package br.com.alice.clinicalaccount.routes

import br.com.alice.clinicalaccount.controllers.CaseRecordCreateBackfillController
import br.com.alice.clinicalaccount.controllers.InternalFeaturesController
import br.com.alice.clinicalaccount.controllers.PersonInternalReferenceBackfillController
import br.com.alice.common.coHandler
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.apiRoutes() {

    val internalFeaturesController by inject<InternalFeaturesController>()
    val caseRecordCreateBackfillController by inject<CaseRecordCreateBackfillController>()
    val personInternalReferenceBackfillController by inject<PersonInternalReferenceBackfillController>()

    route("/internal") {
        post("/association_member") { co<PERSON>and<PERSON>(internalFeaturesController::associateMember) }
        post("/disassociation_member") { co<PERSON>and<PERSON>(internalFeaturesController::disassociateMember) }
        post("/disassociate_inactive_members") { coHandler(internalFeaturesController::disassociateInactiveMembers) }
    }

    route("/backfill") {
        post("/case_record/create_from_disease") { coHandler(caseRecordCreateBackfillController::createCaseRecordFromDiseases) }
        post("/person_internal_reference") { coHandler(personInternalReferenceBackfillController::createPersonInternalReference) }
    }

}
