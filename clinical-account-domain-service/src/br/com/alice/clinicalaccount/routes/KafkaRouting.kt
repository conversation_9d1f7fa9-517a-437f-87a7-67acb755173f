package br.com.alice.clinicalaccount.routes

import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.clinicalaccount.consumers.MemberConsumer
import br.com.alice.clinicalaccount.consumers.PersonClinicalAccountHistoryConsumer
import br.com.alice.clinicalaccount.event.PersonClinicalAccountCreatedEvent
import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.member.onboarding.notifier.MemberOnboardingReadyToTeamAssociationEvent
import br.com.alice.person.model.events.MemberActivatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val memberConsumer by inject<MemberConsumer>()
    consume(
        handlerName = "member-team-association-ready-to-team-association",
        topicName = MemberOnboardingReadyToTeamAssociationEvent.name,
        handler = memberConsumer::processAssociationByOnboarding
    )
    consume(
        handlerName = "member-activated-to-team-association",
        topicName = MemberActivatedEvent.name,
        handler = memberConsumer::processAssociationByMember
    )
    consume(
        handlerName = "move-to-phase-to-team-association",
        topicName = MoveToPhaseEvent.name,
        handler = memberConsumer::processAssociationByMoveToPhase
    )

    consume(
        handlerName = "onboarding-phase-changed-to-team-association",
        topicName = BeneficiaryOnboardingPhaseChangedEvent.name,
        handler = memberConsumer::processAssociationByBeneficiaryOnboardingPhaseChanged
    )

    val personClinicalAccountHistoryConsumer by inject<PersonClinicalAccountHistoryConsumer>()
    consume(
        handlerName = "person-clinical-account-created",
        topicName = PersonClinicalAccountCreatedEvent.name,
        handler = personClinicalAccountHistoryConsumer::consumeCreated
    )
    consume(
        handlerName = "person-clinical-account-deleted",
        topicName = PersonClinicalAccountDeleteEvent.name,
        handler = personClinicalAccountHistoryConsumer::consumeDeleted
    )
    consume(
        handlerName = "person-clinical-account-updated",
        topicName = PersonHealthcareTeamAssociationUpdatedEvent.name,
        handler = personClinicalAccountHistoryConsumer::consumeUpdated
    )
}
