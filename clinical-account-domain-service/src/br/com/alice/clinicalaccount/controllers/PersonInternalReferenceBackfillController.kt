package br.com.alice.clinicalaccount.controllers

import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.toResponse
import br.com.alice.person.client.MemberService
import java.util.UUID

class PersonInternalReferenceBackfillController(
    private val memberService: MemberService,
    private val personInternalReferenceService: PersonInternalReferenceService
) : ClinicalAccountController() {

    suspend fun createPersonInternalReference(request: CreatePersonInternalReferenceRequest) = withClinicalAccountEnvironment {
        val member = memberService.get(request.memberId).get()
        personInternalReferenceService.getForPerson(member.personId).get().toResponse()
    }

    data class CreatePersonInternalReferenceRequest(
        val memberId: UUID
    )

}
