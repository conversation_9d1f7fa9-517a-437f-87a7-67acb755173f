package br.com.alice.filevault.models

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.data.layer.models.EntityType
import br.com.alice.data.layer.models.FileVault
import java.io.InputStream
import java.io.Serializable
import java.util.UUID

interface VaultUpload {
    val domain: String
    val namespace: String?
    val originalFileName: String?
    val fileContent: InputStream
    val fileType: FileType
    val fileSize: Long?
    val referencedLinkId: String?
    val referecedLinkModel: FileVault.ReferenceLinkModel?
}

data class PersonVaultUpload(
    override val domain: String,
    override val namespace: String? = null,
    override val originalFileName: String? = null,
    override val fileContent: InputStream,
    override val fileType: FileType,
    val personId: PersonId,
    val shouldStoreAsPii: Boolean = false,
    override val fileSize: Long? = null,
    override val referencedLinkId: String? = null,
    override val referecedLinkModel: FileVault.ReferenceLinkModel? = null
) : Serializable, VaultUpload {

    fun getReferencedLink() =
        if (hasReferencedLink()) buildReferencedLink()
        else emptyList()

    private fun hasReferencedLink() = this.referencedLinkId.isNotNullOrBlank() && this.referecedLinkModel != null

    private fun buildReferencedLink() = listOf(
        FileVault.ReferencedLink(
            id = this.referencedLinkId!!,
            model = this.referecedLinkModel!!
        )
    )
}

interface VaultDelete {
    val domain: String
    val namespace: String?
    val fileName: String
}

data class PersonVaultDelete(
    override val domain: String,
    override val namespace: String? = null,
    override val fileName: String,
    val personId: PersonId,
    val shouldGetAsPii: Boolean = false,
) : Serializable, VaultDelete

data class GenericVaultDelete(
    override val domain: String,
    override val namespace: String? = null,
    override val fileName: String,
) : Serializable, VaultDelete

data class GenericVaultUpload(
    override val domain: String,
    override val namespace: String? = null,
    override val originalFileName: String? = null,
    override val fileContent: InputStream,
    override val fileType: FileType,
    val entityId: UUID?,
    val entityType: EntityType?,
    override val fileSize: Long? = null,
    override val referencedLinkId: String? = null,
    override val referecedLinkModel: FileVault.ReferenceLinkModel? = null
) : Serializable, VaultUpload

data class PersonVaultUploadByteArray(
    val domain: String,
    val namespace: String? = null,
    val originalFileName: String? = null,
    val fileContent: ByteArray,
    val fileType: FileType,
    val personId: PersonId,
    val fileSize: Long? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PersonVaultUploadByteArray

        if (domain != other.domain) return false
        if (namespace != other.namespace) return false
        if (originalFileName != other.originalFileName) return false
        if (!fileContent.contentEquals(other.fileContent)) return false
        if (fileType != other.fileType) return false
        if (personId != other.personId) return false
        if (fileSize != other.fileSize) return false

        return true
    }

    override fun hashCode(): Int {
        var result = domain.hashCode()
        result = 31 * result + (namespace?.hashCode() ?: 0)
        result = 31 * result + (originalFileName?.hashCode() ?: 0)
        result = 31 * result + fileContent.contentHashCode()
        result = 31 * result + fileType.hashCode()
        result = 31 * result + personId.hashCode()
        result = 31 * result + (fileSize?.hashCode() ?: 0)
        return result
    }
}

data class PersonVaultDeleteByteArray(
    val personId: PersonId,
    val domain: String,
    val namespace: String? = null,
    val fileName: String
)

data class GenericVaultDeleteByteArray(
    val domain: String,
    val namespace: String? = null,
    val fileName: String
)

data class GenericVaultUploadByteArray(
    val domain: String,
    val namespace: String? = null,
    val originalFileName: String? = null,
    val fileContent: ByteArray,
    val fileType: FileType,
    val entityId: UUID? = null,
    val entityType: EntityType? = null,
    val fileSize: Long? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is GenericVaultUploadByteArray) return false

        if (domain != other.domain) return false
        if (namespace != other.namespace) return false
        if (originalFileName != other.originalFileName) return false
        if (!fileContent.contentEquals(other.fileContent)) return false
        if (fileType != other.fileType) return false
        if (entityId != other.entityId) return false
        if (entityType != other.entityType) return false
        if (fileSize != other.fileSize) return false

        return true
    }

    override fun hashCode(): Int {
        var result = domain.hashCode()
        result = 31 * result + (namespace?.hashCode() ?: 0)
        result = 31 * result + (originalFileName?.hashCode() ?: 0)
        result = 31 * result + fileContent.contentHashCode()
        result = 31 * result + fileType.hashCode()
        result = 31 * result + (entityId?.hashCode() ?: 0)
        result = 31 * result + (entityType?.hashCode() ?: 0)
        result = 31 * result + (fileSize?.hashCode() ?: 0)
        return result
    }
}

enum class FileType(val extension: String, val contentType: String) {
    IMAGE_JPG("jpg", "image/jpg"),
    IMAGE_JPEG("jpeg", "image/jpeg"),
    IMAGE_PNG("png", "image/png"),
    PDF("pdf", "application/pdf"),
    AUDIO_MP3("mp3", "audio/mpeg"),
    AUDIO_WAV("wav", "audio/wav"),
    AUDIO_M4A("m4a", "audio/m4a"),
    VIDEO_MP4("mp4", "video/mp4"),
    VIDEO_3GP("3gp", "video/3gpp"),
    VIDEO_MOV("mov", "video/quicktime"),
    CSV("csv", "text/csv"),
    XML("xml", "application/xml"),
    DOC("doc", "application/msword"),
    XLS("xls", "application/vnd.ms-excel"),
    XLSX("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    DOCX("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    IMAGE_HEIC("heic", "image/heic");

    companion object {
        fun fromExtension(extension: String): FileType? = values().find { it.extension == extension.lowercase() }
        fun fromContentType(contentType: String): FileType? =
            values().find { it.contentType == contentType.lowercase() }
    }
}

data class VaultResponse(
    val url: String,
    val id: UUID,
    val type: String,
    val vaultUrl: String,
    val fileName: String? = null,
    val fileSize: Long? = null
)

data class VaultFileResponse(
    val id: UUID,
    val type: String,
    val fileBytes: ByteArray,
    val fileName: String? = null,
    val fileSize: Long? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VaultFileResponse

        if (id != other.id) return false
        if (type != other.type) return false
        if (!fileBytes.contentEquals(other.fileBytes)) return false
        if (fileName != other.fileName) return false
        if (fileSize != other.fileSize) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + fileBytes.contentHashCode()
        result = 31 * result + (fileName?.hashCode() ?: 0)
        result = 31 * result + (fileSize?.hashCode() ?: 0)
        return result
    }
}
