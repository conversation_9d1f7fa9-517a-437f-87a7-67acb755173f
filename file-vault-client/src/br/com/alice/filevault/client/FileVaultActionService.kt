package br.com.alice.filevault.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.filevault.models.GenericVaultDeleteByteArray
import br.com.alice.filevault.models.GenericVaultUploadByteArray
import br.com.alice.filevault.models.PersonVaultDeleteByteArray
import br.com.alice.filevault.models.PersonVaultUploadByteArray
import br.com.alice.filevault.models.VaultFileResponse
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FileVaultActionService : Service {
    override val namespace: String get() = "file_vault"
    override val serviceName: String get() = "upload"

    suspend fun uploadFile(model: PersonVaultUploadByteArray): Result<FileVault, Throwable>
    suspend fun uploadFileAndGenerateSecuredLink(model: PersonVaultUploadByteArray): Result<VaultResponse, Throwable>
    suspend fun deleteFile(model: PersonVaultDeleteByteArray): Result<Boolean, Throwable>

    suspend fun uploadGenericFile(model: GenericVaultUploadByteArray): Result<GenericFileVault, Throwable>
    suspend fun deleteGenericFile(model: GenericVaultDeleteByteArray): Result<Boolean, Throwable>

    suspend fun securedLink(fileVaultId: UUID): Result<VaultResponse, Throwable>
    suspend fun securedLinks(fileVaultIds: List<UUID>): Result<List<VaultResponse>, Throwable>
    suspend fun contentById(fileVaultId: UUID): Result<ByteArray, Throwable>

    suspend fun securedGenericLink(fileVaultId: UUID): Result<VaultResponse, Throwable>
    suspend fun securedGenericLinks(fileVaultIds: List<UUID>): Result<List<VaultResponse>, Throwable>

    suspend fun genericFileContentById(fileVaultId: UUID): Result<ByteArray, Throwable>
    suspend fun securedGenericFile(fileVaultId: UUID): Result<VaultFileResponse, Throwable>

    suspend fun securedLinkFromUrl(fileVaultUrl: String): Result<VaultResponse, Throwable>
}
