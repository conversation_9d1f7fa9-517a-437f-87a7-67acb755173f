package br.com.alice.filevault.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.FileVault
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FileVaultService: Service {

    override val namespace: String get() = "file_vault"

    override val serviceName: String get() = "file_vault"

    suspend fun getByPersonAndReferencedLink(
        personId: PersonId,
        referencedLink: FileVault.ReferencedLink
    ): Result<List<FileVault>, Throwable>

    suspend fun updateFromReference(
        id: UUID,
        referencedLink: FileVault.ReferencedLink
    ): Result<FileVault, Throwable>
}
