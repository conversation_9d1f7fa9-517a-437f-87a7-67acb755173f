package br.com.alice.person.controllers

import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory.buildMember
import br.com.alice.data.layer.helpers.TestModelFactory.buildPerson
import br.com.alice.data.layer.helpers.TestModelFactory.buildPersonInternalReference
import br.com.alice.data.layer.helpers.TestModelFactory.buildProduct
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.BeforeTest

class ProductInfoControllerTest : ControllerTestHelper() {
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val personService: PersonService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(memberService, productService, personService, personInternalReferenceService)
        module.single {
            ProductInfoController(
                memberService,
                productService,
                personService,
                personInternalReferenceService
            )
        }
    }

    private val person = buildPerson()
    private val product = buildProduct()
    private val member = buildMember(personId = person.id, productId = product.id, status = MemberStatus.ACTIVE)
    private val productInfo = ProductInfo(
        brand = product.brand!!,
        primaryAttention = product.primaryAttention!!,
        tier = product.tier!!,
        coPayment = product.coPayment,
        healthcareModelType = product.healthcareModelType,
        refund = product.refund,
        productType = product.type
    )
    private val personInternal = buildPersonInternalReference(personId = person.id)
    private val requestBodyByPersonIds = PersonInfoByPersonIdsRequest(
        personIds = listOf(person.id.toString()),
        changeModelTo = HealthcareModelType.V3
    )
    private val requestBodyByInternalCodes = PersonInfoByInternalCodeRequest(
        internalCodes = listOf(personInternal.internalCode),
        changeModelTo = HealthcareModelType.V3
    )

    @Test
    fun `updateProductInfo should update person with new model`() {
        val expectedResponse = PersonInfoResponse(success = 1, error = 0)
        val productInfo = productInfo.copy(healthcareModelType = requestBodyByPersonIds.changeModelTo!!)
        val personToUpdate = person.copy(productInfo = productInfo)

        coEvery { personService.findByIds(requestBodyByPersonIds.personIds) } returns listOf(person).success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery { productService.getProduct(member.productId) } returns product.success()
        coEvery { personService.update(personToUpdate) } returns personToUpdate.success()

        post(to = "/backfill/person_product_info/by_person_ids", body = requestBodyByPersonIds) { response ->
            ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
        }
        coVerifyOnce {
            personService.findByIds(any())
            memberService.getCurrent(any())
            productService.getProduct(any())
            personService.update(any())
        }
    }

    @Test
    fun `updateProductInfo should update person with product information`() {
        val requestBody = requestBodyByPersonIds.copy(changeModelTo = null)
        val expectedResponse = PersonInfoResponse(success = 1, error = 0)
        val personToUpdate = person.copy(productInfo = productInfo)

        coEvery { personService.findByIds(requestBody.personIds) } returns listOf(person).success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery { productService.getProduct(member.productId) } returns product.success()
        coEvery { personService.update(personToUpdate) } returns personToUpdate.success()

        post(to = "/backfill/person_product_info/by_person_ids", body = requestBody) { response ->
            ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
        }
        coVerifyOnce {
            personService.findByIds(any())
            memberService.getCurrent(any())
            productService.getProduct(any())
            personService.update(any())
        }
    }

    @Test
    fun `updateProductInfo should return 1 error when tier is null`() {
        val product = buildProduct(tier = null)
        val expectedResponse = PersonInfoResponse(success = 0, error = 1)

        coEvery { personService.findByIds(requestBodyByPersonIds.personIds) } returns listOf(person).success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery { productService.getProduct(member.productId) } returns product.success()

        post(to = "/backfill/person_product_info/by_person_ids", body = requestBodyByPersonIds) { response ->
            ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
        }

        coVerifyOnce {
            personService.findByIds(any())
            memberService.getCurrent(any())
            productService.getProduct(any())
        }
    }

    @Test
    fun `updateProductInfoByInternalCode should update person with new model`() {
        val expectedResponse = PersonInfoResponse(success = 1, error = 0)
        val productInfo = productInfo.copy(healthcareModelType = requestBodyByPersonIds.changeModelTo!!)
        val personToUpdate = person.copy(productInfo = productInfo)

        coEvery { personInternalReferenceService.getByInternalCodes(requestBodyByInternalCodes.internalCodes) } returns
                listOf(personInternal).success()
        coEvery { personService.findByIds(requestBodyByPersonIds.personIds) } returns listOf(person).success()
        coEvery { memberService.getCurrent(person.id) } returns member.success()
        coEvery { productService.getProduct(member.productId) } returns product.success()
        coEvery { personService.update(personToUpdate) } returns personToUpdate.success()

        post(to = "/backfill/person_product_info/by_internal_code", body = requestBodyByInternalCodes) { response ->
            ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
        }
        coVerifyOnce {
            personInternalReferenceService.getByInternalCodes(any())
            personService.findByIds(any())
            memberService.getCurrent(any())
            productService.getProduct(any())
            personService.update(any())
        }
    }
}
