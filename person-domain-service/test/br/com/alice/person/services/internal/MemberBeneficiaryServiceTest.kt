package br.com.alice.person.services.internal

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.MemberBeneficiary
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.person.br.com.alice.person.exception.BeneficiaryDataIsOlderThanTheOneOnMember
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class MemberBeneficiaryServiceTest : MockedTestHelper() {
    private val memberService: MemberService = mockk()
    private val service = MemberBeneficiaryService(memberService)

    private val beneficiary = TestModelFactory.buildBeneficiary()
    private val currentMember = TestModelFactory.buildMember(
        id = beneficiary.memberId,
        beneficiaryId = beneficiary.id,
        companyId = beneficiary.companyId,
        companySubContractId = beneficiary.companySubContractId,
        beneficiary = MemberBeneficiary(
            parentBeneficiary = beneficiary.parentBeneficiary,
            type = beneficiary.type,
            contractType = beneficiary.contractType,
            parentBeneficiaryRelationType = beneficiary.parentBeneficiaryRelationType,
            activatedAt = beneficiary.activatedAt,
            canceledAt = beneficiary.canceledAt,
            hiredAt = beneficiary.hiredAt,
            parentBeneficiaryRelatedAt = beneficiary.parentBeneficiaryRelatedAt,
            canceledReason = beneficiary.canceledReason,
            canceledDescription = beneficiary.canceledDescription,
            cnpj = beneficiary.cnpj,
            hasContributed = beneficiary.hasContributed,
            version = beneficiary.version,
            gracePeriodType = beneficiary.gracePeriodType,
            gracePeriodTypeReason = beneficiary.gracePeriodTypeReason,
            gracePeriodBaseDate = beneficiary.gracePeriodBaseDate,
        )
    )

    @Test
    fun `#syncBeneficiary should update member if beneficiary is not null and the version is greater`() = runBlocking {
        val toUpdate = beneficiary.copy(
            companyId = RangeUUID.generate(),
            companySubContractId = RangeUUID.generate(),
            parentBeneficiary = RangeUUID.generate(),
            type = BeneficiaryType.DEPENDENT,
            contractType = BeneficiaryContractType.PJ,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            activatedAt = LocalDateTime.now(),
            canceledAt = LocalDateTime.now(),
            hiredAt = LocalDateTime.now(),
            parentBeneficiaryRelatedAt = LocalDateTime.now(),
            canceledReason = BeneficiaryCancelationReason.ANOTHER_COMPANY,
            canceledDescription = "canceledDescription2",
            cnpj = "cnpj2",
            hasContributed = false,
            version = beneficiary.version + 1,
            gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
            gracePeriodTypeReason = GracePeriodTypeReason.PORTABILITY,
            gracePeriodBaseDate = LocalDate.now().plusDays(1)
        )
        val memberToUpdate = currentMember.copy(
            beneficiaryId = toUpdate.id,
            companyId = toUpdate.companyId,
            companySubContractId = toUpdate.companySubContractId,
            beneficiary = currentMember.beneficiary?.copy(
                parentBeneficiary = toUpdate.parentBeneficiary,
                type = toUpdate.type,
                contractType = toUpdate.contractType,
                parentBeneficiaryRelationType = toUpdate.parentBeneficiaryRelationType,
                activatedAt = toUpdate.activatedAt,
                canceledAt = toUpdate.canceledAt,
                hiredAt = toUpdate.hiredAt,
                parentBeneficiaryRelatedAt = toUpdate.parentBeneficiaryRelatedAt,
                canceledReason = toUpdate.canceledReason,
                canceledDescription = toUpdate.canceledDescription,
                cnpj = toUpdate.cnpj,
                hasContributed = toUpdate.hasContributed,
                version = toUpdate.version,
                gracePeriodType = toUpdate.gracePeriodType,
                gracePeriodTypeReason = toUpdate.gracePeriodTypeReason,
                gracePeriodBaseDate = toUpdate.gracePeriodBaseDate,
            )
        )

        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()
        coEvery { memberService.update(memberToUpdate) } returns
                memberToUpdate.success()

        assertThat(service.syncBeneficiary(toUpdate))
            .isSuccessWithData(memberToUpdate)

        coVerify {
            memberService.get(any())
            memberService.update(any())
        }
    }

    @Test
    fun `#syncBeneficiary should update member if beneficiary is null`() = runBlocking {
        coEvery { memberService.get(currentMember.id) } returns
                currentMember.copy(beneficiary = null).success()
        coEvery {
            memberService.update(
                currentMember.copy(
                    beneficiary = currentMember.beneficiary?.copy(version = beneficiary.version + 1)
                )
            )
        } returns currentMember.copy(
            beneficiary = currentMember.beneficiary?.copy(version = beneficiary.version + 1)
        ).success()

        assertThat(service.syncBeneficiary(
            beneficiary.copy(
                version = beneficiary.version + 1
            )
        )).isSuccessWithData(
            currentMember.copy(
                beneficiary = currentMember.beneficiary?.copy(version = beneficiary.version + 1)
            )
        )

        coVerify {
            memberService.get(any())
            memberService.update(any())
        }
    }

    @Test
    fun `#syncBeneficiary shouldn't update member if version isn't greater`() = runBlocking {
        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()

        assertThat(service.syncBeneficiary(beneficiary))
            .isFailureOfType(BeneficiaryDataIsOlderThanTheOneOnMember::class)

        coVerify { memberService.get(any()) }
        coVerifyNone { memberService.update(any()) }
    }

    @Test
    fun `#syncBeneficiary shouldn't update member if member not found`() = runBlocking {
        coEvery { memberService.get(currentMember.id) } returns
                NotFoundException().failure()

        assertThat(service.syncBeneficiary(beneficiary))
            .isFailureOfType(NotFoundException::class)

        coVerify { memberService.get(any()) }
        coVerifyNone { memberService.update(any()) }
    }
}
