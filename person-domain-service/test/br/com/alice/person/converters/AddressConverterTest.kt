package br.com.alice.person.converters

import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.AddressModel
import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test

class AddressConverterTest {

    private val address = Address(
        state = State.SP,
        city = "Cidade",
        street = "Rua 1",
        number = "123",
        complement = "Complemento",
        neighbourhood = "Bairro",
        postalCode = "12345-678",
        lat = 1.0,
        lng = 2.0
    )

    private val addressModel = AddressModel(
        state = State.SP,
        city = "Cidade",
        street = "Rua 1",
        number = "123",
        complement = "Complemento",
        neighbourhood = "Bairro",
        postalCode = "12345-678",
        lat = 1.0,
        lng = 2.0
    )

    @Test
    fun testToModel() {
        assertEquals(addressModel, address.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(address, addressModel.toTransport())
    }
}
