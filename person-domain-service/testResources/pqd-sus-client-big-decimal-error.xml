<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
    <S:Header xmlns:S="http://www.w3.org/2003/05/soap-envelope">
        <work:WorkContext xmlns:work="http://oracle.com/weblogic/soap/workarea/">rO0ABXdaABV3ZWJsb2dpYy5hcHAuQ0FEU1VTNTAAAADWAAAAI3dlYmxvZ2ljLndvcmthcmVhLlN0cmluZ1dvcmtDb250ZXh0ABQyLjAuMTFfMjAyMTEyMDIuMTg0OAAA</work:WorkContext>
    </S:Header>
    <S:Body xmlns:S="http://www.w3.org/2003/05/soap-envelope">
        <PRPA_IN201306UV02 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3">
            <id root="5560e778-db16-4c83-8d93-93f9dcfd91ba"/>
            <creationTime value="20240123102354"/>
            <interactionId root="2.16.840.1.113883.1.6" extension="PRPA_IN201306UV02"/>
            <processingCode code="T"/>
            <processingModeCode code="T"/>
            <acceptAckCode code="NE"/>
            <receiver typeCode="RCV">
                <device classCode="DEV" determinerCode="INSTANCE">
                    <id root="2.16.840.1.113883.3.72.6.2"/>
                    <name>[SYSTEMCODE]</name>
                </device>
            </receiver>
            <sender typeCode="SND">
                <device classCode="DEV" determinerCode="INSTANCE">
                    <id root="2.16.840.1.113883.3.72.6.5.100.85"/>
                </device>
            </sender>
            <acknowledgement>
                <typeCode code="AA"/>
                <targetMessage>
                    <id root="2.16.840.1.113883.4.714" extension="123456"/>
                </targetMessage>
            </acknowledgement>
            <controlActProcess classCode="CACT" moodCode="EVN">
                <code code="PRPA_TE201306UV02"/>
                <subject typeCode="SUBJ" contextConductionInd="false">
                    <registrationEvent classCode="REG" moodCode="EVN">
                        <statusCode code="completed"/>
                        <subject1 typeCode="SBJ">
                            <realmCode code="SBJ"/>
                            <patient classCode="PAT">
                                <id root="2.16.840.1.113883.3.4594.2" extension="216794055" assigningAuthorityName="CADSUS-BULK"/>
                                <id root="2.16.840.1.113883.3.4594.9" extension="12869993595" assigningAuthorityName="CARGA-ANS"/>
                                <id root="2.16.840.1.113883.3.4594.10" extension="0196180a-6625-46c8-aa67-61398d300fca" assigningAuthorityName="CADSUS-ESUSAB"/>
                                <id root="2.16.840.1.113883.13.37" extension="898001454794781" assigningAuthorityName="SIGA-SP"/>
                                <id root="2.16.840.1.113883.3.4594" extension="**********" assigningAuthorityName="RES-BRASIL"/>
                                <statusCode code="active"/>
                                <confidentialityCode code="N" codeSystem="2.16.840.1.113883.5.25"/>
                                <patientPerson classCode="PSN" determinerCode="INSTANCE">
                                    <name use="L">
                                        <given>MARIANA IRMA DOS SANTOS</given>
                                    </name>
                                    <telecom use="PRN" value="+55-11-26418606"/>
                                    <administrativeGenderCode code="F" codeSystem="2.16.840.1.113883.5.1"/>
                                    <birthTime value="19870104000000"/>
                                    <addr use="H">
                                        <city>355030</city>
                                        <state>SP</state>
                                        <postalCode>03732010</postalCode>
                                        <country>010</country>
                                        <houseNumber>196</houseNumber>
                                        <streetName>FAUSTINO PAGANINI</streetName>
                                        <streetNameType>081</streetNameType>
                                        <additionalLocator>CHACARA CRUZ DO SUL</additionalLocator>
                                    </addr>
                                    <raceCode code="01"/>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.236" extension="707609272531795"/>
                                        <id root="2.16.840.1.113883.13.236.1" extension="D"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.236"/>
                                            <id root="2.16.840.1.113883.13.236.1"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.236" extension="980016293258059"/>
                                        <id root="2.16.840.1.113883.13.236.1" extension="P"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.236"/>
                                            <id root="2.16.840.1.113883.13.236.1"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.236" extension="898001454794781"/>
                                        <id root="2.16.840.1.113883.13.236.1" extension="P"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.236"/>
                                            <id root="2.16.840.1.113883.13.236.1"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.237" extension="36049203881"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.237"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.243" extension="439346861"/>
                                        <id root="2.16.840.1.113883.13.245" extension="10"/>
                                        <id root="2.16.840.1.113883.4.707" extension="SP"/>
                                        <id root="2.16.840.1.113883.13.243.1" extension="20060925000000.0-0300"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.243"/>
                                            <id root="2.16.840.1.113883.13.245"/>
                                            <id root="2.16.840.1.113883.4.707"/>
                                            <id root="2.16.840.1.113883.13.243.1"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <asOtherIDs classCode="ROL">
                                        <id root="2.16.840.1.113883.13.243" extension="00439346861"/>
                                        <id root="2.16.840.1.113883.13.245" extension="66"/>
                                        <id root="2.16.840.1.113883.4.707" extension="SP"/>
                                        <id root="2.16.840.1.113883.13.243.1" extension="20060928000000.0-0300"/>
                                        <scopingOrganization classCode="ORG" determinerCode="INSTANCE">
                                            <id root="2.16.840.1.113883.13.243"/>
                                            <id root="2.16.840.1.113883.13.245"/>
                                            <id root="2.16.840.1.113883.4.707"/>
                                            <id root="2.16.840.1.113883.13.243.1"/>
                                        </scopingOrganization>
                                    </asOtherIDs>
                                    <personalRelationship classCode="PRS">
                                        <code code="PRN" codeSystem="2.16.840.1.113883.1.11.19563"/>
                                        <relationshipHolder1 classCode="PSN" determinerCode="INSTANCE">
                                            <name xsi:type="PN" use="L" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                <given>ELIANA APARECIDA GERALDO DOS SANTOS</given>
                                            </name>
                                        </relationshipHolder1>
                                    </personalRelationship>
                                    <personalRelationship classCode="PRS">
                                        <code code="NPRN" codeSystem="2.16.840.1.113883.1.11.19563"/>
                                        <relationshipHolder1 classCode="PSN" determinerCode="INSTANCE">
                                            <name xsi:type="PN" use="L" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                <given>IOGENE JOSE DOS SANTOS</given>
                                            </name>
                                        </relationshipHolder1>
                                    </personalRelationship>
                                    <birthPlace classCode="BIRTHPL">
                                        <addr>
                                            <city>355030</city>
                                            <country>010</country>
                                        </addr>
                                    </birthPlace>
                                    <languageCommunication>
                                        <languageCode code="pt" codeSystem="2.16.840.1.113883.6.100"/>
                                        <preferenceInd value="true"/>
                                    </languageCommunication>
                                </patientPerson>
                                <providerOrganization classCode="ORG" determinerCode="INSTANCE">
                                    <id root="2.16.840.1.113883.3.4594.2"/>
                                    <id root="2.16.840.1.113883.3.4594.9"/>
                                    <id root="2.16.840.1.113883.3.4594.10"/>
                                    <id root="2.16.840.1.113883.13.37"/>
                                    <id root="2.16.840.1.113883.3.4594"/>
                                    <contactParty classCode="CON"/>
                                </providerOrganization>
                                <subjectOf1>
                                    <queryMatchObservation classCode="OBS" moodCode="EVN">
                                        <code code="IHE_PDQ"/>
                                        <value xsi:type="INT" value="100" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
                                    </queryMatchObservation>
                                </subjectOf1>
                            </patient>
                        </subject1>
                        <custodian typeCode="CST">
                            <assignedEntity classCode="ASSIGNED">
                                <id root="2.16.840.1.113883.3.4594.2"/>
                            </assignedEntity>
                        </custodian>
                    </registrationEvent>
                </subject>
                <queryAck>
                    <queryId root="1.2.840.114350.1.13.28.1.18.5.999" extension="29549351890"/>
                    <queryResponseCode code="OK"/>
                    <resultTotalQuantity value="1"/>
                    <resultCurrentQuantity value="1"/>
                    <resultRemainingQuantity value="0"/>
                </queryAck>
                <queryByParameter>
                    <queryId root="1.2.840.114350.1.13.28.1.18.5.999" extension="29549351890"/>
                    <statusCode code="new"/>
                    <responseModalityCode code="R"/>
                    <responsePriorityCode code="I"/>
                    <parameterList>
                        <livingSubjectId>
                            <value root="2.16.840.1.113883.13.237" extension="36049203881"/>
                            <semanticsText>LivingSubject.id</semanticsText>
                        </livingSubjectId>
                    </parameterList>
                </queryByParameter>
            </controlActProcess>
        </PRPA_IN201306UV02>
    </S:Body>
</soap:Envelope>
