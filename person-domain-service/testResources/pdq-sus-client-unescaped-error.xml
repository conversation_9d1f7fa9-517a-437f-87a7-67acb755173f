<soap:Envelope
        xmlns:env=\"http://www.w3.org/2003/05/soap-envelope\"
        xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\">
<S:Header
xmlns:S=\"http://www.w3.org/2003/05/soap-envelope\">
<WorkContext
xmlns=\"http://oracle.com/weblogic/soap/workarea/\"
        xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"
        xmlns:S=\"http://www.w3.org/2003/05/soap-envelope\">rO0ABXdcABh3ZWJsb2dpYy5hcHAub2htcGlfNS4wLjMAAADWAAAAI3dlYmxvZ2ljLndvcmthcmVhLlN0cmluZ1dvcmtDb250ZXh0ABM1LjAuM18yMDI0MDcwOS4yMjMwAAA=
        </WorkContext>
        </S:Header>
<S:Body
xmlns:S=\"http://www.w3.org/2003/05/soap-envelope\">
<PRPA_IN201306UV02
xmlns=\"urn:hl7-org:v3\" ITSVersion=\"XML_1.0\">
<id root=\"1abe83df-eb3a-4caf-be58-5de61846f8e8\"/>
<creationTime value=\"20241001182605\"/>
<interactionId root=\"2.16.840.1.113883.1.6\" extension=\"PRPA_IN201306UV02\"/>
<processingCode code=\"T\"/>
<processingModeCode code=\"T\"/>
<acceptAckCode code=\"NE\"/>
<receiver typeCode=\"RCV\">
<device classCode=\"DEV\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.3.72.6.2\"/>
<name>[SYSTEMCODE]</name>
        </device>
        </receiver>
<sender typeCode=\"SND\">
<device classCode=\"DEV\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.3.72.6.5.100.85\"/>
        </device>
        </sender>
<acknowledgement>
<typeCode code=\"AA\"/>
<targetMessage>
    <id root=\"2.16.840.1.113883.4.714\" extension=\"123456\"/>
</targetMessage>
</acknowledgement>
<controlActProcess classCode=\"CACT\" moodCode=\"EVN\">
<code code=\"PRPA_TE201306UV02\"/>
<subject typeCode=\"SUBJ\" contextConductionInd=\"false\">
<registrationEvent classCode=\"REG\" moodCode=\"EVN\">
<statusCode code=\"completed\"/>
<subject1 typeCode=\"SBJ\">
<realmCode code=\"SBJ\"/>
<patient classCode=\"PAT\">
<id root=\"2.16.840.1.113883.3.4594.2\" extension=\"222216560\" assigningAuthorityName=\"CADSUS-BULK\"/>
<id root=\"2.16.840.1.113883.3.4594.9\" extension=\"22288930306\" assigningAuthorityName=\"CARGA-ANS\"/>
<id root=\"2.16.840.1.113883.3.4594.2\" extension=\"**********\" assigningAuthorityName=\"CADSUS-BULK\"/>
<id root=\"2.16.840.1.113883.3.4594.1\" extension=\"**********\" assigningAuthorityName=\"CADSUS-UPDATE\"/>
<id root=\"2.16.840.1.113883.3.4594\" extension=\"**********\" assigningAuthorityName=\"RES-BRASIL\"/>
<statusCode code=\"active\"/>
<patientPerson classCode=\"PSN\" determinerCode=\"INSTANCE\">
<name use=\"L\">
<given>MARCO ANTONIO DE OLIVEIRA COSTA</given>
        </name>
<telecom value=\"+55-31-38239489\" use=\"PRN\"/>
<administrativeGenderCode code=\"M\" codeSystem=\"2.16.840.1.113883.5.1\"/>
<birthTime value=\"19970602000000\"/>
<addr use=\"H\">
<city>313130</city>
<postalCode>35164165</postalCode>
<country>010</country>
<houseNumber>76</houseNumber>
<streetName>GALILEIA</streetName>
<streetNameType>081</streetNameType>
<additionalLocator>CANAA</additionalLocator>
        </addr>
<raceCode code=\"01\"/>
<asOtherIDs classCode=\"ROL\">
<id root=\"2.16.840.1.113883.13.236\" extension=\"704602613795621\"/>
<id root=\"2.16.840.1.113883.13.236.1\" extension=\"D\"/>
<scopingOrganization classCode=\"ORG\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.13.236\"/>
<id root=\"2.16.840.1.113883.13.236.1\"/>
        </scopingOrganization>
        </asOtherIDs>
<asOtherIDs classCode=\"ROL\">
<id root=\"2.16.840.1.113883.13.236\" extension=\"898003412726046\"/>
<id root=\"2.16.840.1.113883.13.236.1\" extension=\"P\"/>
<scopingOrganization classCode=\"ORG\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.13.236\"/>
<id root=\"2.16.840.1.113883.13.236.1\"/>
        </scopingOrganization>
        </asOtherIDs>
<asOtherIDs classCode=\"ROL\">
<id root=\"2.16.840.1.113883.13.237\" extension=\"14337864601\"/>
<scopingOrganization classCode=\"ORG\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.13.237\"/>
        </scopingOrganization>
        </asOtherIDs>
<asOtherIDs classCode=\"ROL\">
<id root=\"2.16.840.1.113883.13.241.2\" extension=\"null\"/>
<id root=\"2.16.840.1.113883.4.706.1\" extension=\"CIVIL\"/>
<id root=\"2.16.840.1.113883.4.706.2\" extension=\"39\"/>
<id root=\"2.16.840.1.113883.4.706.3\" extension=\"39\"/>
<id root=\"2.16.840.1.113883.4.706.4\" extension=\"36757\"/>
<id root=\"2.16.840.1.113883.4.706.5\" extension=\"19980604000000.0-0300\"/>
<scopingOrganization classCode=\"ORG\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.13.241.2\"/>
<id root=\"2.16.840.1.113883.4.706.1\"/>
<id root=\"2.16.840.1.113883.4.706.2\"/>
<id root=\"2.16.840.1.113883.4.706.3\"/>
<id root=\"2.16.840.1.113883.4.706.4\"/>
<id root=\"2.16.840.1.113883.4.706.5\"/>
        </scopingOrganization>
        </asOtherIDs>
<personalRelationship classCode=\"PRS\">
<code code=\"PRN\" codeSystem=\"2.16.840.1.113883.1.11.19563\"/>
<relationshipHolder1 classCode=\"PSN\" determinerCode=\"INSTANCE\">
<name
xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" use=\"L\" xsi:type=\"PN\">
<given>MARCIA ADRIENE DE OLIVEIRA COSTA</given>
        </name>
        </relationshipHolder1>
        </personalRelationship>
<personalRelationship classCode=\"PRS\">
<code code=\"NPRN\" codeSystem=\"2.16.840.1.113883.1.11.19563\"/>
<relationshipHolder1 classCode=\"PSN\" determinerCode=\"INSTANCE\">
<name
xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" use=\"L\" xsi:type=\"PN\">
<given>EVERALDO TEIXEIRA DA COSTA</given>
        </name>
        </relationshipHolder1>
        </personalRelationship>
<birthPlace classCode=\"BIRTHPL\">
<addr>
<city>313130</city>
<country>010</country>
</addr>
        </birthPlace>
        </patientPerson>
<providerOrganization classCode=\"ORG\" determinerCode=\"INSTANCE\">
<id root=\"2.16.840.1.113883.3.4594.2\"/>
<id root=\"2.16.840.1.113883.3.4594.9\"/>
<id root=\"2.16.840.1.113883.3.4594.2\"/>
<id root=\"2.16.840.1.113883.3.4594.1\"/>
<id root=\"2.16.840.1.113883.3.4594\"/>
<contactParty classCode=\"CON\"/>
        </providerOrganization>
<subjectOf1>
<queryMatchObservation classCode=\"OBS\" moodCode=\"EVN\">
<code code=\"IHE_PDQ\"/>
<value
        xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" value=\"100\" xsi:type=\"INT\"/>
</queryMatchObservation>
        </subjectOf1>
        </patient>
        </subject1>
<custodian typeCode=\"CST\">
<assignedEntity classCode=\"ASSIGNED\">
<id root=\"2.16.840.1.113883.3.4594.2\"/>
        </assignedEntity>
        </custodian>
        </registrationEvent>
        </subject>
<queryAck>
<queryId root=\"1.2.840.114350.1.13.28.1.18.5.999\" extension=\"46576270828\"/>
<statusCode code=\"deliveredResponse\"/>
<queryResponseCode code=\"OK\"/>
<resultTotalQuantity value=\"1\"/>
<resultCurrentQuantity value=\"1\"/>
<resultRemainingQuantity value=\"0\"/>
</queryAck>
<queryByParameter>
<queryId root=\"1.2.840.114350.1.13.28.1.18.5.999\" extension=\"46576270828\"/>
<statusCode code=\"new\"/>
<responseModalityCode code=\"R\"/>
<responsePriorityCode code=\"I\"/>
<parameterList>
    <livingSubjectId>
        <value root=\"2.16.840.1.113883.13.237\" extension=\"14337864601\"/>
        <semanticsText>LivingSubject.id</semanticsText>
    </livingSubjectId>
</parameterList>
</queryByParameter>
        </controlActProcess>
        </PRPA_IN201306UV02>
        </S:Body>
        </soap:Envelope>
