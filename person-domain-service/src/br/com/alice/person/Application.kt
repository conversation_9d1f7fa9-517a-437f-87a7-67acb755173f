package br.com.alice.person

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.observability.opentelemetry.OpenTelemetryClient
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.data.layer.PERSON_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.client.MemberProductPriceAdjustmentService
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonGracePeriodService
import br.com.alice.person.client.PersonIdentityValidationService
import br.com.alice.person.client.PersonService
import br.com.alice.person.client.UserTypeService
import br.com.alice.person.clients.SusClient
import br.com.alice.person.clients.SusClientImpl
import br.com.alice.person.consumers.BeneficiaryChangedConsumer
import br.com.alice.person.consumers.MemberChangeConsumer
import br.com.alice.person.consumers.PersonProductInfoUpdateConsumer
import br.com.alice.person.consumers.ProductChangedConsumer
import br.com.alice.person.consumers.ProductUpdateConsumer
import br.com.alice.person.consumers.SusCheckConsumers
import br.com.alice.person.controllers.PersonBackfillController
import br.com.alice.person.controllers.ProductInfoController
import br.com.alice.person.ioc.DataLayerServiceModule
import br.com.alice.person.ioc.ServiceModule
import br.com.alice.person.metrics.Metrics
import br.com.alice.person.routes.apiRoutes
import br.com.alice.person.routes.kafkaRoutes
import br.com.alice.person.services.FileVaultHttpClient
import br.com.alice.person.services.MemberProductPriceAdjustmentServiceImpl
import br.com.alice.person.services.MemberProductPriceServiceImpl
import br.com.alice.person.services.MemberServiceImpl
import br.com.alice.person.services.PersonGracePeriodServiceImpl
import br.com.alice.person.services.PersonIdentityValidationServiceImpl
import br.com.alice.person.services.PersonServiceImpl
import br.com.alice.person.services.UserTypeServiceImpl
import br.com.alice.person.services.internal.MemberBeneficiaryService
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.amazonaws.services.lambda.AWSLambdaClientBuilder
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.application.Application
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private const val TIMEOUT_IN_MILLIS = 300L

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        CommunicationModule,
        BusinessDomainClientModule,
        KafkaProducerModule,
        MembershipClientModule,
        NotificationModule,
        ServiceModule,
        DataLayerServiceModule,
        MaraudersMapDomainClientModule,
        HealthPlanDomainClientModule,
        StaffDomainClientModule,
        ProductDomainClientModule,
        ClinicalAccountDomainClientModule,
        GoogleMapsModule,
        FileVaultClientModule,

        module(createdAtStart = true) {
            // Configuration
            single {
                DefaultHttpClient({
                    install(ContentNegotiation) { simpleGson() }
                    install(OpenTelemetryClient)
                }, timeoutInMillis = TIMEOUT_IN_MILLIS)
            }

            //Health Controller
            single { HealthController(SERVICE_NAME) }

            //Load services
            loadServiceServers("br.com.alice.person.services")

            // Exposed Services
            single<PersonService> { PersonServiceImpl(get(), get(), get()) }
            single<PersonGracePeriodService> { PersonGracePeriodServiceImpl(get(), get(), get()) }
            single<UserTypeService> { UserTypeServiceImpl(get(), get()) }
            single<MemberService> {
                MemberServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<MemberProductPriceService> {
                MemberProductPriceServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<MemberProductPriceAdjustmentService> {
                MemberProductPriceAdjustmentServiceImpl(
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<PersonIdentityValidationService> {
                PersonIdentityValidationServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    FileVaultHttpClient
                )
            }
            //Internal Services
            single<MemberBeneficiaryService> { MemberBeneficiaryService(get()) }
            // Consumers
            single<SusClient> {
                SusClientImpl(
                    ServiceConfig.Sus.susConfig(),
                    AWSLambdaClientBuilder.standard().withRegion("sa-east-1").build()
                )
            }
            single { SusCheckConsumers(get(), get()) }
            single { ProductChangedConsumer(get()) }
            single { MemberChangeConsumer(get()) }
            single { PersonProductInfoUpdateConsumer(get(), get(), get()) }
            single { ProductUpdateConsumer(get(), get()) }
            single { BeneficiaryChangedConsumer(get()) }

            //Backfill
            single { ProductInfoController(get(), get(), get(), get()) }
            single { PersonBackfillController(get(), get(), get(), get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, PERSON_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.ALICE_APP,
            FeatureNamespace.MEMBERSHIP,
            FeatureNamespace.PERSON
        )
        Metrics.registerMetrics()
    }
}
