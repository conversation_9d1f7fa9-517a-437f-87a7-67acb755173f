package br.com.alice.zendeskintegrationservice.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ZendeskExternalReference
import br.com.alice.person.model.events.PersonCreatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.zendesk.events.ShouldSyncMemberOnZendeskEvent
import br.com.alice.zendeskintegrationservice.exceptions.PersonWithoutCurrentMemberException
import br.com.alice.zendeskintegrationservice.exceptions.SyncedAtWasBeforeLastUpdate
import br.com.alice.zendeskintegrationservice.services.internal.UpsertZendeskUserService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class ZendeskPersonEventsConsumerTest : ConsumerTest() {
    private val producer: KafkaProducerService = mockk()
    private val zendeskUserUpsertService: UpsertZendeskUserService = mockk()

    private val consumer = ZendeskPersonEventsConsumer(producer, zendeskUserUpsertService)

    private val now = LocalDateTime.now()

    private val person = TestModelFactory.buildPerson(
        dateOfBirth = LocalDateTime.of(1992, 10, 18, 10, 10),
        sex = Sex.FEMALE
    )

    private val event = ShouldSyncMemberOnZendeskEvent(
        person.id,
        now.minusDays(4)
    )

    @Test
    fun `#personConsumer should upsert person on Zendesk when person updated event`() = runBlocking {
        val expected = ShouldSyncMemberOnZendeskEvent(person.id, person.updatedAt)

        coEvery { producer.produce(expected, person.id.toString()) } returns mockk()

        val result = consumer.personUpdatedUpsertZendesk(PersonUpdatedEvent(person))

        assertThat(result).isSuccess()

        coVerifyOnce { producer.produce(any(), any()) }
    }

    @Test
    fun `#personConsumer should upsert person on Zendesk when person created event`() = runBlocking {
        val expected = ShouldSyncMemberOnZendeskEvent(person.id, person.updatedAt)

        coEvery { producer.produce(expected, person.id.toString()) } returns mockk()

        val result = consumer.personCreatedUpsertZendesk(PersonCreatedEvent(person))

        assertThat(result).isSuccess()

        coVerifyOnce { producer.produce(any(), any()) }
    }

    @Test
    fun `#syncMemberOnZendesk should upsert person on Zendesk when member should sync`() = runBlocking {
        coEvery {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(
                person.id,
                event.payload.entityUpdatedAt
            )
        } returns true.success()

        val result = consumer.syncMemberOnZendesk(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(any(), any()) }
    }

    @Test
    fun `#syncMemberOnZendesk should return false`() = runBlocking {
        coEvery {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(
                person.id,
                event.payload.entityUpdatedAt
            )
        } returns false.success()

        val result = consumer.syncMemberOnZendesk(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(any(), any()) }
    }

    @Test
    fun `#syncMemberOnZendesk should return success when PersonWithoutCurrentMemberException`() = runBlocking {
        coEvery {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(
                person.id,
                event.payload.entityUpdatedAt
            )
        } returns PersonWithoutCurrentMemberException().failure()

        val result = consumer.syncMemberOnZendesk(event)

        assertThat(result).isSuccess()

        coVerifyOnce {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(any(), any())
        }
    }

    @Test
    fun `#syncMemberOnZendesk should fail to upsert person on Zendesk with auto retryable when fails with NotFoundException`() = runBlocking {
        coEvery {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(
                person.id,
                event.payload.entityUpdatedAt
            )
        } returns NotFoundException().failure()

        val result = consumer.syncMemberOnZendesk(event)

        assertThat(result).isFailureOfType(AutoRetryableException::class)

        coVerifyOnce {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(any(), any())
        }
    }

    @Test
    fun `#syncMemberOnZendesk should fail to upsert person on Zendesk when zendesk fails`() = runBlocking {
        coEvery {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(
                person.id,
                event.payload.entityUpdatedAt
            )
        } returns Exception().failure()

        val result = consumer.syncMemberOnZendesk(event)

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce {
            zendeskUserUpsertService.verifyShouldSyncAndThenUpsertByPersonId(any(), any())
        }
    }
}
