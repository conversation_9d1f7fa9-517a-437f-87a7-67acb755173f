package br.com.alice.zendeskintegrationservice.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.zendesk.transport.ZendeskOrganization
import br.com.alice.zendesk.transport.ZendeskOrganizationRequest
import br.com.alice.zendeskintegrationservice.clients.ZendeskClient
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


class ZendeskOrganizationServiceTest {

    private val zendeskClient: ZendeskClient = mockk()
    private val zendeskOrganizationService = ZendeskOrganizationService(zendeskClient)

    private val company = TestModelFactory.buildCompany()
    private val externalId = RangeUUID.generate()
    private val zendeskOrganizationRequest = ZendeskOrganizationRequest(company, externalId)

    private val zendeskOrganizationResponse = ZendeskOrganization(1234)

    @Test
    fun `#upsert should create an organization`() = runBlocking {
        coEvery { zendeskClient.createOrUpdateOrganization(zendeskOrganizationRequest) } returns zendeskOrganizationResponse.success()

        val result = zendeskOrganizationService.upsert(zendeskOrganizationRequest)

        assertThat(result).isSuccessWithData(zendeskOrganizationResponse)

        coVerifyOnce { zendeskClient.createOrUpdateOrganization(any()) }
    }
}
