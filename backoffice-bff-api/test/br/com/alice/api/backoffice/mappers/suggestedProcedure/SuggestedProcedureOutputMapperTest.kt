package br.com.alice.api.backoffice.mappers.suggestedProcedure

import br.com.alice.api.backoffice.transfers.suggestedProcedure.SpecialtyResponse
import br.com.alice.api.backoffice.transfers.suggestedProcedure.SuggestedProcedureResponse
import br.com.alice.api.backoffice.transfers.suggestedProcedure.SuggestedProcedureTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregate
import br.com.alice.exec.indicator.models.ResourceSuggestedProcedure
import io.ktor.http.Parameters
import kotlin.test.Test
import org.assertj.core.api.Assertions

class SuggestedProcedureOutputMapperTest {
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()
    private val medicalSpecialty2 = TestModelFactory.buildMedicalSpecialty(
        name = "medical specialty 2",
        active = true
    )

    private val defaultProcedureAggregate = ResourceBundleSpecialtyAggregate(
        id = RangeUUID.generate(),
        appointmentRecommendationLevel = AppointmentRecommendationLevel.DEFAULT,
        status = Status.ACTIVE,
        medicalSpecialtyId = medicalSpecialty.id,
        primaryTuss = "TUSS",
        code = "alice code",
        description = "description",
        healthSpecialistResourceBundleStatus = Status.ACTIVE,
        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
    )
    private val suggestedProcedureAggregate = ResourceBundleSpecialtyAggregate(
        id = RangeUUID.generate(),
        appointmentRecommendationLevel = AppointmentRecommendationLevel.RECOMMENDED,
        status = Status.ACTIVE,
        medicalSpecialtyId = medicalSpecialty.id,
        primaryTuss = "TUSS",
        code = "alice code",
        description = "description",
        healthSpecialistResourceBundleStatus = Status.ACTIVE,
        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
    )
    private val resourceSuggestedProcedure = ResourceSuggestedProcedure(
        procedureDefault = defaultProcedureAggregate,
        suggestedProcedure = listOf(suggestedProcedureAggregate)
    )


    val params = Parameters.build {
        append("page", "1")
        append("size", "10")
    }

    @Test
    fun toSpecialtyPaginatedResponse() {

        val expected = ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(params, 2),
            results = listOf(
                SpecialtyResponse(
                    id = medicalSpecialty.id,
                    name = medicalSpecialty.name,
                    active = medicalSpecialty.active,
                ),
                SpecialtyResponse(
                    id = medicalSpecialty2.id,
                    name = medicalSpecialty2.name,
                    active = medicalSpecialty2.active,
                )
            )
        )
        val result = SuggestedProcedureOutputMapper.toSpecialtyPaginatedResponse(
            items = listOf(medicalSpecialty, medicalSpecialty2),
            total = 2,
            params
        )

        Assertions.assertThat(expected).isEqualTo(result)
    }

    @Test
    fun toPaginatedResponse() {
        val result = SuggestedProcedureOutputMapper.toPaginatedResponse(
            items = listOf(defaultProcedureAggregate, suggestedProcedureAggregate),
            total = 2,
            queryParams = params
        )
        val expected = ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(
                params,
                2
            ),
            results = listOf(
                SuggestedProcedureTransport(
                    id = defaultProcedureAggregate.id,
                    friendlyDescription = "TUSS - alice code - description",
                    active = true,
                ),
                SuggestedProcedureTransport(
                    id = suggestedProcedureAggregate.id,
                    friendlyDescription = "TUSS - alice code - description",
                    active = true,
                )
            )
        )

        Assertions.assertThat(expected).isEqualTo(result)
    }

    @Test
    fun toResponse() {
        val expected = SuggestedProcedureResponse(
            specialty = SpecialtyResponse(
                id = medicalSpecialty.id,
                name = medicalSpecialty.name,
                active = medicalSpecialty.active,
            ),
            defaultProcedure = SuggestedProcedureTransport(
                id = defaultProcedureAggregate.id,
                friendlyDescription = "TUSS - alice code - description",
                active = true,
            ),
            suggestedProcedures = listOf(
                SuggestedProcedureTransport(
                    id = suggestedProcedureAggregate.id,
                    friendlyDescription = "TUSS - alice code - description",
                    active = true,
                )
            )
        )
        val result = SuggestedProcedureOutputMapper.toResponse(
            resource = resourceSuggestedProcedure,
            specialty = medicalSpecialty
        )

        Assertions.assertThat(expected).isEqualTo(result)
    }
}
