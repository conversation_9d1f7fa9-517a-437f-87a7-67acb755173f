package br.com.alice.api.backoffice.controllers.salesFirm

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.transfers.sales.CreateSalesFirmRequest
import br.com.alice.api.backoffice.transfers.sales.SalesFirmResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.common.transfers.PaginationResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class SalesFirmControllerTest : ControllerTestHelper() {
    private val salesFirmService: SalesFirmService = mockk()

    private val salesFirmController = SalesFirmController(salesFirmService)

    private val salesFirm1 = TestModelFactory.buildSalesFirm(name = "Corretora 1")
    private val salesFirm2 = TestModelFactory.buildSalesFirm(name = "Corretora 2")
    private val salesFirm3 = TestModelFactory.buildSalesFirm(name = "Corretora 3")

    private val salesFirms = listOf(salesFirm1, salesFirm2, salesFirm3)

    private val queryParams = Parameters.build {
        append("page", "1")
        append("pageSize", "20")
    }


    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { salesFirmController }
    }


    @Test
    fun `#index returns SalesFirms`() {
        coEvery { salesFirmService.getByFilters("", any()) } returns salesFirms.success()
        coEvery { salesFirmService.countByFilters("") } returns salesFirms.count().success()

        val expectedResponse = ListPaginatedResponse(
            pagination = PaginationResponse(
                pageSize = 20,
                page = 1,
                totalPages = 1
            ),
            results = salesFirms.map { it.convertTo(SalesFirmResponse::class) }
        )

        authenticatedAs(idToken, staff) {
            get("/salesFirm/?filter={}&page1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#index returns SalesFirms by search token`() {
        val searchToken = salesFirm1.name

        coEvery { salesFirmService.getByFilters(searchToken, any()) } returns listOf(salesFirm1).success()
        coEvery { salesFirmService.countByFilters(searchToken) } returns 1.success()

        val expectedResponse = ListPaginatedResponse(
            pagination = PaginationResponse(
                pageSize = 20,
                page = 1,
                totalPages = 1
            ),
            results = listOf(salesFirm1.convertTo(SalesFirmResponse::class))
        )

        authenticatedAs(idToken, staff) {
            get("/salesFirm/?filter={q:\"$searchToken\"}&page1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#get returns a SalesFirm`() {
        coEvery { salesFirmService.get(salesFirm1.id) } returns salesFirm1.success()

        authenticatedAs(idToken, staff) {
            get("/salesFirm/${salesFirm1.id}") { response ->
                assertThat(response).isOKWithData(salesFirm1)
            }
        }
    }

    @Test
    fun `#get fails if not found`() {
        coEvery { salesFirmService.get(any()) } returns Result.failure(NotFoundException())

        authenticatedAs(idToken, staff) {
            get("/salesFirm/${RangeUUID.generate()}") { response ->
                assertThat(response).isBadRequestWithErrorCode("sales_firm_not_found")
            }
        }
    }

    @Test
    fun `#create creates a new sales firm`() {
        val request = CreateSalesFirmRequest(
            name = "Corretora 4",
            legalName = "Corretoral 4 LTDA",
            cnpj = "25542335000162",
            phoneNumber = "81988334455",
            email = "<EMAIL>"
        )

        val salesFirm = request.convertTo(SalesFirm::class)

        coEvery { salesFirmService.add(match { it.cnpj == request.cnpj }) } returns salesFirm.success()

        authenticatedAs(idToken, staff) {
            post("/salesFirm", body = request) { response ->
                assertThat(response).isOKWithData(salesFirm)
            }
        }
    }

    @Test
    fun `#update updates a sales firm`() {
        val updatedName = "${salesFirm1.name} Updated"
        val request = salesFirm1.convertTo(CreateSalesFirmRequest::class).copy(name = updatedName)
        val updatedSalesFirm = salesFirm1.copy(name = updatedName)

        coEvery { salesFirmService.get(salesFirm1.id) } returns salesFirm1.success()
        coEvery { salesFirmService.update(updatedSalesFirm) } returns updatedSalesFirm.success()

        authenticatedAs(idToken, staff) {
            put("/salesFirm/${salesFirm1.id}", body = request) { response ->
                assertThat(response).isOKWithData(updatedSalesFirm)
            }
        }
    }
}
