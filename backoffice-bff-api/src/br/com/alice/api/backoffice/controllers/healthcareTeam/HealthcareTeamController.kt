package br.com.alice.api.backoffice.controllers.healthcareTeam

import br.com.alice.api.backoffice.mappers.healthcareTeam.HealthcareTeamOutputMapper
import br.com.alice.api.backoffice.transfers.healthcareTeam.HealthcareTeamRequest
import br.com.alice.api.backoffice.transfers.healthcareTeam.HealthcareTeamUpdateRequest
import br.com.alice.api.backoffice.transfers.healthcareTeam.PhysicianResponse
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID
import kotlin.reflect.KClass

class HealthcareTeamController(
    private val healthcareTeamService: HealthcareTeamService,
    private val staffService: StaffService,
) : Controller() {

    private object ErrorsCode {
        const val ILLEGAL_ARGUMENT = "healthcare_team_duplicated"
        const val NOT_FOUND_KEY = "healthcare_team_not_found"
        const val DUPLICATED_KEY = "healthcare_team_duplicated"
    }

    private val mapFailures: Array<Pair<KClass<*>, suspend (Throwable) -> ErrorResponse>> = arrayOf(
        NotFoundException::class to { ErrorResponse(ErrorsCode.NOT_FOUND_KEY, i18n(ErrorsCode.NOT_FOUND_KEY)) },
        IllegalArgumentException::class to {
            ErrorResponse(
                ErrorsCode.ILLEGAL_ARGUMENT,
                i18n(ErrorsCode.ILLEGAL_ARGUMENT)
            )
        },
        DuplicatedItemException::class to { ErrorResponse(ErrorsCode.DUPLICATED_KEY, i18n(ErrorsCode.DUPLICATED_KEY)) },
    )

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val nameFilter = parseFilter<String>(queryParams, "name")?.trim()

        val staffIdsFilter = if (!nameFilter.isNullOrBlank()) {
            staffService.findByNameWithRoleAndRange(nameFilter, listOf(Role.MANAGER_PHYSICIAN), range)
                .getOrNull()
                ?.map { it.id }
        } else null

        val filter = HealthcareTeamFilters(
            active = parseFilter<String>(queryParams, "active")?.toBooleanStrictOrNull() ?: true,
            type = HealthcareTeam.Type.LEAN,
            staffIds = staffIdsFilter,
            range = range
        )

        val total = async { healthcareTeamService.countBy(filter).get() }
        val results = async { healthcareTeamService.findBy(filter).get() }

        Response(
            status = HttpStatusCode.OK,
            message = HealthcareTeamOutputMapper.toPaginatedResponse(
                results.await(),
                total.await(),
                queryParams,
                getTeamDescriptions(results.await())
            )
        )
    }

    suspend fun get(id: String): Response {
        val healthcareTeam = getOne(id).get()
        return Response(
            status = HttpStatusCode.OK,
            message = HealthcareTeamOutputMapper.toResponse(healthcareTeam, getTeamDescriptions(listOf(healthcareTeam)))
        )
    }

    suspend fun create(healthcareTeamRequest: HealthcareTeamRequest): Response =
        healthcareTeamService.add(
            HealthcareTeam(
                physicianStaffId = healthcareTeamRequest.physician.id,
                active = healthcareTeamRequest.active,
                type = HealthcareTeam.Type.LEAN,
                segment = healthcareTeamRequest.segment,
                maxMemberAssociation = healthcareTeamRequest.maxMemberAssociation,
                physicianStaffGender = healthcareTeamRequest.physician.gender
            )
        ).coFoldResponse(
            { HealthcareTeamOutputMapper.toResponse(it, getTeamDescriptions(listOf(it))) },
            *mapFailures.plus(
                Exception::class to {
                    ErrorResponse(
                        "healthcare_team_failed_on_create",
                        it.message ?: "An error occurred while creating"
                    )
                }
            )
        )

    suspend fun update(request: HealthcareTeamUpdateRequest): Response =
        getOne(request.id.toString()).map { existentHealthcareTeam ->
            existentHealthcareTeam.copy(
                physicianStaffId = request.physician.id,
                active = request.active,
                maxMemberAssociation = request.maxMemberAssociation,
                segment = request.segment,
                physicianStaffGender = request.physician.gender
            )
        }.flatMap { healthcareTeamToUpdate -> healthcareTeamService.update(healthcareTeamToUpdate, true) }
            .coFoldResponse(
                { HealthcareTeamOutputMapper.toResponse(it, getTeamDescriptions(listOf(it))) },
                *mapFailures.plus(
                    Exception::class to {
                        ErrorResponse(
                            "healthcare_team_failed_on_update",
                            it.message ?: "An error occurred while updating"
                        )
                    }
                )
            )

    suspend fun getPhysicians() =
        staffService
            .findActivesWithAnyRole(listOf(Role.MANAGER_PHYSICIAN))
            .mapEach { PhysicianResponse.from(it) }
            .foldResponse()

    private suspend fun getOne(id: String): Result<HealthcareTeam, Throwable> =
        healthcareTeamService.get(id.toUUID(), true)

    private suspend fun getTeamDescriptions(healthcareTeams: List<HealthcareTeam>): Map<UUID, String> {
        val staffIds = healthcareTeams.map { it.physicianStaffId }.distinct()
        val staffMap = staffService.findByList(staffIds).get().associateBy { it.id }

        return healthcareTeams.associate { healthcareTeam ->
            healthcareTeam.id to staffMap.getValue(healthcareTeam.physicianStaffId).fullName
        }
    }

}
