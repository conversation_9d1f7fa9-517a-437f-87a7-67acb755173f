package br.com.alice.api.backoffice.mappers.salesFirm

import br.com.alice.api.backoffice.transfers.sales.CreateSalesFirmRequest
import br.com.alice.api.backoffice.transfers.sales.UpdateSalesFirmRequest
import br.com.alice.common.convertTo
import br.com.alice.data.layer.models.SalesFirm

object SalesFirmInputMapper {
    fun toCreate(request: CreateSalesFirmRequest) : SalesFirm = request.convertTo(SalesFirm::class)

    fun toUpdate(salesFirm: SalesFirm, request: UpdateSalesFirmRequest) : SalesFirm = salesFirm.copy(
        name = request.name,
        legalName = request.legalName,
        cnpj = request.cnpj,
        email = request.email,
        phoneNumber = request.phoneNumber
    )
}
