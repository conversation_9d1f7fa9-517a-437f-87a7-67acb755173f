package br.com.alice.api.backoffice.mappers.company

import br.com.alice.api.backoffice.transfers.CompanyStaffResponse
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyStaff
import io.ktor.http.Parameters

object CompanyStaffOutputMapper {
    fun toPaginatedResponse(items: List<CompanyStaff>, total: Int, companies: List<Company>, queryParams: Parameters): ListPaginatedResponse<CompanyStaffResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = items.map { staff -> toResponse(staff, companies.first { it.id == staff.companyId }) }
        )

    fun toResponse(companyStaff: CompanyStaff, company: Company): CompanyStaffResponse =
        CompanyStaffResponse(
            id = companyStaff.id,
            firstName = companyStaff.firstName,
            lastName = companyStaff.lastName,
            email = companyStaff.email,
            role = companyStaff.role,
            company = CompanyOutputMapper.toShortResponse(company),
            accessLevel = companyStaff.accessLevel,
        )
}
