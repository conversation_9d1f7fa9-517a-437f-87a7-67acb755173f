package br.com.alice.api.backoffice.transfers

import br.com.alice.data.layer.models.CompanyStaffAccessLevel
import br.com.alice.data.layer.models.CompanyStaffRole
import java.util.UUID

data class CompanyStaffResponse(
    val id: UUID,
    val email: String?,
    val firstName: String?,
    val lastName: String?,
    val role: CompanyStaffRole,
    val company: CompanyShortResponse,
    val accessLevel: CompanyStaffAccessLevel,
)

data class UpdateCompanyStaffRequest(
    val email: String,
    val firstName: String,
    val lastName: String,
    val role: CompanyStaffRole,
    val accessLevel: CompanyStaffAccessLevel,
)

data class CreateCompanyStaffRequest(
    val email: String,
    val firstName: String,
    val lastName: String,
    val role: CompanyStaffRole,
    val companyId: UUID,
    val accessLevel: CompanyStaffAccessLevel,
)
