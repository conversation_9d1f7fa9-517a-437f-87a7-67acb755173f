package br.com.alice.api.backoffice.routes.sales

import br.com.alice.api.backoffice.controllers.salesFirm.SalesFirmController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.salesFirmRoutes() {
    authenticate {
        val salesFirmController by inject<SalesFirmController>()

        route("salesFirm") {
            get("/") { coHandler(salesFirmController::index) }
            post("/") { coHandler(salesFirmController::create) }
            get("/{id}") { coHandler("id", salesFirmController::get) }
            put("/{id}") { coHandler("id", salesFirmController::update) }
        }
    }
}
