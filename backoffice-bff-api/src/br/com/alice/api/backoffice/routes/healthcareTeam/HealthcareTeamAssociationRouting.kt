package br.com.alice.api.backoffice.routes.healthcareTeam

import br.com.alice.api.backoffice.controllers.healthcareTeam.HealthcareTeamAssociationController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.healthcareTeamAssociationRoutes() {

    authenticate {
        val healthcareTeamAssociationController by inject<HealthcareTeamAssociationController>()

        route("healthcareTeamAssociation") {
            get("/") { coHandler(healthcareTeamAssociationController::index) }
            post("/") { coHandler(healthcareTeamAssociationController::create) }
            put("/{id}") { coHandler(healthcareTeamAssociationController::update) }
            get("/{id}") { coHandler("id", healthcareTeamAssociationController::get) }
            get("/healthcareTeams") { coHandler(healthcareTeamAssociationController::getHealthcareTeams) }
            get("/persons") { coHandler(healthcareTeamAssociationController::getPersons) }
        }
    }
}
