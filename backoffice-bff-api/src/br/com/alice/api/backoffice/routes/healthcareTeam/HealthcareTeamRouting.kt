package br.com.alice.api.backoffice.routes.healthcareTeam

import br.com.alice.api.backoffice.controllers.healthcareTeam.HealthcareTeamController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.healthcareTeamRoutes() {

    authenticate {
        val healthcareTeamController by inject<HealthcareTeamController>()

        route("healthcareTeam") {
            get("/") { coHandler(healthcareTeamController::index) }
            post("/") { coHandler(healthcareTeamController::create) }
            put("/{id}") { coHandler(healthcareTeamController::update) }
            get("/{id}") { coHandler("id", healthcareTeamController::get) }
            get("/physicians") { co<PERSON>and<PERSON>(healthcareTeamController::getPhysicians) }
        }
    }
}
