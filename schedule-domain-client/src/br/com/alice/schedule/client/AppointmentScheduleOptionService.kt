package br.com.alice.schedule.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Staff
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentScheduleOptionService : Service {

    override val namespace get() = "schedule"
    override val serviceName get() = "appointment_schedule_option"

    suspend fun listByPerson(
        personId: PersonId,
        fromReferral: Boolean = true,
        subSpecialtyIds: List<UUID> = emptyList(),
    ): Result<List<AppointmentScheduleOption>, Throwable>

    suspend fun listByPersonAndType(
        personId: PersonId,
        type: AppointmentScheduleType,
        fromReferral: Boolean = true,
        subSpecialtyIds: List<UUID> = emptyList(),
    ): Result<List<AppointmentScheduleOption>, Throwable>

    suspend fun searchScheduleOption(
        titlePrefix: String,
        range: IntRange = IntRange(0, 19)
    ): Result<List<AppointmentScheduleOption>, Throwable>

    suspend fun getForSpecialist(specialistId: UUID): Result<AppointmentScheduleOption, Throwable>

    suspend fun getForSpecialists(specialistIds: List<UUID>): Result<List<AppointmentScheduleOption>, Throwable>

    suspend fun getEventsWithProviderUnits(
        staffId: UUID
    ): Result<List<AppointmentScheduleEventTypeWithProviderUnits>, Throwable>

    suspend fun associateStaff(
        staffId: UUID,
        appointmentScheduleEventTypeId: UUID
    ): Result<AppointmentScheduleOption, Throwable>

    suspend fun disassociateStaff(
        staffId: UUID,
        appointmentScheduleEventTypeId: UUID
    ): Result<AppointmentScheduleOption, Throwable>

    suspend fun get(id: UUID): Result<AppointmentScheduleOption, Throwable>

    suspend fun add(model: AppointmentScheduleOption): Result<AppointmentScheduleOption, Throwable>

    suspend fun update(model: AppointmentScheduleOption): Result<AppointmentScheduleOption, Throwable>

    suspend fun findBy(filters: AppointmentScheduleOptionFilters): Result<List<AppointmentScheduleOption>, Throwable>
    suspend fun countBy(filters: AppointmentScheduleOptionFilters): Result<Int, Throwable>

    suspend fun getStaffsAssociatedToEvents(
        appointmentScheduleEventTypeId: UUID,
        staffIds: List<UUID>?,
    ): Result<List<Staff>, Throwable>

    suspend fun getProviderUnitDetailsByStaffSchedule(
        personId: PersonId,
        eventId: UUID,
        staffId: UUID
    ): Result<List<ProviderUnit>, Throwable>

}

data class AppointmentScheduleOptionFilters(
    val titlePrefix: String? = null,
    val specialistIds: List<UUID>? = null,
    val staffIds: List<UUID>? = null,
    val appointmentScheduleEventTypeIds: List<UUID>? = null,
    val specialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID>? = null,
    val appointmentScheduleEventTypeId: UUID? = null,
    val types: List<AppointmentScheduleType>? = null,
    val ageRating: List<AgeRatingType>? = null,
    val active: Boolean? = null,
    val range: IntRange? = null,
    val sortOrder: SortOrder = SortOrder.Descending
) {
    fun isValid() {
        if (subSpecialtyIds.isNotNullOrEmpty()) return
        if (titlePrefix != null) return
        if (specialistIds.isNotNullOrEmpty()) return
        if (staffIds.isNotNullOrEmpty()) return
        if (appointmentScheduleEventTypeIds.isNotNullOrEmpty()) return
        if (specialtyId != null) return
        if (appointmentScheduleEventTypeId != null) return
        if (types.isNotNullOrEmpty()) return
        if (ageRating.isNotNullOrEmpty()) return
        if (active != null) return
        if (range != null) return

        throw IllegalArgumentException("Invalid filters")
    }
}
