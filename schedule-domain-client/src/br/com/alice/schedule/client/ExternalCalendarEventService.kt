package br.com.alice.schedule.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ExternalCalendarEvent
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ExternalCalendarEventService : Service {

    override val namespace get() = "schedule"
    override val serviceName get() = "external_calendar_event"

    suspend fun get(
        id: UUID
    ): Result<ExternalCalendarEvent, Throwable>

    suspend fun getForStaffForPeriod(
        staffId: UUID,
        fromDateTime: LocalDateTime,
        toDateTime: LocalDateTime,
        range: IntRange
    ): Result<List<ExternalCalendarEvent>, Throwable>

    suspend fun cancelFutureEventsForStaff(staffId: UUID): Result<Any, Throwable>

}
