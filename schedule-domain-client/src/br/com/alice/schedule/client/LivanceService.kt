package br.com.alice.schedule.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.ExternalAppointmentSchedule
import br.com.alice.schedule.model.LivanceLocation
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface LivanceService: Service {

    override val namespace get() = "schedule"
    override val serviceName get() = "livance"

    suspend fun getLocations(): Result<List<LivanceLocation>, Throwable>

    suspend fun createAppointment(appointmentSchedule: AppointmentSchedule): Result<ExternalAppointmentSchedule, Throwable>

    suspend fun cancelByAppointmentSchedule(appointmentScheduleId: UUID): Result<List<ExternalAppointmentSchedule>, Throwable>

}

