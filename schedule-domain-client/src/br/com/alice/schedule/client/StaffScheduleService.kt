package br.com.alice.schedule.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.StaffScheduleType
import com.github.kittinunf.result.Result
import io.ktor.util.date.WeekDay
import java.time.LocalTime
import java.util.UUID

@RemoteService
interface StaffScheduleService: Service {

    override val namespace get() = "schedule"
    override val serviceName get() = "staff_schedule"

    suspend fun getStaffSchedules(staffId: UUID): Result<List<StaffSchedule>, Throwable>

    suspend fun getStaffScheduleById(id: UUID): Result<StaffSchedule, Throwable>

    suspend fun create(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        providerUnitId: UUID?,
        alsoDigital: Boolean,
        type: StaffScheduleType = StaffScheduleType.HAD,
    ): Result<StaffSchedule, Throwable>

    suspend fun update(model: StaffSchedule): Result<StaffSchedule, Throwable>

    suspend fun delete(id: UUID, requesterId: UUID? = null): Result<StaffSchedule, Throwable>

    suspend fun addList(models: List<StaffSchedule>): Result<List<StaffSchedule>, Throwable>

    suspend fun updateList(models: List<StaffSchedule>): Result<List<StaffSchedule>, Throwable>

    suspend fun updateStaffScheduleEventTypeAssociation(
        staffScheduleId: UUID,
        appointmentScheduleEventTypeId: UUID,
        isDisassociation: Boolean,
    ): Result<StaffSchedule, Throwable>
}

