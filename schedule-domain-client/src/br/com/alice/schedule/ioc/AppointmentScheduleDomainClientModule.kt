package br.com.alice.schedule.ioc

import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.schedule.AppointmentScheduleDomainConfiguration
import br.com.alice.schedule.SERVICE_NAME
import br.com.alice.schedule.client.*
import org.koin.core.qualifier.named
import org.koin.dsl.module

val AppointmentScheduleDomainClientModule = module {

    val baseUrl = AppointmentScheduleDomainConfiguration.baseUrl
    val invoker = HttpInvoker(baseUrl = "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<AppointmentScheduleService> { AppointmentScheduleServiceClient(invoker) }
    single<AppointmentScheduleCheckInService> { AppointmentScheduleCheckInServiceClient(invoker) }
    single<AppointmentScheduleEventTypeDateExceptionService> { AppointmentScheduleEventTypeDateExceptionServiceClient(invoker) }
    single<AppointmentScheduleEventTypeService> { AppointmentScheduleEventTypeServiceClient(invoker) }
    single<AppointmentScheduleOptionService> { AppointmentScheduleOptionServiceClient(invoker) }
    single<EventTypeProviderUnitService> { EventTypeProviderUnitServiceClient(invoker) }
    single<ExternalCalendarEventService> { ExternalCalendarEventServiceClient(invoker) }
    single<ExternalCalendarRecurrentEventService> { ExternalCalendarRecurrentEventServiceClient(invoker) }
    single<GoogleCalendarEventService> { GoogleCalendarEventServiceClient(invoker) }
    single<PersonCalendlyService> { PersonCalendlyServiceClient(invoker) }
    single<ReferralSchedulingService> { ReferralSchedulingServiceClient(invoker) }
    single<SchedulePreferenceService> { SchedulePreferenceServiceClient(invoker) }
    single<StaffAvailabilityService> { StaffAvailabilityServiceClient(invoker) }
    single<StaffScheduleService> { StaffScheduleServiceClient(invoker) }
    single<StaffSchedulePreferenceService> { StaffSchedulePreferenceServiceClient(invoker) }
    single<EventTypeProviderUnitService> { EventTypeProviderUnitServiceClient(invoker) }
    single<HealthConditionSchedulingService> { HealthConditionSchedulingServiceClient(invoker) }
    single<AppointmentSchedulePreTriageService> { AppointmentSchedulePreTriageServiceClient(invoker) }
    single<ExternalAppointmentScheduleService> { ExternalAppointmentScheduleServiceClient(invoker) }
    single<LivanceService> { LivanceServiceClient(invoker) }

}
