package br.com.alice.schedule.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.schedule.SERVICE_NAME

data class SendAppointmentScheduleNotificationEvent(
    private val appointmentSchedule: AppointmentSchedule,
    private val analyticsEventName: AnalyticsEventName,
    private val notificationTime: AppointmentReminderNotificationTime
) : NotificationEvent<SendAppointmentScheduleNotificationEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = SendAppointmentScheduleNotificationEventPayload(
        appointmentSchedule = appointmentSchedule,
        analyticsEventName = analyticsEventName,
        notificationTime = notificationTime
    )
) {
    companion object {
        const val name = "send-appointment-schedule-notification-event"
    }
}

data class SendAppointmentScheduleNotificationEventPayload(
    val appointmentSchedule: AppointmentSchedule,
    val analyticsEventName: AnalyticsEventName,
    val notificationTime: AppointmentReminderNotificationTime
)
