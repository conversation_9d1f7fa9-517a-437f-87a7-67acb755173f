package br.com.alice.schedule.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.schedule.SERVICE_NAME
import java.time.LocalDate

data class RenewGoogleCalendarWebhooksRequestedEvent(
    private val referenceDate: LocalDate
) : NotificationEvent<RenewGoogleCalendarWebhooksRequestedEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = RenewGoogleCalendarWebhooksRequestedEventPayload(
        referenceDate = referenceDate
    )
) {
    companion object {
        const val name = "renew-google-calendar-webhooks-requested"
    }
}

data class RenewGoogleCalendarWebhooksRequestedEventPayload(
    val referenceDate: LocalDate
)
