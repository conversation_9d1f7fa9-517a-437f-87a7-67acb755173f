package br.com.alice.schedule.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.schedule.SERVICE_NAME

data class AppointmentScheduleUpdatedEvent(
    private val appointmentSchedule: AppointmentSchedule
) : NotificationEvent<AppointmentSchedule>(
    name = name,
    producer = SERVICE_NAME,
    payload = appointmentSchedule
) {
    companion object {
        const val name = "appointment-schedule-updated"
    }
}
