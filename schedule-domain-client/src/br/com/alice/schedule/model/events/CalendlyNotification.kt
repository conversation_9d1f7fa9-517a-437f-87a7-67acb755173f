package br.com.alice.schedule.model.events

import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.serialization.gson
import br.com.alice.schedule.SERVICE_NAME
import br.com.alice.schedule.model.CalendlyV2WebhookCancellation
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

data class CalendlyNotification(
    val event: String,
    val payload: CalendlyNotificationPayload
) {
    val eventDateTime: LocalDateTime?
        get() = parsedDateTime(payload.event?.startTime)

    val eventEndDateTime: LocalDateTime?
        get() = parsedDateTime(payload.event?.endTime)

    val eventType: CalendlyEventType?
        get() = CalendlyEventType.fromString(event)

    private fun parsedDateTime(dateTime: String?): LocalDateTime? = dateTime?.let {
        ZonedDateTime
            .parse(dateTime)
            .withZoneSameInstant(ZoneId.of("UTC"))
            .toLocalDateTime()
    }
}

data class CalendlyNotificationPayload(
    val eventType: CalendlyNotificationEventType?,
    val event: CalendlyNotificationEvent?,
    val invitee: CalendlyNotificationInvitee,
    val tracking: CalendlyNotificationTracking?,
    val questionsAndAnswers: List<QuestionAndAnswer>? = null,
    val eventUri: String? = null,
    val cancellation: CalendlyV2WebhookCancellation? = null
) {
    val personEmail: String?
        get() {
            return when {
                tracking?.utmContent != null && tracking.utmContent.trim().isNotBlank() -> tracking.utmContent
                else -> invitee.email?.trim()
            }
        }
    val staffEmail get() = event?.extendedAssignedTo?.first()?.email

    // This UTM terms must be added on the URL builder that is sent to the mobile app
    // Also, we need to build the calendly component with them at the webview project
    val healthPlanTaskId
        get() = tracking?.utmTerm?.let {
            getFromUtmTerm(it, "healthPlanTaskId") ?: getFromUtmTerm(
                it,
                "health_plan_task_id"
            )
        }
    val personTaskId
        get() = tracking?.utmTerm?.let {
            getFromUtmTerm(it, "personTaskId") ?: getFromUtmTerm(
                it,
                "person_task_id"
            )
        }
    val personCalendlyId
        get() = tracking?.utmTerm?.let {
            getFromUtmTerm(it, "personCalendlyId") ?: getFromUtmTerm(
                it,
                "person_calendly_id"
            )
        }

    private fun getFromUtmTerm(utmTerm: String, key: String) = try {
        gson.fromJson(utmTerm, Map::class.java)
    } catch (ex: Exception) {
        logger.error("Invalid utmTerm format", "utmTerm" to utmTerm, "key" to key)
        null
    }?.getOrDefault(key, null)?.toString()
}

data class QuestionAndAnswer(
    val question: String,
    val answer: String? = null
)

data class CalendlyNotificationTracking(
    val utmContent: String?,
    val utmTerm: String? = null,
    val utmScheduleScreeningContextId: String? = null
)

data class CalendlyNotificationInvitee(
    val email: String?,
    val uuid: String? = null,
)

data class CalendlyNotificationEventType(
    val name: String
)

data class CalendlyNotificationEvent(
    val uuid: String,
    val extendedAssignedTo: List<CalendlyNotificationAssignedTo>,
    val startTime: String?,
    val endTime: String? = null,
    val location: String?
)

data class CalendlyNotificationAssignedTo(
    val name: String,
    val email: String
)

data class CalendlyNotificationReceivedEvent(
    private val notification: CalendlyNotification,
    private val personEmailPartitionKey: String,
) : NotificationEvent<CalendlyNotification>(
    name = name,
    producer = SERVICE_NAME,
    payload = notification,
    partitionKey = personEmailPartitionKey
) {
    companion object {
        const val name = "calendly-notification-received"
    }
}

enum class CalendlyEventType(val value: String) {
    SCHEDULING("invitee.created"),
    CANCELLATION("invitee.canceled");

    companion object {
        fun fromString(value: String) = values().find { it.value == value }
    }
}
