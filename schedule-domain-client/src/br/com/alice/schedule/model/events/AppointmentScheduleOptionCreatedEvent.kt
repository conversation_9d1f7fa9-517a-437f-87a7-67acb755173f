package br.com.alice.schedule.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.schedule.SERVICE_NAME

data class AppointmentScheduleOptionCreatedEvent(
    private val appointmentScheduleOption: AppointmentScheduleOption
) : NotificationEvent<AppointmentScheduleOptionCreatedEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = AppointmentScheduleOptionCreatedEventPayload(
        appointmentScheduleOption = appointmentScheduleOption,
    )
) {
    companion object {
        const val name = "APPOINTMENT-SCHEDULE-OPTION-CREATED"
    }
}

data class AppointmentScheduleOptionCreatedEventPayload(
    val appointmentScheduleOption: AppointmentScheduleOption
)
