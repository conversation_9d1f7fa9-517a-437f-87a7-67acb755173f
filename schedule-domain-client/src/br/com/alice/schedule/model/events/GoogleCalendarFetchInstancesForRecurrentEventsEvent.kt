package br.com.alice.schedule.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.schedule.SERVICE_NAME
import java.time.LocalDate

data class GoogleCalendarFetchInstancesForRecurrentEventsEvent(
    private val referenceDate: LocalDate
) : NotificationEvent<GoogleCalendarFetchInstancesForRecurrentEventsEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = GoogleCalendarFetchInstancesForRecurrentEventsEventPayload(
        referenceDate = referenceDate
    )
) {
    companion object {
        const val name = "google-calendar-fetch-instances-for-recurrent-events"
    }
}

data class GoogleCalendarFetchInstancesForRecurrentEventsEventPayload(
    val referenceDate: LocalDate
)
