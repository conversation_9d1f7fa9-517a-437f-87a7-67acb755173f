package br.com.alice.schedule.model.events

import br.com.alice.common.core.PersonId
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Staff
import br.com.alice.schedule.SERVICE_NAME
import java.util.UUID

data class AppointmentScheduleCreatedEvent(
    private val person: Person,
    private val appointmentSchedule: AppointmentSchedule,
    private val isUsingInternalScheduler: Boolean = false,
    private val professional: Professional? = null
) : NotificationEvent<AppointmentScheduleCreatedPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = AppointmentScheduleCreatedPayload(
        person = AppointmentSchedulePersonPayload.from(person),
        appointmentSchedule = appointmentSchedule,
        isUsingInternalScheduler = isUsingInternalScheduler,
        professional = professional
    )
) {
    companion object {
        const val name = "appointment-schedule-created"
    }
}

data class Professional(
    val name: String,
    val email: String?,
    val id: UUID,
    val profileImageUrl: String?,
) {

    companion object {

        fun from(staff: Staff) =
            Professional(
                name = staff.fullName,
                email = staff.email,
                id = staff.id,
                profileImageUrl = staff.profileImageUrl,
            )
    }
}

data class AppointmentSchedulePersonPayload(
    val personId: PersonId,
    val firstName: String,
    val contactName: String,
    val email: String,
    val fullSocialName: String
) {
    companion object {
        fun from(person: Person) = AppointmentSchedulePersonPayload(
            person.id,
            person.firstName,
            person.contactName,
            person.email,
            person.fullSocialName
        )
    }
}

data class AppointmentScheduleCreatedPayload(
    val person: AppointmentSchedulePersonPayload,
    val appointmentSchedule: AppointmentSchedule,
    val isUsingInternalScheduler: Boolean,
    val professional: Professional? = null
)
