package br.com.alice.api.example.service

import br.com.alice.api.example.services.BookService
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.BookDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class BookServiceTest {

    private val data: BookDataService = mockk()
    private val service = BookService(data)

    private val book = TestModelFactory.buildBook()

    @Test
    fun `#get return book`() = runBlocking {
        coEvery { data.get(book.id) } returns book.success()

        val result = service.get(book.id)
        ResultAssert.assertThat(result).isSuccessWithData(book)

        coVerify(exactly = 1) { data.get(any()) }
    }

    @Test
    fun `#add return created book`() = runBlocking {
        coEvery { data.add(book) } returns book.success()

        val result = service.add(book)
        ResultAssert.assertThat(result).isSuccessWithData(book)

        coVerify(exactly = 1) { data.add(any()) }
    }

    @Test
    fun `#update return updated book`() = runBlocking {
        coEvery { data.get(book.id) } returns book.success()
        coEvery { data.update(book) } returns book.success()

        val result = service.update(book.id, book)
        ResultAssert.assertThat(result).isSuccessWithData(book)

        coVerify(exactly = 1) { data.get(any()) }
        coVerify(exactly = 1) { data.update(any()) }
    }
}
