<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>./logs/application.log</file>
        <!-- encoder is required -->
        <encoder class="br.com.alice.common.logging.LoggingEncoder">
            <timeZone>UTC</timeZone>
            <version></version>
            <jsonGeneratorDecorator class="br.com.alice.common.logging.MaskSensitiveInfoDecorator" />
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder is required -->
        <encoder class="br.com.alice.common.logging.LoggingEncoder">
            <timeZone>UTC</timeZone>
            <version></version>
            <jsonGeneratorDecorator class="br.com.alice.common.logging.MaskSensitiveInfoDecorator" />
        </encoder>
    </appender>

    <logger name="org.apache.kafka" level="${KAFKA_LOG_LEVEL:-ERROR}"/>
    <logger name="io.grpc.netty" level="INFO"/>

    <appender name="ASYNCFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
    </appender>


    <appender name="ASYNCSTDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT"/>
    </appender>

    <root level="${LOG_LEVEL:-INFO}">
        <appender-ref ref="ASYNCFILE"/>
        <appender-ref ref="ASYNCSTDOUT"/>
    </root>
</configuration>
