package br.com.alice.api.example.routes

import br.com.alice.api.example.controllers.RecurringController
import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.recurringRoutes() {

    val recurringController by inject<RecurringController>()

    route("/recurring_subscribers") {
        post("/some_async_action") {
            asyncLayer {
                coHandler(recurringController::someAction)
            }
        }
        post("/some_sync_action") {
            coHandler(recurringController::someAction)
        }
    }

}
