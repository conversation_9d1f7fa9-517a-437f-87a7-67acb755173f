package br.com.alice.exec.indicator.models

import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.HealthcareResource

data class HealthSpecialistProcedure(
    val tussCode: String,
    val aliceCode: String? = null,
    val description: String,
    val hasPrice: Boolean,
    val serviceType: HealthSpecialistProcedureServiceType? = null
)

enum class HealthSpecialistProcedureServiceType {
    SURGICAL_PROCEDURE,
    OUTPATIENT_PROCEDURE,
    EXAM,
    CONSULTATION,
    UNKNOWN
}

fun HealthSpecialistResourceBundle.getServiceType(): HealthSpecialistProcedureServiceType {
    return this.serviceType.toHealthSpecialistProcedureServiceType(this.executionEnvironment)
}

fun HealthSpecialistResourceBundleServiceType.toHealthSpecialistProcedureServiceType(
    executionEnvironment: HealthSpecialistProcedureExecutionEnvironment
): HealthSpecialistProcedureServiceType {
    return when (this) {
        HealthSpecialistResourceBundleServiceType.PROCEDURE -> {
            when (executionEnvironment) {
                HealthSpecialistProcedureExecutionEnvironment.SURGICAL -> HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE
                HealthSpecialistProcedureExecutionEnvironment.OUTPATIENT -> HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
                else -> HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
            }
        }
        HealthSpecialistResourceBundleServiceType.EXAM -> HealthSpecialistProcedureServiceType.EXAM
        HealthSpecialistResourceBundleServiceType.CONSULTATION -> HealthSpecialistProcedureServiceType.CONSULTATION
        HealthSpecialistResourceBundleServiceType.UNDEFINED -> HealthSpecialistProcedureServiceType.UNKNOWN
    }
}

fun HealthcareResource.getServiceType(): HealthSpecialistProcedureServiceType {
    return when (this.code.first()) {
        '1' -> HealthSpecialistProcedureServiceType.CONSULTATION
        '2' -> HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE
        '3' -> HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE
        '4' -> HealthSpecialistProcedureServiceType.EXAM
        '5' -> HealthSpecialistProcedureServiceType.CONSULTATION
        else -> HealthSpecialistProcedureServiceType.UNKNOWN
    }
}

