package br.com.alice.exec.indicator.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.exec.indicator.models.Item
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CreateGuiaService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "create_guia_service"

    suspend fun createItemsBatch(
        personId: PersonId,
        items: List<Item> = emptyList(),
        authorizerId: UUID? = null,
        userEmail: String?,
        professional: ProfessionalIdentification,
        extraGuiaInfo: ExtraGuiaInfo,
        healthEventId: UUID? = null,
        testRequestId: UUID? = null
    ): Result<List<MvAuthorizedProcedure>, Throwable>

}
