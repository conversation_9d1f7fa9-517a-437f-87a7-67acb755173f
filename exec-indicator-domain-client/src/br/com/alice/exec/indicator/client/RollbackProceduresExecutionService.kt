package br.com.alice.exec.indicator.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface RollbackProceduresExecutionService: Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "rollback_procedures_execution"

    suspend fun rollback(
        procedures: List<MvAuthorizedProcedure>,
        executionGroupId: UUID
    ): Result<List<MvAuthorizedProcedure>, Throwable>
}
