package br.com.alice.exec.indicator.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.AttachmentRadiotherapy
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.SERVICE_NAME
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin

class GuiaWithRadiotherapyCreatedEvent(payload: GuiaWithRadiotherapyCreatedPayload) :
    NotificationEvent<GuiaWithRadiotherapyCreatedPayload>(
        name = name,
        producer = SERVICE_NAME,
        payload = payload,
    ) {

    companion object {
        const val name = "guia-with-radiotherapy-created"
    }
}

data class GuiaWithRadiotherapyCreatedPayload(
    val attachment: AttachmentRadiotherapy,
    val totvsGuia: TotvsGuia,
    val radiotherapyTotvsGuia: TotvsGuia,
    val referenceGuiaProcedures: List<MvAuthorizedProcedure>,
    val beneficiary: Beneficiary,
    val origin: Guia<PERSON>rigin,
    val person: Person,
)
