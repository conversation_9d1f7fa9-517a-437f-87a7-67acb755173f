package br.com.alice.exec.indicator.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.exec.indicator.SERVICE_NAME
import java.util.UUID

class ResourceBundleSpecialtyPricingUpdateCreatedEvent(
    id: UUID,
) : NotificationEvent<ResourceBundleSpecialtyPricingUpdateCreatedEventPayload>(
        name = name,
        producer = SERVICE_NAME,
        payload = ResourceBundleSpecialtyPricingUpdateCreatedEventPayload(id)
    ) {
    companion object {
        const val name = "resource-bundle-specialty-pricing-update-created"
    }
}

data class ResourceBundleSpecialtyPricingUpdateCreatedEventPayload(
    val resourceBundleSpecialtyPricingUpdateId: UUID
)
