package br.com.alice.exec.indicator.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.exec.indicator.SERVICE_NAME

class HealthcareBundleUpdatedEvent(payload: HealthcareBundle) :
    NotificationEvent<HealthcareBundle>(
        name = name,
        producer = SERVICE_NAME,
        payload = payload
    ) {
    companion object {
        const val name = "healthcare-bundle-updated"
    }
}
