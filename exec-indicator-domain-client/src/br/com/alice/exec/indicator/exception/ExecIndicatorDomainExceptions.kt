package br.com.alice.exec.indicator.exception

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException

class EitaAuthenticatorSendEmailError(
    message: String,
    code: String = "eita_auth_send_email_error",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(customMessage: String) : this(message ="Occur a error on send EITA authenticator email (${customMessage})")
}

class GuiaProcedureNotExistsException(
    message: String = "A guia não possuí procedimentos",
    code: String = "eita_guia_procedure_not_exists",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class GuiaExecutionGroupNotFoundException(
    message: String = "Guia não encontrada",
    code: String = "eita_guia_execution_group_not_found",
    cause: Throwable? = null
) : NotFoundException(message, code, cause)

class GuiaNotAuthorizedException(
    message: String = "A guia não está autorizada",
    code: String = "guia_not_authorized",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class GuiaWithoutStartDateException(
    message: String = "A guia de internação não possui data de entrada",
    code: String = "guia_without_start_date",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)
