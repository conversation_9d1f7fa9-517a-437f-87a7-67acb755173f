package br.com.alice.api.ehr.helpers

import br.com.alice.api.ehr.model.CaseRecordDetail
import br.com.alice.api.ehr.model.CaseRecordDetailResponse
import br.com.alice.api.ehr.model.HealthDemand
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildProviders
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaff
import br.com.alice.data.layer.models.*
import br.com.alice.healthplan.extensions.onValidInitState
import br.com.alice.healthplan.models.FollowUpRequestTransport
import br.com.alice.healthplan.models.GenericTaskTransport
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.healthplan.models.PrescriptionTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.healthplan.models.TestRequestTransport
import br.com.alice.product.model.ProductWithProviders
import br.com.alice.staff.converters.SimpleStaffResponseConverter
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.reflect.full.memberProperties

object TestTransportFactory {

    fun buildHealthPlanTransport(
        description: String = "Descrição",
        healthGoal: String = "Objetivo de saúde",
        personId: PersonId = PersonId(),
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
    ) =
        HealthPlanTransport(
            id = id,
            personId = personId.toString(),
            description = description,
            healthGoal = healthGoal,
            version = version,
            createdAt = LocalDateTime.now().toString()
        )

    fun buildPrescriptionTransport(
        healthPlanId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId()
    ) =
        PrescriptionTransport(
            id = RangeUUID.generate(),
            healthPlanId = healthPlanId,
            personId = personId.id,
            title = "title",
            description = "Remédios para rinite",
            status = HealthPlanTaskStatus.ACTIVE,
            createdAt = "2020-02-01T00:00",
            frequency = Frequency(FrequencyType.QUANTITY_IN_PERIOD, PeriodUnit.HOUR, 1),
            deadline = Deadline(PeriodUnit.WEEK, 1),
            start = Start(StartType.IF_HEADACHE, LocalDateTime.now()),
            dose = Dose(MedicineUnit.DROP, 10f),
            action = ActionType.DRIP,
            routeOfAdministration = RouteOfAdministration.NASAL,
            medicine = PrescriptionMedicine(
                RangeUUID.generate(),
                1000,
                "Narix",
                MedicineUnit.DROP,
                "1",
                "mg",
                "1",
                "Cloridrato de Nafazolina",
                PrescriptionMedicineType.SIMPLE
            ),
            packing = 1,
            group = HealthPlanTaskGroupTransport(RangeUUID.generate(), healthPlanId, "Nariz Entupido", personId),
            type = HealthPlanTaskType.PRESCRIPTION,
            favorite = false,
        )

    fun buildCaseRecordDetail(record: CaseRecord, staff: Staff) = CaseRecordDetail(
        addedByStaff = SimpleStaffResponseConverter.convert(staff),
        responsibleStaff = staff,
        description = record.description,
        observation = record.observation,
        severity = record.severity,
        status = record.status,
        addedAt = record.addedAt.toBrazilianDateTimeFormat(),
        id = record.id,
        startedAt = record.startedAt.toBrazilianDateTimeFormat(),
        referencedLinks = record.referencedLinks,
        caseCreatedBy = record.caseCreatedBy,
    )

    fun buildCaseRecordDetailResponse(
        record: CaseRecord,
        staff: Staff,
        records: List<CaseRecordDetail>
    ) = CaseRecordDetailResponse(
        addedByStaff = staff,
        responsibleStaff = staff,
        caseId = record.caseId,
        description = record.description,
        observation = record.observation,
        severity = record.severity,
        status = record.status,
        startedAt = record.startedAt.toBrazilianDateTimeFormat(),
        records = records,
        referencedLinks = record.referencedLinks
    )

    fun buildHealthDemand(
        personCase: PersonCase,
        staff: Staff,
        records: List<CaseRecordDetail>
    ) = HealthDemand(
        caseId = personCase.id,
        addedByStaff = staff,
        responsibleStaff = staff,
        description = Disease(
            id = personCase.healthConditionId,
            type = personCase.codeType,
            value = personCase.codeValue,
            description = personCase.codeDescription
        ),
        observation = personCase.observation,
        status = personCase.status,
        severity = personCase.severity,
        addedAt = personCase.addedAt.toBrazilianDateTimeFormat(),
        startedAt = personCase.startedAt.toBrazilianDateTimeFormat(),
        personId = personCase.personId.toString(),
        records = records,
        healthConditionId = personCase.healthConditionId
    )

    fun buildProduct(
        title: String = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
        displayName: String? = "Conforto +",
        complementName: String? = "(Reembolso)",
        prices: List<ProductPrice> = listOf(TestModelFactory.buildProductPrice()),
        bundleIds: List<UUID>? = null,
        id: UUID = RangeUUID.generate(),
        reference: Boolean = false,
        externalIds: List<ExternalId> = listOf(ExternalId(ExternalIdKey.MV_PLAN, "1")),
        anchor: ProductAnchor? = null,
        priceListing: PriceListing? = null,
        active: Boolean = true,
        type: ProductType = ProductType.B2C,
    ) =
        Product(
            id = id,
            title = title,
            displayName = displayName,
            complementName = complementName,
            prices = prices,
            externalIds = externalIds,
            bundleIds = bundleIds,
            reference = reference,
            anchor = anchor,
            active = active,
            type = type,
        ).withPriceListing(priceListing)

    fun buildProductWithProviders(): ProductWithProviders = ProductWithProviders(
        product = buildProduct(),
        providers = buildProviders()
    )

    fun buildHealthPlanTasksTransport(
        staff: Staff,
        prescription: List<Prescription> = emptyList(),
        eating: List<GenericTask> = emptyList(),
        physicalActivity: List<GenericTask> = emptyList(),
        sleep: List<GenericTask> = emptyList(),
        mood: List<GenericTask> = emptyList(),
        others: List<GenericTask> = emptyList(),
        testRequest: List<TestRequest> = emptyList(),
        referral: List<Referral> = emptyList(),
        followUpRequest: List<FollowUpRequest> = emptyList(),
        group: HealthPlanTaskGroupTransport? = null
    ) = HealthPlanTasksTransport(
        prescription = convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
            prescription,
            mapOf(staff.id to staff),
            HealthPlanTaskType.PRESCRIPTION,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        eating = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
            eating,
            mapOf(staff.id to staff),
            HealthPlanTaskType.EATING,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        physicalActivity = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
            physicalActivity,
            mapOf(staff.id to staff),
            HealthPlanTaskType.PHYSICAL_ACTIVITY,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        sleep = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
            sleep,
            mapOf(staff.id to staff),
            HealthPlanTaskType.SLEEP,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        mood = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
            mood,
            mapOf(staff.id to staff),
            HealthPlanTaskType.MOOD,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        others = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
            others,
            mapOf(staff.id to staff),
            HealthPlanTaskType.OTHERS,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        testRequest = convertHealthPlanTaskTransport<TestRequest, TestRequestTransport>(
            testRequest,
            mapOf(staff.id to staff),
            HealthPlanTaskType.TEST_REQUEST,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        referral = convertHealthPlanTaskTransport<Referral, ReferralTransport>(
            referral,
            mapOf(staff.id to staff),
            HealthPlanTaskType.REFERRAL,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
        followUpRequest = convertHealthPlanTaskTransport<FollowUpRequest, FollowUpRequestTransport>(
            followUpRequest,
            mapOf(staff.id to staff),
            HealthPlanTaskType.FOLLOW_UP_REQUEST,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        ),
    )

    fun buildReferralTransport(
        referral: List<Referral> = emptyList(),
        staff: Staff = buildStaff(),
        group: HealthPlanTaskGroupTransport? = null,
    ): List<ReferralTransport> =
        convertHealthPlanTaskTransport<Referral, ReferralTransport>(
            referral,
            mapOf(staff.id to staff),
            HealthPlanTaskType.REFERRAL,
            group?.let { mapOf(it.id!! to it) } ?: emptyMap()
        )

    inline fun <O : HealthPlanTask, reified D : HealthPlanTaskTransport> convertHealthPlanTaskTransport(
        tasks: List<HealthPlanTask>,
        staffMap: Map<UUID, Staff>,
        type: HealthPlanTaskType,
        groups: Map<UUID, HealthPlanTaskGroupTransport>
    ) =
        tasks.filter { it.type == type }.map { it.specialize<O>() }.map { origin ->
            val constructor = D::class.constructors.last()

            val values = origin::class.memberProperties
                .map {
                    it.name to it.getter.call(origin)
                }
                .toMap()

            val parameters = constructor.parameters
                .map { param ->
                    val name = param.name!!
                    val fieldValue = values[name]

                    val value = when {
                        name == HealthPlanTaskTransport::releasedBy.name -> {
                            origin.releasedByStaffId?.let {
                                createTaskRequester(
                                    staffMap.getValue(it),
                                    origin.releasedAt
                                )
                            }
                        }
                        name == HealthPlanTaskTransport::lastRequester.name -> {
                            createTaskRequester(
                                staffMap.getValue(origin.lastRequesterStaffId),
                                origin.updatedAt
                            )
                        }
                        name == HealthPlanTaskTransport::requesters.name -> {
                            origin.requestersStaffIds.map {
                                createTaskRequester(staffMap.getValue(it), null)
                            }.toSet()
                        }
                        name == HealthPlanTaskTransport::group.name -> {
                            groups[values[HealthPlanTask::groupId.name]]
                        }
                        name == HealthPlanTaskTransport::sentence.name -> {
                            origin.fullSentence()
                        }
                        name == HealthPlanTaskTransport::personId.name -> {
                            origin.personId.id
                        }
                        name == HealthPlanTaskTransport::memberCanInit.name -> {
                            origin.onValidInitState()
                        }
                        fieldValue is LocalDateTime -> fieldValue.toString()
                        fieldValue is LocalDate -> fieldValue.toString()
                        else -> fieldValue
                    }

                    param to value
                }.toMap()

            constructor.callBy(parameters) as D
        }

    fun createTaskRequester(
        staff: Staff,
        requestedAt: LocalDateTime? = null
    ): TaskRequesterRequest {
        return TaskRequesterRequest(
            staffId = staff.id,
            name = staff.fullName,
            profileImageUrl = staff.profileImageUrl,
            requestedAt = requestedAt.toString(),
            role = staff.role.description,
        )
    }
}
