package br.com.alice.api.ehr.services.dalya_recommendation

import br.com.alice.akinator.client.AIInferenceService
import br.com.alice.akinator.client.transfer.AIInferenceRequestInternalDTO
import br.com.alice.akinator.client.transfer.AIInferenceResponseDTO
import br.com.alice.api.ehr.ServiceConfig
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.channel.client.ChannelDeIdentifiedService
import br.com.alice.channel.models.MessageResponse
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.UserMessageType
import br.com.alice.client.dalya.models.AppointmentChatMessageRequest
import br.com.alice.client.dalya.models.AppointmentDiseaseRequest
import br.com.alice.client.dalya.models.AppointmentRecommendationRequest
import br.com.alice.client.dalya.models.AppointmentRecommendationWrapperResponse
import br.com.alice.client.dalya.models.DalyaPredictionValues
import br.com.alice.client.dalya.models.DalyaTriageRecommendationResponse
import br.com.alice.client.dalya.models.DalyaUncertaintiesRangeValues
import br.com.alice.client.dalya.models.DalyaUncertaintiesValues
import br.com.alice.client.dalya.models.HealthConditionCodeType
import br.com.alice.client.dalya.models.TriageSuggestedOutput
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AIInferenceResult
import br.com.alice.data.layer.models.AITextInferenceOutput
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.DiseaseDetails
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test

class DalyaRecommendationServiceTest {

    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val channelDeIdentifiedService: ChannelDeIdentifiedService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val aiInferenceService: AIInferenceService = mockk()

    private val service = DalyaRecommendationService(
        personInternalReferenceService,
        channelDeIdentifiedService,
        appointmentService,
        aiInferenceService
    )

    private val dalyaSetupId = ServiceConfig.DalyaAppointmentRecommendation.setupId.toUUID()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personInternalReferenceService,
        channelDeIdentifiedService,
        appointmentService,
        aiInferenceService
    )

    @Test
    fun `#getBy should return dalya recommendation successfully`() = mockRangeUUID {uuid ->
        val personId = PersonId()
        val channelId = "channel_id"

        val internalReference = TestModelFactory.buildPersonInternalReference(personId = personId)
        val appointment = TestModelFactory.buildAppointment(
            personId = personId,
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    description = DiseaseDetails(
                        type = Disease.Type.CIAP_2,
                        value = "N01"
                    ),
                    severity = CaseSeverity.DECOMPENSATED
                )
            )
        )

        val messages = listOf(
            MessageResponse(
                content = "ola, sou medico e vou te atender agora",
                type = MessageType.TEXT,
                createdAt = LocalDateTime.now(),
                userType = UserMessageType.STAFF
            ),
            MessageResponse(
                content = "ola, estou com dor de cabeca",
                type = MessageType.TEXT,
                createdAt = LocalDateTime.now().plusMinutes(2),
                userType = UserMessageType.PERSON
            ),
        )

        val dalyaRequest = buildDalyaRequest(internalReference.internalCode, messages)
        val dalyaResponse = buildDalyaResponse(internalReference.internalCode, uuid)

        val aiInferenceResponse = AIInferenceResponseDTO(
            id = uuid,
            result = AIInferenceResult.SUCCESS,
            outputs = listOf(
                AITextInferenceOutput(
                    model = "dalya v25",
                    result = AIInferenceResult.SUCCESS,
                    output = gson.toJson(dalyaResponse)
                )
            )
        )

        coEvery {
            channelDeIdentifiedService.getMessagesByChannelId(channelId)
        } returns messages.success()

        coEvery {
            appointmentService.get(appointment.id)
        } returns appointment.success()

        coEvery {
            personInternalReferenceService.getForPerson(personId)
        } returns internalReference.success()

        coEvery {
            aiInferenceService.dalyaInference(
                setupId = dalyaSetupId,
                request = AIInferenceRequestInternalDTO(
                    context = gson.toJsonTree(dalyaRequest).asJsonObject,
                    caller = EHR_API_ROOT_SERVICE_NAME
                )
            )
        } returns aiInferenceResponse.success()

        val result = service.getBy(appointmentId = appointment.id, channelId = channelId)

        ResultAssert.assertThat(result).isSuccessWithData(dalyaResponse.message)

        coVerifyOnce {
            channelDeIdentifiedService.getMessagesByChannelId(any())
            appointmentService.get(any())
            personInternalReferenceService.getForPerson(any())
            aiInferenceService.dalyaInference(any(), any())
        }
    }

    @Test
    fun `#getBy should return UnsupportedOperationException if Disease is empty`() = mockRangeUUID {uuid ->
        val personId = PersonId()
        val channelId = "channel_id"

        val internalReference = TestModelFactory.buildPersonInternalReference(personId = personId)
        val appointment = TestModelFactory.buildAppointment(
            personId = personId,
            caseRecordDetails = emptyList())

        val messages = listOf(
            MessageResponse(
                content = "ola, sou medico e vou te atender agora",
                type = MessageType.TEXT,
                createdAt = LocalDateTime.now(),
                userType = UserMessageType.STAFF
            ),
            MessageResponse(
                content = "ola, estou com dor de cabeca",
                type = MessageType.TEXT,
                createdAt = LocalDateTime.now().plusMinutes(2),
                userType = UserMessageType.PERSON
            ),
        )

        coEvery {
            channelDeIdentifiedService.getMessagesByChannelId(channelId)
        } returns messages.success()

        coEvery {
            appointmentService.get(appointment.id)
        } returns appointment.success()

        coEvery {
            personInternalReferenceService.getForPerson(personId)
        } returns internalReference.success()

        val result = service.getBy(appointmentId = appointment.id, channelId = channelId)

        ResultAssert.assertThat(result).isFailureOfType(UnsupportedOperationException::class)

        coVerifyOnce {
            channelDeIdentifiedService.getMessagesByChannelId(any())
            appointmentService.get(any())
            personInternalReferenceService.getForPerson(any())
        }

        coVerifyNone { aiInferenceService.dalyaInference(any(), any()) }
    }

    private fun buildDalyaRequest(
        internalCode: String,
        messages: List<MessageResponse>
    ) = AppointmentRecommendationRequest(
        memberInternalCode = internalCode,
        diseases = listOf(AppointmentDiseaseRequest(HealthConditionCodeType.CIAP_2, "N01")),
        chat = listOf(
            AppointmentChatMessageRequest(
                message = messages[0].content!!,
                timestamp = messages[0].createdAt,
                isMember = false
            ),
            AppointmentChatMessageRequest(
                message = messages[1].content!!,
                timestamp = messages[1].createdAt,
                isMember = true
            )
        ),
    )

    private fun buildDalyaResponse(
        internalCode: String,
        screeningId: UUID
    ) = AppointmentRecommendationWrapperResponse(
        message = DalyaTriageRecommendationResponse(
            memberInternalCode = internalCode,
            screeningId = screeningId,
            dalyaChoice = TriageSuggestedOutput.PA_DIGITAL,
            dalyaPrediction = DalyaPredictionValues(
                paDigital = 2F,
                ps = 1F,
                aps = 2F,
                specialist = 1F
            ),
            dalyaUncertainties = DalyaUncertaintiesValues(
                paDigital = DalyaUncertaintiesRangeValues(
                    lower = 1F,
                    upper = 9F
                ),
                ps = DalyaUncertaintiesRangeValues(
                    lower = 1F,
                    upper = 9F
                ),
                aps = DalyaUncertaintiesRangeValues(
                    lower = 1F,
                    upper = 9F
                ),
                specialist = DalyaUncertaintiesRangeValues(
                    lower = 1F,
                    upper = 9F
                ),
            )
        )
    )
}
