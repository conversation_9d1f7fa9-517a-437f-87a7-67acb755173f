package br.com.alice.api.ehr.services.appointment_autofill

import br.com.alice.channel.models.MessageResponse
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.UserMessageType
import br.com.alice.channel.models.VideoCallTranscriptionMessage
import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class ChatContentHelperTest {
    private val now = LocalDateTime.of(2023, 8, 2, 17, 30, 0)
    private val message = MessageResponse(
        id = RangeUUID.generate().toString(),
        userId = RangeUUID.generate().toString(),
        userType = UserMessageType.PERSON,
        content = "hii",
        createdAt = now,
        type = MessageType.TEXT
    )

    @Test
    fun `#filterAndFormatMessages should ignore message delete`() {
        val textMessageDelete = "\uD83D\uDEAB Esta mensagem foi apagada"
        val messages = listOf(message, message.copy(content = textMessageDelete))
        val result = ChatContentHelper.filterAndFormatMessages(messages)
        assertEquals(
            listOf("[2023-08-02T17:30] (Membro) hii"),
            result
        )
    }

    @Test
    fun `#filterAndFormatMessages should return messages`() {
        val messages = listOf(
            message,
            message.copy(userType = UserMessageType.STAFF, content = "hi @firstname")
        )
        val result = ChatContentHelper.filterAndFormatMessages(messages)
        assertEquals(
            listOf(
                "[2023-08-02T17:30] (Membro) hii",
                "[2023-08-02T17:30] (Médico) hi membro"
            ),
            result
        )
    }

    @Test
    fun `#filterAndFormatMessages should not show messages after channel rename`() {
        val messages = listOf(
            message,
            message.copy(userType = UserMessageType.STAFF, content = "hi @firstname"),
            message.copy(
                userType = UserMessageType.STAFF,
                content = "hi @firstname",
                type = MessageType.CHANNEL_RENAMED,
                createdAt = now.plusMinutes(5)
            ),
            message.copy(
                userType = UserMessageType.STAFF,
                content = "hi after rename",
                type = MessageType.TEXT,
                createdAt = now.plusMinutes(5)
            ),
        )
        val result = ChatContentHelper.filterAndFormatMessages(messages)
        assertEquals(
            listOf(
                "[2023-08-02T17:30] (Membro) hii",
                "[2023-08-02T17:30] (Médico) hi membro"
            ),
            result
        )
    }

    @Test
    fun `#formatVideoCallMessages should ignore message delete`() {
        val message1 = VideoCallTranscriptionMessage(
            startTime = LocalDateTime.parse("2024-06-21T15:51:11.428"),
            content = "Olá @firstname Bom dia.",
            userType = UserMessageType.STAFF
        )
        val message2 = VideoCallTranscriptionMessage(
            startTime = LocalDateTime.parse("2024-06-21T15:51:15.508"),
            content = "Eu estou com dor de cabeça e dor no corpo.",
            userType = UserMessageType.PERSON
        )
        val messages = listOf(
            message1,
            message2
        )

        val result = ChatContentHelper.formatVideoCallMessages(messages)
        assertEquals(
            listOf(
                "[2024-06-21T12:51:11.428] (Médico) Olá membro Bom dia.",
                "[2024-06-21T12:51:15.508] (Membro) Eu estou com dor de cabeça e dor no corpo.",
            ),
            result
        )
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type TEXT`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.TEXT, "ola @firstname")
        assertEquals("ola membro", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type STARTED_QUESTIONNAIRE`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.STARTED_QUESTIONNAIRE, "ola @firstname")
        assertEquals("Questionário de ola membro iniciado", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type ENDED_QUESTIONNAIRE`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.ENDED_QUESTIONNAIRE, "ola @firstname")
        assertEquals("Questionário finalizado", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type QUESTION`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.QUESTION, "ola @firstname")
        assertEquals("Pergunta: ola membro", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type ANSWER`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.ANSWER, "ola @firstname")
        assertEquals("Resposta: ola membro", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type IMAGE`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.IMAGE, "link.image.com")
        assertEquals("Aqui tem uma imagem", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type AUDIO`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.AUDIO, "link.audio.com")
        assertEquals("Aqui tem um audio", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type VIDEO`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.VIDEO, "link.video.com")
        assertEquals("Aqui tem um video", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with type DOCUMENT`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.DOCUMENT, "link.document.com")
        assertEquals("Aqui tem um arquivo", result)
    }

    @Test
    fun `#sanitizeUserMessageContent should sanitize content with invalid type`() {
        val result = ChatContentHelper.sanitizeUserMessageContent(MessageType.INTERNAL_REDIRECT, "invalid_type")
        assertEquals("invalid_type", result)
    }

    @Test
    fun `#naiveTextualDeIdentification`() {
        val result =
            ChatContentHelper.nativeTextualDeIdentification("hi @nickname your name is @firstname @lastname correct? this here  • • ")
        assertEquals("hi membro your name is membro correct? this here , ", result)
    }

    @Test
    fun `#sanitizeUserType should return member when type is person`() {
        val responsePerson = ChatContentHelper.sanitizeUserType(UserMessageType.PERSON)
        assertEquals("Membro", responsePerson)
    }

    @Test
    fun `#sanitizeUserType should return member when type is system`() {
        val result = ChatContentHelper.sanitizeUserType(UserMessageType.SYSTEM)
        assertEquals("Médico", result)
    }

    @Test
    fun `#sanitizeUserType should return member when type is staff`() {
        val result = ChatContentHelper.sanitizeUserType(UserMessageType.STAFF)
        assertEquals("Médico", result)
    }

    @Test
    fun `#sanitizeUserType should return member when type is null`() {
        val result = ChatContentHelper.sanitizeUserType(null)
        assertEquals("Médico", result)
    }
}
