package br.com.alice.api.ehr.services.person_health_info_summary

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ClinicalBackgroundStatus
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.ehr.client.ClinicalBackgroundFilters
import br.com.alice.ehr.client.ClinicalBackgroundService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class SearchClinicalBackgroundServiceTest {

    private val clinicalBackgroundService: ClinicalBackgroundService = mockk()

    private val service = SearchClinicalBackgroundService(
        clinicalBackgroundService
    )

    @BeforeTest
    fun setup() = confirmVerified(
        clinicalBackgroundService
    )

    @Test
    fun `#findByPersonId should return expected values`() = runBlocking {
        val personId = PersonId()
        val clinicalBackgrounds = listOf(
            TestModelFactory.buildUnstructuredClinicalBackground(
                type = ClinicalBackgroundType.ALLERGY,
                content = mapOf("value" to "Camarao")
            )
        )
        coEvery {
            clinicalBackgroundService.getByPersonIdWithFilters(
                personId = personId,
                filters = ClinicalBackgroundFilters(
                    types = listOf(ClinicalBackgroundType.ALLERGY),
                    status = ClinicalBackgroundStatus.ACTIVE
                )
            )
        } returns clinicalBackgrounds.success()


        val result = service.findByPersonId(personId)

        assertThat(result).isSuccessWithData(clinicalBackgrounds)

        coVerifyOnce { clinicalBackgroundService.getByPersonIdWithFilters(any(), any()) }
    }

}
