package br.com.alice.api.ehr.services.person_health_info_summary

import br.com.alice.appointment.client.AppointmentEventFilter
import br.com.alice.appointment.client.AppointmentFilter
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.converters.toResponse
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentEventDetail
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class SearchAcuteCasesServiceTest {

    private val appointmentService: AppointmentService = mockk()
    private val channelService: ChannelService = mockk()
    private val tertiaryIntentionTouchPointService: TertiaryIntentionTouchPointService = mockk()

    private val service = SearchAcuteCasesService(
        appointmentService,
        channelService,
        tertiaryIntentionTouchPointService,
    )

    @BeforeTest
    fun setup() = confirmVerified(
        appointmentService,
        channelService,
        tertiaryIntentionTouchPointService,
    )

    @Test
    fun `#findByPersonId should returns transport data successfully`() = mockLocalDateTime { now ->
        val personId = PersonId()
        val channelId = "channelId"

        val channel = TestModelFactory.buildChannel(
            channelId = channelId,
            personId = personId,
            subCategory = ChannelSubCategory.VIRTUAL_CLINIC
        ).toResponse()
        val appointment = TestModelFactory.buildAppointment(
            personId = personId,
            type = AppointmentType.ASSISTANCE_CARE,
            event = AppointmentEventDetail(
                name = "Atendimento assistencial",
                referenceModelId = channelId,
                referenceModel = AppointmentEventReferenceModel.CHANNEL
            )
        )
        val emergencyAttendance = TestModelFactory.buildTertiaryIntentionEmergency(personId = personId)
        val hospitalizationAttendance = TestModelFactory.buildTertiaryIntentionHospitalization(personId = personId)

        coEvery {
            appointmentService.find(
                AppointmentFilter(
                    personId = personId,
                    type = AppointmentType.ASSISTANCE_CARE,
                    event = AppointmentEventFilter(referenceModel = AppointmentEventReferenceModel.CHANNEL),
                    createdAtGreater = now.minusMonths(6),
                    orderByCreatedAt = true,
                    sortOrder = SortOrder.Descending
                )
            )
        } returns listOf(appointment).success()
        coEvery {
            channelService.findChannelsById(listOf(channelId))
        } returns listOf(channel).success()
        coEvery {
            tertiaryIntentionTouchPointService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = listOf(TertiaryIntentionType.TIT_EMERGENCY, TertiaryIntentionType.TIT_HOSPITALIZATION),
                dateInit = now.minusMonths(6),
                dateLimit = now
            )
        } returns listOf(emergencyAttendance, hospitalizationAttendance).success()

        val expected = AcuteCasesTransport(
            appointments = listOf(appointment),
            tertiaryIntentionTouchPoints = listOf(emergencyAttendance, hospitalizationAttendance),
        )

        val result = service.findByPersonId(personId)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentService.find(any()) }
        coVerifyOnce { channelService.findChannelsById(any()) }
        coVerifyOnce {
            tertiaryIntentionTouchPointService.findTertiaryIntentionByPeriodAndTypes(
                any(),
                any(),
                any(),
                any()
            )
        }
    }

    @Test
    fun `#findByPersonId should returns transport data successfully when it has many appointments per channel`() =
        mockLocalDateTime { now ->
            val personId = PersonId()
            val channelId = "channelId"

            val channel = TestModelFactory.buildChannel(
                channelId = channelId,
                personId = personId,
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC
            ).toResponse()
            val screeningAppointment = TestModelFactory.buildAppointment(
                personId = personId,
                type = AppointmentType.ASSISTANCE_CARE,
                event = AppointmentEventDetail(
                    name = "Atendimento triagem",
                    referenceModelId = channelId,
                    referenceModel = AppointmentEventReferenceModel.CHANNEL
                )
            ).copy(createdAt = now)
            val virtualClinicAppointment = TestModelFactory.buildAppointment(
                personId = personId,
                type = AppointmentType.ASSISTANCE_CARE,
                event = AppointmentEventDetail(
                    name = "Atendimento PA Digital",
                    referenceModelId = channelId,
                    referenceModel = AppointmentEventReferenceModel.CHANNEL
                )
            ).copy(createdAt = now.plusMinutes(10))

            coEvery {
                appointmentService.find(
                    AppointmentFilter(
                        personId = personId,
                        type = AppointmentType.ASSISTANCE_CARE,
                        event = AppointmentEventFilter(referenceModel = AppointmentEventReferenceModel.CHANNEL),
                        createdAtGreater = now.minusMonths(6),
                        orderByCreatedAt = true,
                        sortOrder = SortOrder.Descending
                    )
                )
            } returns listOf(screeningAppointment, virtualClinicAppointment).success()
            coEvery {
                channelService.findChannelsById(listOf(channelId))
            } returns listOf(channel).success()
            coEvery {
                tertiaryIntentionTouchPointService.findTertiaryIntentionByPeriodAndTypes(
                    personId = personId,
                    types = listOf(TertiaryIntentionType.TIT_EMERGENCY, TertiaryIntentionType.TIT_HOSPITALIZATION),
                    dateInit = now.minusMonths(6),
                    dateLimit = now
                )
            } returns emptyList<TertiaryIntentionTouchPoint>().success()

            val expected = AcuteCasesTransport(
                appointments = listOf(virtualClinicAppointment),
                tertiaryIntentionTouchPoints = emptyList(),
            )

            val result = service.findByPersonId(personId)

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { appointmentService.find(any()) }
            coVerifyOnce { channelService.findChannelsById(any()) }
            coVerifyOnce {
                tertiaryIntentionTouchPointService.findTertiaryIntentionByPeriodAndTypes(
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
        }

}
