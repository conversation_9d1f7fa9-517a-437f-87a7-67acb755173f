package br.com.alice.api.ehr.services.specialists

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.SpecialistType.STAFF
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.AssertionsForClassTypes.assertThat

class GetSpecialistsServiceImplTest {
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()

    private val personId = PersonId()


    private val specialty = TestModelFactory.buildMedicalSpecialty(
        requireSpecialist = true,
        generateGeneralistSubSpecialty = false
    )

    private val subSpecialty = TestModelFactory.buildMedicalSpecialty(
        id = RangeUUID.generate(),
        name = "test",
        type = MedicalSpecialtyType.SUBSPECIALTY,
        parentSpecialtyId = specialty.id
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        specialtyId = specialty.id,
        internalSpecialtyId = specialty.id
    ).copy(
        tier = SpecialistTier.EXPERT
    )

    private val generalistSubSpecialtyId = RangeUUID.generate()

    @Test
    fun `#get returns staff specialists`() = runBlocking {
        val service = GetSpecialistsServiceImpl(
            healthProfessionalService,
            medicalSpecialtyService,
        )
        val communitySpecialist = healthProfessional.copy(
            type = StaffType.COMMUNITY_SPECIALIST,
            role = Role.COMMUNITY
        )

        coEvery {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(listOf(specialty.id))
        } returns listOf(healthProfessional, communitySpecialist).success()

        coEvery {
            medicalSpecialtyService.getById(generalistSubSpecialtyId)
        } returns Result.failure(NotFoundException())

        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()

        val response = service.getByPerson(
            subSpecialtyId = generalistSubSpecialtyId,
            personId,
            specialtyId = specialty.id
        )

        assertThat(response).isEqualTo(
            listOf(
                Specialist(
                    name = "",
                    specialtyName = specialty.name,
                    id = communitySpecialist.staffId,
                    type = STAFF,
                    tier = SpecialistTier.EXPERT
                ),
                Specialist(
                    name = "",
                    specialtyName = specialty.name,
                    id = healthProfessional.staffId,
                    type = STAFF,
                    tier = SpecialistTier.EXPERT
                )
            )
        )
        coVerify(exactly = 1) {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(any())
        }
    }

    @Test
    fun `#get returns hp, specialist was filtered by sub specialty`() = runBlocking {
        val service = GetSpecialistsServiceImpl(
            healthProfessionalService,
            medicalSpecialtyService,
        )

        coEvery {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(listOf(specialty.id))
        } returns listOf(healthProfessional.copy(subSpecialtyIds = listOf(subSpecialty.id))).success()

        coEvery {
            medicalSpecialtyService.getById(subSpecialty.id)
        } returns subSpecialty.success()

        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()

        val response = service.getByPerson(
            subSpecialtyId = subSpecialty.id,
            personId,
            specialtyId = specialty.id
        )

        assertThat(response).isEqualTo(
            listOf(
                Specialist(
                    name = "",
                    specialtyName = specialty.name,
                    id = healthProfessional.staffId,
                    type = STAFF,
                    tier = SpecialistTier.EXPERT
                )
            )
        )
        coVerify(exactly = 1) {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(any())
        }
    }
}
