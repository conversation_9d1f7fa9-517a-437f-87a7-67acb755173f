package br.com.alice.api.ehr.services.internal

import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.client.SuggestedProcedureService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import br.com.alice.data.layer.models.SuggestedProcedureType.PROCEDURE_EXECUTED
import br.com.alice.exec.indicator.client.HealthSpecialistProcedureService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.TussCodeWithAliceCodeRequest
import br.com.alice.exec.indicator.client.TussProcedureSpecialtyService
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregate
import br.com.alice.exec.indicator.models.ResourceSuggestedProcedure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class AppointmentExecutedInternalServiceTest {
    private val procedureExecutedService: AppointmentProcedureExecutedService = mockk()
    private val suggestedProcedureService: SuggestedProcedureService = mockk()
    private val tussProcedureSpecialtyService: TussProcedureSpecialtyService = mockk()
    private val healthSpecialistProcedureService: HealthSpecialistProcedureService = mockk()
    private val healthSpecialistResourceBundleManagementService: HealthSpecialistResourceBundleManagementService =
        mockk()


    private val service = AppointmentExecutedInternalService(
        procedureExecutedService,
        suggestedProcedureService,
        tussProcedureSpecialtyService,
        healthSpecialistProcedureService,
        healthSpecialistResourceBundleManagementService
    )

    private val appointmentId = RangeUUID.generate()
    private val procedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
        id = RangeUUID.generate(),
        tussCode = "123",
        tussProcedureAliceCode = "456",
        isPriced = true,
        appointmentId = appointmentId,
        healthSpecialistResourceBundleCode = "456",
        status = ProcedureExecutedStatus.DRAFT

    )
    val procedure = ProcedureResponse(
        id = procedureExecuted.id,
        name = "Consulta Alice",
        tussCode = procedureExecuted.tussCode,
        aliceCode = procedureExecuted.tussProcedureAliceCode,
        groupType = ProcedureGroupType.CONSULTA,
        isPriced = procedureExecuted.isPriced
    )

    private val suggestedProcedure = TestModelFactory.buildSuggestedProcedure(
        id = RangeUUID.generate(),
        tussCode = "123",
        tussProcedureAliceCode = "456",
        healthSpecialistResourceBundleCode = "456",
        specialtyId = RangeUUID.generate()
    )
    private val tussProcedureSpecialty = TestModelFactory.buildTussProcedureSpecialty(
        aliceCode = suggestedProcedure.tussProcedureAliceCode,
        tussCode = suggestedProcedure.tussCode,
    )

    private val healthSpecialistProcedures = listOf(
        HealthSpecialistProcedure(
            tussCode = procedureExecuted.tussCode,
            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode,
            description = "Consulta Alice",
            serviceType = HealthSpecialistProcedureServiceType.CONSULTATION,
            hasPrice = true,
        )
    )
    private val specialtyId = RangeUUID.generate()
    private val defaultProcedureAggregate = ResourceBundleSpecialtyAggregate(
        id = RangeUUID.generate(),
        appointmentRecommendationLevel = AppointmentRecommendationLevel.DEFAULT,
        status = Status.ACTIVE,
        medicalSpecialtyId = specialtyId,
        primaryTuss = "TUSS",
        code = "alice code",
        description = "description",
        healthSpecialistResourceBundleStatus = Status.ACTIVE,
        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
    )

    private val suggestedProcedureAggregate = ResourceBundleSpecialtyAggregate(
        id = RangeUUID.generate(),
        appointmentRecommendationLevel = AppointmentRecommendationLevel.RECOMMENDED,
        status = Status.ACTIVE,
        medicalSpecialtyId = specialtyId,
        primaryTuss = "TUSS",
        code = "alice code",
        description = "description",
        healthSpecialistResourceBundleStatus = Status.ACTIVE,
        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
    )
    private val resourceSuggestedProcedure = ResourceSuggestedProcedure(
        procedureDefault = defaultProcedureAggregate,
        suggestedProcedure = listOf(suggestedProcedureAggregate)
    )

    @Test
    fun `#getByAppointmentId should return a list of ProcedureExecutedResponse with test code description`() =
        runBlocking {
            val expected = listOf(
                procedure.copy(
                    id = procedure.id,
                    name = "Consulta Alice",
                    tussCode = procedure.tussCode,
                    aliceCode = procedure.aliceCode,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = procedure.isPriced
                )
            )

            coEvery { procedureExecutedService.getByAppointmentId(appointmentId) } returns listOf(procedureExecuted).success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = procedureExecuted.tussCode,
                            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()

            val result = service.getByAppointmentId(appointmentId)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { procedureExecutedService.getByAppointmentId(any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
        }

    @Test
    fun `#getByAppointmentId should return a list of ProcedureExecutedResponse with tuss description`() =
        runBlocking {
            val procedureExecuted = procedureExecuted.copy(
                tussProcedureAliceCode = null, healthSpecialistResourceBundleCode = null
            )
            val expected = listOf(
                procedure.copy(
                    id = procedure.id,
                    name = "Consulta Alice",
                    tussCode = procedure.tussCode,
                    aliceCode = null,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = procedure.isPriced
                )
            )

            coEvery { procedureExecutedService.getByAppointmentId(appointmentId) } returns listOf(procedureExecuted).success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = procedureExecuted.tussCode,
                            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()

            val result = service.getByAppointmentId(appointmentId)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { procedureExecutedService.getByAppointmentId(any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
        }

    @Test
    fun `#getDefaultProcedure get default procedure with test code description`() =
        runBlocking {
            val expectedValue = ProcedureResponse(
                id = suggestedProcedure.id,
                name = "Consulta Alice",
                tussCode = suggestedProcedure.tussCode,
                aliceCode = suggestedProcedure.tussProcedureAliceCode,
                groupType = ProcedureGroupType.CONSULTA,
                isPriced = true
            )

            coEvery { suggestedProcedureService.getDefaultProcedure(suggestedProcedure.specialtyId) } returns suggestedProcedure.success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = suggestedProcedure.tussCode,
                            aliceCode = suggestedProcedure.tussProcedureAliceCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()
            coEvery {
                tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                    suggestedProcedure.specialtyId,
                    listOf(suggestedProcedure.tussProcedureAliceCode)
                )
            } returns listOf(tussProcedureSpecialty).success()


            val result = service.getDefaultProcedure(suggestedProcedure.specialtyId)
            assertThat(result).isSuccessWithData(expectedValue)


            coVerifyOnce { suggestedProcedureService.getDefaultProcedure(any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
        }

    @Test
    fun `#getDefaultProcedure get default procedure with test code description with feature flag active`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.EHR, "allow_new_suggested_procedure", true) {
                val tussProcedureSpecialty = TestModelFactory.buildTussProcedureSpecialty(
                    aliceCode = defaultProcedureAggregate.code,
                    tussCode = defaultProcedureAggregate.primaryTuss,
                )
                val expectedValue = ProcedureResponse(
                    id = defaultProcedureAggregate.id,
                    name = defaultProcedureAggregate.description,
                    tussCode = defaultProcedureAggregate.primaryTuss,
                    aliceCode = defaultProcedureAggregate.code,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = true
                )

                coEvery { healthSpecialistResourceBundleManagementService.getSuggestedBySpecialty(specialtyId) } returns resourceSuggestedProcedure.success()
                coEvery {
                    tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                        specialtyId,
                        listOf(resourceSuggestedProcedure.procedureDefault!!.code)
                    )
                } returns listOf(tussProcedureSpecialty).success()

                val result = service.getDefaultProcedure(specialtyId)
                assertThat(result).isSuccessWithData(expectedValue)

                coVerifyOnce { healthSpecialistResourceBundleManagementService.getSuggestedBySpecialty(any()) }
                coVerifyOnce { tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(any(), any()) }
            }
        }

    @Test
    fun `addProcedureResponse should add procedure and return procedure response`() = runBlocking {
        val expected = listOf(procedure.copy(id = procedureExecuted.id))
        coEvery {
            procedureExecutedService.addDefaultProcedureByAppointment(match {
                it.tussCode == procedureExecuted.tussCode &&
                        it.tussProcedureAliceCode == procedureExecuted.tussProcedureAliceCode &&
                        it.isPriced == procedureExecuted.isPriced &&
                        it.appointmentId == appointmentId &&
                        it.status == ProcedureExecutedStatus.DRAFT
            })
        } returns procedureExecuted.success()

        val result = service.addProcedureDefault(appointmentId, procedure)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { procedureExecutedService.addDefaultProcedureByAppointment(any()) }
    }

    @Test
    fun `add should add procedure and return response`() = runBlocking {
        val expected = procedure.copy(id = procedureExecuted.id)

        coEvery {
            procedureExecutedService.add(procedureExecuted)
        } returns procedureExecuted.success()
        coEvery {
            healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                listOf(
                    TussCodeWithAliceCodeRequest(
                        tussCode = procedureExecuted.tussCode,
                        aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                    )
                )
            )
        } returns healthSpecialistProcedures.success()

        val result = service.add(procedureExecuted)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { procedureExecutedService.add(any()) }
        coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
    }

    @Test
    fun `deleteDraftProcedure should delete draft procedure`() = runBlocking {
        coEvery { procedureExecutedService.deleteDraftProcedure(procedureExecuted.id) } returns true.success()

        val result = service.deleteDraftProcedure(procedureExecuted.id)
        assertThat(result).isSuccess()

        coVerifyOnce { procedureExecutedService.deleteDraftProcedure(any()) }
    }

    @Test
    fun `#getSuggestedProcedures should return procedure response with invalid procedure together`() =
        runBlocking {
            val expected = listOf(procedure.copy(id = suggestedProcedure.id))

            coEvery {
                suggestedProcedureService.getSuggestionBySpecialtyAndType(
                    suggestedProcedure.specialtyId,
                    PROCEDURE_EXECUTED
                )
            } returns listOf(suggestedProcedure, suggestedProcedure.copy(tussCode = "IVALID_TUSS")).success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = suggestedProcedure.tussCode,
                            aliceCode = suggestedProcedure.healthSpecialistResourceBundleCode
                        ),
                        TussCodeWithAliceCodeRequest(
                            tussCode = "IVALID_TUSS",
                            aliceCode = suggestedProcedure.healthSpecialistResourceBundleCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()
            coEvery {
                tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                    suggestedProcedure.specialtyId,
                    listOf(suggestedProcedure.healthSpecialistResourceBundleCode!!)
                )
            } returns listOf(tussProcedureSpecialty).success()

            val result =
                service.getSuggestedProcedures(suggestedProcedure.specialtyId, PROCEDURE_EXECUTED)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { suggestedProcedureService.getSuggestionBySpecialtyAndType(any(), any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
            coVerifyOnce { tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(any(), any()) }
        }

    @Test
    fun `#getSuggestedProcedures should return procedure response with feature flag active`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.EHR, "allow_new_suggested_procedure", true) {
                val tussProcedureSpecialty = TestModelFactory.buildTussProcedureSpecialty(
                    aliceCode = suggestedProcedureAggregate.code,
                    tussCode = suggestedProcedureAggregate.primaryTuss,
                )
                val expected = listOf(
                    ProcedureResponse(
                        id = suggestedProcedureAggregate.id,
                        name = suggestedProcedureAggregate.description,
                        tussCode = suggestedProcedureAggregate.primaryTuss,
                        aliceCode = suggestedProcedureAggregate.code,
                        groupType = ProcedureGroupType.CONSULTA,
                        isPriced = true
                    )
                )

                coEvery {
                    healthSpecialistResourceBundleManagementService.getSuggestedBySpecialty(
                        specialtyId
                    )
                } returns resourceSuggestedProcedure.success()

                coEvery {
                    tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(
                        specialtyId,
                        resourceSuggestedProcedure.suggestedProcedure.map { it.code }
                    )
                } returns listOf(tussProcedureSpecialty).success()

                val result =
                    service.getSuggestedProcedures(specialtyId, PROCEDURE_EXECUTED)
                assertThat(result).isSuccessWithData(expected)

                coVerifyOnce { healthSpecialistResourceBundleManagementService.getSuggestedBySpecialty(any()) }
                coVerifyOnce { tussProcedureSpecialtyService.findActiveByAliceCodesAndSpeciality(any(), any()) }
            }
        }
}
