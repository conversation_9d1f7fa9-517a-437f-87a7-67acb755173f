package br.com.alice.api.ehr

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.ProviderTestCode.Brand.DELBONI_AURIEMO
import br.com.alice.data.layer.models.ProviderTestCode.Brand.LAVOISIER
import br.com.alice.data.layer.models.ProviderTestCode.Brand.SALOMAO_ZOPPI
import br.com.alice.provider.client.UpsertProviderTestCodeRequest
import io.ktor.client.HttpClient
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.TextContent
import io.ktor.http.withCharset
import kotlinx.coroutines.coroutineScope
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import java.nio.file.Files
import java.nio.file.Paths

// Uses spreadsheet "Ids": https://docs.google.com/spreadsheets/d/1tklwasMORcOXcZF3zQY0aJdxtNTHd75RkN9JfkEuG8c/edit#gid=**********
suspend fun main() = coroutineScope {
    val authToken = "authToken"
    val csvFile = "csvFile.csv"
    val ehrApi = "https://ehr-api-prod.wonderland.engineering"
    val offset = 0
    val limit = 10_000

    val reader = Files.newBufferedReader(Paths.get(csvFile))
    val csvParser = CSVParser(reader, CSVFormat.DEFAULT.withDelimiter(',').withFirstRecordAsHeader())

    val client = HttpClient()

    // Production Ids
    val providerIds = mapOf(
        Pair("lavoisier", "1368c520-813c-4f6f-bd4c-94d66eb02c00"),
        Pair("salomao", "cb3733fc-018b-48f8-97d3-166bb7e45300"),
        Pair("delboni", "a9b390a4-f964-4002-8f0d-dbf4d3be6400"),
    )

    val startTime = System.nanoTime()

    suspend fun processRow(csvRecord: CSVRecord, index: Int): Boolean {
        val providers = providerIds.keys
        providers.forEach {
            if (csvRecord.get(it) == "sim") {
                val request = UpsertProviderTestCodeRequest(
                    testCode = csvRecord.get("cod alice"),
                    providerCode = csvRecord.get("código dasa"),
                    providerId = providerIds[it]!!.toUUID(),
                    providerBrand = if (it == "lavoisier") LAVOISIER
                    else if (it == "salomao") SALOMAO_ZOPPI
                    else DELBONI_AURIEMO
                )

                val response = client.post("$ehrApi/admin/provider_test_codes") {
                    header("Authorization", "Bearer $authToken")
                    setBody(TextContent(gson.toJson(request), ContentType.Application.Json.withCharset(Charsets.UTF_8)))
                }

                if (response.status != HttpStatusCode.OK) {
                    println("Error importing index=$index request=$request status=${response.status} content=${response.bodyAsText()}")
                    return false
                }
            }
        }

        return true
    }

    var i = 0

    for (csvRecord in csvParser) {
        i += 1

        if (i < offset) continue

        processRow(csvRecord, i)

        if (i % 10 == 0) println(i)
        if (i >= limit) break
    }

    val endTime = System.nanoTime()
    val duration = (endTime - startTime).toDouble() / 1_000_000_000
    println("Duration: $duration")
}
