package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.storage.FileResponse
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.ExcuseNote
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.apache.commons.lang3.RandomStringUtils
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class DocumentControllerTest : ControllerTestHelper() {

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val fileVaultStorage: FileVaultStorage = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val pdfController = DocumentController(healthPlanTaskService, appointmentService, fileVaultStorage)

    private val itiGovFormat = "application/validador-iti json"
    private val shortId = RandomStringUtils.randomAlphanumeric(6)
    private val token = RandomStringUtils.randomNumeric(4)
    private val fileUrl = "https://file-url"
    private val attachment = Attachment(
        id = RangeUUID.generate(),
        type = "pdf",
        fileName = "Prescrição.pdf"
    )

    private val fileResponse = FileResponse(
        id = attachment.id,
        url = fileUrl
    )

    private val task = TestModelFactory.buildHealthPlanTaskPrescription()
        .let {
            it.copy(
                content = it.content?.plus(mapOf("shortId" to shortId, "token" to token)),
                attachments = listOf(attachment)
            )
        }

    private val exceptedResponse = DocumentResponse(
        prescription = PrescriptionDocument(
            signatureFiles = listOf(
                SignatureFile(url = fileUrl)
            )
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        clearMocks(healthPlanTaskService, fileVaultStorage)
        module.single { pdfController }
    }

    @Test
    fun `#getPDF returns 400 when _format is invalid`() {
        internalAuthentication {
            get("/pdf/$shortId?_format=xpto&_secretCode=$token") { response ->
                assertThat(response).isBadRequest()

                coVerify { healthPlanTaskService wasNot called }
                coVerify { fileVaultStorage wasNot called }
            }
        }
    }

    @Test
    fun `#getPDF returns 401 when short id is invalid`() {
        internalAuthentication {
            get("/pdf/dUdnf3493JAD?_format=$itiGovFormat&_secretCode=$token") { response ->
                assertThat(response).isUnauthorized()

                coVerify { healthPlanTaskService wasNot called }
                coVerify { fileVaultStorage wasNot called }
            }
        }
    }

    @Test
    fun `#getPDF returns 401 when token id is invalid`() {
        internalAuthentication {
            get("/pdf/$shortId?_format=$itiGovFormat&_secretCode=12983021") { response ->
                assertThat(response).isUnauthorized()

                coVerify { healthPlanTaskService wasNot called }
                coVerify { fileVaultStorage wasNot called }
            }
        }
    }

    @Test
    fun `#getPDF returns temporary file url`() {
        coEvery {
            healthPlanTaskService.getTask(shortId, token)
        } returns task.success()

        coEvery {
            fileVaultStorage.getFileById(attachment.id.toString())
        } returns fileResponse

        internalAuthentication {
            get("/pdf/$shortId?_format=$itiGovFormat&_secretCode=$token") { response ->
                assertThat(response).isSuccessfulJson()

                val content: DocumentResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(exceptedResponse)

                coVerify(exactly = 1) { healthPlanTaskService.getTask(any(), any()) }
                coVerify(exactly = 1) { fileVaultStorage.getFileById(any()) }
            }
        }
    }

    @Test
    fun `#getExcuseNoteDocumentResponse returns temporary file url`() {
        val appointment = TestModelFactory.buildAppointment().copy(
            excuseNotes = listOf(
                ExcuseNote(
                    description = "teste",
                    id = "12345",
                    token = "5432",
                    attachmentId = attachment.id.toString()
                )
            )
        )

        coEvery {
            appointmentService.get(appointment.id)
        } returns appointment.success()

        coEvery {
            fileVaultStorage.getFileById(attachment.id.toString())
        } returns fileResponse

        internalAuthentication {
            get("/excuse-note-pdf/${appointment.id}/12345?_format=$itiGovFormat&_secretCode=5432") { response ->

                assertThat(response).isOKWithData(exceptedResponse)

                coVerify(exactly = 1) { appointmentService.get(any()) }
                coVerify(exactly = 1) { fileVaultStorage.getFileById(any(), any()) }
            }
        }
    }

    @Test
    fun `#getPDF returns 404 when not found task`() {
        coEvery {
            healthPlanTaskService.getTask(shortId, token)
        } returns NotFoundException().failure()

        internalAuthentication {
            get("/pdf/$shortId?_format=$itiGovFormat&_secretCode=$token") { response ->
                assertThat(response).isNotFound()

                coVerify(exactly = 1) { healthPlanTaskService.getTask(any(), any()) }
                coVerify { fileVaultStorage wasNot called }
            }
        }
    }

    @Test
    fun `#getPDF returns 500 if file not is a PDF`() {
        coEvery {
            healthPlanTaskService.getTask(shortId, token)
        } returns task.copy(
            attachments = listOf(attachment.copy(type = "jpg"))
        ).success()

        internalAuthentication {
            get("/pdf/$shortId?_format=$itiGovFormat&_secretCode=$token") { response ->
                assertThat(response).isInternalServerError()

                coVerify(exactly = 1) { healthPlanTaskService.getTask(any(), any()) }
                coVerify { fileVaultStorage wasNot called }
            }
        }
    }

    @Test
    fun `#getPDF returns 404 if file not found in File Vault`() {
        coEvery {
            healthPlanTaskService.getTask(shortId, token)
        } returns task.success()

        coEvery {
            fileVaultStorage.getFileById(attachment.id.toString())
        } returns null

        internalAuthentication {
            get("/pdf/$shortId?_format=$itiGovFormat&_secretCode=$token") { response ->
                assertThat(response).isNotFound()

                coVerify(exactly = 1) { healthPlanTaskService.getTask(any(), any()) }
                coVerify(exactly = 1) { fileVaultStorage.getFileById(any()) }
            }
        }
    }
}
