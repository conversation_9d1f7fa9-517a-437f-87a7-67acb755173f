package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.coverage.client.City
import br.com.alice.coverage.client.LocationService
import br.com.alice.coverage.client.State
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class LocationControllerTest : ControllerTestHelper() {

    private val locationService: LocationService = mockk()

    private val controller = LocationController(locationService)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(locationService)

    private val saoPauloState = State(id = "35", name = "São Paulo", code = "SP")
    private val saoBernardo = City(id = "10", name = "São Bernardo", state = saoPauloState)
    private val saoCaetano = City(id = "1", name = "São Caetano", state = saoPauloState)

    @Test
    fun `#getStates returns all states`() = runBlocking {
        coEvery { locationService.getStates() } returns listOf(saoPauloState).success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/locations/states") { response ->
                assertThat(response).isOKWithData(listOf(saoPauloState))
            }
        }

        coVerify(exactly = 1) { locationService.getStates() }
    }

    @Test
    fun `#searchCities returns a list of cities`() = runBlocking {
        val query = "sao"

        coEvery { locationService.searchCities(saoPauloState.id, query) } returns listOf(saoBernardo, saoCaetano).success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/locations/states/${saoPauloState.id}/cities?q=$query") { response ->
                assertThat(response).isOKWithData(listOf(saoBernardo, saoCaetano))
            }
        }

        coVerify(exactly = 1) { locationService.searchCities(any(), any()) }
    }

    @Test
    fun `#searchCities returns an error if no query is present`() = runBlocking {
        authenticatedAs(idToken, staffTest) {
            get("/ehr/locations/states/35/cities") { response ->
                assertThat(response).isBadRequestWithErrorCode(
                    "missing_query_parameter"
                )
            }
        }

        coVerify { locationService wasNot called }
    }
}
