package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.HospitalizationAttendanceRequest
import br.com.alice.api.ehr.controllers.model.TertiaryIntentionEvolutionRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TertiaryIntentionDischargeSummaryEvaluation
import br.com.alice.data.layer.models.TertiaryIntentionEvolution
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationOrigin
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationProcedure
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationResponsible
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class HospitalizationAttendanceConverterTest {

    @Test
    fun `#HospitalizationAttendanceResponseConverter should convert correctly`() {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val procedure = TestModelFactory.buildHealthcareResource()
        val person = TestModelFactory.buildPerson()
        val staff = TestModelFactory.buildStaff()
        val startedAt = LocalDateTime.now()
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialist = TestModelFactory.buildHealthProfessional(
            specialtyId = specialty.id,
            staffId = staff.id
        )
        val counterReferral = TestModelFactory.buildCounterReferral(
            staffId = staff.id
        )
        val guia = TestModelFactory.buildTotvsGuia()

        val hospitalizationIntention = TertiaryIntentionTouchPoint(
            personId = person.id,
            staffId = staff.id,
            providerUnitId = providerUnit.id,
            type = TertiaryIntentionType.TIT_HOSPITALIZATION,
            startedAt = startedAt,
            hospitalizationOrigin = TertiaryIntentionHospitalizationOrigin.URGENCY,
            hospitalizationResponsible = TertiaryIntentionHospitalizationResponsible.ALICE_REAR,
            internalPhysician = "Internal Physician",
            hasDischargeSummaryOnTime = false,
            hasFollowTheFlow = true,
            hospitalizationProcedures = listOf(
                TertiaryIntentionHospitalizationProcedure(
                    procedureDate = LocalDate.now(),
                    procedureCode = procedure.code
                )
            ),
            evolutions = listOf(
                TertiaryIntentionEvolution(
                    createdAt = LocalDateTime.now(),
                    description = "Paciente melhorou",
                    staffId = staff.id
                )
            ),
            dischargeForecastInDays = 10,
            dischargeSummaryEvaluation = TertiaryIntentionDischargeSummaryEvaluation.USEFUL_INFORMATION,
            counterReferrals = listOf(counterReferral.id),
            totvsGuias = listOf(guia.id),
        )

        val providerUnitPair = mapOf(providerUnit.id to providerUnit.name)
        val procedurePair = mapOf(procedure.code to procedure)
        val staffPair = mapOf(staff.id to staff.fullName)
        val guiasPair = mapOf(guia.id to guia)
        val counterReferralPair = mapOf(counterReferral.id to counterReferral)
        val healthProfessionalsPair = mapOf(specialist.staffId to specialist)
        val specialtiesPair = mapOf(specialty.id to specialty)

        val result = HospitalizationAttendanceResponseConverter.convert(
            hospitalizationIntention,
            providerUnitPair,
            staffPair,
            procedurePair,
            guiasPair,
            counterReferralPair,
            healthProfessionalsPair,
            specialtiesPair,
        )

        assertThat(result.id).isEqualTo(hospitalizationIntention.id)
        assertThat(result.providerUnit?.name).isEqualTo(providerUnit.name)
        assertThat(result.staffName).isEqualTo(staff.fullName)
        assertThat(result.type).isEqualTo(TertiaryIntentionType.TIT_HOSPITALIZATION)
        assertThat(result.hospitalizationProcedures.size).isEqualTo(1)
        assertThat(result.hospitalizationProcedures.first().procedure.tussCode).isEqualTo(procedure.code)
        assertThat(result.startedAt).isEqualTo(startedAt)
        assertThat(result.hospitalizationOrigin).isEqualTo(hospitalizationIntention.hospitalizationOrigin)
        assertThat(result.hospitalizationResponsible).isEqualTo(hospitalizationIntention.hospitalizationResponsible)
        assertThat(result.hasDischargeSummaryOnTime).isEqualTo(hospitalizationIntention.hasDischargeSummaryOnTime)
        assertThat(result.internalPhysician).isEqualTo(hospitalizationIntention.internalPhysician)
        assertThat(result.hasFollowTheFlow).isEqualTo(hospitalizationIntention.hasFollowTheFlow)
        assertThat(result.evolutions.first().staffName).isEqualTo(staff.fullName)
        assertThat(result.evolutions.first().description)
            .isEqualTo(hospitalizationIntention.evolutions.first().description)
        assertThat(result.dischargeForecastInDays).isEqualTo(hospitalizationIntention.dischargeForecastInDays)
        assertThat(result.dischargeSummaryEvaluation).isEqualTo(hospitalizationIntention.dischargeSummaryEvaluation)

    }

    @Test
    fun `#HospitalizationAttendanceRequestConverter should convert correctly`() {
        val personId = PersonId()
        val staffId = RangeUUID.generate()
        val procedure = TestModelFactory.buildHealthcareResource()
        val request = HospitalizationAttendanceRequest(
            providerUnitId = RangeUUID.generate(),
            hospitalizationOrigin = TertiaryIntentionHospitalizationOrigin.URGENCY,
            hospitalizationResponsible = TertiaryIntentionHospitalizationResponsible.ALICE_REAR,
            internalPhysician = "Potato Developer",
            hasDischargeSummaryOnTime = false,
            hasFollowTheFlow = true,
            hospitalizationProcedures = listOf(
                TertiaryIntentionHospitalizationProcedure(
                    procedureDate = LocalDate.now(),
                    procedureCode = procedure.code
                )
            ),
            newEvolutions = listOf(TertiaryIntentionEvolutionRequest(description = "Paciente melhorou")),
            dischargeForecastInDays = 10,
            dischargeSummaryEvaluation = TertiaryIntentionDischargeSummaryEvaluation.USEFUL_INFORMATION,
        )

        val result = HospitalizationAttendanceRequestConverter.convert(request, personId, staffId)

        assertThat(result.personId).isEqualTo(personId)
        assertThat(result.staffId).isEqualTo(staffId)
        assertThat(result.providerUnitId).isEqualTo(request.providerUnitId)
        assertThat(result.hospitalizationProcedures.size).isEqualTo(1)
        assertThat(result.hospitalizationProcedures.first().procedureCode).isEqualTo(procedure.code)
        assertThat(result.hospitalizationOrigin).isEqualTo(request.hospitalizationOrigin)
        assertThat(result.hospitalizationResponsible).isEqualTo(request.hospitalizationResponsible)
        assertThat(result.hasDischargeSummaryOnTime).isEqualTo(request.hasDischargeSummaryOnTime)
        assertThat(result.internalPhysician).isEqualTo(request.internalPhysician)
        assertThat(result.hasFollowTheFlow).isEqualTo(request.hasFollowTheFlow)
        assertThat(result.evolutions.first().description).isEqualTo(request.newEvolutions.first().description)
        assertThat(result.evolutions.first().staffId).isEqualTo(staffId)
        assertThat(result.dischargeForecastInDays).isEqualTo(request.dischargeForecastInDays)
        assertThat(result.dischargeSummaryEvaluation).isEqualTo(request.dischargeSummaryEvaluation)

    }

}
