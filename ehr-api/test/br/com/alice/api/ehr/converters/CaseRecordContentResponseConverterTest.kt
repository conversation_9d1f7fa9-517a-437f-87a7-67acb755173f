package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.helpers.TestTransportFactory
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class CaseRecordContentResponseConverterTest {
    private val staff = TestModelFactory.buildStaff()
    private val responsibleStaff = TestModelFactory.buildStaff()
    private val personId = PersonId()
    private val caseRecord = TestModelFactory.buildCaseRecord(
        personId = personId,
        addedByStaffId = staff.id,
        responsibleStaffId = responsibleStaff.id
    )
    private val caseRecordDetail = TestTransportFactory.buildCaseRecordDetail(
        caseRecord, staff
    ).copy(responsibleStaff = responsibleStaff)
    private val caseRecordDetailResponse = TestTransportFactory.buildCaseRecordDetailResponse(
        record = caseRecord,
        staff = staff,
        records = listOf(caseRecordDetail)
    ).copy(responsibleStaff = responsibleStaff)
    private val staffMap = mapOf(staff.id to staff, responsibleStaff.id to responsibleStaff)

    @Test
    fun `#CaseRecordDetailResponseConverter returns response with staff`() {
        val response = CaseRecordDetailResponseConverter.convert(listOf(caseRecord), staffMap)

        assertThat(response).isEqualTo(caseRecordDetailResponse)
    }

    @Test
    fun `#CaseRecordDetailResponseConverter returns response with null addedByStaff`() {
        val caseRecord = caseRecord.copy(addedByStaffId = null)
        val caseRecordDetail = caseRecordDetail.copy(addedByStaff = null)
        val response = CaseRecordDetailResponseConverter.convert(listOf(caseRecord), mapOf(responsibleStaff.id to responsibleStaff))

        val caseRecordDetailResponse = TestTransportFactory.buildCaseRecordDetailResponse(
            record = caseRecord,
            staff = responsibleStaff,
            records = listOf(caseRecordDetail)
        ).copy(addedByStaff = null)
        assertThat(response).isEqualTo(caseRecordDetailResponse)
    }

    @Test
    fun `#CaseRecordDetailResponseConverter do not returns error if missing staff data`() {
        val response = CaseRecordDetailResponseConverter.convert(listOf(caseRecord), emptyMap())

        val caseRecordDetailResponse = TestTransportFactory.buildCaseRecordDetailResponse(
            record = caseRecord,
            staff = responsibleStaff,
            records = listOf(caseRecordDetail.copy(addedByStaff = null, responsibleStaff = null))
        ).copy(addedByStaff = null, responsibleStaff = null)

        assertThat(response).isEqualTo(caseRecordDetailResponse)
    }
}
