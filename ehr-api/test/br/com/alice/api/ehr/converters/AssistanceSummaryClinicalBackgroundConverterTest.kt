package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.AssistanceSummaryClinicalBackgroundResponse
import br.com.alice.api.ehr.controllers.model.AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt
import br.com.alice.api.ehr.controllers.model.ClinicalBackgroundCategoryItems
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.data.layer.models.UnstructuredBackground
import br.com.alice.data.layer.models.copy
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.util.*
import kotlin.test.Test

class AssistanceSummaryClinicalBackgroundConverterTest {

    private val medicineBackground = TestModelFactory.buildUnstructuredClinicalBackground(
        type = ClinicalBackgroundType.MEDICINE,
        content = mapOf("value" to "Cloridrato de Bupropiona 150mg")
    )
    private val habitBackground = TestModelFactory.buildUnstructuredClinicalBackground(
        type = ClinicalBackgroundType.HABIT,
        content = mapOf("value" to "Alcool: ate 8 doses (gin ou vodca) 2-4x/mes")
    )
    private val allergyBackground = TestModelFactory.buildUnstructuredClinicalBackground(
        type = ClinicalBackgroundType.ALLERGY,
        content = mapOf("value" to "Poeira")
    )

    @Test
    fun `#convert returns response with all lists successfully`() {
        val backgrounds = listOf(medicineBackground, habitBackground, allergyBackground)

        val response = AssistanceSummaryClinicalBackgroundResponse(
            medicines = listOf(medicineBackground.specialize<UnstructuredBackground>().value!!),
            habits = listOf(habitBackground.specialize<UnstructuredBackground>().value!!),
            allergies = listOf(allergyBackground.specialize<UnstructuredBackground>().value!!),
        )

        val result = AssistanceSummaryClinicalBackgroundConverter.convert(backgrounds)

        assertThat(result).isEqualTo(response)
    }

    @Test
    fun `#convert returns response containing only valid clinical background types successfully`() {
        val backgrounds = listOf(
            medicineBackground,
            habitBackground,
            TestModelFactory.buildUnstructuredClinicalBackground()
        )

        val response = AssistanceSummaryClinicalBackgroundResponse(
            medicines = listOf(medicineBackground.specialize<UnstructuredBackground>().value!!),
            habits = listOf(habitBackground.specialize<UnstructuredBackground>().value!!),
            allergies = emptyList(),
        )

        val result = AssistanceSummaryClinicalBackgroundConverter.convert(backgrounds)

        assertThat(result).isEqualTo(response)
    }

    @Test
    fun `#convertWithUpdateAt returns response with all lists successfully`() {
        val backgrounds = listOf(medicineBackground, habitBackground, allergyBackground)

        val response = AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt(
            medicines = ClinicalBackgroundCategoryItems(
                values = listOf(medicineBackground.specialize<UnstructuredBackground>().value!!),
                updatedAt = medicineBackground.updatedAt
            ),
            habits = ClinicalBackgroundCategoryItems(
                values = listOf(habitBackground.specialize<UnstructuredBackground>().value!!),
                updatedAt = habitBackground.updatedAt
            ),
            allergies = ClinicalBackgroundCategoryItems(
                values = listOf(allergyBackground.specialize<UnstructuredBackground>().value!!),
                updatedAt = allergyBackground.updatedAt
            ),
        )

        val result = AssistanceSummaryClinicalBackgroundConverter.convertWithUpdateAt(backgrounds)

        assertThat(result).isEqualTo(response)
    }

    @Test
    fun `#convertWithUpdateAt returns response containing only valid clinical background types successfully`() {
        val backgrounds = listOf(
            medicineBackground,
            habitBackground,
            TestModelFactory.buildUnstructuredClinicalBackground()
        )

        val response = AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt(
            medicines = ClinicalBackgroundCategoryItems(
                values = listOf(medicineBackground.specialize<UnstructuredBackground>().value!!),
                updatedAt = medicineBackground.updatedAt
            ),
            habits = ClinicalBackgroundCategoryItems(
                values = listOf(habitBackground.specialize<UnstructuredBackground>().value!!),
                updatedAt = habitBackground.updatedAt
            ),
            allergies = ClinicalBackgroundCategoryItems(
                values = emptyList(),
                updatedAt = null
            ),
        )

        val result = AssistanceSummaryClinicalBackgroundConverter.convertWithUpdateAt(backgrounds)

        assertThat(result).isEqualTo(response)
    }

    @Test
    fun `#convertWithUpdatedAt should return most recent updatedAt`() {
        val now = LocalDateTime.now()

        val backgrounds = listOf(
            medicineBackground.copy(
                id = UUID.randomUUID(),
                content = mapOf("value" to "Medicação Número 01"),
                updatedAt = now.minusDays(1)
            ),
            medicineBackground.copy(
                id = UUID.randomUUID(),
                content = mapOf("value" to "Medicação Número 02"),
                updatedAt = now.minusDays(2)
            ),
            medicineBackground.copy(
                id = UUID.randomUUID(),
                content = mapOf("value" to "Medicação Número 03"),
                updatedAt = now
            ),
        )

        val response = AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt(
            medicines = ClinicalBackgroundCategoryItems(
                values = backgrounds.map { it.specialize<UnstructuredBackground>().value!! },
                updatedAt = now
            ),
            habits = ClinicalBackgroundCategoryItems(
                values = emptyList(),
                updatedAt = null
            ),
            allergies = ClinicalBackgroundCategoryItems(
                values = emptyList(),
                updatedAt = null
            ),
        )

        val result = AssistanceSummaryClinicalBackgroundConverter.convertWithUpdateAt(backgrounds)

        assertThat(result).isEqualTo(response)
    }
}
