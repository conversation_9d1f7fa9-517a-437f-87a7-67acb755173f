package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.SurgeryAttendanceRequest
import br.com.alice.api.ehr.controllers.model.TertiaryIntentionEvolutionRequest
import br.com.alice.api.ehr.controllers.model.TertiarySpecialistData
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TertiaryIntentionDischargeSummaryEvaluation
import br.com.alice.data.layer.models.TertiaryIntentionEvolution
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationProcedure
import br.com.alice.data.layer.models.TertiaryIntentionIntensiveCare
import br.com.alice.data.layer.models.TertiaryIntentionSurgeryStatus
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class SurgeryAttendanceConverterTest {

    @Test
    fun `#SurgeryAttendanceResponseConverter should convert correctly`() {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val procedure = TestModelFactory.buildHealthcareResource()
        val person = TestModelFactory.buildPerson()
        val staff = TestModelFactory.buildStaff()
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialist = TestModelFactory.buildHealthProfessional(
            specialtyId = specialty.id,
            staffId = staff.id,
            staff = staff
        )
        val admissionDate = LocalDateTime.now().plusDays(1)
        val intensiveCare = listOf(TertiaryIntentionIntensiveCare(
            date = LocalDate.now(),
            requiresMechanicalVentilation = false
        ))

        val counterReferral = TestModelFactory.buildCounterReferral(
            healthCommunitySpecialistId = specialist.id
        )
        val guia = TestModelFactory.buildTotvsGuia()
        val surgeryIntention = TertiaryIntentionTouchPoint(
            personId = person.id,
            surgeonSpecialistId = staff.id,
            providerUnitId = providerUnit.id,
            hospitalizationProcedures = listOf(
                TertiaryIntentionHospitalizationProcedure(
                    procedureDate = LocalDate.now(),
                    procedureCode = procedure.code
                )
            ),
            staffId = staff.id,
            surgeryStatus = TertiaryIntentionSurgeryStatus.STAND_BY_GRACE,
            surgeryAdmissionDate = admissionDate,
            hospitalizationPostProgramming = "Programacao pos alta",
            evolutions = listOf(
                TertiaryIntentionEvolution(
                    createdAt = LocalDateTime.now(),
                    description = "Paciente em observacao",
                    staffId = staff.id
                )
            ),
            intensiveCare = intensiveCare,
            dischargeForecastInDays = 10,
            hasDischargeSummaryOnTime = true,
            dischargeSummaryEvaluation = TertiaryIntentionDischargeSummaryEvaluation.USEFUL_INFORMATION,
            counterReferrals = listOf(counterReferral.id),
            totvsGuias = listOf(guia.id)
        )

        val providerUnitPair = mapOf(providerUnit.id to providerUnit.name)
        val procedurePair = mapOf(procedure.code to procedure)
        val staffPair = mapOf(staff.id to staff.fullName)
        val specialistPair =
            mapOf(specialist.staffId to TertiarySpecialistData(specialist.id, staff.fullName, specialty.id))
        val specialtiesPair = mapOf(specialty.id to specialty)
        val counterReferrals = mapOf(counterReferral.id to counterReferral)
        val guias = mapOf(guia.id to guia)

        val result = SurgeryAttendanceResponseConverter.convert(
            surgeryIntention,
            providerUnitPair,
            procedurePair,
            staffPair,
            specialistPair,
            specialtiesPair,
            counterReferrals,
            guias,
        )

        assertThat(result.id).isEqualTo(surgeryIntention.id)
        assertThat(result.providerUnit?.name).isEqualTo(providerUnit.name)
        assertThat(result.staffName).isEqualTo(staff.fullName)
        assertThat(result.surgeon?.name).isEqualTo(staff.fullName)
        assertThat(result.hospitalizationProcedures.size).isEqualTo(1)
        assertThat(result.status).isEqualTo(surgeryIntention.surgeryStatus)
        assertThat(result.admissionDate).isEqualTo(admissionDate)
        assertThat(result.postProgramming).isEqualTo(surgeryIntention.hospitalizationPostProgramming)
        assertThat(result.evolutions.first().staffName).isEqualTo(staff.fullName)
        assertThat(result.evolutions.first().description)
            .isEqualTo(surgeryIntention.evolutions.first().description)
        assertThat(result.intensiveCare).isEqualTo(surgeryIntention.intensiveCare)
        assertThat(result.dischargeForecastInDays).isEqualTo(surgeryIntention.dischargeForecastInDays)
        assertThat(result.dischargeSummaryEvaluation).isEqualTo(surgeryIntention.dischargeSummaryEvaluation)

    }

    @Test
    fun `#SurgeryAttendanceRequestConverter should convert correctly`() {
        val personId = PersonId()
        val staffId = RangeUUID.generate()
        val admissionDate = LocalDateTime.now().plusDays(1)
        val startedAt = LocalDateTime.now().minusDays(2)
        val intensiveCare = listOf(TertiaryIntentionIntensiveCare(
            date = LocalDate.now(),
            requiresMechanicalVentilation = false
        ))
        val request = SurgeryAttendanceRequest(
            providerUnitId = RangeUUID.generate(),
            surgeonId = RangeUUID.generate(),
            status = TertiaryIntentionSurgeryStatus.STAND_BY_GRACE,
            admissionDate = admissionDate,
            postProgramming = "Programacao pos alta",
            startedAt = startedAt,
            newEvolutions = listOf(TertiaryIntentionEvolutionRequest(description = "Paciente em observacao")),
            intensiveCare = intensiveCare,
            dischargeForecastInDays = 10,
            hasDischargeSummaryOnTime = true,
            dischargeSummaryEvaluation = TertiaryIntentionDischargeSummaryEvaluation.USEFUL_INFORMATION,
        )

        val result = SurgeryAttendanceRequestConverter.convert(request, personId, staffId)

        assertThat(result.personId).isEqualTo(personId)
        assertThat(result.staffId).isEqualTo(staffId)
        assertThat(result.surgeonSpecialistId).isEqualTo(request.surgeonId)
        assertThat(result.surgeryStatus).isEqualTo(request.status)
        assertThat(result.providerUnitId).isEqualTo(request.providerUnitId)
        assertThat(result.surgeryAdmissionDate).isEqualTo(admissionDate)
        assertThat(result.startedAt).isEqualTo(startedAt)
        assertThat(result.hospitalizationPostProgramming).isEqualTo(request.postProgramming)
        assertThat(result.evolutions.first().description).isEqualTo(request.newEvolutions.first().description)
        assertThat(result.evolutions.first().staffId).isEqualTo(staffId)
        assertThat(result.intensiveCare).isEqualTo(request.intensiveCare)
        assertThat(result.dischargeForecastInDays).isEqualTo(request.dischargeForecastInDays)
        assertThat(result.dischargeSummaryEvaluation).isEqualTo(request.dischargeSummaryEvaluation)

    }

}
