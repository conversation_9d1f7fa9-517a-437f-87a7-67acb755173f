package br.com.alice.api.ehr.services.assistance_summary

import br.com.alice.api.ehr.controllers.model.TriageResultResponse
import br.com.alice.api.ehr.converters.TriageResultResponseConverter
import br.com.alice.api.ehr.services.assistance_summary.SearchTriageService.DalyaDistributionPath.Companion.fromString
import br.com.alice.api.ehr.services.assistance_summary.SearchTriageService.DalyaDistributionPath.WITHOUT_DALYA
import br.com.alice.api.ehr.services.internal.timeline.AttendantsTimelineInternalService
import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.data.layer.models.CaseRecordReferenceModel
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthlogic.client.TriageHistoryService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class SearchTriageService(
    private val channelService: ChannelService,
    private val caseRecordService: CaseRecordService,
    private val healthConditionService: HealthConditionService,
    private val timelineService: TimelineService,
    private val attendantsTimelineService: AttendantsTimelineInternalService,
    private val triageHistoryService: TriageHistoryService,
    private val trackPersonABService: TrackPersonABService
) {

    companion object {
        private const val INTERVAL_FOR_REACTIVATION_CHANNELS_IN_DAYS = 3L
    }

    suspend fun getTriage(channelId: String, currentStaff: Staff, personId: PersonId): Result<TriageResultResponse, Throwable> =
        channelService.get(channelId)
            .flatMap { channel ->
                if (channel.screeningNavigation == null && channel.demands.isEmpty()){
                    return@flatMap getTriageFromLastChannels(channel, currentStaff)
                }

                channel.screeningNavigation?.let { navigation ->
                    getTriageFromScreeningNavigation(navigation.id, personId)
                } ?: getTriageFromDemands(channel, currentStaff)
            }

    private suspend fun getTriageFromLastChannels(
        channel: ChannelResponse,
        currentStaff: Staff
    ): Result<TriageResultResponse, Throwable> =
        if (channel.isNotScreening()) NotFoundException("invalid_channel_id").failure()
        else {
            val createdAfter = LocalDateTime.now()
                .minusDays(INTERVAL_FOR_REACTIVATION_CHANNELS_IN_DAYS)
                .atBeginningOfTheDay()

            channelService.findChannels(
                personId = channel.channelPersonId,
                createdAfter = createdAfter
            ).flatMap { channels ->
                val caseIds = channels.map { channel -> channel.demands.map { it.caseId } }
                    .flatten()
                    .distinct()

                getTimelineItemsByCaseIds(caseIds = caseIds, demandName = null, currentStaff = currentStaff)
            }
        }

    private suspend fun getTriageFromDemands(
        channel: ChannelResponse,
        currentStaff: Staff
    ): Result<TriageResultResponse, Throwable> {
        val caseIds = channel.demands.map { demand -> demand.caseId }.distinct()
        val demandName = channel.demands.firstOrNull()?.description?.description

        return getTimelineItemsByCaseIds(caseIds, demandName, currentStaff)
    }

    private suspend fun getTimelineItemsByCaseIds(
        caseIds: List<UUID>,
        demandName: String?,
        currentStaff: Staff
    ): Result<TriageResultResponse, Throwable> =
        caseRecordService.getByCaseIds(caseIds)
            .map { records ->
                records.map { caseRecord ->
                    caseRecord.referencedLinks
                        .filter { referenceLink -> referenceLink.model == CaseRecordReferenceModel.APPOINTMENT }
                        .map { referenceLink -> referenceLink.id.toUUID() }
                }.flatten()
            }
            .flatMap { referenceIds ->
                getTimelineItems(referenceIds, currentStaff, demandName)
            }

    private suspend fun getTimelineItems(
        referenceIds: List<UUID>,
        currentStaff: Staff,
        demandName: String? = null
    ) =
        timelineService.findBy(
            TimelineFilter(
                referencedModelIds = referenceIds,
                referencedModelClasses = listOf(TimelineReferenceModel.APPOINTMENT)
            )
        ).map { timelineItems ->
                val attendants = attendantsTimelineService.getAttendantsTimeline(
                    timelineList = timelineItems,
                    appointmentDraft = emptyList()
                )

                TriageResultResponseConverter.fromReactivation(
                    timelineItems = timelineItems,
                    attendantsTimeline = attendants,
                    currentStaff = currentStaff,
                    demandName = demandName
                )
            }

    private suspend fun getTriageFromScreeningNavigation(navigationId: UUID, personId: PersonId): Result<TriageResultResponse, Throwable> =
        triageHistoryService.getByNavigationId(navigationId)
            .flatMapPair { triageHistory ->
                val codes = triageHistory.iaSuggestion
                    ?.suggestions
                    ?.map { it.code } ?: return@flatMapPair emptyMap<String, HealthCondition>().success()

                healthConditionService.findByCodesAndType(codes, HealthConditionCodeType.CIAP_2)
                    .map { healthConditions ->
                        healthConditions
                            .filter { it.code != null }
                            .associateBy { it.code!! }
                    }
            }
            .map { (healthConditions, triageHistory) ->
                TriageResultResponseConverter.fromTriageHistory(triageHistory, healthConditions, showDalyaResponse(personId)  == DalyaDistributionPath.WITH_DALYA)
            }

    private fun ChannelResponse.isNotScreening() =
        !(type == ChannelType.ASSISTANCE_CARE && subCategory == ChannelSubCategory.SCREENING)

    private suspend fun showDalyaResponse(personId: PersonId) =
        trackPersonABService.findOrStartAb(
            personId = personId,
            namespace = FeatureNamespace.HEALTH_LOGICS,
            key = "dalya_finish_triage_distribution",
            defaultPath = WITHOUT_DALYA.name
        ).fold(
            { fromString(it.abPath) },
            { WITHOUT_DALYA }
        )

    enum class DalyaDistributionPath {
        WITH_DALYA, WITHOUT_DALYA;

        companion object {
            fun fromString(value: String) = values()
                .firstOrNull { it.name.uppercase() == value.uppercase() }
                ?: WITHOUT_DALYA
        }
    }

}
