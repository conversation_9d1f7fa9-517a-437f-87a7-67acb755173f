package br.com.alice.api.ehr.services.hospitalization_attendance

import br.com.alice.api.ehr.controllers.model.HospitalizationAttendanceResponse
import br.com.alice.api.ehr.converters.HospitalizationAttendanceResponseConverter
import br.com.alice.api.ehr.services.procedure.ProcedureService
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class SearchHospitalizationAttendance(
    private val tertiaryIntentionService: TertiaryIntentionTouchPointService,
    private val staffService: StaffService,
    private val providerUnitService: ProviderUnitService,
    private val counterReferralService: CounterReferralService,
    private val totvsGuiaService: TotvsGuiaService,
    private val healthProfessionalService: HealthProfessionalService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val procedureService: ProcedureService
) {
    suspend fun fetchDataById(tertiaryIntentionId: UUID): HospitalizationAttendanceResponse = coroutineScope {
        val model = tertiaryIntentionService.get(tertiaryIntentionId).get()
        val counterReferralDeferred = async { getCounterReferralPair(model.counterReferrals) }
        val staffPairDeferred = async { getStaffPair(listOf(model)) }
        val providerUnitPairDeferred = async { getProviderUnitPair(listOf(model)) }
        val proceduresPairDeferred = async { procedureService.getProceduresPair(listOf(model)) }
        val guiasDeferred = async { getGuiasPair(model.totvsGuias) }
        val counterReferral = counterReferralDeferred.await()
        val healthCommunitiesMap = healthProfessionalService.getByStaffIds(
            counterReferral.values.map { it.staffId }.distinct()
        ).get()
        val specialties = healthCommunitiesMap.mapNotNull { it.specialtyId }
        val specialtiesMap = medicalSpecialtyService.getByIds(specialties).get().associateBy { it.id }

        HospitalizationAttendanceResponseConverter
            .convert(
                source = model,
                staffPair = staffPairDeferred.await(),
                providerUnitPair = providerUnitPairDeferred.await(),
                proceduresPair = proceduresPairDeferred.await(),
                guiasPair = guiasDeferred.await(),
                counterReferral = counterReferral,
                healthProfessionalsPair = healthCommunitiesMap.associateBy { it.staffId },
                specialtiesPair = specialtiesMap,
            )
    }

    private suspend fun getCounterReferralPair(counterReferrals: List<UUID>): Map<UUID, CounterReferral> =
        counterReferralService.getByIds(counterReferrals).get().associateBy { it.id }


    private suspend fun getGuiasPair(totvsGuias: List<UUID>): Map<UUID, TotvsGuia> =
        totvsGuiaService.findByIds(totvsGuias).get().associateBy { it.id }

    private suspend fun getStaffPair(modelList: List<TertiaryIntentionTouchPoint>): Map<UUID, String> {
        val staffIds = modelList.mapNotNull { it.staffId } +
                modelList.flatMap { it.evolutions.map { evolution -> evolution.staffId } }

        if (staffIds.isEmpty()) {
            return emptyMap()
        }

        val staffs = staffService.findByList(staffIds.distinct()).get()
        return staffs.associateBy({ it.id }, { it.fullName })
    }

    private suspend fun getProviderUnitPair(
        modelList: List<TertiaryIntentionTouchPoint>
    ): Map<UUID, String> {
        val provideUnitIds = modelList.mapNotNull { it.providerUnitId }.distinct()
        val providerUnits = providerUnitService.getByIds(provideUnitIds).get()

        return providerUnits.associateBy({ it.id }, { it.name })
    }
}
