package br.com.alice.api.ehr.services.person_health_info_summary

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.PrescriptionMedicineType
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskService
import com.github.kittinunf.result.map
import java.time.LocalDateTime

class SearchContinuousMedicationService(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val appointmentService: AppointmentService
) {

    suspend fun findByPersonId(personId: PersonId) =
        findPrescriptions(personId)
            .flatMapPair { prescriptions -> findAppointments(prescriptions) }
            .map { (appointments, prescriptions) ->
                val appointmentsMap = appointments.associateBy { it.id }

                prescriptions.map { prescription ->
                    ContinuousPrescriptionTransport(
                        prescription = prescription,
                        appointment = appointmentsMap[prescription.appointmentId]
                    )
                }
            }

    private suspend fun findAppointments(prescriptions: List<Prescription>) =
        prescriptions.mapNotNull { it.appointmentId }.let { ids ->
            appointmentService.getByIds(ids)
        }

    private suspend fun findPrescriptions(personId: PersonId) =
        healthPlanTaskService.findByPersonAndFilters(
            personId = personId,
            filterOptions = HealthPlanTaskFilters(
                types = listOf(HealthPlanTaskType.PRESCRIPTION),
                releasedAtGreater = LocalDateTime.now().minusMonths(VALID_INTERVAL_IN_MONTHS)
            )
        ).map { medications ->
            medications.map { it.specialize<Prescription>() }
                .filter {
                    val isContinuous = it.deadline?.unit == PeriodUnit.CONTINUOUS
                    val isSpecial = it.medicine?.type == PrescriptionMedicineType.SPECIAL

                    isContinuous || isSpecial
                }
                .sortedByDescending { it.releasedAt }
                .distinctBy { it.medicine?.name }
        }

    private companion object {
        const val VALID_INTERVAL_IN_MONTHS = 12L
    }
}

data class ContinuousPrescriptionTransport(
    val prescription: Prescription,
    val appointment: Appointment?
)
