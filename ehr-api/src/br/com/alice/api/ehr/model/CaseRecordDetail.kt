package br.com.alice.api.ehr.model

import br.com.alice.data.layer.models.CaseRecordCreatedByType
import br.com.alice.data.layer.models.CaseRecordReference
import br.com.alice.data.layer.models.CaseSeriousness
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.models.SimpleStaffResponse
import java.time.LocalDateTime
import java.util.UUID

data class CaseRecordDetail(
    val addedByStaff: SimpleStaffResponse? = null,
    val responsibleStaff: Staff? = null,
    val description: Disease,
    val cipes: List<Disease>? = emptyList(),
    val observation: String? = null,
    val severity: CaseSeverity,
    val seriousness: CaseSeriousness? = null,
    val status: CaseStatus,
    val addedAt: String,
    val id: UUID,
    val startedAt: String,
    val referencedLinks: List<CaseRecordReference>,
    val caseCreatedBy: CaseRecordCreatedByType,
    val outcome: String = ""
)

data class CaseRecordDetailResponse(
    val caseId: UUID,
    val addedByStaff: Staff? = null,
    val responsibleStaff: Staff? = null,
    val description: Disease,
    val cipes: List<Disease>? = emptyList(),
    val observation: String? = null,
    val severity: CaseSeverity,
    val seriousness: CaseSeriousness? = null,
    val status: CaseStatus,
    val startedAt: String,
    val records: List<CaseRecordDetail>,
    val referencedLinks: List<CaseRecordReference>
)

data class CaseRecordRequest(
    val responsibleStaffId: UUID,
    val description: Disease,
    val observation: String? = null,
    val severity: CaseSeverity,
    val seriousness: CaseSeriousness? = null,
    val status: CaseStatus,
    val startedAt: LocalDateTime? = null,
)

data class CaseRecordLightRequest(
    val severity: CaseSeverity,
    val description: Disease
)
data class CaseRecordLightEditRequest(
    val severity: CaseSeverity,
)

data class CaseRecordEditRequest(
    val responsibleStaffId: UUID,
    val description: Disease,
    val observation: String? = null,
    val severity: CaseSeverity,
    val seriousness: CaseSeriousness? = null,
    val status: CaseStatus,
    val startedAt: LocalDateTime,
    val referencedLinks: List<CaseRecordReference>? = null
)
