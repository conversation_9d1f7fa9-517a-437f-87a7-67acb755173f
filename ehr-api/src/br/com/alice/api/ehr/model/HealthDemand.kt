package br.com.alice.api.ehr.model

import br.com.alice.data.layer.models.CaseSeriousness
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.Staff
import java.util.UUID

data class HealthDemand(
    val caseId: UUID,
    val addedByStaff: Staff? = null,
    val responsibleStaff: Staff? = null,
    val description: Disease,
    val observation: String? = null,
    val severity: CaseSeverity,
    val seriousness: CaseSeriousness? = null,
    val status: CaseStatus,
    val addedAt: String,
    val startedAt: String,
    val channelId: String? = null,
    val personId: String,
    val records: List<CaseRecordDetail>,
    val healthConditionId: UUID
)
