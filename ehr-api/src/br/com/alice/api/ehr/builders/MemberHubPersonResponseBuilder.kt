package br.com.alice.api.ehr.builders

import br.com.alice.api.ehr.controllers.model.AgeGroup
import br.com.alice.api.ehr.model.MemberHubPersonResponse
import br.com.alice.api.ehr.model.PersonAddress
import br.com.alice.api.ehr.model.PersonCardType
import br.com.alice.data.layer.models.Address
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.core.FeatureService

object MemberHubPersonResponseBuilder {

    private val childrenThreshold
        get() =
            FeatureService.get(
                FeatureNamespace.MEMBERSHIP,
                "children_age_threshold",
                0
            )

    private val Person.isChildren get() = this.age < childrenThreshold
    private val Member.isCassi get() = this.cassiMember != null && this.cassiMember?.accountNumber != null
    private fun Brand?.toType() = when (this) {
        Brand.ALICE -> PersonCardType.ALICE
        Brand.DUQUESA -> PersonCardType.DUQUESA
        else -> PersonCardType.ALICE
    }

    private fun buildAddress(address: Address) = PersonAddress(
        fullAddress = address.toString(),
        shortAddress = address.street,
        latitude = address.lat.toString(),
        longitude = address.lng.toString()
    )

    fun buildPersonResponse(
        person: Person,
        member: Member,
        address: Address? = null,
    ) = MemberHubPersonResponse(
        fullName = person.fullRegisterName,
        shortName = person.nickName ?: person.firstName,
        socialName = person.fullSocialName,
        nationalId = person.nationalId,
        brand = member.brand,
        profilePicture = person.profilePicture?.url,
        ageGroup = if (person.isChildren) AgeGroup.CHILDREN else AgeGroup.ADULT,
        address = address?.let { buildAddress(it) }
    )
}
