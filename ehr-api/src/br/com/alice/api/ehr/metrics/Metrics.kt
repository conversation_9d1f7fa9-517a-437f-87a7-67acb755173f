package br.com.alice.api.ehr.metrics

import br.com.alice.common.Response
import br.com.alice.common.observability.metrics.Metric
import br.com.alice.common.redis.Result

object Metrics {

    const val CREATE_HEALTH_PLAN_TASK = "create_health_plan_task"
    const val UPDATE_HEALTH_PLAN_TASK = "update_health_plan_task"
    const val CREATE_APPOINTMENT = "create_appointment"
    const val UPDATE_APPOINTMENT = "update_appointment"
    const val UPDATE_ASSISTANCE_CARE = "update_assistance_care"

    const val CACHE_SPECIALTIES = "cache_specialties"
    const val CACHE_SPECIALISTS = "cache_specialists"
    const val CACHE_LOCATIONS = "cache_locations"

    fun registerMetrics() {
        listOf(
            CREATE_HEALTH_PLAN_TASK,
            UPDATE_HEALTH_PLAN_TASK,
            CREATE_APPOINTMENT,
            UPDATE_APPOINTMENT,
            UPDATE_ASSISTANCE_CARE,
            CACHE_SPECIALISTS,
            CACHE_SPECIALISTS,
            CACHE_LOCATIONS
        ).forEach { metric ->
            Metric.registerCounter(metric, "result" to Result.SUCCESS.toLabel())
            Metric.registerCounter(metric, "result" to Result.FAILURE.toLabel())
        }
    }

    fun incrementCreateHealthPlanTask(result: Result) =
        Metric.increment(CREATE_HEALTH_PLAN_TASK, "result" to result.toLabel())

    fun incrementUpdateHealthPlanTask(result: Result) =
        Metric.increment(UPDATE_HEALTH_PLAN_TASK, "result" to result.toLabel())

    fun incrementCreateAppointment(result: Result) =
        Metric.increment(CREATE_APPOINTMENT, "result" to result.toLabel())

    fun incrementUpdateAppointment(result: Result) =
        Metric.increment(UPDATE_APPOINTMENT, "result" to result.toLabel())

    fun incrementUpdateAssistanceCare(result: Result) =
        Metric.increment(UPDATE_ASSISTANCE_CARE, "result" to result.toLabel())

    fun incrementCacheSpecialties(result: Result) =
        Metric.increment(CACHE_SPECIALTIES, "result" to result.toLabel())

    fun incrementCacheSpecialists(result: Result) =
        Metric.increment(CACHE_SPECIALISTS, "result" to result.toLabel())

    fun incrementCacheLocations(result: Result) =
        Metric.increment(CACHE_LOCATIONS, "result" to result.toLabel())
}

@Suppress("TooGenericExceptionCaught")
suspend fun withMetric(
    metricName: String,
    combine: suspend () -> Response
): Response {
    try {
        return combine()
    } catch (e: Exception) {
        Metric.increment(metricName, "result" to Result.FAILURE.toLabel())
        throw e
    }
}
