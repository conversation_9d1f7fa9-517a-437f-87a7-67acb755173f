package br.com.alice.api.ehr.routes

import br.com.alice.api.ehr.controllers.AuthController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.authRoutes() {
    val authController by inject<AuthController>()

    post("signIn") { coHandler(authController::signIn) }
    route("/auth/pin") {
        post { coHandler(authController::sendEmailPin) }
        get { co<PERSON>andler(authController::verifyAccessPin) }
    }

    authenticate {
        post("/check_redirect") { coHandler(authController::checkRedirect) }
    }
}
