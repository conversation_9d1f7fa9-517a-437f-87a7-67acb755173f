package br.com.alice.api.ehr.converters.person_health_info_summary.content

import br.com.alice.api.ehr.model.SummaryContent
import br.com.alice.api.ehr.model.SummaryContentValue
import br.com.alice.api.ehr.model.SummaryContentValueAdditionalInfo
import br.com.alice.api.ehr.model.SummaryContentValueReferenceModelClassOption
import br.com.alice.api.ehr.services.person_health_info_summary.PersonHealthInfoSummaryTransport
import br.com.alice.api.ehr.services.person_health_info_summary.PrimaryAttentionAppointmentTransport
import br.com.alice.common.core.extensions.toBrazilianDateFormat

object LastPrimaryAttentionAppointmentContentBuilder : CommonContentBuilder() {

    override fun build(source: PersonHealthInfoSummaryTransport): SummaryContent =
        buildContent(
            label = "Última consulta com MFC/PED",
            values = source.lastPrimaryAttentionAppointment?.build()
        )

    private fun PrimaryAttentionAppointmentTransport.build() = listOf(
        SummaryContentValue(
            description = "${this.healthProfessional.name}, ${this.appointment.getDate().toBrazilianDateFormat()}",
            additionalInfo = SummaryContentValueAdditionalInfo(
                label = this.appointment.name.orEmpty(),
                date = this.appointment.getDate(),
                demands = this.appointment.toContentDemands(),
                referenceModelId = this.appointment.id.toString(),
                referenceModelClass = SummaryContentValueReferenceModelClassOption.APPOINTMENT
            )
        )
    )

}
