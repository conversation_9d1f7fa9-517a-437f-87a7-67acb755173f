package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.DischargeSummaryFileResponse
import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.data.layer.models.ProviderHealthDocument
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.SummaryReferenceType
import java.util.UUID

object DischargeSummaryFileResponseConverter {

    fun dischargeSummaryToResponse(dischargeSummary: DischargeSummary): DischargeSummaryFileResponse =
        DischargeSummaryFileResponse(
            id = dischargeSummary.id,
            type = SummaryReferenceType.DISCHARGE_SUMMARY,
            startedAt = dischargeSummary.dischargeItem.admittedAt,
            endedAt = dischargeSummary.dischargeItem.dischargedAt,
            providerName = dischargeSummary.provider.name
        )

    fun healthDocumentToResponse(
        healthDocument: ProviderHealthDocument,
        providerUnitMap: Map<UUID, ProviderUnit>
    ): DischargeSummaryFileResponse =
        DischargeSummaryFileResponse(
            id = healthDocument.id,
            type = SummaryReferenceType.PROVIDER_HEALTH_DOCUMENT,
            startedAt = healthDocument.createdAt,
            endedAt = healthDocument.createdAt,
            providerName = providerUnitMap[healthDocument.providerUnitId]?.name ?: "NONE"
        )
}
