package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.AssistanceSummaryClinicalBackgroundResponse
import br.com.alice.api.ehr.controllers.model.AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt
import br.com.alice.api.ehr.controllers.model.ClinicalBackgroundCategoryItems
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.data.layer.models.UnstructuredBackground


object AssistanceSummaryClinicalBackgroundConverter {
    fun convert(clinicalBackgrounds: List<ClinicalBackground>) =
        AssistanceSummaryClinicalBackgroundResponse(
            medicines = filterAndParseByType(
                clinicalBackgrounds = clinicalBackgrounds,
                type = ClinicalBackgroundType.MEDICINE
            ),
            habits = filterAndParseByType(
                clinicalBackgrounds = clinicalBackgrounds,
                type = ClinicalBackgroundType.HABIT
            ),
            allergies = filterAndParseByType(
                clinicalBackgrounds = clinicalBackgrounds,
                type = ClinicalBackgroundType.ALLERGY
            ),
        )

    fun convertWithUpdateAt(clinicalBackgrounds: List<ClinicalBackground>) =
        AssistanceSummaryClinicalBackgroundResponseWithUpdatedAt(
            medicines = filterAndGetLastUpdatedAt(clinicalBackgrounds, ClinicalBackgroundType.MEDICINE),
            habits = filterAndGetLastUpdatedAt(clinicalBackgrounds, ClinicalBackgroundType.HABIT),
            allergies = filterAndGetLastUpdatedAt(clinicalBackgrounds, ClinicalBackgroundType.ALLERGY),
        )

    private fun filterAndGetLastUpdatedAt(clinicalBackgrounds: List<ClinicalBackground>, type: ClinicalBackgroundType): ClinicalBackgroundCategoryItems {
        val (values, maxUpdatedAt) = clinicalBackgrounds
            .filter { background -> background.type == type }
            .run {
                mapNotNull { background -> background.specialize<UnstructuredBackground>().value } to maxByOrNull { it.updatedAt }?.updatedAt
            }

        return ClinicalBackgroundCategoryItems(
            values = values,
            updatedAt = maxUpdatedAt
        )
    }

    private fun filterAndParseByType(
        clinicalBackgrounds: List<ClinicalBackground>,
        type: ClinicalBackgroundType
    ) =
        clinicalBackgrounds.filter { background -> background.type == type }
            .mapNotNull { background ->
                background.specialize<UnstructuredBackground>().value
            }
}
