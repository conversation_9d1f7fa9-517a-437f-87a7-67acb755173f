package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.model.RefundCounterReferralResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.RefundCounterReferral

object RefundCounterReferralConverter : Converter<RefundCounterReferral, RefundCounterReferralResponse>(RefundCounterReferral::class, RefundCounterReferralResponse::class) {

    suspend fun convert(source: RefundCounterReferral, healthConditions: List<HealthCondition>, medicalSpecialty: MedicalSpecialty? ) =
        convert(
            source,
            map(RefundCounterReferralResponse::healthConditions) from healthConditions.map { it.toDisease() },
            map(RefundCounterReferralResponse::medicalSpecialty) from medicalSpecialty,
        )

}
