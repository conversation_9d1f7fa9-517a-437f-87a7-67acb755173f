package br.com.alice.api.ehr.converters.person_health_info_summary.content

import br.com.alice.api.ehr.model.SummaryContent
import br.com.alice.api.ehr.model.SummaryContentValue
import br.com.alice.api.ehr.model.SummaryContentValueAdditionalInfo
import br.com.alice.api.ehr.services.person_health_info_summary.PersonHealthInfoSummaryTransport
import br.com.alice.data.layer.models.ClinicalBackgroundType
import br.com.alice.data.layer.models.UnstructuredBackground

object AllergyContentBuilder : CommonContentBuilder() {

    override fun build(source: PersonHealthInfoSummaryTransport): SummaryContent =
        source.clinicalBackgrounds.filter { it.type == ClinicalBackgroundType.ALLERGY }
            .mapNotNull { allergy ->
                val description = allergy.specialize<UnstructuredBackground>().value ?: return@mapNotNull null

                SummaryContentValue(
                    description = description,
                    additionalInfo = SummaryContentValueAdditionalInfo(
                        label = description,
                        date = allergy.addedAt.toLocalDate(),
                        demands = emptyList()
                    )
                )
            }.let { buildContent(label = "Alergias", it) }

}
