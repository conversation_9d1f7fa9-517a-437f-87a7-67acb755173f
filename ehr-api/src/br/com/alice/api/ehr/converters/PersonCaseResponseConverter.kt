package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.ChannelCaseResponse
import br.com.alice.api.ehr.controllers.model.PersonCaseResponse
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.PersonCase

object PersonCaseResponseConverter {

    fun convert(source: PersonCase, channel: ChannelResponse? = null) : PersonCaseResponse =
        PersonCaseResponse(
            caseId = source.id,
            recordId = source.recordId,
            description = Disease(source.codeType, source.codeValue),
            severity = source.severity,
            seriousness = source.seriousness,
            channel = channel?.let { ChannelCaseResponse(channel.id, channel.name) },
            healthConditionId = source.healthConditionId
        )
}
