package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.TriageIaSuggestion
import br.com.alice.api.ehr.controllers.model.TriageIaSuggestionResult
import br.com.alice.api.ehr.controllers.model.TriageQuestion
import br.com.alice.api.ehr.controllers.model.TriageResultResponse
import br.com.alice.api.ehr.controllers.model.TriageResultType.BUD_TRIAGE
import br.com.alice.api.ehr.controllers.model.TriageResultType.FOLLOW_UP_FROM_DEMAND
import br.com.alice.api.ehr.controllers.model.TriageResultType.FOLLOW_UP_WITHOUT_DEMAND
import br.com.alice.api.ehr.model.timeline.ServicedByResponse
import br.com.alice.api.ehr.model.timeline.TimelineItemResponse
import br.com.alice.appointment.models.AttendantsTimeline
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.OptionType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.Timeline
import br.com.alice.healthlogic.models.triage.TriageAnswer
import br.com.alice.healthlogic.models.triage.TriageHistory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import br.com.alice.api.ehr.controllers.model.TriageAnswer as TriageAnswerResponse

object TriageResultResponseConverter {

    private val defaultTimeZonePattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}.\\d{3}".toRegex()
    private val defaultDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")

    fun fromTriageHistory(
        triageHistory: TriageHistory,
        healthConditions: Map<String, HealthCondition> = emptyMap(),
        showIaSuggestion: Boolean = false
    ): TriageResultResponse {
        val questions = triageHistory.answers
            .groupBy { it.questionId }
            .entries
            .map { (_, answers) ->
                TriageQuestion(
                    questionText = answers.firstOrNull()?.question.orEmpty(),
                    questionType = answers.firstOrNull()?.questionType,
                    selectedAnswers = answers.map {
                        TriageAnswerResponse(
                            id = it.answerId,
                            answerText = it.toAnswerText()
                        )
                    }
                )
            }

        return TriageResultResponse(
            type = BUD_TRIAGE,
            navigationStatus = triageHistory.navigationStatus,
            questions = questions,
            suggestedOutput = if (showIaSuggestion) triageHistory.suggestedOutput else null,
            iaSuggestion = triageHistory.iaSuggestion?.let { iaSuggestion ->
                TriageIaSuggestionResult(
                    type = iaSuggestion.type,
                    suggestions = iaSuggestion.suggestions.mapNotNull { suggestion ->
                        val healthCondition = healthConditions[suggestion.code] ?: return@mapNotNull null

                        TriageIaSuggestion(
                            code = suggestion.code,
                            description = suggestion.description,
                            probability = suggestion.probability,
                            healthConditionId = healthCondition.id
                        )
                    }
                )
            },
        )
    }

    fun fromReactivation(
        timelineItems: List<Timeline>,
        attendantsTimeline: AttendantsTimeline,
        currentStaff: Staff,
        demandName: String? = null
    ): TriageResultResponse {
        val parsedItems = timelineItems.map { timeline ->
            val servicedBy = attendantsTimeline.staffs[timeline.staffId]?.let { ServicedByResponse.buildByStaff(it) }
            TimelineItemResponse.from(
                timeline, servicedBy, attendantsTimeline, currentStaff
            )
        }
        val type = if (demandName == null) FOLLOW_UP_WITHOUT_DEMAND else FOLLOW_UP_FROM_DEMAND

        return TriageResultResponse(
            type = type,
            appointments = parsedItems,
            demandName = demandName
        )
    }

    private fun TriageAnswer.toAnswerText() =
        if (questionType == OptionType.CALENDAR || questionType == OptionType.CALENDAR_ALL_DATES) {
            val isDefaultFormat = answer.matches(defaultTimeZonePattern)
            val date = if (isDefaultFormat)
                LocalDateTime.parse(answer, defaultDateTimeFormatter)
            else
                LocalDateTime.parse(answer)

            date.toLocalDate().toBrazilianDateFormat()
        } else
            answer

}
