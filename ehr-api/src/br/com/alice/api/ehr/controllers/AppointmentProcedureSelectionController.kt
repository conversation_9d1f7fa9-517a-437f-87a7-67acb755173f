package br.com.alice.api.ehr.controllers

import br.com.alice.common.DefaultErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.exec.indicator.client.HealthSpecialistProcedureService
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.secondary.attention.models.ProcedureType
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.Parameters

class AppointmentProcedureSelectionController(
    private val healthSpecialistProcedureService: HealthSpecialistProcedureService,
    private val healthProfessionalService: HealthProfessionalService,
    staffService: StaffService,
) : Staff<PERSON>ontroller(staffService) {

    companion object {
        private const val NUMBER_OF_RESULTS_TO_RETURN = 10
    }

    suspend fun getProceduresForExecution(queryParams: Parameters, isRedirect: Boolean = false): Response {
        logger.info("HealthSpecialistResourceBundleController.search()", "query_params" to queryParams.entries())

        val searchTokens = queryParams["q"] ?: return DefaultErrorResponse(code = "missing_search_param")
        val limit = queryParams["limit"]?.toIntOrNull() ?: NUMBER_OF_RESULTS_TO_RETURN
        val typeOfService = queryParams["type_of_service"]?.let { ProcedureType.valueOf(it) }

        val staffId = currentStaffId()

        val onlyPriced = getOnlyPricedParam(queryParams) && typeOfService?.surgicalProcedure() == false

        return healthProfessionalService.findByStaffId(staffId).flatMap {
            val medicalSpecialtyId = it.specialtyId
            healthSpecialistProcedureService.getProceduresForExecution(
                medicalSpecialtyId = medicalSpecialtyId,
                query = searchTokens,
                shouldGetOnlyPriced = onlyPriced,
                limit = limit
            ).map {
                val allProcedures = it
                val suggestedProcedures = allProcedures.filter { it.hasPrice }
                val otherProcedures = allProcedures.filter { !it.hasPrice }
                // This parameter is used to use the return type of old version of the endpoint
                if (isRedirect) {
                    allProcedures
                } else {
                    AppointmentProcedureSelectionResponse(
                        allProcedures = allProcedures,
                        suggestedProcedures = suggestedProcedures,
                        otherProcedures = otherProcedures
                    )
                }
            }
        }.thenError {
            logger.error("HealthSpecialistResourceBundleController.search() - failed to load priced procedures", it)
        }.foldResponse()
    }

    suspend fun getProceduresForReferral(queryParams: Parameters): Response {
        val searchTokens = queryParams["q"] ?: return DefaultErrorResponse(code = "missing_search_param")
        val limit = queryParams["limit"]?.toIntOrNull() ?: NUMBER_OF_RESULTS_TO_RETURN

        return healthSpecialistProcedureService.getProceduresForReferral(
            query = searchTokens,
            limit = limit
        ).thenError {
            logger.error("HealthSpecialistResourceBundleController.search() - failed to load priced procedures", it)
        }.foldResponse()
    }

    private fun getOnlyPricedParam(queryParams: Parameters): Boolean =
        queryParams["only_priced"]?.toBoolean() != false

    data class AppointmentProcedureSelectionResponse(
        // The all procedures property is used just for compatibility with the frontend in the previous version,
        // when the frontend is updated, this property can be removed.
        val allProcedures: List<HealthSpecialistProcedure>,
        val suggestedProcedures: List<HealthSpecialistProcedure>,
        val otherProcedures: List<HealthSpecialistProcedure>
    )
}
