package br.com.alice.api.ehr.controllers

import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.ehr.client.HaocDocumentService
import com.github.kittinunf.result.map
import java.util.UUID

class DischargeSummaryController(
    private val haocDocumentService: HaocDocumentService
) {

    suspend fun getDischargeSummary(personId: PersonId, summaryId: UUID): Response {
        logger.info(
            "Getting discharge summary",
            "personId" to personId,
            "summaryId" to summaryId
        )

        return haocDocumentService.get(summaryId)
            .map { it.content }
            .foldResponse()
    }
}
