package br.com.alice.api.ehr.controllers

import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.foldResponse
import br.com.alice.ehr.client.ThirdPartyAppointmentOrigin
import br.com.alice.ehr.client.ThirdPartyAppointmentService
import br.com.alice.staff.client.StaffService
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.util.UUID

class ThirdPartyAppointmentController(
    staffService: StaffService,
    private val thirdPartyAppointmentService: ThirdPartyAppointmentService
) : StaffController(staffService) {

    suspend fun getAppointments(personId: PersonId): Response =
        thirdPartyAppointmentService
            .findByPersonId(personId)
            .foldResponse()

    suspend fun getAppointment(personId: PersonId, id: UUID, parameters: Parameters): Response {
        val type = parameters["origin"]?.let { getOrigin(it) }
            ?: return Response(HttpStatusCode.BadRequest, "missing origin")

        return thirdPartyAppointmentService
            .getByIdAndOrigin(personId, id, type)
            .foldResponse()
    }

    private fun getOrigin(origin: String) =
        try {
            ThirdPartyAppointmentOrigin.valueOf(origin)
        } catch (ex: Throwable) {
            throw InvalidArgumentException(code = "origin_invalid", message = "origin=$origin is invalid")
        }

}
