package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.converters.TestResultWithFeedbackConverter
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.AliceTestResultType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderName
import br.com.alice.data.layer.models.ReferenceRange
import br.com.alice.data.layer.models.TestResultFeedback
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.staff.client.StaffService
import br.com.alice.testresult.client.TestResultSummaryService
import br.com.alice.testresult.models.TestResultSummary
import br.com.alice.wanda.client.TestResultFeedbackService
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import br.com.alice.testresult.client.TestResultFileService as TestResultAttachmentService

class TestResultController(
    private val testResultSummaryService: TestResultSummaryService,
    private val testResultFeedbackService: TestResultFeedbackService,
    private val testResultAttachmentService: TestResultAttachmentService,
    staffService: StaffService,
) : StaffController(staffService) {

    suspend fun getByPersonId(personId: PersonId): Response {
        val results = testResultSummaryService.findByPersonId(personId)

        return if (!shouldViewTestResultFeedback()) results.mapEach { summary ->
            TestResultWithFeedbackConverter.convert(summary)
        } else {
            enrichWithFeedback(results.get())
        }.foldResponse()
    }

    suspend fun getImagesByPersonId(personId: PersonId): Response {
        val results = testResultSummaryService.findImagesByPersonId(personId)

        return if (!shouldViewTestResultFeedback()) results.mapEach { summary ->
            TestResultWithFeedbackConverter.convert(summary)
        } else {
            enrichWithFeedback(results.get())
        }.foldResponse()
    }

    suspend fun getImageById(
        personId: String,
        id: String,
        request: TestResultImageLinkRequest
    ): Response =
        testResultSummaryService.getImageLink(
            id,
            request.provider.toProviderIntegration(),
            request.resultType
        ).map { ImageLinkResponse(it) }.foldResponse()

    suspend fun sendFeedback(personId: PersonId, request: TestResultFeedbackRequest): Response =
        TestResultFeedback(
            aliceTestResultBundleIds = request.aliceTestResultBundleIds,
            healthPlanTaskIds = request.healthPlanTaskIds,
            content = request.content,
            personId = personId,
            staffId = currentStaffId()
        ).let { testResultFeedbackService.add(it) }.foldResponse()

    suspend fun findValidExamResultByAliceCode(personId: PersonId, queryParams: Parameters): Response {
        val examRequestDate = queryParams["examRequestDate"]?.toLocalDate() ?: LocalDate.now()
        val aliceCode = queryParams["aliceCode"] ?: return Response(HttpStatusCode.BadRequest)
        return testResultAttachmentService.findValidExamResultByAliceCode(
            personId,
            aliceCode,
            examRequestDate
        ).fold(
            { Response(HttpStatusCode.OK, it) },
            {
                if (it is NotFoundException) {
                    return Response(HttpStatusCode.NoContent)
                }

                throw it
            }
        )
    }

    private fun shouldViewTestResultFeedback() =
        FeatureService.get(
            namespace = FeatureNamespace.EHR,
            key = "can_view_test_result_feedback",
            defaultValue = false
        )

    private suspend fun enrichWithFeedback(results: List<TestResultSummary>) =
        coResultOf<List<TestResultSummaryResponse>, Throwable> {
            val ids = results.flatMap { it.items.map { i -> i.id.toSafeUUID() } }
            val feedbacks = if (ids.isNotEmpty()) {
                testResultFeedbackService.getByIds(ids).getOrNullIfNotFound() ?: emptyList()
            } else {
                emptyList()
            }

            results.map { summary ->
                TestResultWithFeedbackConverter.convert(summary, feedbacks)
            }
        }
}

data class ImageLinkResponse(
    val url: String
)

data class TestResultImageLinkRequest(
    val provider: ProviderName,
    val resultType: AliceTestResultType
)

data class TestResultFeedbackRequest(
    val content: String,
    val aliceTestResultBundleIds: List<UUID>,
    val healthPlanTaskIds: List<UUID>
)

data class TestResultSummaryResponse(
    val name: String,
    val items: List<TestResultSummaryItemResponse>
)

data class TestResultSummaryItemResponse(
    val id: String,
    val hasFeedback: Boolean = false,
    val name: String,
    val value: String,
    val unit: String? = null,
    val type: AliceTestResultType,
    val provider: ProviderName,
    val collectedAt: LocalDateTime,
    val reference: String? = null,
    val referenceRange: ReferenceRange
)
