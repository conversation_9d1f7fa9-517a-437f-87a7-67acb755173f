package br.com.alice.api.ehr.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.HealthFormAnswerGroup
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.questionnaire.client.HealthFormManagementService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.time.LocalDateTime
import java.util.UUID

class HealthFormController(
    private val healthFormManagementService: HealthFormManagementService
) : Controller() {

    private val invalidAnswers = listOf("Ningué<PERSON>", "Não")

    suspend fun getByHealthFormAnswerGroup(personId: PersonId, id: UUID): Response =
        span("getByHealthFormAnswerGroup") { span ->
            span.setAttribute("person_id", personId)
            span.setAttribute("group_id", id)

            healthFormManagementService.getHealthFormAnswerGroupById(id)
                .flatMap { group -> getAnswers(personId, group) }
                .map { answers -> getFormResponse(answers) }
                .recordResult(span)
                .foldResponse()
        }

    private suspend fun getFormResponse(answers: List<HealthFormQuestionAnswer>): FormResponse {
        val filteredAnswers = answers.filter { !invalidAnswers.contains(it.answer) }
        val formId = filteredAnswers.first().healthFormId

        val questions = getQuestions(formId)
        val sections = getSections(questions.getSectionIds())
        val form = getHealthForm(formId)

        return questions
            .groupBy { it.healthFormSectionId }
            .filter { entry -> sections[entry.key] != null }
            .mapNotNull { entry -> buildSection(sections.getValue(entry.key).title, entry.value, answers.sortedBy { it.createdAt }) }
            .let { formattedSections ->
                FormResponse(
                    id = form.id,
                    name = form.name,
                    createdAt = filteredAnswers.maxOf { it.createdAt },
                    sections = formattedSections
                )
            }
    }

    private suspend fun getAnswers(
        personId: PersonId,
        healthFormAnswerGroup: HealthFormAnswerGroup
    ) =
        healthFormManagementService.getFormAnswers(
            personId = personId,
            formId = healthFormAnswerGroup.healthFormId,
            healthFormAnswerGroupId = healthFormAnswerGroup.id
        )

    private suspend fun getHealthForm(formId: UUID) =
        healthFormManagementService
            .getFormById(formId)
            .get()

    private suspend fun getSections(sectionIds: List<UUID>) = span("getSections") { span ->
        span.setAttribute("section_ids", sectionIds.toString())

        healthFormManagementService
            .getSectionByIds(sectionIds)
            .recordResult(span)
            .get()
            .associateBy { it.id }
    }

    private suspend fun getQuestions(formId: UUID) = span("getQuestions") { span ->
        span.setAttribute("form_id", formId)

        healthFormManagementService
            .getAllQuestion(listOf(formId))
            .recordResult(span)
            .get()
            .sortedBy { it.index }
    }

    private fun buildSection(
        name: String?,
        questions: List<HealthFormQuestion>,
        answers: List<HealthFormQuestionAnswer>
    ): SectionResponse? =
        questions
            .mapNotNull { question -> question.buildResponse(answers) }
            .let { questionsResponse -> questionsResponse.ifEmpty { null } }
            ?.let { questionsResponse ->
                // Section with only one question and the type of question is OPTION_BUTTON_AND_TEXT
                //  should show only the question without the section name
                val shouldTransformSection = questionsResponse.shouldTransformSection()

                SectionResponse(
                    icon = "health/plan",
                    title = if (shouldTransformSection) questionsResponse.first().title else name,
                    questions = questionsResponse,
                    isText = shouldTransformSection
                )
            }

    private fun HealthFormQuestion.buildResponse(answers: List<HealthFormQuestionAnswer>): QuestionResponse? =
        answers
            .firstOrNull { it.healthFormQuestionId == id }
            ?.let { answer ->
                QuestionResponse(
                    question = question,
                    title = summaryQuestion,
                    type = type,
                    answers = answer.getAnswers().map { finalAnswer ->
                        val option = options.find { it.value.toString() == finalAnswer }?.label ?: finalAnswer
                        AnswerResponse(answer = option, date = answer.createdAt, answerValue = finalAnswer)
                    },
                    index = index,
                    paragraph = answer.getAnswerText()
                )
            }

    private fun List<HealthFormQuestion>.getSectionIds() =
        map { it.healthFormSectionId }.distinct()

    private fun List<QuestionResponse>.shouldTransformSection() =
        size == 1 && first().type == HealthFormQuestionType.OPTION_BUTTONS_AND_TEXT

}

data class FormResponse(
    val id: UUID,
    val name: String,
    val createdAt: LocalDateTime,
    val sections: List<SectionResponse>
)

data class SectionResponse(
    val icon: String? = null,
    val result: String? = null,
    val title: String? = null,
    val isText: Boolean = false,
    val questions: List<QuestionResponse>
)

data class QuestionResponse(
    val question: String,
    val title: String? = null,
    val type: HealthFormQuestionType,
    val answers: List<AnswerResponse>,
    val index: Int,
    val paragraph: String?
)

data class AnswerResponse(
    val date: LocalDateTime,
    val answer: String,
    val answerValue: String
)
