package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.controllers.model.HospitalizationAttendanceRequest
import br.com.alice.api.ehr.services.hospitalization_attendance.CreateHospitalizationAttendance
import br.com.alice.api.ehr.services.hospitalization_attendance.SearchHospitalizationAttendance
import br.com.alice.api.ehr.services.hospitalization_attendance.UpdateHospitalizationAttendance
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.staff.client.StaffService
import io.ktor.http.HttpStatusCode

class HospitalizationAttendanceController(
    staffService: StaffService,
    private val createHospitalizationAttendance : CreateHospitalizationAttendance,
    private val updateHospitalizationAttendance : UpdateHospitalizationAttendance,
    private val searchHospitalizationAttendance : SearchHospitalizationAttendance,
) : StaffController(staffService) {
    suspend fun create(personId: PersonId, request: HospitalizationAttendanceRequest): Response {
        val result = createHospitalizationAttendance.persist(
            personId,
            currentStaffId(),
            request
        )

        return Response(
            status = HttpStatusCode.Created,
            message = result.get()
        )
    }

    suspend fun update(
        personId: PersonId,
        tertiaryIntentionId: String,
        request: HospitalizationAttendanceRequest
    ): Response {
        return updateHospitalizationAttendance
            .persist(personId, tertiaryIntentionId.toUUID(), currentStaffId(), request)
            .foldResponse()
    }

    suspend fun find(personId: PersonId, tertiaryIntentionId: String): Response {
        return searchHospitalizationAttendance
            .fetchDataById(tertiaryIntentionId.toUUID())
            .toResponse()
    }
}
