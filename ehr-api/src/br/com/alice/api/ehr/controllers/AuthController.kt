package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ServiceConfig
import br.com.alice.api.ehr.services.AuthService
import br.com.alice.authentication.sendSignInEmailLink
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.toResponse
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.Staff
import br.com.alice.ehr.client.MailerService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode.Companion.BadRequest
import io.ktor.http.HttpStatusCode.Companion.NoContent
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.Unauthorized
import io.ktor.http.Parameters
import java.time.LocalDateTime

class AuthController(
    private val authService: AuthService,
    private val mailerService: MailerService,
    private val staffService: StaffService
) : Spannable, StaffController(staffService) {
    suspend fun signIn(signInRequest: SignInRequest): Response {
        if (!authService.signIn(signInRequest.idToken!!)) {
            return Response(Unauthorized)
        }

        return Response(OK)
    }

    suspend fun sendEmailPin(signInRequest: FirebaseSignInEmailPinRequest): Response =
        span("sendEmailPin") { span ->
            withUnauthenticatedTokenWithKey(EHR_API_ROOT_SERVICE_NAME) {
                span.setAttribute("email", signInRequest.email)
                staffService.findByEmail(signInRequest.email).mapPair { staff ->
                    signInRequest.redirectToUrls.find { it.system == staff.getSystem() }
                        ?: throw BadRequestException("System not found", "system_not_found")
                }.flatMap { (system, staff) ->
                    sendFirebasePin(staff, system)
                }.recordResult(span).foldResponse()
            }
        }

    suspend fun verifyAccessPin(params: Parameters): Response =
        withUnauthenticatedTokenWithKey(EHR_API_ROOT_SERVICE_NAME) {
            val pin = params["pin"]
            val email = params["email"]

            if (pin.isNullOrBlank() || email.isNullOrBlank()) Response(
                BadRequest,
                ErrorResponse("missing_parameters", "Email and pin are required")
            )
            else authService.getUrl(pin, email).map {
                UrlResponse(it)
            }.foldResponse()
        }

    suspend fun checkRedirect(request: ShouldRedirectRequest): Response =
        currentStaff().let { staff ->
            val system = request.redirectToUrls.first { it.system == staff.getSystem() }
            if (system.system == SystemTypeRequest.EHR) {
                Response(NoContent)
            } else {
                UrlResponse(sendSignInEmailLink(staff.email, system.url)).toResponse()
            }
        }


    private suspend fun sendFirebasePin(staff: Staff, system: SystemRequest): Result<String, Throwable> =
        span("sendFirebasePin") { span ->
            authService.generatedPinAuthentication(staff.email, system.url)
                .flatMap { code ->
                    val linkSendDate = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianDateTimeFormat()
                    val replaceVariables = mapOf(
                        "pin" to code,
                        "recipient" to staff.firstName,
                        "app_name" to system.system.name,
                        "email" to staff.email,
                        "send_date" to linkSendDate,
                    )

                    mailerService.sendEmail(
                        recipientName = staff.fullName,
                        recipientEmail = staff.email,
                        replaceVariables = replaceVariables,
                        templateName = ServiceConfig.EmailTemplate.firebaseSignInMagicNumberTemplate
                    )
                }.recordResult(span)
        }

    private fun Staff.getSystem(): SystemTypeRequest =
        when (this.type) {
            StaffType.PITAYA, StaffType.COMMUNITY_SPECIALIST, StaffType.PARTNER_HEALTH_PROFESSIONAL, StaffType.HEALTH_PROFESSIONAL -> SystemTypeRequest.EHR
            StaffType.HEALTH_ADMINISTRATIVE,StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL -> SystemTypeRequest.EITA
            else -> throw BadRequestException("Invalid staff type", "invalid_staff_type")
        }
}

data class SignInRequest(val idToken: String?)
data class UrlResponse(val link: String)
data class FirebaseSignInEmailLinkRequest(val email: String, val url: String)
data class FirebaseSignInEmailPinRequest(val email: String, val redirectToUrls: List<SystemRequest>)
data class ShouldRedirectRequest(val redirectToUrls: List<SystemRequest>)
data class SystemRequest(val system: SystemTypeRequest, val url: String)
enum class SystemTypeRequest { EHR, EITA }
