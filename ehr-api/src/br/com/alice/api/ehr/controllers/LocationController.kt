package br.com.alice.api.ehr.controllers

import br.com.alice.common.DefaultErrorResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.coverage.client.LocationService
import io.ktor.http.Parameters

class LocationController(
    private val locationService: LocationService
) : Controller() {
    suspend fun getStates() = locationService.getStates().foldResponse()

    suspend fun searchCities(stateId: String, parameters: Parameters) =
        parameters["q"]?.let { query ->
            locationService.searchCities(stateId, query).foldResponse()
        } ?: DefaultErrorResponse(code = "missing_query_parameter")
}
