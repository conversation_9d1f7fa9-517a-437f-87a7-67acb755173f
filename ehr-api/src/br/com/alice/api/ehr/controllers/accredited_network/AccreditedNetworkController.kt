package br.com.alice.api.ehr.controllers.accredited_network

import br.com.alice.api.ehr.controllers.model.AccreditedNetworkRequest
import br.com.alice.api.ehr.controllers.model.AgeGroup
import br.com.alice.api.ehr.controllers.model.FilterType
import br.com.alice.api.ehr.services.internal.accredited_network.AccreditedNetworkInternalService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.MissingParamsException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import io.ktor.http.Parameters

class AccreditedNetworkController(
    private val accreditedNetworkInternalService: AccreditedNetworkInternalService,
) : Controller() {
    suspend fun index(personId: String, queryParams: Parameters): Response =
        accreditedNetworkInternalService.getByPerson(
            personId = personId.toPersonId(),
            request = buildRequest(queryParams)
        ).foldResponse()

    suspend fun getProviderDetails(
        personId: String,
        providerType: String,
        id: String
    ): Response =
        accreditedNetworkInternalService.getProviderDetails(
            personId = personId.toPersonId(),
            providerType = ConsolidatedAccreditedNetworkType.valueOf(providerType),
            id = id.toUUID()
        ).foldResponse()

    suspend fun getSurgerySites(
        personId: String,
        queryParams: Parameters
    ): Response =
        accreditedNetworkInternalService.getSurgerySites(
            personId = personId.toPersonId(),
            queryParams["name"])
            .foldResponse()

    private fun buildRequest(queryParams: Parameters) =
        AccreditedNetworkRequest(
            lat = queryParams["lat"] ?: throw MissingParamsException("lat"),
            lng = queryParams["lng"] ?: throw MissingParamsException("lng"),
            filterType = queryParams["filterType"]?.let { FilterType.valueOf(it) }
                ?: throw MissingParamsException("filterType"),
            rangeInMeters = queryParams["rangeInMeters"]?.toInt()
                ?: throw MissingParamsException("rangeInMeters"),
            specialty = queryParams["specialty"]?.toUUID(),
            subSpecialty = queryParams["subSpecialty"]?.toUUID(),
            ageGroup = queryParams["ageGroup"]?.let { AgeGroup.valueOf(it) }
        )
}
