# EHR API

[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aehr-api&metric=coverage&token=ea2c54ed9469e7a9cf0dc15c05113c4897998b2e)](https://sonarcloud.io/summary/new_code?id=mono%3Aehr-api)

[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aehr-api&metric=code_smells&token=ea2c54ed9469e7a9cf0dc15c05113c4897998b2e)](https://sonarcloud.io/summary/new_code?id=mono%3Aehr-api)

[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aehr-api&metric=duplicated_lines_density&token=ea2c54ed9469e7a9cf0dc15c05113c4897998b2e)](https://sonarcloud.io/summary/new_code?id=mono%3Aehr-api)

[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aehr-api&metric=bugs&token=ea2c54ed9469e7a9cf0dc15c05113c4897998b2e)](https://sonarcloud.io/summary/new_code?id=mono%3Aehr-api)

Handle all HTTP calls from EHR frontend

### Responsible Team
Gestão de Saúde, find us on ``#eng-gestao-de-saude`` on Slack ;)

### Local development

Requirements
* [docker](https://www.docker.com) 
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``clean`` - delete build files
* ``tests`` - run all tests
* ``run`` - run project on 8090 port 

### Run locally
``make run`` and then you can access http://localhost:8090
