plugins {
    kotlin
    id("org.sonarqube")
}

group = "br.com.alice.alice-common-service-client"
version = aliceCommonVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:common-service-client")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

publishing {
    publications {
        create<MavenPublication>("nexus") {
            from(components["java"])
        }
    }

    repositories {
        maven {
            name = "nexus"
            url = uri("https://nexus.devtools.alice.tools/repository/releases/")
            credentials {
                username = System.getenv()["NEXUS_USER"] ?: ""
                password = System.getenv()["NEXUS_PASSWORD"] ?: ""
            }
        }
    }
}

dependencies {
    implementation(project(":common-core"))
    implementation(project(":common-logging"))
    testImplementation(project(":common"))
    testImplementation(project(":common-service"))

    ktor2Dependencies()

    test2Dependencies()
}
