package br.com.alice.common.service.data.dsl

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.TableId
import br.com.alice.common.core.extensions.classToString
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.core.extensions.toLocalTime
import br.com.alice.common.core.extensions.toSnakeCase
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.range
import br.com.alice.common.serialization.LocalDateAdapter
import br.com.alice.common.serialization.LocalDateTimeAdapter
import br.com.alice.common.serialization.LocalTimeAdapter
import com.google.gson.GsonBuilder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID


object SqlGenerator {
    private val gson = GsonBuilder()
        .registerTypeAdapter(LocalDateTime::class.java, LocalDateTimeAdapter())
        .registerTypeAdapter(LocalDate::class.java, LocalDateAdapter())
        .registerTypeAdapter(LocalTime::class.java, LocalTimeAdapter())
        .create()

    private val regexToClearTextForSearch = Regex("[\\(\\)\\|\\!]")
    private const val FIELD_VALUE_ALIAS = "?"

    fun findSql(table: String, query: Query) =
        "SELECT * FROM $table${where(query)}${orderByDistance(query)}${orderBy(query)}${nullsOrder(query)}${rankOrderForSearch(query)}${
            limit(
                query
            )
        }".trim()

    fun countSql(table: String, query: Query) =
        "SELECT ${countWithOrderBy(query)} FROM $table${where(query)}${groupBy(query)}${orderBy(query)}${orderByDistance(query)}${
            nullsOrder(
                query
            )
        }${rankOrderForSearch(query)}${
            limit(
                query
            )
        }".trim()

    fun existsSql(table: String, query: Query) =
        "SELECT 1 FROM $table${where(query)}${orderBy(query)}${orderByDistance(query)}${nullsOrder(query)}${rankOrderForSearch(query)} LIMIT 1".trim()

    private fun where(query: Query): String =
        query.where?.let { " WHERE ${predicate(it)}" } ?: ""


    fun fields(query: Query): List<Any> {
        val fields = mutableListOf<Any>()
        if (query.where != null) {
            fieldsValue(query.where, query, fields)
            fields.addAll(valeuRankOrderForSearch(query))
        }
        return fields
    }

    private fun fieldsValue(predicate: Predicate, query: Query, fields: MutableList<Any>) {
        when (predicate) {
            is Predicate.Eq<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.Diff<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.IEq<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.Greater<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.Less<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.GreaterEq<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.LessEq<*> -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.And -> {
                fieldsValue(predicate.first, query, fields)
                fieldsValue(predicate.second, query, fields)
            }
            is Predicate.Or -> {
                fieldsValue(predicate.first, query, fields)
                fieldsValue(predicate.second, query, fields)
            }
            is Predicate.StartsWith -> fields += "" + fieldValuePrepared(predicate.field, predicate.valueWrapper) + "%"

            is Predicate.Like -> fields += "%" + fieldValuePrepared(predicate.field, predicate.valueWrapper) + "%"

            is Predicate.WithinRange<*> -> withinRangeFieldValue(predicate, query, fields)

            is Predicate.LikeAny<*> -> {
                fields.addAll(predicate.list.map(SqlGenerator::unwrap).map { "%$it%" })
            }

            is Predicate.SimilarTo -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)

            is Predicate.Search -> fields += buildSearchQuery(predicate)
            is Predicate.RankedSearch -> fields += buildSearchQuery(predicate)
            is Predicate.JsonSearch -> fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
            is Predicate.InList<*> ->
                fields.addAll(predicate.list.map { valueWrapped ->
                    fieldValuePrepared(predicate.field, valueWrapped)
                })
            is Predicate.InValuesList<*> ->
                fields.addAll(predicate.list.map { valueWrapped ->
                    fieldValuePrepared(predicate.field, valueWrapped)
                })
            is Predicate.NotInList<*> ->
                fields.addAll(predicate.list.map { valueWrapped ->
                    fieldValuePrepared(predicate.field, valueWrapped)
                })
            is Predicate.ContainsAll<*> -> fields.add(
                predicate.list.map(SqlGenerator::unwrap).map(SqlGenerator::normalizeJson)
                    .joinToString(prefix = "[", postfix = "]")
            )
            is Predicate.Contains<*> -> fields += normalizeJson(
                fieldValuePrepared(
                    predicate.field,
                    predicate.valueWrapper
                )
            )
            is Predicate.ContainsAny<*> -> fields.addAll(
                predicate.list.map(SqlGenerator::unwrap).map(SqlGenerator::normalizeJson)
            )
            is Predicate.Not -> fieldsValue(predicate.innerPredicate, query, fields)
            is Predicate.Scope -> fieldsValue(predicate.innerPredicate, query, fields)
            is Predicate.IsNull, is Predicate.IsNotNull, Predicate.False, is Predicate.IsNotEmptyList, is Predicate.IsEmptyList -> {
            } // do nothing
        }
    }

    private fun withinRangeFieldValue(predicate: Predicate.WithinRange<*>, query: Query, fields: MutableList<Any>) {
        fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
        if (query.orderByDistance != null) fields += fieldValuePrepared(predicate.field, predicate.valueWrapper)
    }

    private fun normalizeJson(value: Any): Any =
        when (value) {
            is UUID -> value.toString()
            else -> value
        }

    private fun unwrap(valueWrapper: ValueWrapper<out Any>): Any = valueWrapper.value


    private fun predicate(predicate: Predicate): String =
        when (predicate) {
            is Predicate.Eq<*> -> "${fieldName(predicate.field)} = $FIELD_VALUE_ALIAS"
            is Predicate.Diff<*> -> "${fieldName(predicate.field)} <> $FIELD_VALUE_ALIAS"
            is Predicate.IEq<*> -> "${fieldName(predicate.field)} ILIKE $FIELD_VALUE_ALIAS"
            is Predicate.Greater<*> -> "${fieldName(predicate.field)} > $FIELD_VALUE_ALIAS"
            is Predicate.Less<*> -> "${fieldName(predicate.field)} < $FIELD_VALUE_ALIAS"
            is Predicate.GreaterEq<*> -> "${fieldName(predicate.field)} >= $FIELD_VALUE_ALIAS"
            is Predicate.LessEq<*> -> "${fieldName(predicate.field)} <= $FIELD_VALUE_ALIAS"
            is Predicate.And -> "${predicate(predicate.first)} AND ${predicate(predicate.second)}"
            is Predicate.Or -> "${predicate(predicate.first)} OR ${predicate(predicate.second)}"
            is Predicate.Not -> "NOT (${predicate(predicate.innerPredicate)})"
            is Predicate.Scope -> "(${predicate(predicate.innerPredicate)})"
            is Predicate.StartsWith -> "${fieldName(predicate.field)} LIKE $FIELD_VALUE_ALIAS"
            is Predicate.Like -> {
                if (predicate.field is Field.JsonbField)
                    "${fieldName(predicate.field)}::text ILIKE $FIELD_VALUE_ALIAS"
                else
                    "${fieldName(predicate.field)} ILIKE $FIELD_VALUE_ALIAS"
            }
            is Predicate.LikeAny<*> -> {
                if (predicate.field is Field.JsonbField)
                    "${fieldName(predicate.field)}::text ILIKE ANY (ARRAY[${
                        fieldListAlias(predicate.list)
                    }])"
                else
                    "${fieldName(predicate.field)} ILIKE ANY (ARRAY[${
                        fieldListAlias(predicate.list)
                    }])"
            }
            is Predicate.SimilarTo -> "${fieldName(predicate.field)} % $FIELD_VALUE_ALIAS"
            is Predicate.Search ->
                "${fieldName(predicate.field)} @@ to_tsquery('portuguese', $FIELD_VALUE_ALIAS)"
            is Predicate.RankedSearch ->
                "${fieldName(predicate.field)} @@ to_tsquery('portuguese', $FIELD_VALUE_ALIAS)"
            is Predicate.JsonSearch -> "${fieldName(predicate.field)} @> $FIELD_VALUE_ALIAS::jsonb"
            is Predicate.InList<*> -> {
                if (predicate.field is Field.JsonbField)
                    "${fieldName(predicate.field)} @> ANY (ARRAY [${fieldListAlias(predicate.list)}]::jsonb[])"
                else
                    "${fieldName(predicate.field)} IN (${fieldListAlias(predicate.list)})"
            }
            is Predicate.InValuesList<*> -> {
                if (predicate.field is Field.JsonbField)
                    "${fieldName(predicate.field)} @> ANY (ARRAY [${fieldListAlias(predicate.list)}]::jsonb[])"
                else
                    "${fieldName(predicate.field)} IN (VALUES ${fieldValuesListAlias(predicate.list)})"
            }
            is Predicate.NotInList<*> -> {
                if (predicate.field is Field.JsonbField)
                    "NOT ${fieldName(predicate.field)} @> ANY (ARRAY [${fieldListAlias(predicate.list)}]::jsonb[])"
                else
                    "${fieldName(predicate.field)} NOT IN (${fieldListAlias(predicate.list)})"
            }
            is Predicate.Contains<*> -> "${fieldName(predicate.field)} ?? $FIELD_VALUE_ALIAS"
            is Predicate.ContainsAny<*> -> "${fieldName(predicate.field)} ??| ARRAY[${
                fieldListAlias(predicate.list)
            }]"
            is Predicate.ContainsAll<*> -> "${fieldName(predicate.field)} @> $FIELD_VALUE_ALIAS::jsonb"
            is Predicate.IsNull -> "${fieldName(predicate.field)} IS NULL"
            is Predicate.IsNotNull -> "${fieldName(predicate.field)} IS NOT NULL"
            is Predicate.False -> "FALSE"
            is Predicate.IsNotEmptyList -> "${fieldName(predicate.field)} != '[]'::jsonb"
            is Predicate.IsEmptyList -> "${fieldName(predicate.field)} = '[]'::jsonb"
            is Predicate.WithinRange<*> -> {
                if (predicate.field is Field.GeographyField)
                    "ST_DWithin(${fieldName(predicate.field)}, $FIELD_VALUE_ALIAS::geography, ${predicate.range})"
                else throw IllegalArgumentException("Predicate WithinRange only works for GeographyField. Current field: ${predicate.field}")
            }
        }


    private fun fieldName(field: Field<*>) = field.name.toSnakeCase()
    private fun fieldName(name: String) = name.toSnakeCase()

    private fun fieldValuePrepared(field: Field<*>, valueWrapper: ValueWrapper<*>): Any =
        when (field) {
            is Field.TextField -> valueWrapper.value!!.toString()
            is Field.UUIDField -> valueWrapper.value!!.toString().toUUID().let {
                require(it.range() != RangeUUID.PERSON_ID_RANGE) {
                    "id.range must not to be in PERSON_ID_RANGE"
                }
                it
            }

            is Field.JsonbField -> valueWrapper.value!!

            is Field.GeographyField -> valueWrapper.value!!

            is Field.TableIdField ->
                if (valueWrapper.value is TableId) valueWrapper.value.id
                else throw IllegalArgumentException("Value should be TableId and not ${valueWrapper.value.classToString()}")
            is Field.IntegerField ->
                if (valueWrapper.value is Double) valueWrapper.value.toInt()
                else valueWrapper.value!!
            is Field.LongField ->
                if (valueWrapper.value is Double) valueWrapper.value.toLong()
                else valueWrapper.value!!
            is Field.BooleanField ->
                valueWrapper.value!!
            is Field.BigDecimalField ->
                valueWrapper.value!!
            is Field.DateTimeField ->
                when (valueWrapper.value) {
                    is Map<*, *> -> toTimestamp(valueWrapper.value)
                    is LocalDateTime -> valueWrapper.value
                    is String -> valueWrapper.value.toLocalDateTime()
                    else -> throw IllegalArgumentException("Value should be a Map<*, *> or String and not ${valueWrapper.value.classToString()}")
                }
            is Field.DateField ->
                when (valueWrapper.value) {
                    is Map<*, *> -> toDate(valueWrapper.value)
                    is LocalDateTime -> valueWrapper.value
                    is String -> valueWrapper.value.toLocalDate()
                    else -> throw IllegalArgumentException("Value should be a Map<*, *> or String and not ${valueWrapper.value.classToString()}")
                }
            is Field.TimeField ->
                when (valueWrapper.value) {
                    is Map<*, *> -> toTime(valueWrapper.value)
                    is LocalDateTime -> valueWrapper.value
                    is String -> valueWrapper.value.toLocalTime()
                    else -> throw IllegalArgumentException("Value should be a Map<*, *> or String and not ${valueWrapper.value.classToString()}")
                }
        }

    private fun fieldListAlias(list: List<ValueWrapper<out Any>>) =
        list.joinToString(", ") { "?" }

    private fun fieldValuesListAlias(list: List<ValueWrapper<out Any>>) =
        list.joinToString(", ") { "(?)" }
    private fun toTimestamp(map: Map<*, *>): LocalDateTime =
        gson.fromJson(map.toString(), LocalDateTime::class.java)

    private fun toDate(map: Map<*, *>): LocalDate =
        gson.fromJson(map.toString(), LocalDate::class.java)

    private fun toTime(map: Map<*, *>): LocalTime =
        gson.fromJson(map.toString(), LocalTime::class.java)

    private fun orderBy(query: Query): String = query.orderBy?.let {
        when {
            query.orderByDistance != null -> ", ${
                it.mapIndexed { index, s -> "${fieldName(s)}${sortOrder(query.sortOrder?.getOrNull(index))}" }
                    .joinToString(", ")
            }"
            else -> " ORDER BY ${
                it.mapIndexed { index, s -> "${fieldName(s)}${sortOrder(query.sortOrder?.getOrNull(index))}" }
                    .joinToString(", ")
            }"
        }
    } ?: ""

    private fun orderByDistance(query: Query): String = query.orderByDistance?.let { field ->
        when {
            query.orderBy != null && query.sortOrderDistance != null -> {
                " ORDER BY st_distance(${fieldName(field)}, $FIELD_VALUE_ALIAS::geography)${sortOrder(query.sortOrderDistance)}"
            }
            else -> {
                " ORDER BY st_distance(${fieldName(field)}, $FIELD_VALUE_ALIAS::geography)${sortOrder(query.sortOrder?.first())}"
            }
        }
    } ?: ""

    private fun countWithOrderBy(query: Query): String =
        query.groupBy?.let { " count(*),${it.joinToString(",") { str -> fieldName(str) }}" } ?: "count(*)"

    private fun groupBy(query: Query): String =
        query.groupBy?.let { " GROUP BY ${it.joinToString(",") { str -> fieldName(str) }}" } ?: ""

    private fun nullsOrder(query: Query): String = when (query.nullsOrder) {
        null -> ""
        NullsOrder.First -> " NULLS FIRST"
        NullsOrder.Last -> " NULLS LAST"
    }

    private fun sortOrder(sortOrder: SortOrder?): String = when (sortOrder) {
        null -> ""
        SortOrder.Ascending -> " ASC"
        SortOrder.Descending -> " DESC"
    }

    private fun rankOrderForSearch(query: Query): String =
        if (query.where != null && query.where is Predicate.RankedSearch && query.orderBy == null) {
            " ORDER BY ts_rank_cd(${fieldName(query.where.field)}, to_tsquery('portuguese', ?), 1) DESC"
        } else ""


    private fun valeuRankOrderForSearch(query: Query): List<String> =
        if (query.where is Predicate.RankedSearch) {
            listOf(buildSearchQuery(query.where))
        } else {
            listOf()
        }


    private fun limit(query: Query): String = query.limit?.let { " LIMIT $it${offset(query)}" } ?: ""
    private fun offset(query: Query): String = query.offset?.let { " OFFSET $it" } ?: ""

    private fun buildSearchQuery(predicate: Predicate): String {
        val value = when (predicate) {
            is Predicate.Search -> predicate.valueWrapper.value
            is Predicate.RankedSearch -> predicate.valueWrapper.value
            else -> ""
        }

        return regexToClearTextForSearch.replace(value, "")
            .unaccent()
            .split(' ')
            .filter { it.isNotBlank() }
            .joinToString(separator = "&", transform = { "${it}:*" })
    }

}
