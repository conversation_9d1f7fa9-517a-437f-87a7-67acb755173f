package br.com.alice.common.service.data.dsl.adapters

import br.com.alice.common.service.data.dsl.ValueWrapper
import br.com.alice.common.service.data.dsl.firstLowerClass
import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.TypeAdapterFactory
import com.google.gson.reflect.TypeToken

class ValueTypeAdapterFactory : TypeAdapterFactory {

    private val typeAdapterFactory: RuntimeTypeAdapterFactory<ValueWrapper<*>> = RuntimeTypeAdapterFactory.of(
        ValueWrapper::class.java, "type", false
    )

    init {
        ValueWrapper::class.sealedSubclasses.forEach { klass ->
            typeAdapterFactory.registerSubtype(klass.java, klass.simpleName?.firstLowerClass())
        }
    }

    override fun <T : Any?> create(gson: Gson, type: TypeToken<T>): TypeAdapter<T> {
        return typeAdapterFactory.create(gson, type)
    }
}
