package br.com.alice.common.service.data.client

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBodyBuilder
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.rfc.Ignore
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success

interface Finder<F, O, E : Any> : FinderByQuery<E> {

    fun queryBuilder(): QueryBuilder<F, O>

    override suspend fun findByQuery(query: Query): Result<List<E>, Throwable>

    @Ignore
    suspend fun findOneOrNull(body: QueryBodyBuilder<F, O>): E? =
        findByQuery(body(queryBuilder()).limit { 1 }.build()).get().firstOrNull()

    @Ignore
    suspend fun findOne(body: QueryBodyBuilder<F, O>): Result<E, Throwable> =
        findByQuery(body(queryBuilder()).limit { 1 }.build()).mapFirst()

    @Ignore
    suspend fun find(body: QueryBodyBuilder<F, O>): Result<List<E>, Throwable> =
        findByQuery(body(queryBuilder()).build())
}

@Suppress("UNCHECKED_CAST")
private fun <V : Any, E : Throwable> Result<List<V>, E>.mapFirst(): Result<V, E> = fold(
    { it.firstOrNull()?.success() ?: NotFoundException("List of entities is empty").failure() as Result<V, E> },
    { it.failure() }
)
