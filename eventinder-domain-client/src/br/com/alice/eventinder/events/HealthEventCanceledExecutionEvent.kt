package br.com.alice.eventinder.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.HealthEvents

class HealthEventCanceledExecutionEvent(payload: HealthEvents) :
    NotificationEvent<HealthEvents>(
        name = name,
        producer = "eventinder-domain-service",
        payload = payload
    ) {
    companion object {
        const val name = "eventinder-health-event-canceled-execution"
    }
}
