package br.com.alice.limbo.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.limbo.models.DeadletterQueueTransport
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface DeadletterQueueTransportService : Service {

    override val namespace get() = "limbo"
    override val serviceName get() = "deadletter_queue_transport"

    suspend fun findByFilters(producer: String, topic: String, range: IntRange): Result<List<DeadletterQueueTransport>, Throwable>

    suspend fun getById(id: UUID): Result<DeadletterQueueTransport, Throwable>

    suspend fun countByFilter(producer: String, topic: String): Result<Int, Throwable>

    suspend fun reprocess(id: UUID): Result<Boolean, Throwable>

    suspend fun drop(eventId: UUID): Result<<PERSON><PERSON><PERSON>, Throwable>

}
