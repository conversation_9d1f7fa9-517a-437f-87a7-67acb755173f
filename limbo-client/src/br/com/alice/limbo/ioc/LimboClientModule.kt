package br.com.alice.limbo.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.limbo.LimboConfiguration
import br.com.alice.limbo.SERVICE_NAME
import br.com.alice.limbo.client.DeadletterQueueTransportService
import br.com.alice.limbo.client.DeadletterQueueTransportServiceClient
import org.koin.core.qualifier.named
import org.koin.dsl.module

val LimboClientModule = module {

    val baseUrl = LimboConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 10_000), "$baseUrl/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<DeadletterQueueTransportService> { DeadletterQueueTransportServiceClient(invoker) }
}
