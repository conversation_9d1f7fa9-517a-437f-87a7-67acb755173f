package br.com.alice.amas.models

import br.com.alice.data.layer.models.ExpenseTypeEnum
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.data.layer.models.InvoiceType
import br.com.alice.data.layer.models.TissInvoiceStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class CreateInvoiceResponse(val id: UUID)

data class InvoiceResponse(
    val id: UUID,
    val code: String,
    val authorizerId: UUID? = null,
    val providerUnitId: UUID? = null,
    val status: TissInvoiceStatus,
    val provider: InvoiceProviderResponse?,
    val createdAt: LocalDateTime,
    val valueTotal: BigDecimal = BigDecimal.ZERO,
    val guiaTotal: Number = 0,
    val batches: List<TissBatchResponse>? = null,
    val type: InvoiceType?,
    val staff: InvoiceStaffResponse? = null,
    val totalValueWithBonus: BigDecimal = BigDecimal.ZERO,
    val expenseType: InvoiceExpenseType? = null
)

data class InvoiceProviderResponse(
    val name: String? = "",
    val cnpj: String? = "",
    val daysForPayment: Int? = null
)

data class InvoiceDetailsResponse(
    val code: String,
    val provider: InvoiceProviderResponse?,
)

data class InvoiceStaffResponse(
    val id: UUID? = null,
    val name: String? = null
)

data class AssociationNf(
    val id: UUID,
    val expenseType: ExpenseTypeEnum
)
