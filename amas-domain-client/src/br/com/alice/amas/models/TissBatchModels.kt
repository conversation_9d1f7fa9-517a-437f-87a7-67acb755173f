package br.com.alice.amas.models

import br.com.alice.data.layer.models.TissBatchStatus
import java.math.BigDecimal
import java.util.UUID

data class TissBatchResponse(
    val id: UUID,
    val file: FileVaultResponse? = null,
    val status: TissBatchStatus,
    val guiaIndexed: Int = 0,
    val guiaQuantity: Int = 0,
    val totalValue: BigDecimal = BigDecimal.ZERO,
    val errors: MutableList<TissBatchErrorResponse> = mutableListOf()
)

data class TissBatchErrorResponse(
    val description: String,
    val lineNumber: Int? = null,
    val guiaNumber: String? = null,
    val procedure: String? = null,
    val procedureCode: String? = null
)

data class TissBatchDetailsResponse(
    val id: UUID,
    val fileName: String,
    val status: TissBatchStatus,
    val guias: List<TissGuiaResponse>? = emptyList()
)
