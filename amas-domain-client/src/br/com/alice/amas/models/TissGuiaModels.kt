package br.com.alice.amas.models

import br.com.alice.amas.responses.TissGuiaExpenseResponse
import br.com.alice.amas.responses.TissGuiaProcedureResponse
import java.util.UUID

data class TissGuiaResponse(
    val id: UUID,
    val number: String,
    val member: TissGuiaMemberResponse,
    val attendanceType: String?,
    val procedures: List<TissGuiaProcedureResponse>? = null,
    val expenses: List<TissGuiaExpenseResponse>? = null,
    val providerRequester: TissGuiaProviderRequesterResponse
)

data class TissGuiaMemberResponse(
    val id: UUID?,
    val name: String? = null,
    val nationalId: String? = null
)

data class TissGuiaNumberResponse(val id: UUID, val number: String)

data class TissGuiaCritiqueResponse(
    val guiaId: UUID,
    val critique: String,
    val procedure: String,
    val code: String,
    val reviewer: String? = null
)

data class TissGuiaProviderRequesterResponse(
    val cnpj: String? = null,
    val code: String? = null,
    val name: String? = null,
)
