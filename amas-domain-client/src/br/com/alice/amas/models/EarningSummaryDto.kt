package br.com.alice.amas.models

import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Council
import java.math.BigDecimal
import java.time.LocalDate

data class EarningSummaryDto(
    val counterReferralId: String? = null,
    val counterReferralDate: LocalDate? = LocalDate.now(),
    val appointmentDate: LocalDate = LocalDate.now(),
    val memberInternalCode: String? = null,
    val healthSpecialistCnpj: String = "",
    val council: Council = Council("", State.SP),
    val healthSpecialistName: String = "",
    val healthSpecialistCode: String = "",
    val healthSpecialistSpecialtyCode: String = "",
    val healthSpecialistSpecialty: String = "",
    val tier: SpecialistTier = SpecialistTier.TALENTED,
    val codPrestador: String = "",
    val providerName: String = "",
    val providerCnpj: String = "",
    val procedureCode: String = "",
    val procedureName: String = "",
    val procedureDescription: String = "",
    val procedureTable: String = "",
    val value: BigDecimal = BigDecimal.ZERO,
    val quantity: Int = 0,
    val period: String = "",
    val healthSpecialistEmail: String = "",
    val providerUnitId: String? = "",
    val aliceCnpj: String = "",
    val line: Int = 0,
)
