package br.com.alice.amas.events

import br.com.alice.common.notification.NotificationEvent
import java.util.UUID

class PreviewEarningSummaryUploadedEvent(previewEarningSummaryUploadedEventPayload: PreviewEarningSummaryUploadedEventPayload) :
    NotificationEvent<PreviewEarningSummaryUploadedEventPayload>(
        name = name,
        producer = "amas-domain-service",
        payload = previewEarningSummaryUploadedEventPayload
    ) {
    companion object {
        const val name = "amas-preview-earning-summary-uploaded"
    }
}

data class PreviewEarningSummaryUploadedEventPayload(
    val previewEarningSummaryId: UUID
)
