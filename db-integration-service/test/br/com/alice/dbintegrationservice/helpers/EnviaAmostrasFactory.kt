package br.com.alice.dbintegrationservice.helpers

object EnviaAmostrasFactory {

    fun buildInvalidDbRecebeAtendimentoResponse() =
        "<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "   <s:Header>\n" +
                "      <ActivityId CorrelationId=\"126180be-e3bb-4d1d-8447-d9df8a373f82\" xmlns=\"http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics\">00000000-0000-0000-0000-000000000000</ActivityId>\n" +
                "   </s:Header>\n" +
                "   <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
                "      <EnviaAmostrasResponse xmlns=\"http://diagnosticosdobrasil.com.br\"/>\n" +
                "   </s:Body>\n" +
                "</s:Envelope>"

    fun buildValidEnviaAmostraResponse(
        tags: List<String> = listOf("TAG"),
    ) =
        """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <ActivityId CorrelationId="5bdd15b1-4f3d-46bc-945e-c1f889e765fe" xmlns="http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics">00000000-0000-0000-0000-000000000000</ActivityId>
    </s:Header>
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        <EnviaAmostrasResponse xmlns="http://diagnosticosdobrasil.com.br">
            <EnviaAmostrasResult>
                <Confirmacao>
                    <ConfirmacaoPedidov2>
                        <ct_ConfirmacaoPedidoEtiqueta_v2>
                            <NumeroAtendimentoApoiado>3321321313</NumeroAtendimentoApoiado>
                            <Status>Processado</Status>
                            <NumeroAtendimentoDB>1231321312312</NumeroAtendimentoDB>
                            <Amostras>${buildAmostras(tags)}</Amostras>
                        </ct_ConfirmacaoPedidoEtiqueta_v2>
                    </ConfirmacaoPedidov2>
                </Confirmacao>
            </EnviaAmostrasResult>
        </EnviaAmostrasResponse>
    </s:Body>
</s:Envelope>
        """.trimIndent()

    fun buildAmonstraNaoProcessada() =
        """
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <ActivityId CorrelationId="8a0f867b-2baf-4dfc-a971-aa901d851041" xmlns="http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics">00000000-0000-0000-0000-000000000000</ActivityId>
    </s:Header>
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        <EnviaAmostrasResponse xmlns="http://diagnosticosdobrasil.com.br">
            <EnviaAmostrasResult>
                <Confirmacao>
                    <ConfirmacaoPedidov2>
                        <ct_ConfirmacaoPedidoEtiqueta_v2>
                            <Status>NaoProcessado</Status>
                            <ErroIntegracao>
                                <ct_ErroIntegracao_v2>
                                    <Codigo>7</Codigo>
                                    <Descricao>Não existem amostras disponíveis para os pedidos solicitados</Descricao>
                                </ct_ErroIntegracao_v2>
                            </ErroIntegracao>
                            <Amostras/>
                        </ct_ConfirmacaoPedidoEtiqueta_v2>
                    </ConfirmacaoPedidov2>
                </Confirmacao>
            </EnviaAmostrasResult>
        </EnviaAmostrasResponse>
    </s:Body>
</s:Envelope>
        """.trimIndent()

    private fun buildAmostras(tags: List<String>) =
        tags.map {
            """
                <ct_AmostraEtiqueta_v2>
                   <NumeroAmostra>111338822601</NumeroAmostra>
                   <Exames>CAIO</Exames>
                   <ContadorAmostra>28-033379</ContadorAmostra>
                   <RGPacienteDB>4047878642</RGPacienteDB>
                   <NomePaciente>ALICE DA SILVA</NomePaciente>
                   <MeioColeta>TBAMAR</MeioColeta>
                   <GrupoInterface>ONE</GrupoInterface>
                   <Material>SORO</Material>
                   <RegiaoColeta/>
                   <Volume>1.1</Volume>
                   <Prioridade>R</Prioridade>
                   <TipoCodigoBarras>CODE 128</TipoCodigoBarras>
                   <CodigoInstrumento>B</CodigoInstrumento>
                   <Origem>U200</Origem>
                   <FlagAmostraMae>false</FlagAmostraMae>
                   <TextoAmostraMae/>
                   <DataSistema>2020-08-28T16:20:00</DataSistema>
                   <EtiquetaAmostra>$it</EtiquetaAmostra>
                </ct_AmostraEtiqueta_v2>
                
            """.trimIndent()
        }.joinToString(separator = "") { it }
}
