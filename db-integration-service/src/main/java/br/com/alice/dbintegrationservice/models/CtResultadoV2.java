
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for ct_Resultado_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_Resultado_v2">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NumeroAtendimentoApoiado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="NumeroAtendimentoDB" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RGPacienteApoiado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RGPacienteDB" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="NomePaciente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Sexo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Peso" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *         &lt;element name="Altura" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *         &lt;element name="NumeroCPF" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DataNascimento" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ListaResultadoProcedimentos" type="{http://diagnosticosdobrasil.com.br}ArrayOfCt_ResultadoProcedimentos_v2" minOccurs="0"/>
 *         &lt;element name="UsoApoiado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_Resultado_v2", propOrder = {
    "numeroAtendimentoApoiado",
    "numeroAtendimentoDB",
    "rgPacienteApoiado",
    "rgPacienteDB",
    "nomePaciente",
    "sexo",
    "peso",
    "altura",
    "numeroCPF",
    "dataNascimento",
    "listaResultadoProcedimentos",
    "usoApoiado"
})
public class CtResultadoV2 {

    @XmlElement(name = "NumeroAtendimentoApoiado")
    protected String numeroAtendimentoApoiado;
    @XmlElement(name = "NumeroAtendimentoDB")
    protected String numeroAtendimentoDB;
    @XmlElement(name = "RGPacienteApoiado")
    protected String rgPacienteApoiado;
    @XmlElement(name = "RGPacienteDB")
    protected String rgPacienteDB;
    @XmlElement(name = "NomePaciente")
    protected String nomePaciente;
    @XmlElement(name = "Sexo")
    protected String sexo;
    @XmlElement(name = "Peso")
    protected double peso;
    @XmlElement(name = "Altura")
    protected double altura;
    @XmlElement(name = "NumeroCPF")
    protected String numeroCPF;
    @XmlElement(name = "DataNascimento", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataNascimento;
    @XmlElement(name = "ListaResultadoProcedimentos")
    protected ArrayOfCtResultadoProcedimentosV2 listaResultadoProcedimentos;
    @XmlElement(name = "UsoApoiado")
    protected String usoApoiado;

    /**
     * Gets the value of the numeroAtendimentoApoiado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroAtendimentoApoiado() {
        return numeroAtendimentoApoiado;
    }

    /**
     * Sets the value of the numeroAtendimentoApoiado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroAtendimentoApoiado(String value) {
        this.numeroAtendimentoApoiado = value;
    }

    /**
     * Gets the value of the numeroAtendimentoDB property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroAtendimentoDB() {
        return numeroAtendimentoDB;
    }

    /**
     * Sets the value of the numeroAtendimentoDB property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroAtendimentoDB(String value) {
        this.numeroAtendimentoDB = value;
    }

    /**
     * Gets the value of the rgPacienteApoiado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRGPacienteApoiado() {
        return rgPacienteApoiado;
    }

    /**
     * Sets the value of the rgPacienteApoiado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRGPacienteApoiado(String value) {
        this.rgPacienteApoiado = value;
    }

    /**
     * Gets the value of the rgPacienteDB property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRGPacienteDB() {
        return rgPacienteDB;
    }

    /**
     * Sets the value of the rgPacienteDB property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRGPacienteDB(String value) {
        this.rgPacienteDB = value;
    }

    /**
     * Gets the value of the nomePaciente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomePaciente() {
        return nomePaciente;
    }

    /**
     * Sets the value of the nomePaciente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomePaciente(String value) {
        this.nomePaciente = value;
    }

    /**
     * Gets the value of the sexo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSexo() {
        return sexo;
    }

    /**
     * Sets the value of the sexo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSexo(String value) {
        this.sexo = value;
    }

    /**
     * Gets the value of the peso property.
     * 
     */
    public double getPeso() {
        return peso;
    }

    /**
     * Sets the value of the peso property.
     * 
     */
    public void setPeso(double value) {
        this.peso = value;
    }

    /**
     * Gets the value of the altura property.
     * 
     */
    public double getAltura() {
        return altura;
    }

    /**
     * Sets the value of the altura property.
     * 
     */
    public void setAltura(double value) {
        this.altura = value;
    }

    /**
     * Gets the value of the numeroCPF property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroCPF() {
        return numeroCPF;
    }

    /**
     * Sets the value of the numeroCPF property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroCPF(String value) {
        this.numeroCPF = value;
    }

    /**
     * Gets the value of the dataNascimento property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataNascimento() {
        return dataNascimento;
    }

    /**
     * Sets the value of the dataNascimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataNascimento(XMLGregorianCalendar value) {
        this.dataNascimento = value;
    }

    /**
     * Gets the value of the listaResultadoProcedimentos property.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfCtResultadoProcedimentosV2 }
     *     
     */
    public ArrayOfCtResultadoProcedimentosV2 getListaResultadoProcedimentos() {
        return listaResultadoProcedimentos;
    }

    /**
     * Sets the value of the listaResultadoProcedimentos property.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfCtResultadoProcedimentosV2 }
     *     
     */
    public void setListaResultadoProcedimentos(ArrayOfCtResultadoProcedimentosV2 value) {
        this.listaResultadoProcedimentos = value;
    }

    /**
     * Gets the value of the usoApoiado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsoApoiado() {
        return usoApoiado;
    }

    /**
     * Sets the value of the usoApoiado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsoApoiado(String value) {
        this.usoApoiado = value;
    }

}
