
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_EnviaAmostrasAtendimentoRequest complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_EnviaAmostrasAtendimentoRequest">
 *   &lt;complexContent>
 *     &lt;extension base="{http://diagnosticosdobrasil.com.br}RequestMessage">
 *       &lt;sequence>
 *         &lt;element name="NumeroAtendimentoApoiado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_EnviaAmostrasAtendimentoRequest", propOrder = {
    "numeroAtendimentoApoiado"
})
public class CtEnviaAmostrasAtendimentoRequest
    extends RequestMessage
{

    @XmlElement(name = "NumeroAtendimentoApoiado")
    protected String numeroAtendimentoApoiado;

    /**
     * Gets the value of the numeroAtendimentoApoiado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroAtendimentoApoiado() {
        return numeroAtendimentoApoiado;
    }

    /**
     * Sets the value of the numeroAtendimentoApoiado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroAtendimentoApoiado(String value) {
        this.numeroAtendimentoApoiado = value;
    }

}
