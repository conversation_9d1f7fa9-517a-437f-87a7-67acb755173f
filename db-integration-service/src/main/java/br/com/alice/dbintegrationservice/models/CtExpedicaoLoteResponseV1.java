
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_ExpedicaoLoteResponse_v1 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_ExpedicaoLoteResponse_v1">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ListResponse" type="{http://diagnosticosdobrasil.com.br}ArrayOfCt_ExpedicaoLoteResponseBody_v1" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_ExpedicaoLoteResponse_v1", propOrder = {
    "listResponse"
})
public class CtExpedicaoLoteResponseV1 {

    @XmlElement(name = "ListResponse")
    protected ArrayOfCtExpedicaoLoteResponseBodyV1 listResponse;

    /**
     * Gets the value of the listResponse property.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfCtExpedicaoLoteResponseBodyV1 }
     *     
     */
    public ArrayOfCtExpedicaoLoteResponseBodyV1 getListResponse() {
        return listResponse;
    }

    /**
     * Sets the value of the listResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfCtExpedicaoLoteResponseBodyV1 }
     *     
     */
    public void setListResponse(ArrayOfCtExpedicaoLoteResponseBodyV1 value) {
        this.listResponse = value;
    }

}
