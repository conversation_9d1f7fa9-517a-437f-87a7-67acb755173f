
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_LoteResultados complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_LoteResultados">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="LoteResultadosv1" type="{http://diagnosticosdobrasil.com.br}ct_LoteResultados_v1" minOccurs="0"/>
 *         &lt;element name="LoteResultadosv2" type="{http://diagnosticosdobrasil.com.br}ct_LoteResultados_v2" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_LoteResultados", propOrder = {
    "loteResultadosv1",
    "loteResultadosv2"
})
public class CtLoteResultados {

    @XmlElement(name = "LoteResultadosv1")
    protected CtLoteResultadosV1 loteResultadosv1;
    @XmlElement(name = "LoteResultadosv2")
    protected CtLoteResultadosV2 loteResultadosv2;

    /**
     * Gets the value of the loteResultadosv1 property.
     * 
     * @return
     *     possible object is
     *     {@link CtLoteResultadosV1 }
     *     
     */
    public CtLoteResultadosV1 getLoteResultadosv1() {
        return loteResultadosv1;
    }

    /**
     * Sets the value of the loteResultadosv1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtLoteResultadosV1 }
     *     
     */
    public void setLoteResultadosv1(CtLoteResultadosV1 value) {
        this.loteResultadosv1 = value;
    }

    /**
     * Gets the value of the loteResultadosv2 property.
     * 
     * @return
     *     possible object is
     *     {@link CtLoteResultadosV2 }
     *     
     */
    public CtLoteResultadosV2 getLoteResultadosv2() {
        return loteResultadosv2;
    }

    /**
     * Sets the value of the loteResultadosv2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtLoteResultadosV2 }
     *     
     */
    public void setLoteResultadosv2(CtLoteResultadosV2 value) {
        this.loteResultadosv2 = value;
    }

}
