
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_DadosStatusAmostra complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_DadosStatusAmostra">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Pedido" type="{http://diagnosticosdobrasil.com.br}ct_DadosStatusPedido_v2" minOccurs="0"/>
 *         &lt;element name="ListaProcedimento" type="{http://diagnosticosdobrasil.com.br}ArrayOfCt_DadosStatusProcedimento_v2" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_DadosStatusAmostra", propOrder = {
    "pedido",
    "listaProcedimento"
})
public class CtDadosStatusAmostra {

    @XmlElement(name = "Pedido")
    protected CtDadosStatusPedidoV2 pedido;
    @XmlElement(name = "ListaProcedimento")
    protected ArrayOfCtDadosStatusProcedimentoV2 listaProcedimento;

    /**
     * Gets the value of the pedido property.
     * 
     * @return
     *     possible object is
     *     {@link CtDadosStatusPedidoV2 }
     *     
     */
    public CtDadosStatusPedidoV2 getPedido() {
        return pedido;
    }

    /**
     * Sets the value of the pedido property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtDadosStatusPedidoV2 }
     *     
     */
    public void setPedido(CtDadosStatusPedidoV2 value) {
        this.pedido = value;
    }

    /**
     * Gets the value of the listaProcedimento property.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfCtDadosStatusProcedimentoV2 }
     *     
     */
    public ArrayOfCtDadosStatusProcedimentoV2 getListaProcedimento() {
        return listaProcedimento;
    }

    /**
     * Sets the value of the listaProcedimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfCtDadosStatusProcedimentoV2 }
     *     
     */
    public void setListaProcedimento(ArrayOfCtDadosStatusProcedimentoV2 value) {
        this.listaProcedimento = value;
    }

}
