
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_Questionario_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_Questionario_v2">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CodigoPerguntaQuestionario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RespostaQuestionario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_Questionario_v2", propOrder = {
    "codigoPerguntaQuestionario",
    "respostaQuestionario"
})
public class CtQuestionarioV2 {

    @XmlElement(name = "CodigoPerguntaQuestionario")
    protected String codigoPerguntaQuestionario;
    @XmlElement(name = "RespostaQuestionario")
    protected String respostaQuestionario;

    /**
     * Gets the value of the codigoPerguntaQuestionario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoPerguntaQuestionario() {
        return codigoPerguntaQuestionario;
    }

    /**
     * Sets the value of the codigoPerguntaQuestionario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoPerguntaQuestionario(String value) {
        this.codigoPerguntaQuestionario = value;
    }

    /**
     * Gets the value of the respostaQuestionario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRespostaQuestionario() {
        return respostaQuestionario;
    }

    /**
     * Sets the value of the respostaQuestionario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRespostaQuestionario(String value) {
        this.respostaQuestionario = value;
    }

}
