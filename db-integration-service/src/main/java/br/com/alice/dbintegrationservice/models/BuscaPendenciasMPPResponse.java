
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BuscaPendenciasMPPResult" type="{http://diagnosticosdobrasil.com.br}ct_BuscaPendenciasMPPResponse_v2" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "buscaPendenciasMPPResult"
})
@XmlRootElement(name = "BuscaPendenciasMPPResponse")
public class BuscaPendenciasMPPResponse {

    @XmlElement(name = "BuscaPendenciasMPPResult")
    protected CtBuscaPendenciasMPPResponseV2 buscaPendenciasMPPResult;

    /**
     * Gets the value of the buscaPendenciasMPPResult property.
     * 
     * @return
     *     possible object is
     *     {@link CtBuscaPendenciasMPPResponseV2 }
     *     
     */
    public CtBuscaPendenciasMPPResponseV2 getBuscaPendenciasMPPResult() {
        return buscaPendenciasMPPResult;
    }

    /**
     * Sets the value of the buscaPendenciasMPPResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtBuscaPendenciasMPPResponseV2 }
     *     
     */
    public void setBuscaPendenciasMPPResult(CtBuscaPendenciasMPPResponseV2 value) {
        this.buscaPendenciasMPPResult = value;
    }

}
