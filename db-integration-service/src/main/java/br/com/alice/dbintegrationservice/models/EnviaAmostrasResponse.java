
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="EnviaAmostrasResult" type="{http://diagnosticosdobrasil.com.br}ct_RecebeAtendimentoEtiquetaResponse" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "enviaAmostrasResult"
})
@XmlRootElement(name = "EnviaAmostrasResponse")
public class EnviaAmostrasResponse {

    @XmlElement(name = "EnviaAmostrasResult")
    protected CtRecebeAtendimentoEtiquetaResponse enviaAmostrasResult;

    /**
     * Gets the value of the enviaAmostrasResult property.
     * 
     * @return
     *     possible object is
     *     {@link CtRecebeAtendimentoEtiquetaResponse }
     *     
     */
    public CtRecebeAtendimentoEtiquetaResponse getEnviaAmostrasResult() {
        return enviaAmostrasResult;
    }

    /**
     * Sets the value of the enviaAmostrasResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtRecebeAtendimentoEtiquetaResponse }
     *     
     */
    public void setEnviaAmostrasResult(CtRecebeAtendimentoEtiquetaResponse value) {
        this.enviaAmostrasResult = value;
    }

}
