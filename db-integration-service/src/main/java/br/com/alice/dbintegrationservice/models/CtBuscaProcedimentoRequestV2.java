
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_BuscaProcedimentoRequest_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_BuscaProcedimentoRequest_v2">
 *   &lt;complexContent>
 *     &lt;extension base="{http://diagnosticosdobrasil.com.br}RequestMessage">
 *       &lt;sequence>
 *         &lt;element name="CodigoProcedimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="NomeProcedimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_BuscaProcedimentoRequest_v2", propOrder = {
    "codigoProcedimento",
    "nomeProcedimento"
})
public class CtBuscaProcedimentoRequestV2
    extends RequestMessage
{

    @XmlElement(name = "CodigoProcedimento")
    protected String codigoProcedimento;
    @XmlElement(name = "NomeProcedimento")
    protected String nomeProcedimento;

    /**
     * Gets the value of the codigoProcedimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoProcedimento() {
        return codigoProcedimento;
    }

    /**
     * Sets the value of the codigoProcedimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoProcedimento(String value) {
        this.codigoProcedimento = value;
    }

    /**
     * Gets the value of the nomeProcedimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeProcedimento() {
        return nomeProcedimento;
    }

    /**
     * Sets the value of the nomeProcedimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeProcedimento(String value) {
        this.nomeProcedimento = value;
    }

}
