
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_ConfirmacaoProcedimento_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_ConfirmacaoProcedimento_v2">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CodigoExameDB" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Status" type="{http://diagnosticosdobrasil.com.br}StatusConfirmacaoEnum_v2"/>
 *         &lt;element name="ErroIntegracao" type="{http://diagnosticosdobrasil.com.br}ArrayOfCt_ErroIntegracao_v2" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_ConfirmacaoProcedimento_v2", propOrder = {
    "codigoExameDB",
    "status",
    "erroIntegracao"
})
public class CtConfirmacaoProcedimentoV2 {

    @XmlElement(name = "CodigoExameDB")
    protected String codigoExameDB;
    @XmlElement(name = "Status", required = true)
    protected StatusConfirmacaoEnumV2 status;
    @XmlElement(name = "ErroIntegracao")
    protected ArrayOfCtErroIntegracaoV2 erroIntegracao;

    /**
     * Gets the value of the codigoExameDB property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoExameDB() {
        return codigoExameDB;
    }

    /**
     * Sets the value of the codigoExameDB property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoExameDB(String value) {
        this.codigoExameDB = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link StatusConfirmacaoEnumV2 }
     *     
     */
    public StatusConfirmacaoEnumV2 getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link StatusConfirmacaoEnumV2 }
     *     
     */
    public void setStatus(StatusConfirmacaoEnumV2 value) {
        this.status = value;
    }

    /**
     * Gets the value of the erroIntegracao property.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfCtErroIntegracaoV2 }
     *     
     */
    public ArrayOfCtErroIntegracaoV2 getErroIntegracao() {
        return erroIntegracao;
    }

    /**
     * Sets the value of the erroIntegracao property.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfCtErroIntegracaoV2 }
     *     
     */
    public void setErroIntegracao(ArrayOfCtErroIntegracaoV2 value) {
        this.erroIntegracao = value;
    }

}
