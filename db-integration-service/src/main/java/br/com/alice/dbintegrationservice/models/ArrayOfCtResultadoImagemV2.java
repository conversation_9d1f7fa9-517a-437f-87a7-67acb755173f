
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for ArrayOfCt_ResultadoImagem_v2 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ArrayOfCt_ResultadoImagem_v2">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ct_ResultadoImagem_v2" type="{http://diagnosticosdobrasil.com.br}ct_ResultadoImagem_v2" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfCt_ResultadoImagem_v2", propOrder = {
    "ctResultadoImagemV2"
})
public class ArrayOfCtResultadoImagemV2 {

    @XmlElement(name = "ct_ResultadoImagem_v2", nillable = true)
    protected List<CtResultadoImagemV2> ctResultadoImagemV2;

    /**
     * Gets the value of the ctResultadoImagemV2 property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ctResultadoImagemV2 property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCtResultadoImagemV2().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CtResultadoImagemV2 }
     * 
     * 
     */
    public List<CtResultadoImagemV2> getCtResultadoImagemV2() {
        if (ctResultadoImagemV2 == null) {
            ctResultadoImagemV2 = new ArrayList<CtResultadoImagemV2>();
        }
        return this.ctResultadoImagemV2;
    }

}
