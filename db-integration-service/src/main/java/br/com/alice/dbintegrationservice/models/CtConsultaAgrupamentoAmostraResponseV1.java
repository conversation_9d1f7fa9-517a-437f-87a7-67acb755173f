
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_ConsultaAgrupamentoAmostraResponse_v1 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_ConsultaAgrupamentoAmostraResponse_v1">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Status" type="{http://diagnosticosdobrasil.com.br}StatusProcessamentoAmostra"/>
 *         &lt;element name="Agrupamento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Mensagem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_ConsultaAgrupamentoAmostraResponse_v1", propOrder = {
    "status",
    "agrupamento",
    "mensagem"
})
public class CtConsultaAgrupamentoAmostraResponseV1 {

    @XmlElement(name = "Status", required = true)
    protected StatusProcessamentoAmostra status;
    @XmlElement(name = "Agrupamento")
    protected String agrupamento;
    @XmlElement(name = "Mensagem")
    protected String mensagem;

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link StatusProcessamentoAmostra }
     *     
     */
    public StatusProcessamentoAmostra getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link StatusProcessamentoAmostra }
     *     
     */
    public void setStatus(StatusProcessamentoAmostra value) {
        this.status = value;
    }

    /**
     * Gets the value of the agrupamento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAgrupamento() {
        return agrupamento;
    }

    /**
     * Sets the value of the agrupamento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAgrupamento(String value) {
        this.agrupamento = value;
    }

    /**
     * Gets the value of the mensagem property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMensagem() {
        return mensagem;
    }

    /**
     * Sets the value of the mensagem property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMensagem(String value) {
        this.mensagem = value;
    }

}
