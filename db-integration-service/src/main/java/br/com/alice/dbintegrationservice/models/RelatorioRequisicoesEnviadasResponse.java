
package br.com.alice.dbintegrationservice.models;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="RelatorioRequisicoesEnviadasResult" type="{http://diagnosticosdobrasil.com.br}ct_RelatorioRequisicoesResponse_v2" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "relatorioRequisicoesEnviadasResult"
})
@XmlRootElement(name = "RelatorioRequisicoesEnviadasResponse")
public class RelatorioRequisicoesEnviadasResponse {

    @XmlElement(name = "RelatorioRequisicoesEnviadasResult")
    protected CtRelatorioRequisicoesResponseV2 relatorioRequisicoesEnviadasResult;

    /**
     * Gets the value of the relatorioRequisicoesEnviadasResult property.
     * 
     * @return
     *     possible object is
     *     {@link CtRelatorioRequisicoesResponseV2 }
     *     
     */
    public CtRelatorioRequisicoesResponseV2 getRelatorioRequisicoesEnviadasResult() {
        return relatorioRequisicoesEnviadasResult;
    }

    /**
     * Sets the value of the relatorioRequisicoesEnviadasResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtRelatorioRequisicoesResponseV2 }
     *     
     */
    public void setRelatorioRequisicoesEnviadasResult(CtRelatorioRequisicoesResponseV2 value) {
        this.relatorioRequisicoesEnviadasResult = value;
    }

}
