package br.com.alice.dbintegrationservice.services

import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.DbLaboratoryTestResultProcess
import br.com.alice.data.layer.services.DbLaboratoryTestResultProcessDataService
import br.com.alice.data.layer.services.DbLaboratoryTestResultProcessDataService.FieldOptions
import br.com.alice.data.layer.services.DbLaboratoryTestResultProcessDataService.OrderingOptions
import br.com.alice.dbintegrationclient.client.DbLaboratoryTestResultProcessService

class DbLaboratoryTestResultProcessServiceImpl(
    private val dbLaboratoryTestResultProcessDataService : DbLaboratoryTestResultProcessDataService) : DbLaboratoryTestResultProcessService,
    Getter<DbLaboratoryTestResultProcess> by dbLaboratoryTestResultProcessDataService,
    Updater<DbLaboratoryTestResultProcess> by dbLaboratoryTestResultProcessDataService,
    Adder<DbLaboratoryTestResultProcess> by dbLaboratoryTestResultProcessDataService,
    Finder<FieldOptions, OrderingOptions,
            DbLaboratoryTestResultProcess> by dbLaboratoryTestResultProcessDataService {

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

}
