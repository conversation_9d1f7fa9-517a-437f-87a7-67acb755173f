package br.com.alice.dbintegrationservice.adapters

import br.com.alice.common.models.Sex
import br.com.alice.data.layer.events.FailedProcedure
import br.com.alice.data.layer.events.FailureReason
import br.com.alice.data.layer.models.Person
import br.com.alice.dbintegrationservice.models.ClaimRequestResponse
import br.com.alice.dbintegrationservice.models.CtRecebeAtendimentoEtiquetaResponse
import br.com.alice.dbintegrationservice.models.RecebeAtendimentoResponse
import br.com.alice.dbintegrationservice.models.RequestPerson
import java.io.StringReader
import java.time.LocalDate
import java.time.LocalDateTime
import javax.xml.bind.JAXBContext
import javax.xml.bind.Unmarshaller

object ClaimRequestAdapter {

    fun adaptClaimRequestResponse(rawResponse: String): ClaimRequestResponse {
        val result = extractRecebeAtendimentoResponseFromResponse(rawResponse).recebeAtendimentoResult
        val failedProcedures = result.statusLote
            .ctStatusLoteV2
            .firstOrNull()
            ?.pedidos
            ?.ctStatusLotePedidoV2?.firstOrNull()
            ?.errosProcedimentos
            ?.ctConfirmacaoProcedimentoV2
            ?.filter {
                it.erroIntegracao.ctErroIntegracaoV2?.firstOrNull() != null
            }?.map {
                val error = it.erroIntegracao.ctErroIntegracaoV2.first()
                FailedProcedure(
                    status = toStatus(error.codigo),
                    description = error.descricao,
                    code = it.codigoExameDB
                )
            }

        val successfulProcedures = result.statusLote
            .ctStatusLoteV2
            .firstOrNull()
            ?.pedidos
            ?.ctStatusLotePedidoV2?.firstOrNull()
            ?.procedimentos
            ?.ctStatusLoteProcedimentoV2
            ?.mapNotNull { it.codigoExameDB }

        return ClaimRequestResponse(
            failedProcedures.orEmpty(),
            successfulProcedures.orEmpty(),
            extractTags(result)
        )
    }

    private fun extractTags(result: CtRecebeAtendimentoEtiquetaResponse): List<String> =
        result.confirmacao
            .confirmacaoPedidov2
            .ctConfirmacaoPedidoEtiquetaV2.firstOrNull()
            ?.amostras
            ?.ctAmostraEtiquetaV2
            ?.mapNotNull { it.etiquetaAmostra } ?: emptyList()

    fun personToRequestPerson(person: Person) = RequestPerson(
        id = person.id.id,
        fullName = person.fullSocialName,
        sex = adaptSex(person.sex),
        dateOfBirth = adaptDateOfBirth(person.dateOfBirth),
        nationalId = person.nationalId
    )

    private fun toStatus(code: String) = when (code.toInt()) {
        5 -> FailureReason.DB_INVALID_PROCEDURE
        7 -> FailureReason.DB_INVALID_REGION
        9 -> FailureReason.DB_ALREADY_SENT
        else -> FailureReason.DB_UNKNOWN
    }

    private fun adaptDateOfBirth(dateOfBirth: LocalDateTime?): LocalDate = when (dateOfBirth) {
        null -> throw IllegalArgumentException("Non valid date of birth")
        else -> dateOfBirth.toLocalDate()
    }

    private fun adaptSex(sex: Sex?) = when (sex) {
        Sex.MALE -> "M"
        Sex.FEMALE -> "F"
        Sex.INTERSEX -> "I"
        else -> throw IllegalArgumentException("Non valid sex")
    }

    private fun extractRecebeAtendimentoResponseFromResponse(response: String): RecebeAtendimentoResponse {
        val startPosition =
            response.indexOf("<RecebeAtendimentoResponse xmlns=\"http://diagnosticosdobrasil.com.br\">")
        val endPosition = response.indexOf("</RecebeAtendimentoResponse>") + "</RecebeAtendimentoResponse>".length
        val xmlData = response.substring(startPosition, endPosition)
        val jaxbContext: JAXBContext = JAXBContext.newInstance(RecebeAtendimentoResponse::class.java)
        val jaxbUnmarshaller: Unmarshaller = jaxbContext.createUnmarshaller()

        return jaxbUnmarshaller.unmarshal(StringReader(xmlData)) as RecebeAtendimentoResponse
    }
}
