package br.com.alice.dbintegrationservice.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.dbintegrationservice.controllers.LaboratoryTestResultController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.apiRoutes() {
    route("/laboratory_test_result") {
        val laboratoryTestResultController by inject<LaboratoryTestResultController>()
        post("/") { asyncLayer { coHandler(laboratoryTestResultController::process) } }
        post("reprocess/{attendanceId}") { asyncLayer { coHandler("attendanceId", laboratoryTestResultController::reprocess) }}
    }
}
