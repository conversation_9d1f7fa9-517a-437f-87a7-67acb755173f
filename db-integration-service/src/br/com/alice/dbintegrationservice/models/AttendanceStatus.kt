package br.com.alice.dbintegrationservice.models

data class CheckAttendanceStatusResponse(
    val code: String,
    val status: AttendanceStatus,
    val failureReason: String? = null
) {
    fun ready() = status == AttendanceStatus.DISCLOSED || status == AttendanceStatus.RELEASE_CLINICAL
}

enum class AttendanceStatus(val description: String) {
    WAITING("Aguardando"),
    RECEIVED_ORIGIN("RecebidoOrigem"),
    TRANSPORT("Transporte"),
    RECEIVED_PRODUCTIVE_UNIT("RecebidoUP"),
    COLLECTION_CONFIRMATION("ConfirmacaoColeta"),
    SCREENED("Triado"),
    RECEIVED_TECNICAL("RecebidoAreaTecnica"),
    RELEASE_TECNICAL("LiberadoTecnico"),
    RELEASE_CLINICAL("LiberadoClinico"),
    DISCLOSED("Divulgado"),
    FAILED("MPP"),
    INVALID_STATUS("Status Inválido")
}
