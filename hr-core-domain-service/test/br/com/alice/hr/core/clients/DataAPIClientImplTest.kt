package br.com.alice.hr.core.clients

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.readFile
import br.com.alice.common.serialization.gsonBuilder
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.hr.core.ServiceConfig
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.clearAllMocks
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.assertThrows
import kotlin.test.AfterTest
import kotlin.test.Test

class DataAPIClientImplTest {
    @AfterTest
    fun setup() = clearAllMocks()

    private val baseUrl = ServiceConfig.DataAPI.baseUrl
    private val populationDataResponse = readFile("testResources/dataapi/population-data.json")
    private val frequencyCoordinationDataResponse = readFile("testResources/dataapi/frequency-coordination-data.json")
    private val gson = gsonBuilder()
        .registerTypeAdapter(PopulationDataResponse::class.java, PopulationDataResponseDeserializer())
        .registerTypeAdapter(FrequencyCoordinationDataResponse::class.java, FrequencyCoordinationDataResponseDeserializer())
        .create()

    @Test
    fun `#getPopulationDataByCompany success`() = runBlocking {
        val companyId = RangeUUID.generate()
        val expectedResponse = gson.fromJson<PopulationDataResponse>(populationDataResponse)
        val httpClient = httpClientMock(
            responseContent = populationDataResponse,
            statusCode = HttpStatusCode.OK,
            url = "$baseUrl/$companyId/population-data",
            httpMethod = HttpMethod.Get
        )

        val dataAPIClient = DataAPIClientImpl(client = httpClient)

        val actualResponse = dataAPIClient.getPopulationDataByCompany(companyId)

        assertEquals(expectedResponse, actualResponse)
    }

    @Test
    fun `#getFrequencyCoordinationByCompany success`() = runBlocking {
        val companyId = RangeUUID.generate()
        val expectedResponse = gson.fromJson<FrequencyCoordinationDataResponse>(frequencyCoordinationDataResponse)
        val httpClient = httpClientMock(
            responseContent = frequencyCoordinationDataResponse,
            statusCode = HttpStatusCode.OK,
            url = "$baseUrl/$companyId/frequency-coordination",
            httpMethod = HttpMethod.Get
        )

        val dataAPIClient = DataAPIClientImpl(client = httpClient)

        val actualResponse = dataAPIClient.getFrequencyCoordinationByCompany(companyId)

        assertEquals(expectedResponse, actualResponse)
    }

    @Test
    fun `#getPopulationDataByCompany failure`(): Unit {
        val companyId = RangeUUID.generate()
        val httpClient = httpClientMock(
            responseContent = "{ \"error\": \"invalid company id\"}",
            statusCode = HttpStatusCode.BadRequest,
            url = "$baseUrl/$companyId/population-data",
            httpMethod = HttpMethod.Get
        )

        val dataAPIClient = DataAPIClientImpl(client = httpClient)

        assertThrows<DataAPIClientException> {
            runBlocking {
                dataAPIClient.getPopulationDataByCompany(companyId)
            }
        }
    }

    private fun httpClientMock(
        responseContent: String = "",
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        url: String,
        httpMethod: HttpMethod
    ): HttpClient = HttpClient(MockEngine) {
        expectSuccess = true
        install(ContentNegotiation) {
            simpleGson {
                registerTypeAdapter(PopulationDataResponse::class.java, PopulationDataResponseDeserializer())
                registerTypeAdapter(FrequencyCoordinationDataResponse::class.java, FrequencyCoordinationDataResponseDeserializer())
            }
        }
        engine {
            addHandler { request ->
                if ((request.method == httpMethod &&
                            request.url.toString() == url)
                ) {
                    println("responding with $responseContent and status code $statusCode")
                    respond(
                        responseContent,
                        statusCode,
                        headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    )
                } else {
                    error("unknown request")
                }
            }
        }
    }
}
