package br.com.alice.hr.core.model.converters

import br.com.alice.data.layer.models.ConsolidatedPopulationData
import br.com.alice.data.layer.models.SectionData
import br.com.alice.hr.core.clients.PopulationDataResponse
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class PopulationDataConvertersTest {
    @Test
    fun `#toConsolidatedPopulationData should convert ConsolidatedPopulationData`() {
        val data = PopulationDataResponse(
            companyId = UUID.randomUUID().toString(),
            companyName = "Alice",
            activeMembers = 10,
            medianAge = 30,
            holders = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            dependents = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            sexDemographics = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            riskClassification = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            healthConditions = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
        )
        val expected = ConsolidatedPopulationData(
            activeMembers = 10,
            medianAge = 30,
            holders = SectionData(10.0F, "Alice", 10),
            dependents = SectionData(10.0F, "Alice", 10),
            sex = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            riskClassification = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            healthConditions = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
        )

        val result = data.toConsolidatedPopulationData()

        assertEquals(expected, result)
    }

    @Test
    fun `#toConsolidatedPopulationData should convert ConsolidatedPopulationData with sex null`() {
        val data = PopulationDataResponse(
            companyId = UUID.randomUUID().toString(),
            companyName = "Alice",
            activeMembers = 10,
            medianAge = 30,
            holders = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            dependents = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            sexDemographics = null,
            riskClassification = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            healthConditions = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
        )
        val expected = ConsolidatedPopulationData(
            activeMembers = 10,
            medianAge = 30,
            holders = SectionData(10.0F, "Alice", 10),
            dependents = SectionData(10.0F, "Alice", 10),
            sex = null,
            riskClassification = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            healthConditions = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
        )

        val result = data.toConsolidatedPopulationData()

        assertEquals(expected, result)
    }

    @Test
    fun `#toConsolidatedPopulationData should convert ConsolidatedPopulationData with riskClassification null`() {
        val data = PopulationDataResponse(
            companyId = UUID.randomUUID().toString(),
            companyName = "Alice",
            activeMembers = 10,
            medianAge = 30,
            holders = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            dependents = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            sexDemographics = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            riskClassification = null,
            healthConditions = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
        )
        val expected = ConsolidatedPopulationData(
            activeMembers = 10,
            medianAge = 30,
            holders = SectionData(10.0F, "Alice", 10),
            dependents = SectionData(10.0F, "Alice", 10),
            sex = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            riskClassification = null,
            healthConditions = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
        )

        val result = data.toConsolidatedPopulationData()

        assertEquals(expected, result)
    }

    @Test
    fun `#toConsolidatedPopulationData should convert ConsolidatedPopulationData with healthConditions null`() {
        val data = PopulationDataResponse(
            companyId = UUID.randomUUID().toString(),
            companyName = "Alice",
            activeMembers = 10,
            medianAge = 30,
            holders = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            dependents = PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10),
            sexDemographics = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            riskClassification = listOf(
                PopulationDataResponse.GenericNamedCountPercentage(10.0, "Alice", 10)
            ),
            healthConditions = null,
        )
        val expected = ConsolidatedPopulationData(
            activeMembers = 10,
            medianAge = 30,
            holders = SectionData(10.0F, "Alice", 10),
            dependents = SectionData(10.0F, "Alice", 10),
            sex = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            riskClassification = listOf(
                SectionData(10.0F, "Alice", 10)
            ),
            healthConditions = null,
        )

        val result = data.toConsolidatedPopulationData()

        assertEquals(expected, result)
    }
}
