package br.com.alice.hr.core.services

import br.com.alice.business.client.CompanyService
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.hr.core.model.CompanyAndBillingAccountableParty
import br.com.alice.hr.core.model.CompanyInfoUpdate
import br.com.alice.moneyin.client.BillingAccountablePartyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertFails

class HrCompanyServiceImplTest {
    private val companyService: CompanyService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val hrCompanyService = HrCompanyServiceImpl(
        companyService,
        billingAccountablePartyService,
    )

    private val company = TestModelFactory.buildCompany()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty().copy(invoiceEmail = "<EMAIL>")
    private val companyInfoUpdate = CompanyInfoUpdate(
        name = "New Company Name",
        phoneNumber = "New Phone Number",
        legalResponsibleEmail = "<EMAIL>",
        financialManagerEmail = "<EMAIL>",
        financialInvoiceEmail = "<EMAIL>",
    )
    private val companyUpdated = company.copy(
        name = companyInfoUpdate.name!!,
        phoneNumber = companyInfoUpdate.phoneNumber!!,
        email = companyInfoUpdate.legalResponsibleEmail!!,
    )
    private val billingAccountablePartyUpdated = billingAccountableParty.copy(
        email = companyInfoUpdate.financialManagerEmail!!,
        invoiceEmail = companyInfoUpdate.financialInvoiceEmail!!,
    )

    @Test
    fun `#updateInformation should update successfully`() = runBlocking {
        coEvery {
            companyService.update(
                match {
                    it.name == companyInfoUpdate.name &&
                    it.phoneNumber == companyInfoUpdate.phoneNumber &&
                    it.email == companyInfoUpdate.legalResponsibleEmail
                }
            )
        } returns companyUpdated.success()
        coEvery {
            billingAccountablePartyService.update(
                match {
                    it.email == companyInfoUpdate.financialManagerEmail &&
                    it.invoiceEmail == companyInfoUpdate.financialInvoiceEmail
                }
            )
        } returns billingAccountablePartyUpdated.success()

        val result = hrCompanyService.updateInformation(
            company,
            billingAccountableParty,
            companyInfoUpdate,
        )

        assertThat(result).isSuccessWithData(
            CompanyAndBillingAccountableParty(
                company = companyUpdated,
                billingAccountableParty = billingAccountablePartyUpdated,
            )
        )

        coVerifyOnce { companyService.update(any()) }
        coVerifyOnce { billingAccountablePartyService.update(any()) }
    }

    @Test
    fun `#updateInformation should fail when company update fails`() = runBlocking {
        coEvery {
            companyService.update(
                match {
                    it.name == companyInfoUpdate.name &&
                    it.phoneNumber == companyInfoUpdate.phoneNumber &&
                    it.email == companyInfoUpdate.legalResponsibleEmail
                }
            )
        } returns IllegalArgumentException().failure()

        assertFails {
            hrCompanyService.updateInformation(
                company,
                billingAccountableParty,
                companyInfoUpdate,
            )
        }

        coVerifyOnce { companyService.update(any()) }
        coVerifyNone { billingAccountablePartyService.update(any()) }
    }

    @Test
    fun `#updateInformation should fail and reset company when billing accountable party update fails`() = runBlocking {
        coEvery {
            companyService.update(
                match {
                    it.name == companyInfoUpdate.name &&
                    it.phoneNumber == companyInfoUpdate.phoneNumber &&
                    it.email == companyInfoUpdate.legalResponsibleEmail
                }
            )
        } returns companyUpdated.success()
        coEvery {
            billingAccountablePartyService.update(
                match {
                    it.email == companyInfoUpdate.financialManagerEmail &&
                    it.invoiceEmail == companyInfoUpdate.financialInvoiceEmail
                }
            )
        } returns Exception().failure()
        coEvery {
            companyService.update(
                match {
                    it.name == company.name &&
                    it.phoneNumber == company.phoneNumber &&
                    it.email == company.email
                }
            )
        } returns company.success()

        assertFails {
            hrCompanyService.updateInformation(
                company,
                billingAccountableParty,
                companyInfoUpdate,
            )
        }

        coVerify(exactly = 2) { companyService.update(any()) }
        coVerifyOnce { billingAccountablePartyService.update(any()) }
    }
}
