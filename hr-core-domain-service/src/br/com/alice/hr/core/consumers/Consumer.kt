package br.com.alice.hr.core.consumers

import br.com.alice.common.consumer.Consumer
import br.com.alice.common.kafka.consumer.AutoRetryableConsumer
import br.com.alice.data.layer.HR_CORE_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME
import kotlin.reflect.KClass

open class Consumer : Consumer(HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME)

open class AutoRetryableConsumer(vararg types: KClass<out Exception>) :
    AutoRetryableConsumer(HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME, *types)