package br.com.alice.hr.core.services

import br.com.alice.business.client.CompanyService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.Company
import br.com.alice.hr.core.client.HrCompanyService
import br.com.alice.hr.core.model.CompanyAndBillingAccountableParty
import br.com.alice.hr.core.model.CompanyInfoUpdate
import br.com.alice.moneyin.client.BillingAccountablePartyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class HrCompanyServiceImpl(
    private val companyService: CompanyService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
): HrCompanyService {
    override suspend fun updateInformation(
        company: Company,
        billingAccountableParty: BillingAccountableParty,
        infoToUpdate: CompanyInfoUpdate,
    ): Result<CompanyAndBillingAccountableParty, Throwable> {
        logger.info("Gonna update company information")

        val companyUpdated = companyService.update(
            company.copy(
                name = infoToUpdate.name ?: company.name,
                phoneNumber = infoToUpdate.phoneNumber ?: company.phoneNumber,
                email = infoToUpdate.legalResponsibleEmail ?: company.email,
            )
        ).get()

        logger.info(
            "Trying to update company information",
            "companyId" to company.id,
        )

        val billingAccountablePartyUpdated = updateBillingAccountableParty(
            billingAccountableParty,
            infoToUpdate,
            company,
        )

        logger.info(
            "Trying to update billing accountable party information",
            "companyId" to company.id,
            "billingAccountablePartyId" to billingAccountableParty.id,
        )

        return CompanyAndBillingAccountableParty(
            company = companyUpdated,
            billingAccountableParty = billingAccountablePartyUpdated,
        ).success()
    }

    private suspend fun updateBillingAccountableParty(
        billingAccountableParty: BillingAccountableParty,
        infoToUpdate: CompanyInfoUpdate,
        company: Company,
    ): BillingAccountableParty =
        try {
            billingAccountablePartyService.update(
                billingAccountableParty.copy(
                    email = infoToUpdate.financialManagerEmail ?: billingAccountableParty.email,
                    invoiceEmail = infoToUpdate.financialInvoiceEmail ?: billingAccountableParty.invoiceEmail,
                )
            ).get()
        } catch (e: Throwable) {
            logger.error(
                "Error trying to update billing accountable party",
                e,
                "companyId" to company.id,
                "billingAccountablePartyId" to billingAccountableParty.id,
            )

            resetCompany(company)

            throw e
        }

    private suspend fun resetCompany(
        company: Company,
    ) = companyService.update(
        company.copy(
            name = company.name,
            phoneNumber = company.phoneNumber,
            email = company.email,
        )
    ).get()
}
