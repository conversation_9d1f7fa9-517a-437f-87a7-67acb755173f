package br.com.alice.hr.core.services.internal

import br.com.alice.common.extensions.coResultOf
import br.com.alice.hr.core.clients.interfaces.DataAPIClient
import br.com.alice.hr.core.clients.FrequencyCoordinationDataResponse
import java.util.UUID

class FrequencyCoordinationDataService(
    private val dataAPIClient: DataAPIClient,
) {
    suspend fun getFrequencyCoordinationData(companyId: UUID) =
        coResultOf<FrequencyCoordinationDataResponse, Throwable> {
            dataAPIClient.getFrequencyCoordinationByCompany(companyId)
        }
}
