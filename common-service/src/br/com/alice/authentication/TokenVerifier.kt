package br.com.alice.authentication

import br.com.alice.authentication.Authenticator.USER_ID_KEY
import br.com.alice.authentication.Authenticator.USER_TYPE_KEY
import br.com.alice.common.core.exceptions.AuthorizationException
import com.google.api.client.json.gson.GsonFactory
import com.google.api.client.json.webtoken.JsonWebSignature
import com.google.firebase.auth.internal.FirebaseCustomAuthToken

interface TokenVerifier {

    fun verifyToken(token: String): Claims?

    fun verifySystemAccessToken(token: String): Claims?

    fun verifyEnvironmentToken(token: String): Map<String, Any>?
}

class TokenVerifierImpl(private val systemAccessTokenVerifier: SystemAccessTokenVerifier)
    : TokenVerifier {

    private val jwsParser = JsonWebSignature.parser(GsonFactory()).setPayloadClass(FirebaseCustomAuthToken.Payload::class.java)

    override fun verifyToken(token: String): Claims? {
        val jws = jwsParser.parse(token)

        return if (jws.payload.audience == GOOGLE_AUDIENCE) {
            systemAccessTokenVerifier.verifyToken(token)
        } else {
            val firebaseToken = Authenticator.verifyIdToken(token, false)
            val claimsHasType = firebaseToken.claims.containsKey(USER_TYPE_KEY)
            val claimsHasId = firebaseToken.claims.containsKey(USER_ID_KEY)
            if (!(claimsHasType && claimsHasId))
                throw AuthorizationException("email=${firebaseToken.email} claims_has_type=$claimsHasType claims_has_id=$claimsHasId")
            Claims.fromMap(firebaseToken.claims)
        }
    }

    override fun verifySystemAccessToken(token: String): Claims? {
        val jws = jwsParser.parse(token)
        if (jws.payload.audience == GOOGLE_AUDIENCE) {
            return systemAccessTokenVerifier.verifyToken(token)
        }
        return null
    }

    override fun verifyEnvironmentToken(token: String) = systemAccessTokenVerifier.verifyTokenRaw(token)
}

private const val GOOGLE_AUDIENCE = "https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit"
