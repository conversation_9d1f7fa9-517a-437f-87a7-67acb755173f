package br.com.alice.common.application.plugin

import io.github.resilience4j.ratelimiter.RateLimiterConfig
import io.github.resilience4j.ratelimiter.RateLimiterRegistry
import io.ktor.server.application.ApplicationCallPipeline
import io.ktor.server.application.BaseApplicationPlugin
import io.ktor.util.AttributeKey
import java.time.Duration
import kotlin.reflect.jvm.jvmName

class RateLimitPlugin(configuration: Configuration) {
    val times = configuration.times
    val duration = configuration.durationInMillis
    val config: RateLimiterConfig = RateLimiterConfig.custom()
        .limitRefreshPeriod(Duration.ofMillis(duration))
        .limitForPeriod(times)
        .timeoutDuration(Duration.ofMillis(400))
        .build()

    val registry: RateLimiterRegistry = RateLimiterRegistry.of(config)

    class Configuration {
        var times = 4
        var durationInMillis = 4_000L
    }

    companion object Plugin : BaseApplicationPlugin<ApplicationCallPipeline, Configuration, RateLimitPlugin> {
        override val key = AttributeKey<RateLimitPlugin>(RateLimitPlugin::class.jvmName)

        override fun install(pipeline: ApplicationCallPipeline, configure: Configuration.() -> Unit): RateLimitPlugin {
            val configuration = Configuration().apply(configure)

            return RateLimitPlugin(configuration)
        }
    }
}
