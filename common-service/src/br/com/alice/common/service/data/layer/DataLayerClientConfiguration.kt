package br.com.alice.common.service.data.layer

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.rfc.HttpInvoker
import com.typesafe.config.ConfigFactory
import io.ktor.client.HttpClient
import io.ktor.server.config.HoconApplicationConfig

object DataLayerClientConfiguration : BaseConfig(
    config = HoconApplicationConfig(ConfigFactory.load("data_layer_client.conf"))
) {
    fun build(httpClient: HttpClient = DefaultHttpClient()) = HttpInvoker(httpClient, baseUrl = baseUrl(), asyncBaseUrl = asyncBaseUrl())

    private fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    private fun baseUrl() = config.property("${environment().value.lowercase()}.baseUrl").getString()

    private fun asyncBaseUrl() = config.property("${environment().value.lowercase()}.async.baseUrl").getString()
}
