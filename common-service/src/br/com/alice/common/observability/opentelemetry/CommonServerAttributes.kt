package br.com.alice.common.observability.opentelemetry

object CommonServerAttributes {
    val gitHash = System.getenv("GIT_HASH") ?: "missing-key"
    val environmentName = System.getenv("ENVIRONMENT") ?: "missing-environment"
    val isStaging = "staging".equals(System.getenv("ENVIRONMENT"), ignoreCase = true)
    val serviceName = System.getenv("SERVICE") ?: "missing-service-name"
    val serviceSquad = System.getenv("SQUAD") ?: "squad-unknown"

    fun serviceType(): String {
        val envServiceType = System.getenv("SERVICE_TYPE")
        if (envServiceType != null) {
            return envServiceType
        }

        return when {
            isDomainService() -> "domain-service"
            isBffApi() -> "bff-api"
            isDataLayer() -> "data-layer"
            else -> "service-unknown"
        }
    }

    private fun isDomainService(): Boolean {
        val domainRegex = "^.*domain-service*".toRegex()
        val searched = domainRegex.find(serviceName.lowercase())

        return searched?.value.toBoolean()
    }

    private fun isBffApi(): Boolean {
        val bffRegex = "^.*api*".toRegex()
        val searched = bffRegex.find(serviceName.lowercase())

        return searched?.value.toBoolean()
    }

    private fun isDataLayer(): Boolean {
        val dlRegex = "^.*data-layer*".toRegex()
        val searched = dlRegex.find(serviceName.lowercase())

        return searched?.value.toBoolean()
    }
}
