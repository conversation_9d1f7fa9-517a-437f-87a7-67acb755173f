package br.com.alice.common.observability.opentelemetry

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.authToken
import br.com.alice.common.deserializeEvent
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.serialization.gson
import com.google.gson.reflect.TypeToken
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.isSuccess
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.ApplicationCallPipeline
import io.ktor.server.application.BaseApplicationPlugin
import io.ktor.server.application.call
import io.ktor.server.plugins.origin
import io.ktor.server.request.ApplicationRequest
import io.ktor.server.request.httpMethod
import io.ktor.server.request.path
import io.ktor.server.routing.Routing
import io.ktor.util.AttributeKey
import io.ktor.util.pipeline.PipelinePhase
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.api.trace.StatusCode
import io.opentelemetry.context.Context
import java.util.Base64

class OpenTelemetryServer(
    private val configuration: Configuration
) {
    class Configuration(var serverAttributes: Map<String, String> = emptyMap(), var filter: (ApplicationCall) -> Boolean = defaultFilter) {
        companion object {
            val defaultFilter: (ApplicationCall) -> Boolean = {
                it.request.path().startsWith("/")
                        && it.request.path() != "/"
                        && it.request.httpMethod !=  HttpMethod.Options
            }
        }
        fun build() = this
    }

    companion object Plugin : BaseApplicationPlugin<Application, Configuration, OpenTelemetryServer> {
        override val key: AttributeKey<OpenTelemetryServer> = AttributeKey("OpenTelemetryFeature")

        private val tracingRouteKey = AttributeKey<String>("tracingRoute")
        private val userTypeTokenGson = object: TypeToken<Map<String, Any>>(){}.type

        override fun install(pipeline: Application, configure: Configuration.() -> Unit): OpenTelemetryServer {
            val configuration = Configuration().apply(configure).build()
            val feature = OpenTelemetryServer(configuration)

            val loggingPhase = PipelinePhase("Logging")
            pipeline.insertPhaseBefore(ApplicationCallPipeline.Monitoring, loggingPhase)

            pipeline.intercept(loggingPhase) {
                if (feature.configuration.filter(call)) {
                    val spanName = with(call.request) { "${httpMethod.value} - ${path()}" }
                    val attributes = feature.buildServerAttributes(call).plus(configuration.serverAttributes)

                    extractContext(call).makeCurrent().use {
                        Tracer.initiateTracingContext {
                            Tracer.span(spanName, SpanKind.SERVER) { span ->
                                attributes.forEach { (key, value) -> span.setAttribute(key, value) }
                                try {
                                    proceed()
                                } catch (e: Throwable) {
                                    span.recordException(e)
                                    throw e
                                } finally {
                                    setResponse(span, call)
                                }
                            }
                        }
                    }
                }
            }

            pipeline.environment.monitor.subscribe(Routing.RoutingCallStarted) { call ->
                val routeTemplate = call.route.parent.toString()
                val route = if (routeTemplate.startsWith("/data") || routeTemplate.startsWith("/rfc"))
                    call.request.path()
                else
                    routeTemplate
                call.attributes.put(tracingRouteKey, route)
            }

            return feature
        }

        private fun setResponse(span: Span, call: ApplicationCall) {
            val statusCode = call.response.status()

            val statusCodeLong: Long = statusCode?.value?.toLong() ?: 0L
            span.setAttribute("http.status_code", statusCodeLong)
            span.setAttribute("http.route", call.attributes.getOrNull(tracingRouteKey) ?: "n/a")

            val spanStatus = if (statusCode != null && statusCode.isSuccess()) StatusCode.OK else StatusCode.ERROR
            span.setStatus(spanStatus)
        }

        private suspend fun extractContext(call: ApplicationCall): Context =
            if (call.request.path().contains("/notification_subscribers/")) {
                val event = deserializeEvent<NotificationEvent<Any>, Any>(call)
                ClientContextInjector.extractContext(event?.openTelemetryInfo)
            }
            else
                ClientContextInjector.extractContext(call)
    }

    private fun buildServerAttributes(call: ApplicationCall): Map<String, String> {

        val request = call.request
        return mapOf(
            "service_type" to "http_server",
            "http.path" to request.path(),
            "http.method" to request.httpMethod.value,
            "http.flavor" to call.request.origin.version,
            "http.target" to request.origin.uri,
            "http.host" to request.origin.serverHost,
            "http.host.port" to request.origin.serverPort.toString(),
            "http.scheme" to request.origin.scheme,
            "http.client_ip" to request.origin.remoteHost,
            "http.user_agent" to request.headers[HttpHeaders.UserAgent].toString(),
            "net.host.name" to request.origin.serverHost.substringBefore(":"),
            "ots.user_id" to request.getUserID()
        )
    }

    private fun ApplicationRequest.getUserID() = runCatching {
        val payload = this.authToken()?.split(".")?.get(1)

        Base64.getDecoder()
            .decode(payload)
            .toString(Charsets.UTF_8)
            .let {
                val decoded : Map<String, Any> = gson.fromJson(it, userTypeTokenGson)

                decoded[Authenticator.USER_ID_KEY].toString()
            }
    }.getOrNull().orEmpty()
}
