package br.com.alice.provider.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.ProviderTestCode
import br.com.alice.data.layer.models.ProviderTestCodeModel

object ProviderTestCodeConverter : Converter<ProviderTestCodeModel, ProviderTestCode>(
    ProviderTestCodeModel::class,
    ProviderTestCode::class,
)

fun ProviderTestCode.toModel() = ProviderTestCodeConverter.unconvert(this)
fun ProviderTestCodeModel.toTransport() = ProviderTestCodeConverter.convert(this)
