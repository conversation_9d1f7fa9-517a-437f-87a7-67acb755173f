package br.com.alice.provider.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.TestCodePackage
import br.com.alice.data.layer.models.TestCodePackageModel

object TestCodePackageConverter : Converter<TestCodePackageModel, TestCodePackage>(
    TestCodePackageModel::class,
    TestCodePackage::class,
)

fun TestCodePackage.toModel() = TestCodePackageConverter.unconvert(this)
fun TestCodePackageModel.toTransport() = TestCodePackageConverter.convert(this)
