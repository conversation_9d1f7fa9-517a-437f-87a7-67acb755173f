package br.com.alice.provider.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyModel

object MedicalSpecialtyConverter : Converter<MedicalSpecialtyModel, MedicalSpecialty>(
    MedicalSpecialtyModel::class,
    MedicalSpecialty::class,
)

fun MedicalSpecialty.toModel() = MedicalSpecialtyConverter.unconvert(this)
fun MedicalSpecialtyModel.toTransport() = MedicalSpecialtyConverter.convert(this)
