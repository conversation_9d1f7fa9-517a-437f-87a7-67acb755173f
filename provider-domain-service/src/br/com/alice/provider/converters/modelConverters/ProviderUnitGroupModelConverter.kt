package br.com.alice.provider.converters.modelConverters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.ProviderUnitGroup
import br.com.alice.data.layer.models.ProviderUnitGroupModel

object ProviderUnitGroupModelConverter: Converter<ProviderUnitGroupModel, ProviderUnitGroup>(
    ProviderUnitGroupModel::class,
    ProviderUnitGroup::class
)

fun ProviderUnitGroup.toModel() = ProviderUnitGroupModelConverter.unconvert(this)
fun ProviderUnitGroupModel.toTransport() = ProviderUnitGroupModelConverter.convert(this)
