package br.com.alice.provider.routes

import br.com.alice.common.coHandler
import br.com.alice.provider.controllers.RecurringController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.recurringRoutes() {

    val recurringController by inject<RecurringController>()

    route("/recurring_subscribers") {
        post("/set_provider_units_cnes") { coHandler(recurringController::setProviderUnitsCnes) }
    }
}
