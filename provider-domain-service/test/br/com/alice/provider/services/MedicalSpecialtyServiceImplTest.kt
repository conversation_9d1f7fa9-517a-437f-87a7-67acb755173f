package br.com.alice.provider.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.services.MedicalSpecialtyModelDataService
import br.com.alice.provider.client.Filter
import br.com.alice.provider.converters.toModel
import br.com.alice.provider.model.MedicalSpecialtyCreatedEvent
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MedicalSpecialtyServiceTest {
    @AfterTest
    fun setup() = clearAllMocks()

    private val data: MedicalSpecialtyModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = MedicalSpecialtyServiceImpl(
        data,
        kafkaProducerService,
    )

    @Test
    fun `#update should remove parentSpecialtyId for specialty type `() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            type = SPECIALTY,
            parentSpecialtyId = RangeUUID.generate()
        )
        val expected = specialty.copy(parentSpecialtyId = null)
        val expectedModel = expected.toModel()

        coEvery {
            data.update(match { it == expectedModel })
        } returns expectedModel.success()
        coEvery {
            kafkaProducerService.produce(any())
        } returns mockk()

        val result = service.update(specialty)

        assertThat(result).isEqualTo(expected.success())

        coVerifyOnce {
            data.update(any())
            kafkaProducerService.produce(any())
        }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count should fetch all MedicalSpecialty`() = runBlocking<Unit> {
        coEvery {
            data.count(queryEq {
                all()
            })
        } returns 1.success()

        val resultCount = service.count()

        assertThat(resultCount).isEqualTo(1.success())
    }

    @Test
    fun `#getById should fetch MedicalSpecialty by ID`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()
        coEvery { data.get(specialty.id) } returns specialtyModel.success()

        val result = service.getById(specialty.id)

        assertThat(result).isEqualTo(specialty.success())
        coVerify(exactly = 1) { data.get(specialty.id) }
    }

    @Test
    fun `#getByIds should fetch a list of MedicalSpecialties`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val anotherSpecialty = TestModelFactory.buildMedicalSpecialty().copy(type = SPECIALTY)

        val specialties = listOf(specialty, anotherSpecialty)
        val specialtyModels = specialties.map { it.toModel() }
        val specialtyIds = specialties.map { it.id }

        coEvery {
            data.find(queryEq {
                where { id.inList(specialtyIds) }
            })
        } returns specialtyModels.success()

        val result = service.getByIds(specialtyIds)

        assertThat(result).isEqualTo(specialties.success())
        coVerify(exactly = 1) { data.find(any()) }
    }

    @Test
    fun `#getByParentId should fetch a specialties by parent`() = runBlocking {
        val parentSpecialtyId = RangeUUID.generate()
        val specialties = listOf(
            TestModelFactory.buildMedicalSpecialty(parentSpecialtyId = parentSpecialtyId),
            TestModelFactory.buildMedicalSpecialty(parentSpecialtyId = parentSpecialtyId)
        )
        val specialtyModels = specialties.map { it.toModel() }

        coEvery {
            data.find(queryEq {
                where { this.parentSpecialtyId.eq(parentSpecialtyId) }.orderBy { name }.sortOrder { asc }
            })
        } returns specialtyModels.success()

        val result = service.getByParentId(parentSpecialtyId)

        assertThat(result).isEqualTo(specialties.success())
        coVerify(exactly = 1) { data.find(any()) }
    }

    @Test
    fun `#getByName should fetch a specialties by name`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialtyModels = specialties.map { it.toModel() }
        val specialty = specialties[0]
        val specialtyModel = specialtyModels[0]

        coEvery {
            data.find(queryEq {
                where {
                    type.eq(specialtyModel.type) and (name.like(specialty.name))
                }.orderBy { name }.sortOrder { asc }
            })
        } returns specialtyModels.success()

        val result = service.getByName(specialty.name, specialty.type)

        assertThat(result).isEqualTo(specialties.success())
        coVerify(exactly = 1) { data.find(any()) }
    }

    @Test
    fun `#findBy should fetch a specialties by name and type`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialtyModels = specialties.map { it.toModel() }
        val specialty = specialties[0]
        val specialtyModel = specialtyModels[0]
        val filter = Filter(name = specialty.name, types = listOf(specialty.type))
        val range = IntRange(0, 10)
        coEvery {
            data.find(queryEq {
                where {
                    name.like(specialty.name) and type.inList(listOf(specialtyModel.type))
                }.offset { range.first }.limit { range.count() }.orderBy { name }.sortOrder { asc }
            })
        } returns specialtyModels.success()

        val result = service.findBy(filter, range)

        assertThat(result).isEqualTo(specialties.success())
        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findBy should fetch a specialties by name and isAdvancedAccess`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialtyModels = specialties.map { it.toModel() }
        val specialty = specialties[0]
        val filter = Filter(name = specialty.name, isAdvancedAccess = true)
        val range = IntRange(0, 10)
        coEvery {
            data.find(queryEq {
                where {
                    name.like(specialty.name) and isAdvancedAccess.eq(true)
                }.offset { range.first }.limit { range.count() }.orderBy { name }.sortOrder { asc }
            })
        } returns specialtyModels.success()

        val result = service.findBy(filter, range)

        assertThat(result).isEqualTo(specialties.success())
        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#countBy should fetch a specialties by name and type`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialty = specialties[0]
        val specialtyModel = specialty.toModel()
        val filter = Filter(name = specialty.name, types = listOf(specialty.type))
        coEvery {
            data.count(queryEq {
                where {
                    name.like(specialty.name) and type.inList(listOf(specialtyModel.type))
                }
            })
        } returns 1.success()

        val result = service.countBy(filter)

        assertThat(result).isEqualTo(1.success())
        coVerifyOnce { data.count(any()) }
    }

    @Test
    fun `#countBy should fetch a specialties by name and isAdvancedAccess`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialty = specialties[0]
        val filter = Filter(name = specialty.name, isAdvancedAccess = true)
        coEvery {
            data.count(queryEq {
                where {
                    name.like(specialty.name) and isAdvancedAccess.eq(true)
                }
            })
        } returns 1.success()

        val result = service.countBy(filter)

        assertThat(result).isEqualTo(1.success())
        coVerifyOnce { data.count(any()) }
    }

    @Test
    fun `#getByType should fetch a specialties by type`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialtyModels = specialties.map { it.toModel() }
        val specialty = specialties[0]
        val specialtyModel = specialtyModels[0]

        coEvery {
            data.find(queryEq {
                where {
                    type.eq(specialtyModel.type)
                }
            })
        } returns specialtyModels.success()

        val result = service.getByType(specialty.type)

        assertThat(result).isEqualTo(specialties.success())
        coVerify(exactly = 1) { data.find(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#getAll should fetch all MedicalSpecialties`() = runBlocking {
        val specialties = listOf(TestModelFactory.buildMedicalSpecialty())
        val specialtyModels = specialties.map { it.toModel() }

        coEvery {
            data.find(queryEq {
                all()
            })
        } returns specialtyModels.success()

        val result = service.getAll()

        assertThat(result).isEqualTo(specialties.success())
        coVerify(exactly = 1) { data.find(any()) }
    }

    @Test
    fun `#add sets parentSpecialtyId as null when type is SPECIALTY`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            type = SPECIALTY,
            parentSpecialtyId = RangeUUID.generate()
        )
        val expected = specialty.copy(parentSpecialtyId = null)
        val expectedModel = expected.toModel()

        coEvery {
            data.add(match { it == expectedModel })
        } returns expectedModel.success()
        coEvery {
            kafkaProducerService.produce(
                MedicalSpecialtyCreatedEvent(
                    medicalSpecialty = expected
                )
            )
        } returns mockk()

        val result = service.add(specialty)

        assertThat(result).isEqualTo(expected.success())

        coVerify(exactly = 1) {
            data.add(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#add calls data service with unmodified model when type is SUBSPECIALTY`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            type = SUBSPECIALTY,
            parentSpecialtyId = RangeUUID.generate()
        )
        val specialtyModel = specialty.toModel()

        coEvery { data.add(specialtyModel) } returns specialtyModel.success()
        coEvery {
            kafkaProducerService.produce(
                MedicalSpecialtyCreatedEvent(specialty)
            )
        } returns mockk()

        val result = service.add(specialty)

        assertThat(result).isEqualTo(specialty.success())

        coVerify(exactly = 1) {
            data.add(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#getByExactlyName should execute the correct query`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()

        coEvery {
            data.findOne(
                queryEq {
                    where { this.type.eq(MedicalSpecialtyType.SPECIALTY) and (this.name.eq(specialty.name)) }
                }
            )
        } returns specialtyModel.success()

        service.getByExactlyName(specialty.name, SPECIALTY)

        coVerify(exactly = 1) { data.findOne(any()) }
    }

    @Test
    fun `#getActives executes the correct query`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()

        coEvery {
            data.find(
                queryEq {
                    where { this.active.eq(true) }
                }
            )
        } returns listOf(specialtyModel).success()

        val result = service.getActives()

        assertThat(result).isSuccessWithData(listOf(specialty))

        coVerify(exactly = 1) { data.find(any()) }
    }

    @Test
    fun `#getActivesByType return all active specialty by type`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()

        coEvery {
            data.find(queryEq {
                where { active.eq(true) and this.type.eq(MedicalSpecialtyType.SPECIALTY) }.orderBy { name }.sortOrder { asc }
            })
        } returns listOf(specialtyModel).success()

        val result = service.getActivesByType(SPECIALTY)

        assertThat(result).isSuccessWithData(listOf(specialty))
    }

    @Test
    fun `#getActivesByType return active specialty by type including internals`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()
        coEvery {
            data.find(queryEq {
                where { active.eq(true) and this.type.eq(MedicalSpecialtyType.SPECIALTY) }.orderBy { name }.sortOrder { asc }
            })
        } returns listOf(specialtyModel).success()

        val result = service.getActivesByType(SPECIALTY, false)

        assertThat(result).isSuccessWithData(listOf(specialty))
    }

    @Test
    fun `#getActivesByType return active specialty by type excluding internals`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val specialtyModel = specialty.toModel()
        coEvery {
            data.find(queryEq {
                where { active.eq(true) and this.type.eq(MedicalSpecialtyType.SPECIALTY) and this.internal.eq(false) }.orderBy { name }
                    .sortOrder { asc }
            })
        } returns listOf(specialtyModel).success()

        val result = service.getActivesByType(SPECIALTY, true)

        assertThat(result).isSuccessWithData(listOf(specialty))
    }

    @Test
    fun `#getActivesByUrlSlugs return active specialties by urlSlug`() = runBlocking {
        val specialties = listOf(
            TestModelFactory.buildMedicalSpecialty().copy(urlSlug = "specialty_0"),
            TestModelFactory.buildMedicalSpecialty().copy(urlSlug = "specialty_1")
        )
        val specialtyModels = specialties.map { it.toModel() }
        coEvery {
            data.find(queryEq {
                where { active.eq(true) and urlSlug.inList(listOf("specialty_0", "specialty_1")) }
            })
        } returns specialtyModels.success()

        val result = service.getActivesByUrlSlugs(listOf("specialty_0", "specialty_1"))

        assertThat(result).isSuccessWithData(specialties)
    }

    @Test
    fun `#getTherapeuticSpecialists executes the correct query`() = runBlocking {
        val specialty = TestModelFactory.buildMedicalSpecialty(isTherapy = true)
        val specialtyModel = specialty.toModel()

        coEvery {
            data.find(
                queryEq {
                    where {
                        this.isTherapy.eq(true)
                    }
                }
            )
        } returns listOf(specialtyModel).success()

        val result = service.getTherapeuticSpecialties()

        assertThat(result).isSuccessWithData(listOf(specialty))

        coVerifyOnce { data.find(any()) }
    }
}
