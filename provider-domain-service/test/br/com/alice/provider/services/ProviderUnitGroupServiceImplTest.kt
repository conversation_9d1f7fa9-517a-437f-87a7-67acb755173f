package br.com.alice.provider.services

import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ProviderUnitGroupModelDataService
import br.com.alice.provider.converters.modelConverters.toModel
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class ProviderUnitGroupServiceImplTest {

    @AfterTest
    fun setup() = clearAllMocks()

    private val ProviderUnitGroupModelDataService: ProviderUnitGroupModelDataService = mockk()
    private val providerUnitGroupServiceImpl = ProviderUnitGroupServiceImpl(ProviderUnitGroupModelDataService)


    private val expectedProviderUnitGroup = TestModelFactory.buildProviderUnitGroup()
    private val expectedProviderUnitGroups = listOf(TestModelFactory.buildProviderUnitGroup())


    @Test
    fun `#add should create provider successfuly`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.add(any())
        } returns expectedProviderUnitGroup.toModel().success()

        val providerUnitGroup = providerUnitGroupServiceImpl.add(expectedProviderUnitGroup)

        assertThat(providerUnitGroup).isEqualTo(expectedProviderUnitGroup.success())
    }

    @Test
    fun `#update should update providerUnitGroup successfuly`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.update(any())
        } returns expectedProviderUnitGroup.toModel().success()

        val providerUnitGroup = providerUnitGroupServiceImpl.update(expectedProviderUnitGroup)

        assertThat(providerUnitGroup).isEqualTo(expectedProviderUnitGroup.success())
    }

    @Test
    fun `#get should get providerUnitGroup successfuly`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.get(expectedProviderUnitGroup.id)
        } returns expectedProviderUnitGroup.toModel().success()

        val providerUnitGroup = providerUnitGroupServiceImpl.get(expectedProviderUnitGroup.id)

        assertThat(providerUnitGroup).isEqualTo(expectedProviderUnitGroup.success())
    }

    @Test
    fun `#getByTitle should get providerUnitGroup by name successfuly`() = runBlocking<Unit> {
        val range = IntRange(0, 5)
        coEvery {
            ProviderUnitGroupModelDataService.find(queryEq {
                where { title.like(expectedProviderUnitGroup.title) }.offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(expectedProviderUnitGroup.toModel()).success()

        val providerUnitGroup =
            providerUnitGroupServiceImpl.getByTitle(range, expectedProviderUnitGroup.title)

        assertThat(providerUnitGroup).isEqualTo(listOf(expectedProviderUnitGroup).success())
    }

    @Test
    fun `#countAll should count providerUnitGroups successfuly`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.count(queryEq {
                all()
            })
        } returns 1.success()

        val count = providerUnitGroupServiceImpl.countAll()

        assertThat(count).isEqualTo(1.success())
    }

    @Test
    fun `#countByTitle should count providerUnitGroups successfuly`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.count(queryEq {
                where { title.like(expectedProviderUnitGroup.title) }
            })
        } returns 1.success()

        val count = providerUnitGroupServiceImpl.countByTitle(expectedProviderUnitGroup.title)

        assertThat(count).isEqualTo(1.success())
    }

    @Test
    fun `#getAll should call data function with expected parameters`() = runBlocking<Unit> {
        coEvery {
            ProviderUnitGroupModelDataService.find(queryEq {
                all().orderBy { title }.orderBy { title }.sortOrder { asc }.limit { 50 }
            })
        } returns expectedProviderUnitGroups.map { it.toModel() }.success()

        val providerUnitGroups = providerUnitGroupServiceImpl.getAll().get()

        assertThat(providerUnitGroups).isEqualTo(expectedProviderUnitGroups)
    }

}
