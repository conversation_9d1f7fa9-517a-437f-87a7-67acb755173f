package br.com.alice.provider.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.PartnerIntegrationProviderType
import br.com.alice.data.layer.models.PartnerIntegrationProviderUnit
import br.com.alice.data.layer.services.PartnerIntegrationProviderUnitModelDataService
import br.com.alice.provider.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PartnerIntegrationProviderUnitServiceImplTest {

    private val partnerIntegrationDataService: PartnerIntegrationProviderUnitModelDataService = mockk()

    private val service = PartnerIntegrationProviderUnitServiceImpl(
        partnerIntegrationDataService
    )

    private val partnerIntegration = PartnerIntegrationProviderUnit(
        providerType = PartnerIntegrationProviderType.LIVANCE,
        providerUnitId = RangeUUID.generate(),
        externalId = "1234"
    )
    private val partnerIntegrationModel = partnerIntegration.toModel()


    @Test
    fun `#get - should return partner integration`() = runBlocking {
        coEvery { partnerIntegrationDataService.get(partnerIntegration.id) } returns partnerIntegrationModel.success()

        val result = service.get(partnerIntegration.id)

        assertThat(result).isSuccessWithData(partnerIntegration)

        coVerifyOnce { partnerIntegrationDataService.get(any()) }
    }

    @Test
    fun `#getByProviderUnitIdAndProviderType - should return partner integration`() = runBlocking {
        coEvery {
            partnerIntegrationDataService.findOne(queryEq {
                where {
                    this.providerUnitId.eq(partnerIntegration.providerUnitId) and
                            this.providerType.eq(partnerIntegration.providerType)
                }
            })
        } returns partnerIntegrationModel.success()

        val result = service.getByProviderUnitIdAndProviderType(
            providerType = partnerIntegration.providerType,
            providerUnitId = partnerIntegration.providerUnitId,
        )

        assertThat(result).isSuccessWithData(partnerIntegration)

        coVerifyOnce { partnerIntegrationDataService.findOne(any()) }
    }

    @Test
    fun `#create - should create partner integration`() = runBlocking {
        coEvery { partnerIntegrationDataService.add(partnerIntegrationModel) } returns partnerIntegrationModel.success()

        val result = service.create(partnerIntegration)

        assertThat(result).isSuccessWithData(partnerIntegration)

        coVerifyOnce { partnerIntegrationDataService.add(any()) }
    }

    @Test
    fun `#update - should update partner integration`() = runBlocking {
        coEvery { partnerIntegrationDataService.update(partnerIntegrationModel) } returns partnerIntegrationModel.success()

        val result = service.update(partnerIntegration)

        assertThat(result).isSuccessWithData(partnerIntegration)

        coVerifyOnce { partnerIntegrationDataService.update(any()) }
    }

    @Test
    fun `#delete - should delete partner integration`() = runBlocking {
        coEvery { partnerIntegrationDataService.delete(partnerIntegrationModel) } returns true.success()

        val result = service.delete(partnerIntegration)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { partnerIntegrationDataService.delete(any()) }
    }

}
