plugins {
    kotlin
    `kotlin-kapt`
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.einstein-integration-service"
version = aliceEinsteinIntegrationServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:einstein-integration-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:test-result-domain-service-data-package"))
	implementation(project(":data-packages:einstein-integration-service-data-package"))
    implementation(project(":data-packages:fhir-domain-service-data-package"))
    implementation(project(":ehr-domain-client"))
    implementation(project(":einstein-integration-client"))

    kapt(project(":common"))

    ktor2Dependencies()

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
