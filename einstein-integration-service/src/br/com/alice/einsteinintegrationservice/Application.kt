package br.com.alice.einsteinintegrationservice

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.EINSTEIN_INTEGRATION_SERVICE_ROOT_SERVICE_NAME
import br.com.alice.data.layer.services.*
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.einsteinintegrationclient.SERVICE_NAME
import br.com.alice.einsteinintegrationclient.client.EinsteinAlergiaService
import br.com.alice.einsteinintegrationclient.client.EinsteinAppointmentService
import br.com.alice.einsteinintegrationclient.client.EinsteinAtendimentoService
import br.com.alice.einsteinintegrationclient.client.EinsteinAvaliacaoInicialService
import br.com.alice.einsteinintegrationclient.client.EinsteinClientService
import br.com.alice.einsteinintegrationclient.client.EinsteinDadosDeAltaService
import br.com.alice.einsteinintegrationclient.client.EinsteinDiagnosticoService
import br.com.alice.einsteinintegrationclient.client.EinsteinEncaminhamentoService
import br.com.alice.einsteinintegrationclient.client.EinsteinMedicamentoService
import br.com.alice.einsteinintegrationclient.client.EinsteinProcedimentoService
import br.com.alice.einsteinintegrationclient.client.EinsteinResultadoExameService
import br.com.alice.einsteinintegrationclient.client.EinsteinResumoInternacaoService
import br.com.alice.einsteinintegrationclient.client.EinsteinStructuredTestResultService
import br.com.alice.einsteinintegrationservice.clients.EinsteinAuthClient
import br.com.alice.einsteinintegrationservice.clients.EinsteinClient
import br.com.alice.einsteinintegrationservice.consumers.EinsteinTestResultCreatedConsumer
import br.com.alice.einsteinintegrationservice.routes.kafkaRoutes
import br.com.alice.einsteinintegrationservice.services.EinsteinAlergiaServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinAppointmentServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinAtendimentoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinAvaliacaoInicialServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinClientServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinDadosDeAltaServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinDiagnosticoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinEncaminhamentoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinMedicamentoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinProcedimentoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinResultadoExameServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinResumoInternacaoServiceImpl
import br.com.alice.einsteinintegrationservice.services.EinsteinStructuredTestResultServiceImpl
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        EhrDomainClientModule,
        KafkaProducerModule,

        module(createdAtStart = true) {
            // Configuration
            single { ServiceConfig.config }

            // Clients
            single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }
            single { EinsteinAuthClient() }
            single { EinsteinClient() }

            // Data Services
            single<EinsteinAlergiaDataService> { EinsteinAlergiaDataServiceClient(get()) }
            single<EinsteinAvaliacaoInicialDataService> { EinsteinAvaliacaoInicialDataServiceClient(get()) }
            single<EinsteinAtendimentoDataService> { EinsteinAtendimentoDataServiceClient(get()) }
            single<EinsteinEncaminhamentoDataService> { EinsteinEncaminhamentoDataServiceClient(get()) }
            single<EinsteinDadosDeAltaDataService> { EinsteinDadosDeAltaDataServiceClient(get()) }
            single<EinsteinMedicamentoDataService> { EinsteinMedicamentoDataServiceClient(get()) }
            single<EinsteinProcedimentoDataService> { EinsteinProcedimentoDataServiceClient(get()) }
            single<EinsteinResultadoExameDataService> { EinsteinResultadoExameDataServiceClient(get()) }
            single<EinsteinResumoInternacaoDataService> { EinsteinResumoInternacaoDataServiceClient(get()) }
            single<EinsteinDiagnosticoDataService> { EinsteinDiagnosticoDataServiceClient(get()) }
            single<EinsteinStructuredTestResultDataService> { EinsteinStructuredTestResultDataServiceClient(get()) }

            // Exposed Services
            single<EinsteinAlergiaService> { EinsteinAlergiaServiceImpl(get()) }
            single<EinsteinAvaliacaoInicialService> { EinsteinAvaliacaoInicialServiceImpl(get()) }
            single<EinsteinAtendimentoService> { EinsteinAtendimentoServiceImpl(get(), get()) }
            single<EinsteinEncaminhamentoService> { EinsteinEncaminhamentoServiceImpl(get()) }
            single<EinsteinDadosDeAltaService> { EinsteinDadosDeAltaServiceImpl(get(), get()) }
            single<EinsteinMedicamentoService> { EinsteinMedicamentoServiceImpl(get()) }
            single<EinsteinProcedimentoService> { EinsteinProcedimentoServiceImpl(get()) }
            single<EinsteinResultadoExameService> { EinsteinResultadoExameServiceImpl(get()) }
            single<EinsteinResumoInternacaoService> { EinsteinResumoInternacaoServiceImpl(get()) }
            single<EinsteinDiagnosticoService> { EinsteinDiagnosticoServiceImpl(get()) }
            single<EinsteinAppointmentService> { EinsteinAppointmentServiceImpl(get(), get(), get(), get(), get(), get(), get()) }
            single<EinsteinStructuredTestResultService> { EinsteinStructuredTestResultServiceImpl(get()) }
            single<EinsteinClientService> { EinsteinClientServiceImpl(get(), get()) }

            loadServiceServers("br.com.alice.einsteinintegrationclient.services")

            // Controllers
            single { HealthController(EINSTEIN_INTEGRATION_SERVICE_ROOT_SERVICE_NAME) }

            // Kafka Consumers
            single { EinsteinTestResultCreatedConsumer(get()) }

        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication){
            <EMAIL>()
            firebase()
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, EINSTEIN_INTEGRATION_SERVICE_ROOT_SERVICE_NAME)
        }

        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }
    }
}
