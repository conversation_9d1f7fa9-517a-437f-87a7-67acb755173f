package br.com.alice.api.channel.controllers

import br.com.alice.api.channel.ControllerTestHelper
import br.com.alice.api.channel.models.ChannelAdministrativeStatusRequest
import br.com.alice.api.channel.models.ChannelArchiveRequest
import br.com.alice.api.channel.models.ChannelAssessmentRequest
import br.com.alice.api.channel.models.ChannelClassificationRequest
import br.com.alice.api.channel.models.ChannelCreateRequest
import br.com.alice.api.channel.models.ChannelDischargeRequest
import br.com.alice.api.channel.models.ChannelHighlightChannelRequest
import br.com.alice.api.channel.models.ChannelMacroResponse
import br.com.alice.api.channel.models.ChannelRenameRequest
import br.com.alice.api.channel.models.ChannelResponse
import br.com.alice.api.channel.models.ChannelStaffInfoRequest
import br.com.alice.api.channel.models.ChannelTagResponse
import br.com.alice.api.channel.models.ChannelTagsRequest
import br.com.alice.api.channel.models.ChannelsZendeskAssignmentTag
import br.com.alice.api.channel.models.ChannelsZendeskAssignmentTagsResponse
import br.com.alice.api.channel.models.ChannelsZendeskTicketRequest
import br.com.alice.api.channel.models.ReRoutingRequest
import br.com.alice.channel.client.ChannelAdministrativeService
import br.com.alice.channel.client.ChannelClassifyService
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.client.FollowUpService
import br.com.alice.channel.client.MacroService
import br.com.alice.channel.client.ReRoutingExtraInfo
import br.com.alice.channel.client.RoutingDashboardResponse
import br.com.alice.channel.client.TagService
import br.com.alice.channel.models.ChannelClassifyResponse
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.ScheduledAutomaticFollowUpResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AdministrativeStatus
import br.com.alice.data.layer.models.ChannelAssessment
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelMacro
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelTag
import br.com.alice.data.layer.models.ChannelTheme
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.ChannelsZendeskTag
import br.com.alice.staff.client.StaffService
import br.com.alice.zendesk.client.ChannelsZendeskTagService
import br.com.alice.zendesk.client.ZendeskTicketService
import br.com.alice.zendesk.transport.ChannelsZendeskTicketData
import br.com.alice.zendesk.transport.ZendeskTicket
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelControllerTest : ControllerTestHelper() {

    private val staffService: StaffService = mockk()
    private val channelService: ChannelService = mockk()
    private val followUpService: FollowUpService = mockk()
    private val macroService: MacroService = mockk()
    private val tagService: TagService = mockk()
    private val zendeskTicketService: ZendeskTicketService = mockk()
    private val channelsZendeskTagService: ChannelsZendeskTagService = mockk()
    private val channelClassifyService: ChannelClassifyService = mockk()
    private val channelAdministrativeService: ChannelAdministrativeService = mockk()
    private val channelController = ChannelController(
        staffService,
        channelService,
        followUpService,
        macroService,
        tagService,
        zendeskTicketService,
        channelsZendeskTagService,
        channelClassifyService,
        channelAdministrativeService
    )

    private val channelId = "channel_id"
    private val currentStaffId = staff.id.toString()
    private val newStaff = TestModelFactory.buildStaff()
    private val channelResponse = ChannelResponse(channelId)
    private val highlightChannelRequest = ChannelHighlightChannelRequest(staffId = currentStaffId)
    private val staffInfo = ChannelStaffInfo(
        id = newStaff.id.toString(),
        name = newStaff.firstName,
        firstName = newStaff.firstName,
        lastName = newStaff.lastName,
        description = "médico",
        profileImageUrl = newStaff.profileImageUrl.orEmpty(),
        roles = listOf(newStaff.role)
    )
    private val channel = ChannelDocument(
        name = "#channel-creation",
        personId = PersonId().toString(),
        channelPersonId = RangeUUID.generate().toString(),
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.ACUTE,
        staff = mutableMapOf(
            newStaff.id.toString() to staffInfo.copy(owner = true)
        ),
        tags = listOf("Tag 1", "Tag 2")
    )
    private val assessment = "EMERGENCY"
    private val request = ChannelAssessmentRequest(channelId, assessment)
    private val channelsZendeskRequest = ChannelsZendeskTicketRequest(
        personId = PersonId().toString(),
        zendeskAssignmentTag = "zendeskAssignmentTag",
        channelName = "channelName",
        channelTag = "channelTag",
        context = "context",
    )
    private val channelCreateRequest = ChannelCreateRequest(
        personId = channel.personId,
        channelPersonId = channel.channelPersonId,
        name = channel.name!!,
        tags = channel.tags,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.LONGITUDINAL,
        subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
        staff = channel.staff.map { (staffId, staffInfo) ->
            staffId to ChannelStaffInfoRequest(
                id = staffInfo.id,
                name = staffInfo.name,
                owner = staffInfo.owner,
                roles = staffInfo.roles
            )
        }.toMap()
    )

    private val channelResponseModel = br.com.alice.channel.models.ChannelResponse(
        id = channelId,
        channelPersonId = "channel_person_id",
        createdAt = LocalDateTime.of(2024, 1, 1, 0, 1),
        name = "test_channel",
        status = ChannelStatus.ACTIVE,
        type = ChannelType.ADMINISTRATIVE,
        tags = listOf("channel_tag_1", "channel_tag_2")
    )

    private val channelTheme = TestModelFactory.buildChannelTheme(
        channelId = channelId,
        staffId = staff.id
    )

    private val date = LocalDateTime.now()
    private val uuid = RangeUUID.generate()
    private val channelArchiveAdministrativeRequest = ChannelArchiveRequest(
        akinatorSetupId = RangeUUID.generate(),
        suggestedTheme = channelTheme.suggestedTheme,
        theme = channelTheme.theme,
        reason = channelTheme.reason,
        screenOpenedAt = date.toString(),
    )

    private val channelAdministrativeStatusRequest = ChannelAdministrativeStatusRequest(
        administrativeStatus = AdministrativeStatus.PENDING_MEMBER
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { channelController }
    }

    @AfterTest
    override fun after() = clearAllMocks()

    @AfterTest
    fun confirmMocks() = confirmVerified(
        staffService,
        channelService,
        followUpService,
        macroService,
        tagService
    )

    @Test
    fun `#setAssessment should set channel assessment`() {
        coEvery {
            channelService.setChannelAssessment(
                channelId = request.channelId,
                channelAssessment = ChannelAssessment.EMERGENCY
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            post("/channels/screening/assessment", body = request) { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce { channelService.setChannelAssessment(any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#setAssessment should not set channel assessment when assessment is invalid`() {
        val invalidRequest = ChannelAssessmentRequest(channelId, "invalid_assessment")

        authenticatedAs(idToken, staffTest) {
            post("/channels/screening/assessment", body = invalidRequest) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#setAssessment should not set channel assessment when occurs some exception`() {
        coEvery {
            channelService.setChannelAssessment(
                channelId = request.channelId,
                channelAssessment = ChannelAssessment.EMERGENCY
            )
        } returns Exception().failure()

        authenticatedAs(idToken, staffTest) {
            post("/channels/screening/assessment", body = request) { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerifyOnce { channelService.setChannelAssessment(any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getScheduledFollowUps should return scheduled FollowUps if any`() {
        val scheduledAutomaticFollowUp = ScheduledAutomaticFollowUpResponse(
            id = RangeUUID.generate(),
            title = "FUP Automático D+1",
            message = "Oi @nickname,<h4>Como você está se sentindo em relação à sua dor de cabeça?</h4>",
            dueDate = LocalDateTime.now().plusDays(1),
        )
        coEvery {
            followUpService.findScheduledFollowUps(channelId)
        } returns listOf(scheduledAutomaticFollowUp).success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/$channelId/follow_ups") { response ->
                assertThat(response).isOKWithData(listOf(scheduledAutomaticFollowUp))
            }
        }

        coVerifyOnce { followUpService.findScheduledFollowUps(any()) }
        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#rename should rename channel`() {
        val renameRequest = ChannelRenameRequest("newChannelName")

        coEvery {
            channelService.rename(channelId, renameRequest.name, staff.id.toString())
        } returns channelId.success()

        authenticatedWithRolesAs(idToken, staffTest, listOf(staff.role.name)) {
            put(to = "/channels/$channelId/rename", body = renameRequest) { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.rename(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#rename should return unauthorized`() {
        val renameRequest = ChannelRenameRequest("newChannelName")

        coEvery {
            channelService.rename(channelId, renameRequest.name, staff.id.toString())
        } returns AuthorizationException("Authorization failed for '${channelId}'").failure()

        authenticatedWithRolesAs(idToken, staffTest, listOf(staff.role.name)) {
            put(to = "/channels/$channelId/rename", body = renameRequest) { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.rename(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#create should create channel`() {
        val now = Timestamp.now()

        mockkStatic(Timestamp::class) {
            every { Timestamp.now() } returns now

            val expectedToCreate = ChannelDocument(
                personId = channelCreateRequest.personId,
                channelPersonId = channelCreateRequest.channelPersonId!!,
                name = channelCreateRequest.name,
                tags = channelCreateRequest.tags!!,
                kind = channelCreateRequest.kind,
                category = channelCreateRequest.category,
                subCategory = channelCreateRequest.subCategory,
                subCategoryClassifier = channelCreateRequest.subCategoryClassifier,
                staff = mutableMapOf(newStaff.id.toString() to staffInfo.copy(owner = true, lastSync = now))
            )

            coEvery { staffService.findByList(listOf(newStaff.id)) } returns listOf(newStaff).success()

            coEvery {
                channelService.add(expectedToCreate)
            } returns expectedToCreate.copy(id = channelId).success()

            authenticatedAs(idToken, staffTest) {
                post(to = "/channels", body = channelCreateRequest) { response ->
                    assertThat(response).isOKWithData(channelResponse)
                }
            }
        }

        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { channelService.add(any()) }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#create should return error on creation`() {
        coEvery { staffService.findByList(listOf(newStaff.id)) } returns listOf(newStaff).success()
        coEvery { channelService.add(any()) } returns IllegalArgumentException().failure()

        authenticatedAs(idToken, staffTest) {
            post(to = "/channels", body = channelCreateRequest) { response ->
                assertThat(response).isBadRequestWithErrorCode("illegal_argument")
            }
        }

        coVerifyOnce { staffService.findByList(any()) }
        coVerifyOnce { channelService.add(any()) }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#addStaff should add staff`() {
        coEvery {
            channelService.addStaff(
                channelId,
                newStaff.id.toString(),
                currentStaffId
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/add/${newStaff.id}") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#addStaff should return unauthorized`() {
        coEvery {
            channelService.addStaff(
                channelId,
                newStaff.id.toString(),
                currentStaffId
            )
        } returns AuthorizationException("Authorization failed for '${channelId}'").failure()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/add/${newStaff.id}") { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.addStaff(any(), any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#removeStaff should remove staff`() {
        coEvery {
            channelService.removeStaff(
                channelId,
                newStaff.id.toString(),
                currentStaffId
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/remove/${newStaff.id}") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.removeStaff(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#removeStaff should return forbidden`() {
        coEvery {
            channelService.removeStaff(channelId, newStaff.id.toString(), currentStaffId)
        } returns AccessForbiddenException("Authorization failed for '${channelId}'").failure()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/remove/${newStaff.id}") { response ->
                assertThat(response).isForbidden()
            }
        }

        coVerifyOnce { channelService.removeStaff(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#giveOwnership should give ownership to staff`() {
        coEvery {
            channelService.giveOwnership(channelId, staffInfo.id, currentStaffId)
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/give_ownership/${staffInfo.id}") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.giveOwnership(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#giveOwnership should return forbidden`() {
        coEvery {
            channelService.giveOwnership(channelId, staffInfo.id, currentStaffId)
        } returns AccessForbiddenException("Authorization failed for '${channelId}'").failure()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/give_ownership/${staffInfo.id}") { response ->
                assertThat(response).isForbidden()
            }
        }

        coVerifyOnce { channelService.giveOwnership(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#archive should archive channel`() {
        coEvery { channelService.archiveChannel(channelId, currentStaffId) } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/archive") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.archiveChannel(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#archive should return unauthorized`() {
        coEvery { channelService.archiveChannel(channelId, currentStaffId) } returns
                AuthorizationException("Authorization failed for '${channelId}'").failure()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/archive") { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.archiveChannel(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#archiveAdministrative should archive administrative channel`() {
        mockkStatic(LocalDateTime::class)
        mockkObject(RangeUUID)

        every { LocalDateTime.now() } returns date

        every { RangeUUID.generate() } returns uuid

        val channelTheme = ChannelTheme(
            id = uuid,
            channelId = channelId,
            staffId = staff.id,
            setupId = channelArchiveAdministrativeRequest.akinatorSetupId,
            suggestedTheme = channelArchiveAdministrativeRequest.suggestedTheme,
            theme = channelArchiveAdministrativeRequest.theme,
            reason = channelArchiveAdministrativeRequest.reason,
            screenOpenedAt = date,
            screenClosedAt = date,
        )

        coEvery {
            channelService.archiveAdministrativeChannel(channelTheme)
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(
                to = "/channels/$channelId/archive_administrative",
                body = channelArchiveAdministrativeRequest
            ) { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.archiveAdministrativeChannel(any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#archiveAdministrative should return unauthorized`() {
        mockkStatic(LocalDateTime::class)
        mockkObject(RangeUUID)

        every { LocalDateTime.now() } returns date

        every { RangeUUID.generate() } returns uuid

        val channelTheme = ChannelTheme(
            id = uuid,
            channelId = channelId,
            staffId = staff.id,
            setupId = channelArchiveAdministrativeRequest.akinatorSetupId,
            suggestedTheme = channelArchiveAdministrativeRequest.suggestedTheme,
            theme = channelArchiveAdministrativeRequest.theme,
            reason = channelArchiveAdministrativeRequest.reason,
            screenOpenedAt = date,
            screenClosedAt = date,
        )

        coEvery {
            channelService.archiveAdministrativeChannel(channelTheme)
        } returns AuthorizationException("Authorization failed for '${channelId}'").failure()

        authenticatedAs(idToken, staffTest) {
            put(
                to = "/channels/$channelId/archive_administrative",
                body = channelArchiveAdministrativeRequest
            ) { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.archiveAdministrativeChannel(any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#findMacros should return error when shortcut is missing`() {
        authenticatedAs(idToken, staffTest) {
            get("/channels/macros") { response ->
                assertThat(response).isBadRequestWithErrorCode("illegal_argument")
            }
        }

        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#findMacros should return macros`() {
        val expected = listOf(ChannelMacroResponse("test", "Oi, isso é um teste"))

        coEvery {
            macroService.find("test")
        } returns listOf(ChannelMacro("test", "Oi, isso é um teste")).success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/macros?shortcut=test") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { macroService.find(any()) }
        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#findMacros should return empty list`() {
        coEvery { macroService.find("test") } returns emptyList<ChannelMacro>().success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/macros?shortcut=test") { response ->
                assertThat(response).isOKWithData(emptyList<ChannelMacroResponse>())
            }
        }

        coVerifyOnce { macroService.find(any()) }
        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getAllTags should return tag list`() {
        val expected = listOf(ChannelTagResponse("TAG"), ChannelTagResponse("TAG2"))

        coEvery { tagService.getAll() } returns listOf(ChannelTag("TAG"), ChannelTag("TAG2")).success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/tags") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { tagService.getAll() }
        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
    }

    @Test
    fun `#getAllTags should return empty list`() {
        coEvery { tagService.getAll() } returns emptyList<ChannelTag>().success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/tags") { response ->
                assertThat(response).isOKWithData(emptyList<ChannelTagResponse>())
            }
        }

        coVerifyOnce { tagService.getAll() }
        coVerify { staffService wasNot called }
        coVerify { channelService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
    }

    @Test
    fun `#addTags should return tag list`() {
        val tags = listOf("TAG")
        coEvery { channelService.setTags(channelId, tags) } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/$channelId/tags", body = ChannelTagsRequest(tags)) { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.setTags(any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#mergeChannels should return error when unauthorized`() {
        coEvery { channelService.merge(channelId, channelId, staff.id.toString()) } returns
                AuthorizationException("Unauthorized_to_merge_with_different_members").failure()

        authenticatedAs(idToken, staffTest) {
            post(to = "/channels/$channelId/merge/$channelId") { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.merge(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#mergeChannels should return ok with channel`() {
        coEvery { channelService.merge(channelId, channelId, staff.id.toString()) } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/channels/$channelId/merge/$channelId") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.merge(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#deleteMessage should return error when error on delete`() {
        coEvery {
            channelService.deleteMessage(channelId, channelId, staff.id.toString(), false)
        } returns AuthorizationException("user_unauthorized").failure()

        authenticatedAs(idToken, staffTest) {
            delete("/channels/$channelId/message/$channelId") { response ->
                assertThat(response).isUnauthorized()
            }
        }

        coVerifyOnce { channelService.deleteMessage(any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#deleteMessage should return ok with channel`() {
        coEvery {
            channelService.deleteMessage(
                channelId,
                channelId,
                staff.id.toString(),
                false
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            delete("/channels/$channelId/message/$channelId") { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.deleteMessage(any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#assignTicketInZendesk should return ticketId`() {
        val ticketId = "123456"
        val ticket = ZendeskTicket(ticketId.toBigInteger())

        coEvery {
            zendeskTicketService.createTicketFromChannel(
                ChannelsZendeskTicketData(
                    requesterPersonId = channelsZendeskRequest.personId,
                    subject = channelsZendeskRequest.channelName,
                    zendeskTag = channelsZendeskRequest.zendeskAssignmentTag,
                    channelId = channelId,
                    channelTag = channelsZendeskRequest.channelTag,
                    context = channelsZendeskRequest.context,
                )
            )
        } returns ticket.success()

        authenticatedAs(idToken, staffTest) {
            post("/channels/$channelId/assign_ticket_in_zendesk", body = channelsZendeskRequest) { response ->
                assertThat(response).isOKWithData(ChannelResponse(ticketId))
            }
        }

        coVerifyOnce { zendeskTicketService.createTicketFromChannel(any()) }

        coVerify { channelService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#assignTicketInZendesk should not return ticketId when ticket could not be created`() {

        coEvery {
            zendeskTicketService.createTicketFromChannel(
                ChannelsZendeskTicketData(
                    requesterPersonId = channelsZendeskRequest.personId,
                    subject = channelsZendeskRequest.channelName,
                    zendeskTag = channelsZendeskRequest.zendeskAssignmentTag,
                    channelId = channelId,
                    channelTag = channelsZendeskRequest.channelTag,
                    context = channelsZendeskRequest.context,
                )
            )
        } returns NotFoundException().failure()

        authenticatedAs(idToken, staffTest) {
            post("/channels/$channelId/assign_ticket_in_zendesk", body = channelsZendeskRequest) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { zendeskTicketService.createTicketFromChannel(any()) }

        coVerify { channelService wasNot called }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#highlight should return an error when the comment is not found`() {
        coEvery { channelService.highlight(channelId, currentStaffId) } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put("/channels/$channelId/highlight", body = highlightChannelRequest) { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.highlight(any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#highlight should return an error when the channel is not found`() {
        coEvery {
            channelService.highlight(channelId, currentStaffId)
        } returns NotFoundException("channel_not_found").failure()

        authenticatedAs(idToken, staffTest) {
            put("/channels/$channelId/highlight", body = highlightChannelRequest) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { channelService.highlight(any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#classification returns channel id after save new classification fields`() {
        val kind = ChannelKind.CHANNEL
        val category = ChannelCategory.ASSISTANCE
        val subCategory = ChannelSubCategory.LONGITUDINAL
        val subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM

        val request = ChannelClassificationRequest(
            kind, category, subCategory, subCategoryClassifier
        )

        coEvery {
            channelService.channelClassification(
                channelId, kind, category, subCategory, subCategoryClassifier,
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put("/channels/$channelId/classification", request) { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce { channelService.channelClassification(any(), any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getRoutingDashboardCounters returns RoutingDashboardResponse with counters`() {
        val routingDashboardResponse = RoutingDashboardResponse(
            chats = 12,
            simultaneity = 1.5.toBigDecimal()
        )
        coEvery {
            channelService.getDashboardCounters()
        } returns routingDashboardResponse.success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/dashboard/counters") { response ->
                assertThat(response).isOKWithData(routingDashboardResponse)
            }
        }

        coVerifyOnce { channelService.getDashboardCounters() }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#reRouting returns channel id when success to re-routing`() {
        val request = ReRoutingRequest(ChannelCategory.ASSISTANCE, ChannelSubCategory.VIRTUAL_CLINIC)
        coEvery {
            channelService.reRouting(channelId, request.category, request.subCategory)
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            authenticatedAs(idToken, staffTest) {
                post("/channels/$channelId/re_routing/v2", request) { response ->
                    assertThat(response).isOKWithData(ChannelResponse(channelId))
                }
            }
        }

        coVerifyOnce { channelService.reRouting(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#reRouting returns channel id when success to re-routing when ChannelSubCategory is null`() {
        val request = ReRoutingRequest(ChannelCategory.ASSISTANCE, null)
        coEvery {
            channelService.reRouting(channelId, request.category, request.subCategory)
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            authenticatedAs(idToken, staffTest) {
                post("/channels/$channelId/re_routing/v2", request) { response ->
                    assertThat(response).isOKWithData(ChannelResponse(channelId))
                }
            }
        }

        coVerifyOnce { channelService.reRouting(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#reRouting returns channel id when success to re-routing when ChannelSubCategory is null and it has reason`() {
        val request = ReRoutingRequest(ChannelCategory.ASSISTANCE, null, reason = "reason")
        coEvery {
            channelService.reRouting(
                channelId = channelId,
                category = request.category,
                subCategory = request.subCategory,
                extraInfo = ReRoutingExtraInfo(
                    reason = request.reason,
                    currentStaffId = staffTest.id
                )
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            authenticatedAs(idToken, staffTest) {
                post("/channels/$channelId/re_routing/v2", request) { response ->
                    assertThat(response).isOKWithData(ChannelResponse(channelId))
                }
            }
        }

        coVerifyOnce { channelService.reRouting(any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#reRouting returns error when an error occurs to re-routing`() {
        val request = ReRoutingRequest(ChannelCategory.ASSISTANCE, ChannelSubCategory.VIRTUAL_CLINIC)
        coEvery {
            channelService.reRouting(channelId, request.category, request.subCategory)
        } returns Exception().failure()

        authenticatedAs(idToken, staffTest) {
            authenticatedAs(idToken, staffTest) {
                post("/channels/$channelId/re_routing/v2", request) { response ->
                    assertThat(response).isInternalServerError()
                }
            }
        }

        coVerifyOnce { channelService.reRouting(any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getNextVirtualClinic returns channel id and call service to do logic`() {
        coEvery { channelService.getNextVirtualClinic(staff.id) } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            authenticatedAs(idToken, staffTest) {
                post("/channels/next_virtual_clinic") { response ->
                    assertThat(response).isOKWithData(ChannelResponse(channelId))
                }
            }
        }

        coVerifyOnce { channelService.getNextVirtualClinic(any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#dischargeChannel should discard channel`() {
        val dischargeRequest = ChannelDischargeRequest(
            channel.name!!,
            channel.kind!!,
            channel.category!!,
            channel.subCategory!!
        )

        coEvery {
            channelService.dischargeChannel(
                channelId = channelId,
                name = dischargeRequest.name,
                kind = dischargeRequest.kind,
                category = dischargeRequest.category,
                subCategory = dischargeRequest.subCategory,
                staffId = staffTest.id.toString()
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/channels/${channelId}/discharge", body = dischargeRequest) { response ->
                assertThat(response).isOKWithData(channelResponse)
            }
        }

        coVerifyOnce { channelService.dischargeChannel(any(), any(), any(), any(), any(), any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getZendeskAssignmentTags should return selected and availableTags`() {
        val selectedTag = ChannelsZendeskAssignmentTag("channel_tag_1", "zendesk_tag_1", "Tag 1")
        val secondTag = ChannelsZendeskAssignmentTag("channel_tag_2", "zendesk_tag_2", "Tag 2")
        val availableTags = listOf(selectedTag, secondTag)
        val expectedResponse = ChannelsZendeskAssignmentTagsResponse(selectedTag, availableTags)

        coEvery {
            channelService.get(channelId)
        } returns channelResponseModel.success()

        coEvery {
            channelsZendeskTagService.findByChannelTags(listOf("channel_tag_1", "channel_tag_2"))
        } returns selectedTag.convertTo(ChannelsZendeskTag::class).success()

        coEvery {
            channelsZendeskTagService.getAll()
        } returns listOf(
            selectedTag.convertTo(ChannelsZendeskTag::class),
            secondTag.convertTo(ChannelsZendeskTag::class),
        ).success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/$channelId/zendesk_assignment_tags") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { channelService.get(any()) }
        coVerifyOnce { channelsZendeskTagService.getAll() }
        coVerifyOnce { channelsZendeskTagService.findByChannelTags(any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#getZendeskAssignmentTags should return only availableTags`() {
        val tag = ChannelsZendeskAssignmentTag("channel_tag_1", "zendesk_tag_1", "Tag 1")
        val availableTags = listOf(tag)
        val expectedResponse = ChannelsZendeskAssignmentTagsResponse(null, availableTags)

        coEvery {
            channelService.get(channelId)
        } returns channelResponseModel.success()

        coEvery {
            channelsZendeskTagService.findByChannelTags(listOf("channel_tag_1", "channel_tag_2"))
        } returns NotFoundException().failure()

        coEvery {
            channelsZendeskTagService.getAll()
        } returns listOf(
            tag.convertTo(ChannelsZendeskTag::class),
        ).success()

        authenticatedAs(idToken, staffTest) {
            get("/channels/$channelId/zendesk_assignment_tags") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { channelService.get(any()) }
        coVerifyOnce { channelsZendeskTagService.getAll() }
        coVerifyOnce { channelsZendeskTagService.findByChannelTags(any()) }
        coVerify { staffService wasNot called }
        coVerify { followUpService wasNot called }
        coVerify { macroService wasNot called }
        coVerify { tagService wasNot called }
    }

    @Test
    fun `#assignToN2Staff returns channel id when success to assign n2 staff`() {
        val expectedStaff = staffTest.copy(role = Role.NAVIGATOR)

        coEvery {
            channelService.assignToN2Staff(channelId, expectedStaff.id.toString())
        } returns channelId.success()

        authenticatedAs(idToken, expectedStaff) {
            put("/channels/$channelId/assign_to_n2_staff") { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce {
            channelService.assignToN2Staff(any(), any())
        }
    }

    @Test
    fun `#getAdministrativeChatClassify return channel classification without problems`() {
        val expectedStaff = staffTest.copy(role = Role.NAVIGATOR)

        val expectedResponse = ChannelClassifyResponse(
            categoryNumber = "13",
            categoryTheme = "Reembolso",
            akinatorSetupId = RangeUUID.generate()
        )
        coEvery {
            channelClassifyService.getAdministrativeChatCategoryNumber(channelId)
        } returns expectedResponse.success()

        authenticatedAs(idToken, expectedStaff) {
            get("/channels/$channelId/administrative_classify") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { channelClassifyService.getAdministrativeChatCategoryNumber(any()) }
    }

    @Test
    fun `#setAdministrativeStatus update administrative status successfully`() {
        coEvery {
            channelAdministrativeService.setAdministrativeStatus(
                channelId,
                channelAdministrativeStatusRequest.administrativeStatus
            )
        } returns channelId.success()

        authenticatedAs(idToken, staffTest) {
            put(
                to = "/channels/$channelId/administrative_status",
                body = channelAdministrativeStatusRequest
            ) { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce { channelAdministrativeService.setAdministrativeStatus(any(), any()) }
    }
}
