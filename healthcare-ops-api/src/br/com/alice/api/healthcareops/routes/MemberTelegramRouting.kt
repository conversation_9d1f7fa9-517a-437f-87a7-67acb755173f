package br.com.alice.api.healthcareops.routes

import br.com.alice.api.healthcareops.controllers.MemberTelegramTrackingController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.memberTelegramRouting() {
    val memberTelegramTrackingController by inject<MemberTelegramTrackingController>()

    authenticate {
        route("/member_telegram_tracking") {
            get("/{id}") { coHandler("id", memberTelegramTrackingController::getTelegramById) }
            put("") { coHandler(memberTelegramTrackingController::saveTelegramCodes) }
            get("/search") { coHandler(memberTelegramTrackingController::findBySearchTokens) }
        }
    }
}
