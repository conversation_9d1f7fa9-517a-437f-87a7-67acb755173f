package br.com.alice.api.healthcareops.models

import br.com.alice.common.extensions.money
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class CompanyProductPriceListingCreateRequest(
    val productId: UUID,
    val priceListingItems: List<PriceListItemRequest>,
    val priceListingId: UUID? = null
)

data class CompanyProductPriceListingUpdateRequest(
    val priceListingItems: List<PriceListItemRequest>,
    val priceListingId: UUID? = null
)

data class CompanyProductPriceListingUpdateBlockForSaleRequest(
    val blockForSale: Boolean
)

data class PriceListItemRequest(
    val minAge: Int,
    val maxAge: Int,
    val amount: BigDecimal,
    val priceAdjustment: BigDecimal? = 0.money,
)

data class CompanyProductPriceListingResponse(
    val id: UUID,
    val priceListingTitle: String?,
    val priceListingId: UUID?,
    val productSaleName: String,
    val productName: String,
    val ansNumber: String,
    val startDate: LocalDate,
    val isBlockedForSale: Boolean,
    val priceListingItems: List<PriceListItemRequest>
)
