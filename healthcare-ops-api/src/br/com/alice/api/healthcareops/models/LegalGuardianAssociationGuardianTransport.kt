package br.com.alice.api.healthcareops.models

import br.com.alice.data.layer.models.Address
import java.time.LocalDateTime
import java.util.UUID

data class LegalGuardianAssociationGuardianListResponse(
    val guardianInfo: List<LegalGuardianAssociationGuardianResponse>
)

data class LegalGuardianAssociationGuardianResponse(
    val id: UUID?,
    val firstName: String?,
    val lastName: String?,
    val socialFirstName: String?,
    val socialLastName: String?,
    val identityDocument: String?,
    val identityDocumentIssuingBody: String?,
    val nationalId: String?,
    val email: String?,
    val archived: Boolean = false,
    val address: Address,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
