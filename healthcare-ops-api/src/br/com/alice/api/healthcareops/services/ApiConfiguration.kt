package br.com.alice.api.healthcareops.services

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ApiConfiguration {
    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun config(key: String) = config.property("${environment().value.lowercase()}.$key").getString()

    fun url(path: String) = "${config("baseUrl")}${path}"
}
