package br.com.alice.api.healthcareops.services

import br.com.alice.api.healthcareops.ServiceConfig
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.storage.FileStorage
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.data.layer.models.EmailCommunication
import br.com.alice.data.layer.models.Person
import br.com.alice.membership.client.EmailCommunicationService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class TaxDocumentService(
    private val personService: PersonService,
    private val fileStorage: FileStorage,
    private val sender: EmailSender,
    private val emailCommunicationService: EmailCommunicationService,
) {
    companion object {
        internal val emailSender = EmailAddress(ServiceConfig.Mailer.senderName(), ServiceConfig.Mailer.senderAddress())
        const val TAX_DOCUMENT_TEMPLATE = "member_ir_with_attachment"
    }

    suspend fun sendTaxDocumentToMember(nationalId: String, idempotencyCheck: Boolean = true): Result<Boolean, Throwable> {
        val person = personService.findByNationalId(nationalId).get()
        val idempotencyKey = "${person.email}+${TAX_DOCUMENT_TEMPLATE}"

        if (shouldSkipEmail(idempotencyCheck, idempotencyKey)) {
            logger.info("tax document: email already sent", "person" to person)
            return false.success()
        }

        val fileContent = fileStorage.getContent("${ServiceConfig.TaxDocument.bucketUrl()}/${person.nationalId}.pdf")
        val emailRequest = createEmailRequest(person, fileContent)

        sender.send(emailRequest).let { emailReceipt ->
            if (emailReceipt.id == "ERROR") return Exception("Email sender failed").failure()
            return saveEmailComumnication(person, emailRequest, idempotencyCheck, idempotencyKey)
        }
    }

    private suspend fun shouldSkipEmail(idempotencyCheck: Boolean, idempotencyKey: String) =
        idempotencyCheck && emailCommunicationService.findBy(idempotencyKey).getOrNullIfNotFound() != null

    private fun createEmailRequest(person: Person, fileContent: ByteArray): SendEmailRequest =
        SendEmailRequest(
            from = emailSender,
            to = listOf(EmailAddress(person.contactName, person.email)),
            templateName = TAX_DOCUMENT_TEMPLATE,
            replaceVariables = mapOf("name" to person.contactName),
            attachments = listOf(
                EmailAttachment(
                    fileName = "${person.nationalId}.pdf",
                    content = fileContent,
                    type = "application/pdf"
                )
            )
        )

    private suspend fun saveEmailComumnication(
        person: Person,
        emailRequest: SendEmailRequest,
        idempotencyCheck: Boolean,
        idempotencyKey: String
    ): Result<Boolean, Throwable> {
        if (!idempotencyCheck) return true.success()

        val emailCommunication = EmailCommunication(
            sender = emailRequest.from.email,
            recipient = person.email,
            template = emailRequest.templateName,
            idempotencyKey = idempotencyKey
        )

        return emailCommunicationService
            .add(emailCommunication)
            .map { true }
    }
}
