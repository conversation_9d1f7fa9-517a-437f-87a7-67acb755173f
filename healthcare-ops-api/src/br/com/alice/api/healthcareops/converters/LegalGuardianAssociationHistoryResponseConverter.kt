package br.com.alice.api.healthcareops.converters

import br.com.alice.api.healthcareops.models.LegalGuardianAssociationHistoryResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.LegalGuardianAssociation

object LegalGuardianAssociationHistoryResponseConverter : Converter<LegalGuardianAssociation, LegalGuardianAssociationHistoryResponse>(
    LegalGuardianAssociation::class, LegalGuardianAssociationHistoryResponse::class
) {
    fun convert(source: LegalGuardianAssociation, guardianName: String) = super.convert(
        source,
        map(LegalGuardianAssociationHistoryResponse::guardianName) from guardianName,
        map(LegalGuardianAssociationHistoryResponse::personId) from source.personId.toString()
    )
}
