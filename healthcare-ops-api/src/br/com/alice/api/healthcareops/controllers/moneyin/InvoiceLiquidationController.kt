package br.com.alice.api.healthcareops.controllers.moneyin

import br.com.alice.api.healthcareops.converters.InvoiceLiquidationTransportConverter
import br.com.alice.authentication.currentUserId
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CreatePaymentRequest
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class InvoiceLiquidationController(
    private val invoiceLiquidationService: InvoiceLiquidationService,
    private val invoicePaymentService: InvoicePaymentService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
) {

    suspend fun createPayment(invoiceLiquidationId: UUID, request: CreatePaymentRequest): Response {
        logger.info(
            "Create payment route",
            "current_staff_id" to currentUserId(),
            "invoice_liquidation_id" to invoiceLiquidationId,
            "request" to request
        )

        val invoiceLiquidation = invoiceLiquidationService.get(invoiceLiquidationId).get()

        val paymentMethod =
            request.payment.method
        val paymentReason =
            request.payment.reason ?: billingAccountablePartyService.get(invoiceLiquidation.billingAccountablePartyId)
                .map {
                    if (it.isLegalPerson)
                        PaymentReason.B2B_LIQUIDATION
                    else PaymentReason.B2C_LIQUIDATION
                }
                .get()

        return invoicePaymentService.createPaymentForLiquidation(
            invoiceLiquidation = invoiceLiquidation,
            paymentMethod,
            paymentReason,
            InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
            request.payment.dueDate?.let { LocalDate.parse(it) },
        ).map(InvoiceLiquidationTransportConverter::convert).foldResponse()
    }

    suspend fun listByPersonId(personId: PersonId) = coroutineScope {

        invoiceLiquidationService.listByPersonId(personId)
            .map { invoiceLiquidations ->
                val invoiceLiquidationIds = invoiceLiquidations.map { it.id }
                val memberInvoiceGroupIds = invoiceLiquidations.map { it.memberInvoiceGroupIds }.flatten()

                val mappedPaymentsDeferred = async {
                    invoicePaymentService.getByInvoiceLiquidationIds(invoiceLiquidationIds, withPaymentDetails = true)
                        .map { payments -> payments.groupBy { it.invoiceLiquidationId!! } }.get()
                }
                val mappedMIGsDeferred = async {
                    memberInvoiceGroupService.getByIds(memberInvoiceGroupIds)
                        .map { migs -> migs.associateBy { it.id } }.get()
                }

                val mappedPayments = mappedPaymentsDeferred.await()
                val mappedMIGs = mappedMIGsDeferred.await()

                InvoiceLiquidationTransportConverter.convert(
                    invoiceLiquidations,
                    mappedPayments,
                    mappedMIGs,
                )
            }
            .foldResponse()
    }

    suspend fun listByCompanyId(companyId: UUID) = coroutineScope {
        invoiceLiquidationService.listByCompanyId(companyId)
            .map { invoiceLiquidations ->
                val invoiceLiquidationIds = invoiceLiquidations.map { it.id }
                val memberInvoiceGroupIds = invoiceLiquidations.map { it.memberInvoiceGroupIds }.flatten()

                val mappedPaymentsDeferred = async {
                    invoicePaymentService.getByInvoiceLiquidationIds(invoiceLiquidationIds, withPaymentDetails = true)
                        .map { payments -> payments.groupBy { it.invoiceLiquidationId!! } }.get()
                }
                val mappedMIGsDeferred = async {
                    memberInvoiceGroupService.getByIds(memberInvoiceGroupIds)
                        .map { migs -> migs.associateBy { it.id } }.get()
                }

                val mappedPayments = mappedPaymentsDeferred.await()
                val mappedMIGs = mappedMIGsDeferred.await()

                InvoiceLiquidationTransportConverter.convert(
                    invoiceLiquidations,
                    mappedPayments,
                    mappedMIGs,
                )
            }
            .foldResponse()
    }
}
