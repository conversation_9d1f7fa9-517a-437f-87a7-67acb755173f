package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.models.PersonPreferencesRequest
import br.com.alice.api.healthcareops.models.PersonPreferencesResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PersonPreferences
import br.com.alice.membership.client.PersonPreferencesService
import com.github.kittinunf.result.map

class PersonPreferencesController(
    private val personPreferencesService: PersonPreferencesService
) : Controller() {
    suspend fun upsert(personId: PersonId, request: PersonPreferencesRequest): Response {
        val personPreferences = personPreferencesService.findByPersonId(personId).getOrNullIfNotFound()
        logger.info("person preferences found", "person_preferences" to personPreferences, "request" to request)

        return if (personPreferences == null)
            personPreferencesService
                .add(PersonPreferences(personId = personId, firstPaymentMethod = request.firstPaymentMethod))
                .map { it.convertTo(PersonPreferencesResponse::class) }
                .foldResponse()
        else
            personPreferencesService
                .update(personPreferences.copy(firstPaymentMethod = request.firstPaymentMethod))
                .map { it.convertTo(PersonPreferencesResponse::class) }
                .foldResponse()
    }

    suspend fun get(personId: PersonId): Response =
        personPreferencesService
            .findByPersonId(personId)
            .map { it.convertTo(PersonPreferencesResponse::class) }
            .foldResponse()
}
