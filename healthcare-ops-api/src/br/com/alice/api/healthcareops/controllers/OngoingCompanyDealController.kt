package br.com.alice.api.healthcareops.controllers

import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.sales_channel.service.OngoingCompanyDealService

class OngoingCompanyDealController(
    val service: OngoingCompanyDealService
): Controller() {

    suspend fun sendContract(dealId: String) =
        service.sendContract(dealId.toUUID()).foldResponse()

}
