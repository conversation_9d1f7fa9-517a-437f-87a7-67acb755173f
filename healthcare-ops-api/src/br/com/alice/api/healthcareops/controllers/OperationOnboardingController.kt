package br.com.alice.api.healthcareops.controllers

import br.com.alice.common.Response
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.foldResponse
import br.com.alice.membership.client.onboarding.OperationOnboardingService
import br.com.alice.membership.model.onboarding.OperationOnboardingPerson
import br.com.alice.membership.model.onboarding.OperationOnboardingPhase
import com.github.kittinunf.result.map
import io.ktor.http.Parameters

class OperationOnboardingController(
    private val operationOnboardingService: OperationOnboardingService
) {

    suspend fun getOperationOnboardingPersons(phaseString: String, parameters: Parameters): Response {
        val phase = OperationOnboardingPhase.valueOf(phaseString.uppercase())
        val startDate = parameters["startDate"]?.toLocalDate()
            ?: throw IllegalArgumentException("Invalid startDate filter")
        val endDate = parameters["endDate"]?.toLocalDate()
            ?: throw IllegalArgumentException("Invalid endDate filter")

        return operationOnboardingService
            .getOperationOnboardingPersons(phase, startDate, endDate)
            .map { operations -> operations.map { it.convertTo(OperationOnboardingPerson::class) } }
            .foldResponse()
    }

}

