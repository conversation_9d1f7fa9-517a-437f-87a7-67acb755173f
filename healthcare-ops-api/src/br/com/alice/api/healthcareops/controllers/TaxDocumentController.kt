package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.models.TaxDocumentRequest
import br.com.alice.api.healthcareops.models.events.TaxDocumentRequestedEvent
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.toResponse
import io.ktor.http.HttpStatusCode

class TaxDocumentController(
    private val kafkaProducerService: KafkaProducerService,
): Controller() {
    suspend fun sendTaxDocumentToMember(nationalId: String): Response {
        kafkaProducerService.produce(TaxDocumentRequestedEvent(nationalId = nationalId, idempotencyCheck = false))
        return Response(HttpStatusCode.Accepted)
    }

    suspend fun sendTaxDocumentToMembers(request: TaxDocumentRequest): Response {
        return request
            .nationalIds
            .map { kafkaProducerService.produce(TaxDocumentRequestedEvent(nationalId = it, idempotencyCheck = true)) }
            .toResponse()
    }
}
