package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.converters.CompanyContractualRiskTransportConverter
import br.com.alice.business.client.BeneficiaryCompiledViewService
import br.com.alice.businessrisk.clients.PersonContractualRiskService
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import com.github.kittinunf.result.map
import java.util.UUID

class CompanyContractualRiskController(
    private val beneficiaryCompiledViewService: BeneficiaryCompiledViewService,
    private val personContractualRiskService: PersonContractualRiskService
) : Controller() {
    suspend fun getCompanyContractualRisk(companyId: UUID) =
        beneficiaryCompiledViewService.findCurrentByCompanyId(companyId).flatMapPair { beneficiaries ->
            personContractualRiskService.findCurrentByPersonIds(beneficiaries.map { it.personId })
        }.map { (personsContractualRisk, beneficiaries) ->
            CompanyContractualRiskTransportConverter.convert(beneficiaries, personsContractualRisk)
        }.foldResponse()
}
