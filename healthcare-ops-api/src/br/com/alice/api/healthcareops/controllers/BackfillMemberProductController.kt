package br.com.alice.api.healthcareops.controllers

import br.com.alice.common.Response
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.PriceListingItem
import br.com.alice.data.layer.models.ProductPrice
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import java.util.UUID

//TODO: Remove it after fixing member selected product inconsistencies
class BackfillMemberProductController(
    private val memberService: MemberService,
    private val memberProductPriceService: MemberProductPriceService,
) {
    suspend fun fixMemberSelectedProduct(memberId: UUID): Response {
        val member = memberService.get(memberId).get()

        if (member.selectedProduct.prices.any { (it.minAge == 21 && it.maxAge == 38) || (it.minAge == 22 && it.maxAge == 38) }) {
            val updatedMember = member.copy(
                selectedProduct = member.selectedProduct.copy(
                    prices = member.selectedProduct.prices.map { productPrice ->
                        if ((productPrice.minAge == 21 && productPrice.maxAge == 38) || (productPrice.minAge == 22 && productPrice.maxAge == 38))
                            ProductPrice(productPrice.id, productPrice.title, 34, productPrice.maxAge, productPrice.amount, productPrice.priceAdjustment)
                        else productPrice
                    }
                )
            )
            logger.info("fix member selected product: updating member product", "member" to member, "udpated_member" to member)
            memberService.update(updatedMember)
        }

        val currentMemberProductPrice = memberProductPriceService.findCurrent(member.id).get()

        if (currentMemberProductPrice.items.any{ (it.minAge == 21 && it.maxAge == 38) || (it.minAge == 22 && it.maxAge == 38) }) {
            val updatedMemberProductPrice = currentMemberProductPrice.copy(
                items = currentMemberProductPrice.items.map { priceListingItem ->
                    if ((priceListingItem.minAge == 21 && priceListingItem.maxAge == 38) || (priceListingItem.minAge == 22 && priceListingItem.maxAge == 38))
                        PriceListingItem(34, priceListingItem.maxAge, priceListingItem.amount, priceListingItem.priceAdjustment)
                    else priceListingItem
                }
            )

            logger.info("fix member selected product: updating member product price", "member_product_price" to currentMemberProductPrice, "updated_member_product_price" to updatedMemberProductPrice)
            memberProductPriceService.update(updatedMemberProductPrice)
        }

        return true.toResponse()
    }
}
