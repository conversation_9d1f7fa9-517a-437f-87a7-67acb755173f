package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.models.MemberTelegramTrackingRequest
import br.com.alice.business.client.MemberTelegramTrackingService
import br.com.alice.business.events.MemberTelegramTrackingRegisteredEvent
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.MemberTelegramTracking
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.util.UUID

class MemberTelegramTrackingController(
    private val memberTelegramTrackingService: MemberTelegramTrackingService,
    private val kafkaProducerService: KafkaProducerService
) :
    Controller() {

    suspend fun getTelegramById(id: UUID) =
        memberTelegramTrackingService.get(id).foldResponse()

    suspend fun findBySearchTokens(params: Parameters): Response {
        val query = params["query"] ?: return Response(HttpStatusCode.OK, emptyList<MemberTelegramTracking>())
        return memberTelegramTrackingService.findBySearchTokens(query).foldResponse()
    }

    suspend fun saveTelegramCodes(params: MemberTelegramTrackingRequest): Response {
        params.ids.filter { it.isNotEmpty() }.distinct().forEach { id ->
            kafkaProducerService.produce(MemberTelegramTrackingRegisteredEvent(id))
        }
        return true.toResponse()
    }

}
