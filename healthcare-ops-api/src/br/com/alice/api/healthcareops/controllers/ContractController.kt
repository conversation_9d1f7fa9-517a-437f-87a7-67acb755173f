package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.services.DocumentService
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.membership.client.ContractGenerator
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.AssociationIsNotValidatedYetException
import br.com.alice.onboarding.client.LegalGuardianAssociationService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class ContractController(
    private val documentService: DocumentService,
    private val personService: PersonService,
    private val onboardingService: OnboardingService,
    private val contractRegistry: ContractRegistry,
    private val contractGenerator: ContractGenerator,
    private val legalGuardianAssociationService: LegalGuardianAssociationService,
) : Controller() {

    suspend fun getContract(personId: String) = contractRegistry.findLatestContract(personId.toPersonId())
        .map { it.certificateUrl ?: it.documentUrl }
        .map { documentService.getExternalUrl(it) }
        .map(::ContractDocumentResponse)
        .foldResponse()

    suspend fun updateDocument(personId: PersonId, multipartRequest: MultipartRequest): Response =
        documentService.uploadContract(personId, multipartRequest)
            .flatMap { contractRegistry.updateDocument(personId, it.url) }
            .map { ContractDocumentResponse(url = it.documentUrl) }
            .foldResponse()

    suspend fun generateContract(personId: PersonId): Response {
        logger.info("ContractController::generateContract starting", "person_id" to personId)
        val person = personService.get(personId).get()

        val currentPhase = onboardingService.getCurrentPhase(personId).get()

        if (currentPhase == OnboardingPhase.PORTABILITY_REVIEW) {
            logger.info(
                "ContractController::generateContract currentPhase == PORTABILITY_REVIEW",
                "current_phase" to currentPhase
            )
            return currentPhase.success()
                .map { ContractDocumentResponse(onboardingPhase = it) }
                .foldResponse()
        }
        if (person.age >= 18) {
            logger.info(
                "ContractController::generateContract person age >= 18",
                "person_id" to personId
            )
            return contractRegistry.start(personId)
                .map { ContractDocumentResponse(onboardingPhase = it.currentPhase) }
                .foldResponse()
        }
        logger.info(
            "ContractController::generateContract person age >= 18",
            "person_id" to personId
        )
        legalGuardianAssociationService
            .findByPersonId(personId)
            .fold(
                { legalGuardianAssociation ->
                    logger.info("Association was founded", "id" to legalGuardianAssociation.id)
                    return if (legalGuardianAssociation.isNotValidated) {
                        logger.error("Association was not validated yet")
                        throw AssociationIsNotValidatedYetException()
                    } else if (legalGuardianAssociation.isSigned == true) {
                        onboardingService.changePhaseTo(
                            personId = personId,
                            newPhase = OnboardingPhase.LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING.nextPhase(),
                        ).map { ContractDocumentResponse(onboardingPhase = it.currentPhase) }.foldResponse()
                    } else {
                        onboardingService.changePhaseTo(
                            personId = personId,
                            newPhase = OnboardingPhase.LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING,
                        ).map { ContractDocumentResponse(onboardingPhase = it.currentPhase) }.foldResponse()
                    }
                }, {
                    logger.info(
                        "ContractController::generateContract error on fold",
                        "it" to it
                    )
                    return true.success().foldResponse()
                }
            )
    }

    suspend fun unarchive(contractId: String) =
        contractRegistry
            .unarchiveContract(contractId.toUUID())
            .foldResponse()
}

data class ContractDocumentResponse(
    val url: String? = null,
    val onboardingPhase: OnboardingPhase? = null,
)
