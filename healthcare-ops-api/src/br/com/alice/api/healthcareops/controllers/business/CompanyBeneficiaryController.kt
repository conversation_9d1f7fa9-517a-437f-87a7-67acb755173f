package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.models.BeneficiaryTransport
import br.com.alice.api.healthcareops.models.BeneficiaryTransportOnboardingPhase
import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Person
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.sales_channel.model.DealBeneficiaryStage
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class CompanyBeneficiaryController(
    private val beneficiaryService: BeneficiaryService,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
    private val personService: PersonService,
    private val memberService: MemberService,
) : Controller() {

    suspend fun getBeneficiariesByCompanyId(companyId: UUID): Response {
        return beneficiaryService.findByCompanyId(companyId = companyId)
            .then { beneficiaries ->
                beneficiaries.ifEmpty {
                    return beneficiaries.toResponse()
                }
            }.mapPair { beneficiaries ->
                beneficiaryOnboardingService.findByBeneficiaryIds(beneficiaries.map { it.id }).get()
            }.flatMap { (onboarding, beneficiaries) ->
                getMembershipAndPersonFromBeneficiaries(beneficiaries).map { personsAndMemberships ->
                    Triple(onboarding, beneficiaries, personsAndMemberships)
                }
            }.map { (onboarding, beneficiaries, personsAndMemberships) ->
                convertToBeneficiaryTransportAndSort(
                    onboarding,
                    beneficiaries,
                    personsAndMemberships.first,
                    personsAndMemberships.second,
                )
            }.foldResponse()
    }

    private suspend fun getMembershipAndPersonFromBeneficiaries(beneficiaries: List<Beneficiary>) = coroutineScope {
        val personIds = beneficiaries.map { it.personId.toString() }
        val memberIds = beneficiaries.map { it.memberId }
        val personsDeferred = async { personService.findByIds(personIds).get() }
        val membersDeferred = async { memberService.findByIds(memberIds).get() }

        val persons = personsDeferred.await()
        val members = membersDeferred.await()

        (persons to members).success()
    }

    private val beneficiaryTypeOrder: HashMap<String, Int> = hashMapOf(
        "Titular" to 0,
        "Dependente" to 1,
        "Não definido" to 2
    )

    private val memberStatusOrder: HashMap<MemberStatus, Int> = hashMapOf(
        MemberStatus.ACTIVE to 0,
        MemberStatus.PENDING to 1,
        MemberStatus.CANCELED to 3,
    )

    private fun sortBeneficiaries(beneficiaries: List<BeneficiaryTransport>): List<BeneficiaryTransport> {
        return beneficiaries
            .sortedWith(compareBy { beneficiaryTypeOrder[it.type] })
            .sortedWith(compareBy { memberStatusOrder[it.status] })
    }

    private fun convertToBeneficiaryTransportAndSort(
        beneficiariesOnboarding: List<BeneficiaryOnboarding>,
        beneficiaries: List<Beneficiary>,
        persons: List<Person>,
        memberships: List<Member>,
    ): List<BeneficiaryTransport> {
        return beneficiaries.mapNotNull { beneficiary ->
            val person = persons.find { it.id.toString() == beneficiary.personId.toString() }
            val onboarding = beneficiariesOnboarding.find { it.beneficiaryId == beneficiary.id }
            val membership = memberships.find { it.id == beneficiary.memberId }

            if (person != null && onboarding != null && membership != null) {
                BeneficiaryTransport(
                    name = person.fullSocialName,
                    age = person.age,
                    biological_sex = person.sex?.description,
                    type = typeTranslator(beneficiary.type),
                    stage = onboarding.currentPhase?.phase.toDealBeneficiaryStage().value,
                    person_id = beneficiary.personId.toString(),
                    status = membership.status,
                    currentOnboardingPhase = BeneficiaryTransportOnboardingPhase(
                        name = onboarding.currentPhase?.phase?.name ?: "",
                        description = onboarding.currentPhase?.phase?.description ?: ""
                    )
                )
            } else null
        }.let { sortBeneficiaries(it) }
    }

    private fun typeTranslator(type: BeneficiaryType): String {
        return when (type) {
            BeneficiaryType.EMPLOYEE -> "Titular"
            BeneficiaryType.DEPENDENT -> "Dependente"
            else -> "Não definido"
        }
    }

    private fun BeneficiaryOnboardingPhaseType?.toDealBeneficiaryStage() = DealBeneficiaryStageConverter.convert(this)

    object DealBeneficiaryStageConverter {
        fun convert(phase: BeneficiaryOnboardingPhaseType?): DealBeneficiaryStage = when (phase) {
            BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD, BeneficiaryOnboardingPhaseType.REGISTRATION
            -> DealBeneficiaryStage.ELEGIBLE_TO_DOWNLOAD_APP

            BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION -> DealBeneficiaryStage.FILL_HEALTH_DECLARATION
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION -> DealBeneficiaryStage.SCHEDULE_MEDICAL_INTERVIEW
            BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION_APPOINTMENT -> DealBeneficiaryStage.SCHEDULE_MEDICAL_INTERVIEW
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT -> DealBeneficiaryStage.SCHEDULE_MEDICAL_INTERVIEW
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED -> DealBeneficiaryStage.MEDICAL_INTERVIEW_SCHEDULED
            BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION -> DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS
            BeneficiaryOnboardingPhaseType.SEND_CPTS -> DealBeneficiaryStage.WAITING_FOR_CPT_ANALYSIS
            BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION -> DealBeneficiaryStage.PENDING_CPT_SIGNATURE
            BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE -> DealBeneficiaryStage.PENDING_CONTRACT_SIGNATURE
            BeneficiaryOnboardingPhaseType.SEND_REGISTRATION -> DealBeneficiaryStage.UPDATING
            BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW -> DealBeneficiaryStage.WAITING_FOR_DEPLOYMENT
            BeneficiaryOnboardingPhaseType.FINISHED -> DealBeneficiaryStage.ACTIVE_MEMBER
            else -> DealBeneficiaryStage.UNKNOWN
        }
    }
}
