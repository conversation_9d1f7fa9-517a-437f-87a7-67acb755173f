package br.com.alice.api.healthcareops.api

import br.com.alice.api.healthcareops.controllers.InvoicePriceController
import br.com.alice.api.healthcareops.models.InvoicePriceRequest
import br.com.alice.api.healthcareops.models.InvoicePriceResponse
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.PaymentMethod
import br.com.alice.membership.client.InvoicePriceService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberPaymentsRoutesTest: RoutesTestHelper() {
    private val invoicePriceService: InvoicePriceService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { InvoicePriceController(invoicePriceService) }
    }

    @Test
    fun `#calculateMonthlyFee should return 200 if can calculate fee`() {
        val personId = PersonId().toString()
        val requestBody = InvoicePriceRequest(
            discountPercentage = BigDecimal("10"),
            paymentMethod = PaymentMethod.BOLETO,
            installment = 1
        )
        val invoicePrice = BigDecimal("750.65")

        coEvery {
            invoicePriceService.calculateInvoicePrice(any(), any(), any(), any())
        } returns invoicePrice.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/$personId/payments/calculate_invoice_price", body = requestBody) { response ->
                assertThat(response).isOKWithData(InvoicePriceResponse(price = invoicePrice))
            }
        }
    }

    @Test
    fun `#calculateMonthlyFee should return error if cannot calculate monthly fee`() {
        val personId = PersonId().toString()
        val requestBody = InvoicePriceRequest(
            discountPercentage = BigDecimal(10),
            paymentMethod = PaymentMethod.BOLETO,
            installment = 1
        )

        coEvery {
            invoicePriceService.calculateInvoicePrice(any(), any(), any(), any())
        } returns Result.failure(Exception())

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/$personId/payments/calculate_invoice_price", body = requestBody) { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }
}
