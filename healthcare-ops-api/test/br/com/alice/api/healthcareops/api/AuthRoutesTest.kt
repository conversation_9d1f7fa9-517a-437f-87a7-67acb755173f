package br.com.alice.api.healthcareops.api

import br.com.alice.api.healthcareops.controllers.AuthController
import br.com.alice.api.healthcareops.models.SignInRequest
import br.com.alice.api.healthcareops.services.AuthService
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthRoutesTest : RoutesTestHelper() {

    private val authService: AuthService = mockk()
    private val authController = AuthController(authService)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { authController }
    }

    @Test
    fun `#sign_in - should return 200 OK when the authentication returned true`() {

        coEvery { authService.signInStaff(token) } returns true

        val request = SignInRequest(idToken = token)

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/sign_in", request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#sign_in - should return 401 Unauthorized when the authentication returned false`() {

        coEvery { authService.signInStaff(token) } returns false

        val request = SignInRequest(idToken = token)

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/sign_in", request) { response ->
                assertThat(response).isUnauthorized()
            }
        }
    }
}

