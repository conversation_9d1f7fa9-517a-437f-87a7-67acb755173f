package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.controllers.business.CompanyActivationFilesController
import br.com.alice.api.healthcareops.models.CompanyActivationFilesTransport
import br.com.alice.business.client.CompanyActivationFilesService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.VaultResponse
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyActivationFilesControllerTest : RoutesTestHelper() {
    private val companyActivationFilesService: CompanyActivationFilesService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val companyId = RangeUUID.generate()
    private val companyActivationFile = TestModelFactory.buildCompanyActivationFiles(companyId = companyId)
    private val vaultResponse = VaultResponse(
        id = companyActivationFile.fileVaultId,
        fileName = companyActivationFile.fileName,
        fileSize = 100L,
        type = "pdf",
        vaultUrl = "http://127.0.0.1/vault",
        url = "http://127.0.0.1/file"
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { CompanyActivationFilesController(companyActivationFilesService, fileVaultActionService) }
    }

    @Test
    fun `#getFilesByCompanyId should generate secureLinks correctly`() {
        coEvery { companyActivationFilesService.findByCompanyId(companyId) } returns listOf(companyActivationFile).success()
        coEvery {
            fileVaultActionService.securedGenericLinks(listOf(companyActivationFile.fileVaultId))
        } returns listOf(vaultResponse).success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/company/$companyId/activation_files") { response ->
                val expectedResponse = listOf(
                    CompanyActivationFilesTransport(
                        id = companyActivationFile.id,
                        fileName = companyActivationFile.fileName,
                        type = companyActivationFile.type.value,
                        url = vaultResponse.url,
                        owner = companyActivationFile.owner
                    )
                )
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }
}
