package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.converters.OngoingCompanyDealConverter
import br.com.alice.api.healthcareops.converters.ProductConverter
import br.com.alice.api.healthcareops.converters.business.CompanyContractConverter.toResponse
import br.com.alice.api.healthcareops.converters.business.CompanySubContractConverter.toResponse
import br.com.alice.api.healthcareops.models.CompanyInfoResponse
import br.com.alice.api.healthcareops.models.CreateCompanyRequest
import br.com.alice.api.healthcareops.models.UpdateCompanyRequest
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.product.client.ProductService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyControllerTest : RoutesTestHelper() {
    private val companyService: CompanyService = mockk()
    private val productService: ProductService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()
    private val salesFirmService: SalesFirmService = mockk()

    private val request = CreateCompanyRequest(
        name = "Acme",
        legalName = "Acme LTDA",
        cnpj = "**************-01",
        email = "<EMAIL>",
        phoneNumber = "(11)*********",
        addressPostalCode = "12345-123",
        addressStreet = "Acme street",
        addressNumber = 500,
        addressCity = "Sao Paulo",
        addressState = "SP"
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            CompanyController(
                companyService,
                productService,
                billingAccountablePartyService,
                companyContractService,
                companySubcontractService,
                fileVaultActionService,
                ongoingCompanyDealService,
                salesFirmService,
            )
        }
    }

    @Test
    fun `#findByQuery - should return companies`() {
        val query = "805900"
        val company = TestModelFactory.buildCompany(cnpj = "**************", status = CompanyStatus.ACTIVE)
        val company2 = TestModelFactory.buildCompany(cnpj = "***************")

        val expected = listOf(
            CompanyInfoResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountablePartyId = company.billingAccountablePartyId!!,
                status = CompanyStatus.ACTIVE
            ),
            CompanyInfoResponse(
                id = company2.id,
                name = company2.name,
                legalName = company2.legalName,
                cnpj = company2.cnpj,
                email = company2.email,
                phoneNumber = company2.phoneNumber,
                address = company2.address,
                billingAccountablePartyId = company2.billingAccountablePartyId!!,
                status = null
            )
        )

        coEvery { companyService.findByFilters(any(), any()) } returns listOf(company, company2)
        coEvery { companyService.countByFilters(any()) } returns 2

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/company?query=${query}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: List<CompanyInfoResponse> = response.bodyAsJson()
                Assertions.assertThat(content).isEqualTo(expected)
            }
        }

        coVerifyOnce { companyService.findByFilters(query, IntRange(0, 19)) }
        coVerifyOnce { companyService.countByFilters(query) }
    }

    @Test
    fun `#getById - should return companyInfoResponse with contract as list by feature flag`() = runBlocking {
        val fileContractId = RangeUUID.generate()
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        val contract = TestModelFactory.buildCompanyContract(
            billingAccountablePartyId = billingAccountableParty.id,
            contractFileIds = listOf(),
            availableProducts = null,
        )
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            contractIds = listOf(contract.id),
            billingAccountablePartyId = null,
        )

        val subcontract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            contractId = contract.id,
            billingAccountablePartyId = null,
        )

        val contractUrl = "http://localhost:9000/file/${fileContractId}"
        val vaultResponse = buildVaultResponse(contractUrl, fileContractId, "contrato.pdf")
        val salesFirm = TestModelFactory.buildSalesFirm()
        val deals = listOf(
            TestModelFactory.buildOngoingCompanyDeal(
                companyId = company.id,
                salesFirmId = salesFirm.id
            ),
            TestModelFactory.buildOngoingCompanyDeal(
                companyId = company.id,
                salesFirmId = salesFirm.id,
            )
        )

        val expected = CompanyInfoResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountablePartyId = company.billingAccountablePartyId,
            availableProducts = listOf(
                ProductConverter.convert(product)
            ),
            defaultProductId = ProductConverter.convert(product),
            contracts = listOf(
                contract.toResponse(
                    billingAccountableParty, files = mapOf(
                        fileContractId to vaultResponse
                    )
                )
                    .copy(subcontracts = listOf(subcontract.toResponse(null)))
            ),
            status = null,
            deals = deals.map { OngoingCompanyDealConverter.convert(it, salesFirm) }
        )

        coEvery { companyService.get(any()) } returns company
        coEvery { companyContractService.findByIds(any()) } returns listOf(contract)
        coEvery { companySubcontractService.findByCompanyId(any()) } returns listOf(subcontract)
        coEvery { productService.findByIds(any(), any()) } returns listOf(product)
        coEvery { billingAccountablePartyService.findById(any()) } returns listOf(
            billingAccountableParty
        )
        coEvery { ongoingCompanyDealService.findByCnpj(any()) } returns deals
        coEvery { fileVaultActionService.securedGenericLinks(any()) } returns listOf(vaultResponse)
        coEvery { salesFirmService.getByIds(deals.map { it.salesFirmId }) } returns listOf(salesFirm)

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/company/${company.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: CompanyInfoResponse = response.bodyAsJson()
                Assertions.assertThat(content).isEqualTo(expected)
            }
        }

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { productService.findByIds(company.availableProducts!!, ProductService.FindOptions(false)) }
    }

    @Test
    fun `#update - update cnpj of a company`() {
        val newCnpj = "123000000922123011"
        val company = TestModelFactory.buildCompany(cnpj = "oldCnpj")
        val companyUpdated = company.copy(cnpj = newCnpj)

        coEvery { companyService.get(any()) } returns company.success()
        coEvery { companyService.update(any(), true) } returns companyUpdated.success()

        val requestUpdate = UpdateCompanyRequest(cnpj = newCnpj)

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/${company.id}", body = requestUpdate) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: Company = response.bodyAsJson()
                Assertions.assertThat(content.cnpj).isEqualTo(newCnpj)
            }
        }

        coVerify(exactly = 1) { companyService.get(company.id) }
        coVerify(exactly = 1) { companyService.update(match { it.cnpj == newCnpj }, true) }
    }

    @Test
    fun `#add - add company from request`() {
        val company = TestModelFactory.buildCompany()

        coEvery { companyService.add(any(), any()) } returns company.success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/company/", body = request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: Company = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(company.id)
            }
        }

        coVerify(exactly = 1) { companyService.add(match { it.cnpj == request.cnpj }, true) }
    }
}
