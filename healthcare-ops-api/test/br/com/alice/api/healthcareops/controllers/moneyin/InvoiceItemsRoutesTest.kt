package br.com.alice.api.healthcareops.controllers.moneyin

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.converters.InvoiceItemsConverter
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CreateInvoiceItemsRequest
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemResponse
import br.com.alice.data.layer.models.InvoiceItemStatus
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoiceItemTypeRequest
import br.com.alice.data.layer.models.InvoiceItemValueUnit
import br.com.alice.data.layer.models.InvoiceItemsResponse
import br.com.alice.moneyin.client.InvoiceItemService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoiceItemsRoutesTest : RoutesTestHelper() {

    private val invoiceItemService: InvoiceItemService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { InvoiceItemsController(invoiceItemService, beneficiaryService) }
    }

    @Test
    fun `#listInvoiceItems should return 401 Unauthorized when user is not authenticated`() {
        val personId = RangeUUID.generate()

        get("/people/$personId/invoice_items") {
            assertThat(it).isUnauthorized()
        }
    }

    @Test
    fun `#listInvoiceItems should return 200 OK and body as expected`() {
        val person = TestModelFactory.buildPerson()
        val personId = person.id
        val invoiceItem1 = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.DISCOUNT,
            type = InvoiceItemType.PRORATION,
            personId = personId,
            absoluteValue = BigDecimal("120.00"))
        val invoiceItem2 = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRORATION,
            personId = personId,
            percentageValue = BigDecimal("140.00"))
        val invoicesItems = listOf(invoiceItem1, invoiceItem2)

        val expected = InvoiceItemsResponse(invoiceItems = listOf(
            InvoiceItemResponse(
                id = invoiceItem1.id,
                referenceDate = invoiceItem1.referenceDate.format(yearMonthFormatter).toString(),
                operation = invoiceItem1.operation,
                type = invoiceItem1.type,
                notes = invoiceItem1.notes,
                value = BigDecimal("120.00"),
                unit = InvoiceItemValueUnit.ABSOLUTE,
                status = invoiceItem1.status,
                createdAt = invoiceItem1.createdAt
            ),
            InvoiceItemResponse(
                id = invoiceItem2.id,
                referenceDate = invoiceItem2.referenceDate.format(yearMonthFormatter).toString(),
                operation = invoiceItem2.operation,
                type = invoiceItem2.type,
                notes = invoiceItem2.notes,
                value = BigDecimal("140.00"),
                unit = InvoiceItemValueUnit.PERCENTAGE,
                status = invoiceItem2.status,
                createdAt = invoiceItem2.createdAt
            )))

        coEvery { invoiceItemService.listInvoiceItemsByPerson(person.id) } returns invoicesItems.success()

        coEvery { beneficiaryService.findByPersonId(personId) } returns TestModelFactory.buildBeneficiary().success()

        coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(any()) } returns emptyList<InvoiceItem>().success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/$personId/invoice_items") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#listInvoiceItems should return 200 OK with subcontract and person invoice items`() {
        val person = TestModelFactory.buildPerson()
        val personId = person.id
        val invoiceItem1 = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.DISCOUNT,
            type = InvoiceItemType.PRORATION,
            personId = personId,
            absoluteValue = BigDecimal("120.00"))
        val invoiceItem2 = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRORATION,
            personId = personId,
            percentageValue = BigDecimal("140.00"))

        val expected = InvoiceItemsResponse(invoiceItems = listOf(
            InvoiceItemResponse(
                id = invoiceItem2.id,
                referenceDate = invoiceItem2.referenceDate.format(yearMonthFormatter).toString(),
                operation = invoiceItem2.operation,
                type = invoiceItem2.type,
                notes = invoiceItem2.notes,
                value = BigDecimal("140.00"),
                unit = InvoiceItemValueUnit.PERCENTAGE,
                status = invoiceItem2.status,
                createdAt = invoiceItem2.createdAt
            ),
            InvoiceItemResponse(
                id = invoiceItem1.id,
                referenceDate = invoiceItem1.referenceDate.format(yearMonthFormatter).toString(),
                operation = invoiceItem1.operation,
                type = invoiceItem1.type,
                notes = invoiceItem1.notes,
                value = BigDecimal("120.00"),
                unit = InvoiceItemValueUnit.ABSOLUTE,
                status = invoiceItem1.status,
                createdAt = invoiceItem1.createdAt
            )))

        val beneficiary = TestModelFactory.buildBeneficiary(companySubContractId = UUID.randomUUID())

        coEvery { beneficiaryService.findByPersonId(personId) } returns beneficiary.success()

        coEvery { invoiceItemService.listActiveInvoiceItemsBySubcontractId(beneficiary.companySubContractId!!) } returns listOf(invoiceItem1).success()

        coEvery { invoiceItemService.listInvoiceItemsByPerson(person.id) } returns listOf(invoiceItem2).success()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/people/$personId/invoice_items") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#createInvoiceItems should return 401 Unauthorized when user is not authenticated`() {
        val personId = RangeUUID.generate()

        val request = CreateInvoiceItemsRequest(
            fromDate = "2021-02",
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemTypeRequest.COPAY,
            notes = "anything",
            unit = InvoiceItemValueUnit.PERCENTAGE,
            value = BigDecimal("20.0"),
            recurringTimes = 2
        )

        post("/people/$personId/invoice_items", body = request) {
            assertThat(it).isUnauthorized()
        }
    }

    @Test
    fun `#createInvoiceItems should return 200 OK and body as expected`() {
        val person = TestModelFactory.buildPerson()
        val personId = person.id

        val request = CreateInvoiceItemsRequest(
            fromDate = "2021-02",
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemTypeRequest.COPAY,
            notes = "anything",
            unit = InvoiceItemValueUnit.PERCENTAGE,
            value = BigDecimal("20.0"),
            recurringTimes = 2
        )

        val invoiceItem1 = InvoiceItem(
            referenceDate = LocalDate.parse("2021-02-01"),
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.COPAY,
            notes = "anything",
            status = InvoiceItemStatus.ACTIVE,
            personId = personId,
            absoluteValue = BigDecimal("20.0"))

        val invoiceItem2 = InvoiceItem(
            referenceDate = LocalDate.parse("2021-03-01"),
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.COPAY,
            notes = "anything",
            status = InvoiceItemStatus.ACTIVE,
            personId = personId,
            absoluteValue = BigDecimal("20.0"))

        val expected1 = InvoiceItemResponse(
            id = invoiceItem1.id,
            referenceDate = invoiceItem1.referenceDate.format(yearMonthFormatter).toString(),
            operation = invoiceItem1.operation,
            type = invoiceItem1.type,
            notes = invoiceItem1.notes,
            value = invoiceItem1.absoluteValue!!,
            unit = InvoiceItemValueUnit.ABSOLUTE,
            status = invoiceItem1.status,
            createdAt = invoiceItem1.createdAt
        )

        val expected2 = InvoiceItemResponse(
            id = invoiceItem2.id,
            referenceDate = invoiceItem2.referenceDate.format(yearMonthFormatter).toString(),
            operation = invoiceItem2.operation,
            type = invoiceItem2.type,
            notes = invoiceItem2.notes,
            value = invoiceItem2.absoluteValue!!,
            unit = InvoiceItemValueUnit.ABSOLUTE,
            status = invoiceItem2.status,
            createdAt = invoiceItem2.createdAt
        )


        coEvery { invoiceItemService.createInvoiceItems(match {it.size == 2 && it[0].referenceDate == LocalDate.parse("2021-02-01") && it[1].referenceDate == LocalDate.parse("2021-03-01")}) } returns listOf(invoiceItem1, invoiceItem2).success()

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/people/$personId/invoice_items", request) {
                assertThat(it).isOKWithData(InvoiceItemsResponse(listOf(expected1, expected2)))
            }
        }
    }

    @Test
    fun `#cancelInvoiceItem should return 401 Unauthorized when user is not authenticated`() {
        val personId = RangeUUID.generate()
        val invoiceItemId = RangeUUID.generate()

        put("/people/$personId/invoice_items/$invoiceItemId/cancel") {
            assertThat(it).isUnauthorized()
        }
    }

    @Test
    fun `#cancelInvoiceItem should return 200 OK and expected response`() {
        val personId = RangeUUID.generate()
        val invoiceItemId = RangeUUID.generate()

        val invoiceItem = TestModelFactory.buildInvoiceItem()
        coEvery { invoiceItemService.cancel(invoiceItemId) } returns invoiceItem.success()

        val expected = InvoiceItemsConverter.convert(invoiceItem)

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/people/$personId/invoice_items/$invoiceItemId/cancel") {
                assertThat(it).isOKWithData(expected)
            }
        }
    }
}
