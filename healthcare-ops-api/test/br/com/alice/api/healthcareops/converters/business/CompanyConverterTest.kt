package br.com.alice.api.healthcareops.converters.business

import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toBillingAccountableParty
import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompany
import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompanyInfoResponse
import br.com.alice.api.healthcareops.converters.ProductConverter
import br.com.alice.api.healthcareops.models.CompanyInfoResponse
import br.com.alice.api.healthcareops.models.CreateCompanyRequest
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import com.github.kittinunf.result.success
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class CompanyConverterTest {
    private val request = CreateCompanyRequest(
        name = "Acme",
        legalName = "Acme LTDA",
        cnpj = "**************-01",
        email = "<EMAIL>",
        phoneNumber = "(11)*********",
        addressPostalCode = "12345-123",
        addressStreet = "Acme street",
        addressNumber = 500,
        addressCity = "Sao Paulo",
        addressState = "SP",
    )

    @Test
    fun `#toCompanyInfoResponse should convert expected`() {
        val product = TestModelFactory.buildProduct()
        val company =
            TestModelFactory.buildCompany(availableProducts = listOf(product.id), defaultProductId = product.id)

        val expected = CompanyInfoResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountablePartyId = company.billingAccountablePartyId,
            availableProducts = listOf(ProductConverter.convert(product)),
            defaultProductId = ProductConverter.convert(product),
            status = company.status,
        )

        val result = company.toCompanyInfoResponse(listOf(product)).success()

        assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#CreateCompanyRequest_toBillingAccountableParty - convert correctly`() {
        val result = request.toBillingAccountableParty()
        val expectedResult = BillingAccountableParty(
            firstName = request.name,
            lastName = request.legalName,
            type = BillingAccountablePartyType.LEGAL_PERSON,
            nationalId = request.cnpj,
            email = request.email,
            address = Address(
                state = State.SP,
                city = request.addressCity,
                street = request.addressStreet,
                number = request.addressNumber.toString(),
                complement = request.addressComplement,
                postalCode = request.addressPostalCode
            )
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expectedResult)
    }

    @Test
    fun `#CreateCompanyRequest_toCompany - convert correctly`() {
        val result = request.toCompany()

        val expectedAddress = CompanyAddress(
            State = request.addressState,
            city = request.addressCity,
            street = request.addressStreet,
            number = request.addressNumber,
            complement = request.addressComplement,
            postalCode = request.addressPostalCode
        )

        val expectedResult = Company(
            parentId = request.parentId,
            externalCode = request.externalCode,
            name = request.name,
            legalName = request.legalName,
            cnpj = request.cnpj,
            email = request.email,
            phoneNumber = request.phoneNumber,
            address = expectedAddress,
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expectedResult)
    }

}
