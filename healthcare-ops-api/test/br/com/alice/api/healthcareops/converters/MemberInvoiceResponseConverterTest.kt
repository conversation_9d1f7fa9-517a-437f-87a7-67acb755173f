package br.com.alice.api.healthcareops.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceItemResponse
import br.com.alice.data.layer.models.InvoicePaymentResponse
import br.com.alice.data.layer.models.MemberInvoiceResponse
import br.com.alice.data.layer.models.MemberInvoicesResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class MemberInvoiceResponseConverterTest {
    private val memberId = RangeUUID.generate()
    private val invoiceId = RangeUUID.generate()

    private val invoicePaymentId1 = RangeUUID.generate()
    private val invoicePaymentId2 = RangeUUID.generate()

    private val invoiceItem = TestModelFactory.buildInvoiceItem()

    private val invoicePayment1 = TestModelFactory.buildInvoicePayment(
        memberInvoiceIds = listOf(invoiceId),
        id = invoicePaymentId1,
        paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePaymentId1)
    )
    private val invoicePayment2 = TestModelFactory.buildInvoicePayment(
        memberInvoiceIds = listOf(invoiceId),
        id = invoicePaymentId2,
        paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentId = invoicePaymentId2)
    )

    private val invoicePayments = listOf(invoicePayment1, invoicePayment2)

    private val memberInvoiceWithPayments = TestModelFactory.buildMemberInvoiceWithPayments(
        memberId = memberId,
        invoicePayments = invoicePayments,
        invoiceItems = listOf(invoiceItem),
    )

    @Test
    fun `#convert a memberInvoice should convert to MemberInvoiceResponse as expected`() {
        val expectedInvoiceItem = InvoiceItemResponse(
            id = invoiceItem.id,
            referenceDate = invoiceItem.referenceDate.format(yearMonthFormatter).toString(),
            operation = invoiceItem.operation,
            type = invoiceItem.type,
            notes = invoiceItem.notes,
            value = invoiceItem.value,
            unit = invoiceItem.unit,
            status = invoiceItem.status,
            createdAt = invoiceItem.createdAt
        )

        val expectedPayments = listOf(
            InvoicePaymentResponse(
                amount = invoicePayment1.amount,
                approvedAt = invoicePayment1.approvedAt,
                status = invoicePayment1.status,
                method = invoicePayment1.method,
                canceledReason = invoicePayment1.canceledReason,
                externalId = invoicePayment1.externalId,
                source = invoicePayment1.source,
                failReason = invoicePayment1.failReason,
                id = invoicePayment1.id,
                paymentDetail = invoicePayment1.paymentDetail
            ),
            InvoicePaymentResponse(
                amount = invoicePayment2.amount,
                approvedAt = invoicePayment2.approvedAt,
                status = invoicePayment2.status,
                method = invoicePayment2.method,
                canceledReason = invoicePayment2.canceledReason,
                externalId = invoicePayment2.externalId,
                source = invoicePayment2.source,
                failReason = invoicePayment2.failReason,
                id = invoicePayment2.id,
                paymentDetail = invoicePayment2.paymentDetail
            ),
        )

        val expected = MemberInvoiceResponse(
            id = memberInvoiceWithPayments.id.toString(),
            referenceDate = memberInvoiceWithPayments.referenceDate,
            status = memberInvoiceWithPayments.status,
            paidAt = memberInvoiceWithPayments.paidAt,
            totalAmount = memberInvoiceWithPayments.totalAmount,
            dueDate = memberInvoiceWithPayments.dueDate.toString(),
            payments = expectedPayments,
            invoiceItems = listOf(expectedInvoiceItem)
        )

        val result = MemberInvoiceResponseConverter.convert(memberInvoiceWithPayments)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#convert a list of memberInvoice should convert to MemberInvoicesResponse as expected`() {
        val memberInvoiceList = listOf(memberInvoiceWithPayments)

        val expectedPayments = listOf(
            InvoicePaymentResponse(
                amount = invoicePayment1.amount,
                approvedAt = invoicePayment1.approvedAt,
                status = invoicePayment1.status,
                method = invoicePayment1.method,
                canceledReason = invoicePayment1.canceledReason,
                externalId = invoicePayment1.externalId,
                source = invoicePayment1.source,
                failReason = invoicePayment1.failReason,
                id = invoicePayment1.id,
                paymentDetail = invoicePayment1.paymentDetail
            ),
            InvoicePaymentResponse(
                amount = invoicePayment2.amount,
                approvedAt = invoicePayment2.approvedAt,
                status = invoicePayment2.status,
                method = invoicePayment2.method,
                canceledReason = invoicePayment2.canceledReason,
                externalId = invoicePayment2.externalId,
                source = invoicePayment2.source,
                failReason = invoicePayment2.failReason,
                id = invoicePayment2.id,
                paymentDetail = invoicePayment2.paymentDetail
            ),
        )

        val expectedInvoiceItem = InvoiceItemResponse(
            id = invoiceItem.id,
            referenceDate = invoiceItem.referenceDate.format(yearMonthFormatter).toString(),
            operation = invoiceItem.operation,
            type = invoiceItem.type,
            notes = invoiceItem.notes,
            value = invoiceItem.value,
            unit = invoiceItem.unit,
            status = invoiceItem.status,
            createdAt = invoiceItem.createdAt
        )

        val expected = MemberInvoicesResponse(
            memberInvoices = listOf(
                MemberInvoiceResponse(
                    id = memberInvoiceWithPayments.id.toString(),
                    referenceDate = memberInvoiceWithPayments.referenceDate,
                    status = memberInvoiceWithPayments.status,
                    paidAt = memberInvoiceWithPayments.paidAt,
                    totalAmount = memberInvoiceWithPayments.totalAmount,
                    payments = expectedPayments,
                    dueDate = memberInvoiceWithPayments.dueDate.toString(),
                    invoiceItems = listOf(expectedInvoiceItem)
                )
            )
        )

        val result = MemberInvoiceResponseConverter.convert(memberInvoiceList)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#convert a list of memberInvoice without payments should convert to MemberInvoicesResponse as expected`() {
        val memberInvoice = TestModelFactory.buildMemberInvoice(
            invoiceItems = listOf(invoiceItem),
        )

        val expectedInvoiceItem = InvoiceItemResponse(
            id = invoiceItem.id,
            referenceDate = invoiceItem.referenceDate.format(yearMonthFormatter).toString(),
            operation = invoiceItem.operation,
            type = invoiceItem.type,
            notes = invoiceItem.notes,
            value = invoiceItem.value,
            unit = invoiceItem.unit,
            status = invoiceItem.status,
            createdAt = invoiceItem.createdAt
        )

        val expected =  MemberInvoiceResponse(
            id = memberInvoice.id.toString(),
            referenceDate = memberInvoice.referenceDate,
            status = memberInvoice.status,
            paidAt = memberInvoice.paidAt,
            totalAmount = memberInvoice.totalAmount,
            dueDate = memberInvoice.dueDate.toString(),
            payments = emptyList(),
            invoiceItems = listOf(expectedInvoiceItem),
        )

        val result = MemberInvoiceResponseConverter.convert(memberInvoice)

        assertThat(result).isEqualTo(expected)
    }
}
