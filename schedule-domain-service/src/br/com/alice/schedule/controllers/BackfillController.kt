package br.com.alice.schedule.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.integrations.googlecalendar.GoogleCalendarApi
import br.com.alice.schedule.services.AppointmentScheduleServiceImpl
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import com.github.kittinunf.result.map
import com.google.api.services.calendar.model.ConferenceData
import com.google.api.services.calendar.model.ConferenceSolution
import com.google.api.services.calendar.model.ConferenceSolutionKey
import com.google.api.services.calendar.model.CreateConferenceRequest
import com.google.api.services.calendar.model.EntryPoint
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackfillController(
    private val googleCalendarApi: GoogleCalendarApi,
    private val googleCalendarEventsService: GoogleCalendarEventServiceImpl,
    private val appointmentScheduleService: AppointmentScheduleServiceImpl,
) : Controller() {

    suspend fun updateAppointmentScheduleStatusAccordingToTask(payload: AppointmentAdjustmentPayload): Response {
        logger.info(
            message = "BackfillController::updateAppointmentScheduleStatusAccordingToTask::Started execution"
        )
        val successCount = AtomicInteger(0)
        val errorCount = AtomicInteger(0)

        payload.ids.chunked(payload.chunk).map {
            val filter = AppointmentScheduleFilter(
                status = listOf(AppointmentScheduleStatus.SCHEDULED),
                endDate = LocalDate.now().minusDays(1),
                ids = it
            )

            appointmentScheduleService.findBy(filter)
                .map { schedules ->
                    schedules.pmap { schedule ->
                        val updated = schedule.markAsNoShow()
                        appointmentScheduleService.update(updated)
                            .then { updatedAppointmentSchedule ->
                                successCount.getAndIncrement()
                            }
                            .thenError { exception ->
                                errorCount.getAndIncrement()
                                logger.error(
                                    message = "BackfillController::updateAppointmentScheduleStatusAccordingToTask",
                                    exception
                                )
                            }
                    }
                }.get()
        }

        logger.info(
            message = "BackfillController::updateAppointmentScheduleStatusAccordingToTask",
            "report" to "Execution report: Success - $successCount | Error - $errorCount"
        )

        return Response.OK
    }

    suspend fun createGoogleCalendarMeet(payload: GoogleMeetPayload): Response {
        logger.info(
            "BackfillController::createGoogleCalendarMeet",
            "externalCalendarEventId" to payload.externalCalendarEventExternalId,
        )
        googleCalendarApi.getCalendarService(payload.refreshToken).map { googleCalendarEventsService ->
            val event = googleCalendarEventsService.get("primary", payload.externalCalendarEventExternalId)?.execute()
            event?.conferenceData = buildConferenceData(payload.appointmentScheduleId.toString())
            event?.location = ""
            val linkPattern = "<a(.*?)</a>".toRegex()
            event?.description = event?.description?.replace(linkPattern, "")

            val zoomPattern = "google meet".toRegex()
            event?.description = event?.description?.replace(zoomPattern, "Google Meet")

            val eventUpdated = googleCalendarEventsService.update(
                "primary",
                payload.externalCalendarEventExternalId,
                event
            )?.setConferenceDataVersion(1)
                ?.setSendUpdates("all")
                ?.execute()

            logger.info(
                "BackfillController::createGoogleCalendarMeet updated",
                "externalCalendarEventId" to payload.externalCalendarEventExternalId,
                "event_updated_google_meet" to eventUpdated?.conferenceData
            )
        }.get()

        return Response.OK
    }

    suspend fun removeFromGoogleCalendarByAppointmentScheduleId(appointmentScheduleId: UUID): Response =
        googleCalendarEventsService.removeFromGoogleCalendar(appointmentScheduleId).toResponse()

    private fun buildConferenceData(id: String): ConferenceData {
        val createConferenceRequest = CreateConferenceRequest()
        createConferenceRequest.requestId = id

        val conferenceSolutionKey = ConferenceSolutionKey()
        conferenceSolutionKey.type = "hangoutsMeet"

        val conferenceSolution = ConferenceSolution()
        conferenceSolution.key = conferenceSolutionKey

        val entryPointType = "video"
        val entryPoints = listOf<EntryPoint>(
            EntryPoint().setEntryPointType(entryPointType)
        )

        val conferenceData = ConferenceData()
        conferenceData.conferenceSolution = conferenceSolution
        conferenceData.entryPoints = entryPoints
        conferenceData.createRequest = createConferenceRequest

        return conferenceData
    }
}

data class GoogleMeetPayload(
    val externalCalendarEventExternalId: String,
    val refreshToken: String,
    val appointmentScheduleId: UUID,
)

data class AppointmentAdjustmentPayload(
    val ids: List<UUID>,
    val chunk: Int
)

data class FillAppointmentScheduleStaffIdRequest(
    val limit: Int,
    val offset: Int,
)

data class FillAppointmentScheduleStaffIdResponse(
    val successCount: Int,
    val errorCount: Int,
    val errors: List<String>,
)
