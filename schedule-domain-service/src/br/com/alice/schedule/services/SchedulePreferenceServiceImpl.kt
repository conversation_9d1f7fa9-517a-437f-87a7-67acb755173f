package br.com.alice.schedule.services

import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.SchedulePreference
import br.com.alice.data.layer.services.SchedulePreferenceModelDataService
import br.com.alice.schedule.client.SchedulePreferenceService
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.converters.toTransport
import br.com.alice.schedule.model.events.SchedulePreferenceCreatedEvent
import br.com.alice.schedule.model.events.ZoomRefreshTokenUpdatedEvent
import br.com.alice.schedule.services.internal.ZoomService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class SchedulePreferenceServiceImpl(
    private val schedulePreferenceModelDataService: SchedulePreferenceModelDataService,
    private val zoomService: ZoomService,
    private val kafkaProducerService: KafkaProducerService
) : SchedulePreferenceService {

    override suspend fun getByStaffId(staffId: UUID) =
        schedulePreferenceModelDataService.findOne {
            where { this.staffId.eq(staffId) }
        }.map { it.toTransport() }

    override suspend fun getOrCreate(staffId: UUID): Result<SchedulePreference, Throwable> =
        schedulePreferenceModelDataService.findOneOrNull { where { this.staffId.eq(staffId) } }?.toTransport()?.success()
            ?: create(
                SchedulePreference(
                    staffId = staffId,
                    weeklyHours = 0,
                    intervalBetweenEvents = 0,
                )
            )

    override suspend fun getByIds(ids: List<UUID>): Result<List<SchedulePreference>, Throwable> =
        schedulePreferenceModelDataService.find {
            where { this.id.inList(ids) }
        }.mapEach { it.toTransport() }

    override suspend fun update(model: SchedulePreference): Result<SchedulePreference, Throwable> =
        schedulePreferenceModelDataService.update(model.toModel()).map { it.toTransport() }

    override suspend fun getAuthorizedByStaffIdsThatHasStaffSchedules(
        staffIds: List<UUID>
    ): Result<List<SchedulePreference>, Throwable> =
        schedulePreferenceModelDataService.find {
            where {
                this.staffId.inList(staffIds) and
                        this.googleRefreshToken.isNotNull() and
                        this.hasStaffSchedules.eq(true) and
                        this.status.eq(Status.ACTIVE)
            }
        }.mapEach { it.toTransport() }

    override suspend fun getAndStoreZoomRefreshToken(
        authorizationCode: String,
        staffId: UUID
    ): Result<SchedulePreference, Throwable> =
        zoomService.fetchZoomRefreshToken(authorizationCode, staffId).let { refreshToken ->
            getOrCreate(staffId)
                .flatMap { schedulePreferenceModelDataService.update(it.copy(zoomRefreshToken = refreshToken).toModel()) }
        }.map { it.toTransport() }

    override suspend fun getOnlyWithCalendar(ids: List<UUID>): Result<List<SchedulePreference>, Throwable> =
        schedulePreferenceModelDataService.find {
            where {
                this.id.inList(ids) and
                        this.googleRefreshToken.isNotNull()
            }
        }.mapEach { it.toTransport() }

    suspend fun create(model: SchedulePreference): Result<SchedulePreference, Throwable> =
        schedulePreferenceModelDataService.add(model.toModel())
            .map { it.toTransport() }
            .then {
                kafkaProducerService.produce(SchedulePreferenceCreatedEvent(it))
            }

    suspend fun getByGoogleCalendarWebhookChannelId(channelId: UUID): Result<SchedulePreference, Throwable> =
        schedulePreferenceModelDataService.findOne {
            where {
                this.googleCalendarWebhookChannelId.eq(channelId)
            }
        }.map { it.toTransport() }

    suspend fun getByGoogleCalendarWebhookCloseToExpiration(referenceDate: LocalDate): Result<List<SchedulePreference>, Throwable> {
        val referencePeriodEnd = referenceDate.plusDays(1).atEndOfTheDay()
        return schedulePreferenceModelDataService.find {
            where {
                this.googleCalendarWebhookExpiration.less(referencePeriodEnd) and
                        this.status.eq(Status.ACTIVE)
            }
        }.mapEach { it.toTransport() }
    }

    suspend fun getZoomMeetingForStaff(
        startTime: LocalDateTime,
        staffId: UUID,
        numberOfSessions: Int
    ): Result<String, Throwable> {
        return this.getByStaffId(staffId = staffId).flatMap { schedulePreference ->
            schedulePreference.zoomRefreshToken?.let {
                logger.info(
                    "SchedulePreferenceServiceImpl::getZoomMeetingForStaff will create zoom meeting",
                    "staff_id" to staffId
                )
                val refreshTokenResponse = zoomService.refreshToken(it, staffId)

                kafkaProducerService.produce(
                    ZoomRefreshTokenUpdatedEvent(refreshTokenResponse.refreshToken, staffId), staffId.toString()
                )

                val meeting = zoomService.createMeeting(refreshTokenResponse.accessToken, startTime, numberOfSessions)

                logger.info(
                    "SchedulePreferenceServiceImpl::getZoomMeetingForStaff meeting generated",
                    "staff_id" to staffId,
                    "meeting" to meeting,
                )

                meeting.success()
            } ?: run {
                logger.info(
                    "SchedulePreferenceServiceImpl::getZoomMeetingForStaff no refresh token to create zoom meeting",
                    "staff_id" to staffId
                )
                schedulePreference.zoomLink.orEmpty().success()
            }
        }
    }
}
