package br.com.alice.schedule.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.AppointmentReminder
import br.com.alice.data.layer.models.AppointmentReminderModel

object AppointmentReminderConverter : Converter<AppointmentReminderModel, AppointmentReminder>(
    AppointmentReminderModel::class,
    AppointmentReminder::class,
)

fun AppointmentReminder.toModel() = AppointmentReminderConverter.unconvert(this)
fun AppointmentReminderModel.toTransport() = AppointmentReminderConverter.convert(this)
