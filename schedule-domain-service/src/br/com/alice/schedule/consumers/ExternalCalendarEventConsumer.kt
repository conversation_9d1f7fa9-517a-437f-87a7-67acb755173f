package br.com.alice.schedule.consumers

import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.events.AppointmentSchedulePersonPayload
import br.com.alice.schedule.model.events.ExternalCalendarEventCreated
import br.com.alice.schedule.model.events.ExternalCalendarEventUpdated
import br.com.alice.schedule.model.events.Professional
import br.com.alice.schedule.services.internal.AppointmentScheduledNotifier
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ExternalCalendarEventConsumer(
    private val appointmentScheduleService: AppointmentScheduleService,
    private val appointmentScheduledNotifier: AppointmentScheduledNotifier,
    private val staffService: StaffService,
    private val personService: PersonService
) : Consumer() {

    suspend fun cancelAppointmentScheduleIfNeeded(event: ExternalCalendarEventUpdated) =
        withSubscribersEnvironment {
            val externalCalendarEvent = event.payload.externalCalendarEvent

            if (externalCalendarEvent.canCancelAppointmentSchedule()) {
                logger.info(
                    "ExternalCalendarEventConsumer::cancelAppointmentScheduleIfNeeded",
                    "external_calendar_event_id" to externalCalendarEvent.id
                )
                appointmentScheduleService.cancel(
                    id = externalCalendarEvent.appointmentScheduleId!!,
                    appointmentScheduleCancelledByType = AppointmentScheduleCancelledByType.HOST
                ).coFoldNotFound { false.success() }
            } else false.success()
        }

    suspend fun updateAppointmentScheduleAndNotifyAttendants(
        externalCalendarEventCreated: ExternalCalendarEventCreated
    ) = withSubscribersEnvironment {
        val externalCalendarEvent = externalCalendarEventCreated.payload.externalCalendarEvent
        val googleMeetLink = externalCalendarEventCreated.payload.googleMeetLink

        logger.info(
            "ExternalCalendarEventConsumer::updateAppointmentScheduleAndNotifyAttendants will update event",
            "event_id" to externalCalendarEvent.id,
            "google_meet_link" to googleMeetLink
        )
        if (!shouldUseGoogleMeet()) return@withSubscribersEnvironment true.success()

        coroutineScope {
            externalCalendarEvent.appointmentScheduleId?.let {
                val appointmentScheduleDeferred = async { appointmentScheduleService.get(it).get() }
                val staffDeferred = async { staffService.get(externalCalendarEvent.staffId).get() }

                val appointmentSchedule = appointmentScheduleDeferred.await().let {
                    if (googleMeetLink.isNullOrBlank()) it
                    else appointmentScheduleService.update(it.copy(location = googleMeetLink)).get()
                }

                val personDeferred = async { personService.get(appointmentSchedule.personId).get() }

                appointmentScheduledNotifier.notifyAttendants(
                    appointmentSchedule = appointmentSchedule,
                    professional = Professional.from(staffDeferred.await()),
                    person = AppointmentSchedulePersonPayload.from(personDeferred.await())
                ).success()
            }
        }
        true.success()
    }

    suspend fun updateAppointmentSchedule(
        externalCalendarEventUpdated: ExternalCalendarEventUpdated
    ) = span("updateAppointmentSchedule") { span ->
        withSubscribersEnvironment {
            val appointmentScheduleId = externalCalendarEventUpdated.payload.externalCalendarEvent.appointmentScheduleId
            val googleMeetLink = externalCalendarEventUpdated.payload.googleMeetLink
            val externalCalendarEventId = externalCalendarEventUpdated.payload.externalCalendarEvent.id.toString()

            span.setAttribute("external_calendar_event_id", externalCalendarEventId)
            span.setAttribute("google_meet_link", googleMeetLink.orEmpty())
            span.setAttribute("appointment_schedule_id", appointmentScheduleId.toString())

            if (googleMeetLink.isNullOrEmpty()) return@withSubscribersEnvironment false.success()

            appointmentScheduleId?.let {
                appointmentScheduleService.get(appointmentScheduleId).flatMap { appointmentSchedule ->
                    if (googleMeetLink != appointmentSchedule.location)
                        appointmentScheduleService.update(appointmentSchedule.copy(location = googleMeetLink))
                    else false.success()
                }
            } ?: false.success()
        }
    }

    private fun shouldUseGoogleMeet() = FeatureService.get(
        namespace = FeatureNamespace.SCHEDULE,
        key = "should_use_google_meet_to_generate_digital_links",
        defaultValue = false
    )

}
