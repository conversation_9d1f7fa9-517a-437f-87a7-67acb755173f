package br.com.alice.schedule.consumers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentReminder
import br.com.alice.schedule.model.events.AppointmentReminderSentEvent
import br.com.alice.schedule.services.internal.AppointmentReminderService
import com.github.kittinunf.result.Result

class AppointmentReminderConsumer(
    private val appointmentReminderService: AppointmentReminderService
): Consumer() {
    suspend fun createAppointmentReminder(appointmentReminderSentEvent: AppointmentReminderSentEvent): Result<Any, Throwable> {
        val appointmentScheduleId = appointmentReminderSentEvent.payload.appointmentScheduleId
        val provider = appointmentReminderSentEvent.payload.provider
        val notificationTime = appointmentReminderSentEvent.payload.notificationTime

        logger.info("AppointmentReminderConsumer::createAppointmentReminder",
            "appointment_schedule_id" to appointmentScheduleId,
            "provider" to provider,
            "notification_time" to notificationTime,
        )
        return withSubscribersEnvironment {
            appointmentReminderService.add(
                AppointmentReminder(
                    appointmentScheduleId = appointmentScheduleId,
                    provider = provider,
                    notificationTime = notificationTime,
                )
            )
        }
    }
}
