package br.com.alice.schedule.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleCheckInService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentSchedulePersonPayload
import br.com.alice.schedule.model.events.HubspotTriggerAppointmentScheduleCanceledEvent
import br.com.alice.schedule.services.internal.AppointmentScheduledNotifier
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.BeforeTest
import org.junit.jupiter.api.BeforeAll
import io.mockk.unmockkAll
import java.time.LocalDateTime

class AppointmentScheduleCancelledConsumerTest : ConsumerTest() {

    private val staffService: StaffService = mockk()
    private val personService: PersonService = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val appointmentScheduledNotifier: AppointmentScheduledNotifier = mockk()
    private val appointmentScheduleCheckInService: AppointmentScheduleCheckInService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val appointmentScheduleCancelledConsumer = AppointmentScheduleCancelledConsumer(
        appointmentScheduledNotifier, appointmentScheduleService, staffService, personService, appointmentScheduleCheckInService, kafkaProducerService
    )

    private val person = TestModelFactory.buildPerson()
    private val staff = TestModelFactory.buildStaff()
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
        staffId = staff.id
    )

    private val personPayload = AppointmentSchedulePersonPayload(
        person.id,
        person.firstName,
        person.contactName,
        person.email,
        person.fullSocialName,
    )

    private val event = AppointmentScheduleCancelledEvent(appointmentSchedule)

    companion object {
        val dateTime = LocalDateTime.now()
        val uuid = RangeUUID.generate()

        @BeforeAll
        @JvmStatic
        fun classSetup() {
            unmockkAll()
        }
    }

    @BeforeTest
    override fun before() {
        super.before()

        mockkStatic(LocalDateTime::class)
        mockkObject(RangeUUID)
        every { LocalDateTime.now() } returns dateTime
        every { RangeUUID.generate() } returns uuid
    }

    @Test
    fun `#notifyAttendants should call method to notifyAttendants`() = runBlocking {

        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule.success()
        coEvery { personService.get(appointmentSchedule.personId) } returns person.success()
        coEvery { staffService.get(appointmentSchedule.staffId!!) } returns staff.success()
        coEvery { appointmentScheduledNotifier.notifyAttendantsCancelAppointmentSchedule(
            appointmentSchedule,
            staff,
            personPayload,
        ) } returns mockk()

        val result = appointmentScheduleCancelledConsumer.notifyAttendants(event)

        assertThat(result).isSuccess()

        coVerify(exactly = 1) {
            appointmentScheduledNotifier.notifyAttendantsCancelAppointmentSchedule(
                appointmentSchedule,
                staff,
                personPayload
            )
        }
    }

    @Test
    fun `#dropAppointmentScheduleCheckIn should call method to dropAppointmentScheduleCheckIn`() = runBlocking {
        val appointmentScheduleCheckIn = TestModelFactory.buildAppointmentScheduleCheckIn(appointmentScheduleId = appointmentSchedule.id)
        val droppedAppointmentScheduleCheckin = appointmentScheduleCheckIn.copy(status = AppointmentScheduleCheckInStatus.DROPPED_BY_CANCEL)
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns listOf(appointmentScheduleCheckIn).success()
        coEvery { appointmentScheduleCheckInService.update(droppedAppointmentScheduleCheckin) } returns droppedAppointmentScheduleCheckin.success()

        val result = appointmentScheduleCancelledConsumer.dropAppointmentScheduleCheckIn(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
            appointmentScheduleCheckInService.update(any())
        }
    }

    @Test
    fun `#dropAppointmentScheduleCheckIn should call method to dropAppointmentScheduleCheckIn and do not drop when status is not SENT`() = runBlocking {
        val appointmentScheduleCheckIn = TestModelFactory.buildAppointmentScheduleCheckIn(appointmentScheduleId = appointmentSchedule.id, status = AppointmentScheduleCheckInStatus.DROPPED_BY_CANCEL)
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns listOf(appointmentScheduleCheckIn.copy(status = AppointmentScheduleCheckInStatus.CONFIRMED)).success()

        val result = appointmentScheduleCancelledConsumer.dropAppointmentScheduleCheckIn(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
        }
    }

    @Test
    fun `#dropAppointmentScheduleCheckIn should call method to dropAppointmentScheduleCheckIn when reschedule`() = runBlocking {
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule.copy(cancelledByType = AppointmentScheduleCancelledByType.RESCHEDULE))
        val appointmentScheduleCheckIn = TestModelFactory.buildAppointmentScheduleCheckIn(appointmentScheduleId = appointmentSchedule.id)
        val droppedAppointmentScheduleCheckin = appointmentScheduleCheckIn.copy(status = AppointmentScheduleCheckInStatus.DROPPED_BY_RESCHEDULE)
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns listOf(appointmentScheduleCheckIn).success()
        coEvery { appointmentScheduleCheckInService.update(droppedAppointmentScheduleCheckin) } returns droppedAppointmentScheduleCheckin.success()

        val result = appointmentScheduleCancelledConsumer.dropAppointmentScheduleCheckIn(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
            appointmentScheduleCheckInService.update(any())
        }
    }

    @Test
    fun `#dropAppointmentScheduleCheckIn should return success when appointment schedule is not found`() = runBlocking {
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns NotFoundException("").failure()

        val result = appointmentScheduleCancelledConsumer.dropAppointmentScheduleCheckIn(event)

        assertThat(result).isSuccessWithData(true)
        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
        }
    }

    @Test
    fun `#dropAppointmentScheduleCheckIn should return error when appointment schedule get throws an exception`() = runBlocking {
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns Exception("").failure()

        val result = appointmentScheduleCancelledConsumer.dropAppointmentScheduleCheckIn(event)

        assertThat(result).isFailure()
        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
        }
    }

    @Test
    fun `#triggerHubspotCanceledEvent should call method to triggerHubspotCanceledEvent`() = runBlocking {
        val appointmentSchedule = appointmentSchedule.copy(type = AppointmentScheduleType.HEALTH_DECLARATION, scheduledByInternalScheduler = true)
        val hubspotTriggerAppointmentScheduleCanceledEvent = HubspotTriggerAppointmentScheduleCanceledEvent(appointmentSchedule)
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule.success()
        coEvery { kafkaProducerService.produce(hubspotTriggerAppointmentScheduleCanceledEvent) } returns mockk()

        val result = appointmentScheduleCancelledConsumer.triggerHubspotCanceledEvent(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleService.get(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#triggerHubspotCanceledEvent should return true when appointment schedule is not health declaration`() = runBlocking {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.TEST)
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule)
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule.success()

        val result = appointmentScheduleCancelledConsumer.triggerHubspotCanceledEvent(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleService.get(any())
        }
    }

    @Test
    fun `#triggerHubspotCanceledEvent should return true when appointment schedule is not scheduled by internal scheduler`() = runBlocking {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule().copy(scheduledByInternalScheduler = false)
        val event = AppointmentScheduleCancelledEvent(appointmentSchedule)
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule.success()

        val result = appointmentScheduleCancelledConsumer.triggerHubspotCanceledEvent(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            appointmentScheduleService.get(any())
        }
    }

}
