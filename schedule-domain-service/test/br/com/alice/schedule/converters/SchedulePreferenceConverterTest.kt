package br.com.alice.schedule.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.SchedulePreference
import br.com.alice.data.layer.models.SchedulePreferenceModel
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDateTime
import kotlin.test.Test

class SchedulePreferenceConverterTest {

    private val schedulePreference = SchedulePreference(
        staffId = RangeUUID.generate(),
        healthProfessionalId = RangeUUID.generate(),
        intervalBetweenEvents = 30,
        weeklyHours = 40,
        googleRefreshToken = "googleRefreshToken",
        googleCalendarLastUpdated = LocalDateTime.now(),
        googleNextSyncToken = "googleNextSyncToken",
        googleCalendarWebhookExpiration = LocalDateTime.now().plusDays(1),
        googleCalendarWebhookChannelId = RangeUUID.generate(),
        zoomLink = "zoomLink",
        zoomRefreshToken = "zoomRefreshToken",
        hasStaffSchedules = true,
        isDoingFullSync = false,
        status = Status.ACTIVE,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        id = RangeUUID.generate(),
        version = 1
    )

    private val schedulePreferenceModel = SchedulePreferenceModel(
        staffId = schedulePreference.staffId,
        healthProfessionalId = schedulePreference.healthProfessionalId,
        intervalBetweenEvents = schedulePreference.intervalBetweenEvents,
        weeklyHours = schedulePreference.weeklyHours,
        googleRefreshToken = schedulePreference.googleRefreshToken,
        googleCalendarLastUpdated = schedulePreference.googleCalendarLastUpdated,
        googleNextSyncToken = schedulePreference.googleNextSyncToken,
        googleCalendarWebhookExpiration = schedulePreference.googleCalendarWebhookExpiration,
        googleCalendarWebhookChannelId = schedulePreference.googleCalendarWebhookChannelId,
        zoomLink = schedulePreference.zoomLink,
        zoomRefreshToken = schedulePreference.zoomRefreshToken,
        hasStaffSchedules = schedulePreference.hasStaffSchedules,
        isDoingFullSync = schedulePreference.isDoingFullSync,
        status = schedulePreference.status,
        createdAt = schedulePreference.createdAt,
        updatedAt = schedulePreference.updatedAt,
        id = schedulePreference.id,
        version = schedulePreference.version
    )

    @Test
    fun testToModel() {
        assertEquals(schedulePreferenceModel, schedulePreference.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(schedulePreference, schedulePreferenceModel.toTransport())
    }
}
