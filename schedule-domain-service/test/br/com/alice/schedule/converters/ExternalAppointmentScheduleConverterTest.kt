package br.com.alice.schedule.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.ExternalAppointmentSchedule
import br.com.alice.data.layer.models.ExternalAppointmentScheduleModel
import br.com.alice.data.layer.models.Professional
import br.com.alice.data.layer.models.ProfessionalModel
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.data.layer.models.ScheduleLocation
import br.com.alice.data.layer.models.ScheduleLocationModel
import br.com.alice.data.layer.models.ScheduleProfessionalCouncil
import br.com.alice.data.layer.models.ScheduleProfessionalCouncilModel
import br.com.alice.data.layer.models.ScheduleProvider
import br.com.alice.data.layer.models.ScheduleStatus
import br.com.alice.data.layer.models.ScheduleType
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDateTime
import kotlin.test.Test

class ExternalAppointmentScheduleConverterTest {

    private val scheduleLocation = ScheduleLocation(
        id = "location123",
        name = "Location Name",
        address = "123 Street",
        mapLink = "http://map.link"
    )

    private val scheduleLocationModel = ScheduleLocationModel(
        id = "location123",
        name = "Location Name",
        address = "123 Street",
        mapLink = "http://map.link"
    )

    private val professional = Professional(
        id = "professional123",
        name = "Professional Name",
        council = ScheduleProfessionalCouncil(
            number = "12345",
            state = State.SP,
            type = "CRP"
        )
    )

    private val professionalModel = ProfessionalModel(
        id = "professional123",
        name = "Professional Name",
        council = ScheduleProfessionalCouncilModel(
            number = "12345",
            state = State.SP,
            type = "CRP"
        )
    )

    private val externalAppointmentSchedule = ExternalAppointmentSchedule(
        id = RangeUUID.generate(),
        personId = PersonId(),
        slotId = "slot123",
        verificationCode = "verification123",
        appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY,
        startTime = LocalDateTime.now(),
        endTime = LocalDateTime.now().plusHours(1),
        provider = ScheduleProvider.EINSTEIN,
        status = ScheduleStatus.SCHEDULED,
        type = ScheduleType.REMOTE,
        location = scheduleLocation,
        professional = professional,
        attendanceLink = "http://attendance.link",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        version = 1,
        isPregnant = false,
        appointmentScheduleId = RangeUUID.generate()
    )

    private val externalAppointmentScheduleModel = ExternalAppointmentScheduleModel(
        id = externalAppointmentSchedule.id,
        personId = externalAppointmentSchedule.personId,
        slotId = externalAppointmentSchedule.slotId,
        verificationCode = externalAppointmentSchedule.verificationCode,
        appointmentType = externalAppointmentSchedule.appointmentType,
        startTime = externalAppointmentSchedule.startTime,
        endTime = externalAppointmentSchedule.endTime,
        provider = externalAppointmentSchedule.provider,
        status = externalAppointmentSchedule.status,
        type = externalAppointmentSchedule.type,
        location = scheduleLocationModel,
        professional = professionalModel,
        attendanceLink = externalAppointmentSchedule.attendanceLink,
        createdAt = externalAppointmentSchedule.createdAt,
        updatedAt = externalAppointmentSchedule.updatedAt,
        version = externalAppointmentSchedule.version,
        isPregnant = externalAppointmentSchedule.isPregnant,
        appointmentScheduleId = externalAppointmentSchedule.appointmentScheduleId
    )

    @Test
    fun testToModel() {
        assertEquals(externalAppointmentScheduleModel, externalAppointmentSchedule.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(externalAppointmentSchedule, externalAppointmentScheduleModel.toTransport())
    }
}
