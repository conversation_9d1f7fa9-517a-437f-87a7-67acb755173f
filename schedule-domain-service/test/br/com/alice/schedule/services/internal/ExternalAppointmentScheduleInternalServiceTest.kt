package br.com.alice.schedule.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.models.State
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory.buildPerson
import br.com.alice.data.layer.models.ExternalAppointmentSchedule
import br.com.alice.data.layer.models.Professional
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.data.layer.models.ScheduleProfessionalCouncil
import br.com.alice.data.layer.models.ScheduleProvider
import br.com.alice.data.layer.models.ScheduleStatus
import br.com.alice.data.layer.models.ScheduleType
import br.com.alice.data.layer.services.ExternalAppointmentScheduleModelDataService
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.model.events.ExternalAppointmentScheduleUpsertEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


class ExternalAppointmentScheduleInternalServiceTest {
    private val dataService: ExternalAppointmentScheduleModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = ExternalAppointmentScheduleInternalService(dataService, kafkaProducerService)
    private val person = buildPerson()
    private val nowDateTime = LocalDateTime.now()
    private val scheduleModel = ExternalAppointmentSchedule(
        attendanceLink = "meet.com.br",
        provider = ScheduleProvider.CIA,
        personId = person.id,
        slotId = RangeUUID.generate().toString(),
        appointmentType = ScheduleAppointmentType.DOCTOR_FAMILY,
        type = ScheduleType.REMOTE,
        location = null,
        professional = Professional("id", "luiz", ScheduleProfessionalCouncil("1", State.SP, "CRM")),
        startTime = nowDateTime,
        endTime = nowDateTime,
        status = ScheduleStatus.SCHEDULED,
        verificationCode = null
    )
    private val producerResult = ProducerResult(LocalDateTime.now(), "1", 1)

    @Test
    fun `#upsert should add new external appointment schedule`() = runBlocking {
        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.personId.eq(scheduleModel.personId) and this.slotId.eq(scheduleModel.slotId) and this.provider.eq(
                        scheduleModel.provider
                    )
                }
            })
        } returns NotFoundException().failure()

        coEvery { dataService.add(scheduleModel.toModel()) } returns scheduleModel.toModel().success()
        coEvery { kafkaProducerService.produce(ExternalAppointmentScheduleUpsertEvent(scheduleModel)) } returns
                producerResult
        val response = service.upsert(scheduleModel)
        ResultAssert.assertThat(response).isSuccessOfType(ExternalAppointmentSchedule::class)

        coVerifyOnce {
            dataService.findOne(any())
            dataService.add(any())
            kafkaProducerService.produce(any())
        }
        coVerifyNone { dataService.update(any()) }

    }

    @Test
    fun `#upsert should update external appointment schedule`() = runBlocking {
        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.personId.eq(scheduleModel.personId) and this.slotId.eq(scheduleModel.slotId) and this.provider.eq(
                        scheduleModel.provider
                    )
                }
            })
        } returns scheduleModel.toModel().success()

        coEvery { dataService.update(scheduleModel.toModel()) } returns scheduleModel.toModel().success()
        coEvery { kafkaProducerService.produce(ExternalAppointmentScheduleUpsertEvent(scheduleModel)) } returns
                producerResult
        val response = service.upsert(scheduleModel)
        ResultAssert.assertThat(response).isSuccessOfType(ExternalAppointmentSchedule::class)

        coVerifyOnce {
            dataService.findOne(any())
            dataService.update(any())
            kafkaProducerService.produce(any())
        }
        coVerifyNone { dataService.add(any()) }
    }

    @Test
    fun `#upsert should return access forbidden exception`() = runBlocking {
        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.personId.eq(scheduleModel.personId) and this.slotId.eq(scheduleModel.slotId) and this.provider.eq(
                        scheduleModel.provider
                    )
                }
            })
        } returns AccessForbiddenException("").failure()


        val response = service.upsert(scheduleModel)
        ResultAssert.assertThat(response).isFailureOfType(AccessForbiddenException::class)
        coVerifyOnce {
            dataService.findOne(any())
        }

        coVerifyNone {
            dataService.update(any())
            dataService.add(any())
            kafkaProducerService.produce(any())
        }

    }
}
