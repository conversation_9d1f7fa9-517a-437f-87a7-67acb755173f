package br.com.alice.schedule.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.schedule.integrations.zoom.ZoomAccessTokenResponse
import br.com.alice.schedule.integrations.zoom.ZoomClient
import br.com.alice.schedule.integrations.zoom.ZoomCreateMeetingResponse
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class ZoomServiceTest {
    private val zoomClient: ZoomClient = mockk()
    private val zoomService = ZoomService(zoomClient)

    private val zoomTokenResponse = ZoomAccessTokenResponse(
        accessToken = "a",
        refreshToken = "b",
        tokenType = "c",
        expiresIn = 10,
        scope = "d"
    )
    private val staffId = RangeUUID.generate()

    @Test
    fun `#fetchZoomRefreshToken should call zoom client to get refresh token response and return only refresh token`() =
        runBlocking {
            val zoomTokenResponse = ZoomAccessTokenResponse(
                accessToken = "a",
                refreshToken = "b",
                tokenType = "c",
                expiresIn = 10,
                scope = "d"
            )

            coEvery { zoomClient.requestRefreshToken("code", staffId) } returns zoomTokenResponse

            val result = zoomService.fetchZoomRefreshToken("code", staffId)
            assertEquals(result, zoomTokenResponse.refreshToken)
        }

    @Test
    fun `#refreshToken should call zoom client to get refresh token response with access token`() =
        runBlocking {
            coEvery { zoomClient.refreshAccessToken("2", staffId) } returns zoomTokenResponse

            val result = zoomService.refreshToken("2", staffId)
            assertEquals(result, zoomTokenResponse)

        }


    @Test
    fun `#createMeeting should create meeting with token`() =
        runBlocking {
            val zoomCreateMeetingResponse = ZoomCreateMeetingResponse(
                joinUrl = "abc",
                startUrl = "cde",
            )

            coEvery {
                zoomClient.createMeeting("z", "2022-01-01T10:00:00Z")
            } returns zoomCreateMeetingResponse

            val result = zoomService.createMeeting(
                "z",
                LocalDateTime.of(2022, 1, 1, 10, 0),
                1
            )

            assertEquals(result, zoomCreateMeetingResponse.joinUrl)
        }
}
