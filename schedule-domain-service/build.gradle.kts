plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.schedule-domain-service"
version = aliceScheduleDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:schedule-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-redis"))
    implementation(project(":common-service"))
    implementation(project(":communication"))
    implementation(project(":appointment-domain-client"))
    implementation(project(":business-domain-client"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:health-plan-domain-service-data-package"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:membership-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:provider-domain-service-data-package"))
	implementation(project(":data-packages:schedule-domain-service-data-package"))
    implementation(project(":data-packages:schedule-domain-service-model-package"))
	implementation(project(":data-packages:wanda-domain-service-data-package"))
    implementation(project(":data-packages:health-condition-domain-service-data-package"))
	implementation(project(":data-packages:marauders-map-domain-service-data-package"))
	implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:questionnaire-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:health-logic-domain-service-data-package"))
    implementation(project(":data-packages:bud-domain-service-data-package"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":health-condition-domain-client"))
    implementation(project(":health-plan-domain-client"))
    implementation(project(":health-logic-domain-client"))
    implementation(project(":marauders-map-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":schedule-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":wanda-domain-client"))

    implementation("org.mnode.ical4j:ical4j:$ical4jVersion")
    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:ses")

    implementation("com.google.api-client:google-api-client:$googleApiClientVersion")
    implementation("com.google.oauth-client:google-oauth-client-jetty:$googleOauthClientJettyVersion")
    implementation("com.google.apis:google-api-services-calendar:$googleApiServicesCalendarVersion")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor2Version")

    ktor2Dependencies()

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
