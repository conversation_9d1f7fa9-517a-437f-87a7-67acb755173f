package br.com.alice.product.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.PriceListingModelDataService
import br.com.alice.product.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PriceListingServiceImplTest {
    private val dataService: PriceListingModelDataService = mockk()
    private val priceListingService = PriceListingServiceImpl(dataService)

    @Test
    fun `#add should call data add`() = runBlocking {
        val priceListing = TestModelFactory.buildPriceListing()
        val priceListingModel = priceListing.toModel()

        coEvery { dataService.add(priceListingModel) } returns priceListingModel.success()
        val result = priceListingService.add(priceListing)

        coVerify(exactly = 1) { dataService.add(priceListingModel) }
        assertThat(result).isSuccessWithData(priceListing)
    }

    @Test
    fun `#delete should call data delete`() = runBlocking {
        val priceListing = TestModelFactory.buildPriceListing()
        val priceListingModel = priceListing.toModel()

        coEvery { dataService.get(priceListing.id) } returns priceListingModel.success()
        coEvery { dataService.delete(priceListingModel) } returns true.success()

        val result = priceListingService.delete(priceListing.id)

        coVerify(exactly = 1) { dataService.delete(priceListingModel) }
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#get should call data get`() = runBlocking {
        val priceListing = TestModelFactory.buildPriceListing()
        val priceListingModel = priceListing.toModel()

        coEvery { dataService.get(priceListing.id) } returns priceListingModel.success()
        val result = priceListingService.get(priceListing.id)

        coVerify(exactly = 1) { dataService.get(priceListing.id) }
        assertThat(result).isSuccessWithData(priceListing)
    }

    @Test
    fun `#list should call data with expected query`() = runBlocking {
        val priceListings = listOf(TestModelFactory.buildPriceListing(), TestModelFactory.buildPriceListing())
        val priceListingModels = priceListings.map { it.toModel() }

        coEvery { dataService.find(queryEq { all() }) } returns priceListingModels.success()

        val result = priceListingService.list()
        assertThat(result).isSuccessWithData(priceListings)
    }
}
