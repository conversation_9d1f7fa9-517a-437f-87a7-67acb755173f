package br.com.alice.product.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.CoPaymentCostInfo
import br.com.alice.data.layer.models.CoPaymentTierPrice
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.services.CoPaymentCostInfoModelDataService
import br.com.alice.product.client.PersonCoPaymentCostInfo
import br.com.alice.product.client.ProductService
import br.com.alice.product.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import kotlin.test.Test

class CoPaymentCostInfoServiceImplTest {
    private val dataService: CoPaymentCostInfoModelDataService = mockk()
    private val productService: ProductService = mockk()
    private val service = CoPaymentCostInfoServiceImpl(
        dataService,
        productService
    )

    private val info = listOf(buildCoPaymentCostInfo(), buildCoPaymentCostInfo())
    private val infoModel = info.map { it.toModel() }

    @Test
    fun `#add should call data add`() = runBlocking {
        val costInfo = buildCoPaymentCostInfo()
        val costInfoModel = costInfo.toModel()

        coEvery { dataService.add(costInfoModel) } returns costInfoModel.success()
        val result = service.add(costInfo)

        coVerifyOnce { dataService.add(any()) }
        assertThat(result).isSuccessWithData(costInfo)
    }

    @Test
    fun `#update should call update sucessfully`() = runBlocking {
        val costInfo = buildCoPaymentCostInfo()
        val costInfoModel = costInfo.toModel()
        val newEventName = "Updated Event"
        val updatedInfo = costInfo.copy(event = newEventName)
        val updatedInfoModel = updatedInfo.toModel()

        coEvery { dataService.get(costInfo.id) } returns costInfoModel.success()
        coEvery { dataService.update(match { it.event == newEventName }) } returns updatedInfoModel.success()

        val result = service.update(updatedInfo)

        coVerifyOnce { dataService.get(any()) }
        coVerifyOnce { dataService.update(any()) }
        assertThat(result).isSuccessWithData(updatedInfo)
    }

    @Test
    fun `#get should call data get`() = runBlocking {
        val costInfo = buildCoPaymentCostInfo()
        val costInfoModel = costInfo.toModel()

        coEvery { dataService.get(costInfo.id) } returns costInfoModel.success()
        val result = service.get(costInfo.id)

        coVerifyOnce { dataService.get(any()) }
        assertThat(result).isSuccessWithData(costInfo)
    }

    @Test
    fun `#findAll should call data with expected query`() = runBlocking {
        val infos = listOf(buildCoPaymentCostInfo(), buildCoPaymentCostInfo())
        val infoModels = infos.map { it.toModel() }

        coEvery {
            dataService.find(queryEq {
                all().orderBy { index }.sortOrder { asc }
            })
        } returns infoModels.success()

        val result = service.findAll()
        assertThat(result).isSuccessWithData(infos)
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#getCoPaymentByProduct should return cost info when tier value is not empty`() = runBlocking {
        val product = TestModelFactory.buildProduct(type = ProductType.B2B, tier = TierType.TIER_1)
        coEvery {
            dataService.find(queryEq {
                all().orderBy { index }.sortOrder { asc }
            })
        } returns infoModel.success()

        coEvery { productService.getProduct(product.id) } returns product.success()

        val result = service.getCoPaymentByProduct(product.id)
        assertThat(result).isSuccessWithData(info.map {
            PersonCoPaymentCostInfo(
                event = it.event,
                description = it.description,
                chargeType = it.chargeType,
                price = it.prices.first { p -> p.tier == TierType.TIER_1 }
            )
        })

        coVerifyOnce {
            dataService.find(any())
            productService.getProduct(any())
        }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#getCoPaymentByProduct should return exception when  tier value empty`() = runBlocking {
        val product = TestModelFactory.buildProduct(type = ProductType.B2B, tier = null)
        coEvery {
            dataService.find(queryEq {
                all().orderBy { index }.sortOrder { asc }
            })
        } returns infoModel.success()

        coEvery { productService.getProduct(product.id) } returns product.success()

        val result = service.getCoPaymentByProduct(product.id)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerifyOnce { productService.getProduct(any()) }
        coVerifyNone { dataService.find(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#getCoPaymentByProductTier should return cost info successfully`() = runBlocking {
        val tier = TierType.TIER_1

        coEvery {
            dataService.find(queryEq {
                all().orderBy { index }.sortOrder { asc }
            })
        } returns infoModel.success()

        val result = service.getCoPaymentByProductTier(tier)
        assertThat(result).isSuccessWithData(info.map {
            PersonCoPaymentCostInfo(
                event = it.event,
                description = it.description,
                chargeType = it.chargeType,
                price = it.prices.first { p -> p.tier == tier }
            )
        })

        coVerifyOnce {
            dataService.find(any())
        }
    }

    private fun buildCoPaymentCostInfo(
        event: String = "Consulta",
        index: Int = 0
    ) =
        CoPaymentCostInfo(
            event = event,
            description = "Ginecologista, Dermatologista, etc",
            chargeType = CoPaymentChargeType.BILLING_CEILING,
            index = index,
            prices = listOf(
                CoPaymentTierPrice(
                    tier = TierType.TIER_1,
                    value = BigDecimal.valueOf(100)
                ),
                CoPaymentTierPrice(
                    tier = TierType.TIER_2,
                    value = BigDecimal.valueOf(70)
                ),
                CoPaymentTierPrice(
                    tier = TierType.TIER_3,
                    value = BigDecimal.valueOf(45)
                ),
            )
        )
}
