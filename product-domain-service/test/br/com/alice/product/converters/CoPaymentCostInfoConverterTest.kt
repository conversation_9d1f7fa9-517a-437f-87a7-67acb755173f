package br.com.alice.product.converters

import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.CoPaymentCostInfo
import br.com.alice.data.layer.models.CoPaymentCostInfoModel
import br.com.alice.data.layer.models.CoPaymentTierPrice
import br.com.alice.data.layer.models.CoPaymentTierPriceModel
import br.com.alice.data.layer.models.TierType
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import kotlin.test.Test

class CoPaymentCostInfoConverterTest {

    private val coPaymentCostInfo = CoPaymentCostInfo(
        event = "Consulta",
        description = "Ginecologista, Dermatologista, etc",
        chargeType = CoPaymentChargeType.BILLING_CEILING,
        prices = listOf(
            CoPaymentTierPrice(
                tier = TierType.TIER_1,
                value = BigDecimal.valueOf(100)
            ),
            CoPaymentTierPrice(
                tier = TierType.TIER_2,
                value = BigDecimal.valueOf(70)
            ),
            CoPaymentTierPrice(
                tier = TierType.TIER_3,
                value = BigDecimal.valueOf(45)
            ),
        ),
        index = 0
    )

    private val coPaymentCostInfoModel = CoPaymentCostInfoModel(
        id = coPaymentCostInfo.id,
        version = coPaymentCostInfo.version,
        createdAt = coPaymentCostInfo.createdAt,
        updatedAt = coPaymentCostInfo.updatedAt,
        event = "Consulta",
        description = "Ginecologista, Dermatologista, etc",
        chargeType = CoPaymentChargeType.BILLING_CEILING,
        prices = listOf(
            CoPaymentTierPriceModel(
                tier = TierType.TIER_1,
                value = BigDecimal.valueOf(100)
            ),
            CoPaymentTierPriceModel(
                tier = TierType.TIER_2,
                value = BigDecimal.valueOf(70)
            ),
            CoPaymentTierPriceModel(
                tier = TierType.TIER_3,
                value = BigDecimal.valueOf(45)
            ),
        ),
        index = 0
    )

    @Test
    fun testToTransport() {
        assertThat(coPaymentCostInfoModel.toTransport()).isEqualTo(coPaymentCostInfo)
    }

    @Test
    fun testToModel() {
        assertThat(coPaymentCostInfo.toModel()).isEqualTo(coPaymentCostInfoModel)
    }

}
