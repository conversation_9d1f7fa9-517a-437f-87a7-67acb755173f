package br.com.alice.product.consumers

import br.com.alice.authentication.Authenticator
import com.google.firebase.auth.FirebaseToken
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

open class ConsumerTest {
    @BeforeTest
    fun before() {
        val firebaseToken: FirebaseToken = mockk()
        mockkObject(Authenticator)

        every { Authenticator.verifyIdToken(any()) } returns firebaseToken
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
    }

    @AfterTest
    fun clear() {
        unmockkAll()
    }
}
