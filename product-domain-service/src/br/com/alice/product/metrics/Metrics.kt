package br.com.alice.product.metrics

import br.com.alice.common.observability.metrics.Metric
import br.com.alice.common.redis.Result

object Metrics {

    private const val NODE_PUSHED_ON_DUMP_QUEUE = "node_pushed_on_site_accredited_network_dump_queue"
    private const val NODE_POPPED_ON_DUMP_QUEUE = "node_popped_on_site_accredited_network_dump_queue"
    private const val QUEUE_SIZE_CHECKED_ON_DUMP_QUEUE = "queue_size_checked_on_site_accredited_network_dump_queue"
    private const val QUEUE_SIZE_INCREMENTED_ON_DUMP_QUEUE = "queue_size_incremented_on_site_accredited_network_dump_queue"
    private const val QUEUE_HEAD_CHECKED_ON_DUMP_QUEUE = "queue_head_checked_on_site_accredited_network_dump_queue"
    private const val QUEUE_HEAD_MOVED_ON_DUMP_QUEUE = "queue_head_moved_on_site_accredited_network_dump_queue"

    fun registerMetrics() {
        listOf(
            NODE_PUSHED_ON_DUMP_QUEUE,
            NODE_POPPED_ON_DUMP_QUEUE,
            QUEUE_SIZE_CHECKED_ON_DUMP_QUEUE,
            QUEUE_SIZE_INCREMENTED_ON_DUMP_QUEUE,
            QUEUE_HEAD_CHECKED_ON_DUMP_QUEUE,
            QUEUE_HEAD_MOVED_ON_DUMP_QUEUE
        ).forEach { metric ->
            Metric.registerCounter(metric, "result" to br.com.alice.common.redis.Result.SUCCESS.toLabel())
            Metric.registerCounter(metric, "result" to br.com.alice.common.redis.Result.FAILURE.toLabel())
        }
    }

    fun incrementNodePushedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            NODE_PUSHED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

    fun incrementNodePoppedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            NODE_POPPED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

    fun incrementQueueSizeCheckedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            QUEUE_SIZE_CHECKED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

    fun incrementQueueSizeIncrementedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            QUEUE_SIZE_INCREMENTED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

    fun incrementQueueHeadCheckedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            QUEUE_HEAD_CHECKED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

    fun incrementQueueHeadMovedOnDumpQueue(
        result: Result,
    ) =
        Metric.increment(
            QUEUE_HEAD_MOVED_ON_DUMP_QUEUE,
            "result" to result.toLabel(),
        )

}
