package br.com.alice.product

import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    object Mailer {
        val defaultSenderName = config.property("${environment().value}.mailer.senderName").getString()
        val defaultSenderEmail = config.property("${environment().value}.mailer.senderEmail").getString()

        val pinPointCampaignId = config.property("${environment().value}.mailer.pinPoint.campaignId").getString()
    }
}
