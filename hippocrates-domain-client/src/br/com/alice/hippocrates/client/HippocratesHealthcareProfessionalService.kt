package br.com.alice.hippocrates.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HippocratesHealthcareProfessional
import com.github.kittinunf.result.Result
import java.util.UUID


@RemoteService
interface HippocratesHealthcareProfessionalService : Service {

    override val namespace get() = "hippocrates"
    override val serviceName get() = "hippocrates_healthcare_professional"

    suspend fun count(): Result<Int, Throwable>
    suspend fun findByRange(range: IntRange): Result<List<HippocratesHealthcareProfessional>, Throwable>
    suspend fun countByToken(term: String): Result<Int, Throwable>
    suspend fun findBySearchTokensAndRange(term: String, range: IntRange): Result<List<HippocratesHealthcareProfessional>, Throwable>
    suspend fun findBySearchTokens(token: String?): Result<List<HippocratesHealthcareProfessional>, Throwable>
    suspend fun add(
        hippocratesHealthcareProfessional: HippocratesHealthcareProfessional
    ): Result<HippocratesHealthcareProfessional, Throwable>
    suspend fun update(
        hippocratesHealthcareProfessional: HippocratesHealthcareProfessional
    ): Result<HippocratesHealthcareProfessional, Throwable>
    suspend fun get(
        id: UUID
    ): Result<HippocratesHealthcareProfessional, Throwable>
}
