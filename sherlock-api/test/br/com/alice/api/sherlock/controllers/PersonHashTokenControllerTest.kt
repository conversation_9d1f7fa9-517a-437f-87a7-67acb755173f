package br.com.alice.api.sherlock.controllers

import br.com.alice.api.sherlock.ExternalAuthControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.SHERLOCK_API_ROOT_SERVICE_NAME
import br.com.alice.sherlock.client.PersonHashTokenService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonHashTokenControllerTest : ExternalAuthControllerTestHelper() {
    private val personHashTokenService: PersonHashTokenService = mockk()
    private val personHashTokenController = PersonHashTokenController(personHashTokenService)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { personHashTokenController }
    }

    @Test
    fun `#process should receive BadRequest when request has more than 100 ids`() {
        val mockIds = (0..150).map { RangeUUID.generate().toString() }
        val mockRequest = gson.toJson(PersonHashTokenRequest(ids = mockIds))

        authenticated(token) {
            post("/person-hash", mockRequest) { response ->
                assertThat(response).isBadRequest()
                assertThat(response.bodyAsText()).isEqualTo("Quantity of ids (151) are bigger than limit of process (100)")
            }
        }
    }

    @Test
    fun `#process should receive UnprocessableEntity for any error at domain-service`() {
        val mockIds = (1..10).map { RangeUUID.generate().toString() }
        val mockRequest = gson.toJson(PersonHashTokenRequest(ids = mockIds))

        coEvery { personHashTokenService.generate(mockIds) } returns IllegalArgumentException().failure()

        authenticated(token) {
            post("/person-hash", mockRequest) { response ->
                assertThat(response).isUnprocessableEntity()
            }
        }
    }

    @Test
    fun `#process should generate hash for person_ids`() {
        val mockIds = (1..3).map { PersonId().toString() }
        val mockRequest = gson.toJson(PersonHashTokenRequest(ids = mockIds))
        val expectedEntity = listOf(
            mapOf(
                "person_id01" to "abc_01",
                "person_id02" to "abc_02",
                "person_id03" to "abc_03"
            ),
            mapOf(
                "person_id01" to "def_01",
                "person_id02" to "def_02",
                "person_id03" to "def_03"
            ),
            mapOf(
                "person_id01" to "ghi_01",
                "person_id02" to "ghi_02",
                "person_id03" to "ghi_03"
            ),
        )
        val expectedResponse = PersonHashTokenResponse(
            source = SHERLOCK_API_ROOT_SERVICE_NAME,
            ids = expectedEntity
        )

        coEvery { personHashTokenService.generate(mockIds) } returns expectedEntity.success()

        authenticated(token) {
            post("/person-hash", mockRequest) { response ->

                assertThat(response).isOK()

                val responseCasted = gson.fromJson(response.bodyAsText(), PersonHashTokenResponse::class.java)
                assertThat(responseCasted)
                    .usingRecursiveComparison()
                    .ignoringFields("timestamp")
                    .isEqualTo(expectedResponse)
            }
        }
    }
}
