package br.com.alice.api.sherlock.controllers

import br.com.alice.api.sherlock.ControllerTestHelper
import br.com.alice.api.sherlock.model.AuditFileRequest
import br.com.alice.api.sherlock.model.AuditFileResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.QueryStatus
import br.com.alice.data.layer.models.SherlockFileResult
import br.com.alice.data.layer.models.VaultFileType
import br.com.alice.sherlock.client.SherlockFileVaultService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class FileControllerTest : ControllerTestHelper() {
    private val staffService: StaffService = mockk()
    private val staffFileService: SherlockFileVaultService = mockk()

    private val fileController = FileController(staffService, staffFileService)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { fileController }
    }

    @Test
    fun `#getFileById - should return 200 with file url`() {
        val fileId = RangeUUID.generate()
        coEvery { staffFileService.getStaffFileById(fileId) } returns "url.com.br".success()

        authenticatedAs(idToken, authorizedStaff) {
            get("/file_vault/$fileId") { response ->
                assertThat(response).isOKWithData("url.com.br")
            }
        }

        coVerifyOnce { staffFileService.getStaffFileById(any()) }
    }

    @Test
    fun `#auditFile - should return 200 with file url`() {
        val fileId = RangeUUID.generate()
        coEvery { staffFileService.auditFile(authorizedStaff.id, fileId, "REASON") } returns SherlockFileResult(
            staffId = authorizedStaff.id,
            fileId = fileId,
            fileType = VaultFileType.PERSON,
            reason = "REASON",
            status = QueryStatus.DONE,
            url = "url.com.br"
        ).success()

        authenticatedAs(idToken, authorizedStaff) {
            post("/file_vault", AuditFileRequest(fileId, "REASON")) { response ->
                assertThat(response).isOKWithData(AuditFileResponse(
                    staffId = authorizedStaff.id,
                    fileId = fileId,
                    fileType = VaultFileType.PERSON,
                    reason = "REASON",
                    status = QueryStatus.DONE,
                    url = "url.com.br"
                ))
            }
        }

        coVerifyOnce { staffFileService.auditFile(any(), any(), any()) }
    }

    @Test
    fun `#auditFile - should return 404 when file is not found`() {
        val fileId = RangeUUID.generate()
        coEvery { staffFileService.auditFile(authorizedStaff.id, fileId, "REASON") } returns
                NotFoundException("OH GOSH WHERE IS IT").failure()

        authenticatedAs(idToken, authorizedStaff) {
            post("/file_vault", AuditFileRequest(fileId, "REASON")) { response ->
                assertThat(response).isNotFoundWithErrorCode("resource_not_found")
            }
        }

        coVerifyOnce { staffFileService.auditFile(any(), any(), any()) }
    }

    @Test
    fun `#auditFile - should return 500 when unexpected problem is encountered`() {
        val fileId = RangeUUID.generate()
        coEvery { staffFileService.auditFile(authorizedStaff.id, fileId, "REASON") } returns
                InternalServiceErrorException("BIG BOGUS").failure()

        authenticatedAs(idToken, authorizedStaff) {
            post("/file_vault", AuditFileRequest(fileId, "REASON")) { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerifyOnce { staffFileService.auditFile(any(), any(), any()) }
    }
}
