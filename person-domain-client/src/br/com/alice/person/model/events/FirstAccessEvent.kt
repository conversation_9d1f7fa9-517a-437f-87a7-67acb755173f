package br.com.alice.person.br.com.alice.person.model.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.PersonLogin
import br.com.alice.person.SERVICE_NAME

class FirstAccessEvent(personLogin: PersonLogin) : NotificationEvent<PersonLogin>(
    name = name,
    producer = SERVICE_NAME,
    payload = personLogin
) {
    companion object {
        const val name = "person-first-access"
    }
}
