package br.com.alice.person.model.events

import br.com.alice.common.convertTo
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Person
import br.com.alice.person.SERVICE_NAME

class PersonUpdatedEvent(person: Person) : NotificationEvent<PersonPayload>(
    producer = SERVICE_NAME,
    name = name,
    payload = person.convertTo(PersonPayload::class)
) {
    companion object {
        const val name = "PERSON-UPDATED"
    }
}

