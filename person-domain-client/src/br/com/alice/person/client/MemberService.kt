package br.com.alice.person.client

import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberLifeCycleEvents
import br.com.alice.data.layer.models.MemberLightweight
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.Product
import br.com.alice.person.br.com.alice.person.model.MemberCurrentAndPrevious
import br.com.alice.person.br.com.alice.person.model.PersonWithReferenceDate
import br.com.alice.person.model.MemberWithProduct
import br.com.alice.person.model.MemberWithProductWithProviders
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface MemberService : Service {

    override val namespace get() = "person"
    override val serviceName get() = "member"

    suspend fun get(id: UUID, findOptions: FindOptions = FindOptions()): Result<Member, Throwable>

    suspend fun findByIds(ids: List<UUID>, findOptions: FindOptions = FindOptions()): Result<List<Member>, Throwable>

    suspend fun getLightweight(id: UUID): Result<MemberLightweight, Throwable>

    suspend fun getCurrent(personId: PersonId): Result<Member, Throwable>

    suspend fun getCurrentsByIds(ids: List<UUID>): Result<List<Member>, Throwable>
    suspend fun getCurrentsByPersonIds(ids: List<PersonId>): Result<List<Member>, Throwable>

    suspend fun findByPerson(personId: PersonId): Result<List<Member>, Throwable>

    suspend fun findByPersonAndStatus(personId: PersonId, status: MemberStatus): Result<List<Member>, Throwable>

    suspend fun findActiveMembership(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<Member, Throwable>

    suspend fun findActiveMembershipWithProduct(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<MemberWithProduct, Throwable>

    suspend fun findMembershipWithProductValidOnDate(
        personsWithReferenceDate: List<PersonWithReferenceDate>,
    ): Result<List<MemberWithProduct>, Throwable>

    suspend fun findMembershipWithProduct(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<MemberWithProductWithProviders, Throwable>

    suspend fun findActiveOrPendingMembership(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<Member, Throwable>

    suspend fun findPendingMembership(
        personId: PersonId,
        findOptions: FindOptions = FindOptions()
    ): Result<Member, Throwable>

    suspend fun findFirstMembership(personId: PersonId): Result<Member, Throwable>

    suspend fun findByPersonIds(
        personIds: List<PersonId>,
        findOptions: FindOptions = FindOptions()
    ): Result<List<Member>, Throwable>

    suspend fun findActiveMembersByPersonIds(personIds: List<PersonId>): Result<List<Member>, Throwable>
    suspend fun findActiveOrPendingMembersByPersonIds(personIds: List<PersonId>): Result<List<Member>, Throwable>

    suspend fun create(
        personId: PersonId,
        contract: OnboardingContract,
        cassiMember: CassiMemberInfo? = null,
    ): Result<Member, Throwable>

    suspend fun hasContractSigned(personId: PersonId): Result<Boolean, Throwable>

    suspend fun activateMember(member: Member, activationDate: LocalDateTime? = null): Result<Member, Throwable>

    suspend fun reactivateMemberById(
        id: UUID,
        reactivationParams: MemberLifeCycleEvents? = null
    ): Result<Member, Throwable>

    suspend fun cancelById(id: UUID): Result<Member, Throwable>

    suspend fun cancel(member: Member, canceledAt: LocalDateTime = LocalDateTime.now()): Result<Member, Throwable>

    suspend fun changeProduct(
        personId: PersonId,
        product: Product,
        keepStatus: Boolean = true,
        activationDate: LocalDateTime? = null
    ): Result<Member, Throwable>

    suspend fun cancelActiveOrPendingMembership(personId: PersonId): Result<Member, Throwable>

    suspend fun cancelPendingMembership(personId: PersonId): Result<Member, Throwable>

    suspend fun findActive(offset: Int, limit: Int): Result<List<Member>, Throwable>

    suspend fun findActiveLightweight(): Result<List<MemberLightweight>, Throwable>

    suspend fun findFirstActivated(personId: PersonId): Result<Member, Throwable>

    suspend fun cancelAndCreateNewMember(
        previousMemberId: UUID,
        activationDate: LocalDateTime? = null
    ): Result<Member, Throwable>

    suspend fun update(member: Member, sendEvent: Boolean = true): Result<Member, Throwable>

    suspend fun findActiveMemberByProduct(productId: UUID): Result<List<Member>, Throwable>

    suspend fun findByIdsAndStatus(ids: List<UUID>, status: List<MemberStatus>): Result<List<Member>, Throwable>

    suspend fun findMemberCurrentAndPrevious(personId: PersonId): Result<MemberCurrentAndPrevious, Throwable>

    suspend fun findDependsByParentMemberIdAndStatus(
        parentMemberId: UUID,
        status: List<MemberStatus>
    ): Result<List<Member>, Throwable>

    data class FindOptions(
        val withPriceListing: Boolean = false,
        val withCassiMember: Boolean = false,
    )

    suspend fun findByCreatedAtPeriodAndStatusWithActivationDate(
        createdAtStart: LocalDateTime,
        createdAtEnd: LocalDateTime,
        statusParam: List<MemberStatus>
    ): Result<List<Member>, Throwable>

    suspend fun findByCanceledWithoutCanceledAtDatetimeFromCreatedAt(
        createdAtStart: LocalDateTime,
        limit: Int = 20,
    ): Result<List<Member>, Throwable>

    suspend fun getByBeneficiaryId(beneficiaryId: UUID): Result<Member, Throwable>

    suspend fun findByBeneficiaryIds(beneficiaryIds: List<UUID>): Result<List<Member>, Throwable>

    // FIXME: range should be mandatory
    suspend fun findByCompanyId(companyId: UUID, range: IntRange? = null): Result<List<Member>, Throwable>

    suspend fun findByCompanyIds(companyIds: List<UUID>, range: IntRange): Result<List<Member>, Throwable>

    // FIXME: range should be mandatory
    suspend fun findByCompanySubContractId(companySubContractId: UUID, range: IntRange? = null): Result<List<Member>, Throwable>

    suspend fun findByParentPersonIdAndStatuses(
        parentPersonId: PersonId,
        statuses: List<MemberStatus>
    ): Result<List<Member>, Throwable>

    suspend fun findByParentBeneficiaryId(
        parentBeneficiaryId: UUID,
        onlyDirectDependents: Boolean
    ): Result<List<Member>, Throwable>

    suspend fun findByCompanyIdAndPersonIds(companyId: UUID, personIds: List<PersonId>): Result<List<Member>, Throwable>

    suspend fun countByBeneficiaryIds(beneficiaryIds: List<UUID>): Result<Int, Throwable>

    suspend fun findByFilter(
        companyId: UUID? = null,
        beneficiaryParentId: UUID? = null,
        status: MemberStatus? = null,
        range: IntRange
    ): Result<List<Member>, Throwable>

    suspend fun findByCancellationDate(
        startDate: LocalDate,
        endDate: LocalDate,
        range: IntRange
    ): Result<List<Member>, Throwable>

    suspend fun countByFilter(
        companyId: UUID?,
        status: MemberStatus?,
        parentBeneficiaryId: UUID?
    ): Result<Int, Throwable>

    suspend fun countByCompanyIdAndStatus(
        companyId: UUID,
        status: MemberStatus,
        type: BeneficiaryType? = null,
        withNoPendingCancellation: Boolean = false
    ): Result<Int, Throwable>
}

class ActiveMembershipNotFoundException(
    message: String,
    code: String = "active_membership_not_Found",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId) : this(
        message = "personId=${personId} doesn't have an active membership"
    )
}

class MembershipMustBeAptToBeReactivatedException(
    message: String,
    code: String = "membership_must_be_apt_to_be_reactivated",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId, membershipStatus: String) : this(
        message = "personId=${personId} has an $membershipStatus as most recent membership and can't be reactivated"
    )
}

class MembershipAlreadyActiveMismatchException(
    message: String,
    code: String = "membership_already_active",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId) : this(
        message = "Person '$personId' has already an active Membership"
    )
}

class PendingMembershipMismatchException(
    message: String,
    code: String = "pending_membership_mismatch",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(personId: PersonId) : this(
        message = "Person '$personId' has a pending Membership for a different Product"
    )
}
