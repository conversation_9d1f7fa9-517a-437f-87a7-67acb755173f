package br.com.alice.person.client

import br.com.alice.common.DatabaseInstance
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonRegistration
import br.com.alice.person.model.SUSPersonInfo
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface PersonService : Service {

    override val namespace get() = "person"
    override val serviceName get() = "person"

    suspend fun create(person: Person, shouldValidateAdditionalInfo: Boolean = false): Result<Person, Throwable>

    suspend fun get(id: PersonId, withUserType: Boolean = false, databaseInstance: DatabaseInstance = DatabaseInstance.READ): Result<Person, Throwable>

    @Deprecated("E-mail is not unique anymore")
    suspend fun findByEmail(emailAddress: String, withUserType: Boolean = false): Result<Person, Throwable>

    suspend fun findPersonWithEmailAlias(limit: Int, offset: Int): Result<List<Person>, Throwable>

    suspend fun findByEmails(emailAddresses: List<String>): Result<List<Person>, Throwable>

    suspend fun findByIds(
        ids: List<String>,
        withUserType: Boolean = false,
        includeTest: Boolean = true
    ): Result<List<Person>, Throwable>

    @Deprecated("Use findAuthorizedBy instead")
    suspend fun findBy(filter: PersonFilter): Result<List<Person>, Throwable>

    suspend fun findAuthorizedBy(filter: PersonFilter): Result<List<Person>, Throwable>

    suspend fun update(
        person: Person,
        sendEvent: Boolean = true,
        shouldValidateAdditionalInfo: Boolean = false
    ): Result<Person, Throwable>

    suspend fun updateList(
        personList: List<Person>,
        sendEvent: Boolean = true,
        shouldValidateAdditionalInfo: Boolean = false
    ): Result<List<Person>, Throwable>

    suspend fun findByNationalId(nationalId: String): Result<Person, Throwable>

    suspend fun findByNationalIds(nationalIds: List<String>): Result<List<Person>, Throwable>

    suspend fun findBySearchTokens(token: String): Result<List<Person>, Throwable>

    suspend fun findAuthorizedBySearchTokens(token: String): Result<List<Person>, Throwable>

    suspend fun findTestPersons(): Result<List<Person>, Throwable>

    suspend fun findPaginated(offset: Int, limit: Int): Result<List<Person>, Throwable>

    suspend fun countAll(): Result<Int, Throwable>

    suspend fun updatePerson(registration: PersonRegistration): Result<Person, Throwable>

    suspend fun updateProfilePicture(id: PersonId, file: AliceFile): Result<Person, Throwable>

    suspend fun findByOpportunities(opportunityIds: List<UUID>): Result<List<Person>, Throwable>

    suspend fun findByOpportunity(opportunityId: UUID): Result<Person, Throwable>

    suspend fun terminateSession(id: PersonId): Result<Boolean, Throwable>

    suspend fun acceptTerms(id: PersonId): Result<Person, Throwable>
    suspend fun findByIdsAndDateOfBirth(
        ids: List<String>,
        firstDayOfMonth: LocalDateTime,
        lastDayOfMonth: LocalDateTime,
        withUserType: Boolean = true,
    ): Result<List<Person>, Throwable>

    suspend fun getNationalId(personId: PersonId): Result<String, Throwable>

    suspend fun updateEmail(personId: PersonId, newEmail: String): Result<Person, Throwable>
    suspend fun updatePhoneNumber(personId: PersonId, newPhoneNumber: String): Result<Person, Throwable>
    suspend fun validateAndUpdatePersonFromSUS(
        personId: PersonId,
        susPersonInfo: SUSPersonInfo
    ): Result<Person, Throwable>

    suspend fun countByLeadId(leadId: UUID): Result<Int, Throwable>
    suspend fun countBySearchToken(searchToken: String): Result<Int, Throwable>

}

data class PersonFilter(
    val ids: List<String>? = null,
    val emails: List<String>? = null,
    val nationalIds: List<String>? = null,
    val isTest: Boolean? = null,
    val dateOfBirthGreaterEqAndLessEq: LocalDateTime? = null,
    val searchTokens: String? = null,
    val range: IntRange? = null,
    val sortOrder: SortOrder = SortOrder.Ascending
) {
    fun isValid(): Boolean =
        if (!ids.isNullOrEmpty()) true
        else if (!emails.isNullOrEmpty()) true
        else if (!nationalIds.isNullOrEmpty()) true
        else if (isTest != null) true
        else if (dateOfBirthGreaterEqAndLessEq != null) true
        else if (searchTokens != null) true
        else if (range != null) true
        else throw InvalidArgumentException("PersonFilter require some field")
}
