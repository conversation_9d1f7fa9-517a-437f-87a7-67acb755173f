package br.com.alice.eita.nullvs.converters

import br.com.alice.data.layer.models.HealthcareResourceType
import br.com.alice.eita.nullvs.events.NullvsProcedureCodeWebhookReceived
import br.com.alice.eita.nullvs.models.procedure.TotvsProcedureCodesReturnPayload
import br.com.alice.eita.nullvs.models.procedureCodes.NullvsProcedureCodesReturnPayload
import java.util.UUID

object TotvsProcedureCodesConverter {
    fun NullvsProcedureCodeWebhookReceived.Payload.toTotvsSuccessConfirmRequest(healthcareResourceId: UUID): TotvsProcedureCodesReturnPayload {
        return TotvsProcedureCodesReturnPayload(
            tableType = this.tableType,
            code = this.code,
            internalId = healthcareResourceId
        )
    }

    fun NullvsProcedureCodeWebhookReceived.Payload.toTotvsErrorConfirmRequest(errorMessage: String): TotvsProcedureCodesReturnPayload {
        return TotvsProcedureCodesReturnPayload(
            errorCode = 400,
            errorMessage = errorMessage
        )
    }
    fun TotvsProcedureCodesReturnPayload.toNullvsProcedureCodesReturnPayload() =
        NullvsProcedureCodesReturnPayload(
            tableType = this.tableType,
            code = this.code,
            internalId = this.internalId,
            errorCode = this.errorCode,
            errorMessage = this.errorMessage,
        )

    fun NullvsProcedureCodesReturnPayload.toTotvsProcedureCodesReturnPayload() =
        TotvsProcedureCodesReturnPayload(
            tableType = this.tableType,
            code = this.code,
            internalId = this.internalId,
            errorCode = this.errorCode,
            errorMessage = this.errorMessage,
        )

    fun getHealthcareResourceType(procedureType: String?) =
        when (procedureType) {
            "0" -> HealthcareResourceType.PROCEDURE
            "1" -> HealthcareResourceType.MATERIAL
            "2" -> HealthcareResourceType.MEDICINE
            "3" -> HealthcareResourceType.FEE
            "4" -> HealthcareResourceType.DAILY_STAY
            "5" -> HealthcareResourceType.OPME
            "6" -> HealthcareResourceType.BUNDLE
            "7" -> HealthcareResourceType.MEDICAL_GASES
            "8" -> HealthcareResourceType.RENT
            "9" -> HealthcareResourceType.OTHER
            else -> HealthcareResourceType.UNKNOWN
        }
}
