package br.com.alice.eita.nullvs.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.EitaNullvsIntegrationRecord
import br.com.alice.data.layer.models.EitaNullvsIntegrationRecordModel

object EitaNullvsIntegrationRecordConverter : Converter<EitaNullvsIntegrationRecordModel, EitaNullvsIntegrationRecord>(
    EitaNullvsIntegrationRecordModel::class,
    EitaNullvsIntegrationRecord::class,
)

fun EitaNullvsIntegrationRecord.toModel() = EitaNullvsIntegrationRecordConverter.unconvert(this)
fun EitaNullvsIntegrationRecordModel.toTransport() = EitaNullvsIntegrationRecordConverter.convert(this)
