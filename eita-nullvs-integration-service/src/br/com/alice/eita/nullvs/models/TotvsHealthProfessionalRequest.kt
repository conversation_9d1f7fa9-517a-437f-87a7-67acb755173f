package br.com.alice.eita.nullvs.models

import com.google.gson.annotations.SerializedName
import java.util.UUID

data class TotvsHealthProfessionalRequest(
    @SerializedName("idlote")
    val idSoc: String,

    @SerializedName("tipo")
    val type: String,

    @SerializedName("entidade")
    val entity: String = "profissionais",

    @SerializedName("total")
    val totalRecords: Int,

    @SerializedName("data")
    val date: String,

    @SerializedName("acao")
    val action: String,

    val payload: List<TotvsHealthProfessionalPayload>
)

data class TotvsHealthProfessionalPayload(
    @SerializedName("BB0_NOME")
    val name: String,

    @SerializedName("BB0_CODSIG")
    val council: String = "CRM",

    @SerializedName("BB0_NUMCR")
    val councilNumber: String,

    @SerializedName("BB0_ESTADO")
    val councilState: String,

    @SerializedName("BB0_ZRETGU")
    val attendsToOnCall: String,

    @SerializedName("BB0_ZTPREC")
    val onCallPaymentMethod: String?,

    @SerializedName("BB0_CGC")
    val nationalId: String?,

    @SerializedName("BB0_SEXO")
    val gender: String?,

    @SerializedName("BB0_XIDALI")
    val internalId: UUID,

    @SerializedName("BB0_YSTAFF")
    val aliceHealthProfessional: String = "1" // must be 1 (true) for all health professionals
)
