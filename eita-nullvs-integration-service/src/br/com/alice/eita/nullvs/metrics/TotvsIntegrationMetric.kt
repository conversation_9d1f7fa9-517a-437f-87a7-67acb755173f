package br.com.alice.eita.nullvs.metrics

import br.com.alice.common.observability.metrics.Metric
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import java.util.Locale

object TotvsIntegrationMetric {
    private const val EITA_NULLVS_INTEGRATION_METRIC_NAME = "eita_nullvs_data_sent"
    private const val EITA_NULLVS_RECORD_SAVED_COUNT = "eita_nullvs_record_saved_count"
    private const val EITA_NULLVS_LOG_SAVED_COUNT = "eita_nullvs_log_saved_count"
    private const val EITA_NULLVS_RECORD_LOG_COUNT = "eita_nullvs_record_log_count"
    private const val EITA_NULLVS_WEBHOOK_CONSUMER_COUNT = "eita_nullvs_webhook_consumer_count"
    private const val EITA_NULLVS_INTEGRATION_RECORD_DUPLICATED = "eita_nullvs_integration_record_duplicated"
    private const val EITA_NULLVS_INTEGRATION_RECORD_NOT_FOUND = "eita_nullvs_integration_record_not_found"

    enum class Method {
        BUNDLE, RESOURCE, FILE_VAULT, NATIONAL_RECEIPT, HEALTH_PROFESSIONAL, GUIA
    }

    fun EitaNullvsInternalModelType.toMetricMethod() = when(this) {
        EitaNullvsInternalModelType.HEALTHCARE_BUNDLE -> Method.BUNDLE
        EitaNullvsInternalModelType.HEALTHCARE_RESOURCE -> Method.RESOURCE
        EitaNullvsInternalModelType.FILE_VAULT -> Method.FILE_VAULT
    }
    enum class Status {
        SUCCESS, FAILURE
    }

    enum class RecordLogStatus {
        PENDING, FINISHED
    }

    fun countTotvsIntegrationRecordDuplicated(internalModelType: EitaNullvsInternalModelType) {
        Metric.increment(
            EITA_NULLVS_INTEGRATION_RECORD_DUPLICATED,
            "method" to internalModelType.toMetricMethod().name.lowercase(Locale.getDefault()),
        )
    }
    fun countTotvsIntegrationRecordNotFound(internalModelType: EitaNullvsInternalModelType) {
        Metric.increment(
            EITA_NULLVS_INTEGRATION_RECORD_NOT_FOUND,
            "method" to internalModelType.toMetricMethod().name.lowercase(Locale.getDefault()),
        )
    }

    fun countTotvsIntegration(method: Method, status: Status) {
        Metric.increment(
            EITA_NULLVS_INTEGRATION_METRIC_NAME,
            "method" to method.name.lowercase(Locale.getDefault()),
            "status" to status.name.lowercase(Locale.getDefault()),
        )
    }

    fun countNullvsRecordSaved(method: Method, status: Status) {
        Metric.increment(EITA_NULLVS_RECORD_SAVED_COUNT,
            "method" to method.name.lowercase(Locale.getDefault()),
            "status" to status.name.lowercase(Locale.getDefault())
        )
    }

    fun countNullvsLogSaved(method: Method, status: Status) {
        Metric.increment(EITA_NULLVS_LOG_SAVED_COUNT,
            "method" to method.name.lowercase(Locale.getDefault()),
            "status" to status.name.lowercase(Locale.getDefault())
        )
    }

    fun countNullvsRecordLog(method: Method, recordLogStatus: RecordLogStatus) {
        Metric.increment(EITA_NULLVS_RECORD_LOG_COUNT,
            "method" to method.name.lowercase(Locale.getDefault()),
            "record_log_status" to recordLogStatus.name.lowercase(Locale.getDefault()),
        )
    }

    fun countNullvsWebhookConsumer(method: Method, status: Status) =
        Metric.increment(EITA_NULLVS_WEBHOOK_CONSUMER_COUNT,
            "method" to method.name.lowercase(Locale.getDefault()),
            "status" to status.name.lowercase(Locale.getDefault()),
        )

}
