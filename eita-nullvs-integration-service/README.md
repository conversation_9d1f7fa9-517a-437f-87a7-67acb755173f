# Alice's EITA Nullvs Integration Domain
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aeita-nullvs-integration-service&metric=coverage&token=f9fe7aaa29511a7615a6dd9c85af868be369d43d)](https://sonarcloud.io/summary/new_code?id=mono%3Aeita-nullvs-integration-service)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aeita-nullvs-integration-service&metric=code_smells&token=f9fe7aaa29511a7615a6dd9c85af868be369d43d)](https://sonarcloud.io/summary/new_code?id=mono%3Aeita-nullvs-integration-service)
[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aeita-nullvs-integration-service&metric=duplicated_lines_density&token=f9fe7aaa29511a7615a6dd9c85af868be369d43d)](https://sonarcloud.io/summary/new_code?id=mono%3Aeita-nullvs-integration-service)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Aeita-nullvs-integration-service&metric=bugs&token=f9fe7aaa29511a7615a6dd9c85af868be369d43d)](https://sonarcloud.io/summary/new_code?id=mono%3Aeita-nullvs-integration-service)

EITA Nullvs Integration domain service is responsible for integrate EITA with TOTVS's systems

![Black Hole](https://www.slashgear.com/img/gallery/10-things-we-know-about-black-holes/l-intro-**********.jpg)

> nam omne totvs, nullvs erit. \
> (sempre que houver um todo, haverá um nada)

### Responsible Team
Provider Lifecycle, find us on ``#eng-provider-lifecycle`` on Slack ;)
