package br.com.alice.eita.nullvs.consumers.healthcare.bundle

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import br.com.alice.data.layer.models.HealthcareBundle
import br.com.alice.data.layer.models.HealthcareBundleStatus
import br.com.alice.data.layer.models.HealthcareResource
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.eita.nullvs.client.EitaNullvsIntegrationLogService
import br.com.alice.eita.nullvs.clients.TotvsBundleClient
import br.com.alice.eita.nullvs.consumers.ConsumerTest
import br.com.alice.eita.nullvs.events.NullvsHealthcareBundleBatchResponseEvent
import br.com.alice.eita.nullvs.events.NullvsHealthcareBundleGroupBatchRequestEvent
import br.com.alice.eita.nullvs.exceptions.TotvsBundleClientPostException
import br.com.alice.eita.nullvs.models.Meta
import br.com.alice.eita.nullvs.models.TotvsActionClient
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundleBatchRequest
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundleBatchResponse
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundleComposition
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundlePayload
import br.com.alice.eita.nullvs.models.bundle.TotvsHealthcareBundleResponse
import br.com.alice.eita.nullvs.services.internals.NullvsHealthcareBundleService
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class NullvsHealthcareBundleGroupBatchRequestConsumerTest : ConsumerTest() {
    private val totvsBundleClient: TotvsBundleClient = mockk()
    private val nullvsIntegrationLogService: EitaNullvsIntegrationLogService = mockk()
    private val healthcareBundleService: HealthcareBundleService = mockk()
    private val nullvsHealthcareBundleService: NullvsHealthcareBundleService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val consumer = NullvsHealthcareBundleGroupBatchRequestConsumer(
        totvsBundleClient,
        LocalProducer,
        nullvsIntegrationLogService,
        healthcareBundleService,
        nullvsHealthcareBundleService,
        healthcareResourceService,
    )

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = EitaNullvsExternalModelType.BUNDLE,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    private val healthcareBundle = TestModelFactory.buildHealthcareBundle(
        compositionHash = RangeUUID.generate().toString(),
        status = HealthcareBundleStatus.PENDING_INTEGRATION,
    )

    private val healthcareResource = TestModelFactory.buildHealthcareResource()

    val request = healthcareBundle.buildTotvsHealthcareBundleRequest(
        meta,
        "",
        "",
        emptyList(),
        healthcareResource
    )

    private val nullvsIntegrationLog = TestModelFactory.buildEitaNullvsIntegrationLog()

    @Test
    fun `#sendNullvsHealthcareBundleGroupRequest - should produce NullvsHealthcareBundleBatchResponseEvent`() = runBlocking {
        val event = NullvsHealthcareBundleGroupBatchRequestEvent(meta, healthcareBundle, IntRange(0, 99))
        val totvsHealthcareBundleResponse =
            TotvsHealthcareBundleResponse(
                httpStatus = "200",
                batch = "001",
                idSoc = "123",
                action = "insert",
            )
        val expected = NullvsHealthcareBundleBatchResponse(meta, totvsHealthcareBundleResponse)

        coEvery {
            healthcareResourceService.getHealthcareResourceAssociatedToBundle(healthcareBundle)
        } returns healthcareResource.success()

        coEvery {
            nullvsHealthcareBundleService.buildUpdateGroupsRequest(
                any(),
                healthcareBundle,
                healthcareResource
            )
        } returns request

        coEvery { totvsBundleClient.sendHealthcareBundle(request) } returns expected

        val result = consumer.sendNullvsHealthcareBundleGroupRequest(event)

        ResultAssert.assertThat(result).isSuccessWithData(expected)
        Assertions.assertThat(LocalProducer.hasEvent(NullvsHealthcareBundleBatchResponseEvent.name)).isTrue

        coVerifyOnce { totvsBundleClient.sendHealthcareBundle(any()) }
    }

    @Test
    fun `#sendNullvsHealthcareBundleGroupRequest - should fail if doesnt find resource related to bundle`() = runBlocking {
        val event = NullvsHealthcareBundleGroupBatchRequestEvent(meta, healthcareBundle, IntRange(0, 99))
        val totvsHealthcareBundleResponse =
            TotvsHealthcareBundleResponse(
                httpStatus = "200",
                batch = "001",
                idSoc = "123",
                action = "insert",
            )
        val expected = NullvsHealthcareBundleBatchResponse(meta, totvsHealthcareBundleResponse)

        coEvery {
            healthcareResourceService.getHealthcareResourceAssociatedToBundle(healthcareBundle)
        } returns NotFoundException("").failure()

        coEvery {
            nullvsHealthcareBundleService.buildUpdateGroupsRequest(
                any(),
                healthcareBundle,
                healthcareResource
            )
        } returns request

        coEvery { totvsBundleClient.sendHealthcareBundle(request) } returns expected

        val result = consumer.sendNullvsHealthcareBundleGroupRequest(event)

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#sendNullvsHealthcareBundleGroupRequest - failure`() = runBlocking {
        val event = NullvsHealthcareBundleGroupBatchRequestEvent(meta, healthcareBundle, IntRange(0, 99))

        coEvery { totvsBundleClient.sendHealthcareBundle(any()) } returns IllegalArgumentException("Erro!").failure()

        coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

        coEvery {
            healthcareResourceService.getHealthcareResourceAssociatedToBundle(healthcareBundle)
        } returns healthcareResource.success()

        coEvery {
            healthcareBundleService.get(meta.internalId)
        } returns healthcareBundle.success()

        coEvery {
            healthcareBundleService.update(match { it.id == meta.internalId })
        } returns healthcareBundle.copy(status = HealthcareBundleStatus.ERROR_INTEGRATION).success()

        coEvery {
            nullvsHealthcareBundleService.buildUpdateGroupsRequest(
                any(),
                healthcareBundle,
                healthcareResource
            )
        } returns request

        val result = consumer.sendNullvsHealthcareBundleGroupRequest(event)

        ResultAssert.assertThat(result).isFailureOfType(IllegalArgumentException::class)
        Assertions.assertThat(LocalProducer.hasEvent(NullvsHealthcareBundleBatchResponseEvent.name)).isFalse

        coVerifyOnce { totvsBundleClient.sendHealthcareBundle(request) }

        coVerifyOnce {
            nullvsIntegrationLogService.add(match {
                it.eventId == meta.eventId &&
                        it.eventName == meta.eventName &&
                        it.integrationEventName == meta.integrationEventName &&
                        it.internalId == meta.internalId &&
                        it.internalModelName == meta.internalModelName &&
                        it.externalModelName == meta.externalModelName &&
                        it.batchType == BatchType.CREATE &&
                        it.description == "Erro!" &&
                        it.status == LogStatus.TOTVS_NOT_CALLED
            })
        }
    }

    @Test
    fun `#sendNullvsHealthcareBundleGroupRequest - should throw AutoRetryableException on TotvsClientClientPostException`() =
        runBlocking {
            val event = NullvsHealthcareBundleGroupBatchRequestEvent(meta, healthcareBundle, IntRange(0, 99))

            coEvery {
                healthcareResourceService.getHealthcareResourceAssociatedToBundle(healthcareBundle)
            } returns healthcareResource.success()

            coEvery { totvsBundleClient.sendHealthcareBundle(any()) } returns
                    TotvsBundleClientPostException(TotvsActionClient.INSERT.code, RuntimeException("GAVE BAD")).failure()
            coEvery { nullvsIntegrationLogService.add(any()) } returns nullvsIntegrationLog.success()

            coEvery {
                healthcareBundleService.get(meta.internalId)
            } returns healthcareBundle.success()

            coEvery {
                healthcareBundleService.update(match { it.id == meta.internalId })
            } returns healthcareBundle.copy(status = HealthcareBundleStatus.ERROR_INTEGRATION).success()

            coEvery {
                nullvsHealthcareBundleService.buildUpdateGroupsRequest(
                    any(),
                    healthcareBundle,
                    healthcareResource
                )
            } returns request

            val result = consumer.sendNullvsHealthcareBundleGroupRequest(event)
            ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

            coVerifyOnce { totvsBundleClient.sendHealthcareBundle(request) }
            coVerifyOnce { nullvsIntegrationLogService.add(any()) }
        }

    private fun HealthcareBundle.buildTotvsHealthcareBundleRequest(
        meta: Meta,
        action: String,
        cnpj: String,
        composition: List<NullvsHealthcareBundleComposition> = emptyList(),
        healthcareResource: HealthcareResource
    ) = NullvsHealthcareBundleBatchRequest(
        meta = meta,
        action = action,
        date = LocalDate.now(),
        payload = listOf(
            NullvsHealthcareBundlePayload(
                providerCnpj = cnpj,
                description = this.name,
                healthcareResourceId = healthcareResource.id,
                validAfter = this.validAfter,
                validBefore = this.validBefore,
                negotiationDate = this.validAfter,
                composition = composition
            )
        )
    )
}
