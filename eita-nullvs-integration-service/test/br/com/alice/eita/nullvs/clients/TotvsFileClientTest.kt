package br.com.alice.eita.nullvs.clients

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.eita.nullvs.client.exceptions.TotvsFileUploadException
import br.com.alice.eita.nullvs.models.file.FileItem
import br.com.alice.eita.nullvs.models.file.TotvsFileResponse
import br.com.alice.eita.nullvs.models.file.TotvsUploadFileResponse
import br.com.alice.eita.nullvs.models.file.TotvsUploadGuiaFileRequest
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.runBlocking
import java.util.Base64
import kotlin.test.Test

class TotvsFileClientTest {

    private val baseUrl = "https://alice.com.br"
    private val secretKey = "12345"

    @Test
    fun `#uploadFile should return success when the request is successful`() = runBlocking {
        val byteArray = ByteArray(1)
        val file = Base64.getEncoder().encodeToString(byteArray)

        val request = TotvsUploadGuiaFileRequest(
            fileName = "file.pdf",
            file = file,
            attachmentsKey = "*********"
        )

        val response = TotvsUploadFileResponse(
            hasError = false,
            errorMessage = "",
            fileName = "file.pdf"
        )
        val serialized = gson.toJson(response)

        val httpClient = httpClientMock(
            responseContent = serialized,
            httpMethod = HttpMethod.Post,
            url = "$baseUrl/rest/totvsHealthPlans/v1/knowledgeBank",
            statusCode = HttpStatusCode.OK,

            )

        val client = TotvsFileClient(baseUrl, secretKey, httpClient)

        val result = client.uploadGuiaFile(request)

        assertThat(result).isSuccessWithData(response)
    }

    @Test
    fun `#uploadFile should return TotvsFileUploadException when totvs returns hasErrors = true`() = runBlocking {
        val byteArray = ByteArray(1)
        val file = Base64.getEncoder().encodeToString(byteArray)

        val request = TotvsUploadGuiaFileRequest(
            fileName = "file.pdf",
            file = file,
            attachmentsKey = "*********"
        )

        val response = TotvsUploadFileResponse(
            hasError = true,
            errorMessage = "Errei",
            fileName = "file.pdf"
        )
        val serialized = gson.toJson(response)

        val httpClient = httpClientMock(
            responseContent = serialized,
            httpMethod = HttpMethod.Post,
            url = "$baseUrl/rest/totvsHealthPlans/v1/knowledgeBank",
            statusCode = HttpStatusCode.OK,

            )

        val client = TotvsFileClient(baseUrl, secretKey, httpClient)

        val result = client.uploadGuiaFile(request)

        assertThat(result).isFailureOfType(TotvsFileUploadException::class)
    }

    @Test
    fun `#uploadFile should return failure when the request is not successful`() = runBlocking {
        val byteArray = ByteArray(1)
        val file = Base64.getEncoder().encodeToString(byteArray)

        val request = TotvsUploadGuiaFileRequest(
            fileName = "file.pdf",
            file = file,
            attachmentsKey = "*********"
        )

        val httpClient = httpClientMock(
            httpMethod = HttpMethod.Post,
            url = "$baseUrl/rest/totvsHealthPlans/v1/knowledgeBank",
            statusCode = HttpStatusCode.BadRequest,
        )

        val client = TotvsFileClient(baseUrl, secretKey, httpClient)

        val result = client.uploadGuiaFile(request)

        assertThat(result).isFailureOfType(ClientRequestException::class)
    }

    @Test
    fun `#getGuiaFiles should return success when the request is successful`() = runBlocking {
        val externalCode = "*********"

        val response = TotvsFileResponse(
            items = listOf(
                FileItem(
                    sequential = "1",
                    totvsFile = "file-totvs.pdf",
                    fileName = "file.pdf"
                )
            )
        )

        val serialized = gson.toJson(response)

        val httpClient = httpClientMock(
            responseContent = serialized,
            httpMethod = HttpMethod.Get,
            url = "$baseUrl/rest/totvsHealthPlans/v1/knowledgeBank?attachmentsKey=$externalCode",
            statusCode = HttpStatusCode.OK,
        )

        val client = TotvsFileClient(baseUrl, secretKey, httpClient)

        val result = client.getGuiaFiles(externalCode)

        assertThat(result).isSuccessWithData(response)
    }

    @Test
    fun `#getGuiaFiles should return failure when the request is not successful`() = runBlocking {
        val externalCode = "*********"

        val httpClient = httpClientMock(
            httpMethod = HttpMethod.Get,
            url = "$baseUrl/rest/totvsHealthPlans/v1/knowledgeBank?attachmentsKey=$externalCode",
            statusCode = HttpStatusCode.BadRequest,
        )

        val client = TotvsFileClient(baseUrl, secretKey, httpClient)

        val result = client.getGuiaFiles(externalCode)

        assertThat(result).isFailureOfType(ClientRequestException::class)
    }

    private fun httpClientMock(
        responseContent: String = "",
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        url: String,
        httpMethod: HttpMethod
    ): HttpClient {
        return HttpClient(MockEngine) {
            install(ContentNegotiation) { simpleGson() }
            expectSuccess = true
            engine {
                addHandler { request ->
                    if (request.method == httpMethod && request.url.toString() == url) {
                        respond(
                            responseContent,
                            statusCode,
                        )
                    } else {
                        respond(
                            responseContent,
                            statusCode,
                        )
                    }
                }
            }
        }
    }
}
