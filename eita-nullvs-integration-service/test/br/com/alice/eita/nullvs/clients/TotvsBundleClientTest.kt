package br.com.alice.eita.nullvs.clients

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import br.com.alice.eita.nullvs.ServiceConfig
import br.com.alice.eita.nullvs.converters.NullvsHealthcareBundleBatchRequestConverter.toTotvsBundleRequest
import br.com.alice.eita.nullvs.events.NullvsHealthcareBundleBatchRequestEvent
import br.com.alice.eita.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.eita.nullvs.models.Meta
import br.com.alice.eita.nullvs.models.TotvsHealthcareBundleBatchResponse
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundleBatchRequest
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundleComposition
import br.com.alice.eita.nullvs.models.bundle.NullvsHealthcareBundlePayload
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.content.TextContent
import io.mockk.clearAllMocks
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.net.URLDecoder
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.common.service.serialization.isoDateGson as gson


class TotvsBundleClientTest {

    private val baseUrl = ServiceConfig.TotvsBundle.baseURL()
    private val secretKey = ServiceConfig.TotvsBundle.secretKey()
    private val healthcareResourceId = RangeUUID.generate()

    @BeforeTest
    fun setup() {
        mockkObject(TotvsIntegrationMetric)
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#sendHealthcareBundle should send healthcare bundle to totvs successfully`() = runBlocking {
        val requestBody = NullvsHealthcareBundleBatchRequest(
            meta = Meta(
                eventId = RangeUUID.generate(),
                eventName = NullvsHealthcareBundleBatchRequestEvent.name,
                internalId = RangeUUID.generate(),
                internalModelName = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
                integrationEventName = NullvsHealthcareBundleBatchRequestEvent.name,
                externalModelName = EitaNullvsExternalModelType.BUNDLE,
                integratedAt = LocalDateTime.now(),
                originalTopic = NullvsHealthcareBundleBatchRequestEvent.name,
            ),
            action = "insert",
            date = LocalDate.now(),
            payload = listOf(
                NullvsHealthcareBundlePayload(
                    providerCnpj = "58549269000101",
                    description = "test",
                    validAfter = LocalDate.now(),
                    negotiationDate = LocalDate.now(),
                    healthcareResourceId = healthcareResourceId,
                    composition = listOf(NullvsHealthcareBundleComposition(RangeUUID.generate()))
                )
            )
        )

        val response = TotvsHealthcareBundleBatchResponse(
            httpStatus = "200",
            batch = "0001",
            idSoc = "123456",
            action = "insert"
        )
        val expectedJson = gson.toJson(response)

        val httpClient = HttpClient(MockEngine) {
            install(ContentNegotiation) { simpleGson() }
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${baseUrl}")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)
                    Assertions.assertThat(request.headers["Authorization"]).isEqualTo("Basic $secretKey")

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    Assertions.assertThat(textContent.text).isEqualTo(gson.toJson(requestBody.toTotvsBundleRequest()))

                    respond(expectedJson)
                }
            }
        }

        val client = TotvsBundleClient(baseUrl, secretKey, httpClient)

        val actualResponse = client.sendHealthcareBundle(requestBody)
        ResultAssert.assertThat(actualResponse).isSuccess()

        coVerifyOnce {
            TotvsIntegrationMetric.countTotvsIntegration(
                TotvsIntegrationMetric.Method.BUNDLE,
                TotvsIntegrationMetric.Status.SUCCESS
            )
        }
    }
}
