package br.com.alice.marauders.map.services.internal

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.marauders.map.client.RiskCalculationConfService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test


internal class RiskCalculatorServiceTest {
    private val healthConditionService: HealthConditionService = mockk()
    private val riskCalculationConfService: RiskCalculationConfService = mockk()
    private val personService: PersonService = mockk()

    private val service = RiskCalculatorService(
        healthConditionService,
        riskCalculationConfService,
        personService
    )

    private val personId = PersonId()
    private val healthCondition = TestModelFactory.buildHealthCondition()
    private val caseRecord = TestModelFactory.buildCaseRecord(
        personId = personId,
        description = Disease(
            type = Disease.Type.CID_10,
            value = healthCondition.code!!
        )
    )

    private val riskGroup = TestModelFactory.buildRiskGroup()
    private val riskCalculationConf = TestModelFactory.buildRiskCalculation(
        riskGroupId = riskGroup.id,
        healthConditionId = healthCondition.id
    )
    private val personAdult = TestModelFactory.buildPerson(personId, dateOfBirth = LocalDateTime.now().minusYears(18))


    @Test
    fun `#calculate with no caseRecords returns 0 success`() = runBlocking{
        val result = service.calculate(emptyList(), personId)

        assertThat(result).isSuccessWithData(0)

        coVerify { healthConditionService wasNot called }
        coVerify { riskCalculationConfService wasNot called }
        coVerify { personService wasNot called }
    }

    @Test
    fun `#calculate with decompensated condition`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
        val riskCalculationConf = riskCalculationConf.copy(decompensatedScore = 1)

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOf(riskCalculationConf).success()

        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(1)
    }

    @Test
    fun `#calculate with compensated condition`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.COMPENSATED)
        val riskCalculationConf = riskCalculationConf.copy(decompensatedScore = 1)

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOf(riskCalculationConf).success()

        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(0)
    }

    @Test
    fun `#calculate with cancelled condition`() = runBlocking {
        val caseRecord = caseRecord.copy(status = CaseStatus.CANCELLED)

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOf(riskCalculationConf).success()
        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(0)
    }

    @Test
    fun `#calculate when case is not on healthConditions table`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns emptyList<HealthCondition>().success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(0)
        coVerify { riskCalculationConfService wasNot called }
    }

    @Test
    fun `#calculate when cases have healthConditions belonging to the same riskGroup`() = runBlocking {
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A00",
        )
        val caseRecord2 = TestModelFactory.buildCaseRecord(
            personId = personId,
            description = Disease(
                type = Disease.Type.CID_10,
                value = healthCondition2.code!!
            )
        )
        val riskCalculationConf = riskCalculationConf.copy(severeScore = 3)
        val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
            riskGroupId = riskGroup.id,
            healthConditionId = healthCondition2.id,
            chronicScore = 2
        )

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value, caseRecord2.description.value)
        ) } returns listOf(healthCondition, healthCondition2).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id, healthCondition2.id)
        ) } returns listOf(riskCalculationConf, riskCalculationConf2).success()

        coEvery { personService.get(personId) } returns personAdult.success()


        val result = service.calculate(listOf(caseRecord, caseRecord2), personId)

        assertThat(result).isSuccessWithData(3)
    }

    @Test
    fun `#calculate when cases have healthConditions without riskGroup`() = runBlocking {
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A00",
        )
        val caseRecord2 = TestModelFactory.buildCaseRecord(
            personId = personId,
            description = Disease(
                type = Disease.Type.CID_10,
                value = healthCondition2.code!!
            )
        )
        val riskCalculationConf = riskCalculationConf.copy(
            severeScore = 3,
            riskGroupId = null,
        )
        val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
            riskGroupId = null,
            healthConditionId = healthCondition2.id,
            chronicScore = 2,
        )

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value, caseRecord2.description.value)
        ) } returns listOf(healthCondition, healthCondition2).success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id, healthCondition2.id)
        ) } returns listOf(riskCalculationConf, riskCalculationConf2).success()

        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOf(caseRecord, caseRecord2), personId)

        assertThat(result).isSuccessWithData(5)
    }

    @Test
    fun `#calculate should return final value expected`() = runBlocking {
        val lisOfHealthConditions = listOf(
            TestModelFactory.buildHealthCondition(
                code = "F809",
            ),TestModelFactory.buildHealthCondition(
                code = "F840",
            ),TestModelFactory.buildHealthCondition(
                code = "H651",
            ),TestModelFactory.buildHealthCondition(
                code = "J304",
            ),TestModelFactory.buildHealthCondition(
                code = "J459",
            )
        )

        val listOfCaseRecords = listOf(
            TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "F809"
                )
            ),TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "F840"
                )
            ),TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "H651"
                )
            ),TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "J304"
                )
            ),TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CID_10,
                    value = "J459"
                )
            ),
        )

        val listRiskConf = listOf(
            TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("0da663cc-ef61-419f-bc2e-f3432f4a5100"),
                healthConditionId = lisOfHealthConditions[0].id,
                chronicScore = 1,
                severeScore = 0,
                decompensatedScore = 0
            ),TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("0da663cc-ef61-419f-bc2e-f3432f4a5100"),
                healthConditionId = lisOfHealthConditions[1].id,
                chronicScore = 1,
                severeScore = 1,
                decompensatedScore = 1
            ),TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("6a79bfab-9570-49f1-9a66-ff5df49b2000"),
                healthConditionId = lisOfHealthConditions[2].id,
                chronicScore = 0,
                severeScore = 0,
                decompensatedScore = 0
            ),TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("33da9023-1e2b-4fd6-b50e-282d6ffc0600"),
                healthConditionId = lisOfHealthConditions[3].id,
                chronicScore = 0,
                severeScore = 0,
                decompensatedScore = 0
            ),TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("6c2de986-5b64-4328-b408-a1e1f987aa00"),
                healthConditionId = lisOfHealthConditions[4].id,
                chronicScore = 1,
                severeScore = 0,
                decompensatedScore = 1
            )
        )

        coEvery { healthConditionService.findByCodes(
            any()
        ) } returns lisOfHealthConditions.success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            any()
        ) } returns listRiskConf.success()

        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOfCaseRecords, personId)

        assertThat(result).isSuccessWithData(5)
    }

    @Test
    fun `#calculate should return 0 when case record have only ciap `() = runBlocking {
        val listOfCaseRecords = listOf(
            TestModelFactory.buildCaseRecord(
                personId = personId,
                severity = CaseSeverity.DECOMPENSATED,
                description = Disease(
                    type = Disease.Type.CIAP_2,
                    value = "F809"
                )
            )
        )

        val result = service.calculate(listOfCaseRecords, personId)

        assertThat(result).isSuccessWithData(0)

        coVerifyNone { healthConditionService.findByCodes(any()) }
        coVerifyNone { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyNone { personService.get(any()) }
    }

    @Test
    fun `#calculate should calculate only cids`() = runBlocking {
        val lisOfHealthConditions = listOf(
            TestModelFactory.buildHealthCondition(
                code = "F809",
            )
        )

        val caseRecordCid = TestModelFactory.buildCaseRecord(
            personId = personId,
            severity = CaseSeverity.DECOMPENSATED,
            description = Disease(
                type = Disease.Type.CID_10,
                value = "F809"
            )
        )
        val caseRecordCiap = TestModelFactory.buildCaseRecord(
            personId = personId,
            severity = CaseSeverity.DECOMPENSATED,
            description = Disease(
                type = Disease.Type.CIAP_2,
                value = "F840"
            )
        )
        val listOfCaseRecords = listOf(
            caseRecordCid, caseRecordCiap
        )

        val listRiskConf = listOf(
            TestModelFactory.buildRiskCalculation(
                riskGroupId = UUID.fromString("0da663cc-ef61-419f-bc2e-f3432f4a5100"),
                healthConditionId = lisOfHealthConditions[0].id,
                chronicScore = 1,
                severeScore = 0,
                decompensatedScore = 0
            )
        )

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecordCid.description.value)
        ) } returns lisOfHealthConditions.success()
        coEvery { riskCalculationConfService.getByHealthConditions(
            lisOfHealthConditions.map { it.id }
        ) } returns listRiskConf.success()

        coEvery { personService.get(personId) } returns personAdult.success()

        val result = service.calculate(listOfCaseRecords, personId)

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#calculate should return 1 when is child whit severity is COMPENSATED`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.COMPENSATED)
        val listOfConfs = listOf(riskCalculationConf.copy(
            childChronicScore = 0,
            childSevereScore = 1,
            childDecompensatedScore = 2
        ))

        val personChild = personAdult.copy(dateOfBirth = LocalDateTime.now().minusYears(5))

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOfConfs.success()

        coEvery { personService.get(personId) } returns personChild.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#calculate should return 3 when is child whit severity is DECOMPENSATED`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
        val listOfConfs = listOf(riskCalculationConf.copy(
            childChronicScore = 0,
            childSevereScore = 1,
            childDecompensatedScore = 2
        ))

        val personChild = personAdult.copy(dateOfBirth = LocalDateTime.now().minusYears(11))

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOfConfs.success()

        coEvery { personService.get(personId) } returns personChild.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(3)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#calculate should return 2 when person have date of birth null`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
        val listOfConfs = listOf(riskCalculationConf.copy(
            chronicScore = 1,
            severeScore = 1,
            decompensatedScore = 0
        ))

        val personChild = personAdult.copy(dateOfBirth = null)

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOfConfs.success()

        coEvery { personService.get(personId) } returns personChild.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(2)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#calculate should return 2 when child have 15 years old`() = runBlocking {
        val caseRecord = caseRecord.copy(severity = CaseSeverity.DECOMPENSATED)
        val listOfConfs = listOf(riskCalculationConf.copy(
            chronicScore = 1,
            severeScore = 1,
            decompensatedScore = 0
        ))

        val personChild = personAdult.copy(dateOfBirth = LocalDateTime.now().minusYears(15))

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value)
        ) } returns listOf(healthCondition).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id)
        ) } returns listOfConfs.success()

        coEvery { personService.get(personId) } returns personChild.success()

        val result = service.calculate(listOf(caseRecord), personId)

        assertThat(result).isSuccessWithData(2)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#calculate should return 3 when child is many risks calculation conf of group`() = runBlocking {
        val healthCondition2 = TestModelFactory.buildHealthCondition(
            code = "A00",
        )
        val caseRecord2 = TestModelFactory.buildCaseRecord(
            personId = personId,
            description = Disease(
                type = Disease.Type.CID_10,
                value = healthCondition2.code!!
            )
        )
        val riskCalculationConf = riskCalculationConf.copy(
            childChronicScore = 2,
            childSevereScore = 1,
            childDecompensatedScore = 0,
            riskGroupId = riskGroup.id,
        )
        val riskCalculationConf2 = TestModelFactory.buildRiskCalculation(
            riskGroupId = riskGroup.id,
            healthConditionId = healthCondition2.id,
            childChronicScore = 0,
            childSevereScore = 1,
            childDecompensatedScore = 0,
        )

        val personChild = personAdult.copy(dateOfBirth = LocalDateTime.now().minusYears(11))

        coEvery { healthConditionService.findByCodes(
            listOf(caseRecord.description.value, caseRecord2.description.value)
        ) } returns listOf(healthCondition, healthCondition2).success()

        coEvery { riskCalculationConfService.getByHealthConditions(
            listOf(healthCondition.id, healthCondition2.id)
        ) } returns listOf(riskCalculationConf, riskCalculationConf2).success()

        coEvery { personService.get(personId) } returns personChild.success()

        val result = service.calculate(listOf(caseRecord, caseRecord2), personId)

        assertThat(result).isSuccessWithData(3)

        coVerifyOnce { healthConditionService.findByCodes(any()) }
        coVerifyOnce { riskCalculationConfService.getByHealthConditions(any()) }
        coVerifyOnce { personService.get(any()) }
    }
}
