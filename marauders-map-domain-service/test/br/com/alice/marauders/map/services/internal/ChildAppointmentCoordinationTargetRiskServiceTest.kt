package br.com.alice.marauders.map.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentCoordination.Type
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.Risk.ReferenceModel
import br.com.alice.wanda.client.AppointmentCoordinationService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChildAppointmentCoordinationTargetRiskServiceTest {
    private val appointmentCoordinationService: AppointmentCoordinationService = mockk()

    private val service = ChildAppointmentCoordinationTargetRiskService(
        appointmentCoordinationService
    )

    @BeforeTest
    fun before() {
        clearAllMocks()
    }

    private val healthConditionId = RangeUUID.generate()
    private val caseRecord = TestModelFactory.buildCaseRecord(healthConditionId = healthConditionId)
    private val risk = TestModelFactory.buildRisk(
        referencedModels = listOf(
            Risk.ReferencedModel(
                id = caseRecord.id,
                ReferenceModel.CASE_RECORD
            )
        )
    )
    private val appointmentCoordination1 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.TERTIARY_INTENTION_EMERGENCY
    )
    private val appointmentCoordination2 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.TERTIARY_INTENTION_EMERGENCY
    )
    private val appointmentCoordination3 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.TERTIARY_INTENTION_EMERGENCY
    )
    private val appointmentCoordination4 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.TERTIARY_INTENTION_HOSPITALIZATION
    )
    private val appointmentCoordination5 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )
    private val appointmentCoordination6 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )
    private val appointmentCoordination7 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )
    private val appointmentCoordination8 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )
    private val appointmentCoordination9 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )
    private val appointmentCoordination10 = TestModelFactory.buildAppointmentCoordination(
        personId = risk.personId,
        appointmentType = Type.PHYSICIAN_APPOINTMENT
    )

    @Test
    fun `#isTargetBasedOnAppointmentCoordination - should return true when there is three events of emergency`() = runBlocking {
        val eventList = listOf(
            appointmentCoordination1,
            appointmentCoordination2,
            appointmentCoordination3
        )

        val expected = risk.referencedModels + listOf(
            Risk.ReferencedModel(appointmentCoordination1.appointmentId, ReferenceModel.TERTIARY_INTENTION_EMERGENCY),
            Risk.ReferencedModel(appointmentCoordination2.appointmentId, ReferenceModel.TERTIARY_INTENTION_EMERGENCY),
            Risk.ReferencedModel(appointmentCoordination3.appointmentId, ReferenceModel.TERTIARY_INTENTION_EMERGENCY),
        )

        coEvery { appointmentCoordinationService.getTargetAppointmentsByPersonId(risk.personId) } returns eventList.success()

        val result = service.isTargetBasedOnAppointmentCoordination(risk)

        ResultAssert.assertThat(result).isSuccessWithData(Pair(true, expected))
        coVerifyOnce{ appointmentCoordinationService.getTargetAppointmentsByPersonId(any()) }
    }

    @Test
    fun `#isTargetBasedOnAppointmentCoordination - should return true when there is three events of physician appointment`() = runBlocking {
        val eventList = listOf(
            appointmentCoordination5,
            appointmentCoordination6,
            appointmentCoordination7,
            appointmentCoordination8,
            appointmentCoordination9,
            appointmentCoordination10

        )

        val expected = risk.referencedModels + listOf(
            Risk.ReferencedModel(appointmentCoordination5.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT),
            Risk.ReferencedModel(appointmentCoordination6.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT),
            Risk.ReferencedModel(appointmentCoordination7.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT),
            Risk.ReferencedModel(appointmentCoordination8.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT),
            Risk.ReferencedModel(appointmentCoordination9.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT),
            Risk.ReferencedModel(appointmentCoordination10.appointmentId, ReferenceModel.PHYSICIAN_APPOINTMENT)
        )

        coEvery { appointmentCoordinationService.getTargetAppointmentsByPersonId(risk.personId) } returns eventList.success()

        val result = service.isTargetBasedOnAppointmentCoordination(risk)

        ResultAssert.assertThat(result).isSuccessWithData(Pair(true, expected))
        coVerifyOnce{ appointmentCoordinationService.getTargetAppointmentsByPersonId(any()) }
    }

    @Test
    fun `#isTargetBasedOnAppointmentCoordination - should return false when there less events of any case`() = runBlocking {
        val eventList = listOf(
            appointmentCoordination1,
            appointmentCoordination2
        )

        coEvery { appointmentCoordinationService.getTargetAppointmentsByPersonId(risk.personId) } returns eventList.success()

        val result = service.isTargetBasedOnAppointmentCoordination(risk)

        ResultAssert.assertThat(result).isSuccessWithData(Pair(false, emptyList()))
        coVerifyOnce{ appointmentCoordinationService.getTargetAppointmentsByPersonId(any()) }
    }

    @Test
    fun `#isTargetBasedOnAppointmentCoordination - should return true when there is one event of hospitalization`() = runBlocking {
        val eventList = listOf(
            appointmentCoordination1,
            appointmentCoordination2,
            appointmentCoordination5,
            appointmentCoordination6,
            appointmentCoordination4
        )

        val expected = risk.referencedModels + listOf(
            Risk.ReferencedModel(appointmentCoordination4.appointmentId, ReferenceModel.TERTIARY_INTENTION_HOSPITALIZATION),
        )

        coEvery { appointmentCoordinationService.getTargetAppointmentsByPersonId(risk.personId) } returns eventList.success()

        val result = service.isTargetBasedOnAppointmentCoordination(risk)

        ResultAssert.assertThat(result).isSuccessWithData(Pair(true, expected))
        coVerifyOnce{ appointmentCoordinationService.getTargetAppointmentsByPersonId(any()) }
    }
}
