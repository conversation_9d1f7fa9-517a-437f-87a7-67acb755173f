package br.com.alice.marauders.map.controllers

import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareMap
import br.com.alice.data.layer.services.HealthcareMapDataService
import br.com.alice.marauders.map.services.internal.HealthcareMapService
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test


internal class BackfillControllerTest : RoutesTestHelper() {
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val healthcareMapService: HealthcareMapService = mockk()
    private val healthcareMapDataService: HealthcareMapDataService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()

    private val controller = BackfillController(
        personClinicalAccountService,
        healthcareMapService,
        healthcareMapDataService,
        personInternalReferenceService
    )

    private val person = TestModelFactory.buildPerson()
    private val personReference = TestModelFactory.buildPersonInternalReference(person.id)
    private val clinicalAccountInfo = TestModelFactory.buildPersonClinicalAccount(personId = person.id)
    private val healthcareMap = TestModelFactory.buildHealthcareMap(personId = person.id)

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(personClinicalAccountService)
        clearMocks(healthcareMapService)
        clearMocks(personInternalReferenceService)

        module.single { controller }
    }

    @Test
    fun `#generateHealthcareMapByPersonId calls service with upsert request`() = runBlocking {
        val request = GenerateHealthcareMapByPersonRequest(
            personId = person.id.toUUID()
        )

        coEvery { personClinicalAccountService.getByPersonId(person.id) } returns clinicalAccountInfo.success()
        coEvery {
            healthcareMapService.upsertHealthcareTeam(person.id, clinicalAccountInfo.healthcareTeamId, any())
        } returns healthcareMap.success()

        post("/backfill/generate_healthcare_map_by_person_id", body = request) { response ->
            ResponseAssert.assertThat(response).isSuccessfulJson()
            val content: HealthcareMap = response.bodyAsJson()
            assertThat(content).isEqualTo(healthcareMap)

            coVerifyOnce { personClinicalAccountService.getByPersonId(any()) }
            coVerifyOnce { healthcareMapService.upsertHealthcareTeam(any(), any(), any()) }
        }
    }

    @Test
    fun `#generateHealthcareMapByMemberInternalCode calls service with upsert request`() = runBlocking {
        val request = GenerateHealthcareMapByMemberInternalCodeRequest(
            memberInternalCode = personReference.internalCode
        )

        coEvery { personInternalReferenceService.getByInternalCode(request.memberInternalCode) } returns personReference.success()
        coEvery { personClinicalAccountService.getByPersonId(personReference.personId) } returns clinicalAccountInfo.success()
        coEvery {
            healthcareMapService.upsertHealthcareTeam(clinicalAccountInfo.personId, clinicalAccountInfo.healthcareTeamId, any())
        } returns healthcareMap.success()

        post("/backfill/generate_healthcare_map_by_member_internal_code", body = request) { response ->
            ResponseAssert.assertThat(response).isSuccessfulJson()
            val content: HealthcareMap = response.bodyAsJson()
            assertThat(content).isEqualTo(healthcareMap)

            coVerifyOnce { personInternalReferenceService.getByInternalCode(any()) }
            coVerifyOnce { personClinicalAccountService.getByPersonId(any()) }
            coVerifyOnce { healthcareMapService.upsertHealthcareTeam(any(), any(), any()) }
        }
    }

    @Test
    fun `#populateIds should populate ids given range`() = runBlocking {
        val request = PopulateIdsRequest(
            start = 0,
            end = 10,
        )
        val range = IntRange(request.start, request.end)
        val anotherHcMap = TestModelFactory.buildHealthcareMap()
        val anotherClinicalAccInfo = TestModelFactory.buildPersonClinicalAccount(personId = anotherHcMap.personId)

        coEvery { healthcareMapService.getByRange(range) } returns listOf(healthcareMap, anotherHcMap).success()

        coEvery { personClinicalAccountService.getByPersonId(healthcareMap.personId) } returns clinicalAccountInfo.success()
        coEvery { personClinicalAccountService.getByPersonId(anotherHcMap.personId) } returns anotherClinicalAccInfo.success()

        coEvery {
            healthcareMapDataService.update(
                healthcareMap.copy(
                    referenceNurseGroupId = clinicalAccountInfo.referenceNursesGroupId,
                    multiStaffIds = clinicalAccountInfo.multiStaffIds
                )
            )
        } returns healthcareMap.success()

        coEvery {
            healthcareMapDataService.update(
                anotherHcMap.copy(
                    referenceNurseGroupId = anotherClinicalAccInfo.referenceNursesGroupId,
                    multiStaffIds = anotherClinicalAccInfo.multiStaffIds
                )
            )
        } returns anotherHcMap.success()

        post("/backfill/populate_healthcare_map_ids", body = request) { response ->
            ResponseAssert.assertThat(response).isSuccessfulJson()
            val content: List<HealthcareMap> = response.bodyAsJson()
            assertThat(content).isEqualTo(listOf(healthcareMap, anotherHcMap))

            coVerifyOnce { healthcareMapService.getByRange(any()) }
            coVerify(exactly = 2) { personClinicalAccountService.getByPersonId(any()) }
            coVerify(exactly = 2) { healthcareMapDataService.update(any()) }
        }
    }
}
