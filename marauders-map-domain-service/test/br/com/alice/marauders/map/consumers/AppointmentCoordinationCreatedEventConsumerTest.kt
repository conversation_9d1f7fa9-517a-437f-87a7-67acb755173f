package br.com.alice.marauders.map.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentCoordination.Type.EXAM
import br.com.alice.data.layer.models.AppointmentCoordination.Type.TERTIARY_INTENTION_EMERGENCY
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.marauders.map.client.TargetRiskRouterService
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AppointmentCoordinationCreatedEventConsumerTest : ConsumerTest() {
    private val targetRiskRouterService: TargetRiskRouterService = mockk()

    private val consumer = AppointmentCoordinationCreatedEventConsumer(targetRiskRouterService)

    private val risk = TestModelFactory.buildRisk()
    private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination(appointmentType = TERTIARY_INTENTION_EMERGENCY)
    private val event = AppointmentCoordinationCreatedEvent(appointmentCoordination)

    @Test
    fun `#validateRisk - should validate risk and return it with target type`() = runBlocking {
        val expected = risk.copy(
            riskDescription = RiskDescription.TARGET,
            addedBy = Risk.AddedBy(null, Risk.AddedByType.SYSTEM)
        )

        coEvery {
            targetRiskRouterService.validateByAge(appointmentCoordination.personId)
        } returns expected.success()

        val result = consumer.validateRisk(event)
        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { targetRiskRouterService.validateByAge(any()) }
    }

    @Test
    fun `#validateRisk - should not validate risk and return it with target type`() = runBlocking {
        val appointmentCoordination = appointmentCoordination.copy(appointmentType = EXAM)
        val event = AppointmentCoordinationCreatedEvent(appointmentCoordination)

        val result = consumer.validateRisk(event)
        ResultAssert.assertThat(result).isSuccessWithData(false)
        coVerifyNone { targetRiskRouterService.validateByAge(any()) }
    }
}
