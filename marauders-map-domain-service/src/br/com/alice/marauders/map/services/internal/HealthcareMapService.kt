package br.com.alice.marauders.map.services.internal

import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.HealthcareMap
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.services.HealthcareMapDataService
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.marauders.map.event.HealthcareMapUpdatedEvent
import br.com.alice.marauders.map.models.PresetsSearchEngine
import br.com.alice.wanda.client.PersonHealthEventService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class HealthcareMapService(
    private val healthcareMapDataService: HealthcareMapDataService,
    private val caseRecordService: CaseRecordService,
    private val personHealthEventService: PersonHealthEventService,
    private val riskService: RiskService,
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val kafkaProducerService: KafkaProducerService

){

    suspend fun getByPersonId(personId: PersonId): Result<HealthcareMap, Throwable> =
        healthcareMapDataService.findOne { where { this.personId.eq(personId) } }

    suspend fun getPossiblyExpiredTargets(): Result<List<HealthcareMap>, Throwable> =
        healthcareMapDataService.find {
            where {
                this.riskDescription.eq(RiskDescription.TARGET)
                    .and(this.riskDate.lessEq(LocalDateTime.now().minusMonths(6)))
            }
        }

    suspend fun upsertHealthcareTeam(personId: PersonId, newHealthcareTeamId: UUID, updatedAt: LocalDateTime): Result<HealthcareMap, Throwable> =
        getByPersonId(personId).flatMap {
            if (updatedAt > it.healthcareTeamAddedAt){
                logger.info("HealthcareMapService::upsertHealthcareTeam - will update row")
                val personClinicalAccount = personClinicalAccountService.getByPersonId(personId).get()

                logger.info(
                    "HealthcareMapService::upsertHealthcareTeam personClinicalAccountInfo",
                    "reference_nurse_group_id" to personClinicalAccount.referenceNursesGroupId,
                    "multi_staff_ids" to personClinicalAccount.multiStaffIds
                )

                healthcareMapDataService.update(
                    it.copy(
                        healthcareTeamId = newHealthcareTeamId,
                        healthcareTeamAddedAt = updatedAt,
                        referenceNurseGroupId = personClinicalAccount.referenceNursesGroupId,
                        multiStaffIds = personClinicalAccount.multiStaffIds
                    )
                ).then { notifyUpdate(it) }
            } else {
                logger.info(
                    "HealthcareMapService::upsertHealthcareTeam - skipped due to dates",
                    "updated_at" to updatedAt,
                    "healthcare_team_added_at" to it.healthcareTeamAddedAt
                )
                it.success()
            }
        }.coFoldNotFound {
            logger.info("HealthcareMapService::upsertHealthcareTeam - will create a healthcare_map record")
            createHealthcareMap(personId, newHealthcareTeamId, updatedAt)
        }

    private suspend fun createHealthcareMap(personId: PersonId, newHealthcareTeamId: UUID, updatedAt: LocalDateTime): Result<HealthcareMap, Throwable> {
            val pairCaseRecord = getCaseRecords(personId)
            val healthEvents = getHealthEvents(personId)
            val personClinicalAccount = personClinicalAccountService.getByPersonId(personId).get()
            val riskCurrent = riskService.getCurrent(personId).getOrNullIfNotFound()
            val presetIds = PresetsSearchEngine.getByCodes(pairCaseRecord.second.map { it.description.value }).map { it.id }

            logger.info(
                "HealthcareMapService::createHealthcareMap personClinicalAccountInfo",
                "reference_nurse_group_id" to personClinicalAccount.referenceNursesGroupId,
                "multi_staff_ids" to personClinicalAccount.multiStaffIds
            )

            val healthcareMap = HealthcareMap(
                personId = personId,
                healthcareTeamId = newHealthcareTeamId,
                cases = pairCaseRecord.second,
                totalCompensated = pairCaseRecord.second.count { c -> c.severity == CaseSeverity.COMPENSATED },
                totalDecompensated = pairCaseRecord.second.count { c -> c.severity == CaseSeverity.DECOMPENSATED },
                healthEvents = healthEvents,
                caseRecordAddedAt = pairCaseRecord.first,
                riskAddedAt = riskCurrent?.addedAt,
                riskValue = riskCurrent?.finalValue,
                riskDescription = riskCurrent?.getMinnesotaRisk(),
                cidGroups = presetIds,
                healthcareTeamAddedAt = updatedAt,
                referenceNurseGroupId = personClinicalAccount.referenceNursesGroupId,
                multiStaffIds = personClinicalAccount.multiStaffIds,
                public = riskCurrent?.let { HealthcareMap.Public.valueOf(it.public) } ?: HealthcareMap.Public.DEFAULT
            )

            return healthcareMapDataService.add(healthcareMap).then { notifyUpdate(it) }
        }

    suspend fun getHealthEvents(personId: PersonId): HealthcareMap.HealthEvents = coroutineScope {
        val nextDeferred = async {
            personHealthEventService.getNextEvent(
                personId,
                HealthcareMapHealthEventService.allowedCategories,
                listOf(PersonHealthEventStatus.NOT_STARTED, PersonHealthEventStatus.IN_PROGRESS)
            )
        }

        val lastDeferred = async {
            personHealthEventService.getLastEvent(
                personId,
                HealthcareMapHealthEventService.allowedCategories,
                listOf(PersonHealthEventStatus.FINISHED, PersonHealthEventStatus.FINISHED_BY_INACTIVITY)
            )
        }

        val nextEvent = nextDeferred.await().getOrNullIfNotFound()
        val lastEvent = lastDeferred.await().getOrNullIfNotFound()

        HealthcareMap.HealthEvents(
            nextHealthEvent = if (nextEvent != null) {
                buildHealthEvent(nextEvent)
            } else null,
            lastHealthEvent = if (lastEvent != null) {
                buildHealthEvent(lastEvent)
            } else null,
        )
    }

    private fun buildHealthEvent(nextHealthEvent: PersonHealthEvent) =
        HealthcareMap.HealthEvent(
            id = nextHealthEvent.id,
            category = nextHealthEvent.category,
            title = nextHealthEvent.title,
            dueDate = nextHealthEvent.dueDate
        )

    private suspend fun getCaseRecords(personId: PersonId): Pair<LocalDateTime?, List<HealthcareMap.HealthcareMapCase>> {
        var caseRecordAddedAt: LocalDateTime? = null
        val healthcareMapCases = caseRecordService.getCasesByPersonId(personId).flatMap { listCases ->
            if (listCases.isEmpty()) return@flatMap emptyList<HealthcareMap.HealthcareMapCase>().success()

            listCases.filter { it.status != CaseStatus.CANCELLED && it.severity != CaseSeverity.INACTIVE }
                .map { caseRecord ->
                    if (caseRecordAddedAt == null || caseRecord.addedAt > caseRecordAddedAt) caseRecordAddedAt =
                        caseRecord.addedAt

                    HealthcareMap.HealthcareMapCase(
                        caseId = caseRecord.caseId,
                        description = caseRecord.description,
                        severity = caseRecord.severity
                    )
                }.success()
        }
        return Pair(caseRecordAddedAt, healthcareMapCases.get())
    }

    suspend fun updateRisk(
        personId: PersonId,
        riskDescription: RiskDescription,
        riskValue: Int,
        riskAddedAt: LocalDateTime,
        public: HealthcareMap.Public? = null
    ): Result<HealthcareMap, Throwable> =
        getByPersonId(personId).flatMap {
            healthcareMapDataService.update(it.copy(
                riskDescription = riskDescription,
                riskValue = riskValue,
                riskAddedAt = riskAddedAt,
                public = public ?: HealthcareMap.Public.DEFAULT
            ))
        }.then { notifyUpdate(it) }

    suspend fun updateHealthEvents(personId: PersonId, healthEvents: HealthcareMap.HealthEvents): Result<HealthcareMap, Throwable> =
        getByPersonId(personId).flatMap {
            healthcareMapDataService.update(it.copy(healthEvents = healthEvents))
        }.then { notifyUpdate(it) }

    suspend fun updateCases(
        personId: PersonId,
        cases: List<HealthcareMap.HealthcareMapCase>,
        caseAddedAt: LocalDateTime?,
        presetIds: List<UUID> = emptyList()
    ) = getByPersonId(personId).flatMap {
        healthcareMapDataService.update(
            it.copy(
                cases = cases,
                caseRecordAddedAt = caseAddedAt,
                totalCompensated = cases.count {c -> c.severity == CaseSeverity.COMPENSATED },
                totalDecompensated = cases.count {c -> c.severity == CaseSeverity.DECOMPENSATED },
                cidGroups = presetIds
            )
        )
    }.then { notifyUpdate(it) }

    suspend fun deleteByPersonClinicalAccount(personClinicalAccount: PersonClinicalAccount, deletedAt: LocalDateTime): Result<Boolean, Throwable> {
        return healthcareMapDataService.findOne {
            where { this.personId.eq(personClinicalAccount.personId) }
        }.flatMap {
            if (deletedAt > it.healthcareTeamAddedAt && personClinicalAccount.healthcareTeamId == it.healthcareTeamId){
                healthcareMapDataService.delete(it)
            }
            true.success()
        }.coFoldNotFound { true.success() }
    }

    suspend fun getByPersonIds(personIds: List<PersonId>): Result<List<HealthcareMap>, Throwable> =
        healthcareMapDataService.find { where { this.personId.inList(personIds) } }

    suspend fun getByRange(range: IntRange): Result<List<HealthcareMap>, Throwable> =
        healthcareMapDataService.find {
            orderBy { createdAt }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }

    private suspend fun notifyUpdate(healthcareMap: HealthcareMap) =
        HealthcareMapUpdatedEvent(healthcareMap).let { kafkaProducerService.produce(it) }

}
