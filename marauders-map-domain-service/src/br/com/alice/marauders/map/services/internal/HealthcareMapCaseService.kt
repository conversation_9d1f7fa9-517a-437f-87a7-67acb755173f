package br.com.alice.marauders.map.services.internal

import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CaseStatus
import br.com.alice.data.layer.models.HealthcareMap
import br.com.alice.marauders.map.models.PresetsSearchEngine
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class HealthcareMapCaseService(
    private val healthcareMapService: HealthcareMapService,
) {

    suspend fun updateHealthcareMapFromCaseRecord(caseRecord: CaseRecord): Result<Boolean, Throwable> =
        healthcareMapService.getByPersonId(caseRecord.personId)
            .flatMap { healthcareMap -> updateHealthcareMap(healthcareMap, caseRecord) }
            .coFoldNotFound { true.success() }

    private suspend fun updateHealthcareMap(
        healthcareMap: HealthcareMap, caseRecord: CaseRecord
    ): Result<Boolean, Throwable> {
        val newCases = updateCasesList(healthcareMap.cases, caseRecord)
        val lastCaseRecordEdition = if (healthcareMap.caseRecordAddedAt == null){
            caseRecord.addedAt
        } else {
            maxOf(caseRecord.addedAt, healthcareMap.caseRecordAddedAt!!)
        }
        val presetIds = PresetsSearchEngine.getByCodes(newCases.map { it.description.value }).map { it.id }
        return healthcareMapService.updateCases(caseRecord.personId, newCases, lastCaseRecordEdition, presetIds).map { true }
    }

    private fun updateCasesList(
        list: List<HealthcareMap.HealthcareMapCase>,
        caseRecord: CaseRecord
    ): List<HealthcareMap.HealthcareMapCase> {
        val newHealthcareMapCase = buildHealthcareMapCaseFromCaseRecord(caseRecord)
            ?: return deleteFromList(list, caseRecord.caseId)

        return if (list.any { it.caseId == newHealthcareMapCase.caseId }) updateList(list, newHealthcareMapCase)
        else addToCasesList(list, newHealthcareMapCase)
    }

    private fun buildHealthcareMapCaseFromCaseRecord(caseRecord: CaseRecord): HealthcareMap.HealthcareMapCase? {
        return if (caseRecord.status != CaseStatus.CANCELLED && caseRecord.severity != CaseSeverity.INACTIVE) {
            HealthcareMap.HealthcareMapCase(
                caseId = caseRecord.caseId,
                description = caseRecord.description,
                severity = caseRecord.severity
            )
        } else {
            null
        }
    }

    private fun updateList(list: List<HealthcareMap.HealthcareMapCase>, newHealthcareMapCase: HealthcareMap.HealthcareMapCase): List<HealthcareMap.HealthcareMapCase> =
        list.map { if(it.caseId == newHealthcareMapCase.caseId) newHealthcareMapCase else it  }

    private fun addToCasesList(list: List<HealthcareMap.HealthcareMapCase>, newHealthcareMapCase: HealthcareMap.HealthcareMapCase): List<HealthcareMap.HealthcareMapCase> =
        list.plus(newHealthcareMapCase)

    private fun deleteFromList(list: List<HealthcareMap.HealthcareMapCase>, caseId: UUID): List<HealthcareMap.HealthcareMapCase> =
        list.filterNot { it.caseId == caseId }


}
