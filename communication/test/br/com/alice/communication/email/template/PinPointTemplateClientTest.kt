package br.com.alice.communication.email.template

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import software.amazon.awssdk.services.pinpoint.PinpointClient
import software.amazon.awssdk.services.pinpoint.model.EmailTemplateResponse
import software.amazon.awssdk.services.pinpoint.model.GetEmailTemplateRequest
import software.amazon.awssdk.services.pinpoint.model.GetEmailTemplateResponse
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class PinPointTemplateClientTest {

    private lateinit var client: PinpointClient
    private lateinit var sutClient: PinPointEmailTemplateClient

    @BeforeTest
    fun setup() {
        this.client = mockk()
        this.sutClient = PinPointEmailTemplateClient(client)
    }

    @Test
    fun `#getTemplates - given a templateName, should return as expected`() = runBlocking<Unit> {
        val templateName = "templateName"
        val html = "html"
        val text = "text"
        val subject = "subject"

        val emailTemplateResponse = EmailTemplateResponse.builder().htmlPart(html).textPart(text).subject(subject).build()
        val getEmailTemplateResponse = GetEmailTemplateResponse.builder().emailTemplateResponse(emailTemplateResponse).build()

        coEvery { client.getEmailTemplate(match<GetEmailTemplateRequest> { it.templateName() == templateName }) } returns getEmailTemplateResponse

        val emailTemplate = sutClient.getTemplate(templateName)

        assertThat(emailTemplate.html).isEqualTo(html)
        assertThat(emailTemplate.text).isEqualTo(text)
        assertThat(emailTemplate.subject).isEqualTo(subject)
    }
}
