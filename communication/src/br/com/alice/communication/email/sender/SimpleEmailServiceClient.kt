package br.com.alice.communication.email.sender

import br.com.alice.common.logging.logger
import br.com.alice.communication.email.model.CalendarEventEmailRequest
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.EmailRequest
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.ses.SesClient
import software.amazon.awssdk.services.ses.model.RawMessage
import software.amazon.awssdk.services.ses.model.SendRawEmailRequest
import java.util.Properties
import javax.activation.DataHandler
import javax.mail.BodyPart
import javax.mail.Message.RecipientType
import javax.mail.Multipart
import javax.mail.Session
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeBodyPart
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeMultipart
import javax.mail.util.ByteArrayDataSource


@Deprecated(
    "Use PinPointEmailClient instead. Messages sent by PinPointEmailClient has events visibility on metabase" +
            " table: Tech Models/spectrum_dds/pinpoint_events."
)
class SimpleEmailServiceClient(private val client: SesClient) : EmailSenderClient() {

    override suspend fun send(request: EmailRequest): EmailReceipt {

        val mimeMessage = buildMimeMessage(request)

        val messageId = request.messageId
        if (!messageId.isNullOrEmpty()) {
            mimeMessage.addHeader("Message-ID", "<$<EMAIL>>")
            mimeMessage.addHeader("In-Reply-To", "<$<EMAIL>>")
            mimeMessage.addHeader("References", "<$<EMAIL>>")
        }

        return try {
            val messageByteArray = getMessageByteArray(mimeMessage)
            val rawMessage = RawMessage.builder()
                .data(SdkBytes.fromByteArray(messageByteArray))
                .build()

            val sendRawEmailRequest = SendRawEmailRequest.builder()
                .rawMessage(rawMessage)
                .build()

            val response = client.sendRawEmail(sendRawEmailRequest)

            logger.info("E-mail sent", "message_id" to response.messageId())
            EmailReceipt(response.messageId())
        } catch (ex: Exception) {
            logger.error("Error while sending e-mail", ex)
            EmailReceipt("ERROR")
        }
    }

    override suspend fun sendCalendarEventEmail(request: CalendarEventEmailRequest): EmailReceipt {

        val mimeMessage = setHeaders(
            subject = request.subject,
            fromAddress = request.from.email,
            fromName = request.from.name,
            to = request.to.map { it.email },
            bcc = request.bcc.map { it.email }
        )
        val messageBodyPart: BodyPart = MimeBodyPart()

        messageBodyPart.dataHandler = DataHandler(
            ByteArrayDataSource(request.iCalEvent, "text/calendar")
        )

        val multipart: Multipart = MimeMultipart()
        multipart.addBodyPart(messageBodyPart)

        request.body?.let {
            val mimeWrap = getBodyContainer(html = it)
            multipart.addBodyPart(mimeWrap)
        }

        mimeMessage.setContent(multipart)

        return try {
            val messageByteArray = getMessageByteArray(mimeMessage)
            val rawMessage = RawMessage.builder()
                .data(SdkBytes.fromByteArray(messageByteArray))
                .build()

            val sendRawEmailRequest = SendRawEmailRequest.builder()
                .rawMessage(rawMessage)
                .build()

            val response = client.sendRawEmail(sendRawEmailRequest)

            logger.info("E-mail sent", "message_id" to response.messageId())
            EmailReceipt(response.messageId())
        } catch (ex: Exception) {
            logger.error("Error while sending e-mail", ex)
            EmailReceipt("ERROR")
        }
    }

    private fun setHeaders(
        subject: String,
        fromAddress: String,
        fromName: String,
        to: List<String>,
        bcc: List<String> = emptyList(),
        messageId: String? = null,
    ): MimeMessage {
        val session = Session.getDefaultInstance(Properties())
        val mimeMessage = MimeMessage(session)
        mimeMessage.setSubject(subject, "UTF-8")
        mimeMessage.setFrom(InternetAddress(fromAddress, fromName))
        mimeMessage.setRecipients(
            RecipientType.TO,
            InternetAddress.parse(to.joinToString())
        )

        if (!messageId.isNullOrEmpty()) {
            mimeMessage.addHeader("Message-ID", "<$<EMAIL>>")
            mimeMessage.addHeader("In-Reply-To", "<$<EMAIL>>")
            mimeMessage.addHeader("References", "<$<EMAIL>>")
        }

        bcc.isNotEmpty().let {
            mimeMessage.setRecipients(
                RecipientType.BCC,
                InternetAddress.parse(bcc.joinToString())
            )
        }

        return mimeMessage
    }

}
