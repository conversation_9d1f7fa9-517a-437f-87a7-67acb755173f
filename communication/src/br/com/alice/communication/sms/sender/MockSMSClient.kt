package br.com.alice.communication.sms.sender

import br.com.alice.common.RangeUUID
import br.com.alice.common.logging.logger
import br.com.alice.communication.sms.model.SMSReceipt
import br.com.alice.communication.sms.model.SMSRequest

object MockSMSClient: SMSSenderClient {

    val sentSMSs = mutableListOf<SMSRequest>()

    override suspend fun send(request: SMSRequest): SMSReceipt {
        val smsReceipt = SMSReceipt(RangeUUID.generate().toString())

        logger.info("MockSMSClient - sms sent",
            "request" to request,
            "smsReceipt" to smsReceipt
        )

        sentSMSs.add(request)

        return smsReceipt
    }
}
