package br.com.alice.communication.apns

import io.jsonwebtoken.JwtException
import io.jsonwebtoken.Jwts
import org.apache.commons.codec.binary.Base64
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Date

class ApnsJWT(
    private val apnsConfig: ApnsConfig
) {
    companion object {
        const val APNS_JWT_EXPIRATION = 3000000L

        private var jwt: String? = null
        private var issueAt: Date? = null
        private var privateKey: PrivateKey? = null
    }

    fun getJwt(): String {
        if (jwt == null || !isJwtValid(privateKey!!)) {
            generatePrivateKey(apnsConfig.accessKey)
            jwt = buildJWT(apnsConfig)
        }
        return jwt!!
    }

    private fun generatePrivateKey(accessKey: String) {
        if (privateKey == null) {
            privateKey = KeyFactory.getInstance("EC").generatePrivate(PKCS8EncodedKeySpec(Base64().decode(accessKey)))
        }
    }

    private fun isJwtValid(key: PrivateKey): Boolean =
        try {
            Jwts.parserBuilder()
                .setSigningKey(key)
                .build()

            issueAt != null && Date().time - issueAt!!.time < APNS_JWT_EXPIRATION
        } catch (ex: JwtException) {
            false
        }

    private fun buildJWT(config: ApnsConfig): String? {
        issueAt = Date()
        return Jwts.builder()
            .setHeaderParam("alg", "ES256")
            .setHeaderParam("kid", config.jwtKid)
            .claim("iss", config.jwtIss)
            .setIssuedAt(issueAt)
            .signWith(privateKey)
            .compact()
    }
}
