package br.com.alice.communication.crm.braze.client.messaging


data class Message(
    val externalUserIds: List<String>,
    val recipientSubscriptionState: String? = null,
    val campaignId: String? = null,
    val messages: MessageContent
)

data class MessageContent(
    val email: EmailContent? = null
)

data class EmailContent(
    val appId: String,
    val subject: String,
    val from: String,
    val emailTemplateId: String? = null,
    val messageVariationId: String? = null,
    val bcc: String? = null,
    val body: String? = null
)

data class MessagingResponse(
    val dispatchId: String,
    val message: String
)
