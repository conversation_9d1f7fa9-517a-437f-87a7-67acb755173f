package br.com.alice.communication.crm.hubspot.b2b

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.logging.logger
import br.com.alice.communication.ServiceConfig
import br.com.alice.communication.crm.hubspot.Metrics
import br.com.alice.communication.crm.hubspot.b2c.client.FilterOperator
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotClient
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotCompany
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotContact
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotContactConflictException
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotDeal
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotException
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotFilter
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotSearchRequest
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotSort
import br.com.alice.communication.crm.hubspot.b2c.client.HubspotVisitor
import br.com.alice.communication.crm.hubspot.b2c.client.SortDirection
import br.com.alice.communication.crm.sales.b2b.AssociationFromDealToContact
import br.com.alice.communication.crm.sales.b2b.AssociationResultV2
import br.com.alice.communication.crm.sales.b2b.CompanyResultV2
import br.com.alice.communication.crm.sales.b2b.CompanyV2
import br.com.alice.communication.crm.sales.b2b.ContactInformation
import br.com.alice.communication.crm.sales.b2b.ContactInformationProperties
import br.com.alice.communication.crm.sales.b2b.ContactResultV2
import br.com.alice.communication.crm.sales.b2b.ContactV2
import br.com.alice.communication.crm.sales.b2b.DealPipelineV2
import br.com.alice.communication.crm.sales.b2b.DealResultV2
import br.com.alice.communication.crm.sales.b2b.DealStageV2
import br.com.alice.communication.crm.sales.b2b.DealStageV2.BACKGROUND_CHECK
import br.com.alice.communication.crm.sales.b2b.DealStageV2.COMPANY_SALES
import br.com.alice.communication.crm.sales.b2b.DealStageV2.CONTRACT_RECEIVED
import br.com.alice.communication.crm.sales.b2b.DealStageV2.FIRST_LOGIN
import br.com.alice.communication.crm.sales.b2b.DealStageV2.HEALTH_DECLARATION_APPOINTMENT
import br.com.alice.communication.crm.sales.b2b.DealStageV2.HEALTH_DECLARATION_APPOINTMENT_NO_SHOW
import br.com.alice.communication.crm.sales.b2b.DealStageV2.INVOICE_SENT
import br.com.alice.communication.crm.sales.b2b.DealStageV2.LEAD
import br.com.alice.communication.crm.sales.b2b.DealStageV2.LOST_LEAD
import br.com.alice.communication.crm.sales.b2b.DealStageV2.LOST_LEAD_B2C
import br.com.alice.communication.crm.sales.b2b.DealStageV2.MEMBER_ACTIVATED
import br.com.alice.communication.crm.sales.b2b.DealStageV2.MEMBER_CANCELED
import br.com.alice.communication.crm.sales.b2b.DealStageV2.MICRO_COMPANY_SALES
import br.com.alice.communication.crm.sales.b2b.DealStageV2.QUALIFIED_TO_BUY
import br.com.alice.communication.crm.sales.b2b.DealStageV2.SCHEDULED_HEALTH_DECLARATION_APPOINTMENT
import br.com.alice.communication.crm.sales.b2b.DealStageV2.SHOPPING_FINISHED
import br.com.alice.communication.crm.sales.b2b.DealStageV2.WAITING_PORTABILITY
import br.com.alice.communication.crm.sales.b2b.DealStageV2.WAITING_PORTABILITY_INFO
import br.com.alice.communication.crm.sales.b2b.DealV2
import br.com.alice.communication.crm.sales.b2b.DealWithStage
import br.com.alice.communication.crm.sales.b2b.DealWithStageResultV2
import br.com.alice.communication.crm.sales.b2b.DealsResult
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipeline
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipelineException
import br.com.alice.communication.crm.sales.b2b.VisitorTokenV2
import br.com.alice.communication.crm.sales.b2b.VisitorV2
import br.com.alice.communication.crm.hubspot.b2b.Metrics as B2BMetrics

class HubspotSalesFunnelPipelineImpl(
    private val hubspotClient: HubspotClient,
): HubspotSalesFunnelPipeline {
    companion object {
        private val pipelineIdDefault = ServiceConfig.Hubspot.pipelineIdDefault()
        private val pipelineIdPME = ServiceConfig.Hubspot.pipelineIdPME()
        val pipelineIdBrokerInternal = ServiceConfig.Hubspot.pipelineIdBrokerInternal()

        val backgroundCheckStageId = ServiceConfig.Hubspot.backgroundCheckStageId()
        val lostLeadStageId = ServiceConfig.Hubspot.lostLeadStageId()
        private val leadStageId = ServiceConfig.Hubspot.leadStageId()

        private val stageMapping = mapOf(
            COMPANY_SALES to "72468306",
            MICRO_COMPANY_SALES to "61835676",
            QUALIFIED_TO_BUY to "qualifiedtobuy",
            FIRST_LOGIN to "presentationscheduled",
            WAITING_PORTABILITY_INFO to "8072794",
            WAITING_PORTABILITY to "7611120",
            SHOPPING_FINISHED to "decisionmakerboughtin",
            HEALTH_DECLARATION_APPOINTMENT to "contractsent",
            SCHEDULED_HEALTH_DECLARATION_APPOINTMENT to "closedwon",
            HEALTH_DECLARATION_APPOINTMENT_NO_SHOW to "7578127",
            CONTRACT_RECEIVED to "closedlost",
            INVOICE_SENT to "3809726",
            MEMBER_ACTIVATED to "3809727",
            MEMBER_CANCELED to "25178888",
            LOST_LEAD_B2C to "3809728",
            BACKGROUND_CHECK to backgroundCheckStageId,
            LEAD to leadStageId,
            LOST_LEAD to lostLeadStageId,
        )

        private val pipelineMapping = mapOf(
            DealPipelineV2.PME to pipelineIdDefault,
            DealPipelineV2.DEFAULT to pipelineIdPME,
            DealPipelineV2.BROKER_INTERNAL to pipelineIdBrokerInternal,
        )
    }

    override suspend fun createNewContact(contact: ContactV2) = try {
        val existentContact = findContactByEmail(contact.email)

        logger.info(
            "Searched contact by email",
            "email" to contact.email,
            "existent_contact" to existentContact,
        )

        if (existentContact != null) {
            logger.info(
                "Contact already exists, updating",
                "contact_id" to existentContact.id,
                "contact" to contact,
            )
            updateContact(existentContact.id, contact)
        } else {
            logger.info(
                "Creating new contact",
                "contact" to contact,
            )
            val request = HubspotContact(
                email = contact.email,
                phone = contact.phone,
                nickname = contact.nickname,
                formattedFirstName = contact.formattedFirstName,
                leadId = contact.leadId.toString(),
                utmSource = contact.utmSource,
                utmMedium = contact.utmMedium,
                utmCampaignName = contact.utmCampaignName,
                utmTerm = contact.utmTerm,
                utmContent = contact.utmContent,
                promoCode = contact.promoCode,
                hsGoogleClickId = contact.hsGoogleClickId,
                utmBlog = contact.utmBlog,
                utmCampaign = contact.utmCampaign,
                utmCampaignId = contact.utmCampaignId,
                utmAdSet = contact.utmAdSet,
                utmAdSetId = contact.utmAdSetId,
                utmAdId = contact.utmAdId,
                dateOfBirthContact = contact.dateOfBirth?.toSaoPauloTimeZone()?.atBeginningOfTheDay(),
                nationalId = contact.nationalId,
            )

            val contactResponse = hubspotClient.createContact(request)
            logger.info("sent contact to Hubspot", "contact_id" to contactResponse.id)

            B2BMetrics.contactCreated()
            ContactResultV2(contactResponse.id)
        }
    } catch (e: HubspotContactConflictException) {
        logger.info("Hubspot contact conflict, returning already created", "contact_id" to e.contactId)
        throw e
    } catch (e: HubspotException) {
        logger.warn("error while trying to send contact to Hubspot", "contact" to contact, e)
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun updateContact(contactId: String, contact: ContactV2) = try {
        val request = HubspotContact(
            email = contact.email,
            phone = contact.phone,
            nickname = contact.nickname,
            formattedFirstName = contact.formattedFirstName,
            leadId = contact.leadId.toString(),
            utmSource = contact.utmSource,
            utmMedium = contact.utmMedium,
            utmCampaignName = contact.utmCampaignName,
            utmTerm = contact.utmTerm,
            utmContent = contact.utmContent,
            promoCode = contact.promoCode,
            hsGoogleClickId = contact.hsGoogleClickId,
            utmBlog = contact.utmBlog,
            utmCampaign = contact.utmCampaign,
            utmCampaignId = contact.utmCampaignId,
            utmAdSet = contact.utmAdSet,
            utmAdSetId = contact.utmAdSetId,
            utmAdId = contact.utmAdId,
            dateOfBirthContact = contact.dateOfBirth?.toSaoPauloTimeZone()?.atBeginningOfTheDay(),
            nationalId = contact.nationalId,
        )

        val contactResponse = hubspotClient.updateContact(contactId, request)
        logger.info("updating contact on Hubspot", "contact_id" to contactResponse.id)

        ContactResultV2(contactResponse.id)
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to update contact on Hubspot",
            "contact_id" to contactId,
            e
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun createDeal(deal: DealV2) = try {
        val hubspotDeal = HubspotDeal(
            dealEmail = deal.email,
            dealname = deal.name,
            dealstage = getHubspotStage(deal.stage),
            pipeline = getHubspotPipeline(deal.pipeline),
            declaredAge = deal.declaredAge,
            utmSource = deal.utmSource,
            utmMedium = deal.utmMedium,
            utmCampaignName = deal.utmCampaignName,
            utmCampaign = deal.utmCampaign,
            utmCampaignId = deal.utmCampaignId,
            utmAdSet = deal.utmAdSet,
            utmAdSetId = deal.utmAdSetId,
            utmAdId = deal.utmAdId,
            utmTerm = deal.utmTerm,
            utmContent = deal.utmContent,
            promoCodeType = deal.promoCodeType,
            promoCode = deal.promoCode,
            simulationLink = deal.simulationLink,
            latestSimulation = deal.latestSimulation?.toLocalDate()?.atBeginningOfTheDay(),
            b2bSegmentoEmpresa = deal.companySegment,
            leadId = deal.leadId.toString(),
            simulationId = deal.simulationId.toString(),
            utmBlog = deal.utmBlog,
            findOutAlice = deal.findOutAlice,
            smallbizIdadeSimulador1 = deal.smallbizIdadeSimulador1,
            smallbizIdadeSimulador2 = deal.smallbizIdadeSimulador2,
            smallbizIdadeSimulador3 = deal.smallbizIdadeSimulador3,
            smallbizIdadeSimulador4 = deal.smallbizIdadeSimulador4,
            smallbizIdadeSimulador5 = deal.smallbizIdadeSimulador5,
            cnpjDaEmpresa = deal.companyCnpj,
            b2bCompanyIdAos = deal.companyId,
            dealPhone = deal.phoneNumber,
            b2bCidadeEmpresa = deal.companyCity,
            numeroDeVidas = deal.employeesQty,
            b2b__tipo_de_lead = deal.b2b__tipo_de_lead,
            eliteAlice = deal.eliteAlice,
        )

        val dealResponse = hubspotClient.createDeal(hubspotDeal)
        logger.info(
            "sent deal to Hubspot",
            "deal_id" to dealResponse.id,
            "deal" to hubspotDeal,
            "pipeline" to hubspotDeal.pipeline,
            "stage" to hubspotDeal.dealstage,
        )

        Metrics.dealCreated(hubspotDeal)

        DealResultV2(dealResponse.id)
    } catch (e: HubspotException) {
        logger.warn("error while trying to create a new deal on Hubspot", "deal" to deal, e)
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun updateDeal(dealId: String, deal: DealV2) = try {
        val hubspotDeal = HubspotDeal(
            dealEmail = deal.email,
            dealname = deal.name,
            dealstage = getHubspotStage(deal.stage),
            declaredAge = deal.declaredAge,
            promoCodeType = deal.promoCodeType,
            promoCode = deal.promoCode,
            simulationLink = deal.simulationLink,
            latestSimulation = deal.latestSimulation?.toLocalDate()?.atBeginningOfTheDay(),
            pipeline = getHubspotPipeline(deal.pipeline),
            numeroDeVidas = deal.employeesQty,
            b2bCidadeEmpresa = deal.companyCity,
            cnpjDaEmpresa = deal.companyCnpj,
            b2bSegmentoEmpresa = deal.companySegment,
            leadId = deal.leadId,
            simulationId = deal.simulationId,
            utmBlog = deal.utmBlog,
            findOutAlice = deal.findOutAlice,
            smallbizIdadeSimulador1 = deal.smallbizIdadeSimulador1,
            smallbizIdadeSimulador2 = deal.smallbizIdadeSimulador2,
            smallbizIdadeSimulador3 = deal.smallbizIdadeSimulador3,
            smallbizIdadeSimulador4 = deal.smallbizIdadeSimulador4,
            smallbizIdadeSimulador5 = deal.smallbizIdadeSimulador5,
            utmCampaign = deal.utmCampaign,
            utmCampaignId = deal.utmCampaignId,
            utmAdSet = deal.utmAdSet,
            utmAdSetId = deal.utmAdSetId,
            utmAdId = deal.utmAdId,
            utmTerm = deal.utmTerm,
            utmContent = deal.utmContent,
            utmSource = deal.utmSource,
            utmMedium = deal.utmMedium,
            utmCampaignName = deal.utmCampaignName,
            dealPhone = deal.phoneNumber,
            b2bCompanyIdAos = deal.companyId,
            eliteAlice = deal.eliteAlice,
        )

        val dealResponse = hubspotClient.updateDeal(dealId, hubspotDeal)

        logger.info(
            "hubspot deal updated",
            "deal_id" to dealResponse.id,
            "deal" to deal,
            "pipeline" to deal.pipeline,
            "stage" to deal.stage,
        )

        Metrics.dealUpdated(hubspotDeal)

        DealResultV2(dealResponse.id)
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to update deal on Hubspot",
            "deal_id" to dealId
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error(
            "unexpected exception while trying to update a DealV2 on Hubspot",
            "deal_id" to dealId,
            "deal" to deal,
            unexpectedException
        )
        throw unexpectedException
    }

    override suspend fun associateContactToDeal(contactId: String, dealId: String) = try {
        val response = hubspotClient.associateContactToDeal(contactId, dealId)

        logger.info(
            "hubspot contact associated to deal",
            "deal_id" to dealId,
            "contact_id" to contactId,
            "response" to response
        )

        AssociationResultV2(success = !response.hasErrors)
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to associate a contact to a deal on hubspot",
            "deal_id" to dealId
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error(
            "unexpected exception while trying to associate a contact to a deal",
            "deal_id" to dealId,
            "contact_id" to contactId,
            unexpectedException
        )
        throw unexpectedException
    }

    override suspend fun identifyVisitor(visitor: VisitorV2) = try {
        val hubspotVisitor = HubspotVisitor(visitor.email, visitor.firstName, visitor.lastName)
        val response = hubspotClient.identifyVisitor(hubspotVisitor)

        logger.info("authenticating visitor", "visitor" to visitor, "response" to response)

        VisitorTokenV2(token = response.token)
    } catch (e: HubspotException) {
        logger.warn("error while identifying a visitor", "visitor" to visitor)
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error(
            "unexpected exception while trying to identify a visitor",
            "visitor" to visitor,
            unexpectedException
        )
        throw unexpectedException
    }

    private suspend fun findContactByEmail(email: String) = try {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(HubspotFilter(propertyName = "email", value = email, operator = FilterOperator.EQ))
        )

        logger.info("Searching contact by email...", "email" to email)

        val response = hubspotClient.searchContact(searchRequest)

        logger.info("Search finished...", "email" to email, "result" to response)

        val contact = response.results.firstOrNull()

        contact?.let { ContactResultV2(it.id) }

    } catch (e: HubspotException) {
        logger.warn("error searching contact by email", "email" to email)
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error("unexpected exception while trying to search a contact", unexpectedException)
        throw unexpectedException
    }

    override suspend fun findDealByCnpj(cnpj: String) = try {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(HubspotFilter(propertyName = "cnpj_da_empresa", value = cnpj, operator = FilterOperator.EQ))
        )

        logger.info("Searching deal by cnpj_da_empresa...", "cnpj" to cnpj)

        val response = hubspotClient.searchDeal(searchRequest)

        logger.info("Search finished...", "cnpj" to cnpj, "result" to response)

        val deal = response.results.firstOrNull()

        deal?.let { DealWithStageResultV2(it.id, it.properties.dealstage) }
    } catch (e: HubspotException) {
        logger.warn("Error searching deal by cnpj_da_empresa", "cnpj" to cnpj)
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error("Unexpected exception while trying to search a contact", unexpectedException)
        throw unexpectedException
    }

    override suspend fun findAllDealsByCnpj(cnpj: String): DealsResult? = try {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(HubspotFilter(propertyName = "cnpj_da_empresa", value = cnpj, operator = FilterOperator.EQ))
        )

        logger.info("Searching all deal with cnpj_da_empresa...", "cnpj" to cnpj)

        val response = hubspotClient.searchDeal(searchRequest)

        logger.info("Search finished...", "cnpj" to cnpj, "result" to response)

        DealsResult(
            deals = response.results.map {
                DealWithStage(it.id, it.properties.dealstage)
            }
        )
    } catch (e: HubspotException) {
        logger.warn("Error searching deal by cnpj_da_empresa", "cnpj" to cnpj)
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error("Unexpected exception while trying to search a contact", unexpectedException)
        throw unexpectedException
    }

    override suspend fun associateContactToCompany(contactId: String, companyId: String) = try {
        val response = hubspotClient.associateContactToCompany(contactId, companyId)

        logger.info(
            "hubspot contact associated to company",
            "company_id" to companyId,
            "contact_id" to contactId,
            "response" to response
        )

        AssociationResultV2(success = !response.hasErrors)
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to associate a contact to a company on hubspot",
            "company_id" to companyId
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    } catch (unexpectedException: Throwable) {
        logger.error(
            "unexpected exception while trying to associate a contact to a company",
            "company_id" to companyId,
            "contact_id" to contactId,
            unexpectedException
        )
        throw unexpectedException
    }

    override suspend fun createCompany(company: CompanyV2): CompanyResultV2 = try {
        val hubspotCompany = HubspotCompany(
            name = company.name,
            cnpj = company.cnpj,
            numeroDeFuncionariosDaEmpresa = company.employeesNumber,
            city = company.city,
            leadId = company.leadId.toString()
        )

        val companyResponse = hubspotClient.createCompany(hubspotCompany)
        logger.info(
            "sent company to Hubspot",
            "company_id" to companyResponse.id
        )

        CompanyResultV2(companyResponse.id)
    } catch (e: HubspotException) {
        logger.warn("error while trying to create a new company on Hubspot", "company" to company, e)
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun updateCompany(companyId: String, company: CompanyV2): CompanyResultV2 = try {
        val hubspotCompany = HubspotCompany(
            name = company.name,
            cnpj = company.cnpj,
            numeroDeFuncionariosDaEmpresa = company.employeesNumber,
            city = company.city,
            leadId = company.leadId.toString()
        )

        val companyResponse = hubspotClient.updateCompany(companyId, hubspotCompany)
        logger.info(
            "company updated in Hubspot",
            "company_id" to companyResponse.id
        )

        CompanyResultV2(companyResponse.id)
    } catch (e: HubspotException) {
        logger.warn("error while trying to update company on Hubspot", "company" to company, e)
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun findCompanyByCnpj(cnpj: String): CompanyResultV2? = try {
        val searchRequest = HubspotSearchRequest(
            filters = listOf(HubspotFilter(propertyName = "cnpj", value = cnpj, operator = FilterOperator.EQ)),
            sorts = listOf(HubspotSort(propertyName = "createdate", direction = SortDirection.DESCENDING))
        )

        logger.info("Searching company by cnpj on Hubspot", "cnpj" to cnpj)

        val response = hubspotClient.searchCompany(searchRequest)

        logger.info("Search finished on Hubspot", "cnpj" to cnpj, "result" to response)

        val company = response.results.firstOrNull()

        company?.let { CompanyResultV2(it.id) }
    } catch (
        e: HubspotException
    ) {
        logger.warn("error while trying to search company by cnpj on Hubspot", "cnpj" to cnpj)
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun getAssociationFromDealToContact(dealId: String): AssociationFromDealToContact = try {
        val association = hubspotClient.getAssociationFromDealToContact(dealId)
        logger.info(
            "got the association between deal and contact",
            "deal_id" to dealId,
        )

        AssociationFromDealToContact(association.results.map { it.id })
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to get the association between deal and contact",
            "deal_id" to dealId,
            e,
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun getContactWithAllInformation(contactId: String): ContactInformationProperties = try {
        val contact = hubspotClient.searchContactWithProperties(
            HubspotSearchRequest(
                filters = listOf(
                    HubspotFilter(
                        propertyName = "hs_object_id",
                        value = contactId,
                        operator = FilterOperator.EQ,
                    )
                )
            )
        )

        logger.info(
            "got the contact",
            "contact_id" to contactId,
        )

        ContactInformationProperties(
            contacts = contact.results.map {
                ContactInformation(
                    email = it.properties.email,
                    hs_object_id = it.id,
                )
            }
        )
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to get the contact",
            "contact_id" to contactId,
            e,
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun getContactWithAllInformationByEmail(
        email: String,
    ): ContactInformation = try {
        val contact = hubspotClient.searchContactWithProperties(
            HubspotSearchRequest(
                filters = listOf(
                    HubspotFilter(
                        propertyName = "email",
                        value = email,
                        operator = FilterOperator.EQ,
                    )
                )
            )
        )

        logger.info(
            "got the contact",
            "email" to email,
        )
        val result = contact.results.firstOrNull()
        ContactInformation(
            hs_object_id = result?.id,
            email = result?.properties?.email,
        )
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to get the contact",
            "email" to email,
            e,
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun getContactInformationByDealId(dealId: String): ContactInformationProperties = try {
        val contact = hubspotClient.searchContactWithProperties(
            HubspotSearchRequest(
                filters = listOf(
                    HubspotFilter(
                        propertyName = "associations.deal",
                        value = dealId,
                        operator = FilterOperator.EQ,
                    )
                )
            )
        )

        logger.info(
            "got the contact by deal id",
            "deal_id" to dealId,
        )

        ContactInformationProperties(
            contacts = contact.results.map {
                ContactInformation(
                    email = it.properties.email,
                    hs_object_id = it.id,
                )
            }
        )
    } catch (e: HubspotException) {
        logger.warn(
            "error while trying to get the contact",
            "deal_id" to dealId,
            e,
        )
        throw HubspotSalesFunnelPipelineException(e.message)
    }

    override suspend fun searchContactOnHubspot(email: String): ContactResultV2? = this.findContactByEmail(email)

    private fun getHubspotStage(stage: DealStageV2?) = stageMapping[stage]

    private fun getHubspotPipeline(pipeline: DealPipelineV2?) = pipelineMapping[pipeline]
}
