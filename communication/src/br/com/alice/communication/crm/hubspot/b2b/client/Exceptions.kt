package br.com.alice.communication.crm.hubspot.b2b.client

open class HubspotException(
    override val message: String,
    cause: Throwable? = null
) : Exception(message, cause)

class CredentialsNotFoundException : HubspotException("Authentication credentials not found")

class HubspotInvalidEmailException(
    message: String,
    val code: String = "hubspot_invalid_email",
    cause: Throwable? = null
) : HubspotException(message, cause)

class HubspotContactConflictException(
    message: String,
    val code: String = "hubspot_contact_conflict",
    val contactId: String,
    cause: Throwable? = null
) : HubspotException(message, cause)
