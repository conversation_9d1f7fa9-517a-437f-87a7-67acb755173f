plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.appointment-domain-client"
version = aliceAppointmentDomainClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:provider-domain-service-data-package"))
	implementation(project(":data-packages:health-condition-domain-service-data-package"))
	implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:file-vault-service-data-package"))
    implementation(project(":staff-domain-client"))
    implementation(project(":health-logic-domain-client"))

    kapt(project(":common"))
    ktor2Dependencies()
    

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
