package br.com.alice.appointment.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.AppointmentEvent
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import com.github.kittinunf.result.Result

@RemoteService
interface AppointmentEventService : Service {
    override val namespace get() = "appointment"
    override val serviceName get() = "appointment_event"

    suspend fun create(appointmentEvent: AppointmentEvent): Result<AppointmentEvent, Throwable>

    suspend fun update(appointmentEvent: AppointmentEvent): Result<AppointmentEvent, Throwable>

    suspend fun getByReferenceId(referenceId: String): Result<AppointmentEvent, Throwable>

    suspend fun getSuggestionByPersonId(personId: PersonId) : Result<List<AppointmentEvent>, Throwable>

    suspend fun getPaginatedByTypeAndPersonId(
        referenceModel: AppointmentEventReferenceModel,
        personId: PersonId,
        range: IntRange
    ): Result<List<AppointmentEvent>, Throwable>

    suspend fun upsert(appointmentEvent: AppointmentEvent): Result<AppointmentEvent, Throwable>

    suspend fun deleteByReferenceId(referenceModelId: String): Result<Boolean, Throwable>
}
