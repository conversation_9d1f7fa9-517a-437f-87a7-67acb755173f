package br.com.alice.appointment.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.AppointmentComponentType
import br.com.alice.data.layer.models.AppointmentMacro
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentMacroService: Service {
    override val namespace get() = "appointment"
    override val serviceName get() = "appointment_macro"

    suspend fun get(id: UUID): Result<AppointmentMacro, Throwable>

    suspend fun getByRange(range: IntRange): Result<List<AppointmentMacro>, Throwable>

    suspend fun create(appointmentMacro: AppointmentMacro): Result<AppointmentMacro, Throwable>

    suspend fun update(appointmentMacro: AppointmentMacro): Result<AppointmentMacro, Throwable>

    suspend fun delete(id: UUID): Result<AppointmentMacro, Throwable>

    suspend fun getByComponentTypesAndTitle(types: List<AppointmentComponentType>?, title: String?, range: IntRange, active: Boolean = true): Result<List<AppointmentMacro>, Throwable>

    suspend fun countByComponentTypesAndTitle(types: List<AppointmentComponentType>?, title: String?, active: Boolean = true): Result<Int, Throwable>
}
