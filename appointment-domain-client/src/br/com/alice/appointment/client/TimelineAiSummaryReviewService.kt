package br.com.alice.appointment.client

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.TimelineAiSummaryReview
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TimelineAiSummaryReviewService : Service {

    override val namespace: String get() = "appointment"
    override val serviceName: String get() = "timeline-ai-summary-review"

    suspend fun get(id: UUID): Result<TimelineAiSummaryReview, Throwable>
    suspend fun add(model: TimelineAiSummaryReview): Result<TimelineAiSummaryReview, Throwable>
    suspend fun update(model: TimelineAiSummaryReview): Result<TimelineAiSummaryReview, Throwable>
    suspend fun upsert(model: TimelineAiSummaryReview): Result<TimelineAiSummaryReview, Throwable>

    suspend fun findBy(filters: TimelineAiSummaryReviewFilter): Result<List<TimelineAiSummaryReview>, Throwable>
    suspend fun findOneBy(filters: TimelineAiSummaryReviewFilter): Result<TimelineAiSummaryReview, Throwable>
}

data class TimelineAiSummaryReviewFilter(
    val timelineIds: List<UUID>? = null,
    val staffIds: List<UUID>? = null
) {
    fun isValid() {
        if (timelineIds.isNotNullOrEmpty()) return
        if (staffIds.isNotNullOrEmpty()) return

        throw InvalidArgumentException("Invalid filter")
    }
}
