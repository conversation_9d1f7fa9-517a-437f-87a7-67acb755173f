package br.com.alice.appointment.models

import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import java.util.UUID

data class StaffTimelineResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val description: String,
    val profileImageUrl: String,
) {
    val fullName: String = "$firstName $lastName"

    companion object {
        fun from(staff: Staff) =
            StaffTimelineResponse(
                id = staff.id,
                firstName = staff.firstName,
                lastName = staff.lastName,
                profileImageUrl = staff.profileImageUrl.orEmpty(),
                description = StaffGenderDescriptionConverter.convert(staff)
            )
    }
}

data class ProviderTimelineResponse(
    val id: UUID,
    val name: String?
) {
    companion object {
        fun from(providerUnit: ProviderUnit) = ProviderTimelineResponse(
            id = providerUnit.id,
            name = providerUnit.name
        )
    }
}

data class AttendantsTimeline(
    val staffs: Map<UUID, StaffTimelineResponse> = emptyMap(),
    val providers: Map<UUID, ProviderTimelineResponse> = emptyMap(),
)
