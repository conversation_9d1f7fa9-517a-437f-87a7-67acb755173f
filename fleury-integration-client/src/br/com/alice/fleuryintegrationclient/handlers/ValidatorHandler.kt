package br.com.alice.fleuryintegrationclient.handlers

import br.com.alice.data.layer.models.FleuryLaudo
import br.com.alice.data.layer.models.ReferenceRange

class ValidatorHandler(
    private val nextHandler: ReferenceRangeHandler? = null
) : ReferenceRangeHandler {

    override fun parseResult(laudo: FleuryLaudo): ReferenceRange? {
        val result = laudo.resultado
        val referenceList = laudo.listaFaixaReferenciaNormal
        if (result.isBlank() || referenceList.isNullOrEmpty()) return ReferenceRange.NOT_APPLIED

        val referenceValue = referenceList.first()
        val referenceDescription = referenceValue.descricao
        return if (referenceValue.limiteInferior.isNullOrBlank() &&
            referenceValue.limiteSuperior.isNullOrBlank() &&
            referenceValue.descricaoFaixaEtaria.isNullOrBlank() &&
            referenceDescription.isNullOrBlank()
        ) ReferenceRange.NOT_APPLIED
        else nextHandler?.parseResult(laudo)
    }
}
