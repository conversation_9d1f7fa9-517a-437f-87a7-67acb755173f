package br.com.alice.fleuryintegrationclient.converters

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toRangeSafeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AliceResultItem
import br.com.alice.data.layer.models.AliceTestResult
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.AliceTestResultType
import br.com.alice.data.layer.models.FleuryResultType
import br.com.alice.data.layer.models.ProviderIntegration.FLEURY
import br.com.alice.data.layer.models.ReferenceRange
import br.com.alice.fleuryintegrationclient.extensions.referenceRange
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class AliceTestResultBundleConverterTest {
    private val idFicha = "idFicha"
    private val idItem = "idItem"
    private val idProdutoImagen = "1012"
    private val idProdutoText = "1013"
    private val idUnidade = "1310"
    private val personId = PersonId()

    val fleuryLaudo = TestModelFactory.buildFleuryLaudo()
    private val fleuryTestResult = TestModelFactory.buildFleuryTestResult(
        personId = personId,
        idFicha = idFicha,
        idItem = idItem,
        idUnidade = idUnidade,
        idProduto = idProdutoText,
        laudos = listOf(fleuryLaudo)
    )

    private val fleuryTestResultImage = TestModelFactory.buildFleuryTestResult(
        personId = personId,
        idFicha = idFicha,
        idItem = idItem,
        idUnidade = idUnidade,
        idProduto = idProdutoImagen,
        laudoFormatado = "laudoFormatado",
        type = FleuryResultType.IMAGE
    )

    @Test
    fun `#build - builds AliceTestResultBundle from list of FleuryTestResult`() {
        val expectedResult = AliceTestResultBundle(
            id = "${fleuryTestResult.id}, ${fleuryTestResultImage.id}".toRangeSafeUUID(),
            personId = personId,
            externalId = idFicha,
            integrationSource = FLEURY,
            results = listOf(
                AliceTestResult(
                    id = fleuryTestResult.id,
                    name = fleuryTestResult.procedimento,
                    collectedAt = fleuryTestResult.dataColeta,
                    releasedAt = fleuryTestResult.createdAt,
                    externalReference = idItem,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem,
                        "id_produto" to idProdutoText,
                        "id_unidade" to idUnidade,
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryLaudo.nomeReduzido,
                            description = fleuryLaudo.nome,
                            result = fleuryLaudo.resultado,
                            unit = fleuryLaudo.unidadeMedida,
                            reference = listOf(fleuryLaudo.friendlyDescription()),
                            referenceRange = fleuryLaudo.referenceRange()
                        )
                    )
                ), AliceTestResult(
                    id = fleuryTestResultImage.id,
                    name = fleuryTestResultImage.procedimento,
                    collectedAt = fleuryTestResultImage.dataColeta,
                    releasedAt = fleuryTestResultImage.createdAt,
                    externalReference = idItem,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem,
                        "id_produto" to idProdutoImagen,
                        "id_unidade" to idUnidade,
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryTestResultImage.procedimento,
                            result = fleuryTestResultImage.laudoFormatado!!,
                            reference = emptyList(),
                            referenceRange = ReferenceRange.NOT_APPLIED,
                            type = AliceTestResultType.IMAGE
                        )
                    )
                )
            )
        )

        val result =
            AliceTestResultBundleConverter.convert(
                listOf(fleuryTestResult, fleuryTestResultImage),
                emptyMap()
            )
        assertThat(result.results).isEqualTo(expectedResult.results)
        assertThat(result.externalId).isEqualTo(expectedResult.externalId)
        assertThat(result.integrationSource).isEqualTo(expectedResult.integrationSource)
        assertThat(result.realizedAtUnit).isEqualTo(expectedResult.realizedAtUnit)
        assertThat(result.personId).isEqualTo(expectedResult.personId)
    }

    @Test
    fun `#build - builds AliceTestResultBundle from list of FleuryTestResult with alice code`() {
        val testCodeImage = TestModelFactory.buildTestCode(code = "98040001", description = "Procedimento IMAGEM")
        val testCodeText = TestModelFactory.buildTestCode(code = "98040002", description = "Procedimento TEXTO")
        val codes = mapOf(
            idProdutoImagen to testCodeImage,
            idProdutoText to testCodeText
        )
        val expectedResult = AliceTestResultBundle(
            id = "${fleuryTestResult.id}, ${fleuryTestResultImage.id}".toRangeSafeUUID(),
            personId = personId,
            externalId = idFicha,
            integrationSource = FLEURY,
            results = listOf(
                AliceTestResult(
                    id = fleuryTestResult.id,
                    name = testCodeText.description,
                    collectedAt = fleuryTestResult.dataColeta,
                    releasedAt = fleuryTestResult.createdAt,
                    externalReference = idItem,
                    procedureId = testCodeText.code,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem,
                        "id_produto" to idProdutoText,
                        "id_unidade" to idUnidade,
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryLaudo.nomeReduzido,
                            description = fleuryLaudo.nome,
                            result = fleuryLaudo.resultado,
                            unit = fleuryLaudo.unidadeMedida,
                            reference = listOf(fleuryLaudo.friendlyDescription()),
                            referenceRange = fleuryLaudo.referenceRange()
                        )
                    )
                ), AliceTestResult(
                    id = fleuryTestResultImage.id,
                    name = testCodeImage.description,
                    collectedAt = fleuryTestResultImage.dataColeta,
                    releasedAt = fleuryTestResultImage.createdAt,
                    externalReference = idItem,
                    procedureId = testCodeImage.code,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem,
                        "id_produto" to idProdutoImagen,
                        "id_unidade" to idUnidade,
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryTestResultImage.procedimento,
                            result = fleuryTestResultImage.laudoFormatado!!,
                            reference = emptyList(),
                            referenceRange = ReferenceRange.NOT_APPLIED,
                            type = AliceTestResultType.IMAGE
                        )
                    )
                )
            )
        )

        val result =
            AliceTestResultBundleConverter.convert(
                listOf(fleuryTestResult, fleuryTestResultImage),
                codes
            )
        assertThat(result.results).isEqualTo(expectedResult.results)
        assertThat(result.externalId).isEqualTo(expectedResult.externalId)
        assertThat(result.integrationSource).isEqualTo(expectedResult.integrationSource)
        assertThat(result.realizedAtUnit).isEqualTo(expectedResult.realizedAtUnit)
        assertThat(result.personId).isEqualTo(expectedResult.personId)
    }

    @Test
    fun `#build - builds AliceTestResultBundle from list of FleuryTestResult when id produto and id unidade is null`() {
        val expectedResult = AliceTestResultBundle(
            id = "${fleuryTestResult.id}, ${fleuryTestResultImage.id}".toRangeSafeUUID(),
            personId = personId,
            externalId = idFicha,
            integrationSource = FLEURY,
            results = listOf(
                AliceTestResult(
                    id = fleuryTestResult.id,
                    name = fleuryTestResult.procedimento,
                    collectedAt = fleuryTestResult.dataColeta,
                    releasedAt = fleuryTestResult.createdAt,
                    externalReference = idItem,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryLaudo.nomeReduzido,
                            description = fleuryLaudo.nome,
                            result = fleuryLaudo.resultado,
                            unit = fleuryLaudo.unidadeMedida,
                            reference = listOf(fleuryLaudo.friendlyDescription()),
                            referenceRange = fleuryLaudo.referenceRange()
                        )
                    )
                ), AliceTestResult(
                    id = fleuryTestResultImage.id,
                    name = fleuryTestResultImage.procedimento,
                    collectedAt = fleuryTestResultImage.dataColeta,
                    releasedAt = fleuryTestResultImage.createdAt,
                    externalReference = idItem,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryTestResultImage.procedimento,
                            result = fleuryTestResultImage.laudoFormatado!!,
                            reference = emptyList(),
                            referenceRange = ReferenceRange.NOT_APPLIED,
                            type = AliceTestResultType.IMAGE
                        )
                    )
                )
            )
        )

        val result =
            AliceTestResultBundleConverter.convert(
                listOf(
                    fleuryTestResult.copy(
                        idUnidade = null,
                        idProduto = null
                    ), fleuryTestResultImage.copy(
                        idUnidade = null,
                        idProduto = null
                    )
                ),
                emptyMap()
            )
        assertThat(result.results).isEqualTo(expectedResult.results)
        assertThat(result.externalId).isEqualTo(expectedResult.externalId)
        assertThat(result.integrationSource).isEqualTo(expectedResult.integrationSource)
        assertThat(result.realizedAtUnit).isEqualTo(expectedResult.realizedAtUnit)
        assertThat(result.personId).isEqualTo(expectedResult.personId)
    }

    @Test
    fun `#build - builds AliceTestResultBundle from a FleuryTestResult with providerItemId`() {
        val expectedResult = AliceTestResultBundle(
            id = fleuryTestResult.id,
            personId = personId,
            externalId = idFicha,
            integrationSource = FLEURY,
            results = listOf(
                AliceTestResult(
                    id = fleuryTestResult.id,
                    name = fleuryTestResult.procedimento,
                    collectedAt = fleuryTestResult.dataColeta,
                    releasedAt = fleuryTestResult.createdAt,
                    externalReference = idItem,
                    additionalIntegrationInformation = mapOf(
                        "ficha" to fleuryTestResultImage.idFicha,
                        "item" to idItem,
                        "id_produto" to idProdutoText,
                        "id_unidade" to idUnidade,
                    ),
                    items = listOf(
                        AliceResultItem(
                            name = fleuryLaudo.nomeReduzido,
                            description = fleuryLaudo.nome,
                            result = fleuryLaudo.resultado,
                            unit = fleuryLaudo.unidadeMedida,
                            reference = listOf(fleuryLaudo.friendlyDescription()),
                            referenceRange = fleuryLaudo.referenceRange(),
                            providerItemId = "123"
                        )
                    )
                )
            )
        )

        val result =
            AliceTestResultBundleConverter.convert(
                fleuryTestResult,
                emptyMap(),
                mapOf(fleuryTestResult.laudos.first().analitoId to "123")
            )
        assertThat(result.results).isEqualTo(expectedResult.results)
        assertThat(result.externalId).isEqualTo(expectedResult.externalId)
        assertThat(result.integrationSource).isEqualTo(expectedResult.integrationSource)
        assertThat(result.realizedAtUnit).isEqualTo(expectedResult.realizedAtUnit)
        assertThat(result.personId).isEqualTo(expectedResult.personId)
    }

}
