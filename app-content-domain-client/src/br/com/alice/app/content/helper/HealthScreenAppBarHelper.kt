package br.com.alice.app.content.helper

import br.com.alice.app.content.AppContentDomainConfiguration
import br.com.alice.app.content.helper.HealthScreenAppBarHelper.Filters.TYPE_FILTER_NAME
import br.com.alice.app.content.helper.HealthScreenAppBarHelper.Path.baseUrl
import br.com.alice.app.content.helper.HealthScreenAppBarHelper.Path.healthScreenStatusFilter
import br.com.alice.app.content.helper.HealthScreenAppBarHelper.Path.pathHealthScreenAllDemands
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.AppBarButtonFilter
import br.com.alice.app.content.model.AppBarFilters
import br.com.alice.app.content.model.RemoteActionItem
import br.com.alice.data.layer.models.ActionPlanTask

object HealthScreenAppBarHelper {

    object Path {
        val baseUrl: String = AppContentDomainConfiguration.AliceServices.memberApiUrl
        const val pathHealthScreenAllDemands = "app_content/screen/health_all_demands"
        const val healthScreenStatusFilter = "?status="
    }

    object Filters {
        const val TYPE_FILTER_NAME = "type"

        object Favorite {
            const val ID = "favoritos"
            const val ICON = "star_outlined"
            const val LABEL = "Favoritos"
            const val TYPE_FILTER_VALUE = "favorites"
        }

        object All {
            const val ID = "all"
            const val ICON = "bullets"
            const val LABEL = "Todos"
        }
    }

    object HealthScreen {
        const val TITLE = "Plano de Ação"
        const val BACK_BUTTON = ""
    }

    fun getAppBar(
        title: String = "Plano de Ação",
        backButton: String = "",
        withFilters: Boolean = false,
        statusFilter: String,
        activeTasks: List<ActionPlanTask>? = null,
        rightRemoteActionItems: List<RemoteActionItem>? = null
    ) = if (withFilters) {
        getAppBarWithFilters(activeTasks!!, title, backButton, statusFilter, rightRemoteActionItems)
    } else {
        getAppBarWithoutFilters(title, backButton, statusFilter, rightRemoteActionItems)
    }

    private fun getAppBarWithoutFilters(
        title: String,
        backButton: String,
        statusFilter: String,
        rightRemoteActionItems: List<RemoteActionItem>? = null
    ) = AppBar(
        title = title,
        back = backButton,
        accessoryFilter = AppBarFilters(
            reloadBaseUrl = "${getReloadUrl()}$statusFilter",
            buttonsFilter = listOf(getAllFilter())
        ),
        rightRemoteActionItems = rightRemoteActionItems
    )

    private fun getAppBarWithFilters(
        activeTasks: List<ActionPlanTask>,
        title: String,
        backButton: String,
        statusFilter: String,
        rightRemoteActionItems: List<RemoteActionItem>? = null
    ) = listOfNotNull(
            getAllFilter(activeTasks.size),
            getFavoriteFilter(activeTasks)
        ).plus(
            getCustomFilters(activeTasks)
        ).let { filters ->
            AppBar(
                title = title,
                back = backButton,
                accessoryFilter = AppBarFilters(
                    reloadBaseUrl = "${getReloadUrl()}$statusFilter",
                    buttonsFilter = filters
                ),
                rightRemoteActionItems = rightRemoteActionItems
            )
        }

    private fun getCustomFilters(
        tasks: List<ActionPlanTask>
    ): List<AppBarButtonFilter> =
        tasks
            .groupBy { it.type }
            .map { (taskType, tasks) ->
                AppBarButtonFilter(
                    id = taskType.toString(),
                    icon = taskType.filterIcon,
                    label = getFilterLabel(taskType.filterName, tasks.size),
                    paramName = TYPE_FILTER_NAME,
                    paramValue = taskType.toString()
                )
            }

    private fun getFavoriteFilter(
        activeTasks: List<ActionPlanTask>
    ) = activeTasks
        .filter { it.favorite }
        .takeIf { it.isNotEmpty() }
        ?.let { favoriteActiveTasks ->
            AppBarButtonFilter(
                id = Filters.Favorite.ID,
                icon = Filters.Favorite.ICON,
                label = getFilterLabel(Filters.Favorite.LABEL, favoriteActiveTasks.size),
                paramName = TYPE_FILTER_NAME,
                paramValue = Filters.Favorite.TYPE_FILTER_VALUE
            )
        }

    private fun getAllFilter(counter: Int = 0) =
        AppBarButtonFilter(
            id = Filters.All.ID,
            icon = Filters.All.ICON,
            label = getFilterLabel(Filters.All.LABEL, counter),
        )

    private fun getFilterLabel(title: String, quantity: Int) = "$title (${quantity})"

    private fun getReloadUrl() = "${baseUrl}${pathHealthScreenAllDemands}${healthScreenStatusFilter}"

}
