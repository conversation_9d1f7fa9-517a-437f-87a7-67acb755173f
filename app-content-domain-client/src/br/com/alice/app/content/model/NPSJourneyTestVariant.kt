package br.com.alice.app.content.model

enum class NPSJourneyTestVariant(val abTestDescription: String) {
    HOME("home"),
    NOTIFICATION("notification"),
    JOURNEY("journey");

    fun isNPSJourneyEligible() = this == JOURNEY || this == NOTIFICATION

    fun isNotificationEligible() = this == NOTIFICATION

    fun showHomeCard() = this == HOME

    companion object {
        fun valueOfAbTest(value: String) = values().firstOrNull { it.abTestDescription == value }
            ?: error("Invalid value $value")
    }
}
