package br.com.alice.app.content.model.section

import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.TabBarItem
import br.com.alice.app.content.model.TabBarVariant

data class TabBarSection(
    val headers: List<Section>? = null,
    val tabBarVariant: TabBarVariant? = null,
    val tabBarItems: List<TabBarItem>,
    val trackUserBehavior: Boolean = false,
) : SectionDataBase()
