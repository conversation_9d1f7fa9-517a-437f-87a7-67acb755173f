package br.com.alice.app.content.model.section

import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.PillOption
import br.com.alice.app.content.model.PillResponseSelectionAlgorithm
import br.com.alice.app.content.model.SectionOrientation

data class PillResponseSection(
    val options: List<PillOption>,
    val stack: SectionOrientation? = null,
    val selectionAlgorithm: PillResponseSelectionAlgorithm? = null,
    val confirmButton: Button,
    val noSelectionButton: Button? = null,
) : SectionDataBase()
