package br.com.alice.app.content.model

import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.data.layer.models.TestRequestPreparation
import java.util.UUID

data class ActionAdditionalProperties(
    val id: String? = null
)

enum class ActionType {
    CHEVRON,
    VALIDATE
}

enum class AppModule {
    CHESHIRE_SCREEN_MODULE
}

data class RemoteAction(
    val method: RemoteActionMethod? = null,
    val endpoint: String? = null,
    val mobileRoute: ActionRouting? = null,
    val params: Map<String, Any>? = null,
    val onTapAction: RemoteAction? = null,
    val transition: RemoteActionTransition? = null,
    val type: RemoteActionType? = null,
    val navigationGroup: String? = null,
    val removeUntilRouteName: String? = null,
    val popToRouteOnComplete: String? = null,
) {
    companion object
}

enum class RemoteActionMethod {
    GET,
    PUT,
    POST,
    DELETE
}

enum class RemoteActionTransition {
    REGULAR,
    REGULAR_FULL_SCREEN,
    MODAL,
    MODAL_WITH_HEADER,
    MODAL_WITH_FOOTER,
    MODAL_WITH_HEADER_AND_FOOTER,
    INPUT,
    INPUT_WIHOUT_CONFIRMATION_UNDISMISSABLE,
    INPUT_WIHOUT_CONFIRMATION,
    INPUT_WITHOUT_KEYBOARD,
    FADE,
    SLIDE,
    INVERSE_SLIDE,
    BOTTOM_SHEET,
    BOTTOM_SHEET_FIXED,
    INDICATOR,
}

enum class RemoteActionType {
    CLOSE,
    LOGOUT,
}

enum class ActionRouting {
    CHESHIRE_SCREEN,
    HEALTH_COMMUNITY,
    HEALTH_TEAM,
    HEALTH_DOCUMENTS,
    HEALTH_PLAN_DETAILS,
    EXTERNAL_APP,
    HELP_CENTER,
    ONBOARDING,
    DUQUESA_APPOINTMENT_INTRO,
    DUQUESA_SCHEDULE_ESTABLISHMENT_GROUPS,
    DUQUESA_SCHEDULE_AVAILABILITY,
    DUQUESA_SCHEDULE_DETAIL,
    DUQUESA_SCHEDULE_CONFIRMATION,
    ALICE_AGORA,
    REDESIGN_ALICE_AGORA,
    TEST_RESULT,
    TEST_RESULT_FEEDBACK,
    REFUND,
    PDF,
    CHANNEL,
    ACCREDITED_NETWORK,
    APPOINTMENT_SCHEDULE,
    PERSONAL_DATA,
    WEBVIEW,
    TEST_REQUEST_UPLOAD,
    HEALTH_DECLARATION,
    HEALTH_MEETINGS_LIST,
    BENEFITS,
    MEMBER_ONBOARDING_V2,
    MEMBER_ONBOARDING_V2_COVER,

    @Deprecated("Use ACCREDITED_NETWORK_PROFILE instead. See more at https://www.notion.so/alicehealth/04-2025-Unifica-o-telas-rede-credenciada-1d1f0f13146a802e9a6de80f48aab144?pvs=4#202f0f13146a80d489e5c3f964c052b2")
    SPECIALIST_DETAILS,
    ACCREDITED_NETWORK_PROFILE,
    DEMAND_CHANNELS_LIST,
    APPOINTMENT_DETAILS,
    SCORE_MAGENTA_INPUT,
    ARCHIVED_CHANNELS,
    QUESTIONNAIRE,
    TEST_PREPARATION,
    REVIEW_TERMS,
    B2B_ACTIVATION,
}

fun RemoteAction.Companion.aliceAgora() = RemoteAction(
    mobileRoute = ActionRouting.ALICE_AGORA,
)

fun RemoteAction.Companion.redesignAliceAgora() = RemoteAction(
    mobileRoute = ActionRouting.REDESIGN_ALICE_AGORA,
)

fun RemoteAction.Companion.personalData() = RemoteAction(
    mobileRoute = ActionRouting.PERSONAL_DATA,
)

fun RemoteAction.Companion.healthDocuments() = RemoteAction(
    mobileRoute = ActionRouting.HEALTH_DOCUMENTS,
)

fun RemoteAction.Companion.healthTeam() = RemoteAction(
    mobileRoute = ActionRouting.HEALTH_TEAM,
)

fun RemoteAction.Companion.logout() = RemoteAction(
    type = RemoteActionType.LOGOUT,
)

fun RemoteAction.Companion.appointmentHub() = getCheshireScreenNavigationAction(ScreenType.APPOINTMENT_HUB)

fun RemoteAction.Companion.accreditedNetworkMenu() =
    getCheshireScreenNavigationAction(ScreenType.ACCREDITED_NETWORK_MENU)

fun RemoteAction.Companion.schedule() =
    getCheshireScreenNavigationAction(ScreenType.SCHEDULE)

fun RemoteAction.Companion.healthAllDemands() =
    getCheshireScreenNavigationAction(ScreenType.HEALTH_ALL_DEMANDS)

fun RemoteAction.Companion.redesignHealthPlanHome() =
    getCheshireScreenNavigationAction(ScreenType.REDESIGN_HEALTH_PLAN_HOME)

fun RemoteAction.Companion.scoreMagentaInput() =
    RemoteAction(mobileRoute = ActionRouting.SCORE_MAGENTA_INPUT)

fun RemoteAction.Companion.newDemandChannel(demandId: UUID) = RemoteAction(
    method = RemoteActionMethod.PUT,
    endpoint = "/channels/v2/demands/$demandId"
)

fun RemoteAction.Companion.appointmentDetails(appointmentId: UUID, asModal: Boolean = true) = RemoteAction(
    mobileRoute = ActionRouting.APPOINTMENT_DETAILS,
    params = mapOf(
        "id" to appointmentId,
        "as_modal" to asModal,
    ),
    transition = if (asModal) RemoteActionTransition.BOTTOM_SHEET else RemoteActionTransition.REGULAR,
)

fun RemoteAction.Companion.demandChannels(demandId: UUID, title: String) = RemoteAction(
    mobileRoute = ActionRouting.DEMAND_CHANNELS_LIST,
    params = mapOf(
        "demand_id" to demandId,
        "title" to title
    )
)

fun RemoteAction.Companion.healthPlanItemDetail(taskIds: List<UUID>) = RemoteAction(
    mobileRoute = ActionRouting.HEALTH_PLAN_DETAILS,
    params = mapOf(
        "task_ids" to taskIds
    )
)

fun RemoteAction.Companion.healthcarePhysician() = RemoteAction(
    mobileRoute = ActionRouting.SPECIALIST_DETAILS,
    params = mapOf(
        "method" to RemoteActionMethod.GET,
        "endpoint" to "/accredited_network/healthcare_team/physician"
    )
)

fun RemoteAction.Companion.healthcarePhysicianV2() = RemoteAction(
    mobileRoute = ActionRouting.ACCREDITED_NETWORK_PROFILE,
    params = mapOf(
        "method" to RemoteActionMethod.GET,
        "endpoint" to "/v2/accredited_network/healthcare_team/physician"
    )
)

fun RemoteAction.Companion.labs() = RemoteAction(
    mobileRoute = ActionRouting.ACCREDITED_NETWORK,
    params = mapOf(
        "endpoint" to "/accredited_network/provider_units?type=LABORATORY",
        "params" to mapOf(
            "title" to "Laboratórios"
        )
    )
)

fun RemoteAction.Companion.saraCalendar(calendarUrl: String) = RemoteAction(
    mobileRoute = ActionRouting.WEBVIEW,
    params = mapOf(
        "link" to calendarUrl,
        "token" to "true",
        "pop_on_complete" to true,
        "feedback_message" to "Agendamento efetuado com sucesso!",
    ),
)

fun RemoteAction.Companion.specialists() = RemoteAction(
    mobileRoute = ActionRouting.ACCREDITED_NETWORK,
    params = mapOf(
        "method" to RemoteActionMethod.GET,
        "endpoint" to "/accredited_network/medical_specialties",
        "params" to mapOf(
            "title" to "Especialidades"
        )
    )
)

fun RemoteAction.Companion.healthMeetingsList() = RemoteAction(
    mobileRoute = ActionRouting.HEALTH_MEETINGS_LIST,
)

fun RemoteAction.Companion.redesignHealthPlanDemandDetail(demandId: UUID) =
    getCheshireScreenNavigationAction(
        type = ScreenType.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL,
        params = mapOf("demand_id" to demandId)
    )

fun RemoteAction.Companion.requestHealthPlanHomeDemandList(
    demandStatuses: List<DemandActionPlanStatus>? = null,
    taskStatuses: List<ActionPlanTaskStatus>? = null
) =
    getCheshireScreenAsyncRequestAction(
        type = ScreenType.REDESIGN_HEALTH_PLAN_HOME_DEMAND_LIST,
        params = mapOfNotNull(
            demandStatuses?.let { "demand_statuses" to it },
            taskStatuses?.let { "task_statuses" to it },
        )
    )

fun RemoteAction.Companion.redesignHealthPlanHistory() =
    getCheshireScreenNavigationAction(ScreenType.REDESIGN_HEALTH_PLAN_HISTORY)

fun RemoteAction.Companion.requestHealthPlanTaskListFilters(
    demandId: UUID? = null,
    taskStatuses: List<ActionPlanTaskStatus>? = null,
    demandStatuses: List<DemandActionPlanStatus>? = null
) =
    getCheshireScreenAsyncRequestAction(
        type = ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST_FILTERS,
        params = mapOfNotNull(
            demandId?.let { "demand_id" to it },
            taskStatuses?.let { "task_statuses" to it },
            demandStatuses?.let { "demand_statuses" to it }
        )
    )

fun RemoteAction.Companion.requestHealthPlanTaskList(
    taskType: ActionPlanTaskType? = null,
    demandId: UUID? = null,
    taskStatuses: List<ActionPlanTaskStatus>? = null,
    demandStatuses: List<DemandActionPlanStatus>? = null
) =
    getCheshireScreenAsyncRequestAction(
        ScreenType.REDESIGN_HEALTH_PLAN_TASK_LIST,
        params = mapOfNotNull(
            taskType?.let { "type" to it },
            demandId?.let { "demand_id" to it },
            taskStatuses?.let { "task_statuses" to it },
            demandStatuses?.let { "demand_statuses" to it }
        )
    )

fun RemoteAction.Companion.getCheshireScreenNavigationAction(
    type: ScreenType,
    params: Map<String, Any>? = null,
    transition: RemoteActionTransition? = null
) = RemoteAction(
    mobileRoute = ActionRouting.CHESHIRE_SCREEN,
    params = mapOf(
        "action" to RemoteAction(
            method = RemoteActionMethod.GET,
            endpoint = "/app_content/screen/${type.lowercase()}",
            params = mapOf("screen_id" to type.value) + (params ?: emptyMap())
        ),
    ),
    transition = transition
)

fun RemoteAction.Companion.getCheshireScreenAsyncRequestAction(
    type: ScreenType,
    params: Map<String, Any>? = null
) = RemoteAction(
    method = RemoteActionMethod.GET,
    endpoint = "/app_content/screen/${type.lowercase()}",
    params = params
)

fun RemoteAction.Companion.getCheshireScreenFromBudProtocolId(
    protocolId: String,
    screenId: String,
    baseUrl: String = "",
) =
    RemoteAction(
        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
        params = mapOf(
            "action" to RemoteAction(
                method = RemoteActionMethod.POST,
                endpoint = "$baseUrl/bud/start/$protocolId",
                params = mapOf(
                    "screen_id" to screenId
                )
            ),
        )
    )

fun RemoteAction.Companion.externalApp(link: String) = RemoteAction(
    mobileRoute = ActionRouting.EXTERNAL_APP,
    params = mapOf(
        "link" to link
    )
)

fun RemoteAction.Companion.pdf(title: String, link: String) = RemoteAction(
    mobileRoute = ActionRouting.PDF,
    params = mapOf(
        "title" to title,
        "link" to link,
    )
)

fun RemoteAction.Companion.benefits() = RemoteAction(
    mobileRoute = ActionRouting.BENEFITS,
)

fun RemoteAction.Companion.preparations(warning: String?, preparations: List<TestRequestPreparation>) = RemoteAction(
    mobileRoute = ActionRouting.TEST_PREPARATION,
    params = mapOfNotNull(
        warning?.let { "warning" to warning },
        "preparations" to preparations
    )
)
