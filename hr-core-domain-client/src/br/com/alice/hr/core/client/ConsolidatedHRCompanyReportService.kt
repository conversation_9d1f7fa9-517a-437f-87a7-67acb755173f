package br.com.alice.hr.core.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ConsolidatedHRCompanyReport
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ConsolidatedHRCompanyReportService : Service {

    override val namespace get() = "hr-core"
    override val serviceName get() = "consolidated_hr_company_report"

    suspend fun getByCompany(companyId: UUID): Result<ConsolidatedHRCompanyReport, Throwable>
    suspend fun upsert(report: ConsolidatedHRCompanyReport): Result<ConsolidatedHRCompanyReport, Throwable>
    suspend fun update(report: ConsolidatedHRCompanyReport): Result<ConsolidatedHRCompanyReport, Throwable>
    suspend fun create(report: ConsolidatedHRCompanyReport): Result<ConsolidatedHRCompanyReport, Throwable>
}
