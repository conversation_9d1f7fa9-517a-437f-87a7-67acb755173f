package br.com.alice.hr.core.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.rfc.Invoker
import br.com.alice.hr.core.HRCoreDomainConfiguration
import br.com.alice.hr.core.SERVICE_NAME
import br.com.alice.hr.core.client.CompanyScoreMagentaService
import br.com.alice.hr.core.client.CompanyScoreMagentaServiceClient
import br.com.alice.hr.core.client.HrCompanyStaffService
import br.com.alice.hr.core.client.HrCompanyStaffServiceClient
import br.com.alice.hr.core.client.ConsolidatedHRCompanyReportService
import br.com.alice.hr.core.client.ConsolidatedHRCompanyReportServiceClient
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.hr.core.client.HrBeneficiaryServiceClient
import br.com.alice.hr.core.client.HrCompanyService
import br.com.alice.hr.core.client.HrCompanyServiceClient
import org.koin.core.qualifier.named
import org.koin.dsl.module

val HRCoreDomainClientModule = module {
    val baseURL = HRCoreDomainConfiguration.baseUrl
    val invoker = HttpInvoker(DefaultHttpClient(), "$baseURL/rfc")

    single<Invoker>(named(SERVICE_NAME)) { invoker }

    single<ConsolidatedHRCompanyReportService> { ConsolidatedHRCompanyReportServiceClient(get(named(SERVICE_NAME))) }
    single<CompanyScoreMagentaService> { CompanyScoreMagentaServiceClient(get(named(SERVICE_NAME))) }
    single<HrCompanyStaffService> { HrCompanyStaffServiceClient(get(named(SERVICE_NAME))) }
    single<HrCompanyService> { HrCompanyServiceClient(get(named(SERVICE_NAME))) }
    single<HrBeneficiaryService> { HrBeneficiaryServiceClient(get(named(SERVICE_NAME))) }
}
