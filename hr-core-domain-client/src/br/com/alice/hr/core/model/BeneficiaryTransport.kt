package br.com.alice.hr.core.model

import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import java.time.LocalDateTime
import java.util.UUID

data class DependentTransport(
    val firstName: String,
    val lastName: String,
    val mothersName: String,
    val nationalId: String,
    val email: String,
    val sex: Sex,
    val birthDate: LocalDateTime,
    val phoneNumber: String,
    val activatedAt: LocalDateTime,
    val address: Address,
    val productId: UUID,
    val parentBeneficiary: UUID,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType,
    val parentBeneficiaryRelatedAt: LocalDateTime,
    val subcontractId: UUID,
    val cnpj: String?,
)

data class TemplateSheetResult(
    val fileByteContent: ByteArray,
    val fileName: String
)
