version: 0.2

env:
  variables:
    GRADLE_OPTS: -Xmx2048m
    COVERALLS_REPO_TOKEN: tzr3PtLF03BLyXa64XVt2ZGHpbQtaXfKN
    CI_NAME: code-pipeline

phases:
  install:
    runtime-versions:
      java: openjdk8
  pre_build:
    commands:
      - echo [PHASE] Entered the pre_build phase...
      - which git
      - pwd
  build:
    commands:
      - git diff --name-only HEAD~1 | grep -v '/' | cut -d'/' -f1 | sort -u
