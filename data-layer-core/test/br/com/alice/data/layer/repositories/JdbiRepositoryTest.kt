package br.com.alice.data.layer.repositories

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.Sex
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.JsonRaw
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Field.TableIdField
import br.com.alice.common.service.data.dsl.Field.TextField
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.eq
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.SalesFirmStaff
import br.com.alice.data.layer.services.PersonPiiToken
import br.com.alice.data.layer.tables.ItauPaymentTable
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.SalesFirmStaffTable
import br.com.alice.data.layer.tables.SalesFirmTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit.DAYS
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class JdbiRepositoryTest : DataServiceTestHelper() {

    private val repository = JdbiRepositoryFactory(mainJdbi).get(PersonTable::class)
    private val itauRepository = JdbiRepositoryFactory(mainJdbi).get(ItauPaymentTable::class)
    private val salesFirmStaffRepository = JdbiRepositoryFactory(mainJdbi).get(SalesFirmStaffTable::class)
    private val salesFirmRepository = JdbiRepositoryFactory(mainJdbi).get(SalesFirmTable::class)

    @BeforeTest
    fun clean() {
        repository.truncate()
        itauRepository.truncate()
        salesFirmStaffRepository.truncate()
        salesFirmRepository.truncate()
    }

    @Test
    fun `should a new entity when it doesn't exist`() {
        val entity = TestTableFactory.buildPersonTable()
        assertThat(repository.add(entity) is Result.Success).isTrue()
        val now = LocalDateTime.now()

        val retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.id).isEqualTo(entity.id)
        assertThat(retrievedEntity.firstName).isEqualTo(entity.firstName)
        assertThat(retrievedEntity.lastName).isEqualTo(entity.lastName)
        assertThat(retrievedEntity.nationalId).isEqualTo(entity.nationalId)
        assertThat(retrievedEntity.email).isEqualTo(entity.email)
        assertThat(retrievedEntity.version).isEqualTo(0)
        assertThat(retrievedEntity.createdAt).isBetween(now.minusSeconds(1), now)
        assertThat(retrievedEntity.updatedAt).isBetween(now.minusSeconds(1), now)
    }

    @Test
    fun `generated fields must work for add and update`() {
        val entity = ItauPaymentTable(
            invoicePaymentId = RangeUUID.generate(),
            pixId = null,
            boletoId = null,
            paidResponse = null,
        )
        val entity2 = ItauPaymentTable(
            invoicePaymentId = RangeUUID.generate(),
            pixId = null,
            boletoId = null,
            paidResponse = null,
        )
        assertThat(itauRepository.add(entity)).isSuccess()
        val retrievedEntity = itauRepository.get(entity.id)!!
        assertThat(retrievedEntity.ourNumber).isEqualTo(1)

        assertThat(itauRepository.add(entity2)).isSuccess()
        val retrievedEntity2 = itauRepository.get(entity2.id)!!
        assertThat(retrievedEntity2.ourNumber).isEqualTo(2)

        val updated = retrievedEntity2.copy(pixId = "12")
        assertThat(itauRepository.update(updated)).isSuccess()
        val retrievedUpdated = itauRepository.get(entity2.id)
        assertThat(retrievedUpdated?.pixId).isEqualTo("12")
        assertThat(retrievedUpdated?.ourNumber).isEqualTo(2)
    }

    @Test
    fun `should not add a new entity when it already exists`() {
        val entity = TestTableFactory.buildPersonTable()
        assertThat(repository.add(entity)).isSuccess()
        assertThat(repository.add(entity)).isFailure()

        val retrievedUsers = repository.getAll()
        assertThat(retrievedUsers.size).isEqualTo(1)
    }

    @Test
    fun `should return empty when an entity doesn't exist`() {
        val retrievedQuestion = repository.get(RangeUUID.generate())
        assertThat(retrievedQuestion).isNull()
    }

    @Test
    fun `should retrieve all entities`() {
        val entity = TestTableFactory.buildPersonTable()
        repository.add(entity)
        val now = LocalDateTime.now()

        val retrievedUserEntities = repository.getAll()
        assertThat(retrievedUserEntities.size).isEqualTo(1)
        val retrievedEntity = retrievedUserEntities[0]
        assertThat(retrievedEntity.id).isEqualTo(entity.id)
        assertThat(retrievedEntity.firstName).isEqualTo(entity.firstName)
        assertThat(retrievedEntity.lastName).isEqualTo(entity.lastName)
        assertThat(retrievedEntity.nationalId).isEqualTo(entity.nationalId)
        assertThat(retrievedEntity.email).isEqualTo(entity.email)
        assertThat(retrievedEntity.version).isEqualTo(0)
        assertThat(retrievedEntity.createdAt).isBetween(now.minusSeconds(1), now)
        assertThat(retrievedEntity.updatedAt).isBetween(now.minusSeconds(1), now)
    }

    @Test
    fun `should update an entity when pass all fields`() {
        var entity = TestTableFactory.buildPersonTable()
        repository.add(entity)
        entity = repository.get(entity.id)!!

        val updatedEntity = entity.copy(
            firstName = "João", lastName = "Pereira", nationalId = "306.773.520-00", email = "<EMAIL>",
            nickName = null, dateOfBirth = null, sex = null
        )
        assertThat(repository.update(updatedEntity)).isNotNull
        val now = LocalDateTime.now()

        val retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.firstName).isEqualTo("João")
        assertThat(retrievedEntity.lastName).isEqualTo("Pereira")
        assertThat(retrievedEntity.nationalId).isEqualTo("306.773.520-00")
        assertThat(retrievedEntity.email).isEqualTo("<EMAIL>")
        assertThat(retrievedEntity.version).isEqualTo(1)
        assertThat(retrievedEntity.createdAt).isBetween(now.minusSeconds(1), now)
        assertThat(retrievedEntity.updatedAt).isBetween(now.minusSeconds(1), now)
    }

    @Test
    fun `should not update a stale entity`() {
        val entity = TestTableFactory.buildPersonTable()
        repository.add(entity)
        val entity1 = repository.get(entity.id)!!
        val entity2 = repository.get(entity.id)!!

        val entity1Updated = entity1.copy(firstName = "João")
        assertThat(repository.update(entity1Updated)).isNotNull()

        val entity2Updated = entity2.copy(firstName = "Mario")
        assertThat(repository.update(entity2Updated)).isNotNull()

        val retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.firstName).isEqualTo("João")
        assertThat(retrievedEntity.version).isEqualTo(1)
    }

    @Test
    fun `should delete an entity`() {
        val entity = TestTableFactory.buildPersonTable()
        repository.add(entity)

        assertThat(repository.delete(entity)).isTrue()

        val retrievedEntity = repository.get(entity.id)
        assertThat(retrievedEntity).isNull()
    }

    @Test
    fun `should delete an entity by id`() {
        val entity = TestTableFactory.buildPersonTable()
        repository.add(entity)

        assertThat(repository.delete(entity.id)).isTrue()

        val retrievedEntity = repository.get(entity.id)
        assertThat(retrievedEntity).isNull()
    }

    @Test
    fun `should not delete an unexistent entity`() {
        assertThat(repository.delete(RangeUUID.generate())).isFalse()
    }

    @Test
    fun `should not add a new PersonModel when national ID already exists`() {
        val person1 = TestTableFactory.buildPersonTable()
        val person2 = TestTableFactory.buildPersonTable()
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isFailure()

        val retrievedUsers = repository.getAll()
        assertThat(retrievedUsers.size).isEqualTo(1)
    }

    @Test
    fun `should delete all`() {
        val person1 = TestTableFactory.buildPersonTable()
        val person2 = TestTableFactory.buildPersonTable().copy(nationalId = "939.868.050-52", email = "<EMAIL>")
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isSuccess()

        val retrievedUsers = repository.getAll()
        assertThat(retrievedUsers.size).isEqualTo(2)

        repository.truncate()
        val retrievedUsersAfterDelete = repository.getAll()
        assertThat(retrievedUsersAfterDelete.size).isEqualTo(0)
    }

    @Test
    fun `should find by query`() {
        val person1 = PersonTable(
            firstName = "José",
            lastName = "da Silva",
            nationalId = "609.048.950-68",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person2 = PersonTable(
            firstName = "João",
            lastName = "Pereira",
            nationalId = "306.773.520-00",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isSuccess()

        val retrievedUsers =
            repository.find(Query(where = eq(TextField(PersonModel::nationalId), "609.048.950-68")))
        assertThat(retrievedUsers.size).isEqualTo(1)
    }

    @Test
    fun `should find by query in list empty`() {
        val retrievedUsers = repository.find(Query(where = Predicate.False))
        assertThat(retrievedUsers.size).isEqualTo(0)
    }

    @Test
    fun `should count by query`() {
        val person1 = PersonTable(
            firstName = "José",
            lastName = "da Silva",
            nationalId = "609.048.950-68",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person2 = PersonTable(
            firstName = "João",
            lastName = "Pereira",
            nationalId = "306.773.520-00",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isSuccess()

        val countUsers =
            repository.count(Query(where = eq(TextField(PersonModel::nationalId), "609.048.950-68")))
        assertThat(countUsers).isEqualTo(1)
    }

    @Test
    fun `should count and group by query`() {
        val person1 = PersonTable(
            firstName = "José",
            lastName = "da Silva",
            nationalId = "609.048.950-68",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person2 = PersonTable(
            firstName = "João",
            lastName = "da Silva",
            nationalId = "306.773.520-00",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person3 = PersonTable(
            firstName = "João",
            lastName = "Oliveira",
            nationalId = "301.773.520-00",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person4 = PersonTable(
            firstName = "José",
            lastName = "Oliveira",
            nationalId = "301.773.520-08",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person5 = PersonTable(
            firstName = "José",
            lastName = "Oliveira",
            nationalId = "301.773.5202-08",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isSuccess()
        assertThat(repository.add(person3)).isSuccess()
        assertThat(repository.add(person4)).isSuccess()
        assertThat(repository.add(person5)).isSuccess()

        val countUsers = repository.countGrouped(
            Query(groupBy = listOf(TextField(PersonModel::lastName).name, TextField(PersonModel::firstName).name))
        )
        val expected = listOf(
            CountByValues(values = listOf("da Silva", "João"), count = 1),
            CountByValues(values = listOf("Oliveira", "João"), count = 1),
            CountByValues(values = listOf("Oliveira", "José"), count = 2),
            CountByValues(values = listOf("da Silva", "José"), count = 1),
        )
        assertThat(countUsers).isEqualTo(expected)
    }

    @Test
    fun `should query exists by query`() {
        val person1 = PersonTable(
            firstName = "José",
            lastName = "da Silva",
            nationalId = "609.048.950-68",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        val person2 = PersonTable(
            firstName = "João",
            lastName = "Pereira",
            nationalId = "306.773.520-00",
            email = "<EMAIL>",
            addresses = listOf(),
            id = PersonPiiToken()
        )
        assertThat(repository.add(person1)).isSuccess()
        assertThat(repository.add(person2)).isSuccess()

        val existsUsers =
            repository.exists(Query(where = eq(TextField(PersonModel::nationalId), "609.048.950-68")))
        assertThat(existsUsers).isTrue()

        val notExistsUsers =
            repository.exists(Query(where = eq(TextField(PersonModel::nationalId), "012.345.678-90")))
        assertThat(notExistsUsers).isFalse()
    }

    data class TestNestedEntity(val someString: String) : JsonSerializable
    enum class TestEnum { TEST }

    @Test
    fun `test data types`() {
        data class TestTable(
            val someInteger: Int,
            val someShort: Short,
            val someLong: Long,
            val someUuid: UUID,
            val someString: String,
            val someEnum: TestEnum,
            val someDateTime: LocalDateTime,
            val someTime: LocalTime,
            val someBoolean: Boolean,
            val someDouble: Double,
            val someBigDecimal: BigDecimal,
            val someList: List<Int>,
            val someDocument: TestNestedEntity,
            val someDocumentList: List<TestNestedEntity>,
            val someMap: Map<String, Any>,
            val someMapInt: Map<String, Int>,
            val someMapFloat: Map<String, Float>,
            val someMapBoolean: Map<String, Boolean>,
            val someMapDocument: Map<String, TestNestedEntity>,
            val someJsonRaw: JsonRaw,
            override val id: UUID = RangeUUID.generate(),
            override val version: Int = 0,
            override val createdAt: LocalDateTime = LocalDateTime.now(),
            override val updatedAt: LocalDateTime = LocalDateTime.now()
        ) : Table<TestTable> {

            override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
                copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

        }

        val testEntityRepository = JdbiRepository(mainJdbi, TestTable::class)

        val entity = TestTable(
            7,
            8,
            9L,
            RangeUUID.generate(),
            "str",
            TestEnum.TEST,
            LocalDateTime.now(),
            LocalTime.NOON,
            true,
            0.6,
            BigDecimal("12.34"),
            listOf(1, 2, 3),
            TestNestedEntity("str"),
            listOf(TestNestedEntity("str1"), TestNestedEntity("str2")),
            mapOf("key" to "value"),
            mapOf("key" to 10),
            mapOf("key" to 10.1f),
            mapOf("key" to true),
            mapOf("key" to TestNestedEntity("value")),
            JsonRaw("{}")
        )
        assertThat(testEntityRepository.add(entity)).isSuccess()

        val retrievedEntity = testEntityRepository.get(entity.id)!!.success()
        assertThat(retrievedEntity).isSuccessWithDataIgnoringGivenFields(
            entity, "createdAt", "updatedAt"
        )

        val updatedEntity = entity.copy(
            someList = listOf(4, 5, 6), someDocument = TestNestedEntity("strUpd"),
            someDocumentList = listOf(TestNestedEntity("str1Upd"), TestNestedEntity("str2Upd"))
        )
        assertThat(testEntityRepository.update(updatedEntity)).isNotNull

        val retrievedEntity2 = testEntityRepository.get(entity.id)!!
        assertThat(retrievedEntity2.someList).isEqualTo(listOf(4, 5, 6))
        assertThat(retrievedEntity2.someDocument).isEqualTo(TestNestedEntity("strUpd"))
        assertThat(retrievedEntity2.someDocumentList).isEqualTo(
            listOf(
                TestNestedEntity("str1Upd"),
                TestNestedEntity("str2Upd")
            )
        )
    }

    @Test
    fun `test data types when nullable`() {
        data class TestTable(
            val someInteger: Int?,
            val someShort: Short?,
            val someLong: Long?,
            val someUuid: UUID?,
            val someString: String?,
            val someEnum: TestEnum?,
            val someDateTime: LocalDateTime?,
            val someTime: LocalTime?,
            val someBoolean: Boolean?,
            val someDouble: Double?,
            val someBigDecimal: BigDecimal?,
            val someList: List<Int>?,
            val someDocument: TestNestedEntity?,
            val someDocumentList: List<TestNestedEntity>?,
            val someJsonRaw: JsonRaw?,
            override val id: UUID = RangeUUID.generate(),
            override val version: Int = 0,
            override val createdAt: LocalDateTime = LocalDateTime.now(),
            override val updatedAt: LocalDateTime = LocalDateTime.now()
        ) : Table<TestTable> {

            override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
                copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

        }

        val testEntityRepository = JdbiRepository(mainJdbi, TestTable::class)

        val entity = TestTable(null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)
        assertThat(testEntityRepository.add(entity) is Result.Success).isTrue()
        val now = LocalDateTime.now()

        val retrievedEntity = testEntityRepository.get(entity.id)!!
        assertThat(retrievedEntity.id).isEqualTo(entity.id)
        assertThat(retrievedEntity.someInteger).isNull()
        assertThat(retrievedEntity.someShort).isNull()
        assertThat(retrievedEntity.someLong).isNull()
        assertThat(retrievedEntity.someUuid).isNull()
        assertThat(retrievedEntity.someString).isNull()
        assertThat(retrievedEntity.someEnum).isNull()
        assertThat(retrievedEntity.someDateTime).isNull()
        assertThat(retrievedEntity.someTime).isNull()
        assertThat(retrievedEntity.someBoolean).isNull()
        assertThat(retrievedEntity.someDouble).isNull()
        assertThat(retrievedEntity.someBigDecimal).isNull()
        assertThat(retrievedEntity.someList).isNull()
        assertThat(retrievedEntity.someDocument).isNull()
        assertThat(retrievedEntity.someDocumentList).isNull()
        assertThat(retrievedEntity.version).isEqualTo(0)
        assertThat(retrievedEntity.createdAt).isBetween(now.minusSeconds(1), now)
        assertThat(retrievedEntity.updatedAt).isBetween(now.minusSeconds(1), now)
        assertThat(retrievedEntity.someJsonRaw).isNull()
    }

    @Test
    fun `#get should anonymize value`() {
        val date = LocalDateTime.now()
        val entity = TestTableFactory.buildPersonTable(sex = Sex.MALE, dateOfBirth = date).copy(anonymized = true)
        assertThat(repository.add(entity)).isSuccess()

        val retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.id).isEqualTo(entity.id)
        assertThat(retrievedEntity.firstName).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.lastName).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.nationalId).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.email).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.sex).isEqualTo(Sex.MALE)
        assertThat(retrievedEntity.dateOfBirth).isEqualTo(date.withDayOfMonth(1).truncatedTo(DAYS))
        assertThat(retrievedEntity.version).isEqualTo(0)
        assertThat(retrievedEntity.createdAt).isEqualTo(entity.createdAt.withDayOfMonth(1).truncatedTo(DAYS))
        assertThat(retrievedEntity.updatedAt).isEqualTo(entity.updatedAt.withDayOfMonth(1).truncatedTo(DAYS))
    }

    @Test
    fun `#find should anonymize value`() {
        val date = LocalDateTime.now()
        val entity = TestTableFactory.buildPersonTable(sex = Sex.MALE, dateOfBirth = date).copy(anonymized = true)
        assertThat(repository.add(entity)).isSuccess()

        val retrievedEntityList = repository.find(Query(where = eq(TableIdField(PersonModel::id), entity.id)))
        val retrievedEntity = retrievedEntityList.first()
        assertThat(retrievedEntityList.size).isEqualTo(1)
        assertThat(retrievedEntity.id).isEqualTo(entity.id)
        assertThat(retrievedEntity.firstName).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.lastName).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.nationalId).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.email).isEqualTo("ANONIMIZADO")
        assertThat(retrievedEntity.sex).isEqualTo(Sex.MALE)
        assertThat(retrievedEntity.dateOfBirth).isEqualTo(date.withDayOfMonth(1).truncatedTo(DAYS))
        assertThat(retrievedEntity.version).isEqualTo(0)
        assertThat(retrievedEntity.createdAt).isEqualTo(entity.createdAt.withDayOfMonth(1).truncatedTo(DAYS))
        assertThat(retrievedEntity.updatedAt).isEqualTo(entity.updatedAt.withDayOfMonth(1).truncatedTo(DAYS))
    }

    @Test
    fun `#anonymize should anonymize entity`() {
        val entity = TestTableFactory.buildPersonTable()
        assertThat(repository.add(entity)).isSuccess()

        assertThat(repository.anonymize(entity.id)).isSuccess()

        var retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.anonymized).isEqualTo(true)
        assertThat(retrievedEntity.firstName).isEqualTo("ANONIMIZADO")

        assertThat(repository.anonymize(entity.id)).isSuccess()
        retrievedEntity = repository.get(entity.id)!!
        assertThat(retrievedEntity.anonymized).isEqualTo(true)
        assertThat(retrievedEntity.firstName).isEqualTo("ANONIMIZADO")
    }

    @Test
    fun `#get should filter soft deleted entities`() {
        val entityFirm = TestTableFactory.buildSalesFirmTable()
        assertThat(salesFirmRepository.add(entityFirm)).isSuccess()

        val entity = TestTableFactory.buildSalesFirmStaffTable(salesFirmId = entityFirm.id).copy(deletedAt = LocalDateTime.now())
        assertThat(salesFirmStaffRepository.add(entity)).isSuccess()

        val retrievedEntity = salesFirmStaffRepository.get(entity.id)
        assertThat(retrievedEntity).isNull()
    }

    @Test
    fun `#getAll should filter soft deleted entities`() {
        val entityFirm = TestTableFactory.buildSalesFirmTable()
        assertThat(salesFirmRepository.add(entityFirm)).isSuccess()

        val entityDeleted = TestTableFactory.buildSalesFirmStaffTable(
            salesFirmId = entityFirm.id,
            email = "<EMAIL>"
        ).copy(deletedAt = LocalDateTime.now())

        val entityNotDeleted = TestTableFactory.buildSalesFirmStaffTable(
            salesFirmId = entityFirm.id,
            email = "<EMAIL>"
        )

        assertThat(salesFirmStaffRepository.add(entityDeleted)).isSuccess()
        assertThat(salesFirmStaffRepository.add(entityNotDeleted)).isSuccess()

        val result = salesFirmStaffRepository.getAll()
        assertThat(result.size).isEqualTo(1)
    }

    @Test
    fun `#find should filter soft deleted entities`() {
        val entityFirm = TestTableFactory.buildSalesFirmTable()
        assertThat(salesFirmRepository.add(entityFirm)).isSuccess()

        val entityDeleted = TestTableFactory.buildSalesFirmStaffTable(
            salesFirmId = entityFirm.id,
            email = "<EMAIL>"
        ).copy(deletedAt = LocalDateTime.now())
        val entityNotDeleted = TestTableFactory.buildSalesFirmStaffTable(
            salesFirmId = entityFirm.id,
            email = "<EMAIL>"
        )
        assertThat(salesFirmStaffRepository.add(entityDeleted)).isSuccess()
        assertThat(salesFirmStaffRepository.add(entityNotDeleted)).isSuccess()

        val result = salesFirmStaffRepository.find(Query(where = eq(Field.UUIDField(SalesFirmStaff::salesFirmId), entityFirm.id)))
        assertThat(result.size).isEqualTo(1)
    }

    @Test
    fun `#softDelete should delete entity`() {
        val entityFirm = TestTableFactory.buildSalesFirmTable()
        assertThat(salesFirmRepository.add(entityFirm)).isSuccess()

        val entity = TestTableFactory.buildSalesFirmStaffTable(salesFirmId = entityFirm.id)
        assertThat(salesFirmStaffRepository.add(entity)).isSuccess()
        assertThat(salesFirmStaffRepository.softDelete(entity)).isTrue()

        val retrievedEntity = salesFirmStaffRepository.get(entity.id)
        assertThat(retrievedEntity).isNull()
    }
}
