package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.RangeUUID
import br.com.alice.common.RangeUUID.PERSON_ID_RANGE
import br.com.alice.common.core.PersonId
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.TestHi
import br.com.alice.data.layer.models.TestPerson
import br.com.alice.data.layer.models.TestPii
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.TestNonPiiTable
import br.com.alice.data.layer.tables.TestPersonTable
import br.com.alice.data.layer.tables.TestPiiTable
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import kotlin.random.Random
import kotlin.test.Test

class DeIdentificationPipelineTest : DataServiceTestHelper() {

    private val authorizationService: AuthorizationService = mockk()
    private val tokenVerifier: TokenVerifier = mockk()
    private val replicationLagService: ReplicationLagService = mockk()
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService

    private val factory = DatabasePipelineFactory(
        mainJdbi,
        mainJdbiReplica,
        authorizationService,
        tokenVerifier,
        personTokenService,
        ContextService(tokenVerifier),
        replicationLagService,
        true
    )

    private val testHiDataPipeline = factory.get(TestHi::class, TestNonPiiTable::class, false)
    private val testPiiDataPipeline = factory.get(TestPii::class, TestPiiTable::class, false)
    private val testPersonDataPipeline = factory.get(TestPerson::class, TestPersonTable::class, false)
    private val personDataPipeline = factory.get(PersonModel::class, PersonTable::class, false)

    private val mainJdbiFactory = JdbiRepositoryFactory(mainJdbi)
    private val testHiJdbi = mainJdbiFactory.get(TestNonPiiTable::class)
    private val testPersonJdbi = mainJdbiFactory.get(TestPersonTable::class)

    private suspend fun createTestPerson(name: String): TestPerson {
        val token = personTokenService.createForPersonId(PersonId()).get()
        val personId = token.personId
        return testPersonDataPipeline.add(TestPerson(name = name, id = personId)).get()
    }

    private suspend fun createPerson(name: String): PersonModel {
        val token = personTokenService.createForPersonId(PersonId()).get()
        val personId = token.personId
        return personDataPipeline.add(DataLayerTestModelFactory.buildPerson(firstName = name, personId = personId)).get()
    }

    @Test
    fun `create and verify person with Pii was saved with personPiiToken`() = runBlocking<Unit> {
        val name = "Gustavo"
        val testPerson = createTestPerson(name)

        val personPiiToken = personTokenService.getPersonPiiToken(testPerson.id)!!

        val testPersonTable = testPersonJdbi.get(personPiiToken.id)

        assertThat(testPersonTable).isNotNull
        assertThat(testPersonTable?.name).isEqualTo(name)
    }

    @Test
    fun `create and verify testHiTable was saved with personNonPiiToken`() = runBlocking<Unit> {
        val name = "Jack"
        val testPerson = createTestPerson(name)

        val healthInformationDetail = "Embarrassing Disease"
        val addedHealthInformation = testHiDataPipeline.add(TestHi(testPerson.id, healthInformationDetail)).get()

        val testHiTable = testHiJdbi.get(addedHealthInformation.id)!!
        val personNonPiiToken = personTokenService.getPersonNonPiiToken(testPerson.id)!!

        assertThat(testHiTable.personId).isEqualTo(personNonPiiToken)
    }

    @Test
    fun `create and retrieve test person using personId`() = runBlocking<Unit> {
        val name = "Jack"
        val testPerson = createTestPerson(name)

        val retrievedTestPerson = testPersonDataPipeline.get(testPerson.id)
        assertThat(retrievedTestPerson.get().name).isEqualTo(testPerson.name)
    }

    @Test
    fun `create and verify health information using personId`() = runBlocking<Unit> {
        val name = "Jack"
        val testPerson = createTestPerson(name)

        val healthInformationDetail = "Embarrassing Disease"
        val addedHealthInformation = testHiDataPipeline.add(TestHi(testPerson.id, healthInformationDetail)).get()

        val retrievedHealthInformation = testHiDataPipeline.get(addedHealthInformation.id).get()
        assertThat(retrievedHealthInformation.healthInformation).isEqualTo(healthInformationDetail)
    }

    @Test
    fun `create with invalid id range does not save`() = runBlocking {
        val testPerson = createTestPerson("John")

        val invalidId = RangeUUID.generate(PERSON_ID_RANGE)
        assertThatThrownBy {
            runBlocking<Unit> {
                testHiDataPipeline.add(TestHi(
                    personId = testPerson.id, healthInformation = "", id = invalidId
                )).get()
            }
        }.isInstanceOf(IllegalArgumentException::class.java)

        assertThat(testHiJdbi.get(invalidId)).isNull()
    }

    @Test
    fun `find testHi by personId`() = runBlocking<Unit> {
        val name = "Jack"
        val testPerson = createTestPerson(name)

        val healthInformationDetail = "Embarrassing Disease"
        testHiDataPipeline.add(TestHi(testPerson.id, healthInformationDetail)).get()

        val query = Query(
            where = Predicate.eq(
                Field.TableIdField(
                    TestHi::personId
                ),
                testPerson.id
            )
        )
        val found = testHiDataPipeline.findByQuery(query).get().firstOrNull()

        assertThat(found?.healthInformation).isNotNull
        assertThat(found?.healthInformation).isEqualTo(healthInformationDetail)
    }

    @Test
    fun `find testPii by personId`() = runBlocking<Unit> {
        val name = "Jack"
        val testPerson = createTestPerson(name)

        val memberId = Random.nextInt().toString()

        testPiiDataPipeline.add(TestPii(testPerson.id, memberId)).get()

        val query = Query(
            where = Predicate.eq(
                Field.TableIdField(
                    TestPii::personId
                ),
                testPerson.id
            )
        )
        val found = testPiiDataPipeline.findByQuery(query).get().firstOrNull()

        assertThat(found?.memberId).isNotNull
        assertThat(found?.memberId).isEqualTo(memberId)
    }

    @Test
    fun `find PersonModel by id`() = runBlocking<Unit> {
        val name = "Jack"
        val person = createPerson(name)

        val query = Query(
            where = Predicate.eq(
                Field.TableIdField(
                    PersonModel::id
                ),
                person.id
            )
        )
        val found = personDataPipeline.findByQuery(query).get().firstOrNull()

        assertThat(found?.id).isNotNull
        assertThat(found?.id).isEqualTo(person.id)
    }
}
