package br.com.alice.common

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.rfc.TestInvoker
import br.com.alice.data.layer.ApplicationModule
import br.com.alice.data.layer.MAIN_JDBI
import br.com.alice.data.layer.MAIN_RO_JDBI
import br.com.alice.data.layer.TOKEN_JDBI
import com.opentable.db.postgres.embedded.EmbeddedPostgres
import io.ktor.server.engine.ApplicationEngine
import io.ktor.server.engine.addShutdownHook
import io.ktor.server.testing.TestApplicationEngine
import io.mockk.mockk
import org.flywaydb.core.Flyway
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.postgres.PostgresPlugin
import org.koin.core.qualifier.named
import org.koin.dsl.module
import org.postgresql.ds.PGSimpleDataSource
import javax.sql.DataSource
import io.zonky.test.db.postgres.embedded.EmbeddedPostgres as ZonkyEmbeddedPostgres

object TestApplication {
    private var testEngine: TestApplicationEngine? = TestApplicationEngine()
    private var applicationEngine: ApplicationEngine? = null
    var httpInvoker = TestInvoker(testEngine!!, "data")

    val mainJdbi = Database.get(DatabaseMigrator.migrate("main"))
    val mainJdbiReplica = mainJdbi
    val tokenJdbi = Database.get(DatabaseMigrator.migrate("token"))

    val dbModule = module(createdAtStart = true) {
        single(qualifier = named(MAIN_JDBI)) { mainJdbi }
        single(qualifier = named(MAIN_RO_JDBI)) { mainJdbi }
        single(qualifier = named(TOKEN_JDBI)) { tokenJdbi }

        single { tokenVerifier }
    }

    fun start() {
        if (testEngine == null) {
            testEngine = TestApplicationEngine()
            httpInvoker = TestInvoker(testEngine!!, "data")
        }

        if (applicationEngine == null) {
            applicationEngine = testEngine!!.start()

            val modules = ApplicationModule.dependencyInjectionModules(true) + dbModule
            ApplicationModule.module(modules, applicationEngine!!.application)

            applicationEngine!!.addShutdownHook {
                DatabaseMigrator.dispose()
            }
        }
    }
}

object Database {

    fun get(db: DataSource): Jdbi {
        val c = db.connection

        return Jdbi.create(c).installPlugin(
            PostgresPlugin()
        )
    }
}

object DatabaseMigrator {

    private val mainDb = buildDB()
    private val tokenDb = buildDB()

    fun migrate(database: String, customDb: ZonkyEmbeddedPostgres? = null, customLocation: String? = null): DataSource {
        val db = customDb?.postgresDatabase ?: (if (database == "main") mainDb else tokenDb).postgresDatabase
        return migrate(db, database, customLocation)
    }

    fun customMainJdbiEmbedded(customDb: EmbeddedPostgres, customMigrationFolder: String): Jdbi =
        Database.get(migrate(customDb.postgresDatabase, "main", customMigrationFolder))

    fun dispose() {
        mainDb.close()
        tokenDb.close()
    }

    private fun buildDB(): ZonkyEmbeddedPostgres =
        ZonkyEmbeddedPostgres
            .builder()
            .start()

    private fun migrate(dataSource: DataSource, database: String, customLocation: String? = null) : DataSource {
        Flyway.configure()
            .placeholders(mapOf("database" to (dataSource as PGSimpleDataSource).databaseName, "randomPassword" to RangeUUID.generate().toString()))
            .dataSource(dataSource)
            .locations(
                "filesystem:../data-layer-core/resources/db/migration/$database",
                "filesystem:../data-layer-core/testresources/db/migration/test/${customLocation ?: "main"}",
                "filesystem:../data-layer-core/testresources/db/migration/test/main"
            )
            .load()
            .migrate()

        return dataSource
    }
}

val tokenVerifier: TokenVerifier = mockk()
