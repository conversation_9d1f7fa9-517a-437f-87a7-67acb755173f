package br.com.alice.data.layer.policies

import br.com.alice.data.layer.SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.models.HealthCommunitySpecialistModel
import br.com.alice.data.layer.models.HealthPlan
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskGroup
import br.com.alice.data.layer.models.HealthPlanTaskReferrals
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.MedicalSpecialtyModel
import br.com.alice.data.layer.models.SpecialistOpinion
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.policies.features.viewAndCreateUpdateProcedures
import br.com.alice.data.layer.subjects.Unauthenticated

val secondaryAttentionPolicySet = policySet {
    match("at secondary-attention-domain-service", { rootService.name == SECONDARY_ATTENTION_DOMAIN_ROOT_SERVICE_NAME }) {
        includes(viewAndCreateUpdateProcedures)

        allows(HealthProfessionalModel::class, Count, View)
        allows(StaffModel::class, Count, View)

        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(Timeline::class, Count, View, Update) // Backfill
            allows(CounterReferral::class, Count, View) // Backfill
            allows(HealthPlanTask::class, Count, View, Create, Update)
            allows(HealthCommunitySpecialistModel::class, Count, View) // Backfill
            allows(MedicalSpecialtyModel::class, Count, View) // Backfill
            allows(HealthPlanTaskGroup::class, Count, View, Create, Update) // Backfill
            allows(HealthPlan::class, Count, View) // Backfill
            allows(GenericFileVault::class, Count, View, Create)
            allows(FileVault::class, Count, View, Create)
            allows(SpecialistOpinion::class, Count, View, Create, Update)
            allows(HealthPlanTaskReferrals::class, Count, View, Create, Update)
        }

    }
}
