package br.com.alice.data.layer.policies

import br.com.alice.data.layer.EXEC_INDICATOR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.policies.features.basicAccessMagicNumbers
import br.com.alice.data.layer.policies.features.canCreateAndDeleteExternalReferral
import br.com.alice.data.layer.policies.features.canViewExternalReferral
import br.com.alice.data.layer.policies.features.canViewGuiaWithProcedures
import br.com.alice.data.layer.policies.features.canViewHealthCondition
import br.com.alice.data.layer.policies.features.canViewMedicalSpecialty
import br.com.alice.data.layer.policies.features.healthDocumentSet
import br.com.alice.data.layer.policies.features.rollbackProcedureExecution
import br.com.alice.data.layer.policies.features.viewAndCountHealthCommunitySpecialist
import br.com.alice.data.layer.policies.features.viewAndCreateHealthDocument
import br.com.alice.data.layer.policies.features.viewAndUpdateAndCreateHealthDocument
import br.com.alice.data.layer.policies.features.viewBeneficiary
import br.com.alice.data.layer.policies.features.viewProductComponents
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Unauthenticated

val execIndicatorApiPolicySet = policySet {

    match("at exec-indicator API", { rootService.name == EXEC_INDICATOR_API_ROOT_SERVICE_NAME }) {
        includes(viewBeneficiary)

        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(healthDocumentSet)
            includes(basicAccessMagicNumbers)
            match("can view", { action is View }) {
                match("StaffModel attempting to login with") { staffAttemptingToLoginWith() }
            }
            match("can view", { action is View }) {
                match("any Health Community Specialist") { resource is HealthCommunitySpecialistModel }
                match("any StructuredAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Medical Specialty") { resource is MedicalSpecialtyModel }
            }
        }
        match("StaffModel", { subject is StaffModel }) {
            match("can view", { action is View }) {
                match("View herself") { resource is StaffModel && subject is StaffModel && resource.id == subject.id }
            }
        }
        match("can view", { action is View }) {
            match("any authorizer") { resource is ExecIndicatorAuthorizerModel }
            match("any ProviderUnit") { resource is ProviderUnitModel }
            match("any StructuredAddress") { resource is StructuredAddress }
            match("any Contact") { resource is ContactModel }
            match("any member") { resource is MemberModel }
            match("any MemberProductPriceModel") { resource is MemberProductPriceModel }
            match("any TestCode") { resource is TestCodeModel }
            match("any TotvsGuia") { resource is TotvsGuiaModel }
            match("any HealthcareResource") { resource is HealthcareResourceModel }
            match("any HealthcareBundle") { resource is HealthcareBundleModel }
            match("any Provider") { resource is ProviderModel }
            match("any Product") { resource is ProductModel }
            match("any HealthInstitutionNegotiation") { resource is HealthInstitutionNegotiationModel }
            match("any HealthSpecialistResourceBundle") { resource is HealthSpecialistResourceBundleModel }
        }
        match("EITA user", { subject is EmailDomain || (subject is StaffModel && subject.isEitaUser()) }) {
            includes(healthDocumentSet)
            includes(viewAndCreateHealthDocument)
            includes(viewAndUpdateAndCreateHealthDocument)
            includes(viewProductComponents)
            includes(viewAndCountHealthCommunitySpecialist)
            includes(rollbackProcedureExecution)
            includes(canViewExternalReferral)
            includes(canViewMedicalSpecialty)
            includes(canViewHealthCondition)
            includes(canCreateAndDeleteExternalReferral)
            includes(canViewGuiaWithProcedures)
            match("can view", { action is View }) {
                match("any PersonModel") { resource is PersonModel }
                match("any MemberModel") { resource is MemberModel }
                match("any ProviderTestCode") { resource is ProviderTestCodeModel }
                match("any ProviderTestCodeDataIntegration") { resource is ProviderTestCodeDataIntegrationModel }
                match("any TestCode") { resource is TestCodeModel }
                match("any authorizer") { resource is ExecIndicatorAuthorizerModel }
                match("any healthPlanTask") { resource is HealthPlanTask }
                match("any staff") { resource is StaffModel }
                match("any provider unit") { resource is ProviderUnitModel }
                match("any structured address") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any Hippocrates Healthcare Professional") { resource is HippocratesHealthcareProfessionalModel }
                match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
                match("any PersonInternalReference") { resource is PersonInternalReference }
                match("any TotvsGuia") { resource is TotvsGuiaModel }
                match("any HealthcareResource") { resource is HealthcareResourceModel }
                match("any Provider") { resource is ProviderModel }
                match("any Product") { resource is ProductModel }
            }
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any authorized procedure") { resource is MvAuthorizedProcedureModel }
                match("any ExecutionGroup") { resource is ExecutionGroupModel }
                match("any DbLaboratoryTestResultProcess") { resource is DbLaboratoryTestResultProcess }
            }
            match("can count", { action is Count }) {
                match("any ExecIndicatorAuthorizer") { resourceIs(ExecIndicatorAuthorizerModel::class) }
                match("any ExecutionGroup") { resourceIs(ExecutionGroupModel::class) }
                match("any HealthcareResource") { resourceIs(HealthcareResourceModel::class) }
                match("any TotvsGuia") { resource is TotvsGuiaModel }
            }
            allows(FileVault::class, Create, Delete)
            allows(TotvsGuiaModel::class, View, Create, Update, Count)
            allows(MvAuthorizedProcedureModel::class, View, Create, Update)
            allows(HospitalizationInfoModel::class, View, Create, Update)
            allows(AttachmentChemotherapyModel::class, View, Count, Create, Update)
            allows(AttachmentOpmeModel::class, View, Count, Create, Update)
            allows(NullvsIntegrationRecordModel::class, View)
            allows(AttachmentRadiotherapyModel::class, View, Count, Create, Update)
            allows(HealthcareBundleModel::class, View)
            allows(HealthInstitutionNegotiationModel::class, View)
        }
        match("EITA Authorizer User", { subject is StaffModel && subject.isEitaAuthorizerUser() }) {
            includes(viewAndCountHealthCommunitySpecialist)
            includes(rollbackProcedureExecution)
            includes(canViewExternalReferral)
            includes(canViewMedicalSpecialty)
            includes(canViewHealthCondition)
            includes(canViewGuiaWithProcedures)
            match("can view", { action is View }) {
                match("any MvAuthorizedProcedure") { resource is MvAuthorizedProcedureModel }
                match("any PersonModel") { resource is PersonModel }
                match("any Staff") { resource is StaffModel }
                match("any HealthPlanTask") { resource is HealthPlanTask }
                match("any ExecIndicatorAuthorizer") { resource is ExecIndicatorAuthorizerModel }
                includes(viewProductComponents)
            }
            match("can count", { action is Count }) {
                match("any MvAuthorizedProcedure") { resourceIs(MvAuthorizedProcedureModel::class) }
            }
            match("can update", { action is Update }) {
                match("any authorized procedure") { resource is MvAuthorizedProcedureModel }
            }
        }
    }
}
