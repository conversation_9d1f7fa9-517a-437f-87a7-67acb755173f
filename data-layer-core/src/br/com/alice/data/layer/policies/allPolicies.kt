package br.com.alice.data.layer.policies

import br.com.alice.data.layer.authorization.policySet

val allPolicies = policySet {
    includes(actionPlanPolicySet)
    includes(akinatorPolicySet)
    includes(amasPolicySet)
    includes(amasBffApiPolicySet)
    includes(atlasPolicySet)
    includes(appContentScreenDetailPolicySet)
    includes(appointmentPolicySet)
    includes(bottiniPolicySet)
    includes(businessPolicySet)
    includes(businessPolicySet)
    includes(businessRiskPolicySet)
    includes(channelPolicySet)
    includes(clinicalAccountPolicySet)
    includes(commonPolicySet)
    includes(coveragePolicySet)
    includes(dbPolicySet)
    includes(dragonRadarPolicySet)
    includes(duquesaPolicySet)
    includes(ehrPolicySet)
    includes(einsteinPolicySet)
    includes(eitaExternalApiPolicySet)
    includes(eventinderPolicySet)
    includes(exampleApiPolicySet)
    includes(execIndicatorPolicySet)
    includes(execIndicatorApiPolicySet)
    includes(featureConfigPolicySet)
    includes(fhirPolicySet)
    includes(fileVaultPolicySet)
    includes(fleuryPolicySet)
    includes(haocPolicySet)
    includes(healthAnalyticsPolicySet)
    includes(healthCareOpsPolicySet)
    includes(healthConditionPolicySet)
    includes(healthLogicPolicySet)
    includes(healthPlanPolicySet)
    includes(limboApiPolicySet)
    includes(maraudersPolicySet)
    includes(memberPolicySet)
    includes(memberWannabePolicySet)
    includes(moneyInPolicySet)
    includes(moneyInBffApiPolicySet)
    includes(nullvsPolicySet)
    includes(eitaNullvsPolicySet)
    includes(onboardingPolicySet)
    includes(personPolicySet)
    includes(productDomainPolicySet)
    includes(providerDomainServicePolicySet)
    includes(questionnairePolicySet)
    includes(refundPolicySet)
    includes(salesChannelPolicySet)
    includes(schedulesPolicySet)
    includes(screeningPolicySet)
    includes(secondaryAttentionPolicySet)
    includes(sherlockPolicySet)
    includes(sortingHatPolicySet)
    includes(staffPolicySet)
    includes(sysOpsPolicySet)
    includes(tempCarcassViewFileVault)
    includes(itauIntegrationPolicySet)
    includes(testResultPolicySet)
    includes(wandaPolicySet)
    includes(zendeskPolicySet)
    includes(tissPolicySet)
    includes(backofficePolicySet)
    includes(memberOnboardingPolicySet)
    includes(hrCorePolicySet)
}
