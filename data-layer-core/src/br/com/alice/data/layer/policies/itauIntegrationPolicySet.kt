package br.com.alice.data.layer.policies

import br.com.alice.data.layer.ITAU_INTEGRATION_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.ItauPaymentModel
import br.com.alice.data.layer.subjects.Unauthenticated

val itauIntegrationPolicySet = policySet {
    match("at itau-integration-service", { rootService.name == ITAU_INTEGRATION_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(ItauPaymentModel::class, View, Count, Create, Update)
        }
    }
}
