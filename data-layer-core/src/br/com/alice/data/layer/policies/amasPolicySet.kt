package br.com.alice.data.layer.policies

import br.com.alice.data.layer.AMAS_BACKFILL_SERVICE_NAME
import br.com.alice.data.layer.AMAS_RECURRING_SERVICE_NAME
import br.com.alice.data.layer.AMAS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.ContactModel
import br.com.alice.data.layer.models.ExecIndicatorAuthorizerModel
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.InvoiceModel
import br.com.alice.data.layer.models.NationalReceiptModel
import br.com.alice.data.layer.models.ProviderUnitModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.policies.features.createAndViewHealthProfessionalTierHistory
import br.com.alice.data.layer.policies.features.createOrUpdateTussProcedureProvider
import br.com.alice.data.layer.policies.features.createOrUpdateTussProcedureSpecialty
import br.com.alice.data.layer.policies.features.createTissBatchHistoric
import br.com.alice.data.layer.policies.features.generateAutomaticInvoices
import br.com.alice.data.layer.policies.features.sync
import br.com.alice.data.layer.policies.features.uploadPreviewEarningSummary
import br.com.alice.data.layer.policies.features.uploadTissBatch
import br.com.alice.data.layer.policies.features.validateTussProcedureProviderPrices
import br.com.alice.data.layer.policies.features.viewNationalReceipt
import br.com.alice.data.layer.subjects.Unauthenticated

val amasPolicySet = policySet {
    match("at amas-domain-service", { rootService.name == AMAS_ROOT_SERVICE_NAME }) {
        includes(sync)
        includes(uploadTissBatch)
        includes(uploadPreviewEarningSummary)
        includes(createOrUpdateTussProcedureProvider)
        includes(createOrUpdateTussProcedureSpecialty)
        includes(validateTussProcedureProviderPrices)
        includes(createTissBatchHistoric)
        includes(viewNationalReceipt)
        includes(generateAutomaticInvoices)
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(createAndViewHealthProfessionalTierHistory)
        }
    }

    match("at amas-domain-service-recurring", { rootService.name == AMAS_RECURRING_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view and update and create", { action is View || action is Update || action is Create }) {
                match("any NationalReceipt") { resource is NationalReceiptModel }
                match("any Invoice") { resource is InvoiceModel }
            }
        }
    }

    match("at amas-domain-service-backfill", { rootService.name == AMAS_BACKFILL_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view and update", { action is View || action is Update }) {
                match("any Invoice") { resource is InvoiceModel }
                match("any NationalReceipt") { resource is NationalReceiptModel }
            }
            match("can view", { action is View }) {
                match("any StaffModel") { resource is StaffModel }
                match("any ProviderUnit") { resource is ProviderUnitModel }
                match("any StructureAddress") { resource is StructuredAddress }
                match("any ContactModel") { resource is ContactModel }
                match("any ExecIndicatorAuthorizer") { resource is ExecIndicatorAuthorizerModel }
            }
            includes(createAndViewHealthProfessionalTierHistory)
            allows(HealthProfessionalModel::class, View)
        }
    }
}
