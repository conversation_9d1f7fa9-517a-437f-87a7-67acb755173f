package br.com.alice.data.layer.policies

import br.com.alice.data.layer.MONEY_IN_BFF_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BillingAccountablePartyModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PaymentDetailModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.ProductModel
import br.com.alice.data.layer.models.ResourceSignTokenModel
import br.com.alice.data.layer.subjects.Unauthenticated

val moneyInBffApiPolicySet = policySet {
    match("at money-in-bff-api", { rootService.name == MONEY_IN_BFF_API_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any ResourceSignTokenModel") { resource is ResourceSignTokenModel }
                match("any ProductModel") { resource is ProductModel }
                match("any BeneficiaryModel") { resource is BeneficiaryModel }
                match("any MemberModel") { resource is MemberModel }
                match("any MemberInvoice") { resource is MemberInvoiceModel }
                match("any InvoicePayment") { resource is InvoicePaymentModel }
                match("any PaymentDetail") { resource is PaymentDetailModel }
                match("any InvoiceItem") { resource is InvoiceItemModel }
                match("any BillingAccountableParty") { resource is BillingAccountablePartyModel }
                match("any Person") { resource is PersonModel }
                match("any MemberInvoiceGroup") { resource is MemberInvoiceGroupModel }
            }
        }
    }

    describe("for /status endpoints") {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any MemberInvoice of Test Persons") { testPersonResource(MemberInvoiceModel::class) }
            }
        }
    }
}
