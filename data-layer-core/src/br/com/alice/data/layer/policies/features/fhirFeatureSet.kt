package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.subjects.Unauthenticated

val createDiagnosticReport = policySet {
    describe("at create-diagnostic-report") {
        match("can view", { action is View }) {
            match("Specific PersonModel") {
                resource is PersonModel && subject is Unauthenticated && resource.nationalId == subject.key
            }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any Fhir Diagnostic Report") { resourceIs(FhirDiagnosticReport::class) }
        }
    }
}

val registerFhirDocument = policySet {
    describe("at action table Fhir Document") {
        match("can view and update and create", { action is View || action is Update || action is Create || action is Count}) {
            match("specific person") {
                resource is FhirDocument && subject is Unauthenticated
            }
        }
    }
}

val upsertHaocFhirProcess = policySet {
    describe("at action table Haoc Fhir Process") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("Unauthenticated") {
                resource is HaocFhirProcess && subject is Unauthenticated
            }
        }
    }
}

val createBundleResource = policySet {
    describe("at create-fhir-bundle-resource") {
        match("can view", { action is View }) {
            match("Specific PersonModel") {
                resource is PersonModel && subject is Unauthenticated && resource.nationalId == subject.key
            }
        }
        match("can view and update and create", { action is View || action is Update || action is Create}) {
           match("any Fhir Bundle Resource") { resource is FhirBundle }
        }
    }
}

val viewProviderAccess = policySet {
    describe("at view-provider-access") {
        match("can view", { action is View }) {
            match("any Fhir Provider Access") { resourceIs(FhirProviderAccess::class) }
        }
    }
}
val registerPublicToken = policySet {
    describe("at register-public-token") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any Fhir Public Token Integration") { resourceIs(PublicTokenIntegration::class) }
        }
        match("can view", { action is View }) {
            match("any PersonModel") { resourceIs(PersonModel::class) }
        }
    }
}

val countAppointment = policySet {
    describe("at bundle-endpoint") {
        match("can count", { action is Count }) {
            match("any Appointment") { resourceIs(Appointment::class) }
        }
    }
}

val viewHealthInformation = policySet {
    describe("at bundle-collection") {
        match("can view", { action is View }) {
            match("PersonModel's Appointment") {
                resource is Appointment && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("PersonModel's Health Measurement") {
                resource is HealthMeasurementModel && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("PersonModel's Health Measurement Type") {
                resource is HealthMeasurementTypeModel && subject is Unauthenticated
            }
            match("PersonModel's Clinical Backgrounds") {
                resource is ClinicalBackground && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("PersonModel's Health Plan") {
                resource is HealthPlan && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("PersonModel's Health Plan Tasks") {
                resource is HealthPlanTask && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("PersonModel's Assistance Care") {
                resource is AssistanceCare && subject is Unauthenticated && resource.personId.toString() == subject.key
            }
            match("any StaffModel") { resource is StaffModel }
            match("any Health Professionals") { resource is HealthProfessionalModel }
            match("any Medical Specialty") { resource is MedicalSpecialtyModel }
        }
    }
}

val registerReadTracking = policySet {
    describe("at member-history") {
        match("can view and create", { action is View || action is Create }) {
            match("any person and provider") { resourceIs(ProviderReadTracking::class) }
        }
    }
}

val registerSummaryHistory = policySet {
    describe("at action table Hospital Summary History") {
        allows(HospitalSummaryHistory::class, View, Update, Create)
    }
}

val createExternalAppointmentSchedule = policySet {
    describe("at External appointment schedule") {
        allows(ExternalAppointmentScheduleModel::class, View, Update, Create)
    }
}

val createExternalReferral = policySet {
    describe("at External Referral") {
        allows(ExternalReferralModel::class, View, Update, Create)
    }
}

val createHaocFhirProcess = policySet {
    describe("at Haoc Fhir Process") {
        allows(HaocFhirProcess::class, View, Count, Create)
    }
}

val deleteHaocFhirProcess = policySet {
    describe("at Haoc Fhir Process") {
        allows(HaocFhirProcess::class, Delete)
    }
}

val viewMemberModel = policySet {
    describe("at view-member-model") {
        match("can view", { action is View }) {
            match("any MemberModel") { resourceIs(MemberModel::class) }
        }
    }
}

val viewFileVaultModel = policySet {
    describe("at view-file-vault") {
        match("can view", { action is View }) {
            match("any FileVault") {
                resource is FileVault &&
                        resource.domain == "member" &&
                        resource.namespace == "term" &&
                        subject is Unauthenticated
            }
        }
    }
}

val viewCreateAndUpdateConsentRegistration = policySet {
    describe("at view-create-update-consent-registration") {
        match("can view", { action is View || action is Update || action is Create }) {
            match("any ConsentRegistration") { resourceIs(ConsentRegistration::class) }
        }
    }
}
