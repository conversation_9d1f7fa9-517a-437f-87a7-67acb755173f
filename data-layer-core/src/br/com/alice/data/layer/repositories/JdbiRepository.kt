package br.com.alice.data.layer.repositories

import br.com.alice.common.core.TableId
import br.com.alice.common.core.exceptions.DatabaseException
import br.com.alice.common.core.extensions.toSnakeCase
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Geography
import br.com.alice.common.observability.opentelemetry.Tracer
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.JsonRaw
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.SqlGenerator
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.service.serialization.isoDateGson
import br.com.alice.data.layer.annotations.GeneratedTableField
import br.com.alice.data.layer.exceptions.DuplicatedItemException
import br.com.alice.data.layer.exceptions.NotFoundException
import br.com.alice.data.layer.services.AnonymizerService
import br.com.alice.data.layer.services.PersonNonPiiToken
import br.com.alice.data.layer.services.PersonPiiToken
import br.com.alice.data.layer.tables.Anonymizable
import br.com.alice.data.layer.tables.AnonymizableTable
import br.com.alice.data.layer.tables.SoftDeletable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.statement.UnableToExecuteStatementException
import org.jdbi.v3.core.statement.Update
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.reflect.KClass
import kotlin.reflect.KParameter
import kotlin.reflect.KProperty1
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.jvm.javaType
import kotlin.reflect.jvm.jvmErasure

open class JdbiRepository<T : Table<T>>(open val jdbi: Jdbi, val cls: KClass<T>) {
    private val tableName = cls.simpleName!!.toSnakeCase().removeSuffix("_table")
    private var jdbiReplica: Jdbi = jdbi

    constructor(jdbi: Jdbi, cls: KClass<T>, jdbiReplica: Jdbi): this(jdbi, cls) {
        this.jdbiReplica = jdbiReplica
    }

    private fun getMemberPropertiesForCreateAndUpdate(): List<KProperty1<T, *>> {
        return cls.memberProperties.filter { it.findAnnotation<GeneratedTableField>() == null }
    }

    fun add(item: T): Result<T, DatabaseException> {
        val memberProperties = getMemberPropertiesForCreateAndUpdate()

        val colNames = StringBuffer()
        memberProperties.joinTo(colNames, ", ") { it.name.toSnakeCase() }

        val colValues = StringBuffer()
        memberProperties.joinTo(colValues, ", ") { formatColVal(it, item) }

        val now = LocalDateTime.now()
        val newItem = item.copyTable(version = 0, updatedAt = now, createdAt = now)

        return Result.of {
            val inserted = jdbi.withHandle<Int, DatabaseException> { handle ->
                val insert = handle.createUpdate("""
                |INSERT INTO "$tableName" ($colNames)
                |VALUES ($colValues)
                |""".trimMargin())

                cls.memberProperties.forEach { bindPropertyToUpdate(it, newItem, insert) }

                span("INSERT INTO $tableName") {
                    it.setAttributes(newItem)
                    try {
                        insert.execute()
                    } catch (e: UnableToExecuteStatementException) {
                        logger.warn("Error on JdbiRepository.add: id=${item.id} class=${cls.qualifiedName}", e)
                        if (e.message?.contains("duplicate key") == true)
                            throw DuplicatedItemException("Duplicated: operation=add id=${item.id} class=${cls.qualifiedName}", cause = e)
                        else
                            throw DatabaseException("Database error: operation=add id=${item.id} class=${cls.qualifiedName}", cause = e)
                    }
                }
            } > 0

            if (inserted) {
                newItem
            } else {
                throw DatabaseException("Database error: operation=add id=${item.id} class=${cls.qualifiedName}")
            }
        }
    }

    open fun get(id: Any, useWriteDb: Boolean = false): T? {
        val jdbiTarget = if(useWriteDb) jdbi else jdbiReplica
        return jdbiTarget.withHandle<T?, DatabaseException> { handle ->
            val rawId = when (id) {
                is TableId -> id.id
                else -> id
            }

            val softDeletableClause = if (cls.isSubclassOf(SoftDeletable::class))  " AND deleted_at IS NULL" else ""

            val sql = "SELECT * FROM \"$tableName\" WHERE id = :id$softDeletableClause"
            span(sql) {
                handle.createQuery(sql)
                    .bind("id", rawId)
                    .map { rs, _ -> resultSetToTable(rs) }
                    .findOne().orElse(null)?.let { AnonymizerService.anonymize(it) }
            }
        }
    }

    fun getAll(useWriteDb: Boolean = false): List<T> {
        val jdbiTarget = if(useWriteDb) jdbi else jdbiReplica

        val softDeletableClause = if (cls.isSubclassOf(SoftDeletable::class))  " WHERE deleted_at IS NULL" else ""

        return jdbiTarget.withHandle<List<T>, DatabaseException> { handle ->
            val sql = "SELECT * FROM \"$tableName\"$softDeletableClause"
            span(sql) {
                handle.createQuery(sql)
                    .map { rs, _ -> resultSetToTable(rs) }
                    .list()
                    .let { list -> list.map { AnonymizerService.anonymize(it) } }
            }
        }
    }

    fun find(query: Query, useWriteDb: Boolean = false): List<T> {
        val queryChecked = softDeletableCheck(query)
        val sql = SqlGenerator.findSql(tableName, queryChecked)
        val fields = SqlGenerator.fields(queryChecked)
        val jdbiTarget = if(useWriteDb) jdbi else jdbiReplica

        return span(sql) {
            jdbiTarget.withHandle<List<T>, DatabaseException> {
                val q = it.createQuery(sql)
                fields.forEachIndexed(q::bind)
                q.map { rs, _ -> resultSetToTable(rs) }
                    .toList()
                    .let { list -> list.map { item -> AnonymizerService.anonymize(item) } }
            }
        }
    }

    fun count(query: Query, useWriteDb: Boolean = false): Int {
        val queryChecked = softDeletableCheck(query)
        val sql = SqlGenerator.countSql(tableName, queryChecked)
        val fields = SqlGenerator.fields(queryChecked)
        val jdbiTarget = if (useWriteDb) jdbi else jdbiReplica

        return span(sql) {
            jdbiTarget.withHandle<Int, DatabaseException> {
                val q = it.createQuery(sql)
                fields.forEachIndexed(q::bind)
                val result = q.map { rs, _ -> rs.getInt(1) }
                result.one()
            }
        }
    }

    fun countGrouped(query: Query, useWriteDb: Boolean = false): List<CountByValues> {
        val queryChecked = softDeletableCheck(query)
        val sql = SqlGenerator.countSql(tableName, queryChecked)
        val fields = SqlGenerator.fields(queryChecked)
        val jdbiTarget = if (useWriteDb) jdbi else jdbiReplica

        return span(sql) {
            jdbiTarget.withHandle<List<CountByValues>, DatabaseException> {
                val q = it.createQuery(sql)
                fields.forEachIndexed(q::bind)
                val result = q.map { rs, _ -> resultSetToCountTable(rs, queryChecked) }
                result.toList()
            }
        }
    }

    private fun resultSetToCountTable(rs: ResultSet, query: Query): CountByValues {
        val columns = query.groupBy?.map { rs.getString(it.toSnakeCase()) } ?: throw Exception("without groupBy")
        return CountByValues(columns, rs.getInt("count"))
    }

    fun exists(query: Query, useWriteDb: Boolean = false): Boolean {
        val queryChecked = softDeletableCheck(query)
        val sql = SqlGenerator.existsSql(tableName, queryChecked)
        val fields = SqlGenerator.fields(queryChecked)
        val jdbiTarget = if (useWriteDb) jdbi else jdbiReplica

        return span(sql) {
            jdbiTarget.withHandle<Boolean, DatabaseException> {
                val q = it.createQuery(sql)
                fields.forEachIndexed(q::bind)
                val result = q.map { rs, _ -> rs.getInt(1) }
                result.findOne().isPresent
            }
        }
    }

    open fun update(item: T): Result<T, DatabaseException> {
        val newItem = item.copyTable(version = item.version + 1, updatedAt = LocalDateTime.now())

        val immutable = listOf("id", "createdAt")
        val assigns = StringBuffer()
        val memberProperties = getMemberPropertiesForCreateAndUpdate()
        memberProperties.filter { !immutable.contains(it.name) }.joinTo(assigns, ", ") {
            val colVal = formatColVal(it, item)
            "${it.name.toSnakeCase()}=$colVal"
        }

        return Result.of {
            jdbi.withHandle<T, DatabaseException> { handle ->
                val anonymizationCheck = anonymizationCheck()

                val update = handle.createUpdate(
                    """
                |UPDATE "$tableName" SET $assigns
                |WHERE id=:id AND version=:currentVersion $anonymizationCheck
                |""".trimMargin()
                ).bind("currentVersion", item.version)

                cls.memberProperties.forEach { bindPropertyToUpdate(it, newItem, update) }

                try {
                    val updatedCount = span("UPDATE \"$tableName\" SET ... WHERE id=:id AND version=:currentVersion $anonymizationCheck") {
                        it.setAttributes(newItem)
                        update.execute()
                    }
                    if (updatedCount > 0) newItem
                    else throw NotFoundException("JdbiRepository.update: entity not found class=${cls.qualifiedName} id=${item.id} version=${item.version}")
                } catch (e: UnableToExecuteStatementException) {
                    logger.warn("Error on JdbiRepository.update: id=${item.id} class=${cls.qualifiedName}", e)

                    if (e.message?.contains("duplicate key") == true)
                        throw DuplicatedItemException("Duplicated: operation=update id=${item.id} class=${cls.qualifiedName}", cause = e)
                    else
                        throw DatabaseException("Database error: operation=update id=${item.id} class=${cls.qualifiedName}", cause = e)
                }
            }
        }
    }

    open fun softDelete(item: T): Boolean = jdbi.withHandle<Int, DatabaseException> { handle ->
        val rawId = when (val id = item.id) {
            is TableId -> id.id
            else -> id
        }

        val builder = StringBuilder()

        val updatedByProp = cls.memberProperties.firstOrNull{ prop -> prop.name == "updatedBy" }
        updatedByProp?.let {
            val colVal = formatColVal(it, item)
            builder.append(", ${it.name.toSnakeCase()}=$colVal")
        }

        val softDeleteQuery = "UPDATE \"$tableName\" SET deleted_at=:date${builder} WHERE id=:id"
        val update = handle.createUpdate(softDeleteQuery)
            .bind("id", rawId)
            .bind("date", LocalDateTime.now())

        updatedByProp?.let {
            bindPropertyToUpdate(it, item, update)
        }

        span(softDeleteQuery) {
            update.execute()
        }

    } > 0

    fun delete(item: T): Boolean = delete(item.id)

    internal fun truncate() = jdbi.withHandle<Int, DatabaseException> { handle ->
        val sql = "TRUNCATE \"$tableName\" CASCADE"
        span(sql) {
            handle.createUpdate(sql)
                .execute()
        }
    } > 0

    open fun delete(id: Any): Boolean = jdbi.withHandle<Int, DatabaseException> { handle ->
        val rawId = when(id) {
            is TableId -> id.id
            else -> id
        }

        val sql = "DELETE FROM \"$tableName\" WHERE id=:id"
        span(sql) {
            handle.createUpdate(sql)
                .bind("id", rawId)
                .execute()
        }

    } > 0

    @Suppress("UNCHECKED_CAST")
    fun anonymize(id: Any): Result<Boolean, DatabaseException> {
        val table: T = get(id) ?: return true.success()
        if (table is AnonymizableTable<*>) {
            if (table.anonymized) return true.success()
            return update(table.anonymize() as T).map { true }
        }
        return true.success()
    }

    private fun bindPropertyToUpdate(it: KProperty1<T, *>, newTable: T, update: Update) {
        when (val value = it.get(newTable)) {
            is Collection<*> -> update.bind(it.name, toJson(value))
            is JsonSerializable -> update.bind(it.name, toJson(value))
            is PersonPiiToken -> update.bind(it.name, value.id)
            is PersonNonPiiToken -> update.bind(it.name, value.id)
            is TsVector -> update.bind(it.name, value.value)
            is Map<*, *> -> update.bind(it.name, toJson(value))
            is JsonRaw -> update.bind(it.name, value.content)
            else -> update.bind(it.name, value)
        }
    }

    private fun resultSetToTable(rs: ResultSet): T = try {
        val cons = cls.primaryConstructor!!

        fun getParams(param: String): KParameter = cons.parameters.find { it.name == param }!!

        val args = cls.memberProperties.associate { prop ->
            val propKClass = prop.returnType.jvmErasure
            val colName = prop.name.toSnakeCase()

            getParams(prop.name) to when {
                isTypeOfJson(propKClass) ->
                    isoDateGson.fromJson(rs.getString(colName), prop.returnType.javaType)

                propKClass.isSubclassOf(UUID::class) -> {
                    val value: String? = rs.getString(colName)
                    val uuid: UUID? = value?.let { UUID.fromString(value) }
                    uuid
                }

                propKClass.isSubclassOf(PersonPiiToken::class) -> rs.getString(colName)
                    ?.let { PersonPiiToken(UUID.fromString(it)) }

                propKClass.isSubclassOf(PersonNonPiiToken::class) -> rs.getString(colName)
                    ?.let { PersonNonPiiToken(UUID.fromString(it)) }

                propKClass.isSubclassOf(LocalDateTime::class) -> rs.getObject(colName, LocalDateTime::class.java)
                propKClass.isSubclassOf(LocalDate::class) -> rs.getObject(colName, LocalDate::class.java)
                propKClass.isSubclassOf(LocalTime::class) -> rs.getObject(colName, LocalTime::class.java)
                propKClass.isSubclassOf(Int::class) -> rs.getObject(colName) as Int?
                propKClass.isSubclassOf(Short::class) -> (rs.getObject(colName) as Int?)?.toShort()
                propKClass.isSubclassOf(Long::class) -> rs.getObject(colName) as Long?
                propKClass.isSubclassOf(Boolean::class) -> rs.getObject(colName) as Boolean?
                propKClass.isSubclassOf(Double::class) -> rs.getObject(colName) as Double?
                propKClass.isSubclassOf(Float::class) -> (rs.getObject(colName) as Double?)?.toFloat()
                propKClass.isSubclassOf(BigDecimal::class) -> rs.getBigDecimal(colName)
                propKClass.isSubclassOf(Enum::class) -> {
                    val name: String? = rs.getString(colName)
                    name?.let { propKClass.java.enumConstants.first { enum -> (enum as Enum<*>).name == it } }
                }

                propKClass.isSubclassOf(TsVector::class) -> rs.getString(colName)?.let { TsVector(it) }
                propKClass.isSubclassOf(JsonRaw::class) -> rs.getString(colName)?.let { JsonRaw(it) }
                else -> rs.getString(colName)
            }
        }

        cons.callBy(args)
    } catch (e: Exception) {
        logger.error("Error parsing ResultSet: ${format(rs)}", e)
        throw e
    }

    private fun softDeletableCheck(query: Query): Query =
        if (!(cls.isSubclassOf(SoftDeletable::class))) query
        else query.copy(where = softDeletePredicate(query.where))

    private fun softDeletePredicate(where: Predicate?) =
        if (where != null) Predicate.isNull(Field.DateTimeField("deletedAt")).and(scope(where))
        else Predicate.isNull(Field.DateTimeField("deletedAt"))

    private fun format(rs: ResultSet): String {
        val metaData = rs.metaData
        val colsValue = mutableListOf<String>()
        for (i in 1..metaData.columnCount) {
            val columnValue = rs.getString(i)
            colsValue.add("${metaData.getColumnName(i)} -> $columnValue")
        }
        return "[${colsValue.joinToString(", ")}]"
    }

    private fun formatColVal(prop: KProperty1<T, *>, item: T): String {
        val kClass = prop.returnType.jvmErasure
        return when {
            isTypeOfJson(kClass) || kClass.isSubclassOf(JsonRaw::class)-> {
                ":${prop.name}::jsonb"
            }
            isGeography(kClass, prop.name) -> {
                ":${prop.name}::geography"
            }
            kClass.isSubclassOf(TsVector::class) -> {
                val tsVector = prop.get(item) as TsVector
                "setweight(to_tsvector('portuguese', :${prop.name}), '${tsVector.weight.name}')"
            }
            else -> {
                ":${prop.name}"
            }
        }
    }

    private fun isGeography(kClass: KClass<*>, name: String) =
        kClass.isSubclassOf(Geography::class)
                && name.toSnakeCase() == "geo_location"

    private fun isTypeOfJson(kClass: KClass<*>) =
        kClass.isSubclassOf(Collection::class)
                || kClass.isSubclassOf(JsonSerializable::class)
                || kClass.isSubclassOf(Map::class)

    private companion object {
        private fun toJson(data: Any): String = gsonRepository.toJson(data)
    }

    private fun anonymizationCheck(): String =
        if (cls is Anonymizable) "AND NOT ${Anonymizable::anonymized.name}"
        else ""

    private fun <T> span(spanName: String, block: (Span) -> T): T =
        Tracer.spanSync(spanName, SpanKind.CLIENT) { span ->
            span.setAttribute("service_type", "database")
            val result = block(span)

            if (result is Collection<*>) span.setAttribute("lines_count", result.count().toString())

            result
        }

    private fun Span.setAttributes(item: T) {
        this.setAttribute("item_id", item.id.toString())
        this.setAttribute("item_version", item.version.toString())
    }

}
