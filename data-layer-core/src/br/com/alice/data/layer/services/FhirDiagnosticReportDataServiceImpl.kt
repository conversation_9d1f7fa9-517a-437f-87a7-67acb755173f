package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FhirDiagnosticReport
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FhirDiagnosticReportTable

class FhirDiagnosticReportDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FhirDiagnosticReport>(factory.get(FhirDiagnosticReport::class, FhirDiagnosticReportTable::class)),
    FhirDiagnosticReportDataService
