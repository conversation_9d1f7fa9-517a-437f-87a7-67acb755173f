package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FaqFeedbackModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FaqFeedbackTable

class FaqFeedbackModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FaqFeedbackModel>(factory.get(FaqFeedbackModel::class, FaqFeedbackTable::class)),
    FaqFeedbackModelDataService
