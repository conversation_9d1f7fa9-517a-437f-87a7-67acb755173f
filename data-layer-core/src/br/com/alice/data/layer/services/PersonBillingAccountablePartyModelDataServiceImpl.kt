package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonBillingAccountablePartyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonBillingAccountablePartyTable

class PersonBillingAccountablePartyModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonBillingAccountablePartyModel>(factory.get(PersonBillingAccountablePartyModel::class, PersonBillingAccountablePartyTable::class)),
    PersonBillingAccountablePartyModelDataService
