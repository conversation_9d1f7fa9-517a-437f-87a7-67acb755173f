package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlanTaskReferrals
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTaskReferralsTable

class HealthPlanTaskReferralsDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlanTaskReferrals>(factory.get(HealthPlanTaskReferrals::class, HealthPlanTaskReferralsTable::class)),
    HealthPlanTaskReferralsDataService
