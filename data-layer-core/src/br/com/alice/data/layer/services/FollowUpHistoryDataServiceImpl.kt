package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FollowUpHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FollowUpHistoryTable

class FollowUpHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FollowUpHistory>(factory.get(FollowUpHistory::class, FollowUpHistoryTable::class)),
    FollowUpHistoryDataService
