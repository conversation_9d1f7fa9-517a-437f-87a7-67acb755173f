package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AITextInference
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AiTextInferenceTable

class AITextInferenceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AITextInference>(factory.get(AITextInference::class, AiTextInferenceTable::class)),
    AITextInferenceDataService
