package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TussProcedureSpecialtyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TussProcedureSpecialtyTable

class TussProcedureSpecialtyDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TussProcedureSpecialtyModel>(factory.get(TussProcedureSpecialtyModel::class, TussProcedureSpecialtyTable::class)),
    TussProcedureSpecialtyModelDataService
