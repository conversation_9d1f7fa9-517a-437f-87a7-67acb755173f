package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EitaNullvsIntegrationRecordModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EitaNullvsIntegrationRecordTable

class EitaNullvsIntegrationRecordModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EitaNullvsIntegrationRecordModel>(factory.get(EitaNullvsIntegrationRecordModel::class, EitaNullvsIntegrationRecordTable::class)),
    EitaNullvsIntegrationRecordModelDataService
