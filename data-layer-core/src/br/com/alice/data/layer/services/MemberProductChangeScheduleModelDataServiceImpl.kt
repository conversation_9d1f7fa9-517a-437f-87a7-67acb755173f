package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberProductChangeScheduleModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberProductChangeScheduleTable

class MemberProductChangeScheduleModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberProductChangeScheduleModel>(factory.get(MemberProductChangeScheduleModel::class, MemberProductChangeScheduleTable::class)),
    MemberProductChangeScheduleModelDataService
