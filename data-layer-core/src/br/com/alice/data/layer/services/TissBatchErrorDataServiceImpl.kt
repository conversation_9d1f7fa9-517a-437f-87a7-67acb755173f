package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TissBatchErrorModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TissBatchErrorTable

class TissBatchErrorDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TissBatchErrorModel>(factory.get(TissBatchErrorModel::class, TissBatchErrorTable::class)),
    TissBatchErrorModelDataService
