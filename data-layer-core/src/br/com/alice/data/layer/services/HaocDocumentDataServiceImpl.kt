package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocDocument
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocDocumentTable

class HaocDocumentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocDocument>(factory.get(HaocDocument::class, HaocDocumentTable::class)),
    HaocDocumentDataService
