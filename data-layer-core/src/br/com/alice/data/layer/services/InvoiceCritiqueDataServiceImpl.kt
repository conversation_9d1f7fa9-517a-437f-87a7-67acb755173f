package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoiceCritiqueModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoiceCritiqueTable

class InvoiceCritiqueDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoiceCritiqueModel>(factory.get(InvoiceCritiqueModel::class, InvoiceCritiqueTable::class)),
    InvoiceCritiqueModelDataService
