package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProcedureProviderModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProcedureProviderTable

class ProcedureProviderDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProcedureProviderModel>(factory.get(ProcedureProviderModel::class, ProcedureProviderTable::class)),
    ProcedureProviderModelDataService
