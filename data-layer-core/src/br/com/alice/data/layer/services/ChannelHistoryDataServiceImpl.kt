package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelHistoryTable

class ChannelHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelHistory>(factory.get(ChannelHistory::class, ChannelHistoryTable::class)),
    ChannelHistoryDataService
