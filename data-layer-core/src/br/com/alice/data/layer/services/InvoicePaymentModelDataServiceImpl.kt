package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoicePaymentModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoicePaymentTable

class InvoicePaymentModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoicePaymentModel>(factory.get(InvoicePaymentModel::class, InvoicePaymentTable::class)),
    InvoicePaymentModelDataService
