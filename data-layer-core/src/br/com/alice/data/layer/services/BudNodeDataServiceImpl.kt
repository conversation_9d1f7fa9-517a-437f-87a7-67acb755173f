package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptNodeTable

class BudNodeDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BudNode>(factory.get(BudNode::class, ServiceScriptNodeTable::class)),
    BudNodeDataService
