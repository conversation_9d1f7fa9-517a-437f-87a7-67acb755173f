package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberHealthMetricModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberHealthMetricTable

class MemberHealthMetricModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberHealthMetricModel>(factory.get(MemberHealthMetricModel::class, MemberHealthMetricTable::class)),
    MemberHealthMetricModelDataService
