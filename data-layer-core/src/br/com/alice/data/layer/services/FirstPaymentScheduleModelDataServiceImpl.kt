package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FirstPaymentScheduleModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FirstPaymentScheduleTable

class FirstPaymentScheduleModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FirstPaymentScheduleModel>(factory.get(FirstPaymentScheduleModel::class, FirstPaymentScheduleTable::class)),
    FirstPaymentScheduleModelDataService
