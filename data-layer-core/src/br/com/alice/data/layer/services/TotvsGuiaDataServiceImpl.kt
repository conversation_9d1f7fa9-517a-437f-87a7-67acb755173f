package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TotvsGuiaModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TotvsGuiaTable

class TotvsGuiaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TotvsGuiaModel>(factory.get(TotvsGuiaModel::class, TotvsGuiaTable::class)),
    TotvsGuiaModelDataService
