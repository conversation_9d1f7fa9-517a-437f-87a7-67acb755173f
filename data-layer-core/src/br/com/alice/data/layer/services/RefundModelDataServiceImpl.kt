package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RefundModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RefundTable

class RefundModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RefundModel>(factory.get(RefundModel::class, RefundTable::class)),
    RefundModelDataService
