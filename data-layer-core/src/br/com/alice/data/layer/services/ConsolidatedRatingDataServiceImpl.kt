package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ConsolidatedRating
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ConsolidatedRatingTable

class ConsolidatedRatingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ConsolidatedRating>(factory.get(ConsolidatedRating::class, ConsolidatedRatingTable::class)),
    ConsolidatedRatingDataService
