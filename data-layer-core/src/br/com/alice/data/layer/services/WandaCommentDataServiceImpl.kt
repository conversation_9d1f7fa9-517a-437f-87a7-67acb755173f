package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.WandaComment
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.WandaCommentTable

class WandaCommentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<WandaComment>(factory.get(WandaComment::class, WandaCommentTable::class)),
    WandaCommentDataService
