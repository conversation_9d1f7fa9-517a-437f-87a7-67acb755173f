package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ExternalCalendarRecurrentEventModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ExternalCalendarRecurrentEventTable

class ExternalCalendarRecurrentEventModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ExternalCalendarRecurrentEventModel>(factory.get(ExternalCalendarRecurrentEventModel::class, ExternalCalendarRecurrentEventTable::class)),
    ExternalCalendarRecurrentEventModelDataService
