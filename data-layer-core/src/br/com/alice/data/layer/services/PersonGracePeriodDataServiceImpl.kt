package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonGracePeriod
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonGracePeriodTable

class PersonGracePeriodDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonGracePeriod>(factory.get(PersonGracePeriod::class, PersonGracePeriodTable::class)),
    PersonGracePeriodDataService
