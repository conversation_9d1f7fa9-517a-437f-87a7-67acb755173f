package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FaqContentModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FaqContentTable

class FaqContentModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FaqContentModel>(factory.get(FaqContentModel::class, FaqContentTable::class)),
    FaqContentModelDataService
