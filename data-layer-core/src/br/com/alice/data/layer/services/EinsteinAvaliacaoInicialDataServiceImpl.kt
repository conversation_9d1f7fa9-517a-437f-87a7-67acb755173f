package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinAvaliacaoInicial
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinAvaliacaoInicialTable


class EinsteinAvaliacaoInicialDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinAvaliacaoInicial>(factory.get(EinsteinAvaliacaoInicial::class, EinsteinAvaliacaoInicialTable::class)),
    EinsteinAvaliacaoInicialDataService
