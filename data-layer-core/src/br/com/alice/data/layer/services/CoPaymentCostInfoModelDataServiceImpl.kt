package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CoPaymentCostInfoModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CoPaymentCostInfoTable

class CoPaymentCostInfoModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CoPaymentCostInfoModel>(
        factory.get(CoPaymentCostInfoModel::class, CoPaymentCostInfoTable::class)
    ),
    CoPaymentCostInfoModelDataService
