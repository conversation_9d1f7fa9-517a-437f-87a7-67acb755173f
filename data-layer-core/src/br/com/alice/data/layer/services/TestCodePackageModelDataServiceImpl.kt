package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TestCodePackageModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TestCodePackageTable

class TestCodePackageModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TestCodePackageModel>(factory.get(TestCodePackageModel::class, TestCodePackageTable::class)),
    TestCodePackageModelDataService
