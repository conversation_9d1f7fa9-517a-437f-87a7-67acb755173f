package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonDefaulterModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonDefaulterTable

class PersonDefaulterModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonDefaulterModel>(factory.get(PersonDefaulterModel::class, PersonDefaulterTable::class)),
    PersonDefaulterModelDataService

