package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoiceModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoiceTable

class InvoiceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoiceModel>(factory.get(InvoiceModel::class, InvoiceTable::class)),
    InvoiceModelDataService
