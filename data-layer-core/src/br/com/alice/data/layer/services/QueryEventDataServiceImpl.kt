package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.QueryEvent
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.QueryEventTable

class QueryEventDataServiceImpl internal constructor(factory: DatabasePipelineFactory):
    BaseDataServiceImpl<QueryEvent>(factory.get(QueryEvent::class, QueryEventTable::class)),
    QueryEventDataService
