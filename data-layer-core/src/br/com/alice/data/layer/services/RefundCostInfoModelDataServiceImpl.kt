package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RefundCostInfoModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RefundCostInfoTable

class RefundCostInfoModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RefundCostInfoModel>(
        factory.get(RefundCostInfoModel::class, RefundCostInfoTable::class)
    ),
    RefundCostInfoModelDataService
