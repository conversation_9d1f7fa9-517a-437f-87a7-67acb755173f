package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinAlergia
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinAlergiaTable


class EinsteinAlergiaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinAlergia>(factory.get(EinsteinAlergia::class, EinsteinAlergiaTable::class)),
    EinsteinAlergiaDataService
