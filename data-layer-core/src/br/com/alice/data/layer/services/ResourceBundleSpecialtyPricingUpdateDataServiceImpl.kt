package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ResourceBundleSpecialtyPricingUpdateTable

class ResourceBundleSpecialtyPricingUpdateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ResourceBundleSpecialtyPricingUpdateModel>(factory.get(ResourceBundleSpecialtyPricingUpdateModel::class, ResourceBundleSpecialtyPricingUpdateTable::class)),
    ResourceBundleSpecialtyPricingUpdateModelDataService
