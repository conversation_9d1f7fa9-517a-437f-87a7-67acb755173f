package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyContractMacoModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyContractMacoTable

class CompanyContractMacoModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyContractMacoModel>(factory.get(CompanyContractMacoModel::class, CompanyContractMacoTable::class)),
    CompanyContractMacoModelDataService
