package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareResourceGroupModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareResourceGroupTable

class HealthcareResourceGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareResourceGroupModel>(factory.get(HealthcareResourceGroupModel::class, HealthcareResourceGroupTable::class)),
    HealthcareResourceGroupModelDataService
