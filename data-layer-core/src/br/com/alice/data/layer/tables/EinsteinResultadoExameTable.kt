package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Exame
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class EinsteinResultadoExameTable(
    override val personId: PersonNonPiiToken,
    val passagem: String,
    val status: String,
    val dataPassagem: String,
    val tipoPassagem: String,
    val localPassagem: String,
    val ocorrencias: String,
    val exames: List<Exame> = emptyList(),
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<EinsteinResultadoExameTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
