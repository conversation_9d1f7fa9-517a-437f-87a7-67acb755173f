package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.EitaNullvsExternalModelType
import br.com.alice.data.layer.models.EitaNullvsInternalModelType
import br.com.alice.data.layer.models.LogStatus
import java.time.LocalDateTime
import java.util.UUID

internal data class EitaNullvsIntegrationLogTable(
    override val id: UUID = RangeUUID.generate(),
    val eventId: UUID,
    val eventName: String,
    val integrationEventName: String,
    val internalId: UUID,
    val internalModelName: EitaNullvsInternalModelType,
    val externalModelName: EitaNullvsExternalModelType,
    val batchId: String,
    val idSoc: String,
    val batchType: BatchType,
    val payloadSequenceId: Int,
    val description: String?,
    val status: LogStatus = LogStatus.PENDING,
    val hash: String? = null,
    val groupId: UUID? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<EitaNullvsIntegrationLogTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
