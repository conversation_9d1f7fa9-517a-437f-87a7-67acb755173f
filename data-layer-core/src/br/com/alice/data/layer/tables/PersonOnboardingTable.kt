package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonOnboardingTable(
    override val personId: PersonPiiToken,
    val currentPhase: OnboardingPhase,
    val finishedAt: LocalDateTime? = null,
    val slaHealthDeclarationAppointmentCompleted: Long? = null,
    val slaContractGenerated: Long? = null,
    val healthDeclarationAppointmentScheduledAt: LocalDateTime? = null,
    val healthDeclarationAppointmentCompletedAt: LocalDateTime? = null,
    val healthDeclarationAppointmentDate: LocalDateTime? = null,
    val legalGuardianResponsibilityTermSentAt: LocalDateTime? = null,
    val contractAvailableAt: LocalDateTime? = null,
    val activeUntil: LocalDateTime? = null,
    val archived: Boolean? = false,
    val ignoreExpiration: Boolean? = false,
    val expiresAt: LocalDateTime? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonOnboardingTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}

