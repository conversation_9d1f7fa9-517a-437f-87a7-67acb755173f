package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ExpireReason
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonLoginTable(
    val nationalId: String,
    val email: String? = null,
    override val personId: PersonPiiToken,
    val accessCode: String,
    val saltAccessCode: String,
    val expirationDate: LocalDateTime,
    val expiredAt: LocalDateTime? = null,
    val expireReason: ExpireReason? = null,
    val firstAccess: Boolean = false,
    val attempts: Int? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonLoginTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
