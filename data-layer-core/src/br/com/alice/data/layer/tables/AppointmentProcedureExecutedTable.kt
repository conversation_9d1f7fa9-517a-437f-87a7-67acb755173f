package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import br.com.alice.data.layer.models.UpdatedByReference
import java.time.LocalDateTime
import java.util.UUID

internal data class AppointmentProcedureExecutedTable(
    val appointmentId: UUID,
    val status: ProcedureExecutedStatus,
    val tussCode: String,
    val tussProcedureAliceCode: String? = null,
    val healthSpecialistResourceBundleCode: String? = null,
    val isPriced: Boolean = false,

    override var updatedBy: UpdatedBy? = null,
    override val deletedAt: LocalDateTime? = null,

    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val version: Int = 0,
) : Table<AppointmentProcedureExecutedTable>, SoftDeletable, UpdatedByReference {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime,
    ) = copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
