package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Accommodation
import br.com.alice.data.layer.models.Coverage
import br.com.alice.data.layer.models.ProductCategory
import br.com.alice.data.layer.models.ProductOptionType
import br.com.alice.data.layer.models.ProviderRepresentation
import java.time.LocalDateTime
import java.util.UUID

internal data class VicProductOptionTable(
    val productId: UUID,
    val productName: String,
    val productCategory: ProductCategory,
    val accommodation: Accommodation,
    val coverage: Coverage,
    val mainHospitals: List<ProviderRepresentation>,
    val mainLaboratories: List<ProviderRepresentation>,
    val siteAccreditedNetworkId: UUID,
    val coPayment: Boolean = false,
    val type: ProductOptionType? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<VicProductOptionTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
