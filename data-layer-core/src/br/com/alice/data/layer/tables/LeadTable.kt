package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.LeadSource
import br.com.alice.data.layer.models.TrackingInfo
import br.com.alice.data.layer.models.ZipcodeAddressLight
import java.time.LocalDateTime
import java.util.UUID

internal data class LeadTable(
    val nationalId: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val email: String,
    val postalCode: String,
    val cnsNumber: String? = null,
    val dateOfBirth: LocalDateTime? = null,
    val declaredAge: Int? = null,
    val invitedAt: LocalDateTime? = null,
    val phoneNumber: String? = null,
    val trackingInfo: TrackingInfo? = null,
    val authorizeCommunication: Boolean? = null,
    val promoCodeId: UUID? = null,
    val productId: UUID? = null,
    val nickName: String? = null,
    val source: LeadSource? = null,
    val sourceId: UUID? = null,
    val zipcodeAddress: ZipcodeAddressLight? = null,
    val simulationHistory: List<UUID> = emptyList(),
    val ongoingCompanyDealId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false
) : Table<LeadTable>, Anonymizable {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
