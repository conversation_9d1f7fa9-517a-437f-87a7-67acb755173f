package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

data class NetworkOwnerTable(
    val name: String,
    val cnpj: String,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
): Table<NetworkOwnerTable> {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) = copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
