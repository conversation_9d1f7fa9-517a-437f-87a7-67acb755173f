package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.MemberProductChangeScheduleStatus
import br.com.alice.data.layer.models.RequestedByModel
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class MemberProductChangeScheduleTable(
    override val id: UUID = RangeUUID.generate(),
    val memberId: UUID,
    override val personId: PersonPiiToken,
    val productId: UUID,
    val applyAt: LocalDateTime,
    val appliedAt: LocalDateTime? = null,
    val status: MemberProductChangeScheduleStatus = MemberProductChangeScheduleStatus.REQUESTED,
    val requestedBy: RequestedByModel? = null,

    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<MemberProductChangeScheduleTable>, PersonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
