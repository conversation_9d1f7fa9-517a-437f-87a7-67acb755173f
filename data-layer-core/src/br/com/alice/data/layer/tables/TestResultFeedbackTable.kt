package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class TestResultFeedbackTable(
    val content: String,
    val addedAt: LocalDateTime = LocalDateTime.now(),
    val aliceTestResultBundleIds: List<UUID> = emptyList(),
    val healthPlanTaskIds: List<UUID> = emptyList(),
    val staffId: UUID,
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonNonPiiToken,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<TestResultFeedbackTable>, PersonNonPiiReference, DeIdentifiedReference{

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
