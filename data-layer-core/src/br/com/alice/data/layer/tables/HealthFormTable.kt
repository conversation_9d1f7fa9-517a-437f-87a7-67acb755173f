package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.FormType
import br.com.alice.common.UpdatedBy
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthFormTable(
    val name: String,
    val key: String,
    val type: FormType,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<HealthFormTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
