package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class ServiceScriptNavigationGroupTable(
    override val personId: PersonNonPiiToken,
    val scriptNodeId: UUID,
    val startedAt: LocalDateTime = LocalDateTime.now(),
    val finishedAt: LocalDateTime? = null,
    val source: ServiceScriptNavigationSource? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ServiceScriptNavigationGroupTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
