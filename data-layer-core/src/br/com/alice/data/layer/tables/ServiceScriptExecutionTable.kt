package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ScriptAction
import java.time.LocalDateTime
import java.util.UUID

internal data class ServiceScriptExecutionTable(
    override val id: UUID = RangeUUID.generate(),
    val triggerId: String,
    val triggerType: String,
    val originalConditions: List<Condition>,
    val triggerConditions: Map<String, Any>,
    val originalActions: List<ScriptAction>,
    val executedActions: List<ScriptAction>,
    val triggeredByStaffId: UUID? = null,
    val triggeredAt: LocalDateTime = LocalDateTime.now(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ServiceScriptExecutionTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
