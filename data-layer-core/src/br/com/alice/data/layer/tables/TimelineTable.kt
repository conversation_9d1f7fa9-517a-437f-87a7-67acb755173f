package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineAppendage
import br.com.alice.data.layer.models.TimelineEvolution
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineStatus
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

data class TimelineTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonNonPiiToken,
    val staffId: UUID?,
    val providerUnitId: UUID? = null,
    val title: String?,
    val description: String,
    val type: TimelineType,
    val status: TimelineStatus? = null,
    val draftGroup: List<UUID> = emptyList(),
    val channelIds: List<String> = emptyList(),
    val specialtyId: UUID? = null,
    val evolutions: List<TimelineEvolution>,
    val referencedLinks: List<Timeline.ReferencedLink> = emptyList(),
    val referencedModelId: UUID,
    val referencedModelDate: LocalDateTime = LocalDateTime.now(),
    val referencedModelClass: TimelineReferenceModel,
    val hasSpecialistOpinion: Boolean? = false,
    val appendages: List<TimelineAppendage> = emptyList(),
    val aiSummary: String? = null,
    override val version: Int,
    override val createdAt: LocalDateTime,
    override val updatedAt: LocalDateTime,
) : Table<TimelineTable>, PersonNonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
