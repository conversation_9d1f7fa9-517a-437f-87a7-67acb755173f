package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.HealthFormQuestionDisplayType
import br.com.alice.data.layer.models.HealthFormQuestionInputConfigurationType
import br.com.alice.data.layer.models.HealthFormQuestionOption
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.HealthFormQuestionValidationType
import br.com.alice.common.UpdatedBy
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthFormQuestionTable(
    val healthFormId: UUID,
    val healthFormSectionId: UUID,
    val index: Int,
    val question: String,
    val summaryQuestion: String? = null,
    val details: String? = null,
    val defaultNext: Int? = null,
    val displayAttributes: Map<HealthFormQuestionDisplayType, Any>? = null,
    val imageUrl: String? = null,
    val type: HealthFormQuestionType,
    val validations: Map<HealthFormQuestionValidationType, Any>? = null,
    val inputConfigurations: Map<HealthFormQuestionInputConfigurationType, Any>? = null,
    val options: List<HealthFormQuestionOption> = emptyList(),
    val active: Boolean = false,
    val required: Boolean = true,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<HealthFormQuestionTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
