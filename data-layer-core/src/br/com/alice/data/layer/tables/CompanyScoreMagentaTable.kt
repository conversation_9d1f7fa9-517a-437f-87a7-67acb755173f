package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.DescriptionByKeyList
import java.time.LocalDateTime
import java.util.UUID

data class CompanyScoreMagentaTable(
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val referenceStartDate: LocalDateTime,
    val referenceEndDate: LocalDateTime,
    val generalScore: Float,
    val eatingScore: Float,
    val sleepScore: Float,
    val habitsScore: Float,
    val lifeQualityScore: Float,
    val physicalActivityScore: Float,
    val mentalHealthScore: Float,
    val adhesion: Float,
    val answersQuantity: Int,
    val descriptionByKey: DescriptionByKeyList,

    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<CompanyScoreMagentaTable> {
    override fun copyTable(
        version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime
    ) = copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
