package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.AdditionalParameters
import br.com.alice.common.Disease
import br.com.alice.data.layer.models.SpecialistOpinionStatus
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class SpecialistOpinionTable(
    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val version: Int = 0,
    val appointmentId: UUID,
    val staffId: UUID,
    val status: SpecialistOpinionStatus = SpecialistOpinionStatus.REQUESTED,
    val medicalSpecialtyId: UUID,
    val assignedStaffId: UUID? = null,
    val assignedAt: LocalDateTime? = null,
    val caseSummary: String,
    val question: String,
    val demand: Disease? = null,
    val files: List<UUID>,
    val additionalParameters: AdditionalParameters? = null,
    override val personId: PersonNonPiiToken,
) : Table<SpecialistOpinionTable>, PersonNonPiiReference, DeIdentifiedReference {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) = copy(
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
