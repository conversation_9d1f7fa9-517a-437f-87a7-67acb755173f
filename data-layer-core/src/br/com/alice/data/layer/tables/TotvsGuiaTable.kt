package br.com.alice.data.layer.tables

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.ProfessionalIdentificationModel
import br.com.alice.data.layer.models.TotvsGuiaOrigin
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.data.layer.models.TotvsGuiaType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class TotvsGuiaTable(
    override val id: UUID = RangeUUID.generate(),
    val code: String,
    val externalCode: String? = null,
    val origin: TotvsGuiaOrigin,
    val requestedAt: LocalDate,
    // requestDateTime will replace requestedAt, which is obsolete
    val requestDateTime: LocalDateTime? = null,
    override val personId: PersonNonPiiToken,
    val type: MvUtil.TISS,
    val totvsType: TotvsGuiaType = TotvsGuiaType.UNKNOWN,
    val requestedByProfessional: ProfessionalIdentificationModel = ProfessionalIdentificationModel.DEFAULT_PROFESSIONAL_IDENTIFICATION,
    val status: TotvsGuiaStatus,
    val passcode: String? = null,
    val medicalRequestFileIds: List<UUID> = emptyList(),
    val executedOnCreation: Boolean? = null,
    val referenceTotvsGuiaId: UUID? = null,
    val newBorn: Boolean = false,
    val cnpj: String? = null,
    val providerUnitId: UUID? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<TotvsGuiaTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
