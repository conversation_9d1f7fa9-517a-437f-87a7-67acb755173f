package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.InsurancePortabilityMissingDocumentFileModel
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswerModel
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswerV2Model
import br.com.alice.data.layer.models.InsurancePortabilityRequestApprovedPackage
import br.com.alice.data.layer.models.InsurancePortabilityRequestCurrentTermPeriod
import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.data.layer.models.InsurancePortabilityRequestStep
import br.com.alice.data.layer.models.InsurancePortabilityRequestType
import br.com.alice.data.layer.models.InsurancePortabilitySuggestedAction
import br.com.alice.data.layer.services.PersonPiiToken
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class InsurancePortabilityRequestTable(
    override val personId: PersonPiiToken,
    val healthInsuranceId: UUID? = null,
    val type: InsurancePortabilityRequestType? = InsurancePortabilityRequestType.NORMAL,
    val step: InsurancePortabilityRequestStep? = null,
    val declinedReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val secondPortabilityRequirement: Boolean? = null,
    val currentTermPeriod: InsurancePortabilityRequestCurrentTermPeriod? = null,
    val minimumTermRequirement: Boolean? = null,
    val minimumTermRequirementEntryDate: LocalDate? = null,
    val priceCompatibilityRequirement: Boolean? = null,
    val priceCompatibilityRequirementPrice: BigDecimal? = null,
    val paymentFulfillmentRequirement: Boolean? = null,
    val hasCpt: Boolean? = null,
    val hasFulfilledCpt: Boolean? = null,
    val ansProtocolCode: String? = null,
    val activePlanRequirement: Boolean? = null,
    val hospitalCompatibility: Boolean? = null,
    val hospitalCompatibilityAnsCode: String? = null,
    val healthInsuranceCode: String? = null,
    val suggestedAction: InsurancePortabilitySuggestedAction? = null,
    val suggestedDeclineReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val approvedPackage: InsurancePortabilityRequestApprovedPackage? = null,
    val answers: List<InsurancePortabilityRequestAnswerModel>,
    val answersV2: List<InsurancePortabilityRequestAnswerV2Model>,
    val status: InsurancePortabilityRequestStatus,
    val declineReason: InsurancePortabilityRequestDeclineReason? = null,
    val missingDocuments: List<InsurancePortabilityMissingDocumentFileModel>? = emptyList(),
    val notes: String? = null,
    val productId: UUID? = null,
    val archived: Boolean = false,
    val pendingAt: LocalDateTime? = null,
    val finishedAt: LocalDateTime? = null,
    val adhesionContract: Boolean = false,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<InsurancePortabilityRequestTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
