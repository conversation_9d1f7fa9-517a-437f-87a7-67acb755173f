package br.com.alice.data.layer.tables

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.ContractModel
import br.com.alice.data.layer.models.MemberBeneficiaryModel
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatusHistoryEntryModel
import br.com.alice.data.layer.models.MvIntegrationInformationModel
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class MemberTable(
    override val personId: PersonPiiToken,
    val contract: ContractModel,
    val activationDate: LocalDateTime? = null,
    val canceledAt: LocalDateTime? = null,
    val archived: Boolean = false,
    val status: MemberStatus,
    val statusHistory: List<MemberStatusHistoryEntryModel>? = null,
    val selectedProduct: MemberProduct,
    val mvInfo: MvIntegrationInformationModel? = null,
    val brand: Brand? = Brand.ALICE,
    val migrationDate: LocalDateTime? = null,
    val externalBrandId: String? = null,
    val externalBrandAccountNumber: String? = null,
    val parentMember: UUID? = null,
    val parentPerson: PersonPiiToken? = null,
    val beneficiaryId: UUID? = null,
    val companyId: UUID? = null,
    val companySubContractId: UUID? = null,
    val beneficiary: MemberBeneficiaryModel? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<MemberTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}

