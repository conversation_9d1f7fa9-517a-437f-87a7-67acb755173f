package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

internal data class SalesFirmTable(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<SalesFirmTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): SalesFirmTable =
        copy(version = version, updatedAt = updatedAt, createdAt = createdAt)
}
