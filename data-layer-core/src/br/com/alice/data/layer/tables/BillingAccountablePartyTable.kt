package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.BillingAccountablePartyTypeModel
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.AddressModel
import java.time.LocalDateTime
import java.util.UUID

internal data class BillingAccountablePartyTable(
    val firstName: String,
    val lastName: String,
    val type: BillingAccountablePartyTypeModel,
    val nationalId: String,
    val email: String,
    val invoiceEmail: String? = null,
    val address: AddressModel? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false,
    val updatedBy: UpdatedBy? = null
) : Table<BillingAccountablePartyTable>, Anonymizable {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ): BillingAccountablePartyTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
