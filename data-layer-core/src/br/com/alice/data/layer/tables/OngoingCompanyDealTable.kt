package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.onlyAlphanumeric
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.OngoingCompanyDealDetails
import br.com.alice.data.layer.models.OngoingDealStatusHistory
import java.time.LocalDateTime
import java.util.UUID

internal data class OngoingCompanyDealTable(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val salesFirmId: UUID,
    val cnpj: String,
    val salesAgentDocument: String?,
    val salesAgentId: UUID?,
    val status: DealStage,
    val legalName: String?,
    val dealDetails: OngoingCompanyDealDetails,
    val sourceCreatedAt: LocalDateTime = LocalDateTime.now(),
    val sourceUpdatedAt: LocalDateTime = LocalDateTime.now(),
    val sourceId: String,
    val channel: DealChannel,
    var searchTokens: TsVector? = null,
    val statusHistory: List<OngoingDealStatusHistory> = emptyList(),
    val deletedAt: LocalDateTime? = null,
    val companyId: UUID? = null,
    val portabilityType: String? = null,
    val portabilityResponse: String? = null,
    val portabilityDeclinedProceed: String? = null,
    val salesFirmAgentPartnershipId: UUID? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<OngoingCompanyDealTable> {
    init {
        val doc = salesAgentDocument?.onlyAlphanumeric() ?: ""
        val cnpjNumbers = cnpj.onlyAlphanumeric()
        searchTokens = TsVector("$name $cnpj $cnpjNumbers $doc".unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): OngoingCompanyDealTable =
        copy(version = version, updatedAt = updatedAt, createdAt = createdAt)
}
