package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.PartnerIntegrationProviderType
import java.time.LocalDateTime
import java.util.UUID

data class PartnerIntegrationProviderUnitTable(
    val providerType: PartnerIntegrationProviderType,
    val providerUnitId: UUID,
    val externalId: String,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PartnerIntegrationProviderUnitTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
