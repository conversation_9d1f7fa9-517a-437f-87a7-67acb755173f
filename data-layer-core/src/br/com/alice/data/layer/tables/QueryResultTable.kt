package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.PIIField
import br.com.alice.data.layer.models.QueryStatus
import java.time.LocalDateTime
import java.util.UUID

data class QueryResultTable(
    val requestId: UUID,
    val requesterId: UUID,
    val description: String,
    val query: String,
    val piiFields: List<PIIField>,
    val status: QueryStatus,
    val fileId: String? = null,
    var errorMessage: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
): Table<QueryResultTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
