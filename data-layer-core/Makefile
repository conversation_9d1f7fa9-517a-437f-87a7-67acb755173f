db_create:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	cd ../; make db_create

db_reset:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	cd ../; make db_reset

db_seed:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	cd ../; make db_seed

clean:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	../make clean

tests:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	cd ../; make db_tests

run:
	@echo -e "\033[33mDEPRECATED - Run make from root project\033[0m"
	cd ../; make db_run
