CREATE TABLE IF NOT EXISTS protocol_tracking(
    type TEXT NOT NULL,
    external_id UUID NOT NULL,
    service_script_navigation_id UUID NOT NULL,
    service_script_action_id UUID NOT NULL,
    id UUID PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIGN KEY (service_script_navigation_id) REFERENCES service_script_navigation(id),
    FOREIGN KEY (service_script_action_id) REFERENCES service_script_action(id)
);
CREATE INDEX IF NOT EXISTS protocol_tracking_service_script_navigation_idx ON protocol_tracking(service_script_navigation_id);
CREATE INDEX IF NOT EXISTS protocol_tracking_service_script_action_idx ON protocol_tracking(service_script_action_id);
