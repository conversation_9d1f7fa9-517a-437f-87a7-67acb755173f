CREATE TABLE IF NOT EXISTS screen_detail (
    "id" UUID PRIMARY KEY,
    "person_id" UUID NOT NULL,
    "screen_type" TEXT NOT NULL,
    "section_type" TEXT NOT NULL,
    "section_content" TEXT NOT NULL,
    "section_context" TEXT NOT NULL,
    "health_form_source" JSONB,
    "health_demand_name" TEXT,
    "start_date" TIMESTAMP,
    "end_date" TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE'::text,
    "position" INTEGER NOT NULL,
    "created_at" TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    "updated_at" TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    "version" INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS screen_detail_person_id_idx ON screen_detail(person_id);
CREATE INDEX IF NOT EXISTS screen_detail_screen_type_idx ON screen_detail(screen_type);
CREATE INDEX IF NOT EXISTS screen_detail_section_type_idx ON screen_detail(section_type);

