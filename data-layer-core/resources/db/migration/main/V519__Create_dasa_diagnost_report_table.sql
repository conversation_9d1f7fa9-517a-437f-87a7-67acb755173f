CREATE TABLE IF NOT EXISTS dasa_diagnostic_report (
    id UUID PRIMARY KEY,
    person_id UUID NOT NULL,
    external_id TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    meta JSONB NOT NULL DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
    status TEXT NOT NULL,
    category J<PERSON>NB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    code JSONB NOT NULL DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
    subject JSONB NOT NULL DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
    encounter JSONB NOT NULL DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
    performer JSONB NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    results_interpreter JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    result JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
    presented_form JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    observations JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    medias <PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    encounters JSONB NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    coverages JSONB NOT NULL DEFAULT '[]'::JSONB,
    service_requests JSONB NOT NULL DEFAULT '[]'::JSONB,
    practitioners JSONB NOT NULL DEFAULT '[]'::JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version INT NOT NULL
 );

CREATE UNIQUE INDEX IF NOT EXISTS dasa_diagnostic_report_external_id_idx ON dasa_diagnostic_report(external_id);
CREATE INDEX IF NOT EXISTS dasa_diagnostic_report_id_idx ON dasa_diagnostic_report(person_id);
CREATE INDEX IF NOT EXISTS dasa_diagnostic_report_idx ON dasa_diagnostic_report(status);
