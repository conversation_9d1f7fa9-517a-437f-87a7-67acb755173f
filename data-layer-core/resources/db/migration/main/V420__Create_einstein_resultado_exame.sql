CREATE TABLE IF NOT EXISTS einstein_resultado_exame (
    id UUID PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    person_id UUID NOT NULL,
    passagem text NOT NULL,
    status text NOT NULL,
    data_passagem text,
    local_passagem text,
    tipo_passagem text,
    ocorrencias text,
    exames JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>
);
