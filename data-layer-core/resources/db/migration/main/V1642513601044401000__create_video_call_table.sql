CREATE TABLE IF NOT EXISTS video_call(
    id UUID NOT NULL PRIMARY KEY,
    external_id UUID UNIQUE NOT NULL,
    channel_id TEXT NOT NULL,
    person_id UUID NOT NULL,
    type TEXT NOT NULL,
    status TEXT NOT NULL,
    started_by_staff_id UUID NOT NULL,
    started_at TIMESTAMP NOT NULL,
    ended_at TIMESTAMP,
    member_joined_at TIMESTAMP,
    version INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS video_call_external_id_idx ON video_call(external_id);
CREATE INDEX IF NOT EXISTS video_call_channel_id_idx ON video_call(channel_id);
CREATE INDEX IF NOT EXISTS video_call_person_id_idx ON video_call(person_id);
CREATE INDEX IF NOT EXISTS video_call_type_idx ON video_call(type);
CREATE INDEX IF NOT EXISTS video_call_status_idx ON video_call(status);
