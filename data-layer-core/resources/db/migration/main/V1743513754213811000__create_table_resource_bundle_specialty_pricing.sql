CREATE TABLE IF NOT EXISTS resource_bundle_specialty_pricing (
    id UUID PRIMARY KEY,
    resource_bundle_specialty_id UUID NOT NULL,
    prices JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    begin_at DATE NOT NULL,
    end_at DATE,
    version INTEGER NOT NULL,

    updated_by <PERSON><PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL,

    FOREIGN KEY (resource_bundle_specialty_id) REFERENCES resource_bundle_specialty(id)
);

ALTER TABLE IF EXISTS resource_bundle_specialty ADD COLUMN IF NOT EXISTS version INTEGER NOT NULL;
