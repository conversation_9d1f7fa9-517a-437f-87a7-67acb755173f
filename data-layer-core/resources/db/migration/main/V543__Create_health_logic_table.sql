CREATE TABLE IF NOT EXISTS health_logic_record (
    id UUID NOT NULL PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    service_script_node_id UUID NOT NULL,
    person_id UUID NOT NULL,
    added_by_staff_id UUID,
    status TEXT NOT NULL,
    comment TEXT,
    added_at timestamp WITHOUT TIME ZONE NOT NULL,
    FOREIG<PERSON> KEY(service_script_node_id) REFERENCES service_script_node(id),
    FOREIGN KEY(added_by_staff_id) REFERENCES staff(id)
);
