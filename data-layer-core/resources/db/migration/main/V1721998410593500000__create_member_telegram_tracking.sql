CREATE TABLE
    IF NOT EXISTS member_telegram_tracking
(
    id UUID NOT NULL,
    created_at                   TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at                   TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    version                      INTEGER NOT NULL,
    external_id                  TEXT NOT NULL,
    name                         TEXT,
    address                      TEXT,
    street_number                TEXT,
    address_complement           TEXT,
    zip                          TEXT,
    city                         TEXT,
    state                        TEXT,
    country                      TEXT,
    name_sender                  TEXT,
    address_sender               TEXT,
    zip_sender                   TEXT,
    city_sender                  TEXT,
    state_sender                 TEXT,
    street_number_sender         TEXT,
    address_complement_sender    TEXT,
    country_sender               TEXT,
    ship_type                    TEXT,
    tracking                     TEXT,
    status                       TEXT,
    status_delivery              TEXT,
    tracking_records_last_update TIMESTAMP(6) WITHOUT TIME ZONE,
    date_submit                  TIMESTAMP(6) WITHOUT TIME ZONE,
    date_last_change             TIMESTAMP(6) WITHOUT TIME ZONE,
    tag                          TEXT,
    tracking_events JSONB DEFAULT '[]'::jsonb,
    telegram_info JSONB DEFAULT '{}'::jsonb,
    postal_type JSONB DEFAULT '{}'::jsonb,
    delivery_estimate TIMESTAMP(6) WITHOUT TIME ZONE,
    search_tokens TSVECTOR,
    PRIMARY KEY (id)
    );
