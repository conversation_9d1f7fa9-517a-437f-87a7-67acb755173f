CREATE TABLE IF NOT EXISTS channels_result
(
    id                   UUID PRIMARY KEY,
    staff_id             UUID      NOT NULL,
    reason               TEXT      NOT NULL,
    status               TEXT      NOT NULL,
    person_internal_code TEXT      NOT NULL,
    channel_ids          JSONB     NOT NULL DEFAULT '[]'::JSONB,
    file_id              TEXT,
    error_message        TEXT,
    version              INTEGER   NOT NULL,
    created_at           TIMESTAMP NOT NULL,
    updated_at           TIMESTAMP NOT NULL,
    FOREIGN KEY (staff_id) REFERENCES staff (id),
    FOREIGN KEY (person_internal_code) REFERENCES person_internal_reference (internal_code)
);
