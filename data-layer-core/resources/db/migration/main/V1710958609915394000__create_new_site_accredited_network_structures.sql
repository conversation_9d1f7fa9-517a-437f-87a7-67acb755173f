CREATE TABLE IF NOT EXISTS site_accredited_network(
    id UUID NOT NULL PRIMARY KEY,
    title TEXT,
    active BOOLEAN NOT NULL DEFAULT FALSE,
    brand TEXT NULL DEFAULT 'ALICE',
    bundle_ids JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    product_ids JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL
);

CREATE INDEX IF NOT EXISTS site_accredited_network_brand_idx ON site_accredited_network(brand);
CREATE INDEX IF NOT EXISTS site_accredited_network_active_idx ON site_accredited_network(active);

CREATE TABLE IF NOT EXISTS site_accredited_network_address(
    id UUID NOT NULL PRIMARY KEY,
    neighborhood TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL
);


CREATE INDEX IF NOT EXISTS site_accredited_network_address_state_idx ON site_accredited_network_address(state);
CREATE INDEX IF NOT EXISTS site_accredited_network_address_city_idx ON site_accredited_network_address(city);

CREATE TABLE IF NOT EXISTS site_accredited_network_provider(
    id UUID NOT NULL PRIMARY KEY,
    product_bundle_id UUID NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    categories JSONB NOT NULL DEFAULT '[]'::JSONB,
    referenced_model_class TEXT NOT NULL,
    referenced_model_id UUID NOT NULL,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    icon TEXT,
    is_flagship BOOLEAN NOT NULL DEFAULT FALSE,
    addresses JSONB NOT NULL DEFAULT '[]'::JSONB,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);


CREATE INDEX IF NOT EXISTS site_accredited_network_provider_active_idx ON site_accredited_network_provider(active);
CREATE INDEX IF NOT EXISTS site_accredited_network_provider_type_idx ON site_accredited_network_provider(type);
CREATE INDEX IF NOT EXISTS site_accredited_network_provider_referenced_model_id_idx ON site_accredited_network_provider(referenced_model_id);
