CREATE TABLE IF NOT EXISTS member_signed_terms
(
    id                  UUID                      NOT NULL PRIMARY KEY,
    member_id           UUID                      NOT NULL,
    signature           JSONB DEFAULT '{}'::JSONB NOT NULL,
    term_type           TEXT                      NOT NULL,
    document_url        TEXT,
    signed_document_url TEXT,
    version             INTEGER                   NOT NULL,
    created_at          timestamp                 NOT NULL,
    updated_at          timestamp                 NOT NULL,
    FOREIGN KEY (member_id) REFERENCES member (id)
);

CREATE INDEX IF NOT EXISTS member_signed_terms_member_id_idx ON member_signed_terms (member_id);
