ALTER TABLE IF EXISTS healthcare_map ADD COLUMN IF NOT EXISTS reference_nurse_group_id UUID;
ALTER TABLE IF EXISTS healthcare_map ADD COLUMN IF NOT EXISTS multi_staff_ids JSONB NOT NULL DEFAULT '[]'::JSONB;

CREATE INDEX IF NOT EXISTS healthcare_map_reference_nurse_group_id_idx ON healthcare_map(reference_nurse_group_id);
CREATE INDEX IF NOT EXISTS healthcare_map_multi_staff_ids_idx ON healthcare_map USING gin(multi_staff_ids);
