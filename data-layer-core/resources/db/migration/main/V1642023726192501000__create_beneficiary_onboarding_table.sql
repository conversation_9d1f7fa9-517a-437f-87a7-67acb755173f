CREATE TABLE IF NOT EXISTS beneficiary_onboarding(
    id UUID PRIMARY KEY,
    beneficiary_id UUID UNIQUE NOT NULL,
    flow_type TEXT NOT NULL,
    initial_product_id UUID NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    FOREIGN KEY (beneficiary_id) REFERENCES beneficiary(id)
);

CREATE UNIQUE INDEX IF NOT EXISTS beneficiary_onboarding_beneficiary_id_unique_idx ON beneficiary_onboarding(beneficiary_id);
