CREATE TABLE IF NOT EXISTS counter_referral_relevance (
    "id" UUID PRIMARY KEY,
    "version" INTEGER NOT NULL,
    "referral_id" UUID NOT NULL,
    "is_necessary" BOOLEAN NOT NULL,
    "comments" TEXT NULL,
    "created_at" TIM<PERSON><PERSON>MP WITHOUT TIME ZONE NOT NULL,
    "updated_at" TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    FOREIGN KEY (referral_id) REFERENCES counter_referral(id)
);

CREATE INDEX IF NOT EXISTS counter_referral_relevance_referral_id_idx ON counter_referral_relevance(referral_id);
