CREATE TABLE IF NOT EXISTS service_script_navigation_group (
    id UUID NOT NULL PRIMARY KEY,
    person_id UUID NOT NULL,
    script_node_id UUID NOT NULL,
    started_at timestamp NOT NULL,
    finished_at timestamp,
    source JSONB,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL
);
CREATE INDEX IF NOT EXISTS service_script_navigation_group_person_id_idx ON service_script_navigation_group(person_id);
CREATE INDEX IF NOT EXISTS service_script_navigation_group_script_node_id_idx ON service_script_navigation_group(script_node_id);
CREATE INDEX IF NOT EXISTS service_script_navigation_group_source_idx ON service_script_navigation_group USING GIN(source);
CREATE INDEX IF NOT EXISTS service_script_navigation_group_finished_at_idx ON service_script_navigation_group(finished_at);
