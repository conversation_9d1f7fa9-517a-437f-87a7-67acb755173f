CREATE TABLE IF NOT EXISTS query_result(
    id UUID PRIMARY KEY NOT NULL,
    request_id UUID NOT NULL,
    requester_id UUID NOT NULL,
    description TEXT NOT NULL,
    query TEXT NOT NULL,
    pii_fields JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
    status TEXT NOT NULL,
    file_id TEXT,
    error_message TEXT,
    version INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIGN KEY (requester_id) REFERENCES staff(id)
);
