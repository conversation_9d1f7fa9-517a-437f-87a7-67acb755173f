CREATE TABLE IF NOT EXISTS cassi_specialist (
    id UUID PRIMARY KEY,
    version INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    name TEXT NOT NULL,
    specialty_id UUID,
    sub_specialty_ids JSONB NOT NULL DEFAULT '[]'::JSONB,
    email TEXT,
    gender TEXT,
    council JSONB,
    phones JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    address JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    image_url TEXT,
    min_attendance_age INTEGER,
    max_attendance_age INTEGER,
    appointment_types JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    show_on_app BOOLEAN NOT NULL DEFAULT TRUE,
    status TEXT,
    url_slug TEXT,
    search_tokens TSVECTOR
);

CREATE INDEX IF NOT EXISTS cassi_specialist_search_tokens_idx ON cassi_specialist USING gin(search_tokens);

CREATE INDEX IF NOT EXISTS cassi_specialist_url_slug_idx ON cassi_specialist(url_slug);
