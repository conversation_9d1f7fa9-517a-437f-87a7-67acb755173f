CREATE TABLE IF NOT EXISTS member_onboarding_step(
    id UUID NOT NULL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    image_url TEXT NOT NULL,
    actions jsonb NOT NULL DEFAULT '[]'::jsonb,
    next UUID NULL DEFAULT NULL,
    member_onboarding_template_id UUID NOT NULL,
    type TEXT NOT NULL,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    FOREIGN KEY (member_onboarding_template_id) REFERENCES member_onboarding_template(id),
    FOREIGN KEY (next) REFERENCES member_onboarding_step(id)
);

CREATE INDEX IF NOT EXISTS member_onboarding_template_id_idx ON member_onboarding_step(member_onboarding_template_id);
CREATE INDEX IF NOT EXISTS next_id_idx ON member_onboarding_step(next);
