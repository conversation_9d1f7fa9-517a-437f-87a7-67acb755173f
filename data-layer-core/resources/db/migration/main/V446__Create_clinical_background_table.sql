CREATE TABLE IF NOT EXISTS clinical_background (
    id UUID PRIMARY KEY,
    person_id UUID NOT NULL,
    type TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version INT NOT NULL,
    added_by_staff_id UUID NOT NULL,
    added_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    deleted_at TIMESTAMP WITHOUT TIME ZONE,
    deleted_by_staff_id UUID,
    appointment_id UUID NOT NULL,

    FOREIGN KEY (added_by_staff_id) REFERENCES staff(id),
    FOREIGN KEY (deleted_by_staff_id) REFERENCES staff(id),
    FOREIGN KEY (appointment_id) REFERENCES appointment(id)
 );

CREATE INDEX IF NOT EXISTS clinical_background_updated_at_idx ON clinical_background(updated_at);
CREATE INDEX IF NOT EXISTS clinical_background_person_id_idx ON clinical_background(person_id);
CREATE INDEX IF NOT EXISTS clinical_background_status_idx ON clinical_background(status);
CREATE INDEX IF NOT EXISTS clinical_background_type_idx ON clinical_background(type);
