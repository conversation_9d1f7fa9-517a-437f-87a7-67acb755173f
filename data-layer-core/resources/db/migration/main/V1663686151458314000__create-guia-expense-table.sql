CREATE TABLE IF NOT EXISTS
    tiss_guia_expense
(
    id                      UUID NOT NULL,
    version                 INTEGER NOT NULL,
    created_at              TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,

    executed_at             DATE NOT NULL,
    execution_start_at      TIME(6) WITHOUT TIME ZONE,
    execution_end_at        TIME(6) WITHOUT TIME ZONE,
    procedure_table         TEXT NOT NULL,
    procedure_code          TEXT NOT NULL,
    quantity                INTEGER NOT NULL,
    unit_measure            TEXT,
    reduce_value            INTEGER,
    unity_value             INTEGER,
    total_value             INTEGER,
    procedure_description   TEXT NOT NULL,
    guia_id                 UUID,
    critique                TEXT,
    reviewer_id             TEXT,
    expense_code            TEXT,
    PRIMARY KEY (id)
    );
