CREATE TABLE IF NOT EXISTS appointment_lock (
    id UUID PRIMARY KEY,
    appointment_id UUID NOT NULL,
    staff_id UUID NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

CREATE UNIQUE INDEX ON appointment_lock(appointment_id);
ALTER TABLE appointment_lock
    ADD CONSTRAINT fk_appointment_id_to_appointment_lock FOREIGN KEY(appointment_id) REFERENCES appointment(id);