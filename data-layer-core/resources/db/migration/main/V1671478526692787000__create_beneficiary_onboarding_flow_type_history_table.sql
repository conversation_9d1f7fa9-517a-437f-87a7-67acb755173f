CREATE TABLE IF NOT EXISTS beneficiary_onboarding_flow_type_history(
    id UUID PRIMARY KEY NOT NULL,
    beneficiary_id UUID NOT NULL,
    flow_version TEXT NOT NULL,
    company_default_flow_type TEXT,
    days_since_beginning_of_contract INT NOT NULL,
    beneficiaries_count_at_day_zero INT NOT NULL,
    base_lives_flow INT NOT NULL,
    beneficiary_metadata JSONB NOT NULL,
    flow_rule TEXT NOT NULL,
    risk_flow TEXT NOT NULL,
    risk_defined_at TIMESTAMP NOT NULL,
    risk_defined_by UUID,
    version INT NOT NULL,
    created_at TIMESTAMP without time zone NOT NULL,
    updated_at TIMESTAMP without time zone NOT NULL
);
