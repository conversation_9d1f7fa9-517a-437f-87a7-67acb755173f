CREATE TABLE IF NOT EXISTS appointment_follow_up (
    id UUID PRIMARY KEY,
    ticket_id BIGINT NOT NULL,
    appointment_id UUID NOT NULL,
    type TEXT NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

CREATE INDEX IF NOT EXISTS appointment_follow_up_appointment_id_idx ON appointment_follow_up (appointment_id);
CREATE INDEX IF NOT EXISTS appointment_follow_up_type_idx ON appointment_follow_up (type);