ALTER TABLE db_laboratory_test_result_process ALTER COLUMN claim_id DROP NOT NULL;
ALTER TABLE db_laboratory_test_result_process ADD COLUMN IF NOT EXISTS execution_group_id UUID;
ALTER TABLE db_laboratory_test_result_process ADD COLUMN IF NOT EXISTS attendance_id TEXT;
ALTER TABLE db_laboratory_test_result_process
    ADD CONSTRAINT fk_execution_group_id_to_db_laboratory_test_result_process FOREIGN KEY(execution_group_id) REFERENCES execution_group(id);

ALTER TABLE db_laboratory_test_result ALTER COLUMN claim_id DROP NOT NULL;
ALTER TABLE db_laboratory_test_result ADD COLUMN IF NOT EXISTS attendance_id TEXT;

UPDATE db_laboratory_test_result_process SET attendance_id = claim_id;
UPDATE db_laboratory_test_result SET attendance_id = claim_id;
