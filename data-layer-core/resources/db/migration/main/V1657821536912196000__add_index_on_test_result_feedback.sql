ALTER TABLE IF EXISTS test_result_feedback RENAME COLUMN alice_test_result_bundle_result_ids TO alice_test_result_bundle_ids;
CREATE INDEX IF NOT EXISTS bundle_ids_idx ON test_result_feedback USING gin (alice_test_result_bundle_ids);
CREATE INDEX IF NOT EXISTS health_plan_task_ids_idx ON test_result_feedback USING gin (health_plan_task_ids);
CREATE INDEX IF NOT EXISTS person_id_idx ON test_result_feedback(person_id);
CREATE INDEX IF NOT EXISTS added_at_idx ON test_result_feedback(added_at);

