CREATE TABLE IF NOT EXISTS staff_channel_history(
    id UUID NOT NULL PRIMARY KEY,
    staff_id UUID NOT NULL,
    status TEXT NOT NULL,
    changed_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, 
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS staff_channel_history_staff_changed_date_unique_idx ON staff_channel_history(staff_id, changed_date);

CREATE INDEX IF NOT EXISTS staff_channel_history_staff_id_idx ON staff_channel_history(staff_id);
