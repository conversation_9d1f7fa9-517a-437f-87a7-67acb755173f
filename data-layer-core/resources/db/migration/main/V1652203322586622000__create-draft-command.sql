CREATE TABLE draft_command(
  id UUID PRIMARY KEY NOT NULL,
  appointment_id UUID NOT NULL,
  action TEXT NOT NULL,
  serialized_model TEXT NOT NULL DEFAULT '',
  referenced_model TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'PENDING',
  version INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  CONSTRAINT fk_draft_command_appointment FOREIGN KEY (appointment_id) REFERENCES appointment(id)
);
