CREATE TABLE IF NOT EXISTS product_with_units_view(
    product_Id UUID NOT NULL,
    title TEXT NOT NULL,
    display_name TEXT,
    type TEXT NOT NULL,
    price_listing TEXT,
    anchor TEXT,
    accommodation TEXT,
    refund TEXT NOT NULL,
    co_payment TEXT NOT NULL,
    active BOOL NOT NULL,
    is_visible_for_sale BOOL NOT NULL,

    bundle_id UUID NOT NULL,
    bundle_type TEXT NOT NULL,

    provider_id UUID NOT NULL,
    provider_flagship BOOL NOT NULL,
    provider_name TEXT NOT NULL,
    provider_logo TEXT,
    provider_thumbnail TEXT,
    provider_about TEXT,
    provider_type TEXT NOT NULL,
    provider_icon TEXT,
    provider_image_url TEXT,

    unit_id UUID NOT NULL,
    unit_name TEXT NOT NULL,
    unit_type TEXT NOT NULL,
    provider_unit_group_id UUID,
    locality_id UUID,
    locality_lat FLOAT,
    locality_lon FLOAT,

    id UUID NOT NULL PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
