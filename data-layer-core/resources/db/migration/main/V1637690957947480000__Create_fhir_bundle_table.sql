CREATE TABLE IF NOT EXISTS fhir_bundle (
	id uuid NOT NULL,
	person_id uuid NOT NULL,
	bundle_type TEXT NOT NULL,
	external_id TEXT NOT NULL,
	encounter jsonb NOT NULL DEFAULT '{}'::jsonb,
	appointments jsonb NOT NULL DEFAULT '[]'::jsonb,
	practitioners jsonb NOT NULL DEFAULT '[]'::jsonb,
	observations jsonb NOT NULL DEFAULT '[]'::jsonb,
	diagnostic_reports jsonb NOT NULL DEFAULT '[]'::jsonb,
	procedures jsonb NOT NULL DEFAULT '[]'::jsonb,
	conditions jsonb NOT NULL DEFAULT '[]'::jsonb,
	allergy_intolerances jsonb NOT NULL DEFAULT '[]'::jsonb,
	medication_statements jsonb NOT NULL DEFAULT '[]'::jsonb,
	risk_assessments jsonb NOT NULL DEFAULT '[]'::jsonb,
	clinical_impressions jsonb NOT NULL DEFAULT '[]'::jsonb,
	version int NOT NULL,
	created_at timestamp NOT NULL,
	updated_at timestamp NOT NULL
);
CREATE UNIQUE INDEX fhir_bundle_external_id_idx ON fhir_bundle USING btree (external_id);
CREATE INDEX fhir_bundle_person_id_idx ON fhir_bundle USING btree (person_id);
