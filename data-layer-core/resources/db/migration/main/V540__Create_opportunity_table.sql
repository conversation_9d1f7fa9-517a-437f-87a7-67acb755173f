CREATE TABLE IF NOT EXISTS opportunity (
    id UUID NOT NULL PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    simulation_id UUID NOT NULL,
    product_id UUID NOT NULL,
    price JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
    expires_at timestamp WITHOUT TIME ZONE NOT NULL,
    FOREIGN KEY(simulation_id) REFERENCES health_product_simulation(id),
    FOREIGN KEY(product_id) REFERENCES product(id)
);