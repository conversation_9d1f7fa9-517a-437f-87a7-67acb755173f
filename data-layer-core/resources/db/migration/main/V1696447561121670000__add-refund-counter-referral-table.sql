CREATE TABLE IF NOT EXISTS refund_counter_referral (
    id                             UUID                      NOT NULL PRIMARY KEY,
    person_id                      UUID                      NOT NULL,
    refund_id                      UUID                      NOT NULL,
    medical_specialty_id           UUID                      ,
    attendance_at                  TIMESTAMP                 WITHOUT TIME ZONE,
    health_condition_ids           JSONB                     NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    staff_name                     TEXT                      ,
    staff_council_state            TEXT                      ,
    staff_council_number           TEXT                      ,
    clinical_evaluation            TEXT                      ,
    medical_orientation            TEXT                      ,
    diagnostic_hypothesis          TEXT                      ,
    sessions_quantity              INTEGER                   ,
    version                        INTEGER                   NOT NULL,
    created_at                     TIMESTAMP                 WITHOUT TIME ZONE NOT NULL,
    updated_at                     TIMESTAMP                 WITHOUT TIME ZONE NOT NULL,

    FOREIGN KEY (refund_id) REFERENCES refund(id)
);

CREATE INDEX IF NOT EXISTS refund_counter_referral_person_id_idx ON refund_counter_referral(person_id);
