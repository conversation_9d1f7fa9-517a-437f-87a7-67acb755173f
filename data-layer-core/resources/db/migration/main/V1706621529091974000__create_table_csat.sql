CREATE TABLE IF NOT EXISTS csat (
    id UUID PRIMARY KEY,
    person_id UUID NOT NULL,
    context_id TEXT NOT NULL,
    context_type TEXT NOT NULL,
    template_id UUID NOT NULL,
    feedback JSONB NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);


CREATE INDEX IF NOT EXISTS csat_template_id_idx ON csat(template_id);
CREATE INDEX IF NOT EXISTS csat_person_id_idx ON csat(person_id);
CREATE INDEX IF NOT EXISTS csat_context_type_idx ON csat(context_type);
CREATE UNIQUE INDEX IF NOT EXISTS csat_context_type_context_id_idx ON csat(context_id, context_type);
