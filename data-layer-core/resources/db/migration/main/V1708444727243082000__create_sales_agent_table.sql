CREATE TABLE IF NOT EXISTS sales_agent (
    id UUID NOT NULL PRIMARY KEY,
    name TEXT NOT NULL,
    document_number TEXT NOT NULL,
    email TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    birth_date DATE NOT NULL,
    sales_firm_id UUID NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,

    FOREIGN KEY (sales_firm_id) REFERENCES sales_firm(id)
);

CREATE UNIQUE INDEX IF NOT EXISTS sales_agent_document_number_sales_firm_id_idx ON sales_agent(document_number, sales_firm_id);
