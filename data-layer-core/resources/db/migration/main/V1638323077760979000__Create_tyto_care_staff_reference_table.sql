CREATE TABLE IF NOT EXISTS tyto_care_staff_reference (
    id                  UUID        NOT NULL   PRIMARY KEY,
    created_at          timestamp   NOT NULL              ,
    updated_at          timestamp   NOT NULL              ,
    version             INTEGER     NOT NULL              ,

    staff_id            UUID        NOT NULL              ,
    first_name          TEXT        NOT NULL              ,
    last_name           TEXT        NOT NULL              ,
    date_of_birth       TEXT        NOT NULL              ,
    sex                 TEXT        NOT NULL              ,
    email               TEXT        NOT NULL              ,
    tyto_identifier     UUID            NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS tyto_care_staff_reference_staff_id_idx ON tyto_care_staff_reference (staff_id);
CREATE INDEX IF NOT EXISTS tyto_care_staff_reference_tyto_identifier_idx ON tyto_care_staff_reference (tyto_identifier);
