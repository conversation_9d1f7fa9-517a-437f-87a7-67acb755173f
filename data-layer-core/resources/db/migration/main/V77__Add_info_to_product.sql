ALTER TABLE product ADD COLUMN IF NOT EXISTS info JSONB;

UPDATE product SET info = '{ "copay": 30, "labs": ["Rede A+"] }'::JSONB WHERE id = '31b11f35-fec3-4082-9994-ddd8b56cf7ae' AND info IS NULL;
UPDATE product SET info = '{ "copay": 30, "labs": ["Rede A+", "Fleury"] }'::JSONB WHERE id = '07c577b1-fc14-4a58-ab52-e1fbb153bda7' AND info IS NULL;
UPDATE product SET info = '{ "copay": 10, "labs": ["Rede A+"] }'::JSONB WHERE id = 'a3fe993f-8ec1-4843-902b-cb78844501df' AND info IS NULL;
UPDATE product SET info = '{ "copay": 10, "labs": ["Rede A+", "Fleury"] }'::JSONB WHERE id = '92d9e9a0-24db-4a9b-9b17-b022078f10b7' AND info IS NULL;