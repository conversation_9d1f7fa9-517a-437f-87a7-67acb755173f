package app.business_domain_service_backfill_test

import data.app.business_domain_service_backfill
import rego.v1

test_unauthenticated_view_count_models_allow if {
	resources = [
		"PersonModel",
		"StaffModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"PersonClinicalAccount",
	]

	every resource in resources {
		{1, 2} == business_domain_service_backfill.allow with input as {"cases": [
			{
				"index": 1,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_view_create_BillingAccountablePartyModel_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
            "index": 3,
            "action": "create",
            "subject": {"opaType": "Unauthenticated"},
            "resource": {"opaType": "BillingAccountablePartyModel"},
        },
	]}
}

test_unauthenticated_CR_count_PersonRegistration_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_unauthenticated_CR_count_StandardCost_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "StandardCostModel"},
		},
	]}
}

test_unauthenticated_CR_count_HealthDeclaration_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_unauthenticated_CRU_count_models_allow if {
	resources = [
		"MemberModel",
		"CassiMemberModel",
		"ProviderModel",
		"ProductModel",
		"ProductBundleModel",
		"ProductGroupModel",
		"CompanyStaffModel",
		"CompanyModel",
		"PersonOnboardingModel",
		"OnboardingContractModel",
		"FileVault",
		"CompanyProductConfigurationModel",
		"GenericFileVault",
	]

	every resource in resources {
		{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
			{
				"index": 1,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_unauthenticated_CRU_BeneficiaryHubspot_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryHubspotModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryHubspotModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "BeneficiaryHubspotModel"},
		},
	]}
}

test_unauthenticated_CRUD_CompanyProductPriceListing_allow if {
	{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_unauthenticated_CRUD_count_models_allow if {
	resources = [
		"BeneficiaryModel",
		"BeneficiaryOnboardingModel",
		"BeneficiaryOnboardingPhaseModel",
		"BeneficiaryCompiledViewModel",
		"MemberContractModel",
		"MemberContractTermModel",
		"CompanyContractModel",
		"CompanySubContractModel",
		"GenericFileVault"
	]

	every resource in resources {
		{1, 2, 3, 4, 5} == business_domain_service_backfill.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "view",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "delete",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
			{
				"index": 5,
				"action": "count",
				"subject": {"opaType": "Unauthenticated"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_staff_product_tech_CRU_count_Beneficiary_allow if {
	{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_staff_product_tech_CRU_count_CassiMember_allow if {
	{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_staff_product_tech_CRU_count_BeneficiaryOnboarding_allow if {
	{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
	]}
}

test_staff_product_tech_CRU_count_BeneficiaryOnboardingPhase_allow if {
	{1, 2, 3, 4} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
	]}
}

test_staff_product_tech_CR_count_PersonRegistration_allow if {
	{1, 2, 3} == business_domain_service_backfill.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_product_tech_create_HealthDeclaration_allow if {
	{1} == business_domain_service_backfill.allow with input as {"cases": [{
		"index": 1,
		"action": "create",
		"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
		"resource": {"opaType": "HealthDeclaration"},
	}]}
}

test_unauthenticated_view_delete_CompanyActivationFilesModel_allow if {
    {1, 2} == business_domain_service_backfill.allow with input as {"cases": [
        {
            "index": 1,
            "action": "view",
            "subject": {"opaType": "Unauthenticated"},
            "resource": {"opaType": "CompanyActivationFilesModel"},
        },
        {
            "index": 2,
            "action": "delete",
            "subject": {"opaType": "Unauthenticated"},
            "resource": {"opaType": "CompanyActivationFilesModel"},
        },
    ]}
}
