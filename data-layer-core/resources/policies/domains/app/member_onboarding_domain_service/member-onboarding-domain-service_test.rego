package app.member_onboarding_domain_service_test

import rego.v1
import data.app.member_onboarding_domain_service


test_unauth_view_count_create_update_and_delete_member_onboarding_allowed if {
    {1,2,3,4,5} == member_onboarding_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            }
        ]
    }
}

test_unauth_view_resources_allowed if {
    {1,2,3,4,5,6,7,8} == member_onboarding_domain_service.allow with input as {
        "cases": [
             {
                 "index": 1,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "MemberModel"
                 }
             },
             {
                 "index": 2,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "PersonModel"
                 }
             },
             {
                 "index": 3,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "BeneficiaryModel"
                 }
             },
             {
                 "index": 4,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "OngoingCompanyDeal"
                 }
             },
             {
                 "index": 5,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "TrackPersonABModel"
                 }
             },
             {
                 "index": 6,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "HealthDeclaration"
                 }
             },
             {
                 "index": 7,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "DeviceModel"
                 }
             },
             {
                 "index": 8,
                 "action": "view",
                 "subject": {
                     "opaType": "Unauthenticated"
                 },
                 "resource": {
                     "opaType": "ClinicalOutcomeRecord"
                 }
            }
        ]
    }
}

test_product_tech_health_role_view_count_create_and_update_member_onboarding_allowed if {
    {1,2,3,4} == member_onboarding_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberOnboarding"
                }
            }
        ]
    }
}
