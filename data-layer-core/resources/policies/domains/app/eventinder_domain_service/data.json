{"rules": [{"conditions": [], "allow": [{"resources": ["HealthEventsModel", "HealthProfessionalModel", "HealthPlanTask"], "actions": ["view", "create", "update"]}, {"resources": ["HealthCommunitySpecialistModel", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["CaseRecord", "PersonCase"], "actions": ["view"]}, {"resources": ["MvAuthorizedProcedureModel", "CounterReferral"], "actions": ["view"], "_comment": "Used in backfill"}, {"resources": ["PersonInternalReference"], "actions": ["view", "create"], "_comment": "Used in backfill"}, {"resources": ["ProductModel", "ProductBundleModel", "PriceListingModel", "ProductPriceListingModel", "CompanyProductPriceListingModel", "ProductGroupModel"], "actions": ["view", "count"]}, {"resources": ["ProviderUnitModel", "StructuredAddress"], "actions": ["view"]}]}, {"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["Appointment", "StructuredAddress", "ContactModel"], "actions": ["view", "count"]}, {"resources": ["HealthEventsModel"], "actions": ["view", "count", "delete"]}]}]}