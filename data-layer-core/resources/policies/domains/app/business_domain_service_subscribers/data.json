{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "branches": [{"conditions": ["${resource.domain} == member", "${resource.namespace} == term"], "allow": [{"resources": ["FileVault"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${resource.namespace} == company_activation_files"], "allow": [{"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view", "create"]}]}], "allow": [{"resources": ["PersonClinicalAccount", "ProductModel", "ProductPriceListingModel", "PriceListingModel", "CompanyProductConfigurationModel", "StaffModel", "RefundCostInfoModel", "CompanyRefundCostInfoModel", "PreActivationPaymentModel", "PersonGracePeriod", "InvoiceLiquidationModel"], "actions": ["view", "count"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingPhaseModel", "BeneficiaryOnboardingModel", "B2bBatchInvoiceReportModel", "BillingAccountablePartyModel", "PersonBillingAccountablePartyModel", "PersonModel", "FileVault", "CompanyModel", "CompanyContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CompanySubContractModel", "CompanyStaffModel", "MemberModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["InvoiceGroupTaxReceiptModel", "HealthDeclaration", "PersonRegistrationModel", "PersonOnboardingModel", "FirstPaymentScheduleModel"], "actions": ["view", "create", "count"]}, {"resources": ["BeneficiaryHubspotModel", "CompanyActivationFilesModel", "MemberInvoiceModel", "InvoicePaymentModel", "ResourceSignTokenModel", "ItauPaymentModel", "CancelPaymentOnAcquirerScheduleModel", "PaymentDetailModel", "BolepixPaymentDetailModel", "PixPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "BoletoPaymentDetailModel", "InvoiceItemModel", "MemberInvoiceGroupModel", "PreActivationPaymentModel", "CompanyProductPriceListingModel", "MemberProductChangeScheduleModel", "MemberTelegramTrackingModel"], "actions": ["view", "update", "create"]}, {"resources": ["OngoingCompanyDeal"], "actions": ["view", "count", "update"]}, {"resources": ["PersonLoginModel"], "actions": ["create"]}, {"resources": ["CassiMemberModel", "MemberContractModel", "MemberContractTermModel", "BeneficiaryCompiledViewModel"], "actions": ["view", "count", "create", "update", "delete"]}]}]}