package app.health_plan_domain_service_test

import rego.v1

import data.app.health_plan_domain_service

test_unauth_view_models_allowed_1 if {
    resources = [
        "PersonModel",
        "HealthCommunitySpecialistModel",
        "StaffModel",
        "HealthDeclaration",
        "MemberModel",
        "HealthForm",
        "HealthFormQuestionAnswer",
        "HealthcareTeamModel",
        "TestCodeModel",
        "TestCodePackageModel",
        "HealthPlanTaskTemplate",
        "HealthPlanTaskGroupTemplate",
        "PersonCase",
        "CounterReferral",
        "Risk",
        "HealthCondition",
        "PersonClinicalAccount",
        "ClinicalOutcomeRecord",
        "OutcomeConf",
        "HealthProfessionalModel",
        "MedicalSpecialtyModel",
        "AppointmentScheduleModel",
        "HealthcareAdditionalTeam",
        "StaffSignTokenModel",
        "StructuredAddress",
        "ContactModel"
    ]

    every resource in resources {
        {1} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_models_allowed_2 if {
    resources = [
        "ActionPlanTask",
        "GuiaModel",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentEvent",
        "AppointmentEvolution",
        "Timeline",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "VideoCall",
        "DasaDiagnosticReport",
        "TertiaryIntentionTouchPoint",
        "PersonEligibilityDuquesa",
        "ClinicalBackground",
        "ConsolidatedRewardsModel",
        "ExternalReferralModel",
        "HealthMeasurementModel",
        "LaboratoryTestResultModel",
        "PregnancyModel",
        "TestResultFileModel",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "ConsentRegistration",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument",
        "HaocFhirProcess",
        "HDataOverview",
        "HLActionRecommendation",
        "HealthLogicRecord",
        "HLAdherence",
        "PersonHealthLogic",
        "AssistanceCare",
        "HealthcareMap",
        "PersonHealthcareTeamRecommendationModel",
        "PersonHealthGoalModel",
        "PersonTeamAssociation",
        "DischargeSummary",
        "AppointmentCoordination",
        "IntentionCoordination",
        "PersonHealthEvent",
        "TestResultFeedback",
        "WandaComment"
    ]

    every resource in resources {
        {1} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_create_models_allowed if {
    resources = [
        "CaseRecord"
    ]

    every resource in resources {
        {1,2} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_count_models_allowed if {
    resources = [
        "BeneficiaryModel",
        "BeneficiaryOnboardingModel",
        "BeneficiaryOnboardingPhaseModel",
        "CassiMemberModel",
        "CompanyModel"
    ]

    every resource in resources {
        {1,2} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_update_models_allowed if {
    resources = [
        "MemberOnboarding"
    ]

    every resource in resources {
        {1,2} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_create_update_models_allowed if {
    resources = [
        "HealthPlan",
        "FileVault",
        "HealthPlanTaskGroup",
        "ProtocolTracking",
        "HealthLogicTracking"
    ]

    every resource in resources {
        {1,2,3} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_count_update_models_allowed if {
    resources = [
        "HealthPlanTaskReferrals","HealthPlanTaskTemplate"
    ]

    every resource in resources {
        {1,2,3} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_count_create_update_models_allowed if {
    resources = [
        "HealthPlanTask"
    ]

    every resource in resources {
        {1,2,3,4} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_create_models_allowed if {
    resources = [
        "GenericFileVault"
    ]

    every resource in resources {
        {1} == health_plan_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}
