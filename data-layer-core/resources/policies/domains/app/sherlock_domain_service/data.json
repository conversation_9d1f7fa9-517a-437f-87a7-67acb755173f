{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["QueryEvent"], "actions": ["create"]}, {"resources": ["StaffModel", "PersonInternalReference"], "actions": ["view"]}, {"resources": ["PersonModel"], "actions": ["view", "count"]}, {"resources": ["QueryResult", "ChannelsResult"], "actions": ["view", "update", "create", "count"]}, {"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view", "create"]}]}]}