package app.hr_core_domain_service_test

import rego.v1

import data.app.hr_core_domain_service

test_unauthenticated_view_allowed if {
    {1} == hr_core_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "CompanyModel",
                },
            }
        ]
    }
}

test_unauthenticated_view_delete_allowed if {
    {1,2} == hr_core_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "CompanyScoreMagenta",
                },
            },
            {
                "index": 2,
                "action": "delete",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "CompanyScoreMagenta",
                },
            }
        ]
    }
}
