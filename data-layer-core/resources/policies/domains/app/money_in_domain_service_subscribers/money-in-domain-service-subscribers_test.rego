package app.money_in_domain_service_subscribers_test

import data.app.money_in_domain_service_subscribers
import rego.v1

test_CRU_models_allow if {
	resources = [
		"MemberInvoiceModel",
		"InvoicePaymentModel",
        "ResourceSignTokenModel",
        "ItauPaymentModel",
        "CancelPaymentOnAcquirerScheduleModel",
		"PaymentDetailModel",
		"InvoiceItemModel",
		"MemberInvoiceGroupModel",
		"MemberModel",
		"PersonModel",
		"ProductModel",
		"BillingAccountablePartyModel",
		"InvoiceLiquidationModel",
		"ProductBundleModel",
		"PriceListingModel",
		"ProductPriceListingModel",
		"PersonBillingAccountablePartyModel",
		"EmailCommunicationModel",
		"BolepixPaymentDetailModel",
		"PixPaymentDetailModel",
		"SimpleCreditCardPaymentDetailModel",
		"BoletoPaymentDetailModel",
	]

	every resource in resources {
		{1, 2, 3} == money_in_domain_service_subscribers.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "view",
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_view_models_allow if {
	resources = [
		"ResourceSignTokenModel",
		"MemberLightweightModel",
		"MemberProductPriceModel",
		"PersonPreferencesModel",
		"ProductPriceAdjustmentModel",
		"MemberProductPriceAdjustmentModel",
	]

	every resource in resources {
		{1} == money_in_domain_service_subscribers.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"resource": {"opaType": resource},
		}]}
	}
}

test_view_count_BeneficiaryOnboarding_allow if {
	{1, 2} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
	]}
}

test_view_count_BeneficiaryOnboardingPhase_allow if {
	{1, 2} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 2,
			"action": "count",
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
	]}
}

test_view_count_CassiMember_allow if {
	{1, 2} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "count",
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_view_count_Beneficiary_allow if {
	{1, 2} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "count",
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_CRU_count_Company_allow if {
	{1, 2, 3, 4} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "view",
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 3,
			"action": "count",
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 4,
			"action": "update",
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_view_create_TrackPersonABModel_allow if {
	{1, 2} == money_in_domain_service_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"resource": {"opaType": "TrackPersonABModel"},
		},
		{
			"index": 2,
			"action": "create",
			"resource": {"opaType": "TrackPersonABModel"},
		},
	]}
}
