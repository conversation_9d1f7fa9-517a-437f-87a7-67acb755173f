package app.exec_indicator_domain_service_backfill_test

import rego.v1

import data.app.exec_indicator_domain_service_backfill

test_view_TotvsGuiaModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "TotvsGuiaModel"
                }
            }
        ]
    }
}

test_update_TotvsGuiaModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {},
                "resource": {
                    "opaType": "TotvsGuiaModel"
                }
            }
        ]
    }
}

test_create_TotvsGuiaModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {},
                "resource": {
                    "opaType": "TotvsGuiaModel"
                }
            }
        ]
    }
}

test_view_HealthcareBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "HealthcareBundleModel"
                }
            }
        ]
    }
}

test_update_HealthcareBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {},
                "resource": {
                    "opaType": "HealthcareBundleModel"
                }
            }
        ]
    }
}

test_view_ProviderUnitModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "ProviderUnitModel"
                }
            }
        ]
    }
}

test_view_ProviderUnitGroupModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "ProviderUnitGroupModel"
                }
            }
        ]
    }
}

test_view_TussProcedureSpecialtyModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_update_TussProcedureSpecialtyModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {},
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_create_TussProcedureSpecialtyModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {},
                "resource": {
                    "opaType": "TussProcedureSpecialtyModel"
                }
            }
        ]
    }
}

test_view_HealthSpecialistResourceBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "HealthSpecialistResourceBundleModel"
                }
            }
        ]
    }
}

test_create_HealthSpecialistResourceBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {},
                "resource": {
                    "opaType": "HealthSpecialistResourceBundleModel"
                }
            }
        ]
    }
}

test_update_HealthSpecialistResourceBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {},
                "resource": {
                    "opaType": "HealthSpecialistResourceBundleModel"
                }
            }
        ]
    }
}

test_delete_HealthSpecialistResourceBundleModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {},
                "resource": {
                    "opaType": "HealthSpecialistResourceBundleModel"
                }
            }
        ]
    }
}

test_view_HealthcareResourceModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {},
                "resource": {
                    "opaType": "HealthcareResourceModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_MvAuthorizedProcedureModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "MvAuthorizedProcedureModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_ExecutionGroupModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "ExecutionGroupModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_HealthEventsModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthEventsModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_ExecIndicatorAuthorizerModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "ExecIndicatorAuthorizerModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_TotvsGuiaModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "TotvsGuiaModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_HealthCommunitySpecialistModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthCommunitySpecialistModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_EligibilityCheckModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "EligibilityCheckModel"
                }
            }
        ]
    }
}

test_Unauthenticated_update_ExecutionGroupModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "ExecutionGroupModel"
                }
            }
        ]
    }
}

test_Unauthenticated_update_MvAuthorizedProcedureModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "MvAuthorizedProcedureModel"
                }
            }
        ]
    }
}

test_Unauthenticated_update_EligibilityCheckModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "EligibilityCheckModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_and_update_and_create_GlossAuthorizationInfoModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GlossAuthorizationInfoModel"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GlossAuthorizationInfoModel"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GlossAuthorizationInfoModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_and_update_and_create_GuiaWithProceduresModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GuiaWithProceduresModel"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GuiaWithProceduresModel"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "GuiaWithProceduresModel"
                }
            }
        ]
    }
}

test_Unauthenticated_count_ExecutionGroupModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "ExecutionGroupModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_HealthcareResourceGroupModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceGroupModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_and_update_HealthcareResourceModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceModel"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceModel"
                }
            }
        ]
    }
}

test_Unauthenticated_view_and_update_and_create_HealthcareResourceGroupAssociationModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceGroupAssociationModel"
                }
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceGroupAssociationModel"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "HealthcareResourceGroupAssociationModel"
                }
            }
        ]
    }
}

test_Unauthenticated_delete_MvAuthorizedProcedureModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "MvAuthorizedProcedureModel"
                }
            }
        ]
    }
}

test_Unauthenticated_delete_TotvsGuiaModel_allowed if {
    1 in exec_indicator_domain_service_backfill.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "delete",
                "subject": {
                    "opaType": "Unauthenticated",
                },
                "resource": {
                    "opaType": "TotvsGuiaModel"
                }
            }
        ]
    }
}
