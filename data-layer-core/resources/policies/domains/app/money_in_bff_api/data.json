{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["ResourceSignTokenModel", "ProductModel", "Beneficiary<PERSON><PERSON>l", "MemberModel", "MemberInvoiceModel", "InvoicePaymentModel", "PaymentDetailModel", "InvoiceItemModel", "BillingAccountablePartyModel", "PersonModel", "MemberInvoiceGroupModel", "BolepixPaymentDetailModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel"], "actions": ["view"]}]}]}