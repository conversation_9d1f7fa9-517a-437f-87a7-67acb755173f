{"rules": [{"conditions": ["${subject.opaType} == FileVault"], "branches": [{"conditions": ["${resource.domain} == member", "${resource.namespace} == term"], "allow": [{"resources": ["FileVault"], "actions": ["view", "count"]}]}]}, {"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["ProductOrderModel", "ProductBundleModel", "ProductPriceListingModel", "PriceListingModel", "ProviderModel", "InsurancePortabilityRequestModel", "CompanyModel", "CompanyContractModel", "MemberContractModel", "MemberContractTermModel", "PersonGracePeriod", "HealthCondition", "CompanySubContractModel"], "actions": ["view", "count"]}, {"resources": ["PersonRegistrationModel"], "actions": ["view", "create"]}, {"resources": ["MemberModel", "OnboardingContractModel", "ProductModel", "PersonModel", "Beneficiary<PERSON><PERSON>l"], "actions": ["view", "count", "update"]}, {"resources": ["PromoCodeModel"], "actions": ["view", "update"]}, {"resources": ["OnboardingBackgroundCheckModel", "PersonIdentityValidationModel"], "actions": ["view", "update", "create"]}, {"resources": ["PersonOnboardingModel", "HealthDeclaration"], "actions": ["view", "update", "create", "count"]}]}]}