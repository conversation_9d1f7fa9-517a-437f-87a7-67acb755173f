package app.sorting_hat_domain_service_test

import rego.v1
import data.app.sorting_hat_domain_service

test_unauth_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11,12} == sorting_hat_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Risk"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Risk"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareAdditionalTeam"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareAdditionalTeam"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "RoutingRule"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "RoutingRule"
                }
            },
            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StructuredAddress"
                }
            },
            {
                "index": 12,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StructuredAddress"
                }
            }
        ]
    }
}

test_unauth_view_count_create_and_update_person_clinical_account_allowed if {
    {1,2,3,4} == sorting_hat_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
        ]
    }
}

test_unauth_view_count_create_and_update_person_team_association_allowed if {
    {1,2,3,4} == sorting_hat_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                }
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                }
            },
        ]
    }
}

test_unauth_create_routing_history_allowed if {
    1 in sorting_hat_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "RoutingHistory"
                }
            },
        ]
    }
}
