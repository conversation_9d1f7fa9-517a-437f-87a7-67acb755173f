package app.fhir_domain_service_recurring_test

import rego.v1

import data.app.fhir_domain_service_recurring

test_Person_view_count_allowed if {
	{1,2} == fhir_domain_service_recurring.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "PersonModel",
            },
		}, {
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "PersonModel",
            },
        }]
	}
}

test_HaocFhirProcess_view_update_create_allowed if {
    {1,2,3} == fhir_domain_service_recurring.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HaocFhirProcess",
            },
        }, {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HaocFhirProcess",
            },
        }, {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "HaocFhirProcess",
            },
        }]
    }
}

test_FhirDocument_view_update_create_count_allowed if {
    {1,2,3,4} == fhir_domain_service_recurring.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "FhirDocument",
            },
        }, {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "FhirDocument",
            },
        }, {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "FhirDocument",
            },
        }, {
            "index": 4,
            "action": "count",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "FhirDocument",
            },
        }]
    }
}
