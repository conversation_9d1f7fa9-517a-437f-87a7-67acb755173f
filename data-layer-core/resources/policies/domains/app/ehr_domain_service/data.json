{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["ActionPlanTask", "AppContentScreenDetail", "AppointmentCoordination", "AppointmentEvent", "AppointmentProcedureExecuted", "AssistanceCare", "Channel", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalOutcomeRecord", "ContactModel", "ConsentRegistration", "DasaDiagnosticReport", "DbLaboratoryTestResult", "DbLaboratoryTestResultProcess", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExecIndicatorAuthorizerModel", "ExecutionGroupModel", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "FleuryTestResultFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HaocFhirProcess", "HDataOverview", "HLActionRecommendation", "HLAdherence", "HealthcareMap", "HealthcareResourceModel", "HealthcareTeamChannel", "HealthcareTeamModel", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "HealthDeclaration", "HealthForm", "HealthFormQuestion", "HealthLogicRecord", "HealthMeasurementModel", "HealthMeasurementTypeModel", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthSpecialistResourceBundleModel", "HLAdherence", "HospitalizationInfoModel", "IntentionCoordination", "LaboratoryTestResultModel", "MedicalSpecialtyModel", "MemberModel", "MvAuthorizedProcedureModel", "OutcomeConf", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonModel", "PersonTeamAssociation", "ProviderUnitModel", "RiskCalculationConf", "ServiceScriptExecution", "ServiceScriptNode", "ServiceScriptRelationship", "StructuredAddress", "TestCodeModel", "TestCodePackageModel", "TestResultFeedback", "TestResultFileModel", "Timeline", "TotvsGuiaModel", "VideoCall", "WandaComment"], "actions": ["view"]}, {"resources": ["AppointmentEvolution", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel", "ProviderUnitModel"], "actions": ["view", "count"]}, {"resources": ["CaseRecord"], "actions": ["view", "create"]}, {"resources": ["DraftCommandModel", "HealthCondition", "HealthCommunityUnreferencedAccessModel"], "actions": ["view", "update"]}, {"resources": ["AppointmentScheduleModel", "ClinicalBackground", "ConsolidatedRewardsModel", "CounterReferral", "HealthCommunitySpecialistModel", "HealthFormAnswerGroup", "HealthFormQuestionAnswer", "HealthLogicTracking", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthProfessionalModel", "MemberHealthMetricModel", "PersonHealthEvent", "PersonInternalReference", "PregnancyModel", "ProtocolTracking", "StaffModel", "TertiaryIntentionTouchPoint"], "actions": ["view", "create", "update"]}, {"resources": ["Appointment", "AssistanceSummaryModel"], "actions": ["view", "count", "create", "update"]}, {"resources": ["PersonClinicalAccount"], "actions": ["view", "create", "update", "delete"]}]}]}