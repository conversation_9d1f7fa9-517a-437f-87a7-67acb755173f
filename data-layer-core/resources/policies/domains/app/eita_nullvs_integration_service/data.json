{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["EitaNullvsIntegrationLogModel", "EitaNullvsIntegrationRecordModel"], "actions": ["view", "create", "update", "count"]}, {"resources": ["NullvsIntegrationRecordModel"], "actions": ["view", "create", "update", "delete"]}, {"resources": ["ProviderModel", "ProviderUnitModel", "HealthCommunitySpecialistModel", "HealthcareResourceGroupAssociationModel", "FileVault", "ProviderUnitGroupModel", "HealthcareResourceGroupModel", "TotvsGuiaModel", "HealthProfessionalModel", "HealthProfessionalOpsProfileModel"], "actions": ["view", "count"]}, {"resources": ["HealthcareResourceModel", "HealthcareBundleModel", "CompanyProductPriceListingModel", "NationalReceiptModel", "HealthInstitutionNegotiationModel"], "actions": ["create", "update", "view"]}]}]}