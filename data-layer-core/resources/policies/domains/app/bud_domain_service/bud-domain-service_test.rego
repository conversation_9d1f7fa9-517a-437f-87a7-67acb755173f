package app.bud_domain_service_test

import rego.v1

import data.app.bud_domain_service

test_unauth_view_and_create_and_update_and_count if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24 } == bud_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BudNode"
                },
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BudNode"
                },
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BudNode"
                },
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BudNode"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                },
            },
            {
                "index": 6,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                },
            },
            {
                "index": 7,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                },
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptAction"
                },
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigation"
                },
            },
            {
                "index": 10,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigation"
                },
            },
            {
                "index": 11,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigation"
                },
            },
            {
                "index": 12,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigation"
                },
            },
            {
                "index": 13,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigationGroup"
                },
            },
            {
                "index": 14,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigationGroup"
                },
            },
            {
                "index": 15,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigationGroup"
                },
            },
            {
                "index": 16,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNavigationGroup"
                },
            },
            {
                "index": 17,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNode"
                },
            },
            {
                "index": 18,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNode"
                },
            },
            {
                "index": 19,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNode"
                },
            },
            {
                "index": 20,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptNode"
                },
            },
            {
                "index": 21,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                },
            },
            {
                "index": 22,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                },
            },
            {
                "index": 23,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                },
            },
            {
                "index": 24,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ServiceScriptRelationship"
                },
            }
        ]
    }
}
