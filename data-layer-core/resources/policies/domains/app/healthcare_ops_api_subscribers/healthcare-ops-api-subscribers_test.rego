package app.healthcare_ops_api_subscribers_test

import data.app.healthcare_ops_api_subscribers
import rego.v1

test_unauthenticated_view_PersonModel_allow if {
	{1} == healthcare_ops_api_subscribers.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "PersonModel"},
	}]}
}

test_unauthenticated_view_create_EmailCommunication_allow if {
	{1, 2} == healthcare_ops_api_subscribers.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "EmailCommunicationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "EmailCommunicationModel"},
		},
	]}
}
