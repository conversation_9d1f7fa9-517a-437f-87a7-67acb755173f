{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["Beneficiary<PERSON><PERSON>l", "BillingAccountablePartyModel", "CompanyModel", "CompanySubContractModel", "MemberModel", "PersonBillingAccountablePartyModel", "PersonModel", "PriceListingModel", "ProductModel", "ProductPriceListingModel", "ProviderModel"], "actions": ["view"]}, {"resources": ["ChannelsZendeskTag", "ZendeskExternalReference"], "actions": ["view", "create", "update"]}]}]}