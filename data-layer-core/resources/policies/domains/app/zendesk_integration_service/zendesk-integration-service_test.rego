package app.zendesk_integration_service

import rego.v1

import data.app.zendesk_integration_service

test_unauth_view_model_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 } == zendesk_integration_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BillingAccountablePartyModel"
                },
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanyModel"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberModel"
                },
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonBillingAccountablePartyModel"
                },
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                },
            },
            {
                "index": 8,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PriceListingModel"
                },
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ProductModel"
                },
            },
            {
                "index": 10,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ProductPriceListingModel"
                },
            },
            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ProviderModel"
                },
            }
        ]
    }
}

test_unauth_view_and_create_and_update_model_allowed if {
    { 1, 2, 3, 4, 5, 6 } == zendesk_integration_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelsZendeskTag"
                },
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelsZendeskTag"
                },
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelsZendeskTag"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ZendeskExternalReference"
                },
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ZendeskExternalReference"
                },
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ZendeskExternalReference"
                },
            }
        ]
    }
}
