package br.com.alice.common.controllers

import br.com.alice.authentication.Authenticator
import br.com.alice.common.coHandler
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import com.google.firebase.auth.FirebaseToken
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode.Companion.InternalServerError
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.assertj.core.api.Assertions.assertThat
import org.koin.ktor.plugin.Koin
import kotlin.test.Test

class HealthCheckControllerTest {

    private val controller = HealthController("test")

    @Test
    fun `#checkHealthWithAuth should return 200`(): Unit = testApplication {
        val firebaseToken: FirebaseToken = mockk()
        mockkObject(Authenticator) {
            every { Authenticator.verifyIdToken(any()) } returns firebaseToken
            every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
            every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
    
            application {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }

                install(Koin)

                routing {
                    get("/") { coHandler(controller::checkHealthWithAuth) }
                }
            }
    
            client.get("/").let { response ->
                assertThat(response.status).isEqualTo(OK)
    
                val content = gson.fromJson<HealthResponse>(response.bodyAsText())
                assertThat(content.systemName).isEqualTo("test")
            }
        }

    }

    @Test
    fun `#checkHealthWithAuth should return 500`(): Unit = testApplication {
        application {
            install(Koin)

            routing {
                get("/") { coHandler(controller::checkHealthWithAuth) }
            }
        }

        client.get("/").let { response ->
            assertThat(response.status).isEqualTo(InternalServerError)

            val content = response.bodyAsText()
            assertThat(content).isEqualTo("FirebaseApp with name [DEFAULT] doesn't exist. ")
        }
    }
}
