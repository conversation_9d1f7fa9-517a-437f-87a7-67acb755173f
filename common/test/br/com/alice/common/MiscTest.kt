package br.com.alice.common

import br.com.alice.common.core.exceptions.BadRequestException
import io.mockk.clearAllMocks
import io.mockk.coVerify
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeAll
import kotlin.test.BeforeTest
import kotlin.test.Test

class MiscTest {

    companion object {
        @BeforeAll
        @JvmStatic
        fun classSetup() {
            unmockkAll()
            mockkStatic("kotlinx.coroutines.DelayKt")
        }
    }

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }

    @Test
    fun `#retry should run expected number of attempts`() = runBlocking<Unit> {
        val retries = 2
        val expectedAttempts = 3
        var actualAttempts = 0

        try {
            retry(retries = retries) {
                actualAttempts++
                throw Exception()
            }
        } catch (ex: Exception) { }

        (0..retries).forEach{ attempt ->
            coVerify { delay(attempt * 200L) }
        }
        coVerify(exactly = 0) { delay((retries + 1) * 200L) }

        assertThat(actualAttempts).isEqualTo(expectedAttempts)
    }

    @Test
    fun `#retry should return block's response`() = runBlocking<Unit> {
        val expectedResponse = "Test response string"

        val actualResponse = retry { expectedResponse }

        coVerify { delay(0L) }
        coVerify(exactly = 0) { delay(1 * 200L) }
        assertThat(actualResponse).isEqualTo(expectedResponse)
    }

    @Test
    fun `#retry should propagate the same exception`() = runBlocking<Unit> {
        class SpecificException : BadRequestException("Test expection propagation", "specific_exception")

        assertThatThrownBy { runBlocking {
            retry { throw SpecificException() }
        } }.isInstanceOf(SpecificException::class.java)
            .hasMessage("Test expection propagation")

        (0..1).forEach{ attempt ->
            coVerify { delay(attempt * 200L) }
        }
        coVerify(exactly = 0) { delay(2 * 200L) }
    }
}
