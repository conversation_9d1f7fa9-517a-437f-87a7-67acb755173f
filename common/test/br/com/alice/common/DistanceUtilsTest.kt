package br.com.alice.common

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class DistanceUtilsTest {

    // Pair(latitude, longitude)
    private val casaAlice = Pair(-23.57182895, -46.69271973)
    private val office = Pair(-23.57228442, -46.69272687)
    private val potatoSquare = Pair(-23.56708268, -46.69478663)


    @Test
    fun `#distanceInMeters returns a double for distance by other address`() {
        val expectedCasaAliceToOffice = 50

        val resultCasaAliceToOffice =
            DistanceUtils.distanceInMeters(casaAlice.first, casaAlice.second, office.first, office.second)
        assertThat(resultCasaAliceToOffice.toInt()).isEqualTo(expectedCasaAliceToOffice)

        val expectedCasaAliceToPotatoSquare = 568
        val resultCasaAliceToPotatoSquare =
            DistanceUtils.distanceInMeters(casaAlice.first, casaAlice.second, potatoSquare.first, potatoSquare.second)
        assertThat(resultCasaAliceToPotatoSquare.toInt()).isEqualTo(expectedCasaAliceToPotatoSquare)
    }

    @Test
    fun `#formatDistance returns a string with distance in meters or kilometers`() {
        assertThat(DistanceUtils.formatDistance(50.0)).isEqualTo("50 m")
        assertThat(DistanceUtils.formatDistance(0.0)).isEqualTo("0 m")
        assertThat(DistanceUtils.formatDistance(999.0)).isEqualTo("999 m")
        assertThat(DistanceUtils.formatDistance(999.8)).isEqualTo("1 km")
        assertThat(DistanceUtils.formatDistance(50.67)).isEqualTo("51 m")
        assertThat(DistanceUtils.formatDistance(24_878.0)).isEqualTo("25 km")
        assertThat(DistanceUtils.formatDistance(12_340.8)).isEqualTo("12 km")
    }
}
