package br.com.alice.common.codegen

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CodeGenWithMoreThanOneParameterForListFunctionsServiceTest : Service {

    override val namespace get() = "common"
    override val serviceName get() = "code-gen"

    suspend fun get(id: UUID): Result<CodeGenModel, Throwable>

    suspend fun findByIds(ids: List<UUID>): Result<List<CodeGenModel>, Throwable>

    suspend fun add(model: CodeGenModel): Result<CodeGenModel, Throwable>

    suspend fun update(model: CodeGenModel): Result<CodeGenModel, Throwable>

    suspend fun addList(models: List<CodeGenModel>, otherModels: List<CodeGenModel>): Result<List<CodeGenModel>, Throwable>

    suspend fun updateList(models: List<CodeGenModel>, otherModels: List<CodeGenModel>): Result<List<CodeGenModel>, Throwable>

    suspend fun deleteList(models: List<CodeGenModel>, otherModels: List<CodeGenModel>): Result<List<CodeGenModel>, Throwable>
}
