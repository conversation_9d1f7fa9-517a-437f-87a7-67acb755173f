package br.com.alice.common.extensions

import br.com.alice.authentication.authToken
import br.com.alice.common.AsyncLayerContext
import br.com.alice.common.AuthTokenContext
import br.com.alice.common.CallerContext
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.RootServiceContext
import br.com.alice.common.UseReadDatabaseContext
import br.com.alice.common.UseWriteDatabaseContext
import br.com.alice.common.coHandler
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.exceptions.ServiceServerNotFoundException
import br.com.alice.common.handler
import br.com.alice.common.logging.LoggingArguments.json
import br.com.alice.common.logging.logger
import br.com.alice.common.rfc.AsyncLayer
import br.com.alice.common.rfc.Caller
import br.com.alice.common.rfc.CallerTime
import br.com.alice.common.rfc.ReadDb
import br.com.alice.common.rfc.RemoteCall
import br.com.alice.common.rfc.RootServiceToken
import br.com.alice.common.rfc.Serializer
import br.com.alice.common.rfc.SerializerRfcException
import br.com.alice.common.rfc.Service
import br.com.alice.common.rfc.ServiceServer
import br.com.alice.common.rfc.WriteDb
import br.com.alice.common.toStatusCode
import com.github.kittinunf.result.failure
import com.google.gson.JsonElement
import com.google.gson.JsonParser
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.HttpStatusCode.Companion.InternalServerError
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.Unauthorized
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.application
import io.ktor.server.application.call
import io.ktor.server.request.header
import io.ktor.server.request.receiveText
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.util.getOrFail
import io.ktor.util.pipeline.PipelineContext
import kotlinx.coroutines.withContext
import org.koin.ktor.ext.getKoin
import org.koin.ktor.ext.inject
import java.time.LocalDateTime

suspend fun ApplicationCall.receiveRemoteCall() =
    RemoteCall(
        namespace = parameters.getOrFail("namespace"),
        service = parameters.getOrFail("service"),
        method = parameters.getOrFail("method"),
        parameters = receiveText()
    )

fun Route.rfcRoutes(
    mainRoute: String = "/rfc/{namespace}/{service}/{method}",
    checkHealthWithAuth: Boolean = false
) {

    suspend fun handle(remoteCall: RemoteCall): Pair<HttpStatusCode, String> {
        val servicePath = Service.path(remoteCall.namespace, remoteCall.service)
        val serviceServer = getKoin()
            .getAll<ServiceServer>()
            .find { it.service.path() == servicePath }
            ?: return run {
                logger.error(
                    "ServiceServer not found",
                    "path" to servicePath,
                    "namespace" to remoteCall.namespace,
                    "service" to remoteCall.service,
                    "method" to remoteCall.method
                )
                NotFound to Serializer.encodeResult(
                    ServiceServerNotFoundException("There's no serviceServer for path: $servicePath").failure()
                )
            }

        val start = System.nanoTime()
        val startTime = LocalDateTime.now()
        val responseBody = serviceServer.call(remoteCall.method, remoteCall.parameters)
        val durationMs = (System.nanoTime() - start) / 1000000

        val status = try {
            val jsonElement: JsonElement = JsonParser.parseString(responseBody)
            val isSuccessful = jsonElement.asJsonObject["success"] != null

            if (isSuccessful) {
                logger.trace(
                    "processed RFC command successfully",
                    "namespace" to remoteCall.namespace,
                    "service" to remoteCall.service,
                    "method" to remoteCall.method,
                    "is_successful" to true,
                    "start_time" to startTime,
                    "duration_ms" to durationMs
                )
                OK
            } else {
                if (!responseBody.contains(NotFoundException::class.qualifiedName!!)) {
                    logger.warn(
                        "processed RFC command with error",
                        "namespace" to remoteCall.namespace,
                        "service" to remoteCall.service,
                        "method" to remoteCall.method,
                        "is_successful" to false,
                        "start_time" to startTime,
                        "duration_ms" to durationMs,
                        json("request", remoteCall.parameters),
                        json("response", responseBody)
                    )
                }

                SerializerRfcException.decode(jsonElement.asJsonObject["error"]).toStatusCode()
            }
        } catch (ex: Exception) {
            logger.error("error logging", ex)
            InternalServerError
        }

        return status to responseBody
    }

    val healthController by inject<HealthController>()
    get("/") {
        if (checkHealthWithAuth)
            coHandler(healthController::checkHealthWithAuth)
        else
            handler(healthController::checkHealth)
    }

    post(mainRoute) {
        val namespace = call.parameters["namespace"]!!
        val service = call.parameters["service"]!!
        val method = call.parameters["method"]!!
        val fullPath = "/rfc/$namespace/$service/$method"

        logLatency()

        val authToken = call.request.authToken()
            ?: return@post call.respond(
                Unauthorized,
                Serializer.encodeResult(
                    AuthorizationException("Call to /rfc/$namespace/$service/$method without authToken.").failure()
                )
            )

        val environmentToken = call.request.header(HttpHeaders.RootServiceToken)
        val asyncLayerContext = call.request.header(HttpHeaders.AsyncLayer)
        val dbWriteContext = call.request.header(HttpHeaders.WriteDb)
        val dbReadContext = call.request.header(HttpHeaders.ReadDb)
        val serviceName = application.attributes.getOrNull(PolicyRootServiceKey)

        var allContext = coroutineContext + RootServiceContext(environmentToken) + AuthTokenContext(authToken) + CallerContext(serviceName, fullPath)
        asyncLayerContext?.let { allContext += AsyncLayerContext() }
        dbWriteContext?.let { allContext += UseWriteDatabaseContext() }
        dbReadContext?.let { allContext += UseReadDatabaseContext() }

        withContext(allContext) {
            handle(call.receiveRemoteCall()).let { (status, body) -> call.respond(status, body) }
        }
    }
}

fun PipelineContext<Unit, ApplicationCall>.logLatency() {
    val callerTime = call.request.header(HttpHeaders.CallerTime)
    val caller = call.request.header(HttpHeaders.Caller)
    callerTime?.let {
        val latency = System.currentTimeMillis() - callerTime.toLong()
        logger.debug("Request from $caller arrived in ${latency}ms", "caller" to caller, "latency" to latency)
    }
}
