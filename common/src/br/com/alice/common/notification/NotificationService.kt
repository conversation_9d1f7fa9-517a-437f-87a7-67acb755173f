package br.com.alice.common.notification

import br.com.alice.common.notification.sns.NotificationEventEnvelope
import io.ktor.util.StringValues
import java.util.UUID

interface NotificationService {

    suspend fun publish(event: NotificationEvent<*>): PublishEventResult

    fun canHandleSubscriptionConfirmation(headers: StringValues): Boolean

    fun confirmSubscription(message: String): SubscriptionConfirmationResult

    fun validateEvent(eventEnvelope: NotificationEventEnvelope)
}

data class PublishEventResult(
    val messageId: UUID,
    val providerData: Any,
    val success: Boolean = true
)

data class SubscriptionConfirmationResult(
    val data: Any
)
