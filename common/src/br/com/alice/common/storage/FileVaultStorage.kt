package br.com.alice.common.storage

import br.com.alice.common.MultipartRequest
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.idToken
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.common.systemAccessToken
import com.github.kittinunf.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.delete
import io.ktor.client.request.forms.FormPart
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.utils.io.core.buildPacket
import io.ktor.utils.io.core.writeFully
import java.util.UUID

class FileVaultStorage(private val client: HttpClient) {

    private val fileVaultUrl = BaseConfig.instance.fileVaultUrl()

    suspend fun store(
        personId: PersonId,
        domain: String,
        namespace: String? = null,
        multipartRequest: MultipartRequest,
        shouldStoreAsPii: Boolean = false,
    ): Result<AliceFile, Throwable> = coResultOf {
        if (multipartRequest.fileContent == null || multipartRequest.file == null)
            throw InvalidArgumentException("Invalid file content", "invalid_file")

        val token = idToken() ?: systemAccessToken()
        val asInput = multipartRequest.fileContent!!.readBytes()
        val inputSize = asInput.size
        val referenceId = multipartRequest.parameters[FileVaultConstants.Parameters.REFERENCED_LINK_ID] ?: ""
        val referenceType = multipartRequest.parameters[FileVaultConstants.Parameters.REFERENCED_LINK_MODEL] ?: ""

        val fileName = multipartRequest.file!!.name.toUrlEncoded()
        logger.info(
            "File vault storage will store file",
            "person_id" to personId,
            "namespace" to namespace,
            "domain" to domain,
            "size" to inputSize,
            "file_name" to fileName,
        )

        val data = MultiPartFormDataContent(formData {
            appendInput(
                key = "file",
                headers = Headers.build {
                    append(
                        HttpHeaders.ContentDisposition,
                        "filename=$fileName"
                    )
                },
                size = inputSize.toLong()
            ) { buildPacket { writeFully(asInput, 0, inputSize) } }
            namespace?.let { append(FormPart("namespace", it)) }
            append(FormPart("personId", personId.toString()))
            append(FormPart("fileSize", inputSize.toLong()))
            append(FormPart("shouldStoreAsPii", shouldStoreAsPii))
            append(FormPart("fileName", fileName))
            append(FormPart(FileVaultConstants.Parameters.REFERENCED_LINK_ID, referenceId))
            append(FormPart(FileVaultConstants.Parameters.REFERENCED_LINK_MODEL, referenceType))
        })

        val response = client.post("$fileVaultUrl/$domain/file") {
            header(HttpHeaders.Authorization, "Bearer $token")
            header(HttpHeaders.Accept, ContentType.Application.Json)
            setBody(data)
        }.body<AliceFile>()

        response.copy(fileName = fileName)
    }

    suspend fun delete(
        personId: String,
        domain: String,
        namespace: String? = null,
        fileName: String,
        shouldGetAsPii: Boolean = false,
    ): Result<FileDeleteResponse, Throwable> = coResultOf {
        val token = idToken() ?: systemAccessToken()

        logger.info(
            "FileVaultStorage::delete will delete stored file",
            "person_id" to personId,
            "namespace" to namespace,
            "domain" to domain,
            "file_name" to fileName
        )
        val parameters = mapOf(
            "personId" to personId,
            "fileName" to fileName,
            "shouldGetAsPii" to shouldGetAsPii.toString()
        ).let {
            if (namespace.isNullOrEmpty()) it
            else it.plus("namespace" to namespace)
        }
        val parametersAsQueryParams = parameters.encodeContent().asQueryParam().substring(1)

        client.delete("$fileVaultUrl/$domain/file/?$parametersAsQueryParams") {
            header(HttpHeaders.Authorization, "Bearer $token")
            header(HttpHeaders.Accept, ContentType.Application.Json)
        }.body<FileDeleteResponse>()
    }

    suspend fun getFileById(
        id: String,
        usServiceUrl: Boolean = false,
        shouldGetAsPii: Boolean = false,
    ): FileResponse? {
        val token = idToken() ?: systemAccessToken()

        return try {
            client.get(
                "$fileVaultUrl${if (usServiceUrl) "/service" else ""}/file/$id?shouldGetAsPii=${shouldGetAsPii}"
            ) {
                header(HttpHeaders.Authorization, "Bearer $token")
                header(HttpHeaders.Accept, ContentType.Application.Json)
            }.body()
        } catch (e: Exception) {
            logger.error("error trying to get file from FileVault", e)
            null
        }
    }

    suspend fun getFileByIds(ids: List<String>): List<FileResponse> {
        if (ids.isEmpty()) return emptyList()
        val token = idToken() ?: systemAccessToken()

        return try {
            client.get("$fileVaultUrl/file/batch?ids=${ids.joinToString(",")}") {
                header(HttpHeaders.Authorization, "Bearer $token")
                header(HttpHeaders.Accept, ContentType.Application.Json)
            }.body()
        } catch (e: Exception) {
            logger.error("error trying to get files from FileVault", e)
            emptyList()
        }
    }

    fun getFileUrl(id: String) = "$fileVaultUrl/file/$id"

    private fun Map<String, String?>.encodeContent() =
        map { (k, v) -> if (v != null) "${k.toUrlEncoded()}=${v.toUrlEncoded()}" else "" }.joinToString("&")

    private fun String.asQueryParam() = takeIf { it.isNotEmpty() }?.let { "&$it" } ?: ""
}

data class AliceFile(
    val id: UUID,
    val fileName: String,
    val url: String,
    val type: String,
    val fileSize: Long? = null
) : JsonSerializable

// TODO: refactor with br.com.alice.filevault.controllers.FileUploadResponse
data class FileResponse(
    val id: UUID,
    val url: String,
    val filename: String? = null
)

data class FileDeleteResponse(
    val success: Boolean
)
