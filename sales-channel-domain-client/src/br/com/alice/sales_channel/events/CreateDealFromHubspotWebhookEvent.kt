package br.com.alice.sales_channel.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.SALES_CHANNEL_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.DealStage
import java.time.LocalDateTime
import java.util.UUID


data class CreateDealFromHubspotWebhookEvent(
    val id: String,
    val stage: DealStage,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val companyName: String,
    val companyDocument: String,
    val companyLegalName: String?,
    val employeeCount: Int? = null,
    val livesCount: Int?,
    val contractModel: String?,
    val companyId: UUID? = null,
    val portabilityType: String ? = null,
    val portabilityResponse: String ? = null,
    val portabilityDeclinedProceed: String ? = null,
    val isCompulsoryMembership: Boolean? = null,
    val compulsoryMembershipType: String? = null,
    val compulsoryMembershipResponse: String? = null,
    val sellerName: String,
    val sellerDocument: String?,
    val sellerEmail: String? = null,
    val sellerPhoneNumber: String? = null,
    val sellerBirthDate: LocalDateTime? = null,
) : NotificationEvent<CreateDealFromHubspotWebhookPayload>(
    name = name,
    producer = SALES_CHANNEL_API_ROOT_SERVICE_NAME,
    payload = CreateDealFromHubspotWebhookPayload(
        id,
        stage,
        createdAt,
        updatedAt,
        companyName,
        companyDocument,
        companyLegalName,
        employeeCount,
        livesCount,
        contractModel,
        companyId,
        portabilityType,
        portabilityResponse,
        portabilityDeclinedProceed,
        isCompulsoryMembership,
        compulsoryMembershipType,
        compulsoryMembershipResponse,
        sellerName,
        sellerDocument,
        sellerEmail,
        sellerPhoneNumber,
        sellerBirthDate
    )
) {
    companion object {
        const val name = "deal-created-event"
    }
}

data class CreateDealFromHubspotWebhookPayload(
    val id: String,
    val stage: DealStage,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val companyName: String,
    val companyDocument: String,
    val companyLegalName: String?,
    val employeeCount: Int? = null,
    val livesCount: Int?,
    val contractModel: String?,
    val companyId: UUID? = null,
    val portabilityType: String ? = null,
    val portabilityResponse: String ? = null,
    val portabilityDeclinedProceed: String ? = null,
    val isCompulsoryMembership: Boolean? = null,
    val compulsoryMembershipType: String? = null,
    val compulsoryMembershipResponse: String? = null,
    val sellerName: String,
    val sellerDocument: String?,
    val sellerEmail: String? = null,
    val sellerPhoneNumber: String? = null,
    val sellerBirthDate: LocalDateTime? = null,
)


