package br.com.alice.provider.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ProviderTestCodeDataIntegration
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProviderTestCodeDataIntegrationService : Service {
    override val namespace get() = "provider_test_code"
    override val serviceName get() = "data_integration"

    suspend fun getAll(): Result<List<ProviderTestCodeDataIntegration>, Throwable>

    suspend fun getByIds(ids: List<UUID>): Result<List<ProviderTestCodeDataIntegration>, Throwable>
}


