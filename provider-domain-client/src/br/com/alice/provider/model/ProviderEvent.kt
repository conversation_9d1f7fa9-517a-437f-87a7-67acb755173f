package br.com.alice.provider.model

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Provider
import br.com.alice.provider.SERVICE_NAME

data class ProviderUpdatedEvent(
    val provider: Provider
) : NotificationEvent<ProviderUpdatedPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = ProviderUpdatedPayload(provider)
) {
    companion object {
        const val name = "provider-updated"
    }
}

data class ProviderCreatedEvent(
    val provider: Provider
) : NotificationEvent<ProviderCreatedPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = ProviderCreatedPayload(provider)
) {
    companion object {
        const val name = "provider-created"
    }
}

data class ProviderCreatedPayload(
    val provider: Provider
)

data class ProviderUpdatedPayload(
    val provider: Provider
)
