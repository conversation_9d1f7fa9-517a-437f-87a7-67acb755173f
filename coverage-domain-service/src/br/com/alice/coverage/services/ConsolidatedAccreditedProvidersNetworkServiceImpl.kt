package br.com.alice.coverage.services

import br.com.alice.common.Brand
import br.com.alice.common.extensions.mapEach
import br.com.alice.coverage.client.ConsolidatedAccreditedProvidersNetworkService
import br.com.alice.coverage.converters.toConsolidatedProviderTransport
import br.com.alice.coverage.model.accredited_network.request.UnitsByNameRequest
import br.com.alice.coverage.model.accredited_network.request.UnitsRequest
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedProviderLightTransport
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetwork
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class ConsolidatedAccreditedProvidersNetworkServiceImpl(
    private val productService: ProductService,
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkServiceImpl
) : ConsolidatedAccreditedProvidersNetworkService {
    companion object {
        val PROVIDER_BUNDLE_TYPES = listOf(
            ProductBundleType.HOSPITAL,
            ProductBundleType.LABORATORY,
            ProductBundleType.MATERNITY,
            ProductBundleType.VACCINE,
        )
    }

    override suspend fun getProviderUnits(request: UnitsRequest): Result<List<ConsolidatedProviderLightTransport>, Throwable> =
        with(request) {
            getProductWithActiveProviderBundles(productId)
                .flatMap { (product, bundles) ->
                    val bundleIds = bundles.map { it.id }
                    if (bundleIds.isEmpty()) return@flatMap Result.success(emptyList<ConsolidatedAccreditedNetwork>())
                    consolidatedAccreditedNetworkService.findProvidersByGeoLocationAndParams(
                        bundleIds,
                        types,
                        lat,
                        lng,
                        rangeInMeters,
                        product.brand ?: Brand.ALICE
                    )
                }.mapEach {
                    it.toConsolidatedProviderTransport()
                }
        }

    override suspend fun getProviderUnitsByName(request: UnitsByNameRequest): Result<List<ConsolidatedProviderLightTransport>, Throwable> =
        with(request) {
            getProductWithActiveProviderBundles(productId)
                .flatMap { (_, bundles) ->
                    val bundleIds = bundles.map { it.id }
                    if (bundleIds.isEmpty()) return@flatMap Result.success(emptyList<ConsolidatedAccreditedNetwork>())
                    consolidatedAccreditedNetworkService.findProvidersByNameAndParams(
                        bundleIds,
                        types,
                        name
                    )
                }.mapEach {
                    it.toConsolidatedProviderTransport()
                }
        }


    private suspend fun getProductWithActiveProviderBundles(productId: UUID) =
        productService.getProductWithBundles(productId, ProductService.FindOptions(false))
            .map { productWithBundles ->
                productWithBundles
                    .copy(bundles = productWithBundles.bundles
                        .filter { it.active })
            }.map { productWithBundles ->
                productWithBundles
                    .copy(bundles = productWithBundles.bundles
                        .filter { it.type in PROVIDER_BUNDLE_TYPES })
            }
}
