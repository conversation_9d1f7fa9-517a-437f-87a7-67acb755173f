package br.com.alice.coverage.route

import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterCassiEvent
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterHealthProfessionalEvent
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterUnitEvent
import br.com.alice.coverage.br.com.alice.coverage.event.HealthProfessionalRatingEvent
import br.com.alice.coverage.br.com.alice.coverage.event.ProviderBackfillChangeEvent
import br.com.alice.coverage.br.com.alice.coverage.event.ProviderBundleChangeEvent
import br.com.alice.coverage.br.com.alice.coverage.event.SpecialtyTiersBundleChangeEvent
import br.com.alice.coverage.consumers.AccreditedNetworkFavoriteSpecialityUpdaterConsumer
import br.com.alice.coverage.consumers.CassiSpecialistConsumer
import br.com.alice.coverage.consumers.ConsolidatedAccreditedNetworkCassiConsumer
import br.com.alice.coverage.consumers.ConsolidatedAccreditedNetworkHealthProfessionalConsumer
import br.com.alice.coverage.consumers.ConsolidatedAccreditedNetworkUnitConsumer
import br.com.alice.coverage.consumers.HealthProfessionalConsumer
import br.com.alice.coverage.consumers.ProductBundleConsumer
import br.com.alice.coverage.consumers.ProviderConsumer
import br.com.alice.coverage.consumers.ProviderUnitConsumer
import br.com.alice.coverage.consumers.SpecialistRatingConsumer
import br.com.alice.coverage.consumers.SpecialtyConsumer
import br.com.alice.product.model.events.ProductBundleCreatedEvent
import br.com.alice.product.model.events.ProductBundleUpdatedEvent
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import br.com.alice.provider.model.ProviderUpdatedEvent
import br.com.alice.staff.event.CassiSpecialistCreatedEvent
import br.com.alice.staff.event.CassiSpecialistUpdatedEvent
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {
    val consolidatedAccreditedNetworkUnitConsumer by inject<ConsolidatedAccreditedNetworkUnitConsumer>()

    consume(
        "upsert-provider-unit",
        AccreditedNetworkRegisterUnitEvent.name,
        consolidatedAccreditedNetworkUnitConsumer::registerByProviderUnit
    )

    val consolidatedAccreditedNetworkCassiConsumer by inject<ConsolidatedAccreditedNetworkCassiConsumer>()
    consume(
        "upsert-cassi-specialist",
        AccreditedNetworkRegisterCassiEvent.name,
        consolidatedAccreditedNetworkCassiConsumer::registerByCassiSpecialist
    )

    val providerUnitConsumer by inject<ProviderUnitConsumer>()
    consume(
        "send-event-to-accredited-network",
        ProviderUnitCreatedEvent.name,
        providerUnitConsumer::sendAccreditedNetworkUnitByUnitCreate
    )
    consume(
        "send-event-to-accredited-network",
        ProviderUnitUpdatedEvent.name,
        providerUnitConsumer::sendAccreditedNetworkUnitByUnitUpdate
    )

    val providerConsumer by inject<ProviderConsumer>()
    consume(
        "send-event-to-accredited-network-by-provider-change",
        ProviderUpdatedEvent.name,
        providerConsumer::sendAccreditedNetworkUnitByProviderUpdate
    )

    consume(
        "send-event-to-accredited-network-by-bundle-change",
        ProviderBundleChangeEvent.name,
        providerConsumer::sendAccreditedNetworkUnitByProviderBundle
    )
    consume(
        "send-event-to-accredited-network-by-backfill",
        ProviderBackfillChangeEvent.name,
        providerConsumer::sendAccreditedNetworkUnitByProviderBackfill
    )

    val productBundleConsumer by inject<ProductBundleConsumer>()
    consume(
        "send-event-to-provider-by-bundle-create",
        ProductBundleCreatedEvent.name,
        productBundleConsumer::sendAccreditedNetworkProviderByBundleCreate
    )

    consume(
        "send-event-to-provider-by-bundle-update",
        ProductBundleUpdatedEvent.name,
        productBundleConsumer::sendAccreditedNetworkProviderByBundleUpdate
    )

    val cassiSpecialistConsumer by inject<CassiSpecialistConsumer>()
    consume(
        "send-event-to-accredited-network-by-cassi-specialist-change",
        CassiSpecialistCreatedEvent.name,
        cassiSpecialistConsumer::sendAccreditedNetworkCassiBySpecialistCreate
    )

    consume(
        "send-event-to-accredited-network-by-cassi-specialist-change",
        CassiSpecialistUpdatedEvent.name,
        cassiSpecialistConsumer::sendAccreditedNetworkCassiBySpecialistUpdate
    )

    val healthProfessionalConsumer by inject<HealthProfessionalConsumer>()
    consume(
        "send-event-to-accredited-network-by-health-professional-change",
        HealthProfessionalCreatedEvent.name,
        healthProfessionalConsumer::sendAccreditedNetworkByHealthProfessionalCreated
    )

    consume(
        "send-event-to-accredited-network-by-health-professional-change",
        HealthProfessionalUpdatedEvent.name,
        healthProfessionalConsumer::sendAccreditedNetworkHealthProfessionalUpdated
    )

    val consolidatedAccreditedNetworkHealthProfessionalConsumer by inject<ConsolidatedAccreditedNetworkHealthProfessionalConsumer>()
    consume(
        "upsert-health-professional",
        AccreditedNetworkRegisterHealthProfessionalEvent.name,
        consolidatedAccreditedNetworkHealthProfessionalConsumer::registerByHealthProfessional
    )

    val specialtyConsumer by inject<SpecialtyConsumer>()
    consume(
        "send-event-to-accredited-network-by-bundle-change-to-hp",
        SpecialtyTiersBundleChangeEvent.name,
        specialtyConsumer::sendAccreditedNetworkHealthProfessionalByBundleChange
    )

    val specialistRatingConsumer by inject<SpecialistRatingConsumer>()
    consume(
        "register-specialist-rating",
        HealthProfessionalRatingEvent.name,
        specialistRatingConsumer::registerSpecialistRating
    )

    val accreditedNetworkFavoriteSpecialityUpdaterConsumer by inject<AccreditedNetworkFavoriteSpecialityUpdaterConsumer>()
    consume(
        "update-favorite-health-professional-speciality",
        HealthProfessionalUpdatedEvent.name,
        accreditedNetworkFavoriteSpecialityUpdaterConsumer::updateFavoriteHealthProfessionalSpeciality
    )
    consume(
        "update-favorite-provider-unit-speciality",
        ProviderUnitUpdatedEvent.name,
        accreditedNetworkFavoriteSpecialityUpdaterConsumer::updateFavoriteProviderUnitSpeciality
    )
}
