package br.com.alice.coverage

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.coverage.ioc.DataLayerServiceModule
import br.com.alice.coverage.ioc.ServiceModule
import br.com.alice.coverage.route.backFillRoutes
import br.com.alice.coverage.route.kafkaRoutes
import br.com.alice.coverage.route.apiRoutes
import br.com.alice.data.layer.COVERAGE_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.secondary.attention.ioc.SecondaryAttentionDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.server.application.Application
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        ProviderDomainClientModule,
        StaffDomainClientModule,
        ProductDomainClientModule,
        SecondaryAttentionDomainClientModule,
        KafkaProducerModule,
        GoogleMapsModule,
        ServiceModule,

        module {
            single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            backFillRoutes()
            apiRoutes()
            application.attributes.put(PolicyRootServiceKey, COVERAGE_DOMAIN_ROOT_SERVICE_NAME)
        }
        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }
        featureConfigBootstrap(FeatureNamespace.COVERAGE)
    }
}
