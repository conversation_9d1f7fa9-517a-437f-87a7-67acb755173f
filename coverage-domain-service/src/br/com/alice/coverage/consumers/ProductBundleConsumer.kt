package br.com.alice.coverage.consumers

import br.com.alice.common.coroutine.pmap
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.opentelemetry.Tracer
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkCassiOrigin
import br.com.alice.coverage.br.com.alice.coverage.event.AccreditedNetworkRegisterCassiEvent
import br.com.alice.coverage.br.com.alice.coverage.event.ProviderBundleChangeEvent
import br.com.alice.coverage.br.com.alice.coverage.event.SpecialtyTiersBundleChangeEvent
import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.product.model.events.ProductBundleCreatedEvent
import br.com.alice.product.model.events.ProductBundleUpdatedEvent
import br.com.alice.product.transport_model.ProductBundleUpdateSummary
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.util.UUID

class ProductBundleConsumer(
    private val kafkaProducerService: KafkaProducerService,
) : Consumer() {

    suspend fun sendAccreditedNetworkProviderByBundleCreate(event: ProductBundleCreatedEvent) =
        withSubscribersEnvironment {
            val bundle = event.payload.productBundle
            logger.info(
                "consumer product bundle event by created",
                "bundle_id" to bundle.id,
                "bundle_type" to bundle.type
            )
            sendEventByType(bundle, null)
        }

    suspend fun sendAccreditedNetworkProviderByBundleUpdate(event: ProductBundleUpdatedEvent) =
        withSubscribersEnvironment {
            val bundle = event.payload.productBundle
            val changes = event.payload.productBundleUpdateSummary
            logger.info(
                "consumer product bundle event by updated",
                "bundle_id" to bundle.id,
                "bundle_type" to bundle.type
            )
            sendEventByType(bundle, changes)
        }

    private suspend fun sendEventByType(bundle: ProductBundle, changes: ProductBundleUpdateSummary?) =
        when (bundle.type) {
            ProductBundleType.HOSPITAL,
            ProductBundleType.MATERNITY,
            ProductBundleType.LABORATORY,
            ProductBundleType.CLINICAL_COMMUNITY,
            ProductBundleType.VACCINE -> sendEventByProviderType(bundle, changes)

            ProductBundleType.CASSI_SPECIALIST -> sendEventByCassiType(bundle, changes)

            ProductBundleType.SPECIALITY_TIERS -> sendEventBySpecialtyTiersType(bundle, changes)
            else -> false.success()
        }

    private suspend fun sendEventByProviderType(
        bundle: ProductBundle,
        changes: ProductBundleUpdateSummary?
    ): Result<Boolean, Throwable> {
        val providersToUpdate = when {
            changes?.activated == true -> bundle.providerIds
            changes?.inactivated == true -> bundle.providerIds.plus(changes.providerIdsRemoved).distinct()
            changes != null -> changes.providerIdsRemoved.plus(changes.providerIdsAdded).distinct()
            bundle.active -> bundle.providerIds
            else -> emptyList()
        }

        providersToUpdate.pmap {
            sendUnitEventByBundle(it)
        }
        return providersToUpdate.isNotEmpty().success()
    }

    private suspend fun sendEventByCassiType(
        bundle: ProductBundle,
        changes: ProductBundleUpdateSummary?
    ): Result<Boolean, Throwable> {
        val cassiSpecialistsToUpdate = when {
            changes?.activated == true -> bundle.externalSpecialists
            changes?.inactivated == true -> bundle.externalSpecialists.plus(changes.externalSpecialistsIdsRemoved).distinct()
            changes != null -> changes.externalSpecialistsIdsAdded.plus(changes.externalSpecialistsIdsRemoved).distinct()
            bundle.active -> bundle.externalSpecialists
            else -> emptyList()
        }

        cassiSpecialistsToUpdate.pmap {
            sendCassiSpecialistEventByBundle(it)
        }
        return cassiSpecialistsToUpdate.isNotEmpty().success()
    }

    private suspend fun sendEventBySpecialtyTiersType(
        bundle: ProductBundle,
        changes: ProductBundleUpdateSummary?
    ): Result<Boolean, Throwable> {
        val specialtyTiersToUpdate = when {
            changes?.activated == true -> bundle.specialtyTiers
            changes?.inactivated == true -> bundle.specialtyTiers.plus(changes.specialtyTiersRemoved).distinct()
            changes != null -> changes.specialtyTiersAdded.plus(changes.specialtyTiersRemoved).distinct()
            bundle.active -> bundle.specialtyTiers
            else -> emptyList()
        }

        specialtyTiersToUpdate.pmap { specialtyTier ->
            sendSpecialtyTiersEventByBundle(specialtyTier)
        }
        return specialtyTiersToUpdate.isNotEmpty().success()
    }

    private suspend fun sendUnitEventByBundle(providerId: UUID) = Tracer.initTrace(providerId) {
        kafkaProducerService.produce(ProviderBundleChangeEvent(providerId))
    }

    private suspend fun sendCassiSpecialistEventByBundle(cassiSpecialistId: UUID) =
        Tracer.initTrace(cassiSpecialistId) {
            kafkaProducerService.produce(
                AccreditedNetworkRegisterCassiEvent(
                    cassiSpecialistId,
                    AccreditedNetworkCassiOrigin.PRODUCT_BUNDLE
                )
            )
        }

    private suspend fun sendSpecialtyTiersEventByBundle(specialtyTiers: SpecialtyTiers) =
        Tracer.initTrace(specialtyTiers.specialtyId) {
            kafkaProducerService.produce(
                SpecialtyTiersBundleChangeEvent(specialtyTiers)
            )
        }
}
