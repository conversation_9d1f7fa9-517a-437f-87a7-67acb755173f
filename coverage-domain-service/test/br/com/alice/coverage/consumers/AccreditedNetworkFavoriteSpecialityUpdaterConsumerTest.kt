package br.com.alice.coverage.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyProfile
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class AccreditedNetworkFavoriteSpecialityUpdaterConsumerTest : ConsumerTest() {
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val consumer = AccreditedNetworkFavoriteSpecialityUpdaterConsumer(
     healthProfessionalService,
     accreditedNetworkFavoriteService,
     providerUnitService
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional()
    private val providerUnit = TestModelFactory.buildProviderUnit(
        medicalSpecialtyProfile = listOf(TestModelFactory.buildMedicalSpecialtyProfile())
    )
    private val favorite = TestModelFactory.buildAccreditedNetworkFavorite(
        specialtyIds = listOf(RangeUUID.generate())
    )

    @Test
    fun `updateFavoriteHealthProfessionalSpeciality should send event`(): Unit = runBlocking {
        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(healthProfessional.id) } returns listOf(favorite).success()

        val updatedValue = favorite.copy(
            specialtyIds = listOf(healthProfessional.specialtyId!!)
        )

        coEvery { accreditedNetworkFavoriteService.update(updatedValue) } returns updatedValue.success()

        val result = consumer.updateFavoriteHealthProfessionalSpeciality(
            HealthProfessionalUpdatedEvent(
                healthProfessional.id
            )
        )

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `updateFavoriteHealthProfessionalSpeciality should call update for each record returned by findByReferenceId`(): Unit = runBlocking {
        val specialityId = RangeUUID.generate()

        val favorites = listOf(
            favorite.copy(
                specialtyIds = listOf(specialityId)
            ),
            favorite.copy(
                specialtyIds = listOf(specialityId)
            ),
            favorite.copy(
                specialtyIds = listOf(specialityId)
            )
        )

        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(healthProfessional.id) } returns favorites.success()

        val updatedValue = favorite.copy(
            specialtyIds = listOf(healthProfessional.specialtyId!!)
        )

        coEvery { accreditedNetworkFavoriteService.update(updatedValue) } returns updatedValue.success()

        val result = consumer.updateFavoriteHealthProfessionalSpeciality(
            HealthProfessionalUpdatedEvent(
                healthProfessional.id
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        coVerify(exactly = 3) {
            accreditedNetworkFavoriteService.update(any())
        }
    }

    @Test
    fun `updateFavoriteHealthProfessionalSpeciality should not call update when the health professional speciality ID doesn't change`(): Unit = runBlocking {
        val specialityId = RangeUUID.generate()

        val healthProfessional = healthProfessional.copy(
            specialtyId = specialityId
        )
        val favorite = favorite.copy(
            specialtyIds = listOf(specialityId)
        )

        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(healthProfessional.id) } returns listOf(favorite).success()

        val result = consumer.updateFavoriteHealthProfessionalSpeciality(
            HealthProfessionalUpdatedEvent(
                healthProfessional.id
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        coVerify { accreditedNetworkFavoriteService.update(any()) wasNot called }
    }

    @Test
    fun `updateFavoriteProviderUnitSpeciality should send event`(): Unit = runBlocking {
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) } returns listOf(favorite).success()

        val updatedValue = favorite.copy(
            specialtyIds = providerUnit.medicalSpecialtyProfile?.map { it.specialtyId } ?: emptyList()
        )

        coEvery { accreditedNetworkFavoriteService.update(updatedValue) } returns updatedValue.success()

        val result = consumer.updateFavoriteProviderUnitSpeciality(
            ProviderUnitUpdatedEvent(
                providerUnit
            )
        )

        ResultAssert.assertThat(result).isSuccess()
    }

    @Test
    fun `updateFavoriteProviderUnitSpeciality should call update for each record returned by findByReferenceId`(): Unit = runBlocking {
        val specialityId = RangeUUID.generate()

        val favorites = listOf(
            favorite.copy(
                specialtyIds = listOf(specialityId)
            ),
            favorite.copy(
                specialtyIds = listOf(specialityId)
            ),
            favorite.copy(
                specialtyIds = listOf(specialityId)
            )
        )

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) } returns favorites.success()

        val updatedValue = favorite.copy(
            specialtyIds = providerUnit.medicalSpecialtyProfile?.map { it.specialtyId } ?: emptyList()
        )

        coEvery { accreditedNetworkFavoriteService.update(updatedValue) } returns updatedValue.success()

        val result = consumer.updateFavoriteProviderUnitSpeciality(
            ProviderUnitUpdatedEvent(
                providerUnit
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        coVerify(exactly = 3) {
            accreditedNetworkFavoriteService.update(any())
        }
    }

    @Test
    fun `updateFavoriteProviderUnitSpeciality should not call update when the provider unit speciality ID doesn't change`(): Unit = runBlocking {
        val specialityId = RangeUUID.generate()

        val providerUnit = providerUnit.copy(
            medicalSpecialtyProfile = listOf(
                MedicalSpecialtyProfile(
                    specialtyId = specialityId,
                )
            )
        )
        val favorite = favorite.copy(
            specialtyIds = listOf(specialityId)
        )

        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) } returns listOf(favorite).success()

        val result = consumer.updateFavoriteProviderUnitSpeciality(
            ProviderUnitUpdatedEvent(
                providerUnit
            )
        )

        ResultAssert.assertThat(result).isSuccess()
        coVerify { accreditedNetworkFavoriteService.update(any()) wasNot called }
    }

    @Test
    fun `updateFavoriteHealthProfessionalSpeciality should fail when healthProfessionalService returns failure`(): Unit = runBlocking {
        coEvery { healthProfessionalService.get(healthProfessional.id) } returns Exception("Service error").failure()

        val result = consumer.updateFavoriteHealthProfessionalSpeciality(
            HealthProfessionalUpdatedEvent(healthProfessional.id)
        )

        ResultAssert.assertThat(result).isFailure()
        coVerify { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) wasNot called }
    }

    @Test
    fun `updateFavoriteHealthProfessionalSpeciality should fail when findByReferenceId returns failure`(): Unit = runBlocking {
        coEvery { healthProfessionalService.get(healthProfessional.id) } returns healthProfessional.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(healthProfessional.id) } returns Exception("Find error").failure()

        val result = consumer.updateFavoriteHealthProfessionalSpeciality(
            HealthProfessionalUpdatedEvent(healthProfessional.id)
        )

        ResultAssert.assertThat(result).isFailure()
        coVerify { accreditedNetworkFavoriteService.update(any()) wasNot called }
    }

    @Test
    fun `updateFavoriteProviderUnitSpeciality should fail when providerUnitService returns failure`(): Unit = runBlocking {
        coEvery { providerUnitService.get(providerUnit.id) } returns Exception("Service error").failure()

        val result = consumer.updateFavoriteProviderUnitSpeciality(
            ProviderUnitUpdatedEvent(providerUnit)
        )

        ResultAssert.assertThat(result).isFailure()
        coVerify { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) wasNot called }
    }

    @Test
    fun `updateFavoriteProviderUnitSpeciality should fail when findByReferenceId returns failure`(): Unit = runBlocking {
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
        coEvery { accreditedNetworkFavoriteService.findByReferenceId(providerUnit.id) } returns Exception("Find error").failure()

        val result = consumer.updateFavoriteProviderUnitSpeciality(
            ProviderUnitUpdatedEvent(providerUnit)
        )

        ResultAssert.assertThat(result).isFailure()
        coVerify { accreditedNetworkFavoriteService.update(any()) wasNot called }
    }
 }
