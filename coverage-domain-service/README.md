# Alice's Coverage Domain
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=mono%3Acoverage-domain-service&metric=coverage&token=c6021ef303a760ab13f482c42d8ca20043f61fec)](https://sonarcloud.io/summary/new_code?id=mono%3Acoverage-domain-service)

[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=mono%3Acoverage-domain-service&metric=code_smells&token=c6021ef303a760ab13f482c42d8ca20043f61fec)](https://sonarcloud.io/summary/new_code?id=mono%3Acoverage-domain-service)

[![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=mono%3Acoverage-domain-service&metric=duplicated_lines_density&token=c6021ef303a760ab13f482c42d8ca20043f61fec)](https://sonarcloud.io/summary/new_code?id=mono%3Acoverage-domain-service)

[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=mono%3Acoverage-domain-service&metric=bugs&token=c6021ef303a760ab13f482c42d8ca20043f61fec)](https://sonarcloud.io/summary/new_code?id=mono%3Acoverage-domain-service)

This is our Coverage domain, responsible for Coverage data.

### Responsible Team
Health Community, find us on ``#eng-comunidade-de-saude`` on Slack ;)

### Local development

Requirements
* Port 8024
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``db_reset db_seed db_run`` - to start our database and data layer
* ``run service=coverage-domain-service`` - to run it
