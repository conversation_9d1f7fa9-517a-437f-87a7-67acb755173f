package br.com.alice.healthcondition.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.data.layer.models.HealthConditionRelated
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthConditionRelatedService: Service {
    override val namespace get() = "health_condition"
    override val serviceName get() = "health_condition_related"

    suspend fun add(relation: HealthConditionRelated): Result<HealthConditionRelated, Throwable>

    suspend fun update(relation: HealthConditionRelated): Result<HealthConditionRelated, Throwable>

    suspend fun get(id: UUID): Result<HealthConditionRelated, Throwable>

    suspend fun getByRange(range: IntRange): Result<List<HealthConditionRelated>, Throwable>

    suspend fun getCipeTypesByHealthConditionIdAndTypes(healthConditionId: UUID, types: List<HealthConditionCodeType>): Result<List<HealthConditionRelated>, Throwable>

    suspend fun getByHealthConditionIdsAndType(healthConditionIds: List<UUID>, type: HealthConditionCodeType): Result<List<HealthConditionRelated>, Throwable>

    suspend fun count(): Result<Int, Throwable>

    suspend fun getAll(): Result<List<HealthConditionRelated>, Throwable>
    suspend fun getAnyHealthConditionIdsAndType(
        healthConditionIds: List<UUID>,
        type: HealthConditionCodeType
    ): Result<List<HealthConditionRelated>, Throwable>
}
