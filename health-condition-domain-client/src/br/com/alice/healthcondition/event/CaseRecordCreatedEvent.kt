package br.com.alice.healthcondition.event

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.healthcondition.SERVICE_NAME

data class CaseRecordCreatedEvent(
    private val caseRecord: CaseRecord
): NotificationEvent<CaseRecordCreatedEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = CaseRecordCreatedEventPayload(caseRecord)
) {
    companion object {
        const val name = "case-record-created"
    }
}

data class CaseRecordCreatedEventPayload(val caseRecord: CaseRecord)
