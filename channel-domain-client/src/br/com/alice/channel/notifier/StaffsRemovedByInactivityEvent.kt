package br.com.alice.channel.notifier

import br.com.alice.channel.SERVICE_NAME
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.common.core.PersonId
import br.com.alice.common.notification.NotificationEvent
import java.util.UUID

data class StaffsRemovedByInactivityEvent(
    private val channelId: String,
    private val personId: PersonId,
    private val staffIds: List<UUID>,
    private val channel: ChannelDocument
) : NotificationEvent<StaffsRemovedByInactivityEventEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = StaffsRemovedByInactivityEventEventPayload(
        channelId,
        staffIds,
        personId,
        channel.copy(lastPreviewableMessage = null),
    )
) {
    companion object {
        const val name = "channel_remove_staffs_by_inactivity"
    }
}

data class StaffsRemovedByInactivityEventEventPayload(
    val channelId: String,
    val staffIds: List<UUID>,
    val personId: PersonId,
    val channel: ChannelDocument
)
