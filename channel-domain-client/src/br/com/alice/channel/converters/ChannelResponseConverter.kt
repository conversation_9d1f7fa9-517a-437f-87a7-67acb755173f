package br.com.alice.channel.converters

import br.com.alice.channel.extensions.toLocalDateTime
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Channel

object ChannelResponseConverter : Converter<ChannelDocument, ChannelResponse>(
    ChannelDocument::class, ChannelResponse::class
) {
    fun convert(source: ChannelDocument, channelId: String = "", channelPersonId: String = "") =
        convert(
            source,
            map(ChannelResponse::id) from (source.id ?: channelId),
            map(ChannelResponse::channelPersonId) from (source.channelPersonId.ifEmpty { channelPersonId }),
            map(ChannelResponse::name) from (source.name ?: ""),
            map(ChannelResponse::type) from source.type!!,
            map(ChannelResponse::status) from source.status,
            map(ChannelResponse::ownerStaffId) from source.getOwner(),
            map(ChannelResponse::staffIds) from (source.staffIds ?: emptyList()),
            map(ChannelResponse::staffHistoryIds) from source.staffHistory.keys.toList(),
            map(ChannelResponse::staff) from source.staff,
            map(ChannelResponse::tags) from source.tags,
            map(ChannelResponse::createdAt) from source.createdAt.toLocalDateTime(),
            map(ChannelResponse::timeLastMessage) from source.timeLastMessage?.toLocalDateTime(),
            map(ChannelResponse::isArchived) from (source.archivedAt != null),
            map(ChannelResponse::personId) from source.personId,
            map(ChannelResponse::screeningNavigation) from source.screeningNavigation,
            map(ChannelResponse::archivedAt) from source.archivedAt?.toLocalDateTime()
        )
}

object ChannelBackupConverter : Converter<Channel, ChannelResponse>(
    Channel::class, ChannelResponse::class
) {
    fun convert(source: Channel) =
        convert(
            source,
            map(ChannelResponse::id) from source.channelId,
            map(ChannelResponse::channelPersonId) from "",
            map(ChannelResponse::name) from source.name.orEmpty(),
            map(ChannelResponse::type) from source.type!!,
            map(ChannelResponse::staffIds) from source.staffIds.map { it.toString() },
            map(ChannelResponse::staffHistoryIds) from source.staffHistory.map { it.toString() },
            map(ChannelResponse::createdAt) from source.channelCreatedAt,
            map(ChannelResponse::isArchived) from (source.archivedAt != null),
            map(ChannelResponse::personId) from source.personId.toString(),
            map(ChannelResponse::archivedAt) from source.archivedAt
        )
}

fun ChannelDocument.toResponse() =
    ChannelResponseConverter.convert(this)

fun Channel.toResponse() =
    ChannelBackupConverter.convert(this)
