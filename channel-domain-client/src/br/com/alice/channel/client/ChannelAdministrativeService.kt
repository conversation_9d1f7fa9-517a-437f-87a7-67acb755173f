package br.com.alice.channel.client

import br.com.alice.data.layer.models.AdministrativeStatus
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result

@RemoteService
interface ChannelAdministrativeService : Service {
    override val namespace get() = "channel"
    override val serviceName get() = "channel_administrative_service"
    
    suspend fun setAdministrativeStatus(
        channelId: String,
        administrativeStatus: AdministrativeStatus
    ): Result<String, Throwable>
}
