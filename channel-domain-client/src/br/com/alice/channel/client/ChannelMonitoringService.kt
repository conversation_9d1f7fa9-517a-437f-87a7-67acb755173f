package br.com.alice.channel.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result

@RemoteService
interface ChannelMonitoringService : Service {

    override val namespace get() = "channel"
    override val serviceName get() = "monitored"

    suspend fun setMonitoredChannel(channelId: String): Result<String, Throwable>

}
