package br.com.alice.featureconfig.services

import br.com.alice.common.asyncLayer
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.ABTest
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.featureconfig.client.ABTestingService
import br.com.alice.featureconfig.client.FeatureConfigService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class ABTestingServiceImpl(
    private val featureConfigService: FeatureConfigService
) : ABTestingService {

    override suspend fun listTests(range: IntRange, keyPrefix: String?): Result<List<ABTest>, Throwable> = asyncLayer {
        useReadDatabase {
            featureConfigService.searchForDistributionType(range, keyPrefix).mapEach {
                buildABTest(it)
            }
        }
    }

    override suspend fun countTests(keyPrefix: String?): Result<Int, Throwable> = asyncLayer {
        useReadDatabase {
            featureConfigService.countDistributionType(keyPrefix)
        }
    }

    override suspend fun get(id: UUID): Result<ABTest, Throwable> = asyncLayer {
        useReadDatabase {
            featureConfigService.getDistributionTypeById(id).map {
                buildABTest(it)
            }
        }
    }

    override suspend fun update(abTest: ABTest): Result<ABTest, Throwable> {
        val featureConfig = abTest.featureConfig
        return featureConfigService.update(featureConfig).map {
            buildABTest(it)
        }

    }

    override suspend fun add(abTest: ABTest): Result<ABTest, Throwable> {
        return featureConfigService.add(abTest.featureConfig).map {
            buildABTest(it)
        }
    }

    override suspend fun delete(abTest: ABTest): Result<Boolean, Throwable> {
        return featureConfigService.delete(abTest.featureConfig)
    }

    private fun buildABTest(featureConfig: FeatureConfig) =
        ABTest(
            id = featureConfig.id,
            key = featureConfig.key,
            featureConfig = featureConfig,
            description = featureConfig.description,
            namespace = featureConfig.namespace,
            active = featureConfig.active,
            isPublic = featureConfig.isPublic
        )

}
