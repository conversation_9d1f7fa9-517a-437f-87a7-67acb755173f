package br.com.alice.featureconfig.services.external

import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.FeatureConfigModel
import com.google.cloud.Timestamp
import com.google.cloud.firestore.Firestore
import com.google.firebase.cloud.FirestoreClient
import io.grpc.LoadBalancerRegistry
import io.grpc.internal.PickFirstLoadBalancerProvider
import java.time.ZoneOffset
import java.util.Date

class FirestoreService: Spannable {

    private val firestore: Firestore by lazy {
        LoadBalancerRegistry.getDefaultRegistry().register(PickFirstLoadBalancerProvider())
        FirestoreClient.getFirestore()
    }

    private val featureConfigsCollection = "feature_configs"

    suspend fun upsertFeatureConfig(item: FeatureConfigModel) {
        span("upsertFeatureConfig") {
            val firestoreFeatureConfig = FirestoreFeatureConfig(
                id = item.id.toString(),
                namespace = item.namespace.toString(),
                key = item.key,
                type = item.type.toString(),
                value = item.value,
                description = item.description,
                active = item.active,
                createdAt = Timestamp.of(Date.from(item.createdAt.toInstant(ZoneOffset.UTC))),
                updatedAt = Timestamp.of(Date.from(item.updatedAt.toInstant(ZoneOffset.UTC))),
                public = item.isPublic
            )

            logger.info(
                "Creating feature config on Firestore",
                "composite_key" to item.firestoreKey(),
                "type" to firestoreFeatureConfig.type,
                "value" to firestoreFeatureConfig.value,
                "active" to firestoreFeatureConfig.active,
                "public" to firestoreFeatureConfig.public
            )

            firestore
                .collection(featureConfigsCollection)
                .document(item.firestoreKey())
                .set(firestoreFeatureConfig)
                .get()
                .updateTime
        }
    }

    suspend fun deleteFeatureConfig(item: FeatureConfigModel) {
        span("deleteFeatureConfig") {
            firestore
                .collection(featureConfigsCollection)
                .document(item.firestoreKey())
                .delete()
                .get()
                .updateTime
        }
    }

    data class FirestoreFeatureConfig(
        val id: String,
        val namespace: String,
        val key: String,
        val createdAt: Timestamp,
        val updatedAt: Timestamp,
        val type: String,
        val value: String,
        val description: String,
        val active: Boolean,
        val public: Boolean
    )

    private fun FeatureConfigModel.firestoreKey() = "${namespace.name.lowercase()}-${key.lowercase()}"

}
