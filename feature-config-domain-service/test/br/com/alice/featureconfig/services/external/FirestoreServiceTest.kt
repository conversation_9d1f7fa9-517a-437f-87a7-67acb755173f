package br.com.alice.featureconfig.services.external

import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureConfigModel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.converters.toModel
import br.com.alice.featureconfig.services.external.FirestoreService.FirestoreFeatureConfig
import com.google.api.core.ApiFuture
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.DocumentReference
import com.google.cloud.firestore.Firestore
import com.google.cloud.firestore.WriteResult
import com.google.firebase.FirebaseApp
import com.google.firebase.cloud.FirestoreClient
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import java.time.ZoneOffset
import java.util.Date
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class FirestoreServiceTest {

    private val firebaseApp: FirebaseApp = mockk()
    private val firestore: Firestore = mockk()
    private val collectionReference: CollectionReference = mockk()
    private val documentReference: DocumentReference = mockk()
    private val apiFutureWriteResult: ApiFuture<WriteResult> = mockk()
    private val writeResult: WriteResult = mockk()

    private val featureConfig = TestModelFactory.buildFeatureConfig(
        namespace = FeatureNamespace.EHR,
        key = "my_key_01",
        value = "value"
    ).toModel()

    private val firestoreService = FirestoreService()

    @BeforeTest
    fun setup() {
        mockkStatic(FirebaseApp::class)
        mockkStatic(FirestoreClient::class)

        every { FirebaseApp.getInstance() } returns firebaseApp
        every { FirestoreClient.getFirestore() } returns firestore
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        firebaseApp,
        firestore,
        collectionReference,
        documentReference,
        apiFutureWriteResult,
        writeResult
    )

    @Test
    fun `#upsertFeatureConfig should add a feature config`() = runBlocking {
        val compositeKey = "${featureConfig.namespace.toString().lowercase()}-${featureConfig.key.lowercase()}"
        val firestoreFeatureConfig = buildFirestoreFeatureConfig(featureConfig)

        every { firestore.collection("feature_configs") } returns collectionReference
        every { collectionReference.document(compositeKey) } returns documentReference
        every { documentReference.set(firestoreFeatureConfig) } returns apiFutureWriteResult
        every { apiFutureWriteResult.get() } returns writeResult
        every { writeResult.updateTime } returns Timestamp.now()

        firestoreService.upsertFeatureConfig(featureConfig)

        verifyOnce { firestore.collection(any()) }
        verifyOnce { collectionReference.document(any()) }
        verifyOnce { documentReference.set(any<Any>()) }
        verifyOnce { apiFutureWriteResult.get() }
        verifyOnce { writeResult.updateTime }
    }

    @Test
    fun `#upsertFeatureConfig should update a feature config`() = runBlocking {
        val updatedFeatureConfig = featureConfig.copy(value = "new_value")
        val compositeKey = "${updatedFeatureConfig.namespace.toString().lowercase()}-${updatedFeatureConfig.key.lowercase()}"
        val firestoreFeatureConfig = buildFirestoreFeatureConfig(updatedFeatureConfig)

        every { firestore.collection("feature_configs") } returns collectionReference
        every { collectionReference.document(compositeKey) } returns documentReference
        every { documentReference.set(firestoreFeatureConfig) } returns apiFutureWriteResult
        every { apiFutureWriteResult.get() } returns writeResult
        every { writeResult.updateTime } returns Timestamp.now()

        firestoreService.upsertFeatureConfig(updatedFeatureConfig)

        verifyOnce { firestore.collection(any()) }
        verifyOnce { collectionReference.document(any()) }
        verifyOnce { documentReference.set(any<Any>()) }
        verifyOnce { apiFutureWriteResult.get() }
        verifyOnce { writeResult.updateTime }
    }

    @Test
    fun `#deleteFeatureConfig should delete a feature config from Firestore`() = runBlocking {
        val compositeKey = "${featureConfig.namespace.toString().lowercase()}-${featureConfig.key.lowercase()}"

        every { firestore.collection("feature_configs") } returns collectionReference
        every { collectionReference.document(compositeKey) } returns documentReference
        every { documentReference.delete() } returns apiFutureWriteResult
        every { apiFutureWriteResult.get() } returns writeResult
        every { writeResult.updateTime } returns Timestamp.now()

        firestoreService.deleteFeatureConfig(featureConfig)

        verifyOnce { firestore.collection(any()) }
        verifyOnce { collectionReference.document(any()) }
        verifyOnce { documentReference.delete() }
        verifyOnce { apiFutureWriteResult.get() }
        verifyOnce { writeResult.updateTime }
    }

    private fun buildFirestoreFeatureConfig(item: FeatureConfigModel) : FirestoreFeatureConfig =
        FirestoreFeatureConfig(
            id = item.id.toString(),
            namespace = item.namespace.toString(),
            key = item.key,
            type = item.type.toString(),
            value = item.value,
            description = item.description,
            active = item.active,
            createdAt = Timestamp.of(Date.from(item.createdAt.toInstant(ZoneOffset.UTC))),
            updatedAt = Timestamp.of(Date.from(item.updatedAt.toInstant(ZoneOffset.UTC))),
            public = item.isPublic
        )
}
