package br.com.alice.screening.model
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.serialization.gson

data class ScreenWithPlaceholders(
    val id: String,
    val placeholders: List<String> = emptyList(),
    val description: String,
    val answerOptions: List<String> = emptyList()
)

enum class ScreenRepresentationType {
    CARD_SECTION, PILL_SECTION,
}

data class ScreenRepresentation(
    val screen: ScreensTransport,
    val description: String,
    val type: ScreenRepresentationType,
) {

    fun screenTransportWithReplacedPlaceholders(placeholderValues: Map<String, String>): ScreensTransport? {
        var screenString = gson.toJson(screen)

        placeholderValues.forEach {
            screenString = screenString.replace("{{${it.key}}}", it.value)
        }
        return gson.fromJson(screenString, ScreensTransport::class.java)
    }
}
